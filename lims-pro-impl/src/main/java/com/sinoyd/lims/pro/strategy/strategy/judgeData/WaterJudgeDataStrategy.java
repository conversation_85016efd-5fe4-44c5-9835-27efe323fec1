package com.sinoyd.lims.pro.strategy.strategy.judgeData;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.factory.quality.JudgeData;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.repository.SampleJudgeDataRepository;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.JudgeDataStrategyKey.WATER_JUDGEDATA)
public class WaterJudgeDataStrategy extends AbsJudgeDataStrategy {

    private SampleJudgeDataRepository sampleJudgeDataRepository;

    @Override
    public void calculateJudgeData(List<DtoSampleJudgeData> judgeDataList) {
        getData(judgeDataList);
        QualityControlKind controlKind = new JudgeData();
        List<String> valueList = new ArrayList<>();
        for (DtoSampleJudgeData judgeData : judgeDataList) {
            setJuageData(judgeData);
            Optional<DtoTest> testOptional = testList.stream().filter(p -> judgeData.getTestId().equals(p.getId())).findFirst();
            if (testOptional.isPresent()) {
                Optional<DtoCompareJudge> compareJudgeOptional = compareJudges.stream().filter(p -> p.getAnalyzeItemId()
                        .equals(testOptional.get().getAnalyzeItemId())).findFirst();
                if (compareJudgeOptional.isPresent()) {
                    List<DtoQualityControlLimit> limitList = qualityControlLimitList.stream()
                            .filter(p -> p.getTestId().equals(compareJudgeOptional.get().getId())
                                    && judgeData.getCompareType().equals(p.getQcType())).collect(Collectors.toList());
                    //判断在线值和实验值是否是有效数
                    if (MathUtil.isNumber(judgeData.getExpectedValue()) && MathUtil.isNumber(judgeData.getOnlineValue())) {
                        valueList.clear();
                        //实验值
                        valueList.add(judgeData.getExpectedValue());
                        //在线值
                        valueList.add(judgeData.getOnlineValue());
                        //当均值
                        valueList.add(judgeData.getOnlineValue());
                        limitList.forEach(limit -> {
                            limit.setJudgeDataType(IFileNameConstant.JudgeDataStrategyKey.WATER_JUDGEDATA);
                        });
                        Map<String, Object> qcMap = controlKind.calculateDeviationValue(limitList, valueList);
                        String val = qcMap.get("qcRate").toString();
                        DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                        //值要做修约
                        String reviseValue = val;
                        if (StringUtil.isNotNull(limit)) {
                            Integer sign = controlKind.getComparisonConfig(IFileNameConstant.JudgeDataStrategyKey.WATER_JUDGEDATA, limit.getJudgingMethod()).getMostSignificance();
                            Integer md = controlKind.getComparisonConfig(IFileNameConstant.JudgeDataStrategyKey.WATER_JUDGEDATA, limit.getJudgingMethod()).getMostDecimal();
                            reviseValue = calculateService.revise(sign, md, val);
                            Boolean isPass = controlKind.deviationQualified(limit, reviseValue);
                            if (isPass) {
                                judgeData.setPass("是");
                            } else {
                                judgeData.setPass("否");
                            }
                            judgeData.setJudgingMethod(limit.getJudgingMethod());
                            judgeData.setAllowLimit(limit.getAllowLimit());
                            judgeData.setCheckItemValue(limit.getRangeConfig());
                        }
                        if (judgeData.getIsNotEvaluate()) judgeData.setPass("/");
                        judgeData.setQcRateValue(reviseValue);

                    }
                }
            }
        }
        sampleJudgeDataRepository.save(judgeDataList);
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataRepository(SampleJudgeDataRepository sampleJudgeDataRepository) {
        this.sampleJudgeDataRepository = sampleJudgeDataRepository;
    }
}
