package com.sinoyd.lims.pro.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.lims.pro.annotate.RegulatoryPlatformField;
import com.sinoyd.lims.pro.annotate.SoapResponseIgnore;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import lombok.extern.slf4j.Slf4j;

import javax.xml.bind.annotation.XmlElement;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 上海监管平台返回对象工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/13
 */
@Slf4j
public class RPJsonObjectUtil {

    /**
     * 将json对象转换为对象集合
     *
     * @param jsonObject json对象
     * @param c          对象类型
     * @param <T>        需要转换的对象泛型
     * @return java对象集合
     */
    public static <T> T jsonObjToObj(JSONObject jsonObject, Class<T> c) {
        try {
            T t = c.newInstance();
            Field[] fields = c.getDeclaredFields();
            for (Field field : fields) {
                //判断是否有SoapResponseIgnore标签注解, 有则在响应结果转换中忽略此字段
                SoapResponseIgnore soapXmlLabelIgnore = field.getAnnotation(SoapResponseIgnore.class);
                if (soapXmlLabelIgnore != null) {
                    continue;
                }
                //标签名称获取RegulatoryPlatformColumn注解中的value值
                XmlElement xmlElement = field.getAnnotation(XmlElement.class);
                if (xmlElement != null) {
                    field.setAccessible(true);
                    JSONObject itemObj = JSONObject.parseObject(jsonObject.toJSONString());
                    String val = itemObj.getString(xmlElement.name().replace(SoapBodyConstants.LABEL_ITEM_PREFIX, ""));
                    field.set(t, val);
                }
            }
            return t;
        } catch (Exception e) {
            log.error("json数据转换失败: {}", e.getMessage(), e);
            throw new RuntimeException("json数据转换失败");
        }
    }

    /**
     * 将json数组转换为对象集合
     *
     * @param jsonArray json数组
     * @param c         对象类型
     * @param <T>       需要转换的对象泛型
     * @return java对象集合
     */
    public static <T> List<T> jsonArrayToList(JSONArray jsonArray, Class<T> c) {
        List<T> list = new ArrayList<>();
        try {
            for (Object obj : jsonArray) {
                T t = c.newInstance();
                Field[] fields = c.getDeclaredFields();
                for (Field field : fields) {
                    //判断是否有SoapResponseIgnore标签注解, 有则在响应结果转换中忽略此字段
                    SoapResponseIgnore soapXmlLabelIgnore = field.getAnnotation(SoapResponseIgnore.class);
                    if (soapXmlLabelIgnore != null) {
                        continue;
                    }
                    //标签名称获取RegulatoryPlatformColumn注解中的value值
                    XmlElement xmlElement = field.getAnnotation(XmlElement.class);
                    if (xmlElement != null) {
                        field.setAccessible(true);
                        JSONObject itemObj = JSONObject.parseObject(JSONObject.toJSONString(obj));
                        String val = itemObj.getString(xmlElement.name().replace(SoapBodyConstants.LABEL_ITEM_PREFIX, ""));
                        field.set(t, val);
                    }
                }
                list.add(t);
            }
        } catch (Exception e) {
            log.error("json数据转换失败: {}", e.getMessage(), e);
            throw new RuntimeException("json数据转换失败");
        }
        return list;
    }
}
