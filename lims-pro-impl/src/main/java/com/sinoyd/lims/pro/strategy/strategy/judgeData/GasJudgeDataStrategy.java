package com.sinoyd.lims.pro.strategy.strategy.judgeData;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.factory.quality.JudgeData;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolderEvaluate;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.repository.SampleFolderEvaluateRepository;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import com.sinoyd.lims.pro.service.SampleService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.JudgeDataStrategyKey.GAS_JUDGEDATA)
public class GasJudgeDataStrategy extends AbsJudgeDataStrategy {

    private SampleService sampleService;

    private SampleFolderEvaluateRepository sampleFolderEvaluateRepository;

    private AnalyseDataService analyseDataService;

    @Override
    public void calculateJudgeData(List<DtoSampleJudgeData> judgeDataList) {
        getData(judgeDataList);
        QualityControlKind controlKind = new JudgeData();
        //存在点位
        List<String> sampleIds = judgeDataList.stream().map(DtoSampleJudgeData::getSampleId).collect(Collectors.toList());
        List<DtoSample> sampleList = sampleService.findAll(sampleIds);
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolderEvaluate> folderEvaluateList = sampleFolderEvaluateRepository.findBySampleFolderIdIn(folderIds);
        List<DtoSampleFolderEvaluate> changeEvaluateList = new ArrayList<>();
        testList.forEach(test -> {
            //获取比对数据
            folderIds.forEach(f -> {
                Optional<DtoSampleFolderEvaluate> evaluateOptional = folderEvaluateList.stream()
                        .filter(p -> p.getSampleFolderId().equals(f) && test.getId().equals(p.getTestId())).findFirst();
                List<String> samIds = sampleList.stream().filter(p -> f.equals(p.getSampleFolderId()))
                        .map(DtoSample::getId).collect(Collectors.toList());
                List<DtoSampleJudgeData> dataList = judgeDataList.stream().filter(p -> p.getTestId().equals(test.getId())
                        && samIds.contains(p.getSampleId())).collect(Collectors.toList());
                Optional<DtoCompareJudge> compareJudgeOptional = compareJudges.stream().filter(p -> p.getAnalyzeItemId()
                        .equals(test.getAnalyzeItemId())).findFirst();
                List<Integer> comTypeList = dataList.stream().map(DtoSampleJudgeData::getCompareType).distinct().collect(Collectors.toList());
                if (compareJudgeOptional.isPresent() && dataList.size() > 0) {
                    List<DtoQualityControlLimit> limitList = qualityControlLimitList.stream()
                            .filter(p -> p.getTestId().equals(compareJudgeOptional.get().getId())
                                    && comTypeList.contains(p.getQcType())).collect(Collectors.toList());
                    //计算均值
                    List<String> zxList = new ArrayList<>();
                    List<String> syList = new ArrayList<>();
                    dataList.forEach(data -> {
                        if (!data.getIsNotEvaluate()) {
                            // 在线值不需要比较检出限
                            zxList.add(data.getOnlineValue());
                            syList.add(super.halfLimit(data.getExpectedValue(), test.getExamLimitValue(), test.getExamLimitValueLess()));
                        }
                    });
                    controlKind.setSign(test.getMostSignificance());
                    controlKind.setMd(test.getMostDecimal());
                    controlKind.setZxList(zxList);
                    controlKind.setSyList(syList);
                    controlKind.setExamLimitValue(test.getExamLimitValue());
                    controlKind.setReviseType(test.getReviseType());
                    controlKind.setConfigValue(analyseDataService.getConfigValue("sample"));
                    limitList.forEach(limit -> {
                        limit.setJudgeDataType(IFileNameConstant.JudgeDataStrategyKey.GAS_JUDGEDATA);
                    });
                    Map<String, Object> qcMap = controlKind.calculateDeviationValue(limitList, new ArrayList<>());
                    //判断是否合格
                    DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                    String val = qcMap.get("qcRate").toString();
                    //值要做修约
                    String reviseValue = val;
                    Boolean isPass = null;
                    if (StringUtil.isNotNull(limit)) {
                        Integer sign = controlKind.getComparisonConfig(IFileNameConstant.JudgeDataStrategyKey.GAS_JUDGEDATA, limit.getJudgingMethod()).getMostSignificance();
                        Integer md = controlKind.getComparisonConfig(IFileNameConstant.JudgeDataStrategyKey.GAS_JUDGEDATA, limit.getJudgingMethod()).getMostDecimal();
                        reviseValue = calculateService.revise(sign, md, val);
                        isPass = controlKind.deviationQualified(limit, reviseValue);
                        dataList.forEach(data -> {
                            data.setJudgingMethod(limit.getJudgingMethod());
                            data.setAllowLimit(limit.getAllowLimit());
                            data.setCheckItemValue(limit.getRangeConfig());
                        });
                    }
                    //绑定到点位信息上
                    if (evaluateOptional.isPresent()) {
                        evaluateOptional.get().setQcRateValue(reviseValue);
                        if (StringUtil.isNotNull(isPass) && isPass) {
                            evaluateOptional.get().setResultEvaluate("是");
                        } else {
                            evaluateOptional.get().setResultEvaluate("否");
                        }
                    } else {
                        DtoSampleFolderEvaluate sampleFolderEvaluate = new DtoSampleFolderEvaluate();
                        sampleFolderEvaluate.setSampleFolderId(f);
                        sampleFolderEvaluate.setTestId(test.getId());
                        sampleFolderEvaluate.setQcRateValue(reviseValue);
                        if (StringUtil.isNotNull(isPass) && isPass) {
                            sampleFolderEvaluate.setResultEvaluate("是");
                        } else {
                            sampleFolderEvaluate.setResultEvaluate("否");
                        }
                        changeEvaluateList.add(sampleFolderEvaluate);
                    }
                }
            });
        });
        //新增点位情况
        if (changeEvaluateList.size() > 0) {
            sampleFolderEvaluateRepository.save(changeEvaluateList);
        }
        //修改点位情况
        if (folderEvaluateList.size() > 0) {
            sampleFolderEvaluateRepository.save(folderEvaluateList);
        }
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    public void setSampleFolderEvaluateRepository(SampleFolderEvaluateRepository sampleFolderEvaluateRepository) {
        this.sampleFolderEvaluateRepository = sampleFolderEvaluateRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }
}
