package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.RPDevicesVO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 上海监管平台设备信息服务策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Component
public class RPDevices extends AbsRegulatoryPlatformRemote<RPDevicesVO>{
    @Override
    protected void filterCriteria(List<RPDevicesVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //分析方法模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getKey())) {
            list.removeIf(i -> !i.getInstrumentName().contains(methodCriteria.getKey()));
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.仪器设备信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.仪器设备信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.仪器设备信息.getDeleteMethod();
    }
}
