package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.quality.QualityParallel;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.InstrumentUseRecordRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.TestQCRangeService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * QualityManage操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class QualityManageServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQualityManage, String, QualityManageRepository> implements QualityManageService {

    //#region 注入
    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    @Autowired
    @Lazy
    private TestQCRangeService testQCRangeService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    private InstrumentUseRecordRepository instrumentUseRecordRepository;

    @Autowired
    @Lazy
    private CalculateService calculateService;

    @Autowired
    private ParamsFormulaRepository paramsFormulaRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    @Autowired
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private QualityControlService qualityControlService;

    //#endregion

    @Override
    public void findByPage(PageBean<DtoQualityManage> pb, BaseCriteria qualityManageCriteria) {
        pb.setEntityName("DtoQualityManage q,DtoAnalyseData a");
        pb.setSelect("select q,a.sampleId,a.testId,a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.analystId,a.analystName,a.isCompleteField");
        comRepository.findByPage(pb, qualityManageCriteria);

        List<DtoQualityManage> datas = pb.getData();
        List<DtoQualityManage> newDatas = new ArrayList<>();

        Iterator<DtoQualityManage> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        Set<String> instrumentIdSet = new HashSet<>();
        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoQualityManage qm = (DtoQualityManage) objs[0];
            qm.setSampleId((String) objs[1]);
            qm.setTestId((String) objs[2]);
            qm.setTestName((String) objs[3]);
            qm.setRedAnalyzeMethodName((String) objs[4]);
            qm.setRedCountryStandard((String) objs[5]);
            qm.setAnalystId((String) objs[6]);
            qm.setAnalystName((String) objs[7]);
            qm.setIsCompleteField((Boolean) objs[8]);
            newDatas.add(qm);
            if (!UUIDHelper.GUID_EMPTY.equals(qm.getInstrumentId())) {
                instrumentIdSet.add(qm.getInstrumentId());
            }
        }
        List<DtoInstrument> instrumentList = instrumentIdSet.size() > 0 ? instrumentService.findAll(instrumentIdSet) : new ArrayList<>();

        for (DtoQualityManage qm : newDatas) {
            if (!UUIDHelper.GUID_EMPTY.equals(qm.getInstrumentId())) {
                DtoInstrument instrument = instrumentList.stream().filter(p -> p.getId().equals(qm.getInstrumentId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(instrument)) {
                    qm.setInstrumentName(instrument.getInstrumentName());
                    qm.setInstrumentsCode(instrument.getInstrumentsCode());
                    qm.setModel(instrument.getModel());
                }
            }
        }

        pb.setData(newDatas);
    }

    @Override
    public DtoQualityManage findOne(String id) {
        DtoQualityManage qm = super.findOne(id);
        if (StringUtil.isNotEmpty(qm.getInstrumentId()) && !UUIDHelper.GUID_EMPTY.equals(qm.getInstrumentId())) {
            DtoInstrument instrument = instrumentService.findOne(qm.getInstrumentId());
            if (StringUtil.isNotNull(instrument)) {
                qm.setInstrumentName(instrument.getInstrumentName());
                qm.setInstrumentsCode(instrument.getInstrumentsCode());
                qm.setModel(instrument.getModel());
            }
        }
        if (StringUtil.isNotEmpty(qm.getAnaId())) {
            DtoAnalyseData analyseData = analyseDataRepository.findOne(qm.getAnaId());
            qm.setTestId(analyseData.getTestId());
        }
        return qm;
    }

    /**
     * 外部质控添加质控信息
     *
     * @param sampleId 样品id
     * @param testIds  测试项目id
     */
    @Transactional
    @Override
    public void saveOutside(String sampleId, List<String> testIds) {
        DtoSample sample = sampleRepository.findOne(sampleId);
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(sample.getReceiveId());
        DtoProject project = projectRepository.findOne(record.getProjectId());
        DtoAnalyseDataQMAdd qmAdd = new DtoAnalyseDataQMAdd(project, sample, record, testIds, true);
        analyseDataService.addQMAnalyseData(qmAdd);
        List<DtoQualityManage> qmList = new ArrayList<>();

        for (String testId : qmAdd.getTest2AnaId().keySet()) {
            for (String anaId : qmAdd.getTest2AnaId().get(testId)) {
                DtoQualityManage qm = new DtoQualityManage();
                qm.setAnaId(anaId);
                qm.setQmType(-1);
                qm.setQmPersonId(PrincipalContextUser.getPrincipal().getUserId());
                qm.setQmTime(new Date());
                qm.setInstrumentId(UUIDHelper.GUID_EMPTY);
                qm.setIsMixedStandard(false);
                qm.setRedAnalyzeItemName("");
                qmList.add(qm);
            }
        }

        repository.save(qmList);
    }

    @Transactional
    @Override
    public DtoQualityManage save(DtoQualityManage qm) {
        DtoSample sample = sampleRepository.findOne(qm.getSampleId());
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(sample.getReceiveId());
        DtoProject project = projectRepository.findOne(record.getProjectId());
        DtoAnalyseDataQMAdd qmAdd = new DtoAnalyseDataQMAdd(project, sample, record, Collections.singletonList(qm.getTestId()), true);
        analyseDataService.addQMAnalyseData(qmAdd);
        int qmType = getQmTypeByProjectId(record.getProjectId(), sample);
        if (qmAdd.getTest2AnaId().containsKey(qm.getTestId())) {
            String anaId = qmAdd.getTest2AnaId().get(qm.getTestId()).get(0);
            qm.setAnaId(anaId);
            qm.setQmPersonId(PrincipalContextUser.getPrincipal().getUserId());
            qm.setQmTime(new Date());
            qm.setQmType(qmType);
        } else {
            throw new BaseException("添加失败");
        }
        super.save(qm);
        return qm;
    }

    @Override
    @Transactional
    public List<DtoQualityManage> batchCreate(List<DtoQualityManage> qualityManageList) {
        if (qualityManageList.size() == 1 && Boolean.FALSE.equals(qualityManageList.get(0).getIsMixedStandard())) {
            //单条和之前一样
            save(qualityManageList.get(0));
        } else {
            //校验测试项目是否都存在
            qualityManageList = checkBatchCreate(qualityManageList);
            //相关数据
            DtoSample sample = sampleRepository.findOne(qualityManageList.get(0).getSampleId());
            DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(sample.getReceiveId());
            DtoProject project = projectRepository.findOne(record.getProjectId());
            int qmType = getQmTypeByProjectId(record.getProjectId(), sample);
            Date now = new Date();
            for (DtoQualityManage qualityManage : qualityManageList) {
                DtoAnalyseDataQMAdd qmAdd = new DtoAnalyseDataQMAdd(project, sample, record, Collections.singletonList(qualityManage.getTestId()), true);
                analyseDataService.addQMAnalyseData(qmAdd);
                if (qmAdd.getTest2AnaId().containsKey(qualityManage.getTestId())) {
                    String anaId = qmAdd.getTest2AnaId().get(qualityManage.getTestId()).get(0);
                    qualityManage.setAnaId(anaId);
                    qualityManage.setQmPersonId(PrincipalContextUser.getPrincipal().getUserId());
                    qualityManage.setQmTime(now);
                    qualityManage.setQmType(qmType);
                }
            }
            super.save(qualityManageList);
        }
        return new ArrayList<>();
    }

    /**
     * 获取项目下qmtype
     *
     * @param projectId 项目标识
     * @return qmtype
     */
    private int getQmTypeByProjectId(String projectId, DtoSample sample) {
        int qmType = -1;
        DtoProjectPlan projectPlan = projectPlanRepository.findByProjectId(projectId);
        if (projectPlan != null) {
            if (EnumPorjectQCGrade.内部质控.getValue().equals(projectPlan.getQcGrade()) || EnumPorjectQCGrade.现场质控.getValue().equals(projectPlan.getQcGrade())) {
                if (EnumInnerQCType.标样考核.getValue().equals(projectPlan.getQcType())) {
                    qmType = EnumQMType.标样.getValue();
                }
                if (EnumInnerQCType.加标样考核.getValue().equals(projectPlan.getQcType())) {
                    qmType = EnumQMType.加标样.getValue();
                }
                if (EnumInnerQCType.盲样考核.getValue().equals(projectPlan.getQcType()) && sample != null
                        && EnumPRO.EnumSampleBlindType.密码加标.getValue().equals(sample.getBlindType())) {
                    qmType = EnumQMType.加标样.getValue();
                }
            }
        }
        return qmType;
    }

    @Override
    public List<DtoQualityManage> checkBatchCreate(List<DtoQualityManage> qualityManageList) {
        List<String> analyzeItemIdList = qualityManageList.stream().map(DtoQualityManage::getAnalyzeItemId).collect(Collectors.toList());
        List<DtoTest> testList = testRepository.findByAnalyzeItemIdIn(analyzeItemIdList);
        for (DtoQualityManage qualityManage : qualityManageList) {
            Optional<DtoTest> testOptional = testList.stream()
                    .filter(t -> qualityManage.getAnalyzeItemId().equals(t.getAnalyzeItemId()) && qualityManage.getAnalyzeMethodId().equals(t.getAnalyzeMethodId())).findFirst();
            if (!testOptional.isPresent()) {
                String errorMsg = String.format("%s对应的测试项目不存在！", qualityManage.getRedAnalyzeItemName());
                throw new BaseException(errorMsg);
            } else {
                DtoTest test = testOptional.get();
                qualityManage.setIsOutsourcing(test.getIsOutsourcing());
                qualityManage.setIsCompleteField(test.getIsCompleteField());
                qualityManage.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                qualityManage.setRedCountryStandard(test.getRedCountryStandard());
                qualityManage.setTestId(test.getId());
            }
        }
        return qualityManageList;
    }


    @Transactional
    @Override
    public DtoQualityManage update(DtoQualityManage entity) {
        return comRepository.merge(entity);
    }

    @Transactional
    @Override
    public List<DtoQualityManage> update(Collection<DtoQualityManage> entities) {
        for (DtoQualityManage entity : entities) {
            comRepository.merge(entity);
        }
        return new ArrayList<>(entities);
    }

    @Transactional
    @Override
    public void addQualityManages(DtoSample sample, DtoReceiveSampleRecord record, List<DtoQualityManage> qualityManages) {
        DtoProject project = projectRepository.findOne(record.getProjectId());
        DtoAnalyseDataQMAdd qmAdd = new DtoAnalyseDataQMAdd(project, sample, record, qualityManages.stream().map(DtoQualityManage::getTestId).collect(Collectors.toList()), false);
        analyseDataService.addQMAnalyseData(qmAdd);

        for (DtoQualityManage qm : qualityManages) {
            if (qmAdd.getTest2AnaId().containsKey(qm.getTestId()) && qmAdd.getTest2AnaId().get(qm.getTestId()).size() > 0) {
                String anaId = qmAdd.getTest2AnaId().get(qm.getTestId()).get(0);
                qm.setAnaId(anaId);
                if (StringUtils.isNotNullAndEmpty(qm.getAnalystId()) && !UUIDHelper.GUID_EMPTY.equals(qm.getAnalystId())) {
                    analyseDataRepository.updateAnalyst(Collections.singletonList(anaId), qm.getAnalystId(), qm.getAnalystName(), UUIDHelper.GUID_EMPTY,
                            EnumPRO.EnumAnalyseDataStatus.未测.toString(), EnumPRO.EnumAnalyseDataStatus.未测.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }
                qmAdd.getTest2AnaId().get(qm.getTestId()).remove(0);
            } else {
                qm.setAnaId("");
            }
        }
        List<DtoQualityManage> qmList = qualityManages.stream().filter(p -> !p.getAnaId().equals("")).collect(Collectors.toList());
        repository.save(qmList);
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoQualityManage qm = repository.findOne(String.valueOf(id));
        analyseDataService.deleteQMAnalyseData(Collections.singletonList(qm.getAnaId()));
        return super.logicDeleteById(id);
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> qmIds = new ArrayList<>();
        for (Object id : ids) {
            qmIds.add(String.valueOf(id));
        }
        List<DtoQualityManage> qmList = repository.findAll(qmIds);
        analyseDataService.deleteQMAnalyseData(qmList.stream().map(DtoQualityManage::getAnaId).collect(Collectors.toList()));
        return super.logicDeleteById(ids);
    }

    /**
     * @param instrumentId 仪器id
     * @param ids          质控信息id集合
     */
    @Transactional
    @Override
    public void updateInstrument(String instrumentId, List<String> ids) {
        List<DtoQualityManage> qmList = repository.findAll(ids);
        List<String> instrumentIds = qmList.stream().map(DtoQualityManage::getAnaId).distinct().collect(Collectors.toList());
        instrumentIds.add(instrumentId);
        List<DtoInstrument> instruments = instrumentService.findAll(instrumentIds);
        String instrName = instruments.stream().filter(p -> p.getId().equals(instrumentId)).
                map(p -> p.getInstrumentName() + " " + p.getModel() + " " + p.getInstrumentsCode()).findFirst().orElse("");

        HashMap<String, String> anaMap = new HashMap<>();
        for (DtoQualityManage qm : qmList) {
            if (!qm.getInstrumentId().equals(instrumentId)) {
                DtoInstrument instr = instruments.stream().filter(p -> p.getId().equals(qm.getInstrumentId())).findFirst().orElse(null);
                anaMap.put(qm.getAnaId(), StringUtil.isNotNull(instr) ? instr.getInstrumentName() + " " + instr.getModel() + " " + instr.getInstrumentsCode() : "");
                qm.setInstrumentId(instrumentId);
                comRepository.merge(qm);
            }
        }
        List<String> anaIds = new ArrayList<>(anaMap.keySet());
        if (anaIds.size() > 0) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findAll(anaIds);
            DtoSample sam = sampleRepository.findOne(analyseDataList.get(0).getSampleId());
            List<DtoLog> logList = new ArrayList<>();
            for (DtoAnalyseData anaData : analyseDataList) {
                String comment = String.format("设置了样品%s的指标%s的分析仪器，原分析仪器为%s，更换为%s。",
                        sam.getCode(),
                        anaData.getRedAnalyzeItemName(),
                        anaMap.get(anaData.getId()),
                        instrName);

                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumLogOperateType.修改检测仪器.toString());
                log.setLogType(EnumLogType.样品数据.getValue());
                log.setObjectId(sam.getReceiveId());
                log.setObjectType(EnumLogObjectType.送样单.getValue());
                log.setComment(comment);
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
            newLogService.createLog(logList, EnumLogType.样品数据.getValue());
        }
    }

    @Override
    public List<DtoQMProjectEvaluation> findProjectEvaluation(String projectId) {
        List<DtoQMEvaluation> qmList = this.findQMEvaluationListNotInFolder(projectId);
        qmList.addAll(this.findQMEvaluationListInFolder(projectId));
        List<String> instrumentIds = qmList.stream().map(DtoQMEvaluation::getInstrumentId).distinct().collect(Collectors.toList());
        List<DtoInstrument> instrumentList = instrumentIds.size() > 0 ? instrumentService.findAll(instrumentIds) : new ArrayList<>();

        for (DtoQMEvaluation qm : qmList) {
            if (!UUIDHelper.GUID_EMPTY.equals(qm.getInstrumentId())) {
                DtoInstrument instrument = instrumentList.stream().filter(p -> p.getId().equals(qm.getInstrumentId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(instrument)) {
                    qm.setInstrumentName(instrument.getInstrumentName());
                    qm.setInstrumentsCode(instrument.getInstrumentsCode());
                    qm.setModel(instrument.getModel());
                }
            }
        }

        List<DtoQMProjectEvaluation> evaList = new ArrayList<>();
        //处理质控详细数据返回
        this.handleQcDetail(evaList, qmList, projectId);
        return evaList;
    }

    /**
     * 处理不同类型的
     *
     * @param evaList   返回的集合
     * @param qmList    质控数据集合
     * @param projectId 项目id
     */
    protected void handleQcDetail(List<DtoQMProjectEvaluation> evaList, List<DtoQMEvaluation> qmList, String projectId) {
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(projectId);
        if (plan.getQcGrade().equals(EnumPorjectQCGrade.内部质控.getValue()) || EnumPorjectQCGrade.现场质控.getValue().equals(plan.getQcGrade())) {
            if (plan.getQcType().equals(EnumInnerQCType.标样考核.getValue())) {
                DtoQMProjectEvaluation eva = this.findStandardQMProjectEvaluation("标样考核", qmList);
                evaList.add(eva);
            } else if (plan.getQcType().equals(EnumInnerQCType.盲样考核.getValue())) {
                qmList.sort(Comparator.comparing(DtoQMEvaluation::getBlindType));
                qmList.stream().collect(Collectors.groupingBy(DtoQMEvaluation::getBlindType, Collectors.toList())).forEach((blindType, list) -> {
                    if (blindType.equals(EnumSampleBlindType.实际水样.getValue())) {
                        DtoQMProjectEvaluation eva = this.findOtherQMProjectEvaluation("实际水样信息", list);
                        evaList.add(eva);
                    } else if (blindType.equals(EnumSampleBlindType.标样.getValue())) {
                        DtoQMProjectEvaluation eva = this.findStandardQMProjectEvaluation("标样信息", list);
                        evaList.add(eva);
                    } else if (blindType.equals(EnumSampleBlindType.留样复测.getValue())) {
                        DtoQMProjectEvaluation eva = this.findReQMProjectEvaluation("留样复测信息", list);
                        evaList.add(eva);
                    } else if (blindType.equals(EnumSampleBlindType.加标样.getValue())) {
                        DtoQMProjectEvaluation eva = this.findMarkQMProjectEvaluation("加标样信息", list);
                        evaList.add(eva);
                    } else if (blindType.equals(EnumSampleBlindType.密码加标.getValue())) {
                        DtoQMProjectEvaluation eva = this.findPassPordMarkQMProjectEvaluation("密码加标信息", list);
                        evaList.add(eva);
                    } else if (blindType.equals(EnumSampleBlindType.密码平行.getValue())) {
                        DtoQMProjectEvaluation eva = this.findParallelQMProjectEvaluation("密码平行信息", list);
                        evaList.add(eva);
                    }
                });
            } else if (plan.getQcType().equals(EnumInnerQCType.加标样考核.getValue())) {
                DtoQMProjectEvaluation eva = this.findMarkQMProjectEvaluation("加标样信息", qmList);
                evaList.add(eva);
            } else {
                DtoQMProjectEvaluation eva = this.findOtherQMProjectEvaluation("一般质控", qmList);
                evaList.add(eva);
            }
        } else {
            DtoQMProjectEvaluation eva = this.findOtherQMProjectEvaluation("一般质控", qmList);
            evaList.add(eva);
        }
    }

    protected List<DtoQMEvaluation> findQMEvaluationListNotInFolder(String projectId) {
        PageBean<DtoQMEvaluation> pb = new PageBean<>();
        Map<String, Object> params = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQMEvaluation(");
        stringBuilder.append("a.id,a.testId,a.sampleId,s.code,s.blindType,s.status,s.parentSampleId,s.redFolderName,");
        stringBuilder.append("a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.status,");
        stringBuilder.append("a.analystName,a.testValueD,a.testValue,a.dimension,q.qmType,q.qmCode," +
                "q.qmValue,q.qmRange,q.qmVolume,q.stTestValue,q.redAnalyzeItemName,q.instrumentId,q.unitId,q.unit,q.uncertainType," +
                "q.rangeLow,q.rangeHigh)");
        pb.setSelect(stringBuilder.toString());

        pb.setEntityName("DtoQualityManage q,DtoAnalyseData a,DtoSample s");
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 ");
        pb.addCondition(" and q.anaId = a.id");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and s.projectId = :projectId");
        params.put("projectId", projectId);
        pb.addCondition(" and a.workSheetFolderId = :workSheetFolderId");
        params.put("workSheetFolderId", UUIDHelper.GUID_EMPTY);
        comRepository.findByPage(pb, params);
        return pb.getData();
    }

    protected List<DtoQMEvaluation> findQMEvaluationListInFolder(String projectId) {
        PageBean<DtoQMEvaluation> pb = new PageBean<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQMEvaluation(");
        stringBuilder.append("a.id,a.testId,a.sampleId,s.code,s.blindType,s.status,s.parentSampleId,s.redFolderName,");
        stringBuilder.append("a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.status,");
        stringBuilder.append("a.analystName,a.testValueD,a.testValue,a.dimension,q.qmType,q.qmCode,q.qmValue,");
        stringBuilder.append("q.qmRange,q.redAnalyzeItemName,q.instrumentId,ws.workSheetCode,ws.checkerName,ws.auditorName," +
                "q.qmVolume,q.stTestValue,q.unitId,q.unit,q.uncertainType,q.rangeLow,q.rangeHigh)");
        pb.setSelect(stringBuilder.toString());
        Map<String, Object> params = new HashMap<>();
        pb.setEntityName("DtoQualityManage q,DtoAnalyseData a,DtoWorkSheetFolder ws,DtoSample s");
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 and ws.isDeleted = 0");
        pb.addCondition(" and q.anaId = a.id");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and a.workSheetFolderId = ws.id");
        pb.addCondition(" and s.projectId = :projectId");
        params.put("projectId", projectId);
        comRepository.findByPage(pb, params);
        return pb.getData();
    }

    /**
     * 获取标样评价信息
     *
     * @param name   选项卡名称
     * @param qmList 质控数据
     * @return 标样评价信息
     */
    protected DtoQMProjectEvaluation<DtoQMEvaluation> findStandardQMProjectEvaluation(String name, List<DtoQMEvaluation> qmList) {
        qmList.sort(Comparator.comparing(DtoQMEvaluation::getRedAnalyzeItemName));
        DtoQMProjectEvaluation<DtoQMEvaluation> eva = new DtoQMProjectEvaluation<>();
        eva.setCardName(name);
        eva.setBlindType(EnumSampleBlindType.标样.getValue());
        for (DtoQMEvaluation qm : qmList) {
            qm.setAnalyseRemark(this.standardIsPass(qm) ? "合格" : "不合格");
            if (EnumBase.EnumUncertainType.百分比.getValue().equals(qm.getUncertainType()) && StringUtil.isNotEmpty(qm.getQmRange())) {
                qm.setQmRange(qm.getQmRange() + "%");
            }
            eva.getSampleEvaluation().add(qm);
        }

        return eva;
    }

    /**
     * 获取加标样评价信息
     *
     * @param name   选项卡名称
     * @param qmList 质控数据
     * @return 加标样评价信息
     */
    protected DtoQMProjectEvaluation<DtoQMEvaluation> findMarkQMProjectEvaluation(String name, List<DtoQMEvaluation> qmList) {
        qmList.sort(Comparator.comparing(DtoQMEvaluation::getRedAnalyzeItemName));
        DtoQMProjectEvaluation<DtoQMEvaluation> eva = new DtoQMProjectEvaluation<>();
        eva.setCardName(name);
        eva.setBlindType(EnumSampleBlindType.加标样.getValue());
        for (DtoQMEvaluation qm : qmList) {
            Boolean isNotNull = Boolean.TRUE;
            isNotNull = checkNullQmValue(qm, isNotNull);
            //计算加标回收率
            String qcRecoverRate = "";
            /**
             * 加入标准量：qmValue
             * 样值：stTestValue
             * 计算规则为（出证结果-样值）/加入标准值*100%，保留1位小数
             */
            BigDecimal testValue;
            try {
                testValue = new BigDecimal(qm.getTestValue().trim());
            } catch (Exception e) {
                testValue = qm.getTestValueD();
            }

            BigDecimal stTestValue;
            try {
                stTestValue = new BigDecimal(qm.getStTestValue().trim());
            } catch (Exception e) {
                stTestValue = new BigDecimal(0);
            }
            if (isNotNull) {
                BigDecimal qmValue = new BigDecimal(qm.getQmValue().trim());
                qcRecoverRate = new BigDecimal(100)
                        .multiply(testValue
                                .subtract(stTestValue)
                                .divide(qmValue, BigDecimal.ROUND_HALF_UP))
                        .setScale(1, BigDecimal.ROUND_HALF_UP)
                        .toString() + "%";
            }
            qm.setQcRecoverRate(qcRecoverRate);
            qm.setAnalyseRemark(this.standardIsPass(qm) ? "合格" : "不合格");
            eva.getSampleEvaluation().add(qm);
        }
        return eva;
    }

    /**
     * 闵行个性化，检查空值
     *
     * @param qm        加标样质量管理数据
     * @param isNotNull 是否空值
     * @return
     */
    protected Boolean checkNullQmValue(DtoQMEvaluation qm, Boolean isNotNull) {
        try {
            new BigDecimal(qm.getQmValue().trim());
        } catch (Exception e) {
            return false;
        }
        return isNotNull;
    }


    /**
     * 获取密码加标评价信息
     *
     * @param name   选项卡名称
     * @param qmList 质控数据
     * @return 密码加标评价信息
     */
    protected DtoQMProjectEvaluation<DtoQMEvaluation> findPassPordMarkQMProjectEvaluation(String name, List<DtoQMEvaluation> qmList) {
        qmList.sort(Comparator.comparing(DtoQMEvaluation::getRedAnalyzeItemName));
        DtoQMProjectEvaluation<DtoQMEvaluation> eva = new DtoQMProjectEvaluation<>();
        eva.setCardName(name);
        eva.setBlindType(EnumSampleBlindType.密码加标.getValue());
        List<String> parentSampleIds = qmList.stream().map(DtoQMEvaluation::getParentSampleId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(parentSampleIds);
        //获取所有公式
        List<String> testIds = analyseDatas.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoParamsFormula> formulas = paramsFormulaRepository.findByObjectIds(testIds);
        List<String> formulaIds = formulas.stream().map(DtoParamsFormula::getId).collect(Collectors.toList());
        List<DtoParamsPartFormula> partFormulas = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
        //获取所有公式参数
        List<DtoParamsTestFormula> params = paramsTestFormulaRepository.findByObjIdIn(formulaIds);
        //获取原样中分析数据参数值
        List<DtoAnalyseOriginalRecord> records = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDatas.stream().map(DtoAnalyseData::getId).collect(Collectors.toList()));
        qmList.stream().collect(Collectors.groupingBy(DtoQMEvaluation::getSampleId, Collectors.toList())).forEach((sampleId, list) -> {
            for (DtoQMEvaluation qm : list) {
                //获取对应原样值
                DtoAnalyseData yAna = analyseDatas.stream().filter(a -> a.getSampleId().equals(qm.getParentSampleId()) && a.getTestId().equals(qm.getTestId())).findFirst().orElse(null);
                if (yAna != null) {
                    //获取原样参数值
                    DtoAnalyseOriginalRecord records2YAna = records.stream().filter(r -> r.getAnalyseDataId().equals(yAna.getId())).findFirst().orElse(null);
                    //获取测试项目公式
                    DtoParamsFormula formula = formulas.stream().filter(f -> f.getObjectId().equals(qm.getTestId())).findFirst().orElse(null);
                    if (formula != null) {
                        //获取测得量公式
                        DtoParamsPartFormula partFormula = partFormulas.stream().filter(p -> p.getFormulaId().equals(formula.getId()) && p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue())).findFirst().orElse(null);
                        //获取公式所有参数
                        List<DtoParamsTestFormula> params2Formula = params.stream().filter(p -> p.getObjId().equals(formula.getId())).collect(Collectors.toList());
                        if (partFormula != null && records2YAna != null) {
                            TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
                            };
                            List<DtoAnalyseOriginalJson> originalJsonList = JsonIterator.deserialize(records2YAna.getJson(), typeLiteral);
                            Map<String, Object> map = new HashMap<>();
                            for (DtoParamsTestFormula param : params2Formula) {
                                String alias = param.getAlias();
                                DtoAnalyseOriginalJson originalJson = originalJsonList.stream().filter(m -> m.getAlias().equals(alias)).findFirst().orElse(null);
                                if (originalJson != null) {
                                    if (StringUtils.isNotNullAndEmpty(originalJson.getDefaultValue())) {
                                        if ("/".equals(originalJson.getDefaultValue().toString())) {
                                            map.put(alias, BigDecimal.ZERO);
                                            continue;
                                        }
                                        map.put(alias, new BigDecimal(originalJson.getDefaultValue()));
                                    }
                                }
                            }
                            //根据原样计算样值
                            Object value = calculateService.calculationExpression(partFormula.getFormula(), map);
                            //修约后的结果
                            String stTestValue = proService.getDecimal(partFormula.getMostSignificance(), partFormula.getMostDecimal(), value.toString());
                            //根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限对加标测定值及样值进行比较处理
                            stTestValue = qualityControlService.checkLimitForJb(stTestValue, partFormula, yAna.getTestId(), yAna.getSampleTypeId());
                            qm.setStTestValue(stTestValue);
                        }
                    }
                    //计算加标回收率
                    String qcRecoverRate = "";
                    /**
                     * 加入标准量：qmValue
                     * 样值：stTestValue
                     * 计算规则为（出证结果-样值）/加入标准值*100%，保留1位小数
                     */
                    BigDecimal testValue;
                    try {
                        testValue = new BigDecimal(qm.getTestValue().trim());
                    } catch (Exception e) {
                        testValue = qm.getTestValueD();
                    }
                    BigDecimal qmValue = new BigDecimal(qm.getQmValue().trim());
                    BigDecimal stTestValue;
                    try {
                        stTestValue = new BigDecimal(qm.getStTestValue().trim());
                    } catch (Exception e) {
                        stTestValue = new BigDecimal(0);
                    }
                    qcRecoverRate = new BigDecimal(100)
                            .multiply(testValue
                                    .subtract(stTestValue)
                                    .divide(qmValue, BigDecimal.ROUND_HALF_UP))
                            .setScale(1, BigDecimal.ROUND_HALF_UP)
                            .toString() + "%";
                    qm.setQcRecoverRate(qcRecoverRate);
                    qm.setAnalyseRemark(this.standardIsPass(qm) ? "合格" : "不合格");
                }
                eva.getSampleEvaluation().add(qm);
            }

        });
        return eva;
    }

    /**
     * 获取留样复测评价信息
     *
     * @param name   选项卡名称
     * @param qmList 质控数据
     * @return 留样复测评价信息
     */
    protected DtoQMProjectEvaluation findReQMProjectEvaluation(String name, List<DtoQMEvaluation> qmList) {
        qmList.sort(Comparator.comparing(DtoQMEvaluation::getSampleCode).thenComparing(DtoQMEvaluation::getRedAnalyzeItemName));
        DtoQMProjectEvaluation<DtoQMEvaluation> eva = new DtoQMProjectEvaluation<>();
        eva.setCardName(name);
        eva.setBlindType(EnumSampleBlindType.留样复测.getValue());
        List<String> parentSampleIds = qmList.stream().map(DtoQMEvaluation::getParentSampleId).collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findAll(parentSampleIds);
        List<DtoAnalyseData> analyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(parentSampleIds);
        //将list转为map
        Map<String, String> sampleCodeMap = samples.stream().collect(Collectors.toMap(DtoSample::getId, DtoSample::getCode));
        //将list转为map
        Map<String, DtoAnalyseData> analyseDataMap = analyseDatas.stream().collect(Collectors.toMap(p -> p.getSampleId() + p.getTestId(), data -> data));

        List<String> testIds = qmList.stream().map(DtoQMEvaluation::getTestId).distinct().collect(Collectors.toList());
        List<DtoTestQCRange> qcRanges = testIds.size() > 0 ? testQCRangeService.findByTestIdIn(testIds) : new ArrayList<>();

        qmList.stream().collect(Collectors.groupingBy(DtoQMEvaluation::getSampleId, Collectors.toList())).forEach((sampleId, list) -> {
            for (DtoQMEvaluation qm : list) {
                DtoAnalyseData yAna = analyseDataMap.getOrDefault(qm.getSampleId() + qm.getTestId(), null);
                qm.setParentSampleCode(sampleCodeMap.getOrDefault(qm.getParentSampleId(), ""));
                if (StringUtil.isNotNull(yAna)) {
                    List<BigDecimal> values = new ArrayList<>();
                    this.getPxValues(yAna, qm, values);
                    BigDecimal avg = values.get(0);
                    BigDecimal max = values.get(1);
                    BigDecimal min = values.get(2);
                    DtoTestQCRangeResult pxResult = testQCRangeService.qcRangeIsPass(
                            qcRanges.stream().filter(p -> p.getTestId().equals(qm.getTestId()) && p.getQcType().equals(new QualityParallel().qcTypeValue())).collect(Collectors.toList()),
                            new QualityParallel().qcTypeValue(), avg, max, min);

                    String analyseRemark = String.format("原样出证结果为:%s,%s", yAna.getTestValue(),
                            pxResult.getIsPass() ? "结果合格" : "结果不合格");
                    qm.setAnalyseRemark(analyseRemark);
                }
                eva.getSampleEvaluation().add(qm);
            }
        });
        return eva;
    }

    /**
     * 获取密码平行评价信息
     *
     * @param name   选项卡名称
     * @param qmList 质控数据
     * @return 密码平行评价信息
     */
    protected DtoQMProjectEvaluation findParallelQMProjectEvaluation(String name, List<DtoQMEvaluation> qmList) {
        qmList.sort(Comparator.comparing(DtoQMEvaluation::getSampleCode).thenComparing(DtoQMEvaluation::getRedAnalyzeItemName));
        DtoQMProjectEvaluation<DtoQMEvaluation> eva = new DtoQMProjectEvaluation<>();
        eva.setCardName(name);
        eva.setBlindType(EnumSampleBlindType.密码平行.getValue());
        List<String> parentSampleIds = qmList.stream().map(DtoQMEvaluation::getParentSampleId).collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findAll(parentSampleIds);
        List<DtoAnalyseData> analyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(parentSampleIds);
        List<String> workSheetFolderIds = analyseDatas.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        List<DtoWorkSheetFolder> workSheetFolders = workSheetFolderRepository.findByIdIn(workSheetFolderIds);
        List<DtoInstrument> instruments = instrumentService.findAll();
        List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordRepository.findAll();
        //将list转为map
        Map<String, String> sampleCodeMap = samples.stream().collect(Collectors.toMap(DtoSample::getId, DtoSample::getCode));
        //将list转为map
        Map<String, DtoAnalyseData> analyseDataMap = analyseDatas.stream().collect(Collectors.toMap(p -> p.getSampleId() + p.getTestId(), data -> data));
        List<String> testIds = qmList.stream().map(DtoQMEvaluation::getTestId).distinct().collect(Collectors.toList());
        List<DtoTestQCRange> qcRanges = testIds.size() > 0 ? testQCRangeService.findByTestIdIn(testIds) : new ArrayList<>();
        qmList.stream().collect(Collectors.groupingBy(DtoQMEvaluation::getSampleId, Collectors.toList())).forEach((sampleId, list) -> {
            for (DtoQMEvaluation qm : list) {
                DtoAnalyseData yAna = analyseDataMap.getOrDefault(qm.getParentSampleId() + qm.getTestId(), null);
                qm.setParentSampleCode(sampleCodeMap.getOrDefault(qm.getParentSampleId(), ""));
                if (StringUtil.isNotNull(yAna)) {
                    List<BigDecimal> values = new ArrayList<>();
                    this.getPxValues(yAna, qm, values);
                    BigDecimal avg = values.get(0);
                    BigDecimal max = values.get(1);
                    BigDecimal min = values.get(2);
                    DtoTestQCRangeResult pxResult = testQCRangeService.qcRangeIsPass(
                            qcRanges.stream().filter(p -> p.getTestId().equals(qm.getTestId()) && p.getQcType().equals(new QualityParallel().qcTypeValue())).collect(Collectors.toList()),
                            new QualityParallel().qcTypeValue(), avg, max, min);
                    String analyseRemark = String.format("原样出证结果为:%s,%s", yAna.getTestValue(),
                            pxResult.getIsPass() ? "结果合格" : "结果不合格");
                    qm.setAnalyseRemark(analyseRemark);
                    //设置原样数据
                    DtoQMEvaluation yQm = new DtoQMEvaluation();
                    yQm.setId(yAna.getId());
                    yQm.setSampleId(yAna.getSampleId());
                    yQm.setTestValue(yAna.getTestValue());
                    yQm.setTestValueD(yAna.getTestValueD());
                    yQm.setRedAnalyzeItemName(yAna.getRedAnalyzeItemName());
                    yQm.setRedAnalyzeMethodName(yAna.getRedAnalyzeMethodName());
                    yQm.setRedCountryStandard(yAna.getRedCountryStandard());
                    yQm.setTestId(yAna.getTestId());
                    yQm.setAnalystName(yAna.getAnalystName());
                    yQm.setStatus(yAna.getStatus());
                    yQm.setDimension(yAna.getDimension());
                    DtoSample ySample = samples.stream().filter(s -> s.getId().equals(qm.getParentSampleId())).findFirst().orElse(null);
                    if (ySample != null) {
                        yQm.setRedFolderName(ySample.getRedFolderName());
                    }
                    DtoSample sample = samples.stream().filter(s -> s.getId().equals(yAna.getSampleId())).findFirst().orElse(null);
                    if (sample != null) {
                        yQm.setParentSampleCode(sample.getCode());
                        yQm.setBlindType(sample.getBlindType());
                        yQm.setSampleStatus(sample.getStatus());
                    }
                    DtoWorkSheetFolder workSheetFolder = workSheetFolders.stream().filter(w -> w.getId().equals(yAna.getWorkSheetFolderId())).findFirst().orElse(null);
                    if (workSheetFolder != null) {
                        yQm.setCheckerName(workSheetFolder.getCheckerName());
                        yQm.setAuditorName(workSheetFolder.getAuditorName());
                        List<DtoInstrumentUseRecord> useRecord = useRecords.stream().filter(u -> u.getObjectId().equals(workSheetFolder.getId())).collect(Collectors.toList());
                        List<String> instrumentIds = useRecord.stream().map(DtoInstrumentUseRecord::getInstrumentId).collect(Collectors.toList());
                        List<DtoInstrument> instrumentList = instruments.stream().filter(i -> instrumentIds.contains(i.getId())).collect(Collectors.toList());
                        yQm.setInstrumentName(instrumentList.stream().map(DtoInstrument::getInstrumentName).collect(Collectors.joining(",")));
                        yQm.setInstrumentsCode(instrumentList.stream().map(DtoInstrument::getInstrumentsCode).collect(Collectors.joining(",")));
                    }
                    eva.getSampleEvaluation().add(yQm);
                    eva.getSampleEvaluation().add(qm);
                }
            }
        });
        return eva;
    }

    /**
     * 获取其它评价信息
     *
     * @param name   选项卡名称
     * @param qmList 质控数据
     * @return 其它评价信息
     */
    protected DtoQMProjectEvaluation findOtherQMProjectEvaluation(String name, List<DtoQMEvaluation> qmList) {
        qmList.sort(Comparator.comparing(DtoQMEvaluation::getSampleCode).thenComparing(DtoQMEvaluation::getRedAnalyzeItemName));
        DtoQMProjectEvaluation<DtoQMSampleEvaluation> eva = new DtoQMProjectEvaluation<>();
        eva.setCardName(name);
        eva.setBlindType(EnumSampleBlindType.非盲样.getValue());
        qmList.stream().collect(Collectors.groupingBy(DtoQMEvaluation::getSampleId, Collectors.toList())).forEach((sampleId, list) -> {
            DtoQMSampleEvaluation sampleEva = new DtoQMSampleEvaluation();
            sampleEva.setSampleId(sampleId);
            sampleEva.setSampleCode(list.get(0).getSampleCode());
            sampleEva.setStatus(list.get(0).getSampleStatus());
            sampleEva.setEvaluation(list);
            eva.getSampleEvaluation().add(sampleEva);
        });

        return eva;
    }

    private Boolean standardIsPass(DtoQMEvaluation qm) {
        if (qm.getQmType().equals(EnumQMType.标样.getValue())) {
            if (EnumBase.EnumUncertainType.区间.getValue().equals(qm.getUncertainType())) {
                if (StringUtil.isNotEmpty(qm.getQmValue()) && StringUtil.isNotEmpty(qm.getRangeLow()) && StringUtil.isNotEmpty(qm.getRangeHigh())
                        && MathUtil.isNumeral(qm.getQmValue()) && MathUtil.isNumeral(qm.getRangeLow()) && MathUtil.isNumeral(qm.getRangeHigh())) {
                    //根据检查项值是否在范围区间内（[标准值-下限]~[标准值+上限]）判定是否合格
                    BigDecimal bzValue = new BigDecimal(qm.getQmValue());
                    BigDecimal rangeLow = new BigDecimal(qm.getRangeLow());
                    BigDecimal rangeHigh = new BigDecimal(qm.getRangeHigh());
                    return qm.getTestValueD().compareTo(bzValue.subtract(rangeLow)) >= 0
                            && qm.getTestValueD().compareTo(bzValue.add(rangeHigh)) <= 0;
                }
                return false;
            } else {
                BigDecimal concentration = MathUtil.getBigDecimal(qm.getQmValue());
                BigDecimal uncertainty = MathUtil.getBigDecimal(qm.getQmRange());
                if (EnumBase.EnumUncertainType.百分比.getValue().equals(qm.getUncertainType())) {
                    BigDecimal top = concentration.multiply(BigDecimal.ONE.add(uncertainty)).multiply(new BigDecimal("0.01"));
                    BigDecimal low = concentration.multiply(BigDecimal.ONE.subtract(uncertainty)).multiply(new BigDecimal("0.01"));
                    if (low.compareTo(qm.getTestValueD()) <= 0 && top.compareTo(qm.getTestValueD()) >= 0) {
                        return true;
                    }
                }
                if (concentration.subtract(uncertainty).compareTo(qm.getTestValueD()) <= 0 &&
                        concentration.add(uncertainty).compareTo(qm.getTestValueD()) >= 0) {
                    return true;
                }
                return false;
            }
        } else if (qm.getQmType().equals(EnumQMType.加标样.getValue())) {
            //TODO 涉及加标样后需实现
//            testQCRangeService.calculateJBPass(qm.getTestValue(), qm.get)
        }

        return true;
    }

    private void getPxValues(DtoAnalyseData analyseData, DtoQMEvaluation qm, List<BigDecimal> values) {
        List<BigDecimal> testValues = new ArrayList<>();
        if (MathUtil.isNumeral(analyseData.getTestValue())) {
            testValues.add(MathUtil.getBigDecimal(analyseData.getTestValue()));
        } else {
            testValues.add(analyseData.getTestValueD());
        }
        if (MathUtil.isNumeral(qm.getTestValue())) {
            testValues.add(MathUtil.getBigDecimal(qm.getTestValue()));
        } else {
            testValues.add(qm.getTestValueD());
        }

        if (testValues.size() > 0) {
            testValues.sort(Comparator.comparing((BigDecimal p) -> p));
            BigDecimal max = testValues.get(1);
            BigDecimal min = testValues.get(0);
            BigDecimal sum = max.add(min);
            BigDecimal avg = sum.divide(new BigDecimal(2), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
            values.add(avg);
            values.add(max);
            values.add(min);
        }
    }

}