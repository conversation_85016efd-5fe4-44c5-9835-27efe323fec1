package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 质控项目查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年11月16日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QCProjectCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 关键字
     */
    private String key;

    /**
     * 模块编码
     */
    private String module;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 登记人id
     */
    private String inceptPersonId;

    /**
     * 控制类型
     */
    private Integer qcGrade = EnumStatus.所有.getValue();

    /**
     * 控制类型分类
     */
    private Integer qcType = EnumStatus.所有.getValue();

    /**
     * 状态
     */
    private Integer status = EnumStatus.所有.getValue();

    /**
     * 包含样品
     */
    private String sample;

    /**
     * 项目id列表
     */
    private List<String> projectIdList;

    /**
     * 是否协同任务
     */
    private Boolean isAssist;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = pl.projectId");
        condition.append(" and p.id = s.projectId");

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.inceptTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.inceptTime < :endTime");
            values.put("endTime", to);
        }

        if (StringUtil.isNotEmpty(this.module)) {
            condition.append(" and s.module = :module");
            values.put("module", this.module);
        }
        if (!qcGrade.equals(EnumStatus.所有.getValue())) {
            condition.append(" and pl.qcGrade = :qcGrade");
            values.put("qcGrade", this.qcGrade);
        }
        if (!qcType.equals(EnumStatus.所有.getValue())) {
            condition.append(" and pl.qcType = :qcType");
            values.put("qcType", this.qcType);
        }
        if (StringUtil.isNotEmpty(this.projectStatus)) {
            condition.append(" and p.status = :projectStatus");
            values.put("projectStatus", this.projectStatus);
        }
        if (!EnumStatus.所有.getValue().equals(this.status)) {
            condition.append(" and s.status = :status");
            values.put("status", this.status);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (p.projectCode like :key or p.projectName like :key)");
            values.put("key", "%" + key + "%");
        }
        if (StringUtil.isNotEmpty(this.sample)) {
            condition.append(" and exists (select 1 from DtoSample s where s.orgId = :orgId and s.isDeleted = 0 and p.id = s.projectId " +
                    "and s.code like :sample)");
            values.put("sample", "%" + sample + "%");
        }
        if (StringUtil.isNotEmpty(this.inceptPersonId)) {
            condition.append(" and p.inceptPersonId = :inceptPersonId");
            values.put("inceptPersonId", this.inceptPersonId);
        }
        if (StringUtil.isNotEmpty(projectIdList)) {
            condition.append(" and p.id in :projectIdList");
            values.put("projectIdList", this.projectIdList);
        }
        if(isAssist!=null){
            condition.append(" and p.isAssist = :isAssist");
            values.put("isAssist", this.isAssist);
        }
        return condition.toString();
    }
}
