package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.EnumHelper;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.AnalyzeItemSortDetailRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.ReceiveSubSampleRecordCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumReceiveSubRecordStatusName;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumSubRecordType;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 领样单操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/20
 * @since V100R001
 */
@Service
public class ReceiveSubSampleRecordServiceImpl extends BaseJpaServiceImpl<DtoReceiveSubSampleRecord, String, ReceiveSubSampleRecordRepository> implements ReceiveSubSampleRecordService {

    //#region 注入
    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private ParamsFormulaService paramsFormulaService;

    @Autowired
    @Lazy
    private ParamsTestFormulaService paramsTestFormulaService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private SubmitRecordService submitRecordService;

    @Autowired
    private AnalyzeItemSortDetailRepository analyzeItemSortDetailRepository;

    @Autowired
    @Lazy
    private CodeService codeService;


    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    private SamplingFrequencyTestService samplingFrequencyTestService;

    @Autowired
    private QualityControlService qualityControlService;

    @Autowired
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;


    @Autowired
    @Lazy
    private SampleGroupRepository sampleGroupRepository;

    @Autowired
    @Lazy
    private SampleGroup2TestRepository sampleGroup2TestRepository;

    @Autowired
    @Lazy
    private ProjectApprovalService projectApprovalService;

    private SampleJudgeDataService sampleJudgeDataService;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    /**
     * 创建领样单
     *
     * @param record  送样单
     * @param samples 样品
     * @param type    类型 XC,FX
     */
    @Transactional
    @Override
    public void createSubRecord(DtoReceiveSampleRecord record, List<DtoSample> samples, String type) {
        DtoReceiveSubSampleRecord subRecord = new DtoReceiveSubSampleRecord();
        subRecord.setReceiveId(record.getId());
        subRecord.setProjectId(record.getProjectId());
        subRecord.setReceiveTime(new Date());
        subRecord.setReceivePersonId(UUIDHelper.GUID_EMPTY);
        subRecord.setReceiveName("");
        subRecord.setCheckerId(UUIDHelper.GUID_EMPTY);
        subRecord.setCheckerName("");
        subRecord.setAuditorId(UUIDHelper.GUID_EMPTY);
        subRecord.setAuditorName("");
        subRecord.setBackOpinion("");
        subRecord.setDomainId(UUIDHelper.GUID_EMPTY);
        subRecord.setCode(String.format("%s-%s", record.getRecordCode(), type));
        if (type.equals(EnumSubRecordType.分析.getValue())) {
            subRecord.setStatus(EnumReceiveSubRecordStatusName.未领取.toString());
            Integer[] value = {EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()};
            subRecord.setSubStatus(EnumHelper.GetIntByArray(value));
        } else {
            subRecord.setStatus(EnumReceiveSubRecordStatusName.测试中.toString());
            Integer[] value = {EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()};
            subRecord.setSubStatus(EnumHelper.GetIntByArray(value));
        }
        repository.save(subRecord);
        if (StringUtil.isNotNull(samples) && samples.size() > 0) {
            List<DtoReceiveSubSampleRecord2Sample> r2sList = new ArrayList<>();
            for (DtoSample sample : samples) {
                DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                r2s.setReceiveSubSampleRecordId(subRecord.getId());
                r2s.setSampleId(sample.getId());
                r2sList.add(r2s);
            }
            comRepository.insertBatch(r2sList);
        }

        newLogService.createLog(subRecord.getId(), String.format("创建%s领样单%s", type.equals(EnumSubRecordType.分析.getValue()) ? EnumSubRecordType.分析.toString() : EnumSubRecordType.现场.toString(),
                        subRecord.getRecordCode()), "",
                EnumPRO.EnumLogType.现场领样单信息.getValue(), EnumPRO.EnumLogObjectType.现场领样单.getValue(), EnumPRO.EnumLogOperateType.创建现场领样单.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 根据送样单id和类型查询领样单
     *
     * @param receiveId 送样单id
     * @param type      类型 XC,FX
     * @return 返回对应送样单下的对应类型的领样单
     */
    @Override
    public DtoReceiveSubSampleRecord findByReceiveIdAndType(String receiveId, String type) {
        List<DtoReceiveSubSampleRecord> subRecords = repository.findByReceiveId(receiveId);
        if (StringUtil.isNotNull(subRecords) && subRecords.size() > 0) {
            Integer filterStatus = type.equals(EnumSubRecordType.分析.getValue()) ? EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() : EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue();
            DtoReceiveSubSampleRecord record = subRecords.stream().filter(p -> (p.getSubStatus() & filterStatus) != 0).findFirst().orElse(null);
            if (EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue().equals(filterStatus) && record != null) {
                receiveSampleRecordService.autoGnerateXcInstrumentUseRecord(record.getId());
            }
            return record;
        }
        return null;
    }

    /**
     * 获取领样单id下的现场指标
     *
     * @param subId 领样单id
     * @return 返回对应领样单下的现场指标
     */
    @Override
    public List<DtoTest> findLocalTest(String subId) {
        Map<String, Object> values = new HashMap<>();
        PageBean<String> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoSample s,DtoReceiveSubSampleRecord2Sample r2s");
        pb.setSelect("select distinct a.testId");
        pb.addCondition(" and s.isDeleted = 0  and a.isDeleted = 0 and r2s.isDeleted = 0 ");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and a.isCompleteField = 1");
        pb.addCondition(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        pb.addCondition(" and s.id = r2s.sampleId");
        pb.addCondition(" and r2s.receiveSubSampleRecordId = :subId");
        values.put("subId", subId);
        List<String> testIds = comRepository.find(pb.getAutoQuery(), values);
        return testIds.size() > 0 ? testService.findRedisByIds(testIds) : new ArrayList<>();
    }

    /**
     * 更换现场公式
     *
     * @param dtoAnalyseDataChangeFormula 更换dto
     * @return 返回对应更换数据
     */
    @Override
    @Transactional
    public DtoLocalDataProperty changeAnalyseDataFormula(DtoAnalyseDataChangeFormula dtoAnalyseDataChangeFormula) {
        //公式
        String formulaId = dtoAnalyseDataChangeFormula.getFormulaId();
        //相应的分析数据
        List<String> analyseDataIds = dtoAnalyseDataChangeFormula.getAnalyzeDataIds();

        //相应的公式数据
        DtoParamsFormula paramsFormula = paramsFormulaService.findOne(formulaId);
        String formula = paramsFormula.getFormula();
        List<DtoParamsTestFormula> paramsTestFormulaList = paramsTestFormulaService.findByObjectId(formulaId);

        //已经保存过的数据
        List<DtoAnalyseOriginalRecord> analyseOriginalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIds);

        //查询出的数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findAll(analyseDataIds);
        List<DtoAnalyseDataTemp> analyseDataTemps = findAnalyseData(analyseDataList);

        String oldFormula = "";
        List<Map<String, Object>> anaMapList = new ArrayList<>();
        for (DtoAnalyseDataTemp dtoAnalyseDataTemp : analyseDataTemps) {
            String anaId = dtoAnalyseDataTemp.getId();
            DtoAnalyseOriginalRecord analyseOriginalRecord = analyseOriginalRecords.stream()
                    .filter(p -> p.getAnalyseDataId().equals(anaId)).findFirst().orElse(null);
            //说明数据存在，先判断公式是否改变
            List<DtoParamsTestFormula> paramsTestFormulas = new ArrayList<>();
            //是否有原始数据
            Boolean isOriginal = false;
            //公式是否改变
            Boolean isChangeFormula = true;
            DtoAnalyseOriginalRecord dtoAnalyseOriginalRecord = new DtoAnalyseOriginalRecord();
            if (StringUtil.isNotNull(analyseOriginalRecord)) {
                oldFormula = analyseOriginalRecord.getTestFormula();
                TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
                };
                if (formulaId.equals(analyseOriginalRecord.getTestFormulaId())) { //说明公式未改变，只需要验证参数是否变化
                    //新的公式参数
                    List<DtoParamsTestFormula> lastNewParamsTestFormula = new ArrayList<>(paramsTestFormulaList);
                    //老的公式参数
                    List<DtoParamsTestFormula> oldParamsTestFormula = JsonIterator.deserialize(analyseOriginalRecord.getJson(), typeLiteral);

                    Set<String> lastAlias = lastNewParamsTestFormula.stream().map(DtoParamsTestFormula::getAlias).collect(Collectors.toSet());

                    Set<String> olAlias = oldParamsTestFormula.stream().map(DtoParamsTestFormula::getAlias).collect(Collectors.toSet());
                    //取差集
                    lastAlias.removeAll(olAlias);
                    //说明完全一致，不需要进行处理
                    if (lastNewParamsTestFormula.size() == oldParamsTestFormula.size() && lastAlias.size() == 0) {
                        paramsTestFormulaList = oldParamsTestFormula;
                    } else { //说明参数有不一致的地方
                        for (DtoParamsTestFormula paramsTestFormula : lastNewParamsTestFormula) {
                            Optional<DtoParamsTestFormula> optionalDtoParamsTestFormula = oldParamsTestFormula.stream().filter(p -> p.getAlias().equals(paramsTestFormula.getAlias())).findFirst();
                            if (optionalDtoParamsTestFormula.isPresent()) {
                                DtoParamsTestFormula dtoParamsTestFormula = optionalDtoParamsTestFormula.get();
                                //把值更新成老的
                                paramsTestFormula.setDefaultValue(dtoParamsTestFormula.getDefaultValue());
                            }
                        }
                        //把最新的赋值给变量
                        paramsTestFormulaList = lastNewParamsTestFormula;
                    }
                    isChangeFormula = false;
                    isOriginal = true;
                }
                dtoAnalyseOriginalRecord.setId(analyseOriginalRecord.getId());
            }

            if (isChangeFormula) {
                paramsTestFormulaList = new ArrayList<>(paramsTestFormulaList);
            }
            dtoAnalyseDataTemp.setFormula(formula);
            dtoAnalyseDataTemp.setFormulaId(formulaId);
            Map<String, Object> map = getAnalyseDataMap(dtoAnalyseDataTemp);
            for (DtoParamsTestFormula dtoParamsTestFormula : paramsTestFormulaList) {
                map.put(dtoParamsTestFormula.getAlias(), dtoParamsTestFormula.getDefaultValue());
            }
            anaMapList.add(map);

            dtoAnalyseOriginalRecord.setTestFormulaId(formulaId);
            dtoAnalyseOriginalRecord.setTestFormula(formula);
            dtoAnalyseOriginalRecord.setAnalyseDataId(anaId);
            dtoAnalyseOriginalRecord.setJson(JsonStream.serialize(paramsTestFormulaList));
            if (isOriginal) {
                comRepository.merge(dtoAnalyseOriginalRecord);
            } else {
                analyseOriginalRecordRepository.save(dtoAnalyseOriginalRecord);
            }
        }
        List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs = new ArrayList<>();
        for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulaList) {
            DtoTestFormulaParamsConfig dtoTestFormulaParamsConfig = new DtoTestFormulaParamsConfig();
            dtoTestFormulaParamsConfig.setOrderNum(paramsTestFormula.getOrderNum());
            dtoTestFormulaParamsConfig.setAlias(paramsTestFormula.getAlias());
            dtoTestFormulaParamsConfig.setIsMust(paramsTestFormula.getIsMust());
            dtoTestFormulaParamsConfig.setIsEditable(paramsTestFormula.getIsEditable());
            dtoTestFormulaParamsConfig.setIsCalculate(formula.contains("[" + paramsTestFormula.getAlias() + "]"));
            testFormulaParamsConfigs.add(dtoTestFormulaParamsConfig);
        }

        DtoLocalDataProperty localProperty = new DtoLocalDataProperty();
        localProperty.setAnalyseData(anaMapList);
        localProperty.setParamsConfig(testFormulaParamsConfigs);
        localProperty.setIsAlterFormula(true);

        String comment = String.format("更新公式为:%s", formula);
        if (StringUtils.isNotNullAndEmpty(oldFormula)) {
            if (!oldFormula.equals(formula)) {
                comment = String.format("公式由:%s,更新为:%s", oldFormula, formula);
            }
        }
        DtoLog log = new DtoLog();
        log.setComment(comment);
        log.setLogType(EnumPRO.EnumLogType.现场领样单数据.getValue());
        log.setObjectId(dtoAnalyseDataChangeFormula.getSubId());
        log.setObjectType(EnumPRO.EnumLogObjectType.现场领样单.getValue());
        log.setOperateInfo(EnumPRO.EnumLogOperateType.修改现场领样单.name());
        newLogService.createLog(log);

        return localProperty;
    }

    /**
     * 获取领样单id下的现场数据
     *
     * @param subId  领样单id
     * @param module 模块
     * @param type   状态
     * @return 返回对应领样单下的现场数据
     */
    @Override
    public List<DtoLocalDataProperty> findLocalData(String subId, String module, Integer type, String sortId, String key) {
        //所有的分析数据集
        List<DtoAnalyseDataTemp> analyseDataTemps = findAnalyseData(subId);
        this.setGroupSampleId(analyseDataTemps);
        if (StringUtils.isNotNullAndEmpty(key)) {
            analyseDataTemps = analyseDataTemps.stream().filter(a -> a.getRedAnalyzeItemName().contains(key)).collect(Collectors.toList());
        }

        //分析数据ids
        List<String> anaAllIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());

        //所有的测试项目ids
        List<String> testIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());

        //相关的测试项目
        List<DtoTest> dtoTests = testService.findRedisByIds(testIds);

        //启用公式的测试项目
        List<String> tIds = dtoTests.stream().filter(p -> StringUtil.isNotNull(p.getIsUseFormula()) && p.getIsUseFormula()).map(DtoTest::getId).collect(Collectors.toList());

        List<DtoAnalyzeItemSortDetail> sortDetails = analyzeItemSortDetailRepository.findBySortId(sortId);

        //要找到测试项目的公式（最新的配置的数据，已排除加删及过滤了不启用公式的数据）
        List<DtoParamsFormula> formulaList = new ArrayList<>();
        List<DtoParamsTestFormula> paramsTestFormulas = new ArrayList<>();
        if (tIds.size() > 0) {
            formulaList = paramsFormulaService.findByObjectIds(tIds);
            //所有的公式ids
            List<String> fIds = formulaList.stream().map(DtoParamsFormula::getId).distinct().collect(Collectors.toList());
            paramsTestFormulas = paramsTestFormulaService.findByObjIds(fIds);
        }

        //分析数据相关的公式数据
        List<DtoAnalyseOriginalRecord> analyseOriginalRecords = new ArrayList<>();
        if (anaAllIds.size() > 0) {
            analyseOriginalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(anaAllIds);
        }

        List<Map<String, Object>> anaMapList = new ArrayList<>();
        Map<String, List<DtoTestFormulaParamsConfig>> testFormulaParamsConfigMap = new HashMap<>();

        Map<String, String> codeMap = analyseDataTemps.stream().
                collect(Collectors.groupingBy(DtoAnalyseDataTemp::getSampleId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSampleCode())));
        Map<String, List<String>> orderNum2SmpIdListMap = new HashMap<>();
        for (DtoAnalyseDataTemp dtoAnalyseDataTemp : analyseDataTemps) {
            //给数据加入排序值
            Map<String, Object> map = workSheetFolderService.getAnalyseDataMap(analyseDataTemps, dtoAnalyseDataTemp, codeMap);
            String orderNum = map.get("orderNum").toString();
            String smpId = map.get("sampleId").toString();
            map.put("sortNumber", 0);
            if (!orderNum2SmpIdListMap.containsKey(orderNum)) {
                orderNum2SmpIdListMap.put(orderNum, new ArrayList<>());
            }
            if (!orderNum2SmpIdListMap.get(orderNum).contains(smpId)) {
                orderNum2SmpIdListMap.get(orderNum).add(smpId);
            }
        }
        for (DtoAnalyseDataTemp dtoAnalyseDataTemp : analyseDataTemps) {
            String anaId = dtoAnalyseDataTemp.getId();
            String testId = dtoAnalyseDataTemp.getTestId();
            String sampleTypeId = dtoAnalyseDataTemp.getSampleTypeId();
            String bigSampleTypeId = dtoAnalyseDataTemp.getBigSampleTypeId();
            String formula = "";
            String formulaId = UUIDHelper.GUID_EMPTY;
            Integer sortNumber = 0;
            Optional<DtoAnalyzeItemSortDetail> detail = sortDetails.stream().filter(p -> p.getAnalyzeItemId().equals(dtoAnalyseDataTemp.getAnalyseItemId())).findFirst();
            if (detail.isPresent()) {
                sortNumber = detail.get().getOrderNum();
            }
            //优先判断数据上是否存储过公式，如果存储过，那么就是用该公式
            //如果未存储过，否启用公式
            //如果启用，判断数据上是否配置小类公式，
            //如果小类未配置公式，判断大类是否配置公式
            List<DtoParamsTestFormula> paramsTestFormulaList = new ArrayList<>();
            DtoAnalyseOriginalRecord analyseOriginalRecord = analyseOriginalRecords.stream()
                    .filter(p -> p.getAnalyseDataId().equals(anaId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(analyseOriginalRecord)) {
                TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
                };
                formula = analyseOriginalRecord.getTestFormula();
                formulaId = analyseOriginalRecord.getTestFormulaId();
                paramsTestFormulaList = JsonIterator.deserialize(analyseOriginalRecord.getJson(), typeLiteral);
            } else {
                DtoParamsFormula dtoParamsFormula = formulaList.stream()
                        .filter(p -> p.getSampleTypeId().equals(sampleTypeId) && p.getObjectId().equals(testId))
                        .max(Comparator.comparing(DtoParamsFormula::getConfigDate)).orElse(null);
                if (StringUtil.isNotNull(dtoParamsFormula)) { //小类是否有公式
                    formula = dtoParamsFormula.getFormula();
                    formulaId = dtoParamsFormula.getId();
                    paramsTestFormulaList = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(dtoParamsFormula.getId())).collect(Collectors.toList());
                } else { //大类是否有公式
                    DtoParamsFormula bigParamsFormula = formulaList.stream()
                            .filter(p -> p.getSampleTypeId().equals(bigSampleTypeId) && p.getObjectId().equals(testId))
                            .max(Comparator.comparing(DtoParamsFormula::getConfigDate)).orElse(null);
                    if (StringUtil.isNotNull(bigParamsFormula)) {
                        formula = bigParamsFormula.getFormula();
                        formulaId = bigParamsFormula.getId();
                        paramsTestFormulaList = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(bigParamsFormula.getId())).collect(Collectors.toList());
                    }
                }
            }
            dtoAnalyseDataTemp.setFormula(formula);
            dtoAnalyseDataTemp.setFormulaId(formulaId);

            Map<String, Object> map = this.getAnalyseDataMap(dtoAnalyseDataTemp);
            Map<String, Object> sortMap = workSheetFolderService.getAnalyseDataMap(analyseDataTemps, dtoAnalyseDataTemp, codeMap);
            map.put("orderNum", sortMap.get("orderNum"));
            if (StringUtils.isNotNullAndEmpty(formulaId) && !UUIDHelper.GUID_EMPTY.equals(formulaId)) {
                List<DtoTestFormulaParamsConfig> dtoTestFormulaParamsConfigList = new ArrayList<>();
                if (testFormulaParamsConfigMap.containsKey(formulaId)) {
                    dtoTestFormulaParamsConfigList = testFormulaParamsConfigMap.get(formulaId);
                }
                //公式参数倒序排序
                paramsTestFormulaList = paramsTestFormulaList.stream().sorted(Comparator.comparing(DtoParamsTestFormula::getOrderNum).reversed())
                        .collect(Collectors.toList());
                for (DtoParamsTestFormula dtoParamsTestFormula : paramsTestFormulaList) {
                    map.put(dtoParamsTestFormula.getAlias(), dtoParamsTestFormula.getDefaultValue());
                    //参数中不包含该参数，需要加进来
                    if (dtoTestFormulaParamsConfigList.stream().filter(p -> p.getAlias().equals(dtoParamsTestFormula.getAlias()))
                            .collect(Collectors.toList()).size() == 0) {
                        DtoTestFormulaParamsConfig dtoTestFormulaParamsConfig = new DtoTestFormulaParamsConfig();
                        dtoTestFormulaParamsConfig.setOrderNum(dtoParamsTestFormula.getOrderNum());
                        dtoTestFormulaParamsConfig.setAlias(dtoParamsTestFormula.getAlias());
                        dtoTestFormulaParamsConfig.setIsMust(dtoParamsTestFormula.getIsMust());
                        dtoTestFormulaParamsConfig.setIsEditable(dtoParamsTestFormula.getIsEditable());
                        dtoTestFormulaParamsConfig.setIsCalculate(formula.contains("[" + dtoParamsTestFormula.getAlias() + "]"));
                        dtoTestFormulaParamsConfigList.add(dtoTestFormulaParamsConfig);
                    }
                }
                testFormulaParamsConfigMap.put(formulaId, dtoTestFormulaParamsConfigList);
            }
            map.put("sortNumber", sortNumber);
            anaMapList.add(map);
        }
        List<String> fList = analyseDataTemps.stream().sorted(Comparator.comparing(DtoAnalyseDataTemp::getFormulaId)).map(DtoAnalyseDataTemp::getFormulaId).distinct().collect(Collectors.toList());
        List<DtoParamsPartFormula> allParamsPartFormulaList = StringUtil.isNotEmpty(fList) ? paramsPartFormulaRepository.findByFormulaIdIn(fList) : new ArrayList<>();
        Map<String, List<DtoParamsPartFormula>> paramsPartFormulaMap = allParamsPartFormulaList.stream().collect(Collectors.groupingBy(DtoParamsPartFormula::getFormulaId));

        //拆分相应的选项卡
        List<DtoLocalDataProperty> localProperties = new ArrayList<>();
        for (String formulaId : fList) {
            DtoLocalDataProperty localProperty = new DtoLocalDataProperty();
            List<DtoAnalyseDataTemp> formulaDataAllList = analyseDataTemps.stream().filter(p -> formulaId.equals(p.getFormulaId())).collect(Collectors.toList());

            //所有的数据
            List<DtoAnalyseDataTemp> formulaDataList = formulaDataAllList;
            List<Map<String, Object>> mapList = anaMapList.stream().filter(p -> formulaId.equals(p.get("formulaId"))).collect(Collectors.toList());
            List<Integer> dataStatusList = new ArrayList<>();
            if (module.equals(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue())) {
                if (type.equals(EnumPRO.EnumStatus.待处理.getValue())) {
                    dataStatusList = Arrays.asList(EnumPRO.EnumAnalyseDataStatus.未测.getValue(), EnumPRO.EnumAnalyseDataStatus.在测.getValue(), EnumPRO.EnumAnalyseDataStatus.拒绝.getValue());
                } else if (type.equals(EnumPRO.EnumStatus.已处理.getValue())) {
                    dataStatusList = Arrays.asList(EnumPRO.EnumAnalyseDataStatus.已测.getValue(), EnumPRO.EnumAnalyseDataStatus.复核通过.getValue());
                }
            } else if (module.equals(EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue())) {
                if (type.equals(EnumPRO.EnumStatus.待处理.getValue())) {
                    dataStatusList = Collections.singletonList(EnumPRO.EnumAnalyseDataStatus.已测.getValue());
                } else if (type.equals(EnumPRO.EnumStatus.已处理.getValue())) {
                    dataStatusList = Collections.singletonList(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue());
                } else {
                    dataStatusList = Arrays.asList(EnumPRO.EnumAnalyseDataStatus.已测.getValue(), EnumPRO.EnumAnalyseDataStatus.复核通过.getValue());
                }
            } else if (module.equals(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue())) {
                dataStatusList = Collections.singletonList(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue());
                if (type.equals(EnumPRO.EnumStatus.待处理.getValue())) {
                    formulaDataList = formulaDataList.stream().filter(p -> !p.getIsDataEnabled()).collect(Collectors.toList());
                    mapList = mapList.stream().filter(p -> !((Boolean) p.get("isDataEnabled"))).collect(Collectors.toList());
                } else if (type.equals(EnumPRO.EnumStatus.已处理.getValue())) {
                    formulaDataList = formulaDataList.stream().filter(DtoAnalyseDataTemp::getIsDataEnabled).collect(Collectors.toList());
                    mapList = mapList.stream().filter(p -> (Boolean) p.get("isDataEnabled")).collect(Collectors.toList());
                }
            }
            if (dataStatusList.size() > 0) {
                List<Integer> dataStatuss = dataStatusList;
                formulaDataList = formulaDataList.stream().filter(p -> dataStatuss.contains(p.getDataStatus())).collect(Collectors.toList());
                mapList = mapList.stream().filter(p -> dataStatuss.contains((Integer) p.get("dataStatus"))).collect(Collectors.toList());
            }
            if (mapList.size() == 0) {
                continue;
            }
            //是否能更换公式，判断当前选项卡数据个数是否完整
            Boolean isAlter = formulaDataList.size() == formulaDataAllList.size();
            Map<String, Object> testInfo = new HashMap<>();
            testInfo.put("redAnalyzeItemName", "数据录入");
            String testId = UUIDHelper.GUID_EMPTY;//测试项目id
            String formula = "";//公式id
            if (formulaDataList.size() > 0 && formulaDataList.stream().anyMatch(p -> !p.getFormulaId().equals(UUIDHelper.GUID_EMPTY))) {
                DtoAnalyseDataTemp dtoAnalyseDataTemp = formulaDataList.get(0);
                testId = dtoAnalyseDataTemp.getTestId();
                testInfo.put("redAnalyzeItemName", dtoAnalyseDataTemp.getRedAnalyzeItemName());
                formula = dtoAnalyseDataTemp.getFormula();
            }
            testInfo.put("formulaId", formulaId);
            testInfo.put("formula", formula);
            testInfo.put("testId", testId);
            testInfo.put("paramsFormulaList", new ArrayList<>());
            if (paramsPartFormulaMap.containsKey(formulaId)) {
                testInfo.put("paramsFormulaList", paramsPartFormulaMap.get(formulaId).stream()
                        .filter(v -> StringUtil.isNotEmpty(v.getFormula())).map(DtoParamsPartFormula::getFullFormula).collect(Collectors.toList()));
            }
            List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs = new ArrayList<>();
            if (testFormulaParamsConfigMap.containsKey(formulaId)) {
                testFormulaParamsConfigs = testFormulaParamsConfigMap.get(formulaId).stream().sorted(Comparator.comparing(DtoTestFormulaParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
            }
            mapList = mapList.stream().sorted(Comparator.comparing(ReceiveSubSampleRecordServiceImpl::comparingByOrderNum).thenComparing(ReceiveSubSampleRecordServiceImpl::comparingBySampleCode)
                    .thenComparing(ReceiveSubSampleRecordServiceImpl::comparingByItemSort, Comparator.reverseOrder())).collect(Collectors.toList());
            localProperty.setAnalyseData(mapList);
            localProperty.setParamsConfig(testFormulaParamsConfigs);
            localProperty.setTest(testInfo);
            localProperty.setIsAlterFormula(isAlter);
            localProperties.add(localProperty);
        }

        return localProperties;
    }

    private List<DtoAnalyseDataTemp> findAnalyseData(String subId) {
        List<DtoAnalyseDataTemp> resultList = new ArrayList<>();
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2SampleList =
                receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(subId);
        if (StringUtil.isNotEmpty(receiveSubSampleRecord2SampleList)) {
            List<String> sampleIds = receiveSubSampleRecord2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId)
                    .distinct().collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findLocalData(sampleIds);
            resultList = findAnalyseData(analyseDataList, sampleIds);
        }
        return resultList;
    }

    private List<DtoAnalyseDataTemp> findAnalyseData(List<DtoAnalyseData> analyseDataList) {
        List<DtoAnalyseDataTemp> resultList = new ArrayList<>();
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId)
                    .distinct().collect(Collectors.toList());
            resultList = findAnalyseData(analyseDataList, sampleIds);
        }
        return resultList;
    }

    private List<DtoAnalyseDataTemp> findAnalyseData(List<DtoAnalyseData> analyseDataList, List<String> sampleIds) {
        List<String> qcIds = analyseDataList.stream().map(DtoAnalyseData::getQcId).collect(Collectors.toList());
        List<DtoQualityControl> qualityControls = qualityControlService.findAll(qcIds);
        List<DtoAnalyseDataTemp> resultList = new ArrayList<>();
        List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
        Set<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toSet());
        List<DtoSampleType> sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
        for (DtoAnalyseData dtoAnalyseData : analyseDataList) {
            DtoAnalyseDataTemp dtoAnalyseDataTemp = new DtoAnalyseDataTemp();
            BeanUtils.copyProperties(dtoAnalyseData, dtoAnalyseDataTemp);
            Optional<DtoSample> sampleOptional = sampleList.stream().filter(s -> s.getId().equals(dtoAnalyseDataTemp.getSampleId()))
                    .findFirst();
            sampleOptional.ifPresent(sample -> {
                dtoAnalyseDataTemp.setInspectedEntId(sample.getInspectedEntId());
                dtoAnalyseDataTemp.setInspectedEnt(sample.getInspectedEnt());
                dtoAnalyseDataTemp.setRedFolderName(sample.getRedFolderName());
                dtoAnalyseDataTemp.setSampleTime(sample.getSamplingTimeBegin());
                dtoAnalyseDataTemp.setSampleCode(sample.getCode());
                dtoAnalyseDataTemp.setCycleOrder(sample.getCycleOrder());
                dtoAnalyseDataTemp.setTimesOrder(sample.getTimesOrder());
                dtoAnalyseDataTemp.setSampleTypeId(sample.getSampleTypeId());
                dtoAnalyseDataTemp.setReceiveId(sample.getReceiveId());
                dtoAnalyseDataTemp.setSampleId(sample.getId());
                dtoAnalyseDataTemp.setSampleCategory(sample.getSampleCategory());
                dtoAnalyseDataTemp.setAssociateSampleId(sample.getAssociateSampleId());
                Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.stream()
                        .filter(s -> s.getId().equals(sample.getSampleTypeId())).findFirst();
                sampleTypeOptional.ifPresent(t -> {
                    dtoAnalyseDataTemp.setBigSampleTypeId(t.getParentId());
                    dtoAnalyseDataTemp.setSampleTypeName(t.getTypeName());
                });
            });
            Optional<DtoQualityControl> qualityControl = qualityControls.stream().filter(q -> q.getId().equals(dtoAnalyseData.getQcId())).findFirst();
            qualityControl.ifPresent(qc -> {
                dtoAnalyseDataTemp.setQcCode(qc.getQcCode());
                dtoAnalyseDataTemp.setQcTestValue(qc.getQcTestValue());
                dtoAnalyseDataTemp.setQcTestValueDimensionId(qc.getQcTestValueDimensionId());
                dtoAnalyseDataTemp.setQcConcentration(qc.getQcConcentration());
                dtoAnalyseDataTemp.setQcConcentrationDimensionId(qc.getQcConcentrationDimensionId());
                dtoAnalyseDataTemp.setQcStandardId(qc.getQcStandardId());
                dtoAnalyseDataTemp.setQcStandardDate(qc.getQcStandardDate());
                dtoAnalyseDataTemp.setQcValidDate(qc.getQcValidDate());
                dtoAnalyseDataTemp.setQcValue(qc.getQcValue());
                dtoAnalyseDataTemp.setQcVolume(qc.getQcVolume());
                dtoAnalyseDataTemp.setUncertainType(qc.getUncertainType());
                dtoAnalyseDataTemp.setQcValueDimensionId(qc.getQcValueDimensionId());
                dtoAnalyseDataTemp.setQcVolumeDimensionId(qc.getQcVolumeDimensionId());

            });
            //dtoAnalyseDataTemp.setSampleId(dtoAnalyseData.getSampleId());
            resultList.add(dtoAnalyseDataTemp);
            resultList.sort(Comparator.comparing(DtoAnalyseDataTemp::getSampleCode));
        }
        return resultList;
    }


    /**
     * 获取领样单id下的现场数据
     *
     * @param subId 领样单id
     * @return 返回对应领样单下的现场数据
     */
    @Override
    public List<DtoAnalyseData> findLocalDataBySubId(String subId) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoAnalyseData> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a, DtoSample b,DtoReceiveSubSampleRecord2Sample r2s");
        pb.setSelect("select a");
        pb.addCondition(" and b.isDeleted = 0  and a.isDeleted = 0 and r2s.isDeleted = 0 ");
        pb.addCondition(" and a.sampleId = b.id");
        pb.addCondition(" and b.id = r2s.sampleId");
        pb.addCondition(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        pb.addCondition(" and a.isCompleteField = 1");
        pb.addCondition(" and r2s.receiveSubSampleRecordId = :subId");
        values.put("subId", subId);
        return comRepository.find(pb.getAutoQuery(), values);
    }

    private void setGroupSampleId(List<DtoAnalyseDataTemp> analyseDataTemps) {
        for (DtoAnalyseDataTemp analyseDataTemp : analyseDataTemps) {
            if (StringUtils.isNotNullAndEmpty(analyseDataTemp.getAssociateSampleId()) &&
                    !UUIDHelper.GUID_EMPTY.equals(analyseDataTemp.getAssociateSampleId()) &&
                    !analyseDataTemp.getQcType().equals(new QualityBlank().qcTypeValue())) {
                analyseDataTemp.setGroupSampleId(analyseDataTemp.getAssociateSampleId());
            } else {
                analyseDataTemp.setGroupSampleId(analyseDataTemp.getSampleId());
            }
        }
    }
    //#endregion

    //#region 样品分配

    /**
     * 获取样品分配列表
     */
    @Override
    public void findAssignByPage(PageBean<DtoReceiveSubSampleRecord> pb, BaseCriteria receiveSubSampleRecordCriteria) {
        pb.setEntityName("DtoReceiveSubSampleRecord s,DtoReceiveSampleRecord r,DtoProject p");
        pb.setSelect("select s,r.recordCode,r.senderId,r.senderName,r.sendTime,r.samplingTime,p.projectCode, p.projectName, p.projectTypeId,p.customerId, p.customerName" +
                ",p.grade,p.inceptTime,p.isStress,p.linkMan,p.linkPhone," +
                "json_value(r.json,'$.labSampleTypes') as sampleTypeNames," +
                "json_value(r.json,'$.sampleTypeIds') as sampleTypeIds");
        pb.setSort(" bitand(s.subStatus , " + EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue() + "), r.createDate desc");
        pb.setSortOrder("");
        ReceiveSubSampleRecordCriteria subSampleRecordCriteria = (ReceiveSubSampleRecordCriteria) receiveSubSampleRecordCriteria;
        subSampleRecordCriteria.setIsShowCustomer(getIsShowCustomer());
        comRepository.findByPage(pb, subSampleRecordCriteria);

        Iterator<DtoReceiveSubSampleRecord> ite = pb.getData().iterator();
        // 循环迭代获取JPQL中查询返回的属性

        List<DtoReceiveSubSampleRecord> newDatas = new ArrayList<>();//频次指标集合
        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            DtoReceiveSubSampleRecord sub = (DtoReceiveSubSampleRecord) objs[0];
            sub.setRecordCode((String) objs[1]);
            sub.setSenderId((String) objs[2]);
            sub.setSenderName((String) objs[3]);
            sub.setSendTime((Date) objs[4]);
            sub.setSamplingTime((Date) objs[5]);
            sub.setProjectCode((String) objs[6]);
            sub.setProjectName((String) objs[7]);
            sub.setProjectTypeId((String) objs[8]);
            sub.setCustomerId((String) objs[9]);
            sub.setCustomerName((String) objs[10]);
            sub.setGrade((Integer) objs[11]);
            sub.setInceptTime((Date) objs[12]);
            sub.setIsStress((Boolean) objs[13]);
            sub.setLinkMan((String) objs[14]);
            sub.setLinkPhone((String) objs[15]);
            sub.setSampleTypeNames((String) objs[16]);
            if (!StringUtil.isNotEmpty(sub.getSampleTypeNames()) || sub.getSampleTypeNames().equals("null")) {
                String sampleTypeIds = (String) objs[17];
                if (StringUtil.isNotEmpty(sampleTypeIds)) {
                    List<String> sampleType = sampleTypeService.findRedisByIds(Arrays.asList(sampleTypeIds.split(",")))
                            .stream().map(DtoSampleType::getTypeName).collect(Collectors.toList());
                    sub.setSampleTypeNames(String.join(",", sampleType));
                }
            }
            sub.setReceiveSubstatus((sub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) > 0 ? EnumPRO.EnumAssignStatus.已分配.toString() : EnumPRO.EnumAssignStatus.未分配.toString());
            newDatas.add(sub);
        }

        //筛选项目类型，重新赋值项目类型名称
        List<String> projectTypeIds = newDatas.stream().map(DtoReceiveSubSampleRecord::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = projectTypeIds.size() > 0 ? projectTypeService.findRedisByIds(projectTypeIds) : new ArrayList<>();
        Map<String, String> projectTypeMap = projectTypes.stream().collect(Collectors.toMap(DtoProjectType::getId, DtoProjectType::getName));
        for (DtoReceiveSubSampleRecord sub : newDatas) {
            sub.setProjectTypeName(projectTypeMap.getOrDefault(sub.getProjectTypeId(), ""));
        }

        DtoCode isShowFolder = codeService.findByCode("PRO_IS_SHOW_FOLDER");
        if (StringUtil.isNotNull(isShowFolder)) {
            if ("1".equals(isShowFolder.getDictValue())) {
                newDatas.forEach(p -> {
                    p.setCustomerId("/");
                    p.setCustomerName("/");
                });
            }
        }

        pb.setData(newDatas);
    }

    /**
     * 核查能否分配领样单
     *
     * @param ids 领样单id集合
     */
    @Override
    public void canAssignReceiveSubRecord(List<String> ids) {
        Map<String, List<String>> msgMap = canSubRecord(ids);
        if (msgMap.size() > 0) {
            //读取存在未设置分析人的领样单
            List<String> msgList = new ArrayList<>();
            List<DtoReceiveSubSampleRecord> subRecords = repository.findAll(msgMap.keySet());
            //进行遍历，组装信息
            for (DtoReceiveSubSampleRecord sub : subRecords) {
                msgList.add(String.format("%s中的指标：%s", sub.getCode().replace("-FX", ""), String.join(",", msgMap.get(sub.getId()))));
            }
            //抛出给前端进行提示
            throw new BaseException(String.format("以下送样单</br>%s未设置完分析人员，无法进行样品分配！", String.join("</br>", msgList)));
        }
    }

    @Override
    public Map<String, List<String>> canSubRecord(List<String> ids) {
        List<DtoReceiveSubSampleRecord> subSampleRecords = repository.findAll(ids);
        //判断是否处在方案申请变更流程中
        List<String> projectIds = subSampleRecords.stream().map(DtoReceiveSubSampleRecord::getProjectId).distinct().collect(Collectors.toList());
        projectApprovalService.checkCondition(projectIds);
        //数据库读取领样单下样品的未分包的实验室数据中未分配分析人的数据
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select distinct r2s.receiveSubSampleRecordId,a.redAnalyzeItemName");
        stringBuilder.append(" from DtoAnalyseData a,DtoReceiveSubSampleRecord2Sample r2s where 1=1");
        stringBuilder.append(" and a.sampleId = r2s.sampleId");
        stringBuilder.append(" and r2s.receiveSubSampleRecordId in :receiveSubSampleRecordIds");
        stringBuilder.append(" and a.isDeleted = 0 and r2s.isDeleted = 0 ");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and a.isCompleteField = 0");
        stringBuilder.append(" and (a.analystId = '00000000-0000-0000-0000-000000000000' or a.analystId = '')");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        values.put("receiveSubSampleRecordIds", ids);
        List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);

        //按照领样单id添加Map对象，形成领样单id与未设置分析人的指标集合的关联
        Map<String, List<String>> msgMap = new HashMap<>();
        for (Object[] data : datas) {
            String receiveSubSampleRecordId = (String) data[0];
            String redAnalyzeItemName = (String) data[1];
            if (!msgMap.containsKey(receiveSubSampleRecordId)) {
                msgMap.put(receiveSubSampleRecordId, new ArrayList<>());
            }
            msgMap.get(receiveSubSampleRecordId).add(redAnalyzeItemName);
        }
        return msgMap;
    }

    /**
     * 样品分配
     *
     * @param subIds 领样单id集合
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void assign(List<String> subIds) {
        //进行遍历
        for (String subId : subIds) {
            assign(subId);
        }
    }

    /**
     * 样品分配
     *
     * @param subId 领样单id
     */
    @Transactional
    public void assign(String subId) {
        //获取领样单下的样品
        List<DtoSample> samples = this.findSampleBySubId(subId);

        //筛选为未领取的样品
        samples = samples.stream().filter(p -> p.getInnerReceiveStatus() < EnumPRO.EnumInnerReceiveStatus.已经领取.getValue()).collect(Collectors.toList());
        //进行样品领取状态修改
        this.assignSamples(samples);

        //获取领样单并修改领样单状态
        DtoReceiveSubSampleRecord subRecord = repository.findOne(subId);
        this.assignReceiveSubSampleRecord(subRecord);

        //进行样品分配的日志插入
        this.assignSampleLogs(samples);

        //数据库读取领取样品对应数据的所有分析人，进行实验室缓存
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select distinct a.analystId");
        stringBuilder.append(" from DtoAnalyseData a,DtoReceiveSubSampleRecord2Sample r2s where 1=1");
        stringBuilder.append(" and a.sampleId = r2s.sampleId");
        stringBuilder.append(" and r2s.receiveSubSampleRecordId = :receiveSubSampleRecordId");
        stringBuilder.append(" and a.isDeleted = 0 and r2s.isDeleted = 0 ");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and a.isCompleteField = 0");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        values.put("receiveSubSampleRecordId", subId);
        List<String> analystIds = comRepository.find(stringBuilder.toString(), values);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.样品分配, "", "", analystIds);
                    }
                }
        );
    }

    /**
     * 查询领样单下的样品分析人员\方法分配
     *
     * @param id       领样单id
     * @param isPerson 是否查询人员
     * @return 分析人员\方法分配
     */
    @Override
    public DtoSampleAssignTemp findSampleAssignInfoById(String id, Boolean isPerson) {
        //获取领样单下的样品并进行排序
        List<DtoSample> samples = this.findSampleBySubId(id);
        samples.sort(Comparator.comparing(DtoSample::getCode));
        //获取是否显示敏感信息
        DtoCode isShowFolder = codeService.findByCode("PRO_IS_SHOW_FOLDER");
        //获取领样单样品下的实验室分析数据
        List<DtoAnalyseData> analyseDataList = this.findAnalyseDataBySubId(id);

        Boolean isQM = this.findIsQM(id);
        if (isPerson) {
            return isQM ? this.findQMPerson(analyseDataList, samples, isShowFolder) : this.findPerson(analyseDataList, samples, isShowFolder);
        }
        return isQM ? this.findQMMethod(analyseDataList, samples, isShowFolder) : this.findMethod(analyseDataList, samples, isShowFolder);
    }

    /**
     * 修改人员
     *
     * @param paramList 传参实体
     */
    @Transactional
    @Override
    public void changePerson(List<DtoSampleAssignParam> paramList) {
        //遍历传参，纳入所需更改的样品和人员的集合，纳入测试项目id的范围
        Set<String> sampleIdSet = new HashSet<>();
        Set<String> personIdSet = new HashSet<>();
        List<String> testIds = new ArrayList<>();
        for (DtoSampleAssignParam param : paramList) {
            sampleIdSet.addAll(param.getSampleIds());
            personIdSet.add(param.getAnalystId());
            testIds.add(param.getTestId());
        }

        if (sampleIdSet.size() > 0) {
            //读取所有人员
            List<DtoPerson> personList = personIdSet.size() > 0 ? personService.findAllDeleted(new ArrayList<>(personIdSet)) : new ArrayList<>();

            List<DtoAnalyseData> analyseDataList = this.findAnalyseDataBySampleIds(new ArrayList<>(sampleIdSet), null, testIds);
            //遍历传参，筛选出来为当前样品下或不为对应测试项目id的数据，最终保证涵盖的指标与指标对应的数据与前端所选为一致的
            for (DtoSampleAssignParam param : paramList) {
                analyseDataList = analyseDataList.stream().filter(p -> (param.getSampleIds().contains(p.getSampleId())
                                && p.getTestId().equals(param.getTestId())) || !p.getTestId().equals(param.getTestId()))
                        .collect(Collectors.toList());
            }
            //读取样品数据
            List<DtoSample> samples = sampleRepository.findAll(sampleIdSet);

            //修改分析数据的分析人员
            this.changeAnalysePerson(paramList, analyseDataList, personList);

            //插入修改分析人员的日志数据
            this.createChangePersonLogs(paramList, analyseDataList, samples, personList);

            //提取出涉及修改的人员，最终需进行通知进行实验室缓存的修改
            Set<String> analystSet = analyseDataList.stream().map(DtoAnalyseData::getAnalystId).collect(Collectors.toSet());
            analystSet.addAll(personIdSet);

            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            String projectId = "";
                            if (samples.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId()))) {
                                projectId = samples.stream().sorted(Comparator.comparing(DtoSample::getProjectId).reversed()).collect(Collectors.toList()).get(0).getProjectId();
                            }
                            proService.sendProMessage(EnumPRO.EnumProAction.更换人员, projectId, "", new ArrayList<>(analystSet));
                        }
                    }
            );
        }
    }

    /**
     * 检查样品状态，筛选出在检测单内或已确认的数据，这些数据无法进行修改
     *
     * @param paramList 传参实体
     * @return
     */
    @Override
    public List<String> checkSampleStatus(List<DtoSampleAssignParam> paramList) {

        Set<String> sampleIdSet = new HashSet<>();
        List<String> testIds = new ArrayList<>();
        for (DtoSampleAssignParam param : paramList) {
            sampleIdSet.addAll(param.getSampleIds());
            testIds.add(param.getTestId());
        }

        List<DtoAnalyseData> analyseDataList = this.findAnalyseDataBySampleIds(new ArrayList<>(sampleIdSet), null, testIds);
        //遍历传参，筛选出来为当前样品下或不为对应测试项目id的数据，最终保证涵盖的指标与指标对应的数据与前端所选为一致的
        for (DtoSampleAssignParam param : paramList) {
            analyseDataList = analyseDataList.stream().filter(p -> (param.getSampleIds().contains(p.getSampleId())
                            && p.getTestId().equals(param.getTestId())) || !p.getTestId().equals(param.getTestId()))
                    .collect(Collectors.toList());
        }
        //读取样品数据
        //筛选出在检测单内或已确认的数据，这些数据无法进行修改
        List<String> samIds = analyseDataList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId())
                        || p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).
                map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        return samIds;
    }


    /**
     * 修改方法
     *
     * @param param 传参实体
     */
    @Transactional
    @Override
    public void changeMethod(DtoSampleAssignParam param) {
        DtoTest newTest = testService.findOne(param.getNewTestId());
        //获取新分析方法id
        String analyzeMethodIds = newTest.getAnalyzeMethodId();

        if (StringUtil.isNotNull(param.getIsChangeAll()) && param.getIsChangeAll()) {
            //判断是否按岗位分配
            DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
            if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
                //获取旧的分析方法
                String oldTestId = param.getOldMethodId();
                DtoTest oldTest = testService.findOne(oldTestId);
                if (StringUtil.isNull(oldTest)) {
                    throw new BaseException("测试项目不存在!");
                }
                String oldMethodId = oldTest.getAnalyzeMethodId();
                //获取同方法的测试项目对应的所有待检测分析数据,并过滤出同方法的分析数据
                List<DtoAnalyseData> sameMethodDataList = fetchAnaDataBySameMethod(oldMethodId);
                List<String> sameAnaItemIdList = sameMethodDataList.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
                List<String> oriAnaItemIdList = param.getAnalyseItemIds();
                for (String anaItemId : sameAnaItemIdList) {
                    if (!oriAnaItemIdList.contains(anaItemId)) {
                        oriAnaItemIdList.add(anaItemId);
                    }
                }
                param.setAnalyseItemIds(oriAnaItemIdList);
                List<String> sameSampleIdList = sameMethodDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<String> oriSampleIdList = param.getSampleIds();
                for (String sampleId : sameSampleIdList) {
                    if (!oriSampleIdList.contains(sampleId)) {
                        oriSampleIdList.add(sampleId);
                    }
                }
                param.setSampleIds(oriSampleIdList);
            }
        }

        //判断是否处在方案申请变更流程中
        List<String> projectIds = sampleRepository.findAll(param.getSampleIds()).stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
        projectApprovalService.checkCondition(projectIds);

        //需要更新的分析项目id列表
        List<String> analyzeItemIds = param.getAnalyseItemIds();
        //获取需要更新的分析项目id对应的测试项目信息
        List<DtoTest> mappedTestList = testRepository.findByAnalyzeItemIdInAndIsDeletedFalse(analyzeItemIds);
        //获取需要更新的分析项目id对应的分析方法
        Map<String, List<DtoTest>> analyzeItemMethodMap = mappedTestList.parallelStream().collect(Collectors.groupingBy(DtoTest::getAnalyzeItemId));
        //遍历分析项目，把没有当前新分析方法的分析项目过滤掉
        List<String> updateAnalyzeItemIds = new ArrayList<>();
        for (String analyzeItemId : analyzeItemMethodMap.keySet()) {
            List<DtoTest> thisTestList = analyzeItemMethodMap.get(analyzeItemId);
            List<String> thisAnalyzeMethodIds = thisTestList.parallelStream().map(DtoTest::getAnalyzeMethodId).collect(Collectors.toList());
            if (thisAnalyzeMethodIds.contains(newTest.getAnalyzeMethodId())) {
                updateAnalyzeItemIds.add(analyzeItemId);
            }
        }
        param.setAnalyseItemIds(updateAnalyzeItemIds);
        if (StringUtil.isNotNull(newTest) && param.getSampleIds().size() > 0) {
            //根据样品读取分析数据，并筛选为需要修改方法的数据
            List<DtoAnalyseData> analyseDataList = this.findAnalyseDataBySampleIds(param.getSampleIds(), param.getAnalyseItemIds(), new ArrayList<>());
            //analyseDataList = analyseDataList.stream().filter(p -> !p.getTestId().equals(newTest.getId())).collect(Collectors.toList());
            //读取样品数据
            List<DtoSample> samples = sampleRepository.findAll(param.getSampleIds());
            //筛选出在检测单内或已确认的数据，这些数据无法进行修改
            Set<String> samIds = analyseDataList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId()) || p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).
                    map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());

            if (samIds.size() > 0) {
                //抛出给前端
                List<DtoSample> inTestSamples = samples.stream().filter(p -> samIds.contains(p.getId())).collect(Collectors.toList());
                String msg = String.join(",", inTestSamples.stream().sorted(Comparator.comparing(DtoSample::getCode)).map(DtoSample::getCode).collect(Collectors.toSet()));
                throw new BaseException(msg + "分析数据已经存在检测单中，如要更换方法请先删除检测单后修改！");
            }

            //获取需要修改的数据的老的TestId
            if (StringUtil.isNotEmpty(analyseDataList)) {
                List<String> oldTestIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
                this.changeFrequencyTestMethod(samples, oldTestIds, analyzeMethodIds);
            }
            // 判断统一样品中是否已存在待修改的测试项目
            for (DtoSample sample : samples) {

            }

            //进行分析数据方法的更换
            List<String> analystIds = this.changeAnalyseMethod(samples, analyseDataList, newTest);

            // 更新样品分组表关联测试项目冗余的分析方法
            this.changeSampleGroup2Test(param, analyseDataList, newTest);

            //插入方法更换的日志
            this.createChangeMethodLogs(param, analyseDataList, samples, newTest);

            //合并修改前的分析人集合
            analystIds.addAll(analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()));

            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            String projectId = "";
                            if (samples.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId()))) {
                                projectId = samples.stream().sorted(Comparator.comparing(DtoSample::getProjectId).reversed()).collect(Collectors.toList()).get(0).getProjectId();
                            }
                            proService.sendProMessage(EnumPRO.EnumProAction.更换方法, projectId, "", analystIds.stream().distinct().collect(Collectors.toList()));
                        }
                    }
            );
        }
    }

    //#endregion

    /**
     * 根据领样单查询所选样品的指标类型
     *
     * @param sampleIds 样品id
     * @return 样品指标类型
     */
    @Override
    public List<DtoSampleTestType> findSampleTestType(List<String> sampleIds) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select r2s.sampleId,r.subStatus");
        stringBuilder.append(" from DtoReceiveSubSampleRecord2Sample r2s,DtoReceiveSubSampleRecord r where 1=1");
        stringBuilder.append(" and r2s.receiveSubSampleRecordId = r.id");
        stringBuilder.append(" and r2s.isDeleted = 0 and r.isDeleted = 0 ");
        stringBuilder.append(" and r2s.sampleId in :sampleIds");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and r.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        values.put("sampleIds", sampleIds);
        List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);

        List<DtoSampleTestType> sampleTestTypes = sampleIds.stream().map(p -> new DtoSampleTestType(p, EnumPRO.EnumSampleTestType.无指标.getValue())).collect(Collectors.toList());
        for (DtoSampleTestType stt : sampleTestTypes) {
            List<Integer> subStatuss = datas.stream().filter(p -> p[0].equals(stt.getSampleId())).map(p -> (Integer) p[1]).collect(Collectors.toList());
            for (Integer subStatus : subStatuss) {
                if ((subStatus & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0) {
                    stt.setType(stt.getType() + EnumPRO.EnumSampleTestType.实验室指标.getValue());
                } else if ((subStatus & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0) {
                    stt.setType(stt.getType() + EnumPRO.EnumSampleTestType.现场指标.getValue());
                }
            }
        }
        return sampleTestTypes;
    }

    //#region 私有方法


    /**
     * 关联修改频次方法
     *
     * @param samples            样品
     * @param oldTests           老测试项目
     * @param newAnalyzeMethodId 修改的方法
     */
    private void changeFrequencyTestMethod(List<DtoSample> samples, List<String> oldTests, String newAnalyzeMethodId) {
        //获取样品对应的点位Id
        List<String> sampleFolderIds = samples.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toList());
        //获取样品对应的频率id
        List<String> SamplingFrequencyIds = samples.stream().map(DtoSample::getSamplingFrequencyId).collect(Collectors.toList());

        //查询样品关联的频次记录
        List<DtoSamplingFrequencyTest> samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdInAndSamplingFrequencyIdIn(sampleFolderIds, SamplingFrequencyIds);

        //获取需要修改的数据
        samplingFrequencyTests = samplingFrequencyTests.stream().filter(p -> oldTests.contains(p.getTestId())).collect(Collectors.toList());

        //获取包含新方法的测试项目
        List<DtoTest> newTests = testRepository.findByAnalyzeMethodIdAndIsDeletedFalse(newAnalyzeMethodId);

        //修改方法
        samplingFrequencyTests.forEach(p -> {
            List<DtoTest> newTest = newTests.stream().filter(test -> p.getAnalyseItemId().equals(test.getAnalyzeItemId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(newTest)) {
                DtoTest test = newTest.get(0);
                p.setAnalyzeMethodId(test.getAnalyzeMethodId());
                p.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                p.setRedCountryStandard(test.getRedCountryStandard());
                p.setTestId(test.getId());
            }
        });
        if (StringUtil.isNotEmpty(samplingFrequencyTests)) {
            samplingFrequencyTestService.updateBatch(samplingFrequencyTests);
        }
        sampleJudgeDataService.updateJudgeDataByTest(samples.stream().map(DtoSample::getId).distinct().collect(Collectors.toList()),
                oldTests, newTests.stream().map(DtoTest::getId).distinct().collect(Collectors.toList()));
    }

    /**
     * 分配后修改样品状态
     *
     * @param samples 需领取的样品
     */
    private void assignSamples(List<DtoSample> samples) {
        List<DtoSample> sampleDatas = new ArrayList<>();
        for (DtoSample sample : samples) {
            DtoSample sampleData = new DtoSample();
            sampleData.setId(sample.getId());
            //领取状态改为已经领取
            sampleData.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
            if (sample.getStatus().equals(EnumPRO.EnumSampleStatus.样品未领样.toString()) || sample.getStatus().equals(EnumPRO.EnumSampleStatus.样品未采样.toString())) {
                //若样品状态在待检之前的则改为样品待检
                sampleData.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
            } else {
                //否则维持原状态
                sampleData.setStatus(sample.getStatus());
            }
            sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sampleData.setModifyDate(new Date());

            sampleDatas.add(sampleData);
        }
        if (sampleDatas.size() > 0) {
            //进行批量更新
            comRepository.updateBatch(sampleDatas);
        }
    }

    /**
     * 分配后修改领样单状态
     *
     * @param subRecord 领样单
     */
    protected void assignReceiveSubSampleRecord(DtoReceiveSubSampleRecord subRecord) {
        if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) == 0) {
            updateReceiveSubSampleRecordStatus(subRecord);
        }
    }

    /**
     * 修改领样单状态
     *
     * @param subRecord 领样单实体
     */
    private void updateReceiveSubSampleRecordStatus(DtoReceiveSubSampleRecord subRecord) {
        //实验室领样单状态修改
        subRecord.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue());
        subRecord.setStatus(EnumReceiveSubRecordStatusName.测试中.toString());
        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.SKIP_SAMPLE_RECEIVE);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            subRecord.setReceivePersonId(UUIDHelper.GUID_EMPTY);
            subRecord.setReceiveName("/");
        } else {
            subRecord.setReceivePersonId(PrincipalContextUser.getPrincipal().getUserId());
            subRecord.setReceiveName(PrincipalContextUser.getPrincipal().getUserName());
        }
        subRecord.setReceiveTime(new Date());
        repository.saveAndFlush(subRecord);

        //添加日志
        newLogService.createLog(subRecord.getId(), String.format("领取了实验室领样单%s相关样品。", subRecord.getCode()), "",
                EnumPRO.EnumLogType.实验室领样单流程.getValue(), EnumPRO.EnumLogObjectType.实验室领样单.getValue(), EnumPRO.EnumLogOperateType.实验室领样单领样.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //添加提交记录
        submitRecordService.createSubmitRecord(subRecord.getId(), EnumPRO.EnumSubmitObjectType.分析领样单.getValue(), EnumPRO.EnumSubmitType.样品分配提交.getValue(),
                "", "", "", "");
    }

    /**
     * 分配后插入日志数据
     *
     * @param samples 需领取的样品
     */
    private void assignSampleLogs(List<DtoSample> samples) {
        //遍历样品，插入样品领取的日志
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : samples) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.样品领取.toString());
            log.setLogType(EnumPRO.EnumLogType.样品流程.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
            log.setComment(String.format("对样品%s进行了送样领取", sampleService.getSampleName(sample, "")));
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumPRO.EnumLogType.样品流程.getValue());
        }
    }

    /**
     * 获取领样单下的样品
     *
     * @param subId 领样单id
     * @return 样品
     */
    private List<DtoSample> findSampleBySubId(String subId) {
        Map<String, Object> values = new HashMap<>();
        //获取领样单下的样品
        PageBean<DtoSample> pb = new PageBean<>();
        pb.setEntityName("DtoSample s,DtoReceiveSubSampleRecord2Sample r2s");
        pb.setSelect("select s");
        pb.addCondition(" and s.isDeleted = 0 and r2s.isDeleted = 0 ");
        pb.addCondition(" and s.id = r2s.sampleId");
        pb.addCondition(" and r2s.receiveSubSampleRecordId = :subId");
        values.put("subId", subId);
        return comRepository.find(pb.getAutoQuery(), values);
    }

    /**
     * 获取领样单下的样品数据
     *
     * @param subId 领样单id
     * @return 样品
     */
    private List<DtoAnalyseData> findAnalyseDataBySubId(String subId) {
        Map<String, Object> values = new HashMap<>();
        //获取领样单下的样品
        PageBean<DtoAnalyseData> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoReceiveSubSampleRecord2Sample r2s");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.DtoAnalyseData(a.id,a.workSheetFolderId,a.sampleId,a.sampleTypeId,a.testId,a.analyseItemId,a.redAnalyzeItemName," +
                "a.analyzeMethodId,a.redAnalyzeMethodName,a.redCountryStandard,a.analystId,a.analystName,a.status,a.qcGrade,a.isCompleteField,a.isOutsourcing,a.dataStatus,a.isDataEnabled)");
        pb.addCondition(" and a.isDeleted = 0 and r2s.isDeleted = 0 ");
        pb.addCondition(" and a.isCompleteField = 0");
        pb.addCondition(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        pb.addCondition(" and a.sampleId = r2s.sampleId");
        pb.addCondition(" and r2s.receiveSubSampleRecordId = :subId");
        values.put("subId", subId);
        return comRepository.find(pb.getAutoQuery(), values);
    }

    /**
     * 获取样品下的数据
     *
     * @param sampleIds 样品id集合
     * @return 数据
     */
    private List<DtoAnalyseData> findAnalyseDataBySampleIds(List<String> sampleIds, List<String> analyseItemIds, List<String> testIds) {
        //获取所选的样品的实验室非分包数据
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select new com.sinoyd.lims.pro.dto.DtoAnalyseData(a.id,a.workSheetFolderId,a.sampleId,a.sampleTypeId,a.testId,a.analyseItemId,a.redAnalyzeItemName," +
                "a.analyzeMethodId,a.redAnalyzeMethodName,a.redCountryStandard,a.analystId,a.analystName,a.status,a.qcGrade,a.isCompleteField,a.isOutsourcing,a.dataStatus,a.isDataEnabled)");
        stringBuilder.append(" from DtoAnalyseData a where 1=1");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and a.isCompleteField = 0");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and a.sampleId in :sampleIds");
        if (StringUtil.isNotEmpty(analyseItemIds)) {
            stringBuilder.append(" and a.analyseItemId in :analyseItemIds");
            values.put("analyseItemIds", analyseItemIds);
        }
        if (testIds.size() > 0) {
            stringBuilder.append(" and a.testId in :testIds");
            values.put("testIds", testIds);
        }
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        values.put("sampleIds", sampleIds);
        return comRepository.find(stringBuilder.toString(), values);
    }

    /**
     * 返回map 数据源
     *
     * @param dtoAnalyseDataTemp 分析数据
     * @return 返回map 数据源
     */
    private Map<String, Object> getAnalyseDataMap(DtoAnalyseDataTemp dtoAnalyseDataTemp) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", dtoAnalyseDataTemp.getId());
        map.put("analyzeItemId", dtoAnalyseDataTemp.getAnalyseItemId());
        map.put("analyzeMethodId", dtoAnalyseDataTemp.getAnalyzeMethodId());
        map.put("associateSampleId", dtoAnalyseDataTemp.getAssociateSampleId());
        map.put("dataInputTime", dtoAnalyseDataTemp.getDataInputTime());
        map.put("dimension", dtoAnalyseDataTemp.getDimension());
        map.put("dimensionId", dtoAnalyseDataTemp.getDimensionId());
        map.put("examLimitValue", dtoAnalyseDataTemp.getExamLimitValue());
        map.put("grade", dtoAnalyseDataTemp.getGrade());
        map.put("inspectedEnt", dtoAnalyseDataTemp.getInspectedEnt());
        map.put("inspectedEntId", dtoAnalyseDataTemp.getInspectedEntId());
        map.put("isQC", dtoAnalyseDataTemp.getIsQC());
        map.put("mostDecimal", dtoAnalyseDataTemp.getMostDecimal());
        map.put("mostSignificance", dtoAnalyseDataTemp.getMostSignificance());
        map.put("qcId", dtoAnalyseDataTemp.getQcId());
        map.put("qcGrade", dtoAnalyseDataTemp.getQcGrade());
        map.put("qcType", dtoAnalyseDataTemp.getQcType());
        map.put("sampleCategory", dtoAnalyseDataTemp.getSampleCategory());
        map.put("redAnalyzeItemName", dtoAnalyseDataTemp.getRedAnalyzeItemName());
        map.put("redAnalyzeMethodName", dtoAnalyseDataTemp.getRedAnalyzeMethodName());
        map.put("redCountryStandard", dtoAnalyseDataTemp.getRedCountryStandard());
        map.put("redFolderName", dtoAnalyseDataTemp.getRedFolderName());
        map.put("requireDeadLine", dtoAnalyseDataTemp.getRequireDeadLine());
        map.put("sampleCode", dtoAnalyseDataTemp.getSampleCode());
        map.put("sampleId", dtoAnalyseDataTemp.getSampleId());
        map.put("sampleRemark", dtoAnalyseDataTemp.getSampleRemark());
        map.put("sampleTypeId", dtoAnalyseDataTemp.getSampleTypeId());
        map.put("sampleTypeName", dtoAnalyseDataTemp.getSampleTypeName());
        map.put("bigSampleTypeId", dtoAnalyseDataTemp.getBigSampleTypeId());
        map.put("testId", dtoAnalyseDataTemp.getTestId());
        map.put("testOrignValue", dtoAnalyseDataTemp.getTestOrignValue());
        map.put("testValue", dtoAnalyseDataTemp.getTestValue());
        map.put("testValueD", dtoAnalyseDataTemp.getTestValueD());
        map.put("testValueDstr", dtoAnalyseDataTemp.getTestValueDstr());
        map.put("dataStatus", dtoAnalyseDataTemp.getDataStatus());
        map.put("formula", dtoAnalyseDataTemp.getFormula());
        map.put("formulaId", dtoAnalyseDataTemp.getFormulaId());
        map.put("isDataEnabled", dtoAnalyseDataTemp.getIsDataEnabled());
        map.put("groupSampleId", dtoAnalyseDataTemp.getGroupSampleId());
        map.put("qcInfo", dtoAnalyseDataTemp.getQcInfo());
        map.put("qcCode", dtoAnalyseDataTemp.getQcCode());
        map.put("qcTestValue", dtoAnalyseDataTemp.getQcTestValue());
        map.put("qcTestValueDimensionId", dtoAnalyseDataTemp.getQcTestValueDimensionId());
        map.put("qcConcentration", dtoAnalyseDataTemp.getQcConcentration());
        map.put("qcConcentrationDimensionId", dtoAnalyseDataTemp.getQcConcentrationDimensionId());
        map.put("qcStandardId", dtoAnalyseDataTemp.getQcStandardId());
        map.put("qcStandardDate", dtoAnalyseDataTemp.getQcStandardDate());
        map.put("qcValidDate", dtoAnalyseDataTemp.getQcValidDate());
        map.put("qcValue", dtoAnalyseDataTemp.getQcValue());
        map.put("uncertainType", dtoAnalyseDataTemp.getUncertainType());
        map.put("qcVolume", dtoAnalyseDataTemp.getQcVolume());
        map.put("qcValueDimensionId", dtoAnalyseDataTemp.getQcValueDimensionId());
        map.put("qcVolumeDimensionId", dtoAnalyseDataTemp.getQcVolumeDimensionId());
        map.put("isSci", dtoAnalyseDataTemp.getIsSci());
        String analyzeTime = StringUtil.isNotNull(dtoAnalyseDataTemp.getAnalyzeTime()) ? DateUtil.dateToString(dtoAnalyseDataTemp.getAnalyzeTime(), DateUtil.YEAR) : "";
        map.put("analyzeTime", (StringUtil.isNotEmpty(analyzeTime) && !analyzeTime.contains("1753")) ? analyzeTime : "");
        return map;
    }

    /**
     * 查询领样单下的样品分析人员分配
     *
     * @param analyseDataList 分析数据集合
     * @param samples         样品集合
     * @return 分析人员分配
     */
    private DtoSampleAssignTemp findPerson(List<DtoAnalyseData> analyseDataList, List<DtoSample> samples, DtoCode isShowFolder) {
        DtoSampleAssignTemp temp = new DtoSampleAssignTemp();
        temp.setIsQM(false);
        //按照样品和测试项目进行分组
        Map<String, DtoAnalyseData> analyseDataMap = analyseDataList.stream().
                collect(Collectors.groupingBy(p -> String.format("%s_%s", p.getSampleId(), p.getTestId()), Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        List<DtoAnalyseData> testAnaList = new ArrayList<>(analyseDataList.stream().
                collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());

        List<String> personIds = analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = new ArrayList<>();
        if (personIds.size() > 0) {
            personList = personService.findAll(personIds);
        }
        testAnaList.sort(Comparator.comparing(DtoAnalyseData::getRedAnalyzeMethodName));
        List<Map<String, Object>> tests = new ArrayList<>();
        for (DtoAnalyseData testAna : testAnaList) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("id", testAna.getTestId());
            obj.put("analyzeMethodId", testAna.getAnalyzeMethodId());
            obj.put("redAnalyzeItemName", testAna.getRedAnalyzeItemName());
            obj.put("redAnalyzeMethodName", testAna.getRedAnalyzeMethodName());
            obj.put("redCountryStandard", testAna.getRedCountryStandard());
            tests.add(obj);
        }
        //获取所有质控样数据
        List<String> qcIds = samples.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        qcIds.removeIf(p -> UUIDHelper.GUID_EMPTY.equals(p));
        List<DtoQualityControl> qualityControls = qualityControlService.findAll(qcIds);
        temp.setTest(tests);
        List<Map<String, Object>> perObjects = new ArrayList<>();
        for (DtoSample sample : samples) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("sampleId", sample.getId());
            obj.put("sampleTypeId", sample.getSampleTypeId());
            obj.put("code", sample.getCode());
            setQcMsg(obj, sample, qualityControls);
//            obj.put("redFolderName", sample.getRedFolderName());
            setFolderName(obj, isShowFolder, sample);
            for (DtoAnalyseData testAna : testAnaList) {
                String key = String.format("%s_%s", sample.getId(), testAna.getTestId());
                DtoAnalyseData analyseData = analyseDataMap.getOrDefault(key, null);
                if (StringUtil.isNotNull(analyseData)) {
                    Map<String, Object> anaObject = new HashMap<>();
                    anaObject.put("analystId", analyseData.getAnalystId());
                    anaObject.put("analystName", analyseData.getAnalystName());
                    if (!StringUtil.isNotEmpty(analyseData.getAnalystName())) {
                        Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(analyseData.getAnalystId())).findFirst();
                        if (person.isPresent()) {
                            anaObject.put("analystName", person.get().getCName());
                        }
                    }
                    anaObject.put("status", analyseData.getStatus());
                    obj.put(testAna.getTestId(), anaObject);
                } else {
                    obj.put(testAna.getTestId(), null);
                }
            }
            perObjects.add(obj);
        }
        temp.setPerson(perObjects);
        return temp;
    }

    /**
     * 查询领样单下的样品分析人员分配
     *
     * @param analyseDataList 分析数据集合
     * @param samples         样品集合
     * @return 分析人员分配
     */
    private DtoSampleAssignTemp findQMPerson(List<DtoAnalyseData> analyseDataList, List<DtoSample> samples, DtoCode isShowFolder) {
        DtoSampleAssignTemp temp = new DtoSampleAssignTemp();
        temp.setIsQM(true);

        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(
                Collectors.groupingBy(p -> String.format("%s_%s", p.getSampleId(), p.getTestId())));

        List<DtoAnalyseData> testAnaList = new ArrayList<>(analyseDataList.stream().
                collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());

        testAnaList.sort(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName));
        List<Map<String, Object>> tests = new ArrayList<>();
        //获取所有质控样数据
        List<String> qcIds = samples.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        qcIds.removeIf(p -> UUIDHelper.GUID_EMPTY.equals(p));
        List<DtoQualityControl> qualityControls = qualityControlService.findAll(qcIds);

        for (DtoAnalyseData testAna : testAnaList) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("id", testAna.getTestId());
            obj.put("analyzeMethodId", testAna.getAnalyzeMethodId());
            obj.put("redAnalyzeItemName", testAna.getRedAnalyzeItemName());
            obj.put("redAnalyzeMethodName", testAna.getRedAnalyzeMethodName());
            obj.put("redCountryStandard", testAna.getRedCountryStandard());
            tests.add(obj);
        }
        temp.setTest(tests);
        List<Map<String, Object>> perObjects = new ArrayList<>();
        for (DtoSample sample : samples) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("sampleId", sample.getId());
            obj.put("sampleTypeId", sample.getSampleTypeId());
            obj.put("code", sample.getCode());
            setQcMsg(obj, sample, qualityControls);
//            obj.put("redFolderName", sample.getRedFolderName());
            setFolderName(obj, isShowFolder, sample);
            for (DtoAnalyseData testAna : testAnaList) {
                String key = String.format("%s_%s", sample.getId(), testAna.getTestId());
                if (analyseDataMap.containsKey(key)) {
                    Map<String, Object> anaObject = new HashMap<>();
                    String analystName = analyseDataMap.get(key).stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getAnalystName())).
                            sorted(Comparator.comparing(DtoAnalyseData::getAnalystName)).
                            map(p -> String.format("%s(%s)", p.getAnalystName(), p.getStatus())).collect(Collectors.joining(","));
                    String analystId = analyseDataMap.get(key).stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getAnalystName())).
                            sorted(Comparator.comparing(DtoAnalyseData::getAnalystName)).map(AnalyseData::getAnalystId)
                            .collect(Collectors.joining(","));
                    anaObject.put("analystId", analystId);
                    anaObject.put("analystName", analystName);
                    obj.put(testAna.getTestId(), anaObject);
                } else {
                    obj.put(testAna.getTestId(), null);
                }
            }
            perObjects.add(obj);
        }
        temp.setPerson(perObjects);

        return temp;
    }

    /**
     * 查询领样单下的样品分析方法分配
     *
     * @param analyseDataList 分析数据集合
     * @param samples         样品集合
     * @return 分析方法分配
     */
    private DtoSampleAssignTemp findMethod(List<DtoAnalyseData> analyseDataList, List<DtoSample> samples, DtoCode isShowFolder) {
        DtoSampleAssignTemp temp = new DtoSampleAssignTemp();
        temp.setIsQM(false);

        //按照样品和测试项目进行分组
        Map<String, DtoAnalyseData> analyseDataMap = analyseDataList.stream().
                collect(Collectors.groupingBy(p -> String.format("%s_%s", p.getSampleId(), p.getTestId()), Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        // 根据测试项目分组后的每个测试项目一条记录组合的集合
        List<DtoAnalyseData> testAnaList = new ArrayList<>(analyseDataList.stream().
                collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());

        //BUG2024011999460 【重要】【2024-1-22】【马川江】【样品分配】样品分配中，列表中因子的排序，改成按分析方法、分析项目名称顺序排列，保证同方法的显示在一起
        List<String> sortItemNameList = analyseDataList.stream().sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeMethodName)
                        .thenComparing(DtoAnalyseData::getRedAnalyzeItemName))
                .map(DtoAnalyseData::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
        testAnaList.sort(Comparator.comparing(DtoAnalyseData::getRedAnalyzeMethodName).thenComparing(DtoAnalyseData::getRedAnalyzeItemName));
        List<Map<String, Object>> analyzeItems = new ArrayList<>();
        for (DtoAnalyseData analyseData : testAnaList) {
            Map<String, Object> objItem = new HashMap<>();
            objItem.put("redCountryStandard", analyseData.getRedCountryStandard());
            objItem.put("redAnalyzeItemName", analyseData.getRedAnalyzeItemName());
            objItem.put("analyzeItemId", analyseData.getAnalyseItemId());
            objItem.put("id", UUIDHelper.GUID_EMPTY);
            analyseDataList.stream().filter(v -> analyseData.getTestId().equals(v.getTestId())).findFirst()
                    .ifPresent(v -> objItem.put("id", v.getTestId()));
            analyzeItems.add(objItem);
        }
        temp.setTest(analyzeItems);

        //获取所有质控样数据
        List<String> qcIds = samples.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        qcIds.removeIf(p -> UUIDHelper.GUID_EMPTY.equals(p));
        List<DtoQualityControl> qualityControls = qualityControlService.findAll(qcIds);

        List<String> sampleTypeIds = samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        Map<String, String> parentMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getParentId));

        List<Map<String, Object>> methodObjects = new ArrayList<>();
        for (DtoSample sample : samples) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("sampleId", sample.getId());
            obj.put("sampleTypeId", sample.getSampleTypeId());
            obj.put("bigSampleTypeId", parentMap.getOrDefault(sample.getSampleTypeId(), ""));
            obj.put("code", sample.getCode());
            setQcMsg(obj, sample, qualityControls);
//            obj.put("redFolderName", sample.getRedFolderName());
            setFolderName(obj, isShowFolder, sample);
            for (DtoAnalyseData analyseData : testAnaList) {
                String key = String.format("%s_%s", sample.getId(), analyseData.getTestId());
                DtoAnalyseData data = analyseDataMap.getOrDefault(key, null);
                if (StringUtil.isNotNull(data)) {
                    Map<String, Object> anaObject = new HashMap<>();
                    anaObject.put("testId", data.getTestId());
                    anaObject.put("status", data.getStatus());
                    anaObject.put("redAnalyzeMethodName", data.getRedAnalyzeMethodName());
                    anaObject.put("redAnalyzeItemId", data.getAnalyseItemId());
                    obj.put(data.getTestId(), anaObject);
                } else {
                    obj.put(analyseData.getTestId(), null);
                }
            }
            methodObjects.add(obj);
        }
        temp.setAnalyzeMethod(methodObjects);

        return temp;
    }


    /**
     * 设置是否隐藏数据
     *
     * @param obj          返回的数据
     * @param isShowFolder 开关的常量
     * @param sample       样品数据
     */
    private void setFolderName(Map<String, Object> obj, DtoCode isShowFolder, DtoSample sample) {
        if (StringUtil.isNotNull(isShowFolder)) {
            if ("1".equals(isShowFolder.getDictValue())) {
                if (StringUtil.isNotEmpty(sample.getQcId())) {
                    obj.put("redFolderName", "/");
                    if (!UUIDHelper.GUID_EMPTY.equals(sample.getQcId())) {
                        DtoQualityControl qc = qualityControlService.findOne(sample.getQcId());
                        Integer qcType = qc.getQcType();
                        Integer qcGrade = qc.getQcGrade();
                        if ((EnumLIM.EnumQCGrade.外部质控.getValue().equals(qcGrade) && EnumLIM.EnumQCType.空白.getValue().equals(qcType))
                                || (EnumLIM.EnumQCGrade.外部质控.getValue().equals(qcGrade) && EnumLIM.EnumQCType.运输空白.getValue().equals(qcType))) {
                            obj.put("redFolderName", sample.getRedFolderName());
                        }
                    }
                }
            } else {
                obj.put("redFolderName", sample.getRedFolderName());
            }
        } else {
            obj.put("redFolderName", sample.getRedFolderName());
        }
    }

    /**
     * 判断是否显示客户信息
     *
     * @return 是否显示
     */
    private Boolean getIsShowCustomer() {
        Boolean result = true;
        DtoCode isShowFolder = codeService.findByCode("PRO_IS_SHOW_FOLDER");
        if (StringUtil.isNotNull(isShowFolder)) {
            if ("1".equals(isShowFolder.getDictValue())) {
                result = false;
            }
        }
        return result;
    }


    /**
     * 查询领样单下的样品分析方法分配
     *
     * @param analyseDataList 分析数据集合
     * @param samples         样品集合
     * @return 分析方法分配
     */
    private DtoSampleAssignTemp findQMMethod(List<DtoAnalyseData> analyseDataList, List<DtoSample> samples, DtoCode isShowFolder) {
        DtoSampleAssignTemp temp = new DtoSampleAssignTemp();
        temp.setIsQM(true);

        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(
                Collectors.groupingBy(p -> String.format("%s_%s", p.getSampleId(), p.getTestId())));

        List<DtoAnalyseData> testAnaList = new ArrayList<>(analyseDataList.stream().
                collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());

        //BUG2024011999460 【重要】【2024-1-22】【马川江】【样品分配】样品分配中，列表中因子的排序，改成按分析方法、分析项目名称顺序排列，保证同方法的显示在一起
        List<DtoAnalyseData> copyTestAnaList = new ArrayList<>(testAnaList);
        if (!samples.isEmpty()) {
            String sample0Id = samples.get(0).getId();
            for (DtoAnalyseData copyItem : copyTestAnaList) {
                DtoAnalyseData sortItem = analyseDataList.stream().filter(a -> sample0Id.equals(a.getSampleId()) && copyItem.getAnalyseItemId().equals(a.getAnalyseItemId()))
                        .findFirst().orElse(null);
                //原字段redAnalyzeMethodName不知道有啥用，临时用此字段存放排序内容
                copyItem.setAnalyseDataStatus(sortItem != null && StringUtil.isNotEmpty(sortItem.getRedAnalyzeMethodName()) ? sortItem.getRedAnalyzeMethodName() : "");
            }
            copyTestAnaList.sort(Comparator.comparing(DtoAnalyseData::getAnalyseDataStatus));
        }
        List<Map<String, Object>> tests = new ArrayList<>();
        for (DtoAnalyseData testAna : copyTestAnaList) {
            Map<String, Object> objTest = new HashMap<>();
            objTest.put("id", testAna.getTestId());
            objTest.put("analyzeMethodId", testAna.getSampleTypeId());
            objTest.put("redAnalyzeItemName", testAna.getRedAnalyzeItemName());
            objTest.put("redAnalyzeMethodName", testAna.getRedAnalyzeMethodName());
            objTest.put("redCountryStandard", testAna.getRedCountryStandard());
            tests.add(objTest);
        }
        temp.setTest(tests);

        //获取所有质控样数据
        List<String> qcIds = samples.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        qcIds.removeIf(p -> UUIDHelper.GUID_EMPTY.equals(p));
        List<DtoQualityControl> qualityControls = qualityControlService.findAll(qcIds);

        List<Map<String, Object>> methodObjects = new ArrayList<>();
        for (DtoSample sample : samples) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("sampleId", sample.getId());
            obj.put("sampleTypeId", sample.getSampleTypeId());
            obj.put("code", sample.getCode());
            setQcMsg(obj, sample, qualityControls);
//            obj.put("redFolderName", sample.getRedFolderName());
            setFolderName(obj, isShowFolder, sample);
            for (DtoAnalyseData testAna : testAnaList) {
                String key = String.format("%s_%s", sample.getId(), testAna.getTestId());
                if (analyseDataMap.containsKey(key)) {
                    Map<String, Object> anaObject = new HashMap<>();
                    List<String> methods = analyseDataMap.get(key).stream().map(p -> String.format("%s(%s)", p.getRedAnalyzeMethodName(), p.getStatus())).collect(Collectors.toList());
                    if (methods.size() > 0) {
                        anaObject.put("redAnalyzeMethodName", methods.get(0));//TODO 同一个测试项目多个人员，这边是显示多个相同方法还是一个？
                    } else {
                        obj.put(testAna.getTestId(), null);
                    }
                    obj.put(testAna.getTestId(), anaObject);
                } else {
                    obj.put(testAna.getTestId(), null);
                }
            }
            methodObjects.add(obj);
        }
        temp.setAnalyzeMethod(methodObjects);

        return temp;
    }

    /**
     * 设置样品的质控类型和质控等级
     *
     * @param obj             需要设置的数据
     * @param sample          样品
     * @param qualityControls 质控数据
     */
    private void setQcMsg(Map<String, Object> obj, DtoSample sample, List<DtoQualityControl> qualityControls) {
        obj.put("qcType", sample.getQcType());
        obj.put("qcGrade", sample.getQcGrade());
        //获取样品的质控类型
        if (StringUtil.isNotEmpty(sample.getQcId())) {
            if (!UUIDHelper.GUID_EMPTY.equals(sample.getQcId())) {
                Optional<DtoQualityControl> qcOptional = qualityControls.stream().filter(p -> sample.getQcId().equals(p.getId())).findFirst();
                qcOptional.ifPresent(p -> {
                    obj.put("qcType", p.getQcType());
                    obj.put("qcGrade", p.getQcGrade());
                });
            }
        }
    }

    /**
     * 根据领样单id获取是否质控
     *
     * @param id 领样单id
     * @return 返回对应领样单是否质控任务
     */
    private Boolean findIsQM(String id) {
        DtoReceiveSubSampleRecord subRecord = repository.findOne(id);
        DtoProject project = projectRepository.findOne(subRecord.getProjectId());
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        return projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue());
    }

    /**
     * 修改数据的分析人
     *
     * @param paramList       传参集合
     * @param analyseDataList 分析数据
     * @param personList      人员集合
     */
    private void changeAnalysePerson(List<DtoSampleAssignParam> paramList, List<DtoAnalyseData> analyseDataList, List<DtoPerson> personList) {
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        for (DtoSampleAssignParam param : paramList) {
            //遍历传参，提取修改后的分析人信息
            DtoPerson analyst = personList.stream().filter(p -> p.getId().equals(param.getAnalystId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(analyst)) {
                //筛选该指标的与修改后分析人不一致的数据并进行遍历
                List<DtoAnalyseData> thisAnaDataList = analyseDataList.stream().filter(p -> p.getTestId().equals(param.getTestId()) && !param.getAnalystId().equals(p.getAnalystId())).collect(Collectors.toList());
                for (DtoAnalyseData data : thisAnaDataList) {
                    //进行数据信息修改
                    DtoAnalyseData anaData = new DtoAnalyseData();
                    anaData.setId(data.getId());
                    anaData.setAnalystId(analyst.getId());
                    anaData.setAnalystName(analyst.getCName());
                    anaData.setDomainId(analyst.getDomainId());
                    anaData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    anaData.setModifyDate(new Date());
                    anaDatas.add(anaData);
                }
            }
        }
        if (anaDatas.size() > 0) {
            //批量更新
            comRepository.updateBatch(anaDatas);
        }
    }

    /**
     * 插入修改分析人的日志
     *
     * @param paramList       传参集合
     * @param analyseDataList 分析数据
     * @param samples         样品集合
     * @param personList      人员集合
     */
    private void createChangePersonLogs(List<DtoSampleAssignParam> paramList, List<DtoAnalyseData> analyseDataList, List<DtoSample> samples, List<DtoPerson> personList) {
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSampleAssignParam param : paramList) {
            //遍历传参，提取修改后的分析人信息
            String analystName = personList.stream().filter(p -> p.getId().equals(param.getAnalystId())).map(DtoPerson::getCName).findFirst().orElse("");
            //筛选该指标的与修改后分析人不一致的数据并进行遍历
            List<DtoAnalyseData> thisAnaDataList = analyseDataList.stream().filter(p -> p.getTestId().equals(param.getTestId()) && !param.getAnalystId().equals(p.getAnalystId())).collect(Collectors.toList());
            for (DtoAnalyseData data : thisAnaDataList) {
                //进行日志记录
                String sampleCode = samples.stream().filter(p -> p.getId().equals(data.getSampleId())).map(DtoSample::getCode).findFirst().orElse("");
                String comment = String.format("更换了样品%s的指标%s的检测人员，原检测人员为%s，更换为%s。",
                        sampleCode,
                        data.getRedAnalyzeItemName(),
                        StringUtils.isNotNullAndEmpty(data.getAnalystName()) ? data.getAnalystName() : "",
                        analystName);
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改检测人员.toString());
                log.setLogType(EnumPRO.EnumLogType.实验室领样单分配.getValue());
                log.setObjectId(param.getSubId());
                log.setObjectType(EnumPRO.EnumLogObjectType.实验室领样单.getValue());
                log.setComment(comment);
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
        }
        //批量插入日志
        newLogService.createLog(logList, EnumPRO.EnumLogType.实验室领样单分配.getValue());
    }

    /**
     * 修改分析数据方法
     *
     * @param samples         样品
     * @param analyseDataList 分析数据
     * @param test            新的指标
     * @return 修改后的分析人集合
     */
    private List<String> changeAnalyseMethod(List<DtoSample> samples, List<DtoAnalyseData> analyseDataList, DtoTest test) {

        //提取送样单及项目信息
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(samples.get(0).getReceiveId());
        DtoProject project = projectRepository.findOne(record.getProjectId());

        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        //数据上分析项目id的集合
        List<String> analyseDataAnalyzeItemIds = analyseDataList.parallelStream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
        //获取包含上面分析项目的测试项目集合
        List<DtoTest> testList = testRepository.findByAnalyzeItemIdIn(analyseDataAnalyzeItemIds);
        //获取上面测试项目的id集合
        List<String> testIdList = testList.parallelStream().map(DtoTest::getId).collect(Collectors.toList());

        //按照检测类型进行分组
        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleTypeId));
        for (String sampleTypeId : analyseDataMap.keySet()) {
            //提取该检测类型下的分析数据
            List<DtoAnalyseData> thisAnaDatas = analyseDataMap.get(sampleTypeId);
            //提取一个该检测类型下的样品
            List<DtoSample> groupSamples = samples.stream().filter(p -> p.getId().equals(thisAnaDatas.get(0).getSampleId())).collect(Collectors.toList());
            //调用方法获取添加该指标后的分析数据并取第一条当做模板
            DtoAnalyseDataAdd dtoAnalyseDataAdd = new DtoAnalyseDataAdd(project, groupSamples, record, Collections.singletonList(test), false);
            dtoAnalyseDataAdd.setIsAddAssociateTest(false);
            dtoAnalyseDataAdd.setIsUniqueTest(false);
            List<DtoAnalyseData> changeAnaDatas = analyseDataService.getAddAnalyseData(dtoAnalyseDataAdd);
            //模板数据
            DtoAnalyseData changeAnaData = changeAnaDatas.get(0);

            for (DtoAnalyseData analyseData : thisAnaDatas) {
                //遍历该组数据，替换相应的信息为模板数据上的信息
                DtoAnalyseData anaData = new DtoAnalyseData();
                anaData.setId(analyseData.getId());
                anaData.setSampleId(analyseData.getSampleId());
                if (testIdList.contains(analyseData.getTestId())) {
                    if (test.getAnalyzeItemId().equals(analyseData.getAnalyseItemId())) {
                        anaData.setTestId(test.getId());
                        anaData.setAnalyzeMethodId(test.getAnalyzeMethodId());
                        anaData.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                        anaData.setRedCountryStandard(test.getRedCountryStandard());
                    } else {
                        String analyzeItemId = analyseData.getAnalyseItemId();
                        String newAnalyzeMethodId = test.getAnalyzeMethodId();
                        DtoTest newTest = testRepository.findByAnalyzeItemIdAndAnalyzeMethodIdAndIsDeletedFalse(analyzeItemId, newAnalyzeMethodId);
                        if (StringUtil.isNotNull(newTest)) {
                            anaData.setTestId(newTest.getId());
                            anaData.setAnalyzeMethodId(newTest.getAnalyzeMethodId());
                            anaData.setRedAnalyzeMethodName(newTest.getRedAnalyzeMethodName());
                            anaData.setRedCountryStandard(newTest.getRedCountryStandard());
                        } else {
                            anaData.setTestId(analyseData.getTestId());
                        }
                    }
                }
                anaData.setMostSignificance(changeAnaData.getMostSignificance());
                anaData.setMostDecimal(changeAnaData.getMostDecimal());
                anaData.setExamLimitValue(StringUtils.isNotNullAndEmpty(changeAnaData.getExamLimitValue()) ? changeAnaData.getExamLimitValue() : "");
                anaData.setDimensionId(changeAnaData.getDimensionId());
                anaData.setDimension(StringUtils.isNotNullAndEmpty(changeAnaData.getDimension()) ? changeAnaData.getDimension() : "");
                anaData.setAnalystId(changeAnaData.getAnalystId());
                anaData.setAnalystName(StringUtils.isNotNullAndEmpty(changeAnaData.getAnalystName()) ? changeAnaData.getAnalystName() : "");
                anaData.setDomainId(changeAnaData.getDomainId());
                anaData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                anaData.setModifyDate(new Date());
                anaData.setLowerLimit(changeAnaData.getLowerLimit());

                anaDatas.add(anaData);
            }
        }
        // 同一样品下存在相同测试项目校验
        Map<String, List<DtoAnalyseData>> analyseGroup = anaDatas.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        analyseGroup.forEach((k, v) -> {
            v.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId)).forEach((tk, tv) -> {
                if (tv.size() > 1) {
                    throw new BaseException("样品中存在相同的测试项目，请确认后重试！");
                }
            });
        });
        if (anaDatas.size() > 0) {
            //批量更新
            comRepository.updateBatch(anaDatas);
        }
        return anaDatas.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList());
    }

    /**
     * 插入修改分析方法的日志
     *
     * @param param           传参
     * @param analyseDataList 分析数据
     * @param samples         样品
     */
    private void createChangeMethodLogs(DtoSampleAssignParam param, List<DtoAnalyseData> analyseDataList, List<DtoSample> samples, DtoTest test) {
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : samples) {
            DtoAnalyseData analyseData = analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(analyseData)) {
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改检测方法.toString());
                log.setLogType(EnumPRO.EnumLogType.实验室领样单分配.getValue());
                log.setObjectId(param.getSubId());
                log.setObjectType(EnumPRO.EnumLogObjectType.实验室领样单.getValue());
                log.setComment(String.format("更换了样品%s的指标%s的检测方法，原检测方法为%s，更换为%s，原因:%s", sample.getCode(), analyseData.getRedAnalyzeItemName(),
                        analyseData.getRedAnalyzeMethodName(), test.getRedAnalyzeMethodName(), param.getOpinion()));
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
        }
        //批量插入日志
        newLogService.createLog(logList, EnumPRO.EnumLogType.实验室领样单分配.getValue());
    }


    /**
     * 修改样品分组表关联测试项目的冗余分析方法
     *
     * @param param           参数
     * @param analyseDataList 待修改的分析数据
     * @param newTest         新的测试项目
     */
    private void changeSampleGroup2Test(DtoSampleAssignParam param, List<DtoAnalyseData> analyseDataList, DtoTest newTest) {
        // 获取样品分组数据
        List<DtoSampleGroup> dtoSampleGroups = sampleGroupRepository.findBySampleIdIn(param.getSampleIds());
        List<String> sampleGroupIds = dtoSampleGroups.stream().map(DtoSampleGroup::getId).collect(Collectors.toList());
        List<DtoSampleGroup2Test> dtoSampleGroup2Tests = sampleGroup2TestRepository.findBySampleGroupIdIn(sampleGroupIds);
        // 待修改的分析数据根据样品id进行分组
        Map<String, List<DtoAnalyseData>> analyseDataToMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        // 定义待修改数据
        List<DtoSampleGroup2Test> updateSampleGroup2Tests = new ArrayList<>();
        for (String sampleId : analyseDataToMap.keySet()) {
            // 获取当前样品下的分组数据id集合
            List<DtoSampleGroup> sampleGroups = dtoSampleGroups.stream().filter(p -> sampleId.equals(p.getSampleId())).collect(Collectors.toList());
            List<String> groupIdsBySampleId = sampleGroups.stream().map(DtoSampleGroup::getId).collect(Collectors.toList());
            // 根据id集合关联测试项目
            List<DtoSampleGroup2Test> sampleGroup2TestList = dtoSampleGroup2Tests.stream().filter(p -> groupIdsBySampleId.contains(p.getSampleGroupId())).collect(Collectors.toList());
            // 获取当前样品下的分析数据，根据分析因子分组
            List<DtoAnalyseData> dtoAnalyseData = analyseDataToMap.get(sampleId);
            Map<String, DtoAnalyseData> analyseDataByItemIdMap = dtoAnalyseData.stream().collect(Collectors.toMap(DtoAnalyseData::getAnalyseItemId, p -> p));
            for (DtoSampleGroup2Test sampleGroup2Test : sampleGroup2TestList) {
                // 判断当前分析数据是否为待修改数据
                if (analyseDataByItemIdMap.containsKey(sampleGroup2Test.getAnalyzeItemId())) {
                    DtoAnalyseData analyseData = analyseDataByItemIdMap.get(sampleGroup2Test.getAnalyzeItemId());
                    if (newTest.getAnalyzeMethodId().equals(analyseData.getAnalyzeMethodId())) {
                        sampleGroup2Test.setTestId(newTest.getId());
                        sampleGroup2Test.setRedAnalyzeMethodName(newTest.getRedAnalyzeMethodName());
                        sampleGroup2Test.setRedCountryStandard(newTest.getRedCountryStandard());
                    } else {
                        String analyzeItemId = analyseData.getAnalyseItemId();
                        String newAnalyzeMethodId = newTest.getAnalyzeMethodId();
                        DtoTest newTest2 = testRepository.findByAnalyzeItemIdAndAnalyzeMethodIdAndIsDeletedFalse(analyzeItemId, newAnalyzeMethodId);
                        if (StringUtil.isNotNull(newTest2)) {
                            sampleGroup2Test.setTestId(newTest2.getId());
                            sampleGroup2Test.setAnalyzeItemId(newTest2.getAnalyzeMethodId());
                            sampleGroup2Test.setRedAnalyzeMethodName(newTest2.getRedAnalyzeMethodName());
                            sampleGroup2Test.setRedCountryStandard(newTest2.getRedCountryStandard());
                        }
                    }
                    updateSampleGroup2Tests.add(sampleGroup2Test);

                }
            }
        }
        sampleGroup2TestRepository.save(updateSampleGroup2Tests);
    }

    /**
     * 按样品编号排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    private static String comparingBySampleCode(Map<String, Object> map) {
        return (String) map.get("sampleCode");
    }

    /**
     * 按分析项目排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    private static Integer comparingByItemSort(Map<String, Object> map) {
        return (Integer) map.get("sortNumber");
    }

    /**
     * 按排序值排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    private static String comparingByOrderNum(Map<String, Object> map) {
        return (String) map.get("orderNum");
    }

    //#endregion

    private List<DtoAnalyseData> fetchAnaDataBySameMethod(String oldMethodId) {
        StringBuilder stringBuilder = new StringBuilder();
        Map<String, Object> values = new HashMap<>();
        stringBuilder.append("select a.testId,a.analyseItemId,a.redAnalyzeItemName,a.analyzeMethodId,a.redAnalyzeMethodName,");
        stringBuilder.append("a.redCountryStandard,a.grade,a.sampleId,a.lowerLimit ");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample b ");
        stringBuilder.append(" where 1=1 and a.isDeleted = 0 ");
        stringBuilder.append(" and a.sampleId = b.id");
        stringBuilder.append(" and b.isDeleted = 0");
        stringBuilder.append(" and a.analyzeMethodId = :oldMethodId ");
        values.put("oldMethodId", oldMethodId);
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and b.status <> :status");
        values.put("status", EnumPRO.EnumSampleStatus.样品作废.toString());
        stringBuilder.append(" and (a.workSheetId = :workSheetId or a.workSheetId = '' or a.workSheetId = null)");
        values.put("workSheetId", UUIDHelper.GUID_EMPTY);
        stringBuilder.append(" and (a.workSheetFolderId = :workSheetFolderId or a.workSheetFolderId = '' or a.workSheetFolderId = null)");
        values.put("workSheetFolderId", UUIDHelper.GUID_EMPTY);
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0"); //过滤分包
        stringBuilder.append(" and a.isCompleteField = 0 "); //过滤现场
        stringBuilder.append(" and a.dataStatus = :dataStatus "); //数据状态
        values.put("dataStatus", EnumPRO.EnumAnalyseDataStatus.未测.getValue());
        stringBuilder.append(" and b.innerReceiveStatus = :innerReceiveStatus "); //过滤样品领样状态
        values.put("innerReceiveStatus", EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
        List<DtoAnalyseData> datas = comRepository.find(stringBuilder.toString(), values);
        List<DtoAnalyseData> newDatas = new ArrayList<>();
        Iterator<DtoAnalyseData> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性
        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoAnalyseData analyseData = new DtoAnalyseData();
            String testId = (String) objs[0];
            String analyseItemId = (String) objs[1];
            String redAnalyzeItemName = (String) objs[2];
            String analyzeMethodId = (String) objs[3];
            String redAnalyzeMethodName = (String) objs[4];
            String redCountryStandard = (String) objs[5];
            Integer grade = (Integer) objs[6];
            String sampleId = (String) objs[7];
            String lowerLimit = (String) objs[8];
            analyseData.setTestId(testId);
            analyseData.setAnalyseItemId(analyseItemId);
            analyseData.setRedAnalyzeItemName(redAnalyzeItemName);
            analyseData.setAnalyzeMethodId(analyzeMethodId);
            analyseData.setRedAnalyzeMethodName(redAnalyzeMethodName);
            analyseData.setRedCountryStandard(redCountryStandard);
            analyseData.setGrade(grade);
            analyseData.setSampleId(sampleId);
            analyseData.setLowerLimit(lowerLimit);
            newDatas.add(analyseData);
        }
        return newDatas;
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }
}