package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.AnalyseTimelinessFutureService;
import com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO;
import com.sinoyd.lims.pro.vo.AnalyzeTimelinessMapVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;

/**
 * 分析数据统计多线程操作接口实现
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/04/28
 */
@Service
@Slf4j
public class AnalyseTimelinessFutureServiceImpl implements AnalyseTimelinessFutureService {


    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    @Override
    public Future<List<AnalyzeTimelinessMapVO>> handleDate(List<AnalyzeTimelinessMapVO> voList, List<Map<String, Integer>> overdueMapList,
                                                           Map<String, DtoCalendarDate> date2CalendarDate,
                                                           Integer warnDay) {
        log.info("=================当前子线程:" + Thread.currentThread().getName() + "=========================");
        int willOverdueCount = 0;
        int overdueCount = 0;
        Calendar calendarAnalyze = Calendar.getInstance();
        for (AnalyzeTimelinessMapVO mapVo : voList) {
            calendarAnalyze.clear();
            Integer dataStatus = mapVo.getDataStatus();
            Date samplingTime = mapVo.getSamplingTime();
            Date analyzeTime = mapVo.getAnalyzeTime();
            Integer analyseDayLen = mapVo.getAnalyseDayLen();
            //设置要求完成时间
            Calendar calendarComplete = Calendar.getInstance();
            for (int i = 0; i < analyseDayLen; i++) {
                Calendar calendar = addCalendarDay(samplingTime, i + 1);
                DtoCalendarDate calendarDate = date2CalendarDate.get(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR));
                calendarComplete = calendar;
                if (StringUtil.isNotNull(calendarDate) && calendarDate.getType().equals(1)) {
                    analyseDayLen++;
                }
            }
            //设置分析时间
            calendarAnalyze.setTime(analyzeTime);
            //设置预警时间
            Calendar calendarWarn = addCalendarDay(analyzeTime, warnDay);
            // 即将超期
            if (calendarComplete.compareTo(calendarAnalyze) >= 0 && calendarComplete.compareTo(calendarWarn) <= 0 && !EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(dataStatus)) {
                willOverdueCount++;
            }
            //已超期
            if (calendarComplete.compareTo(calendarAnalyze) < 0) {
                overdueCount++;
            }
        }
        Map<String, Integer> map = new HashMap<>();
        map.put("overdueCount", overdueCount);
        map.put("willOverdueCount", willOverdueCount);
        overdueMapList.add(map);
        return new AsyncResult<>(voList);
    }

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<List<AnalyzeDetailDataVO>> handleDetailDate(List<AnalyzeDetailDataVO> voList, Map<String, DtoCalendarDate> date2CalendarDate) {
        for (AnalyzeDetailDataVO vo : voList) {
            Integer analyseDayLen = vo.getAnalyseDayLen();
            for (int i = 0; i < analyseDayLen; i++) {
                Calendar calendar = addCalendarDay(vo.getSamplingDate(), i + 1);
                vo.setRequiredCompleteDate(calendar.getTime());
                DtoCalendarDate calendarDate = date2CalendarDate.get(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR));
                if (StringUtil.isNotNull(calendarDate) && calendarDate.getType().equals(1)) {
                    analyseDayLen++;
                }
            }
        }
        return new AsyncResult<>(voList);
    }

    /**
     * 根据日期以及天数获取日历时间
     *
     * @param date 日期
     * @param day  天数
     * @return 日历日期
     */
    private Calendar addCalendarDay(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar;
    }

}
