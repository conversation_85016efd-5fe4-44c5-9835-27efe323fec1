package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RPEntrustingUnitInfoVO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上海监管平台委托方信息服务策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Component
public class RPEntrustingUnitInfo extends AbsRegulatoryPlatformRemote<RPEntrustingUnitInfoVO> {

    @Override
    protected void filterCriteria(List<RPEntrustingUnitInfoVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //分析方法模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getKey())) {
            list.removeIf(e -> !e.getEnterpriseName().contains(methodCriteria.getKey()));
        }
    }

    @Override
    protected String loadFindSoapBody(String userName, String password, BaseCriteria criteria, PageBean<RPEntrustingUnitInfoVO> pageBean) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        Map<String, Object> criteriaMap = new HashMap<>();
        if (StringUtil.isNotEmpty(methodCriteria.getEnterpriseName())){
            criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "qymc", methodCriteria.getEnterpriseName());
        }
        if (StringUtil.isNotEmpty(methodCriteria.getSocialCreditCode())){
            criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "xydm", methodCriteria.getSocialCreditCode());
        }
        if (StringUtil.isNotEmpty(methodCriteria.getPollutionSourceCode())){
            criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "wrybm", methodCriteria.getPollutionSourceCode());
        }
        return RPSoapBodyUtil.loadSoapBody(userName, password, queryMethodName(), criteriaMap);
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.委托单位信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.委托单位信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.委托单位信息.getDeleteMethod();
    }
}
