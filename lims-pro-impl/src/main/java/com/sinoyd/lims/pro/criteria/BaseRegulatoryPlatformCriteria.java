package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform.RPContract;
import com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform.RPPollutantDischargingUnitInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上海监管平台基础查询条件
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseRegulatoryPlatformCriteria extends BaseCriteria {

    /**
     * 监管平台查询方法
     */
    private String methodName;

    /**
     * 关键字查询条件
     */
    private String key;

    /**
     * 监管平台任务id【采样计划：点位信息查询用】
     */
    private String pId;

    /**
     * 企业名称关键字，模糊匹配【企业列表查询用、排污单位查询用】
     * <p/>
     * {@link com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform.RPEntrustingUnitInfo} <br/>
     * {@link RPPollutantDischargingUnitInfo} <br/>
     */
    private String enterpriseName;

    /**
     * 统一社会信用代码，精准匹配【企业列表查询用、排污单位查询用】
     * <p/>
     * {@link com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform.RPEntrustingUnitInfo} <br/>
     * {@link RPPollutantDischargingUnitInfo} <br/>
     */
    private String socialCreditCode;

    /**
     * 污染源编码，精准匹配【企业列表查询用】
     * <p/>
     * {@link com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform.RPEntrustingUnitInfo}
     */
    private String pollutionSourceCode;

    /**
     * 合同提交日期开始时间 【合同列表查询用】
     * <p/>
     * {@link RPContract}
     */
    private String startTime;

    /**
     * 合同提交日期结束时间【合同列表查询用】
     * <p/>
     * {@link RPContract}
     */
    private String endTime;

    /**
     * 合同标题，模糊匹配【合同列表查询用】
     * <p/>
     * {@link RPContract}
     */
    private String contractTitle;

    /**
     * 合同甲方名称，模糊匹配【合同列表查询用】
     * <p/>
     * {@link RPContract}
     */
    private String contractPartyAName;


    @Override
    public String getCondition() {
        return "";
    }

}
