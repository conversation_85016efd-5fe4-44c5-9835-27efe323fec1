package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.RPReportVO;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 上海监管平台报告数据策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/15
 */
@Component
public class RPReport extends AbsRegulatoryPlatformRemote<RPReportVO> {

    @Override
    protected void validatePushData(RegulatoryPlatformPushVO pushVo) {
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(pushVo.getId()).stream().findFirst();
        //判断项目合同信息
        if (!projectContractOp.isPresent()) {
            throw new BaseException("LIMS：项目不存在推送与信息管理信息，请确认！");
        }
        if (!StringUtil.isNotEmpty(projectContractOp.get().getPId())) {
            throw new BaseException("LIMS:项目未推送！");
        }
        DtoDocument document = documentRepository.findOne(pushVo.getDocumentId());
        if (document == null) {
            throw new BaseException("LIMS:系统未找到相关报告附件！");
        }
        DtoReport report = reportRepository.findOne(document.getFolderId());
        if (report == null) {
            throw new BaseException("LIMS:系统未找到报告！");
        }
        if (StringUtil.isEmpty(report.getRegulateCode())) {
            throw new BaseException("LIMS：报告未生成监管平台系统编号");
        }
    }

    @Override
    @Transactional
    public void push(RegulatoryPlatformPushVO pushVo) {
        //如果报告信息已经推送过，且报告编号已经存在，则先删除监管平台报告
        deleteRpReport(pushVo);
        //推送报告信息
        super.push(pushVo);
    }

    /**
     * 删除监管平台报告
     *
     * @param pushVo 推送参数
     */
    protected void deleteRpReport(RegulatoryPlatformPushVO pushVo) {
        //如果报告信息已经推送过，且报告编号已经存在，则先删除监管平台报告
        DtoDocument document = documentRepository.findOne(pushVo.getDocumentId());
        if (document != null && "已经推送".equals(document.getRemark()) && StringUtil.isNotEmpty(document.getShDocumentId())) {
            Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(pushVo.getId()).stream().findFirst();
            //判断项目合同信息
            if (projectContractOp.isPresent()) {
                Map<String, Object> requestParams = new HashMap<>();
                requestParams.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "MTID", projectContractOp.get().getPId());
                requestParams.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "ID", document.getShDocumentId());
                doRequest(deleteMethodName(), requestParams);
            }
        }
    }

    @Override
    protected RPReportVO getPushInstance(RegulatoryPlatformPushVO pushVo) {
        Optional<DtoProjectContract> projectContractOp = projectContractRepository
                .findByProjectId(pushVo.getId()).stream().findFirst();
        if (projectContractOp.isPresent()) {
            DtoDocument document = documentRepository.findOne(pushVo.getDocumentId());
            DtoReport report = reportRepository.findOne(document.getFolderId());
            return new RPReportVO(projectContractOp.get().getPId(), report);
        }
        return super.getPushInstance(pushVo);
    }

    @Override
    protected void handlePushDoc(RegulatoryPlatformPushVO pushVo, RPReportVO data) {
        DtoDocument document = documentRepository.findOne(pushVo.getDocumentId());
        String fileName = document.getFilename();
        String rpFileName = pushRemoteFile(document, EnumPRO.EnumRegulatoryPlatformDocType.监测任务数据报告_退回上阶段.getValue());
        data.setFileName(fileName);
        data.setRpFileName(rpFileName);
    }

    @Override
    @Transactional
    public void handlePushResult(RegulatoryPlatformPushVO pushVo, JSONObject result) {
        DtoDocument document = documentRepository.findOne(pushVo.getDocumentId());
        //获取监管平台上传的附件报告id
        JSONObject response = getResponse(result, insertMethodName()).getJSONObject("ResultStruct");
        String message = response.getString("message");
        if (!message.contains("报告编号：")){
            throw new BaseException("监管平台：推送报告未返回报告编号！");
        }
        String rpReportId = message.substring(message.indexOf("报告编号：") + 5) ;
        //更新报告附件的推送状态
        document.setRemark("已经推送");
        document.setShDocumentId(rpReportId);
        documentRepository.saveAndFlush(document);
        //处理项目推送数据状态
        Optional<DtoProjectContract> projectContractOp = projectContractRepository
                .findByProjectId(pushVo.getId()).stream().findFirst();
        List<DtoReport> reports = reportRepository.findByProjectId(pushVo.getId());

        //获取到其余的报告附件（包含未签发的报告附件）
        List<String> reportIds = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
        //获取此项目下其余pdf报告附件
        List<DtoDocument> otherReport = documentRepository.findByFolderIdInAndDocTypeIdAndIsDeletedFalseOrderByCreateDateDesc(reportIds, BaseCodeHelper.DOCUMENT_EXTEND_REPORT_ARCHIVE_FILE)
                .stream().filter(p -> !p.getId().equals(document.getId()) && !p.getIsDeleted() && p.getDocSuffix().contains("pdf")).collect(Collectors.toList());
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            //判断其他报告是否也全部推送，如果已经全部推送，则更新报告推送状态为已完成推送
            if (otherReport.size() == 0 || otherReport.stream().allMatch(p -> "已经推送".equals(p.getRemark()))) {
                projectContract.setReportHasPush(1);
                projectContract.setIsHandle(1);
                projectContractRepository.saveAndFlush(projectContract);
            }
        }
        super.handlePushResult(pushVo, result);
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.报告信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.报告信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.报告信息.getDeleteMethod();
    }
}
