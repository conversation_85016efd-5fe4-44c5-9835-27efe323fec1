package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.utils.XmlUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 上海监管平台方案编制策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/12
 */
@Component
public class RPTaskPlan extends AbsRegulatoryPlatformRemote<RPTaskPlanVO> {

    @Override
    protected void validatePushData(RegulatoryPlatformPushVO pushVo) {
        DtoProject project = projectService.findOne(pushVo.getId());
        if (project == null) {
            throw new BaseException("LIMS:需推送方案的项目不存在！");
        }
        //校验项目推送信息
        DtoProjectContract projectContract = validateContract(project);
        //校验方案状态
        validateStatus(project);
        //校验测试项目匹配
        validTestMatch(project, projectContract);
    }

    @Override
    protected String loadPushSoapBody(String username, String password, RPTaskPlanVO pushData) {
        return RPSoapBodyUtil.loadSoapBody(username, password)
                .replace("${body_labels}", this.loadBodyFields(pushData));
    }

    /**
     * 加载请求体字段标签
     *
     * @param pushData 推送数据
     * @return 字段标签
     */
    private String loadBodyFields(RPTaskPlanVO pushData) {
        StringBuilder monitorTaskPlanListXml = new StringBuilder();
        if (StringUtil.isNotEmpty(pushData.getMonitorTaskPlans())) {
            pushData.getMonitorTaskPlans()
                    .forEach(plan -> monitorTaskPlanListXml.append(RPSoapBodyUtil.loadBodyFields(plan)).append("\n"));
        }
        //方案请求体标签模板
        return XmlUtil.readXml("classpath:RegulatoryPlatform/RPTaskMonitorPlan.xml")
                .replace("${TaskPlanMethodName}", insertMethodName())
                .replace("${rpProjectId}", pushData.getRpProjectId())
                .replace("${monitorTaskPlanList}", monitorTaskPlanListXml.toString());
    }

    @Override
    protected RPTaskPlanVO getPushInstance(RegulatoryPlatformPushVO pushVo) {
        RPTaskPlanVO result = new RPTaskPlanVO();
        DtoProject project = projectService.findOne(pushVo.getId());
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(project.getId()).stream().findFirst();
        //判断项目合同信息
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            //项目推送采样类型是否为分析类型
            boolean isAnalyze = EnumPRO.EnumRegulatoryPlatformTaskSamplingType.分析.getValue().toString().equals(projectContract.getIsSampleId());
            //设置上海监管平台项目id
            result.setRpProjectId(projectContract.getPId());
            //查询点位数据
            List<String> sampleFolderIds = pushVo.getSampleFolderIds();
            List<DtoSampleFolder> sampleFolders = StringUtil.isNotEmpty(sampleFolderIds) ? sampleFolderRepository.findAll(sampleFolderIds) : new ArrayList<>();
            //查询点位测试项目分组数据
            TestCriteria criteria = new TestCriteria();
            criteria.setSampleFolderIds(sampleFolderIds);
            Map<String, List<DtoTest>> folderTestGroup = folderExtendService.findSampleFolderGroupTest(criteria);
            //获取点位周期频次信息
            List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(sampleFolderIds);
            result.setMonitorTaskPlans(createTaskPlanMonitorData(sampleFolders, frequencyList, folderTestGroup, isAnalyze));
            return result;
        }
        return super.getPushInstance(pushVo);
    }

    @Override
    @Transactional
    public void handlePushResult(RegulatoryPlatformPushVO pushVo, JSONObject result) {
        DtoProject project = projectService.findOne(pushVo.getId());
        Optional<DtoProjectContract> projectContractOp = projectContractRepository
                .findByProjectId(project.getId()).stream().findFirst();
        //更新推送结果
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            projectContract.setSchemeHasPush(1);
            projectContractRepository.save(projectContract);
        }
    }

    /**
     * 创建任务下监测方案数据
     * </p>
     * 目前只会有对单个任务进行方案推送，所以只会有一条数据
     *
     * @param sampleFolders   项目的点位数据
     * @param frequencyList   点位频次数据
     * @param folderTestGroup 点位测试项目分组数据
     * @param isAnalyze       是否为分析类型
     * @return 监测方案数据
     */
    private List<RPTaskPlanMonitorVO> createTaskPlanMonitorData(List<DtoSampleFolder> sampleFolders,
                                                                List<DtoSamplingFrequency> frequencyList,
                                                                Map<String, List<DtoTest>> folderTestGroup,
                                                                Boolean isAnalyze) {
        List<RPTaskPlanMonitorVO> result = new ArrayList<>();
        RPTaskPlanMonitorVO planMonitor = new RPTaskPlanMonitorVO();
        //存放点位id与推送点位id的映射Map（key: 点位id，value:推送至监管平台的点位id）
        Map<String, String> folderId2PushIdMap = new HashMap<>();
        //存放点位id与推送点位频次数据id的映射Map（key: 点位id，value:推送至监管平台的频次数据id）
        Map<String, String> frequencyId2PushIdMap = new HashMap<>();
        //点位数据
        planMonitor.setSchemeList(createPlanSchemeData(sampleFolders, folderId2PushIdMap));
        //点位频次数据
        planMonitor.setTaskList(createPlanTaskData(sampleFolders, frequencyList, frequencyId2PushIdMap, folderId2PushIdMap));
        //点位频次测试项目数据
        planMonitor.setProjectList(createPlanTestData(folderTestGroup, frequencyId2PushIdMap, isAnalyze));
        result.add(planMonitor);
        return result;
    }

    /**
     * 创建推送点位数据
     *
     * @param sampleFolders      项目的点位数据
     * @param folderId2PushIdMap 存放点位id与推送点位id的映射Map（key: 点位id，value:推送至监管平台的点位id）
     * @return 推送点位数据
     */
    private List<RPTaskPlanSchemeVO> createPlanSchemeData(List<DtoSampleFolder> sampleFolders, Map<String, String> folderId2PushIdMap) {
        //点位数据
        List<RPTaskPlanSchemeVO> schemeList = new ArrayList<>();
        for (DtoSampleFolder folder : sampleFolders) {
            String pushFolderId = UUIDHelper.NewID();
            folderId2PushIdMap.put(folder.getId(), pushFolderId);
            schemeList.add(new RPTaskPlanSchemeVO(folder, pushFolderId));
        }
        return schemeList;
    }

    /**
     * 创建推送点位频次数据
     *
     * @param sampleFolders         项目的点位数据
     * @param frequencyList         点位频次数据
     * @param frequencyId2PushIdMap 存放点位id与推送点位频次数据id的映射Map（key: 点位id，value:推送至监管平台的频次数据id）
     * @param folderId2PushIdMap    存放点位id与推送点位id的映射Map（key: 点位id，value:推送至监管平台的点位id）
     * @return 推送点位频次数据
     */
    private List<RPTaskPlanTaskVO> createPlanTaskData(List<DtoSampleFolder> sampleFolders, List<DtoSamplingFrequency> frequencyList,
                                                      Map<String, String> frequencyId2PushIdMap, Map<String, String> folderId2PushIdMap) {
        //点位频次数据
        List<RPTaskPlanTaskVO> taskList = new ArrayList<>();
        for (DtoSampleFolder folder : sampleFolders) {
            String pushFrequencyId = UUIDHelper.NewID();
            frequencyId2PushIdMap.put(folder.getId(), pushFrequencyId);
            //获取点位频次周期批次
            List<DtoSamplingFrequency> frequency2Folder = frequencyList.stream().filter(f -> f.getSampleFolderId().equals(folder.getId())).collect(Collectors.toList());
            //获取最大的周期
            DtoSamplingFrequency frequency = frequency2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getPeriodCount)).orElse(null);
            Integer periodCount = 1;
            if (frequency != null) {
                periodCount = frequency.getPeriodCount();
            }
            //获取最大的批次
            Integer timePerPeriod = 1;
            frequency = frequency2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod)).orElse(null);
            if (frequency != null) {
                timePerPeriod = frequency.getTimePerPeriod();
            }
            frequencyId2PushIdMap.put(folder.getId(), pushFrequencyId);
            taskList.add(new RPTaskPlanTaskVO(pushFrequencyId, folderId2PushIdMap.get(folder.getId()), periodCount.toString(), timePerPeriod.toString()));
        }
        return taskList;
    }

    /**
     * 创建推送点位频次测试项目数据
     *
     * @param folderTestGroup       点位测试项目分组
     * @param frequencyId2PushIdMap 存放点位id与推送点位频次数据id的映射Map（key: 点位id，value:推送至监管平台的频次数据id）
     * @param isAnalyze             采样类型是否为分析类型
     * @return 推送频次测试项目数据
     */
    private List<RPTaskPlanTestVO> createPlanTestData(Map<String, List<DtoTest>> folderTestGroup, Map<String, String> frequencyId2PushIdMap, Boolean isAnalyze) {
        //点位频次测试项目数据
        List<RPTaskPlanTestVO> testList = new ArrayList<>();
        folderTestGroup.forEach((folderId, folderTests) ->
                folderTests.forEach(test -> testList.add(new RPTaskPlanTestVO(test, frequencyId2PushIdMap.get(folderId), isAnalyze))));
        return testList;
    }


    /**
     * 校验项目推送信息
     *
     * @param project LIMS项目信息
     */
    private DtoProjectContract validateContract(DtoProject project) {
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(project.getId()).stream().findFirst();
        //判断项目合同信息
        if (!projectContractOp.isPresent()) {
            throw new BaseException("LIMS：项目不存在推送与信息管理信息，请确认！");
        }
        return projectContractOp.get();
    }

    /**
     * 校验方案状态
     *
     * @param project LIMS项目信息
     */
    private void validateStatus(DtoProject project) {
        List<String> statusList = Stream.of(
                        EnumPRO.EnumProjectStatus.方案确认中.name(), EnumPRO.EnumProjectStatus.开展中.name(),
                        EnumPRO.EnumProjectStatus.数据汇总中.name(), EnumPRO.EnumProjectStatus.已办结.name())
                .collect(Collectors.toList());
        if (!statusList.contains(project.getStatus())) {
            throw new BaseException("LIMS：方案需审核完成后才可推送！");
        }
    }

    /**
     * 校验分析方法是否匹配
     *
     * @param project         项目信息
     * @param projectContract 项目推送信息
     */
    private void validTestMatch(DtoProject project, DtoProjectContract projectContract) {
        boolean isAnalyze = EnumPRO.EnumRegulatoryPlatformTaskSamplingType.分析.getValue().toString().equals(projectContract.getIsSampleId());
        List<DtoSampleFolder> folderList = sampleFolderRepository.findByProjectId(project.getId());
        List<String> folderIds = folderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        TestCriteria criteria = new TestCriteria();
        criteria.setSampleFolderIds(folderIds);
        List<DtoTest> testList = folderExtendService.findBySampleFolderId(criteria);
        if (testList.stream().anyMatch(p -> EnumLIM.EnumShTestMatchStatus.未匹配.getValue().equals(p.getShMatchStatus()))) {
            throw new BaseException("LIMS：存在测试项目未匹配分析方法，请确认！如果当前列表中所有分析方法已匹配，请点击左侧[方法匹配同步]刷新后重试！");
        }
        if (!isAnalyze && testList.stream().anyMatch(p -> StringUtil.isEmpty(p.getShSamplingMethodId()))) {
            throw new BaseException("LIMS：存在测试项目未匹配分析方法或采样方法，请确认！");
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.方案信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.方案信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.方案信息.getDeleteMethod();
    }
}
