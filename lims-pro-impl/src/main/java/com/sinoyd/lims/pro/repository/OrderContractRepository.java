package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 合同管理repository
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/12/30
 */
public interface OrderContractRepository extends IBaseJpaRepository<DtoOrderContract, String> {

    /**
     * 根据合同编码和id查询数量
     *
     * @param contractCode 合同编码
     * @param id           id
     * @return
     */
    Integer countByContractCodeAndIdNotAndIsDeletedFalse(String contractCode, String id);

    /**
     * 根据订单id查询
     *
     * @param orderIds 订单id
     * @return 合同信息
     */
    List<DtoOrderContract> findByOrderIdIn(List<String> orderIds);

    /**
     * 根据监管平台合同id查询合同数据
     *
     * @param rpConstractId 监管平台合同id
     * @return 合同数据
     */
    List<DtoOrderContract> findByCId(String rpConstractId);

    /**
     * 根据监管平台合同id查询合同数据
     *
     * @param rpConstractIds 监管平台合同id集合
     * @return 合同数据
     */
    List<DtoOrderContract> findByCIdIn(Collection<String> rpConstractIds);

    /**
     * 判断是否存在相同的样品编号
     *
     * @param code 样品编号
     * @return 相同样品编号的数量
     */
    @Query("select count(s.id) from DtoOrderContract s where s.isDeleted = 0 and s.contractCode = :code ")
    Integer countByCode(@Param("code") String code);
}
