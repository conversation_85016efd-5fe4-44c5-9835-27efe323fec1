package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 现场送样提交
 *
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_RECEIVE)
public class ReceiveSubmitStrategy extends AbsSubmitRestrictStrategy {
    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String receiveId = objMap.toString();
        //排除比对样 -- 质控和替代
        List<DtoSample> sampleList = sampleService.findByReceiveId(receiveId).stream()
                .filter(p -> !p.getIsDeleted() &&
                        !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataService.findDataBySampleIds(sampleIds).stream()
                .filter(p -> !p.getIsOutsourcing() && (!p.getIsDeleted() || p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.作废.getValue())))
                .collect(Collectors.toList());
        DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordService.findOne(receiveId);
//        restrictVoList.add(super.checkStatus(status, receiveSampleRecord.getInfoStatus().toString(), EnumPRO.EnumRestrictItem.提交状态验证));
        //是否存在空的样品编号
        restrictVoList.add(checkSampleCode(sampleList));
        //是否存在交接单，存在则判断电子签名，不存在则不需要判断
        if (super.checkDocument(receiveId, BaseCodeHelper.DOCUMENT_SAMPLE_RECORD).size() > 0) {
            restrictVoList.add(checkHaveSignature(PrincipalContextUser.getPrincipal().getUserId()));
        }
        if (config2TestByDataList(analyseDataList).size() > 0) {
            restrictVoList.add(super.checkRemind(receiveId));
        }
        if (receiveSampleRecord.getIsBatchReceive()) {
            restrictVoList.add(checkSampleGroup(sampleIds));
        }
        return restrictVoList;
    }

    /**
     * 分批接样时，验证是否填写接样人接样日期
     *
     * @param sampleIds 样品Ids
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSampleGroup(List<String> sampleIds) {
        List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findBySampleIdIn(sampleIds);
        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        // 筛选接样人等于空，或者接样日期等于空或者1753 的数据
        List<DtoSampleGroup> sampleGroups = sampleGroupList.stream().filter(p -> StringUtil.isEmpty(p.getRecipientName()) ||
                StringUtil.isNull(p.getReceiveSampleDate()) ||
                date1753.compareTo(p.getReceiveSampleDate()) == 0).collect(Collectors.toList());
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.交接单接样数据验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.交接单接样数据验证.getModuleName());
        if (StringUtil.isEmpty(sampleGroupList)) {
            restrictVo.setExceptionOption("样品交接单不能为空");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        } else if (StringUtil.isNotEmpty(sampleGroups)) {
            restrictVo.setExceptionOption("交接单中接样人或接样日期未填写");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }


    /**
     * 样品编号验证
     *
     * @param sampleList 样品集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSampleCode(List<DtoSample> sampleList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        //样品编号
        sampleList = sampleList.stream().filter(p -> !StringUtil.isNotEmpty(p.getCode())).collect(Collectors.toList());
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.交接是否空编号.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.交接是否空编号.getModuleName());
        if (sampleList.size() > 0) {
            restrictVo.setExceptionOption("存在样品编号为空的样品");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 验证采样单签名
     *
     * @param userId 当前人员id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkHaveSignature(String userId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        DtoPerson person = personRepository.findOne(userId);
        List<DtoDocument> documents = this.checkDocument(userId, BaseCodeHelper.DOCUMENT_EXTEND_TYPE_SIGNATURE);
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.采样单电子签名.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.采样单电子签名.getModuleName());
        if (documents.size() == 0) {
            restrictVo.setExceptionOption("未上传电子签名");
            restrictVo.setIsUnusual(Boolean.FALSE);
        } else {
            for (DtoDocument document : documents) {
                if (!restrictVo.getIsUnusual()) {
                    break;
                }
                try {
                    InputStream stream = new FileInputStream(filePathConfig.getFilePath() + document.getPath());
                } catch (FileNotFoundException e) {
                    restrictVo.setExceptionOption(String.format("未找到%s上传的电子签名", person.getCName()));
                    restrictVo.setIsUnusual(Boolean.FALSE);
                }
            }
        }
        return restrictVo;
    }
}
