package com.sinoyd.lims.pro.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.criteria.FolderPeriodCriteria;
import com.sinoyd.lims.pro.criteria.SamplingArrangeCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoPoiSamplingArrange;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.Arrange2MethodService;
import com.sinoyd.lims.pro.service.SamplingArrangeService;
import com.sinoyd.lims.pro.service.SamplingFrequencyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * 采样计划安排接口实现类
 * <AUTHOR>
 * @version V1.0.0 2023/11/08
 * @since V100R001
 */
@Service
@Slf4j
public class SamplingArrangeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingArrange,String,SamplingArrangeRepository> implements SamplingArrangeService {

    private SampleTypeRepository sampleTypeRepository;

    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    private SamplingFrequencyRepository samplingFrequencyRepository;

    private SamplingFrequencyService samplingFrequencyService;

    private ProjectRepository projectRepository;

    private SampleFolderRepository sampleFolderRepository;

    private ProjectTypeService projectTypeService;

    private TestRepository testRepository;

    private Arrange2MethodService arrange2MethodService;

    private SampleRepository sampleRepository;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private PersonRepository personRepository;

    private boolean needInit = true;

    @Override
    public void findByPage(PageBean<DtoSamplingArrange> page, BaseCriteria criteria) {
        newFindByPage(page, criteria, true);
    }

    /**
     * 可控制是否填充分页数据方分页方法
     *
     * @param page                  分页参数
     * @param criteria              查询参数
     * @param isLoadTransientFileds 是否填充附加数据
     */
    private void newFindByPage(PageBean<DtoSamplingArrange> page, BaseCriteria criteria, Boolean isLoadTransientFileds) {
        long t1 = System.currentTimeMillis();
        //数据同步，避免没有初始化数据
        if (needInit) {
            samplingFrequencyService.syncFolderPeriod(null, Boolean.FALSE);
            needInit = false;
        }
        //基础数据查询
        page.setEntityName("DtoSamplingArrange a,DtoProject p,DtoSampleType s,DtoSampleFolder d");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        long t2 = System.currentTimeMillis();
        log.info("列表数据查询：" + (t2 - t1));
        //附加数据查询
        List<DtoSamplingArrange> list = page.getData();
        if (isLoadTransientFileds) {
            queryAdditionalData(list);
        }
        long t3 = System.currentTimeMillis();
        log.info("列表附加数据查询：" + (t3 - t2));
        page.setData(list);
    }

    @Override
    public PageBean<DtoProject> showTree(PageBean<DtoSamplingArrange> pageBean, BaseCriteria samplingArrangeCriteria) {
        //保存分页 及 每页数据
        int pageNo = pageBean.getPageNo();
        int pageSize = pageBean.getRowsPerPage();
        //查询所有周期
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        newFindByPage(pageBean, samplingArrangeCriteria, false);
        //项目分组
        List<DtoSamplingArrange> originData = pageBean.getData();
        //相关数据查询
        long t1 = System.currentTimeMillis();
        List<String> projectIds = originData.stream().map(DtoSamplingArrange::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> allProjectList = projectRepository.findAll(projectIds);
        allProjectList.sort(Comparator.comparing(DtoProject::getProjectCode));
        List<String> projectTypeIds = allProjectList.stream().map(DtoProject::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);
        long t2 = System.currentTimeMillis();
        log.info("树结构相关数据查询：" + (t2 - t1));
        //构建树结构
        List<DtoProject> dtoProjectList = allProjectList.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        List<String> pageProjectIds = dtoProjectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        originData = originData.stream().filter(a -> pageProjectIds.contains(a.getProjectId())).collect(Collectors.toList());
        queryAdditionalData(originData);
        for (DtoProject project : dtoProjectList) {
            //获取下属点位周期并 排序 （是否安排/检测类型/点位/周期）
            List<DtoSamplingArrange> arrangeList = originData.stream().filter(a -> project.getId().equals(a.getProjectId())).collect(Collectors.toList());
            arrangeList.sort(Comparator.comparing(DtoSamplingArrange::getIsArrange).thenComparing(DtoSamplingArrange::getSampleType)
                    .thenComparing(DtoSamplingArrange::getWatchSpot).thenComparing(DtoSamplingArrange::getPeriodCount));
            project.setArrangeList(arrangeList);
            //未安排提示
            project.setArrangeMsg(buildArrangeMsg(arrangeList));
            //项目类型
            Optional<DtoProjectType> projectTypeOptional = projectTypes.parallelStream().filter(p -> project.getProjectTypeId().equals(p.getId()))
                    .findFirst();
            projectTypeOptional.ifPresent(p -> {
                project.setProjectTypeName(StringUtil.isNotEmpty(p.getName()) ? p.getName() : "");
            });
            project.setSampleType("");
        }
        PageBean<DtoProject> projectPageBean = new PageBean<>();
        projectPageBean.setData(dtoProjectList);
        projectPageBean.setRowsCount(allProjectList.size());
        long t3 = System.currentTimeMillis();
        log.info("树结构构建：" + (t3 - t2));
        return projectPageBean;
    }

    @Override
    public DtoSamplingArrange findBySamplingPlanId(String samplingPlanId) {
        List<DtoSamplingArrange> samplingArrangeList = repository.findBySamplingPlanId(samplingPlanId);
        queryAdditionalData(samplingArrangeList);
        List<DtoArrange2Method> arrange2MethodList = arrange2MethodService.findBySamplingPlanId(samplingPlanId);
        DtoSamplingArrange samplingArrange = new DtoSamplingArrange();
        samplingArrange.setSamplingPlanId(samplingPlanId);
        samplingArrange.setSamplingArrangeList(samplingArrangeList);
        samplingArrange.setArrange2MethodList(arrange2MethodList);
        samplingArrange.setPlanSamplingTime(samplingArrangeList.get(0).getPlanSamplingTime());
        samplingArrange.setTeam(samplingArrangeList.get(0).getTeam());
        samplingArrange.setTeamId(samplingArrangeList.get(0).getTeamId());
        samplingArrange.setChargePerson(samplingArrangeList.get(0).getChargePerson());
        samplingArrange.setChargePersonId(samplingArrangeList.get(0).getChargePersonId());
        samplingArrange.setCar(samplingArrangeList.get(0).getCar());
        samplingArrange.setCarId(samplingArrangeList.get(0).getCarId());
        samplingArrange.setIsArrange(samplingArrangeList.get(0).getIsArrange());
        if (StringUtil.isNotEmpty(samplingArrangeList.get(0).getSamplingPeople())) {
            samplingArrange.setPersonList(Arrays.asList(samplingArrangeList.get(0).getSamplingPeople().split("、")));
        }
        if (StringUtil.isNotEmpty(samplingArrangeList.get(0).getSamplingPeopleIds())) {
            samplingArrange.setPersonIdList(Arrays.asList(samplingArrangeList.get(0).getSamplingPeopleIds().split(";")));
        }
        return samplingArrange;
    }

    @Override
    @Transactional
    public void submit(List<String> samplingPlanIds, String status) {
        List<DtoSamplingArrange> samplingArrangeList = repository.findBySamplingPlanIdIn(samplingPlanIds);
        samplingArrangeList.forEach(a -> a.setStatus(status));
        super.save(samplingArrangeList);
    }

    /**
     * 构建未安排提示
     *
     * @param arrangeList 数据集
     * @return 提示信息
     */
    private String buildArrangeMsg(List<DtoSamplingArrange> arrangeList) {
        List<DtoSamplingArrange> notArrangeList = arrangeList.stream().filter(a -> Boolean.FALSE.equals(a.getIsArrange())).collect(Collectors.toList());
        if (!notArrangeList.isEmpty()) {
            StringBuilder msg = new StringBuilder("未安排：");
            Map<String, List<DtoSamplingArrange>> typeMap = notArrangeList.stream().collect(Collectors.groupingBy(DtoSamplingArrange::getSampleType));
            typeMap.forEach((k, v) -> {
                msg.append(k).append("(").append(v.size()).append(") ");
            });
            return msg.toString();
        }
        return "";
    }

    /**
     * 查询额外数据
     *
     * @param list 原数据
     */
    private void queryAdditionalData(List<DtoSamplingArrange> list) {
        long t1 = System.currentTimeMillis();
        //关联数据集
        List<String> projectIds = list.stream().map(DtoSamplingArrange::getProjectId).collect(Collectors.toList());
        List<DtoProject> dtoProjectList = projectRepository.findAll(projectIds);
        long t2 = System.currentTimeMillis();
        log.info("dtoProjectList：" + (t2 - t1));
        List<String> folderIdlist = list.stream().map(DtoSamplingArrange::getSampleFolderId).collect(Collectors.toList());
        List<DtoSampleFolder> dtoSampleFolderList = sampleFolderRepository.findAll(folderIdlist);
        long t3 = System.currentTimeMillis();
        log.info("dtoSampleFolderList：" + (t3 - t2));
        List<DtoSampleType> dtoSampleTypeList = sampleTypeRepository.findAll();
        long t4 = System.currentTimeMillis();
        log.info("dtoSampleTypeList：" + (t4 - t3));
        List<DtoSamplingFrequency> dtoSamplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(folderIdlist);
        long t5 = System.currentTimeMillis();
        log.info("dtoSamplingFrequencyList：" + (t5 - t4));
        List<DtoSamplingFrequencyTest> dtoSamplingFrequencyTestList = samplingFrequencyTestRepository.findBySampleFolderIdIn(folderIdlist);
        long t6 = System.currentTimeMillis();
        log.info("dtoSamplingFrequencyTestList：" + (t6 - t5));
        List<DtoTest> dtoTestList = testRepository.findAll();
        long t7 = System.currentTimeMillis();
        log.info("dtoTestList：" + (t7 - t6));
        //项目编号 项目名称  点位名称 点位编号 检测类型 周期文本 分析项目名称
        for (DtoSamplingArrange dtoSamplingArrange : list) {
            //项目编号 项目名称
            DtoProject dtoProject = dtoProjectList.stream().filter(p -> p.getId().equals(dtoSamplingArrange.getProjectId())).findFirst().orElse(null);
            if (dtoProject != null) {
                dtoSamplingArrange.setProjectCode(dtoProject.getProjectCode());
                dtoSamplingArrange.setProjectName(dtoProject.getProjectName());
            }
            //点位名称 点位编号
            DtoSampleFolder dtoSampleFolder = dtoSampleFolderList.stream().filter(f -> f.getId().equals(dtoSamplingArrange.getSampleFolderId())).findFirst().orElse(null);
            if (dtoSampleFolder != null) {
                dtoSamplingArrange.setFolderCode(dtoSampleFolder.getFolderCode());
                dtoSamplingArrange.setWatchSpot(dtoSampleFolder.getWatchSpot());
            }
            //检测类型
            DtoSampleType dtoSampleType = dtoSampleTypeList.stream().filter(t -> t.getId().equals(dtoSamplingArrange.getSampleTypeId())).findFirst().orElse(null);
            if (dtoSampleType != null) {
                dtoSamplingArrange.setSampleType(dtoSampleType.getTypeName());
            }
            //分析项目名称
            //BUG2024011299340 按分析系项目名称顺序排列；分析项目能够能够根据合并规则，进行总称合并显示；
            List<String> tempFrequencyIdList = dtoSamplingFrequencyList.stream().filter(a -> a.getSampleFolderId().equals(dtoSamplingArrange.getSampleFolderId())
                    && a.getPeriodCount().equals(dtoSamplingArrange.getPeriodCount())).map(DtoSamplingFrequency::getId).collect(Collectors.toList());
            List<String> testIds = dtoSamplingFrequencyTestList.stream().filter(t -> tempFrequencyIdList.contains(t.getSamplingFrequencyId()))
                    .map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> testList = dtoTestList.stream().filter(t -> testIds.contains(t.getId())).collect(Collectors.toList());
            handleTotal(dtoTestList, testList);
            List<String> analyzeItemList = testList.stream().map(DtoTest::getRedAnalyzeItemName).distinct().sorted().collect(Collectors.toList());
            dtoSamplingArrange.setRedAnalyzeItemName(String.join("、", analyzeItemList));
            //周期文本
            dtoSamplingArrange.setPeriodName("第" + dtoSamplingArrange.getPeriodCount() + "周期");
        }
        long t8 = System.currentTimeMillis();
        log.info("遍历处理数据：" + (t8 - t7));
    }

    /**
     * 处理测试项目总称显示
     *
     * @param dtoTestList     所有测试项目
     * @param currentTestList 需重组测试项目
     */
    private void handleTotal(List<DtoTest> dtoTestList, List<DtoTest> currentTestList) {
        //查询父项目
        List<String> parentTestIds = currentTestList.stream().map(DtoTest::getParentId).collect(Collectors.toList());
        List<DtoTest> parentTestList = dtoTestList.stream().filter(t -> parentTestIds.contains(t.getId()) && Boolean.TRUE.equals(t.getIsTotalTest())).collect(Collectors.toList());
        //总称测试项目重组
        for (DtoTest parentTest : parentTestList) {
            List<DtoTest> childrenTestList = currentTestList.stream().filter(t -> parentTest.getId().equals(t.getParentId())).collect(Collectors.toList());
            if (childrenTestList.size() > parentTest.getMergeBase()) {
                currentTestList.removeAll(childrenTestList);
                currentTestList.add(parentTest);
            }
        }
    }

    @Override
    @Transactional
    public DtoSamplingArrange batchSaveArrange(DtoSamplingArrange dtoSamplingArrange) {
        if (StringUtil.isEmpty(dtoSamplingArrange.getSamplingPlanId())) {
            dtoSamplingArrange.setSamplingPlanId(UUIDHelper.NewID());
        }
        List<DtoSamplingArrange> existsSamplingArrangeList = repository.findBySamplingPlanId(dtoSamplingArrange.getSamplingPlanId());
        List<DtoSamplingArrange> delSamplingArrangeList = existsSamplingArrangeList.stream()
                .filter(s -> dtoSamplingArrange.getIdList().stream().noneMatch(a -> a.equals(s.getId())))
                .collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delSamplingArrangeList)) {
            for (DtoSamplingArrange samplingArrange : delSamplingArrangeList) {
                samplingArrange.setSamplingPlanId(UUIDHelper.GUID_EMPTY);
                samplingArrange.setIsArrange(false);
                samplingArrange.setStatus("");
            }
            repository.save(delSamplingArrangeList);
        }
        if (StringUtil.isNotNull(dtoSamplingArrange.getIdList())) {
            List<DtoSamplingArrange> arrangeList = repository.findAll(dtoSamplingArrange.getIdList());
            if (StringUtil.isNotNull(arrangeList)) {
                for (DtoSamplingArrange target : arrangeList) {
                    copyPartProperties(dtoSamplingArrange, target);
                }
                repository.save(arrangeList);
            }
        }
        List<DtoArrange2Method> existsArrange2MethodList = arrange2MethodService.findBySamplingPlanId(dtoSamplingArrange.getSamplingPlanId());
        List<DtoArrange2Method> delArrange2MethodList = existsArrange2MethodList.stream().filter(a -> dtoSamplingArrange.getArrange2MethodList().stream().noneMatch(m -> m.getId().equals(a.getId()))).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delArrange2MethodList)) {
            arrange2MethodService.delete(delArrange2MethodList);
        }
        if (StringUtil.isNotEmpty(dtoSamplingArrange.getArrange2MethodList())) {
            for (DtoArrange2Method arrange2Method : dtoSamplingArrange.getArrange2MethodList()) {
                arrange2Method.setSamplingPlanId(dtoSamplingArrange.getSamplingPlanId());
            }
            arrange2MethodService.save(dtoSamplingArrange.getArrange2MethodList());
        }
        return dtoSamplingArrange;
    }

    /**
     * 部分属性复制
     *
     * @param source 源对象
     * @param target 目标对象
     */
    private void copyPartProperties(DtoSamplingArrange source, DtoSamplingArrange target) {
        target.setPlanSamplingTime(source.getPlanSamplingTime());
        target.setTeam(source.getTeam());
        target.setTeamId(source.getTeamId());
        target.setChargePerson(source.getChargePerson());
        target.setChargePersonId(source.getChargePersonId());
        target.setCar(source.getCar());
        target.setCarId(source.getCarId());
        if (StringUtil.isNotNull(source.getPersonList())) {
            target.setSamplingPeople(String.join("、", source.getPersonList()));
        }
        if (StringUtil.isNotNull(source.getPersonIdList())) {
            target.setSamplingPeopleIds(String.join(";", source.getPersonIdList()));
        }
        if (Boolean.TRUE.equals(source.getIsChangeStatus())) {
            target.setIsArrange(true);
            target.setSamplingPlanId(source.getSamplingPlanId());
            target.setStatus(EnumLIM.EnumSamplingArrangeStatus.编辑中.name());
        }
    }

    @Override
    @Transactional
    public void batchDelArrange(List<String> ids) {
        List<DtoSamplingArrange> dtoSamplingArrangeList = repository.findAll(ids);
        for (DtoSamplingArrange dtoSamplingArrange : dtoSamplingArrangeList) {
            clearPartProperties(dtoSamplingArrange);
        }
        repository.save(dtoSamplingArrangeList);
    }

    /**
     * 安排属性清除
     *
     * @param target 目标对象
     */
    private void clearPartProperties(DtoSamplingArrange target) {
        target.setPlanSamplingTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        target.setTeam(null);
        target.setTeamId(UUIDHelper.GUID_EMPTY);
        target.setChargePerson("");
        target.setChargePersonId(UUIDHelper.GUID_EMPTY);
        target.setCar(null);
        target.setCarId(UUIDHelper.GUID_EMPTY);
        target.setSamplingPeople(null);
        target.setSamplingPeopleIds(null);
        target.setIsArrange(false);
        target.setSamplingPlanId(UUIDHelper.GUID_EMPTY);
        target.setStatus("");
    }


    @Override
    public void collectPreviewData(List<DtoSamplingArrange> data, List<Map<String, Object>> previewData) {
        //日期分组并排序
        List<Date> dateList = data.stream().map(DtoSamplingArrange::getPlanSamplingTime)
                .filter(StringUtil::isNotNull).distinct().sorted().collect(Collectors.toList());
        for (Date date : dateList) {
            Map<String, Object> dateMap = new HashMap<>();
            List<DtoSamplingArrange> dateDataList = data.stream().filter(d -> date.equals(d.getPlanSamplingTime())).collect(Collectors.toList());
            //统计日期数据
            collectDateMapPartData(dateMap, date, dateDataList);
            //统计负责人下属数据
            List<Map<String, Object>> chargePersonRelatedDataList = new ArrayList<>();
            collectDateTeamData(dateMap, chargePersonRelatedDataList, dateDataList);
            previewData.add(dateMap);
        }
    }

    @Override
    public void submitArrange(List<String> ids) {
        List<DtoSamplingArrange> dtoSamplingArrangeList = repository.findAll(ids);
        List<String> projectIds = dtoSamplingArrangeList.stream().map(DtoSamplingArrange::getProjectId).collect(Collectors.toList());
        List<DtoProject> dtoProjectList = projectRepository.findAll(projectIds);
        List<String> folderIdlist = dtoSamplingArrangeList.stream().map(DtoSamplingArrange::getSampleFolderId).collect(Collectors.toList());
        List<DtoSampleFolder> dtoSampleFolderList = sampleFolderRepository.findAll(folderIdlist);
        for (DtoSamplingArrange dtoSamplingArrange : dtoSamplingArrangeList) {
            //项目名称
            DtoProject dtoProject = dtoProjectList.stream().filter(p -> p.getId().equals(dtoSamplingArrange.getProjectId())).findFirst().orElse(null);
            if (dtoProject != null) {
                dtoSamplingArrange.setProjectName(dtoProject.getProjectName());
            }
            //点位名称
            DtoSampleFolder dtoSampleFolder = dtoSampleFolderList.stream().filter(f -> f.getId().equals(dtoSamplingArrange.getSampleFolderId())).findFirst().orElse(null);
            if (dtoSampleFolder != null) {
                dtoSamplingArrange.setWatchSpot(dtoSampleFolder.getWatchSpot());
            }
            if (StringUtil.isEmpty(dtoSamplingArrange.getChargePerson())) {
                throw new BaseException(String.format("项目：%s/点位：%s/周期：%s，未设置采样负责人",
                        dtoSamplingArrange.getProjectName(), dtoSamplingArrange.getWatchSpot(), dtoSamplingArrange.getPeriodCount()));
            }
            String planSamplingTime = DateUtil.dateToString(dtoSamplingArrange.getPlanSamplingTime(), DateUtil.YEAR);
            if ("1753-01-01".equals(planSamplingTime)) {
                throw new BaseException(String.format("项目：%s/点位：%s/周期：%s，未设置采样时间",
                        dtoSamplingArrange.getProjectName(), dtoSamplingArrange.getWatchSpot(), dtoSamplingArrange.getPeriodCount()));
            }
            dtoSamplingArrange.setIsArrange(true);
            dtoSamplingArrange.setStatus(EnumLIM.EnumSamplingArrangeStatus.编辑中.name());
            dtoSamplingArrange.setSamplingPlanId(UUIDHelper.NewID());
        }
        repository.save(dtoSamplingArrangeList);
    }

    @Override
    public DtoSamplingArrange findAttachPath(String id) {
        return repository.findOne(id);
    }

    @Override
    public void download(BaseCriteria criteria, HttpServletResponse response) {
        SamplingArrangeCriteria samplingArrangeCriteria = (SamplingArrangeCriteria)criteria;
        samplingArrangeCriteria.setIsArrange(true);
        PageBean<DtoSamplingArrange> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setSort("planSamplingTime-chargePerson+");
        findByPage(pageBean,samplingArrangeCriteria);
        List<DtoPoiSamplingArrange> list = new ArrayList<>();
        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        pageBean.getData().forEach(v->{
            if (StringUtil.isNotNull(v.getPlanSamplingTime()) && v.getPlanSamplingTime().compareTo(date1753) != 0) {
                DtoPoiSamplingArrange poiSamplingArrange = new DtoPoiSamplingArrange();
                BeanUtils.copyProperties(v,poiSamplingArrange);
                list.add(poiSamplingArrange);
            }
        });
        ExportParams params = new ExportParams();
        params.setStyle(ExcelStyle.class);
        PoiExcelUtils.exportExcel(list,DtoPoiSamplingArrange.class,"采样计划安排表",params,response);
    }

    @Override
    public void folderPeriodPageQuery(PageBean<DtoSamplingArrange> pageBean, BaseCriteria folderPeriodCriteria) {
        long t1 = System.currentTimeMillis();
        pageBean.setEntityName("DtoSamplingArrange a");
        pageBean.setSelect("select a");
        FolderPeriodCriteria criteria = (FolderPeriodCriteria)folderPeriodCriteria;
        int page = pageBean.getPageNo();
        int rows = pageBean.getRowsPerPage();
        boolean simplePage = StringUtil.isEmpty(criteria.getStartTime())
                &&StringUtil.isEmpty(criteria.getEndTime())
                &&StringUtil.isEmpty(criteria.getRecordCode());
        if(simplePage){
            super.findByPage(pageBean, criteria);
        }else{
            pageBean.setPageNo(1);
            pageBean.setRowsPerPage(Integer.MAX_VALUE);
            super.findByPage(pageBean, criteria);
        }
        List<DtoSamplingArrange> list = pageBean.getData();
        long t2 = System.currentTimeMillis();
        log.info("数据查询耗时："+(t2-t1));
        if(StringUtil.isNotEmpty(list)){
            queryAdditionalData(list);
            long t3 = System.currentTimeMillis();
            log.info("基础附加字段处理："+(t3-t2));
            //采样人 采样日期 送样单号
            List<DtoSample> sampleList;
            if(simplePage){
                List<String> sampleFolderIds = list.stream().map(DtoSamplingArrange::getSampleFolderId).collect(Collectors.toList());
                sampleList = sampleRepository.findBySampleFolderIdIn(sampleFolderIds);
            }else{
                sampleList = sampleRepository.findAll();
            }
            List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findAll();
            List<DtoPerson> personList = personRepository.findAll();
            for (DtoSamplingArrange arrange:list) {
                List<DtoSample> folderSampleList = sampleList.stream().filter(v->StringUtil.isNotEmpty(v.getSampleFolderId())
                        &&v.getSampleFolderId().equals(arrange.getSampleFolderId())).collect(Collectors.toList());
                Set<String> samplingPersonIds = folderSampleList.stream().map(DtoSample::getSamplingPersonId).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
                List<String> names = personList.stream().filter(v->samplingPersonIds.contains(v.getId())).map(DtoPerson::getCName)
                        .distinct().sorted().collect(Collectors.toList());
                arrange.setActualPeopleStr(String.join("、",names));
                List<String> dates = folderSampleList.stream().filter(v->v.getSamplingTimeBegin()!=null)
                        .map(v->DateUtil.dateToString(v.getSamplingTimeBegin(),DateUtil.YEAR)).distinct().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                arrange.setActualDateStr(String.join("、",dates));
                List<String> receiveIds = folderSampleList.stream().map(DtoSample::getReceiveId).collect(Collectors.toList());
                List<String> recordCodes = recordList.stream().filter(v->receiveIds.contains(v.getId())).map(DtoReceiveSampleRecord::getRecordCode)
                        .distinct().sorted().collect(Collectors.toList());
                arrange.setRecordCode(String.join("、",recordCodes));
            }
            long t4 = System.currentTimeMillis();
            log.info("排序附加字段处理："+(t4-t3));
        }
        if(!simplePage){
            //特殊检索部分  采样日期区间 送样单号
            if(StringUtil.isNotEmpty(criteria.getRecordCode())){
                list = list.stream().filter(v->StringUtil.isNotEmpty(v.getRecordCode())&& v.getRecordCode().contains(criteria.getRecordCode()))
                        .collect(Collectors.toList());
            }
            if(StringUtil.isNotEmpty(criteria.getStartTime())){
                Date startTime = DateUtil.stringToDate(criteria.getStartTime(),DateUtil.YEAR);
                list = list.stream().filter(v->{
                    if(StringUtil.isNotEmpty(v.getActualDateStr())){
                        String[] dates = v.getActualDateStr().split("、");
                        for (String dateStr:dates) {
                            Date date = DateUtil.stringToDate(dateStr,DateUtil.YEAR);
                            if(startTime.equals(date)||startTime.before(date)){
                                return true;
                            }
                        }
                    }
                    return false;
                }).collect(Collectors.toList());
            }
            if(StringUtil.isEmpty(criteria.getEndTime())){
                Date endTime = DateUtil.stringToDate(criteria.getEndTime(),DateUtil.YEAR);
                list = list.stream().filter(v->{
                    if(StringUtil.isNotEmpty(v.getActualDateStr())){
                        String[] dates = v.getActualDateStr().split("、");
                        for (String dateStr:dates) {
                            Date date = DateUtil.stringToDate(dateStr,DateUtil.YEAR);
                            if(endTime.equals(date)||endTime.after(date)){
                                return true;
                            }
                        }
                    }
                    return false;
                }).collect(Collectors.toList());
            }
        }
        //列表按采样日期倒序、检测类型、送样单号、点位名称顺序排列；
        list.sort(Comparator.comparing(DtoSamplingArrange::getActualDateStr,Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(DtoSamplingArrange::getSampleType,Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoSamplingArrange::getRecordCode,Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoSamplingArrange::getWatchSpot,Comparator.nullsLast(Comparator.naturalOrder())));
        if(!simplePage){
            pageBean.setRowsCount(list.size());
            list =list.stream().skip((long) (page - 1) *rows).limit(rows).collect(Collectors.toList());
        }
        pageBean.setData(list);
        long t5 = System.currentTimeMillis();
        log.info("全部查询："+(t5-t1));
    }

    /**
     * 统计当日数据
     *
     * @param dateMap      数据容器
     * @param date         日期
     * @param dateDataList 日期相关原始数据
     */
    private void collectDateMapPartData(Map<String, Object> dateMap, Date date, List<DtoSamplingArrange> dateDataList) {
        //日期
        dateMap.put("date", DateUtil.dateToString(date, DateUtil.YEAR));
        Set<String> chargePersonIdSet = new HashSet<>();
        Set<String> folderIdSet = new HashSet<>();
        int summerSampleCount = 0;
        for (DtoSamplingArrange dtoSamplingArrangePlan : dateDataList) {
            if (StringUtil.isNotEmpty(dtoSamplingArrangePlan.getChargePersonId())) {
                chargePersonIdSet.add(dtoSamplingArrangePlan.getChargePersonId());
            }
            if (StringUtil.isNotEmpty(dtoSamplingArrangePlan.getSampleFolderId())) {
                folderIdSet.add(dtoSamplingArrangePlan.getSampleFolderId());
            }
            if (StringUtil.isNotNull(dtoSamplingArrangePlan.getSampleCount())) {
                summerSampleCount += dtoSamplingArrangePlan.getSampleCount();
            }
        }
        //采样小组数
        dateMap.put("chargePersonCountSet", chargePersonIdSet.size());
        //计划采样点位数
        dateMap.put("folderCount", folderIdSet.size());
        //计划样品数
        dateMap.put("sampleCount", summerSampleCount);
    }

    /**
     * 统计团队数据及挂载三级数据
     *
     * @param dateMap                     数据结果容器
     * @param chargePersonRelatedDataList 团队数据容器
     * @param dateDataList                当日数据集
     */
    private void collectDateTeamData(Map<String, Object> dateMap, List<Map<String, Object>> chargePersonRelatedDataList, List<DtoSamplingArrange> dateDataList) {
        List<String> chargePersonIdList = dateDataList.stream().sorted(Comparator.comparing(DtoSamplingArrange::getChargePerson))
                .map(DtoSamplingArrange::getChargePersonId).distinct().collect(Collectors.toList());
        for (String chargePersonId : chargePersonIdList) {
            Map<String, Object> map = new HashMap<>();
            List<DtoSamplingArrange> singleTeamDataList = dateDataList.stream().filter(d -> chargePersonId.equals(d.getChargePersonId())).collect(Collectors.toList());
            //挂载三级数据
            map.put("list", singleTeamDataList);
            //采样小组名称
            map.put("chargePerson", singleTeamDataList.get(0).getChargePerson());
            int summer = 0;
            for (DtoSamplingArrange singleTeamData : singleTeamDataList) {
                if (StringUtil.isNotNull(singleTeamData.getSampleCount())) {
                    summer += singleTeamData.getSampleCount();
                }
            }
            //计划采样数
            map.put("sampleCount", summer);
            chargePersonRelatedDataList.add(map);
        }
        dateMap.put("chargePersonRelatedDataList", chargePersonRelatedDataList);
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestRepository(SamplingFrequencyTestRepository samplingFrequencyTestRepository) {
        this.samplingFrequencyTestRepository = samplingFrequencyTestRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }


    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    @Lazy
    public void setSamplingFrequencyService(SamplingFrequencyService samplingFrequencyService) {
        this.samplingFrequencyService = samplingFrequencyService;
    }

    @Autowired
    @Lazy
    public void setProjectTypeService(ProjectTypeService projectTypeService) {
        this.projectTypeService = projectTypeService;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setArrange2MethodService(Arrange2MethodService arrange2MethodService) {
        this.arrange2MethodService = arrange2MethodService;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}
