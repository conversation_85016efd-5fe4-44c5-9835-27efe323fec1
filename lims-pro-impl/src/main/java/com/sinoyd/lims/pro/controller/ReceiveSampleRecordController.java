package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.customer.DtoUpdateAnalyst;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.pro.criteria.*;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecordParamInfo;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecordParamTemplate;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import com.sinoyd.lims.pro.service.SampleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * ReceiveSampleRecord服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: ReceiveSampleRecord服务")
@RestController
@RequestMapping("api/pro/receiveSampleRecord")
public class ReceiveSampleRecordController extends BaseJpaController<DtoReceiveSampleRecord, String, ReceiveSampleRecordService> {

    @Autowired
    private SampleService sampleService;

    /**
     * 分页动态条件查询送样单
     *
     * @param receiveSampleRecordCriteria 条件参数
     * @return RestResponse<List < ReceiveSampleRecord>>
     */
    @ApiOperation(value = "分页动态条件查询送样单", notes = "分页动态条件查询送样单")
    @GetMapping
    public RestResponse<List<DtoReceiveSampleRecord>> findByPage(ReceiveSampleRecordCriteria receiveSampleRecordCriteria) {
        PageBean<DtoReceiveSampleRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoReceiveSampleRecord>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, receiveSampleRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 分页动态条件查询送样单统计
     *
     * @param receiveSampleRecordCriteria 条件参数
     * @return RestResponse<List < DtoReceiveSampleRecordQuery>>
     */
    @ApiOperation(value = "分页动态条件查询送样单", notes = "分页动态条件查询送样单")
    @GetMapping(path = "/query")
    public RestResponse<List<DtoReceiveSampleRecordQuery>> queryByPage(ReceiveSampleRecordQueryCriteria receiveSampleRecordCriteria) {
        PageBean<DtoReceiveSampleRecordQuery> pageBean = super.getPageBean();
        RestResponse<List<DtoReceiveSampleRecordQuery>> restResponse = new RestResponse<>();
        service.queryByPage(pageBean, receiveSampleRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询送样单
     *
     * @param id 主键id
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "按主键查询送样单", notes = "按主键查询送样单")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReceiveSampleRecord> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReceiveSampleRecord> restResponse = new RestResponse<>();
        DtoReceiveSampleRecord receiveSampleRecord = service.findOne(id);
        restResponse.setData(receiveSampleRecord);
        restResponse.setRestStatus(StringUtil.isNull(receiveSampleRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增送样单
     *
     * @param receiveSampleRecord 实体列表
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "新增送样单", notes = "新增送样单")
    @PostMapping
    public RestResponse<DtoReceiveSampleRecord> create(@RequestBody @Validated DtoReceiveSampleRecord receiveSampleRecord) {
        RestResponse<DtoReceiveSampleRecord> restResponse = new RestResponse<>();
        restResponse.setData(service.save(receiveSampleRecord, true));
        return restResponse;
    }

    /**
     * 修改送样单
     *
     * @param receiveSampleRecord 实体列表
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "修改送样单", notes = "修改送样单")
    @PutMapping
    public RestResponse<DtoReceiveSampleRecord> update(@RequestBody @Validated DtoReceiveSampleRecord receiveSampleRecord) {
        RestResponse<DtoReceiveSampleRecord> restResponse = new RestResponse<>();
        restResponse.setData(service.updateRecord(receiveSampleRecord, true));
        return restResponse;
    }

    /**
     * "根据id删除送样单
     *
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除送样单", notes = "根据id删除送样单")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(id));

        return restResp;
    }

    /**
     * "根据id批量删除送样单
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除送样单", notes = "根据id批量删除送样单")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }
    //

    /**
     * 根据id批量删除送样单--用于移动端
     *
     * @param dto 信息
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除送样单", notes = "根据id批量删除送样单")
    @PostMapping("/removeRecordPhone")
    public RestResponse<String> deleteRecordPhone(@RequestBody DtoScanMessage dto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(dto.getIds()));
        return restResp;
    }

    /**
     * 创建内部送样单
     *
     * @param temp 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "创建内部送样单", notes = "创建内部送样单")
    @PostMapping(path = "/innerSample")
    public RestResponse<DtoReceiveSampleRecord> innerSample(@RequestBody @Validated DtoInnerRecordTemp temp) {
        RestResponse<DtoReceiveSampleRecord> restResponse = new RestResponse<>();
        restResponse.setData(service.createInnerReceiveRecord(temp));
        return restResponse;
    }

    /**
     * 核查能否提交送样单
     *
     * @param dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "核查能否提交送样单", notes = "核查能否提交送样单")
    @PostMapping(path = "/checkCondition")
    public RestResponse<Boolean> checkCondition(@RequestBody DtoRecordSubmitTemp dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.canSubmitReceiveRecord(dto.getIds(), dto.getType());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 提交送样单
     *
     * @param dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "提交送样单", notes = "提交送样单")
    @PostMapping(path = "/submit")
    public RestResponse<List<String>> submit(@RequestBody DtoRecordSubmitTemp dto) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        try {
            restResponse.setData(service.submitReceiveRecord(dto.getIds(), dto.getType(), dto.getNextPersonId(), dto.getNextPerson(), dto.getOpinion(),
                    dto.getSubmitTime(), dto.getRecipientId()));
        } catch (Exception ex) {
            throw new BaseException(ex.getMessage());
        }
        return restResponse;
    }

    /**
     * 复核送样单
     *
     * @param dto    传输实体
     * @param isPass 是否通过
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复核送样单", notes = "复核送样单")
    @PostMapping(path = "/check/{isPass}")
    public RestResponse<List<String>> check(@RequestBody DtoRecordSubmitTemp dto, @PathVariable(name = "isPass") Boolean isPass) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.checkRecords(dto.getIds(), isPass, dto.getOpinion()));
        return restResponse;
    }

    /**
     * 审核送样单
     *
     * @param dto    传输实体
     * @param isPass 是否通过
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "审核送样单", notes = "审核送样单")
    @PostMapping(path = "/audit/{isPass}")
    public RestResponse<List<String>> audit(@RequestBody DtoRecordSubmitTemp dto, @PathVariable(name = "isPass") Boolean isPass) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.auditRecords(dto.getIds(), isPass, dto.getOpinion(), dto.getType(), dto.getIsReport()));
        return restResponse;
    }

    /**
     * 送样单剔除样品
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "送样单剔除样品", notes = "送样单剔除样品")
    @DeleteMapping(path = "/remove")
    public RestResponse<Boolean> remove(@RequestBody DtoSampleItemParams dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        Boolean isDelRecord = service.removeSample(dto.getReceiveId(), dto.getSampleIds());
        sampleService.check(dto.getSampleIds());
        //样品全部删除，则需要删除送样单
        if (isDelRecord) {
            service.logicDeleteById(dto.getReceiveId());
        }
        restResponse.setData(isDelRecord);
        return restResponse;
    }

    /**
     * 样品加入送样单
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "样品加入送样单", notes = "样品加入送样单")
    @PostMapping(path = "/join")
    public RestResponse<Boolean> join(@RequestBody DtoSampleItemParams dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.joinRecord(dto.getReceiveId(), dto.getSampleIds());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 确认扫码
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "确认扫码", notes = "确认扫码")
    @PostMapping(path = "/scanAffirm")
    public RestResponse<Boolean> scanAffirmByGroupIds(@RequestBody DtoScanMessage dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.scanAffirmByGroupIds(dto.getIds());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 扫码显示样品
     *
     * @param dto 传输实体
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "确认扫码", notes = "确认扫码")
    @PostMapping(path = "/scanCode")
    public RestResponse<DtoSampleGroup> scanCode(@RequestBody DtoScanMessage dto) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        restResponse.setData(service.scanCode(dto.getCodeStr()));
        return restResponse;
    }

    /**
     * 获取分组信息
     *
     * @param dto 传输实体
     * @return RestResponse<Map < String, Object>>
     */
    @ApiOperation(value = "确认扫码", notes = "确认扫码")
    @PostMapping(path = "/getGroupByReceiveId")
    public RestResponse<Map<String, Object>> getSampleGroupByReceiveId(@RequestBody DtoScanMessage dto) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.getSampleGroupByReceiveId(dto.getReceiveId()));
        return restResponse;
    }

    /**
     * 获取分组信息
     *
     * @param receiveId 送样单标识
     * @return RestResponse<Map < String, Object>>
     */
    @ApiOperation(value = "获取分组信息", notes = "获取分组信息")
    @GetMapping(path = "/{receiveId}/group")
    public RestResponse<List<DtoSampleTypeGroup>> getSampleGroupByReceiveId(@PathVariable String receiveId) {
        RestResponse<List<DtoSampleTypeGroup>> restResponse = new RestResponse<>();
        restResponse.setData(service.getAllSampleGroupByReceiveId(receiveId));
        return restResponse;
    }

    /**
     * 根据送样单id获取分析指标
     *
     * @param receiveId 送样单id
     * @return RestResponse<List < DtoTest>>
     */
    @ApiOperation(value = "根据送样单id获取分析指标", notes = "根据送样单id获取分析指标")
    @GetMapping("/analyseTests")
    public RestResponse<List<DtoTest>> findAnalyseTests(@RequestParam(name = "receiveId") String receiveId) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> testList = service.findAnalyseTest(receiveId);
        restResponse.setData(testList);
        restResponse.setRestStatus(testList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 更新样品分配中所有分析人员为默认人员
     *
     * @param updateAnalyst 接收数据
     * @return RestResponse<String>
     */
    @ApiOperation(value = "更新样品分配中所有分析人员为默认人员", notes = "更新样品分配中所有分析人员为默认人员")
    @PutMapping("/defaultPerson")
    public RestResponse<String> updateDefaultPerson(@RequestBody DtoUpdateAnalyst updateAnalyst) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateDefaultAnalyst(updateAnalyst);
        restResponse.setMsg("更新成功");
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 查询送样单下包含的所有检测类型
     *
     * @param id 主键id
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "按主键查询送样单下的所有检测类型", notes = "按主键查询送样单下的所有检测类型")
    @GetMapping(path = "/sampleType")
    public RestResponse<List<DtoSampleType>> findSampleType(String id) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        List<DtoSampleType> sampleTypeList = service.findSampleTypeForRecord(id);
        restResponse.setData(sampleTypeList);
        restResponse.setRestStatus(StringUtil.isNull(sampleTypeList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 判断送样单中是否存在样品编号为空的样品
     *
     * @param id 主键id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "判断送样单中是否存在样品编号为空的样品", notes = "判断送样单中是否存在样品编号为空的样品")
    @GetMapping(path = "/codeByReceiveId")
    public RestResponse<Boolean> checkSampleCodeByReceiveId(String id) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.checkSampleCodeByReceiveId(id));
        return restResponse;
    }

    /**
     * 新增送样单参数模板
     *
     * @param template 模板对象
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "新增送样单参数模板", notes = "新增送样单参数模板")
    @PostMapping(path = "/paramTemplate")
    public RestResponse<DtoReceiveSampleRecordParamTemplate> addParamTemplate(@RequestBody DtoReceiveSampleRecordParamTemplate template) {
        RestResponse<DtoReceiveSampleRecordParamTemplate> restResponse = new RestResponse<>();
        DtoReceiveSampleRecordParamTemplate newTemplate = service.saveParamTemplate(template);
        restResponse.setData(newTemplate);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 修改送样单参数模板
     *
     * @param template 模板对象
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "修改送样单参数模板", notes = "修改送样单参数模板")
    @PutMapping(path = "/paramTemplate")
    public RestResponse<DtoReceiveSampleRecordParamTemplate> updateParamTemplate(@RequestBody DtoReceiveSampleRecordParamTemplate template) {
        RestResponse<DtoReceiveSampleRecordParamTemplate> restResponse = new RestResponse<>();
        DtoReceiveSampleRecordParamTemplate newTemplate = service.updateParamTemplate(template);
        restResponse.setData(newTemplate);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 动态条件查询送样单参数模板
     *
     * @param criteria 模板查询对象
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "动态条件查询送样单参数模板", notes = "动态条件查询送样单参数模板")
    @GetMapping(path = "/paramTemplate")
    public RestResponse<List<DtoReceiveSampleRecordParamTemplate>> queryParamTemplate(ReceiveSampleRecordParamTemplateCriteria criteria) {
        RestResponse<List<DtoReceiveSampleRecordParamTemplate>> restResponse = new RestResponse<>();
        List<DtoReceiveSampleRecordParamTemplate> templateList = service.queryParamTemplate(criteria);
        restResponse.setData(templateList);
        restResponse.setCount(templateList.size());
        return restResponse;
    }

    /**
     * 获取送样单下对应检测类型的参数及现场测试项目
     *
     * @param criteria 模板查询对象
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "获取送样单下对应检测类型的参数及现场测试项目", notes = "获取送样单下对应检测类型的参数及现场测试项目")
    @GetMapping(path = "/templateParam")
    public RestResponse<List<DtoReceiveSampleRecordParamInfo>> getTemplateParam(ReceiveSampleRecordParamInfoCriteria criteria) {
        RestResponse<List<DtoReceiveSampleRecordParamInfo>> restResponse = new RestResponse<>();
        List<DtoReceiveSampleRecordParamInfo> templateList = service.getTemplateParam(criteria);
        restResponse.setData(templateList);
        restResponse.setCount(templateList.size());
        return restResponse;
    }


    /**
     * "根据id批量删除送样单参数模板
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除送样单参数模板", notes = "根据id批量删除送样单参数模板")
    @DeleteMapping(path = "/paramTemplate")
    public RestResponse<String> deleteParamTemplate(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.deleteParamTemplate(ids));
        return restResp;
    }

    /**
     * 刷新送样单检测类型
     *
     * @param ids 送样单ids
     */
    @ApiOperation(value = "刷新送样单检测类型", notes = "刷新送样单检测类型")
    @PostMapping(path = "/reloadRecordJson")
    public RestResponse<Boolean> reloadRecordJsonByIds(@RequestBody List<String> ids) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.reloadRecordJsonByIds(ids);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(Boolean.TRUE);
        return restResp;
    }

    /**
     * 按主键查询送样单参数模板
     *
     * @param id 主键id
     * @return RestResponse<DtoReceiveSampleRecordParamTemplate>
     */
    @ApiOperation(value = "按主键查询送样单参数模板", notes = "按主键查询送样单参数模板")
    @GetMapping(path = "/paramTemplate/{id}")
    public RestResponse<DtoReceiveSampleRecordParamTemplate> findParamTemplate(@PathVariable(name = "id") String id) {
        RestResponse<DtoReceiveSampleRecordParamTemplate> restResponse = new RestResponse<>();
        DtoReceiveSampleRecordParamTemplate template = service.findParamTemplate(id);
        restResponse.setData(template);
        restResponse.setRestStatus(StringUtil.isNull(template) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 参数模板excel导出
     *
     * @param templateId 模板id
     * @param response   响应数据
     */
    @ApiOperation(value = "参数模板excel导出", notes = "参数模板excel导出")
    @GetMapping("/paramTemplate/export")
    public void exportExcel(String templateId, String receiveId, HttpServletResponse response) {
        service.paramTemplateExport(templateId, receiveId, response);
    }

    /**
     * 现场数据excel导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/paramTemplate/import")
    public RestResponse<Boolean> importExcel(MultipartFile file, HttpServletResponse response) {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.paramTemplateImport(file, objectMap, response);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 默认样品有效期
     *
     * @return RestResponse<ConfigModel>
     */
    @ApiOperation(value = "默认样品有效期", notes = "默认样品有效期")
    @GetMapping("/defaultDateConfig")
    public RestResponse<ConfigModel> getDefaultDateConfig() {
        RestResponse<ConfigModel> restResponse = new RestResponse<>();
        ConfigModel model = service.getDefaultDateConfig();
        restResponse.setData(model);
        restResponse.setRestStatus(StringUtil.isNull(model) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 配置现场任务审核
     * true：一审 false：二审
     *
     * @return 返回判断
     */
    @ApiOperation(value = "配置现场任务审核", notes = "配置现场任务审核")
    @GetMapping("/recordAudit")
    public RestResponse<Boolean> isRecordAudit() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.isRecordAudit());
        return restResponse;
    }

    /**
     * 地图底图是否使用天地图
     * true 是，false 否
     *
     * @return 返回判断
     */
    @ApiOperation(value = "地图底图是否使用天地图", notes = "地图底图是否使用天地图")
    @GetMapping("/worldEarth")
    public RestResponse<Boolean> isWorldEarth() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.isWorldEarth());
        return restResponse;
    }

    /**
     * 查询送样单下流量校准可选的点位周期
     *
     * @param flowCalibrationId 流量校准标识
     * @param receiveId         送样单标识
     * @return 返回判断
     */
    @ApiOperation(value = "查询送样单下流量校准可选的点位周期", notes = "查询送样单下流量校准可选的点位周期")
    @GetMapping("/folderPeriodSelect")
    public RestResponse<List<Map<String, Object>>> queryInsFolderPeriodSelectList(String flowCalibrationId, String receiveId) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(service.queryInsFolderPeriodSelectList(flowCalibrationId, receiveId));
        return restResponse;
    }


    /**
     * 记录交接单日志
     *
     * @param dtoRecordSubmitTemp 接收字段（id，opinion）
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "记录交接单日志", notes = "记录交接单日志")
    @PostMapping(path = "/deliveryReceiptModify")
    public RestResponse<Boolean> deliveryReceiptModify(@RequestBody DtoRecordSubmitTemp dtoRecordSubmitTemp) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.deliveryReceiptModify(dtoRecordSubmitTemp.getId(), dtoRecordSubmitTemp.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 刷新现场数据样品
     *
     * @param receiveIds 送样单Ids
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "刷新现场数据样品", notes = "刷新现场数据样品")
    @PostMapping(path = "/refreshLocalSample")
    public RestResponse<Boolean> refreshLocalSample(@RequestBody List<String> receiveIds) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.refreshLocalSample(receiveIds);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 刷新分析项目参数
     *
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "刷新分析项目参数", notes = "刷新分析项目参数")
    @PostMapping(path = "/refreshParams")
    public RestResponse<Boolean> refreshParams() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.refreshParams();
        restResponse.setData(true);
        return restResponse;
    }

}