package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSample;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * Sample数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SampleRepository extends IBaseJpaRepository<DtoSample, String>, LimsRepository<DtoSample, String> {

    /**
     * 按样品查询相应的样品信息
     *
     * @param code 样品编号
     * @return 返回相应的样品信息
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.code = :code")
    List<DtoSample> findByCode(@Param("code") String code);

    @Query("select s from DtoSample s where s.isDeleted = 0 and s.code in :codeList")
    List<DtoSample> findByCodeIn(@Param("codeList") List<String> codeList);


    /**
     * 按项目的ids查询相应的样品信息
     *
     * @param ids 样品主键id
     * @return 返回相应的样品数据
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.id in :ids")
    List<DtoSample> findByIds(@Param("ids") Collection<String> ids);

    /**
     * 根据id集合查询样品，过滤假删的数据
     *
     * @param ids 样品id集合
     * @return 查询到的样品list
     */
    List<DtoSample> findByIdInAndIsDeletedFalse(Collection<String> ids);

    /**
     * 根据点位Id与频次记录Id获取样品列表
     *
     * @param foldIds      点位Id
     * @param frequencyIds 频次记录Id
     * @return 查询到的List
     */
    List<DtoSample> findBySampleFolderIdInAndSamplingFrequencyIdIn(Collection<String> foldIds, Collection<String> frequencyIds);

    /**
     * 按频次id查询相应的样品信息
     *
     * @param samplingFrequencyId 频次id
     * @return 返回相应的样品信息
     */
    List<DtoSample> findByIsDeletedFalseAndSamplingFrequencyId(String samplingFrequencyId);

    /**
     * 按送样单id和检测类型id查询样品
     *
     * @param receiveId    送样单id
     * @param sampleTypeId 检测类型id
     * @return 返回对应送样单对应检测类型的样品列表
     */
    List<DtoSample> findByIsDeletedFalseAndReceiveIdAndSampleTypeId(String receiveId, String sampleTypeId);

    /**
     * 按送样单id查询样品
     *
     * @param receiveId 送样单id
     * @return 返回对应送样单的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.receiveId = :receiveId ")
    List<DtoSample> findByReceiveId(@Param("receiveId") String receiveId);

    /**
     * 按送样单id集合查询样品
     *
     * @param receiveIds 送样单id集合
     * @return 返回对应送样单的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.receiveId in :receiveIds ")
    List<DtoSample> findByReceiveIdIn(@Param("receiveIds") Collection<String> receiveIds);

    /**
     * 判断是否存在相同的样品编号
     *
     * @param code 样品编号
     * @return 相同样品编号的数量
     */
    @Query("select count(s.id) from DtoSample s where s.isDeleted = 0 and s.code = :code ")
    Integer countByCode(@Param("code") String code);

    /**
     * 判断相同点位id的数量
     *
     * @param sampleFolderId 点位id
     * @param id             样品id
     * @return 相同点位id的数量
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.sampleFolderId = :sampleFolderId and s.id <> :id ")
    Integer countBySampleFolderIdAndIdNot(@Param("sampleFolderId") String sampleFolderId, @Param("id") String id);

    /**
     * 获取项目下样品的数量
     *
     * @param projectId 项目id
     * @return 项目下样品的数量
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.projectId = :projectId ")
    Integer countByProjectId(@Param("projectId") String projectId);

    /**
     * 判断是否存在相同的样品点位名称
     *
     * @param receiveId     送样单id
     * @param redFolderName 样品点位名称
     * @return 相同样品点位名称的数量
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.receiveId = :receiveId and s.redFolderName = :redFolderName ")
    Integer countByReceiveIdAndRedFolderName(@Param("receiveId") String receiveId, @Param("redFolderName") String redFolderName);

    /**
     * 判断是否存在相同的样品点位名称
     *
     * @param receiveId     送样单id
     * @param sampleTypeId  样品类型id
     * @param redFolderName 样品点位名称
     * @return 相同样品点位名称的数量
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.receiveId = :receiveId and s.sampleTypeId = :sampleTypeId and s.redFolderName = :redFolderName ")
    Integer countByReceiveIdAndSampleTypeIdAndRedFolderName(@Param("receiveId") String receiveId, @Param("sampleTypeId") String sampleTypeId, @Param("redFolderName") String redFolderName);

    /**
     * 判断是否存在相同的样品编号
     *
     * @param code 样品编号
     * @param id   样品id
     * @return 相同样品编号的数量
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.code = :code and s.id <> :id ")
    Integer countByCodeAndIdNot(@Param("code") String code, @Param("id") String id);

    /**
     * 判断是否存在相同的样品点位名称
     *
     * @param projectId     项目id
     * @param redFolderName 样品点位名称
     * @param id            样品id
     * @return 相同样品点位名称的数量
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.projectId = :projectId and s.redFolderName = :redFolderName and s.id <> :id ")
    Integer countByProjectIdAndRedFolderNameAndIdNot(@Param("projectId") String projectId, @Param("redFolderName") String redFolderName, @Param("id") String id);

    /**
     * 项目id和对应采样状态下的条数
     *
     * @param projectId      项目id
     * @param samplingStatus 样品编号
     * @return 返回项目id和样品编号下的条数
     */
    @Query("select count(s) from DtoSample s where s.isDeleted = 0 and s.projectId = :projectId and s.samplingStatus = :samplingStatus ")
    Integer countByProjectIdAndSamplingStatus(@Param("projectId") String projectId, @Param("samplingStatus") Integer samplingStatus);

    /**
     * 按原样样品id集合查询相应的关联样品信息
     *
     * @param ids 原样样品id集合
     * @return 返回相应的关联样品信息
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.associateSampleId in :ids ")
    List<DtoSample> findByAssociateSampleIdIn(@Param("ids") Collection<String> ids);

    /**
     * 按原样样品id查询相应的关联样品信息
     *
     * @param associateSampleId 原样样品id
     * @return 返回相应的关联样品信息
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.associateSampleId = :associateSampleId ")
    List<DtoSample> findByAssociateSampleId(@Param("associateSampleId") String associateSampleId);

    /**
     * 按质控id
     *
     * @param qcIds 质控id
     * @return 返回相应的关联样品信息
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.qcId in :qcIds ")
    List<DtoSample> findByQcIdIn(@Param("qcIds") List<String> qcIds);

    /**
     * 按频次id查询相应的样品
     *
     * @param samplingFrequencyId 频次id
     * @return 返回相应频次的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.samplingFrequencyId = :samplingFrequencyId ")
    List<DtoSample> findBySamplingFrequencyId(@Param("samplingFrequencyId") String samplingFrequencyId);

    /**
     * 按频次id集合查询相应的样品
     *
     * @param samplingFrequencyIds 频次id集合
     * @return 返回相应频次的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.samplingFrequencyId in :samplingFrequencyIds ")
    List<DtoSample> findBySamplingFrequencyIdIn(@Param("samplingFrequencyIds") Collection<String> samplingFrequencyIds);

    /**
     * 按点位id查询相应的样品
     *
     * @param sampleFolderId 点位id
     * @return 返回相应点位的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.sampleFolderId = :sampleFolderId ")
    List<DtoSample> findBySampleFolderId(@Param("sampleFolderId") String sampleFolderId);

    /**
     * 按点位id查询相应的样品
     *
     * @param sampleFolderId 点位id
     * @return 返回相应点位的样品
     */
    @Query("select s from DtoSample s where (s.isDeleted = 0 or (s.isDeleted = 1 and s.status = '样品作废')) and s.sampleFolderId = :sampleFolderId ")
    List<DtoSample> findBySampleFolderIdWithInvaild(@Param("sampleFolderId") String sampleFolderId);

    /**
     * 按点位id查询相应的样品
     *
     * @param sampleFolderId 点位id
     * @return 返回相应点位的样品
     */
    List<DtoSample> findBySampleFolderIdOrderById(String sampleFolderId);

    /**
     * 按点位id集合查询相应的样品
     *
     * @param sampleFolderIds 点位id集合
     * @return 返回相应点位的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.sampleFolderId in :sampleFolderIds ")
    List<DtoSample> findBySampleFolderIdIn(@Param("sampleFolderIds") Collection<String> sampleFolderIds);

    /**
     * 按点位id集合查询相应的样品
     *
     * @param folderIds 点位id集合
     * @return 返回相应点位的样品
     */
    List<DtoSample> findBySampleFolderIdInOrderById(List<String> folderIds);

    /**
     * 按点位id集合查询相应的样品
     *
     * @param folderIds 点位id集合
     * @return 返回相应点位的样品
     */
    List<DtoSample> findByIsDeletedFalseAndSampleFolderIdInOrderById(List<String> folderIds);

    /**
     * 按项目id查询样品
     *
     * @param projectId 项目id
     * @return 返回对应项目的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.projectId = :projectId ")
    List<DtoSample> findByProjectId(@Param("projectId") String projectId);

    /**
     * 按项目id查询样品
     *
     * @param projectId 项目id
     * @return 返回对应项目的样品
     */
    @Query("select s from DtoSample s where (s.isDeleted = 0 or (s.isDeleted = 1 and s.status = '样品作废')) and s.projectId = :projectId ")
    List<DtoSample> findByProjectIdWithInvaild(@Param("projectId") String projectId);

    /**
     * 按项目id查询样品
     *
     * @param projectId 项目id
     * @return 返回对应项目的样品
     */
    List<DtoSample> findByProjectIdOrderById(String projectId);

    /**
     * 按项目id查询复制样品的信息（复制样品不能返回所有字段，会影响性能，只能返回部分字段进行处理）
     *
     * @param projectId 项目id
     * @return 返回对应项目的样品
     */
    @Query("select new  com.sinoyd.lims.pro.dto.DtoSample(s.id,s.sampleFolderId," +
            "s.sampleTypeId,s.samplingFrequencyId,s.redAnalyzeItems,s.code," +
            "s.redFolderName,s.cycleOrder,s.timesOrder,s.sampleOrder,s.sampleCategory," +
            "s.inspectedEnt,s.inspectedEntId,s.lat,s.lon) from DtoSample s where " +
            "(s.isDeleted = 0 or s.status = '样品作废' ) and s.projectId = :projectId ")
    List<DtoSample> findCopySampleByProjectId(@Param("projectId") String projectId);

    /**
     * 按项目id查询样品(包括假删)
     *
     * @param projectId 项目id
     * @return 返回对应项目的样品
     */
    @Query("select s from DtoSample s where s.projectId = :projectId ")
    List<DtoSample> findByProjectIdContainsIsDeleted(@Param("projectId") String projectId);

    /**
     * 按送样单id查询样品(包括假删)
     *
     * @param receiveId 送样单id
     * @return 返回对应项目的样品
     */
    @Query("select s from DtoSample s where s.receiveId = :receiveId ")
    List<DtoSample> findByReceiveIdContainsIsDeleted(@Param("receiveId") String receiveId);

    /**
     * 按项目id集合查询相应的样品
     *
     * @param projectIds 项目id集合
     * @return 返回相应点位的样品
     */
    @Query("select s from DtoSample s where s.isDeleted = 0 and s.projectId in :projectIds ")
    List<DtoSample> findByProjectIdIn(@Param("projectIds") Collection<String> projectIds);

    /**
     * 批量办结样品状态
     *
     * @param ids                样品的ids
     * @param status             样品状态
     * @param innerReceiveStatus 领样状态
     * @param ananlyzeStatus     分析状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.status = :status,s.samplingStatus = :samplingStatus," +
            "s.innerReceiveStatus = :innerReceiveStatus,s.ananlyzeStatus = :ananlyzeStatus," +
            "s.modifyDate=:modifyDate,s.modifier=:modifier where s.id in :ids")
    Integer finishSample(@Param("ids") List<String> ids,
                         @Param("status") String status,
                         @Param("samplingStatus") Integer samplingStatus,
                         @Param("innerReceiveStatus") Integer innerReceiveStatus,
                         @Param("ananlyzeStatus") Integer ananlyzeStatus,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate);

    /**
     * 批量清除样品
     *
     * @param ids 样品的ids
     * @return 返回相应的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.receiveId = '00000000-0000-0000-0000-000000000000'," +
            "s.isDeleted = 1,s.modifyDate=:modifyDate,s.modifier=:modifier where s.id in :ids")
    Integer deleteSample(@Param("ids") List<String> ids,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate);

    /**
     * 批量删除样品信息
     *
     * @param ids 样品ids
     * @return 返回删除信息
     */
    @Transactional
    @Modifying
    @Query("delete from DtoSample as  s where s.id in :ids")
    Integer deleteByIds(@Param("ids") List<String> ids);

    /**
     * 修改样品的状态
     *
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.status = :status,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateSampleStatus(@Param("ids") List<String> ids, @Param("status") String status,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);

    /**
     * 修改样品的状态和采样状态
     *
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.status = :status, s.samplingStatus = :samplingStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateSampleStatus(@Param("ids") List<String> ids, @Param("samplingStatus") Integer samplingStatus, @Param("status") String status,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);

    /**
     * 修改送样单样品的采样状态
     *
     * @param receiveId      送样单id
     * @param samplingStatus 采样状态
     * @param modifier       修改人
     * @param modifyDate     修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.samplingStatus = :samplingStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.receiveId = :receiveId")
    Integer updateSamplingStatus(@Param("receiveId") String receiveId,
                                 @Param("samplingStatus") Integer samplingStatus,
                                 @Param("modifier") String modifier,
                                 @Param("modifyDate") Date modifyDate);

    /**
     * 修改样品的状态和分析状态
     *
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.status = :status,s.ananlyzeStatus=:analyzeStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateSampleStatus(@Param("ids") List<String> ids,
                               @Param("status") String status,
                               @Param("analyzeStatus") Integer analyzeStatus,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);

    /**
     * 修改样品状态（样品交接后进行状态的变更）
     *
     * @param ids                样品id
     * @param innerReceiveStatus 领取状态
     * @param ananlyzeStatus     分析状态
     * @param samplingStatus     采样状态
     * @param status             状态
     * @param modifier           修改人
     * @param modifyDate         修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.innerReceiveStatus = :innerReceiveStatus,s.ananlyzeStatus = :ananlyzeStatus,s.samplingStatus = :samplingStatus,s.status = :status,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateSampleStatus(@Param("ids") List<String> ids,
                               @Param("innerReceiveStatus") Integer innerReceiveStatus,
                               @Param("ananlyzeStatus") Integer ananlyzeStatus,
                               @Param("samplingStatus") Integer samplingStatus,
                               @Param("status") String status,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);

    /**
     * 修改样品状态（样品交接后进行状态的变更）
     *
     * @param ids               样品ids
     * @param lastNewSubmitTime 最新一次检测单数据提交时间
     * @param modifier          修改人
     * @param modifyDate        修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSample s set s.lastNewSubmitTime = :lastNewSubmitTime,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateLastNewSubmitTime(@Param("ids") List<String> ids,
                                    @Param("lastNewSubmitTime") Date lastNewSubmitTime,
                                    @Param("modifier") String modifier,
                                    @Param("modifyDate") Date modifyDate);

    /**
     * 按送样单进行分组得到相应的样品数
     *
     * @param receiveIds 送样单ids
     * @return 进行分钟得到相应的样品数
     */
    @Query("select count(s.id),receiveId,sampleTypeId  from DtoSample s where s.isDeleted=0 and s.receiveId in :receiveIds group by s.receiveId,s.sampleTypeId")
    List<Object[]> countSampleNumGroupByReceiveIdAndSampleTypeId(@Param("receiveIds") List<String> receiveIds);

    /**
     * 批量修改样品更改状态
     *
     * @param ids 样品的ids
     * @return 返回相应的条数
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Modifying
    @Query("update DtoSample s set s.dataChangeStatus = :dataChangeStatus,s.modifyDate=:modifyDate,s.modifier=:modifier where s.id in :ids")
    Integer updateDataChangeStatus(@Param("ids") List<String> ids,
                                   @Param("dataChangeStatus") Integer dataChangeStatus,
                                   @Param("modifier") String modifier,
                                   @Param("modifyDate") Date modifyDate);

//    /**
//     * 获取已检毕样品的采样日期和最新数据提交时间
//     *
//     * @param samplingTimeBegin 采样时间
//     * @return 进行分组得到相应的样品数
//     */
//    @Query("select samplingTimeBegin,lastNewSubmitTime  from TB_PRO_Sample s " +
//            "inner join TB_PRO_ReceiveSubSampleRecord2Sample r2s on s.id = r2s.sampleId " +
//            "inner join TB_PRO_ReceiveSubSampleRecord rs on r2s.receiveSubSampleRecordId = rs.id " +
//            "where s.orgId = :orgId and rs.orgId = :orgId and " +
//            "s.samplingTimeBegin >= :samplingTimeBegin and s.isDeleted = 0 and s.sampleCategory = 0 and s.status = '样品检毕' and bitand(rs.subStatus,1)>0")
//    List<Object[]> findSamplingTimeBeginAndLastNewSubmitTime(@Param("samplingTimeBegin") Date samplingTimeBegin, @Param("orgId") String orgId);

    /**
     * 按样品状态获取对应状态的原样个数
     *
     * @param inceptTime 采样时间
     * @return 进行分组得到相应的样品数
     */
    @Query(value = "select s.status,count(s.id)  from DtoSample s where s.inceptTime >= :inceptTime and s.isDeleted=0 and s.sampleCategory = 0 group by s.status")
    List<Object[]> countSampleNumGroupByStatus(@Param("inceptTime") Date inceptTime);
}