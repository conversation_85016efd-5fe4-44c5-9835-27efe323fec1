package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.CostRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.*;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SamplingAchievement2Person操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class SamplingAchievement2PersonServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingAchievement2Person, String, SamplingAchievement2PersonRepository> implements SamplingAchievement2PersonService {

    private DepartmentService departmentService;

    private PersonService personService;

    private SamplingAchievementDetailsService samplingAchievementDetailsService;

    private SamplingAchievementDetailsRepository samplingAchievementDetailsRepository;

    private SampleGroupRepository sampleGroupRepository;

    private CommonRepository commonRepository;

    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    private AnalyseDataRepository analyseDataRepository;

    private TestRepository testRepository;

    private CostRepository costRepository;

    private ProjectRepository projectRepository;

    private OrderQuotationRepository orderQuotationRepository;

    private CodeService codeService;

    private OrderFormRepository orderFormRepository;

    private QuotationDetailRepository quotationDetailRepository;

    private SampleFolderRepository sampleFolderRepository;


    @Override
    public void findByPage(PageBean<DtoSamplingAchievement2Person> page, BaseCriteria criteria) {
        SamplingAchievement2PersonCriteria achievement2PersonCriteria = (SamplingAchievement2PersonCriteria) criteria;
        page.setEntityName("DtoSamplingAchievement2Person a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData(), achievement2PersonCriteria);
        page.getData().sort(Comparator.comparing(DtoSamplingAchievement2Person::getPersonName, Comparator.nullsLast(Comparator.naturalOrder())));
    }

    @Override
    @Transactional
    public void selectPerson(List<String> personIds) {
        List<DtoSamplingAchievement2Person> saveList = new ArrayList<>();
        personIds.forEach(p -> {
            DtoSamplingAchievement2Person data = new DtoSamplingAchievement2Person();
            data.setPersonId(p);
            saveList.add(data);
        });
        if (StringUtil.isNotEmpty(saveList)) {
            super.save(saveList);
        }
    }

    @Override
    public void verifySelectPerson(List<String> personIds) {
        List<DtoSamplingAchievement2Person> achievements = repository.findByPersonIdIn(personIds);
        List<DtoPerson> personList = personService.findAll(personIds);
        List<String> personNames = new ArrayList<>();
        personList.forEach(p -> {
            Optional<DtoSamplingAchievement2Person> achievement = achievements.stream().filter(a -> a.getPersonId().equals(p.getId())).findFirst();
            achievement.ifPresent(a -> personNames.add(p.getCName()));
        });
        if (StringUtil.isNotEmpty(personNames)) {
            throw new BaseException(String.join(",", personNames) + "的绩效已存在");
        }
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        samplingAchievementDetailsRepository.deleteByAchievementIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public void updateData(Integer year, List<String> ids) {
        List<DtoSamplingAchievement2Person> achievements = repository.findAll(ids);
        List<String> personIds = achievements.stream().map(DtoSamplingAchievement2Person::getPersonId).collect(Collectors.toList());
        Date startTime = DateUtil.stringToDate(CalendarUtil.getCurrentYearBegin(year), DateUtil.YEAR);
        Date endTime = DateUtil.stringToDate(CalendarUtil.getCurrentYearEnd(year), DateUtil.YEAR);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        endTime = calendar.getTime();
        StringBuilder condition = new StringBuilder();
        condition.append("select s.projectId,s.id,s.code,s.sampleFolderId,s.redFolderName,s.sampleTypeId,r.id,r.recordCode,r.samplingTime ");
        condition.append("from DtoSample s, DtoReceiveSampleRecord r where s.receiveId = r.id and s.isDeleted = 0 and r.isDeleted = 0 ");
        condition.append("and r.receiveStatus >= 2 and r.samplingTime >= :startTime and r.samplingTime < :endTime and s.sampleCategory = 0 ");
        condition.append("and exists(select 1 from DtoSamplingPersonConfig spc where r.id = spc.objectId and spc.samplingPersonId in :personIds) ");
        Map<String, Object> values = new HashMap<>();
        values.put("startTime", startTime);
        values.put("endTime", endTime);
        values.put("personIds", personIds);
        List<Object[]> datas = commonRepository.find(condition.toString(), values);
        List<DtoSamplingAchievementDetails> details = new ArrayList<>();
        for (Object[] data : datas) {
            DtoSamplingAchievementDetails samplingAchievementDetails = new DtoSamplingAchievementDetails();
            samplingAchievementDetails.setProjectId(data[0].toString());
            samplingAchievementDetails.setSampleId(data[1].toString());
            samplingAchievementDetails.setSampleCode(data[2].toString());
            samplingAchievementDetails.setSampleFolderId(data[3].toString());
            samplingAchievementDetails.setSampleFolderName(data[4].toString());
            samplingAchievementDetails.setSampleTypeId(data[5].toString());
            samplingAchievementDetails.setReceiveId(data[6].toString());
            samplingAchievementDetails.setRecordCode(data[7].toString());
            samplingAchievementDetails.setSamplingTime((Date) data[8]);
            details.add(samplingAchievementDetails);
        }
        List<String> projectIds = details.stream().map(DtoSamplingAchievementDetails::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> projects = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
        // 筛选关联订单的项目
        projects = projects.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getOrderId())).collect(Collectors.toList());
        projectIds = projects.stream().map(DtoProject::getId).collect(Collectors.toList());
        // 过滤掉未绑定订单的项目样品数据
        List<String> finalProjectIds = projectIds;
        details.removeIf(p -> !finalProjectIds.contains(p.getProjectId()));
        List<String> orderIds = projects.stream().map(DtoProject::getOrderId).distinct().collect(Collectors.toList());
        List<DtoOrderQuotation> quotations = StringUtil.isNotEmpty(orderIds) ? orderQuotationRepository.findByOrderIdIn(orderIds) : new ArrayList<>();
        List<DtoOrderForm> orderFormList = StringUtil.isNotEmpty(orderIds) ? orderFormRepository.findAll(orderIds) : new ArrayList<>();
        Map<String, DtoOrderForm> orderFormMap = orderFormList.stream().collect(Collectors.toMap(DtoOrderForm::getId, p -> p));
        List<DtoQuotationDetail> quotationDetails = StringUtil.isNotEmpty(orderIds) ?
                quotationDetailRepository.findByOrderIdIn(orderIds) : new ArrayList<>();
        Map<String, List<DtoQuotationDetail>> quotationDetailMap = quotationDetails.stream().collect(Collectors.groupingBy(DtoQuotationDetail::getOrderId));
        // 点位
        List<String> sampleFolderIds = details.stream().map(DtoSamplingAchievementDetails::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> sampleFolderList = StringUtil.isNotEmpty(sampleFolderIds) ? sampleFolderRepository.findAll(sampleFolderIds) : new ArrayList<>();
        Map<String, DtoSampleFolder> sampleFolderMap = sampleFolderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, p -> p));
        Map<String, Object> proId2ZSXS = new HashMap<>();
        for (DtoProject project : projects) {
            DtoOrderQuotation quotation = quotations.stream().filter(q -> q.getOrderId().equals(project.getOrderId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(quotation)) {
                BigDecimal num = new BigDecimal(0);
                if (quotation.getFinalQuotation().compareTo(BigDecimal.ZERO) > 0 && (quotation.getTestPrice().add(quotation.getOtherPrice()).compareTo(BigDecimal.ZERO) > 0)) {
                    num = quotation.getFinalQuotation().divide(quotation.getTestPrice().add(quotation.getOtherPrice()).add((quotation.getTestPrice().add(quotation.getOtherPrice())).multiply(quotation.getTaxRate()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);
                } else if (quotation.getFinalQuotation().compareTo(BigDecimal.ZERO) == 0
                        && quotation.getTotalPrice().compareTo(BigDecimal.ZERO) > 0
                        && (quotation.getTestPrice().add(quotation.getOtherPrice()).compareTo(BigDecimal.ZERO) > 0)) {
                    //BUG2024052901649 【重要】【2024-5-31】【马川江】【绩效统计】采样绩效统计、分析绩效统计，计算产值的时候，如果“最终报价”为0的，需要带入“参考总价”进行计算。
                    num = quotation.getTotalPrice().divide(quotation.getTestPrice().add(quotation.getOtherPrice()).add((quotation.getTestPrice().add(quotation.getOtherPrice())).multiply(quotation.getTaxRate()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);
                }
                proId2ZSXS.put(project.getId(), num);
            }
        }
        List<String> receiveIds = details.stream().map(DtoSamplingAchievementDetails::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoSamplingPersonConfig> samplingPersonConfigs = StringUtil.isNotEmpty(receiveIds) ? samplingPersonConfigRepository.findByObjectIdIn(receiveIds) : new ArrayList<>();
        List<DtoProject> finalProjects = projects;
        details.forEach(d -> {
            List<String> samplingPersonConfigs2Receive = samplingPersonConfigs.stream().filter(s -> s.getObjectId().equals(d.getReceiveId())).map(DtoSamplingPersonConfig::getSamplingPersonId).collect(Collectors.toList());
            d.setSamplingPersonIds(String.join(",", samplingPersonConfigs2Receive));
            finalProjects.stream().filter(p -> p.getId().equals(d.getProjectId())).findFirst().ifPresent(pro -> {
                d.setOrderId(pro.getOrderId());
            });
        });
        List<String> sampleIds = details.stream().map(DtoSamplingAchievementDetails::getSampleId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds).stream().filter(a -> !a.getIsOutsourcing()).collect(Collectors.toList()) : new ArrayList<>();
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        List<String> parentTestIds = tests.stream().filter(t -> !UUIDHelper.GUID_EMPTY.equals(t.getParentId())).map(DtoTest::getParentId).collect(Collectors.toList());
        List<DtoTest> parentTests = StringUtil.isNotEmpty(parentTestIds) ? testRepository.findAll(parentTestIds) : new ArrayList<>();
        testIds.addAll(parentTestIds);
        List<DtoCost> costs = StringUtil.isNotEmpty(testIds) ? costRepository.findByTestIdIn(testIds) : new ArrayList<>();
        samplingAchievementDetailsRepository.deleteByAchievementIdInAndSamplingTimeBetween(ids, startTime, endTime);
        List<DtoSamplingAchievementDetails> saveList = new ArrayList<>();
        achievements.forEach(a -> {
            List<DtoSamplingAchievementDetails> details2Achievement = details.stream().filter(d -> d.getSamplingPersonIds().contains(a.getPersonId())).collect(Collectors.toList());
            for (DtoSamplingAchievementDetails data : details2Achievement) {
                DtoOrderForm orderForm = orderFormMap.get(data.getOrderId());
                if (StringUtil.isNotNull(orderForm)) {
                    DtoSampleFolder sampleFolder = sampleFolderMap.getOrDefault(data.getSampleFolderId(), new DtoSampleFolder());
                    List<DtoQuotationDetail> dtoQuotationDetails = quotationDetailMap.getOrDefault(orderForm.getId(),new ArrayList<>());

                    DtoSamplingAchievementDetails save = new DtoSamplingAchievementDetails();
                    BeanUtils.copyProperties(data, save, "id");
                    save.setAchievementId(a.getId());
                    List<DtoAnalyseData> analyseData2Details = analyseDataList.stream().filter(ad -> ad.getSampleId().equals(save.getSampleId())).collect(Collectors.toList());
                    List<String> testIds2Details = analyseData2Details.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
                    List<DtoTest> tests2Details = tests.stream().filter(t -> testIds2Details.contains(t.getId())).collect(Collectors.toList());
                    Map<String, List<DtoTest>> parentId2Tests = tests2Details.stream().collect(Collectors.groupingBy(DtoTest::getParentId));


                    BigDecimal totalAmount = BigDecimal.ZERO;
                    List<DtoQuotationDetail> detailList;
                    for (Map.Entry<String, List<DtoTest>> entry : parentId2Tests.entrySet()) {
                        String parentId = entry.getKey();
                        List<DtoTest> childTests = entry.getValue();
                        // 根据是否为总称测试项目，分别获取检测费用明细
                        DtoTest parentTest = parentTests.stream().filter(t -> t.getId().equals(parentId)).findFirst().orElse(null);
                        if (StringUtil.isNotNull(parentTest) && parentTest.getIsTotalTest() && childTests.size() > 0) {

                            detailList = dtoQuotationDetails.stream().filter(p -> parentTest.getId().equals(p.getTestId()) &&
                                    data.getSampleTypeId().equals(p.getSampleTypeId()) &&
                                    p.getFolderName().contains(sampleFolder.getWatchSpot())).collect(Collectors.toList());

                        } else {
                            List<String> childTestIds = childTests.stream().map(DtoTest::getId).collect(Collectors.toList());
                            detailList = dtoQuotationDetails.stream().filter(p -> childTestIds.contains(p.getTestId()) &&
                                    data.getSampleTypeId().equals(p.getSampleTypeId()) &&
                                    StringUtil.isNotNull(p.getFolderName()) &&
                                    p.getFolderName().contains(sampleFolder.getWatchSpot())).collect(Collectors.toList());

                        }
                        // 根据测试项目
                        Map<String, List<DtoQuotationDetail>> detailByTestMap = detailList.stream().collect(Collectors.groupingBy(DtoQuotationDetail::getTestId));
                        for (Map.Entry<String, List<DtoQuotationDetail>> listEntry : detailByTestMap.entrySet()) {
                            List<DtoQuotationDetail> value = listEntry.getValue();
                            BigDecimal totalSamplingPrice = value.stream().map(DtoQuotationDetail::getSamplingPrice)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal averageSamplingPrice = totalSamplingPrice.divide(BigDecimal.valueOf(value.size() > 0 ? value.size() : 1), 2, BigDecimal.ROUND_HALF_UP);
                            totalAmount = totalAmount.add(averageSamplingPrice);
                        }
                    }
                    if (proId2ZSXS.containsKey(save.getProjectId())) {
                        totalAmount = totalAmount.multiply((BigDecimal) proId2ZSXS.get(save.getProjectId()));
                    }
                    totalAmount = totalAmount.divide(new BigDecimal(save.getSamplingPersonIds().split(",").length), 0, BigDecimal.ROUND_HALF_UP);
                    save.setTotalAmount(totalAmount);
                    saveList.add(save);
                }
            }
        });

        if (StringUtil.isNotEmpty(saveList)) {
            samplingAchievementDetailsRepository.save(saveList);
        }
    }

    @Override
    public Map<String, BigDecimal> chartForPerMonth() {
        DtoCode code = codeService.findByCode("SamplingPerformance");
        List<DtoSamplingAchievementDetails> details = samplingAchievementDetailsRepository.findAll();
        final Map<String, BigDecimal> map = new HashMap<>();
        if (code != null && "1".equals(code.getDictValue())) {
            map.putAll(details.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getSamplingTime(), "yyyy-MM"),
                    Collectors.mapping(DtoSamplingAchievementDetails::getTotalAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)))));
        } else {
            Map<String, List<DtoSamplingAchievementDetails>> samplingDate2Details = details.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getSamplingTime(), "yyyy-MM"), Collectors.toList()));
            samplingDate2Details.forEach((k, v) -> {
                List<String> sampleFolderIds = v.stream().map(DtoSamplingAchievementDetails::getSampleFolderId).distinct().collect(Collectors.toList());
                BigDecimal num = new BigDecimal(String.valueOf(sampleFolderIds.size()));
                map.put(k, num);
            });
        }
        return map;
    }

    /**
     * 填充冗余数据
     *
     * @param achievements 绩效
     * @param criteria     查询条件
     */
    private void fillingTransientFields(List<DtoSamplingAchievement2Person> achievements, SamplingAchievement2PersonCriteria criteria) {
        PageBean<DtoSamplingAchievementDetails> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        SamplingAchievementDetailsCriteria achievementDetailsCriteria = new SamplingAchievementDetailsCriteria();
        achievementDetailsCriteria.setStartTime(criteria.getStartTime());
        achievementDetailsCriteria.setEndTime(criteria.getEndTime());
        samplingAchievementDetailsService.findByPage(pb, achievementDetailsCriteria);
        List<DtoSamplingAchievementDetails> details = pb.getData();
        List<String> receiveIds = details.stream().map(DtoSamplingAchievementDetails::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoSampleGroup> sampleGroups = StringUtil.isNotEmpty(receiveIds) ? sampleGroupRepository.findByReceiveIdIn(receiveIds) : new ArrayList<>();
        List<String> personIds = achievements.stream().map(DtoSamplingAchievement2Person::getPersonId).collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        List<DtoDepartment> departments = departmentService.findAll();
        achievements.forEach(a -> {
            Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(a.getPersonId())).findFirst();
            person.ifPresent(p -> {
                a.setPersonName(p.getCName());
                Optional<DtoDepartment> dtoDepartment = departments.stream().filter(d -> d.getId().equals(p.getDeptId())).findFirst();
                dtoDepartment.ifPresent(d -> a.setDeptName(d.getDeptName()));
            });
            List<DtoSamplingAchievementDetails> details2Achievement = details.stream().filter(d -> d.getAchievementId().equals(a.getId())).collect(Collectors.toList());
            List<String> sampleFolderIds2Achievement = details2Achievement.stream().map(DtoSamplingAchievementDetails::getSampleFolderId).distinct().collect(Collectors.toList());
            a.setFolderNum(sampleFolderIds2Achievement.size());
            List<String> sampleIds2Achievement = details2Achievement.stream().map(DtoSamplingAchievementDetails::getSampleId).collect(Collectors.toList());
            List<DtoSampleGroup> sampleGroups2Achievement = sampleGroups.stream().filter(s -> sampleIds2Achievement.contains(s.getSampleId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleGroups2Achievement)) {
                a.setSampleNum(sampleGroups2Achievement.size());
            } else {
                a.setSampleNum(details2Achievement.size());
            }
            List<String> receiveIds2Achievement = details2Achievement.stream().map(DtoSamplingAchievementDetails::getReceiveId).distinct().collect(Collectors.toList());
            a.setRecordNum(receiveIds2Achievement.size());
            a.setTotal(details2Achievement.stream().collect(Collectors.mapping(DtoSamplingAchievementDetails::getTotalAmount,
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        });
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setSamplingAchievementDetailsService(SamplingAchievementDetailsService samplingAchievementDetailsService) {
        this.samplingAchievementDetailsService = samplingAchievementDetailsService;
    }

    @Autowired
    public void setSampleGroupRepository(SampleGroupRepository sampleGroupRepository) {
        this.sampleGroupRepository = sampleGroupRepository;
    }

    @Autowired
    public void setSamplingAchievementDetailsRepository(SamplingAchievementDetailsRepository samplingAchievementDetailsRepository) {
        this.samplingAchievementDetailsRepository = samplingAchievementDetailsRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setSamplingPersonConfigRepository(SamplingPersonConfigRepository samplingPersonConfigRepository) {
        this.samplingPersonConfigRepository = samplingPersonConfigRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setCostRepository(CostRepository costRepository) {
        this.costRepository = costRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setOrderQuotationRepository(OrderQuotationRepository orderQuotationRepository) {
        this.orderQuotationRepository = orderQuotationRepository;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setOrderFormRepository(OrderFormRepository orderFormRepository) {
        this.orderFormRepository = orderFormRepository;
    }

    @Autowired
    @Lazy
    public void setQuotationDetailRepository(QuotationDetailRepository quotationDetailRepository) {
        this.quotationDetailRepository = quotationDetailRepository;
    }

    @Autowired
    @Lazy
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }
}
