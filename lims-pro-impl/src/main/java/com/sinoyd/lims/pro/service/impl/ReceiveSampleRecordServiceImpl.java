package com.sinoyd.lims.pro.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.customer.DtoImportConsumable;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.CommonBatchRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoMarkersData;
import com.sinoyd.lims.lim.dto.customer.DtoUpdateAnalyst;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.ReceiveSampleRecordCriteria;
import com.sinoyd.lims.pro.criteria.ReceiveSampleRecordParamInfoCriteria;
import com.sinoyd.lims.pro.criteria.ReceiveSampleRecordParamTemplateCriteria;
import com.sinoyd.lims.pro.criteria.ReceiveSampleRecordQueryCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.entity.SampleGroup;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.strategy.strategy.dataValidator.AbsDataValidator;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.pro.util.VerifyUtils;
import com.sinoyd.lims.pro.verify.FieldDataVerifyHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 送样单操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/19
 * @since V100R001
 */
@Service
@Slf4j
public class ReceiveSampleRecordServiceImpl extends BaseJpaServiceImpl<DtoReceiveSampleRecord, String, ReceiveSampleRecordRepository> implements ReceiveSampleRecordService {

    //#region 注入
    @Autowired
    @Qualifier("receiveSampleRecord")
    @Lazy
    private SerialNumberService serialNumberService;

    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    private CommonBatchRepository commonBatchRepository;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    private SamplingCarConfigRepository samplingCarConfigRepository;

    @Autowired
    protected SamplingPersonConfigRepository samplingPersonConfigRepository;

    @Autowired
    @Lazy
    private SamplingCarConfigService samplingCarConfigService;

    @Autowired
    @Lazy
    private SamplingPersonConfigService samplingPersonConfigService;

    @Autowired
    @Lazy
    private StatusForRecordService statusForRecordService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    @Autowired
    @Lazy
    protected ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private AnalyseDataService analyseDataService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private InstrumentUseRecordService instrumentUseRecordService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private ParamsDataService paramsDataService;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private StatusForProjectService statusForProjectService;

    @Autowired
    @Lazy
    private StatusForQCProjectService statusForQCProjectService;

    @Autowired
    @Lazy
    private StatusForRecordRepository statusForRecordRepository;

    @Autowired
    @Lazy
    private CarManageService carManageService;

    @Autowired
    private Document2LogRepository document2LogRepository;

    @Autowired
    @Lazy
    private PerformanceStatisticForLocalDataService performanceStatisticForLocalDataService;

    @Autowired
    @Lazy
    private PerformanceStatisticForSampleDataService performanceStatisticForSampleDataService;

    @Autowired
    @Lazy
    private SubmitRecordService submitRecordService;

    @Autowired
    private SubmitRecordRepository submitRecordRepository;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    @Lazy
    private SchemeService schemeService;

    @Autowired
    @Lazy
    private SampleGroupService sampleGroupService;

    @Autowired
    private SampleGroupRepository sampleGroupRepository;

    @Autowired
    @Lazy
    protected SignatureService signatureService;

    @Autowired
    protected DocumentRepository documentRepository;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    @Lazy
    private CodeService codeService;

    @Autowired
    private Person2TestRepository person2TestRepository;

    private ReportDetailService reportDetailService;

    private SampleJudgeDataService sampleJudgeDataService;

    @Autowired
    private EnvironmentalRecordRepository environmentalRecordRepository;

    @Autowired
    private EnvironmentalRecord2SampleRepository environmentalRecord2SampleRepository;

    @Autowired
    private EnvironmentalRecord2TestRepository environmentalRecord2TestRepository;
    //#endregion

    @Autowired
    private ReceiveSampleRecordParamInfoRepository receiveSampleRecordParamInfoRepository;
    @Autowired
    private ReceiveSampleRecordParamTemplateRepository receiveSampleRecordParamTemplateRepository;
    @Autowired
    private ParamsConfigRepository paramsConfigRepository;
    @Autowired
    private ParamsDataRepository paramsDataRepository;
    @Autowired
    @Lazy
    private List<AbsDataValidator> dataValidatorList;

    @Autowired
    private SampleTypeGroupRepository sampleTypeGroupRepository;

    private IConfigService configService;
    private EnvironmentEnterpriseService environmentEnterpriseService;

    @Autowired
    private SamplingFrequencyRepository samplingFrequencyRepository;

    @Autowired
    private FlowCalibration2FrequencyRepository flowCalibration2FrequencyRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    private MarkersDataService markersDataService;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private SampleTypeGroup2TestService sampleTypeGroup2TestService;

    @Autowired
    private TestOperateLogRepository testOperateLogRepository;

    @Autowired
    private ParamsRepository paramsRepository;

    @Override
    public void findByPage(PageBean<DtoReceiveSampleRecord> pb, BaseCriteria receiveSampleRecordCriteria) {
        ReceiveSampleRecordCriteria criteria = (ReceiveSampleRecordCriteria) receiveSampleRecordCriteria;
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (criteria.getModule().equals(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue())) {
            if (!StringUtils.isNotNullAndEmpty(userId) || !authorizeService.haveActionPermission(userId, ProCodeHelper.LOCAL_AUDIT_AUTH)) {
                return;
            }
        }

        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoReceiveSampleRecord r, DtoProject p, DtoStatusForRecord s");
        pb.setSelect("select r, p.projectCode, p.projectName, p.projectTypeId, p.grade, p.customerId, p.customerName, s.lastNewOpinion" +
                ",json_value(r.json,'$.sampleNum') as sampleNum,json_value(r.json,'$.sampleTypeIds') as sampleTypeIds" +
                ",p.inceptTime,p.isStress,p.linkMan,p.linkPhone,p.inspectedEntId,p.inspectedEnt,p.inspectedLinkMan,p.inspectedLinkPhone,p.inspectedAddress");

        super.findByPage(pb, receiveSampleRecordCriteria);

        List<DtoReceiveSampleRecord> datas = pb.getData();
        if (StringUtil.isNotEmpty(datas)) {
            List<DtoReceiveSampleRecord> newDatas = new ArrayList<>();
            Iterator<DtoReceiveSampleRecord> ite = datas.iterator();

            Map<String, List<String>> sampleTypeMap = new HashMap<>();
            Set<String> sampleTypeIds = new HashSet<>();
            while (ite.hasNext()) {
                Object obj = ite.next();
                Object[] objs = (Object[]) obj;
                // 按查询顺序下标一一获取
                DtoReceiveSampleRecord record = (DtoReceiveSampleRecord) objs[0];
                record.setProjectCode((String) objs[1]);
                record.setProjectName((String) objs[2]);
                record.setProjectTypeId((String) objs[3]);
                record.setGrade((Integer) objs[4]);
                record.setCustomerId((String) objs[5]);
                record.setCustomerName((String) objs[6]);
                record.setComment((String) objs[7]);
                record.setSampleNum(0);
                if (MathUtil.isInteger(objs[8])) {
                    record.setSampleNum(Integer.valueOf(String.valueOf(objs[8])));
                }
                record.setSampleTypeIds((String) objs[9]);
                if (StringUtils.isNotNullAndEmpty(record.getSampleTypeIds())) {
                    sampleTypeMap.put(record.getId(), Arrays.stream(record.getSampleTypeIds().split(",")).collect(Collectors.toList()));
                    sampleTypeIds.addAll(sampleTypeMap.get(record.getId()));
                }
                record.setInceptTime((Date) objs[10]);
                record.setIsStress((Boolean) objs[11]);
                record.setLinkMan((String) objs[12]);
                record.setLinkPhone((String) objs[13]);
                record.setInspectedEntId((String) objs[14]);
                record.setInspectedEnt((String) objs[15]);
                record.setInspectedLinkMan((String) objs[16]);
                record.setInspectedLinkPhone((String) objs[17]);
                record.setInspectedAddress((String) objs[18]);
                newDatas.add(record);
            }
            //送样单的意见
            Map<String, String> lastNewOpinionMap = new HashMap<>();
            if (criteria.getModule().equals(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue())
                    || criteria.getModule().equals(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue())
                    || criteria.getModule().equals(EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue())) {
                // 循环迭代获取JPQL中查询返回的属性
                List<String> receiveIds = newDatas.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
                List<DtoStatusForRecord> sfpList = statusForRecordRepository.findByReceiveIdIn(receiveIds);
                lastNewOpinionMap = sfpList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getLastNewOpinion())).
                        sorted(Comparator.comparing(DtoStatusForRecord::getModifyDate).reversed()).
                        collect(Collectors.groupingBy(DtoStatusForRecord::getReceiveId,
                                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getLastNewOpinion())));
            }
            List<DtoSampleType> samTypeList = sampleTypeIds.size() > 0 ? sampleTypeService.findRedisByIds(new ArrayList<>(sampleTypeIds)) : new ArrayList<>();
            samTypeList.sort(Comparator.comparing(DtoSampleType::getTypeName));
            List<String> projectTypeIds = newDatas.stream().map(DtoReceiveSampleRecord::getProjectTypeId).distinct().collect(Collectors.toList());
            List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);
            //将list转为map
            Map<String, String> projectTypeMap = projectTypes.stream().collect(Collectors.toMap(DtoProjectType::getId, DtoProjectType::getName));
            List<String> receiveIds = newDatas.stream().map(DtoReceiveSampleRecord::getId).distinct().collect(Collectors.toList());
            List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findByReceiveIdIn(receiveIds);
            List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(receiveIds);
            List<String> samIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<String> projectIds = newDatas.parallelStream().map(DtoReceiveSampleRecord::getProjectId).collect(Collectors.toList());
            List<DtoProjectPlan> projectPlans = projectPlanRepository.findByProjectIdIn(projectIds);
            List<DtoSamplingPersonConfig> configList = samplingPersonConfigRepository.findByObjectIdIn(receiveIds);
            for (DtoReceiveSampleRecord record : newDatas) {
                record.setProjectTypeName(projectTypeMap.getOrDefault(record.getProjectTypeId(), ""));
                //需要排除已经删除或已经作废的样品
                List<DtoSampleGroup> recGroupList = sampleGroupList.stream().filter(p -> samIds.contains(p.getSampleId()) &&
                        record.getId().equals(p.getReceiveId())).collect(Collectors.toList());
                record.setSunScanCount(recGroupList.size());
                record.setScanCount(recGroupList.stream().filter(SampleGroup::getHasScanned).count());
                record.setNoScanCount(recGroupList.stream().filter(p -> !p.getHasScanned()).count());
                //填充项目计划中的数据
                Optional<DtoProjectPlan> projectPlan = projectPlans.stream()
                        .filter(p -> p.getProjectId().equals(record.getProjectId())).findFirst();
                projectPlan.ifPresent(p -> {
                    record.setDeadLine(p.getDeadLine());
                    record.setReportDate(p.getReportDate());
                });
                if (sampleTypeMap.containsKey(record.getId())) {
                    record.setSampleTypes(samTypeList.stream().filter(p -> sampleTypeMap.get(record.getId()).contains(p.getId())).collect(Collectors.toList()));
                }
                //填充项目类型编码
                Optional<DtoProjectType> projectTypeOptional = projectTypes.stream().filter(p -> p.getId().equals(record.getProjectTypeId())).findFirst();
                projectTypeOptional.ifPresent(p -> {
                    record.setProjectTypeCode("");
                    if (StringUtil.isNotEmpty(p.getConfig())) {
                        record.setProjectTypeCode(StringUtil.isNotNull(JsonIterator.deserialize(p.getConfig(), Map.class).get("LIM_ProjectTypeCode_IND"))
                                ? JsonIterator.deserialize(p.getConfig(), Map.class).get("LIM_ProjectTypeCode_IND").toString()
                                : "");
                    }
                });
                record.setBackOpinion(lastNewOpinionMap.getOrDefault(record.getId(), ""));
                List<DtoSamplingPersonConfig> personConfigList = configList.stream()
                        .filter(p -> p.getObjectId().equals(record.getId())).collect(Collectors.toList());
                if (personConfigList.size() > 0) {
                    record.setSamplingPersonIds(personConfigList.stream().map(DtoSamplingPersonConfig::getSamplingPersonId).collect(Collectors.toList()));
                }
            }
            pb.setData(newDatas);
        }
    }

    @Override
    public void queryByPage(PageBean<DtoReceiveSampleRecordQuery> pb, BaseCriteria receiveSampleRecordCriteria) {
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoReceiveSampleRecord r, DtoProject p");
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordQuery(");
        stringBuilder.append("r.id,r.projectId,r.recordCode,r.samplingTime,r.sendTime,r.senderId,");
        stringBuilder.append("r.senderName,r.receiveTime,r.recorderId,r.receiveType,r.status,r.receiveStatus,");
        stringBuilder.append("r.infoStatus,r.checkerId,json_value(r.json,'$.sampleTypeIds'),p.projectCode,p.projectTypeId,p.projectName,");
        stringBuilder.append("p.inceptPersonId,p.inceptTime,p.isStress,p.grade,p.customerId,p.customerName,");
        stringBuilder.append("p.inspectedEntId,p.inspectedLinkMan,p.inspectedLinkPhone,p.inspectedAddress,");
        stringBuilder.append("p.inspectedEnt,p.customerOwner,p.customerAddress,p.linkMan,p.linkPhone,p.linkEmail,p.linkFax,p.zipCode)");
        pb.setSelect(stringBuilder.toString());
        ReceiveSampleRecordQueryCriteria criteria = (ReceiveSampleRecordQueryCriteria) receiveSampleRecordCriteria;
        List<DtoProjectType> qcProjectTypes = projectTypeService.findByWorkflowId(EnumWorkflowCode.质控项目.getValue());
        if (qcProjectTypes.size() > 0) {
            criteria.setExceptProjectTypeId(qcProjectTypes.get(0).getId());
        }
        comRepository.findByPage(pb, criteria);
        if (StringUtil.isNotNull(pb.getData()) && pb.getData().size() > 0) {
            List<String> receiveIds = pb.getData().stream().map(DtoReceiveSampleRecordQuery::getId).collect(Collectors.toList());
            List<String> projectTypeIds = pb.getData().stream().map(DtoReceiveSampleRecordQuery::getProjectTypeId).distinct().collect(Collectors.toList());
            List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);

            List<DtoReceiveSubSampleRecord> subRecordList = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);

            List<String> objectIds = new ArrayList<>();
            objectIds.addAll(receiveIds);
            List<String> anaSubIds = subRecordList.stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0).
                    map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
            objectIds.addAll(anaSubIds);
            List<DtoSubmitRecord> submitRecords = submitRecordRepository.findByObjectIdIn(objectIds);
            submitRecords.sort(Comparator.comparing(DtoSubmitRecord::getSubmitTime));
            Map<String, DtoSubmitRecord> submitRecordMap = submitRecords.stream().
                    collect(Collectors.groupingBy(p -> p.getObjectId() + p.getSubmitType(), Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));

            Map<String, List<String>> sampleTypeMap = new HashMap<>();
            Set<String> sampleTypeIds = new HashSet<>();
            for (DtoReceiveSampleRecordQuery record : pb.getData()) {
                if (StringUtil.isNotEmpty(record.getSampleTypeIds())) {
                    sampleTypeMap.put(record.getId(), Arrays.stream(record.getSampleTypeIds().split(",")).collect(Collectors.toList()));
                    sampleTypeIds.addAll(sampleTypeMap.get(record.getId()));
                }
                Integer receiveStatus = record.getReceiveStatus();
                //如果送样单状态是新建的，说明肯定不管是外部送样、现场送样、内部送样应该都处理待处理的状态
                if (receiveStatus.equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
                    record.setSendStatus(EnumStatus.待处理.getValue());
                } else {//其余状态就按infoStatus进行判断
                    record.setSendStatus(record.getInfoStatus() > EnumReceiveInfoStatus.信息登记中.getValue() || !UUIDHelper.GUID_EMPTY.equals(record.getCheckerId()) || !record.getStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.toString()) ?
                            EnumStatus.已处理.getValue() : EnumStatus.待处理.getValue());
                }
                if (record.getSendStatus().equals(EnumStatus.已处理.getValue())) {
                    DtoSubmitRecord submit = submitRecordMap.getOrDefault(record.getId() + EnumSubmitType.数据录入提交.getValue(), null);
                    if (StringUtil.isNotNull(submit)) {
                        record.setSendStatusTip(String.format("操作人：%s</br>操作时间：%s", submit.getSubmitPersonName(), DateUtil.dateToString(submit.getSubmitTime(), DateUtil.FULL)));
                    }
                }

                DtoReceiveSubSampleRecord localSub = subRecordList.stream().filter(p -> p.getReceiveId().equals(record.getId()) && (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0).findFirst().orElse(null);

                List<String> dataStatusTipList = new ArrayList<>();
                if (StringUtil.isNotNull(localSub)) {
                    dataStatusTipList.add("现场数据状态：" + localSub.getStatus());
                }
                DtoReceiveSubSampleRecord anaSub = subRecordList.stream().filter(p -> p.getReceiveId().equals(record.getId()) && (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0).findFirst().orElse(null);
                if (StringUtil.isNotNull(anaSub)) {
                    record.setAnaSubId(anaSub.getId());
                    record.setInnerStatus(record.getReceiveStatus() > EnumLIM.EnumReceiveRecordStatus.新建.getValue() ? EnumStatus.已处理.getValue() : EnumStatus.待处理.getValue());
                    if (record.getInnerStatus().equals(EnumStatus.已处理.getValue())) {
                        DtoSubmitRecord submit = submitRecordMap.getOrDefault(record.getId() + EnumSubmitType.样品交接提交.getValue(), null);
                        if (StringUtil.isNotNull(submit)) {
                            record.setInnerStatusTip(String.format("操作人：%s</br>操作时间：%s", submit.getSubmitPersonName(), DateUtil.dateToString(submit.getSubmitTime(), DateUtil.FULL)));
                        }
                    }

                    if (record.getInnerStatus().equals(EnumStatus.已处理.getValue())) {
                        record.setAssignStatus((anaSub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) > 0 ? EnumStatus.已处理.getValue() : EnumStatus.待处理.getValue());
                        if (record.getAssignStatus().equals(EnumStatus.已处理.getValue())) {
                            DtoSubmitRecord submit = submitRecordMap.getOrDefault(anaSub.getId() + EnumSubmitType.样品分配提交.getValue(), null);
                            if (StringUtil.isNotNull(submit)) {
                                record.setAssignStatusTip(String.format("操作人：%s</br>操作时间：%s", submit.getSubmitPersonName(), DateUtil.dateToString(submit.getSubmitTime(), DateUtil.FULL)));
                            }
                        }
                    }
                    dataStatusTipList.add("实验室数据状态：" + anaSub.getStatus());
                }
                record.setDataStatusTip(String.join("</br>", dataStatusTipList));
                if (dataStatusTipList.size() > 0) {
                    record.setDataStatus(record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue()) ? EnumStatus.已处理.getValue() : EnumStatus.待处理.getValue());
                }

                DtoProjectType projectType = projectTypes.stream().filter(p -> p.getId().equals(record.getProjectTypeId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(projectType)) {
                    record.setProjectTypeName(projectType.getName());
                }
            }

            List<DtoSampleType> samTypeList = sampleTypeIds.size() > 0 ? sampleTypeService.findRedisByIds(new ArrayList<>(sampleTypeIds)) : new ArrayList<>();
            samTypeList.sort(Comparator.comparing(DtoSampleType::getTypeName));

            for (DtoReceiveSampleRecordQuery record : pb.getData()) {
                if (sampleTypeMap.containsKey(record.getId())) {
                    record.setSampleTypeNames(String.join(",", samTypeList.stream().filter(p -> sampleTypeMap.get(record.getId()).contains(p.getId())).map(DtoSampleType::getTypeName).collect(Collectors.toList())));
                }
            }
        }
    }

    @Override
    public DtoReceiveSampleRecord findOne(String id) {
        DtoReceiveSampleRecord receiveSampleRecord = repository.findOne(id);
        if (StringUtil.isNotNull(receiveSampleRecord)) {
            this.getRecordDetail(receiveSampleRecord);
        }
        return receiveSampleRecord;
    }

    @Override
    public DtoReceiveSampleRecord findOutsideSendSampleById(String projectId) {
        List<DtoReceiveSampleRecord> recordList = repository.findByProjectId(projectId);
        DtoReceiveSampleRecord record = StringUtil.isEmpty(recordList) ? null : recordList.get(0);
        DtoReceiveSampleRecord receiveSampleRecord = null;
        if (StringUtil.isNotNull(record)) {
            receiveSampleRecord = this.getRecordDetail(record);
        }
        return receiveSampleRecord;
    }

    @Transactional
    @Override
    public DtoReceiveSampleRecord save(DtoReceiveSampleRecord dto) {
        return save(dto, false);
    }

    protected void savaReceiveSampleRecordInd(DtoReceiveSampleRecord dto, DtoProject project) {

    }

    /**
     * 创建状态
     *
     * @param projectId 项目id
     */
    @Transactional
    @Override
    public void createOutsideStatus(String projectId) {
        DtoProject project = projectRepository.findOne(projectId);
        if (StringUtil.isNotNull(project)) {
            String projectType = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
            if (projectType.equals(EnumProjectType.送样类.getValue())) {
                DtoReceiveSampleRecord record = this.findOutsideSendSampleById(projectId);
                DtoStatusForRecord status = statusForRecordRepository.findByReceiveIdAndModule(record.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                Boolean skipReceive = proService.switchIsOpen(ProCodeHelper.SKIP_SAMPLE_RECEIVE);
                if (StringUtil.isNull(status)) {
                    status = new DtoStatusForRecord();
                    status.setModule(EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                    status.setReceiveId(record.getId());
                    status.setStatus(skipReceive ? EnumStatus.已处理.getValue() : EnumStatus.待处理.getValue());
                    statusForRecordService.save(status);

                    if (skipReceive) {
                        this.submitInnerSample(record.getId(), new Date(), UUIDHelper.GUID_EMPTY, "");
                    }
                    String currentModule = EnumProjectStatus.getModuleCode(project.getStatus());
                    homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                            PrincipalContextUser.getPrincipal().getOrgId()
                            , currentModule, EnumLIM.EnumHomeTaskModule.样品交接.getValue());
                }

                DtoPerson per = personService.findOne(project.getInceptPersonId());
                DtoSubmitRecord submitRecord = new DtoSubmitRecord();
                submitRecord.setObjectId(record.getId());
                submitRecord.setObjectType(EnumSubmitObjectType.送样单.getValue());
                submitRecord.setSubmitType(EnumSubmitType.数据录入提交.getValue());
                submitRecord.setSubmitPersonId(project.getInceptPersonId());
                submitRecord.setSubmitPersonName(StringUtil.isNotNull(per) ? per.getCName() : "");
                submitRecord.setSubmitTime(project.getInputTime());
                submitRecord.setNextPerson("");
                submitRecord.setSubmitRemark("");
                submitRecord.setStateFrom("");
                submitRecord.setStateTo("");
                submitRecordService.createSubmitRecords(Collections.singletonList(submitRecord),
                        PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getUserName(),
                        PrincipalContextUser.getPrincipal().getOrgId());
            }
        }
    }

    /**
     * 保存送样单数据
     *
     * @param dto             送样单数据
     * @param isUpdateProject 是否保存项目相关信息
     * @return 保存后的送样单数据
     */
    @Override
    @Transactional
    public DtoReceiveSampleRecord save(DtoReceiveSampleRecord dto, Boolean isUpdateProject) {
        if (dto.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue())) {
            DtoProject project = projectRepository.findOne(dto.getProjectId());
            if (project.getSamplingStatus().equals(EnumSampledStatus.已采毕.getValue())) {
                throw new BaseException("该项目已采样完成，无法新建送样单！");
            }
            savaReceiveSampleRecordInd(dto, project);
            if (isUpdateProject) {
                //处理项目上的收件单位数据
                loadProjectInspectedData(project, dto);
                projectRepository.save(project);
            }
        }
        dto.setRecordCode(this.createReceiveSampleRecordCode());
        dto.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.新建.getValue());
        dto.setStatus(EnumLIM.EnumReceiveRecordStatus.新建.toString());

        //写入json信息
        DtoRecordJson jsonEntity = new DtoRecordJson();
        jsonEntity.setSampleNum(dto.getSampleNum());
        jsonEntity.setSampleTypeIds(dto.getSampleTypeIds());

//        try {
//            if (StringUtil.isNotEmpty(dto.getSampleTypeIds())) {
//                List<String> sampleTypeNames = sampleTypeService.findAll(Arrays.asList(dto.getSampleTypeIds().split(",").clone()))
//                        .stream().map(DtoSampleType::getTypeName).collect(Collectors.toList());
//                jsonEntity.setLabSampleTypes(String.join(",", sampleTypeNames));
//            }
//        } catch (Exception ex) {
//            System.out.println(ex.getMessage());
//        }

        try {
            dto.setJson(JsonStream.serialize(jsonEntity));
        } catch (Exception ex) {
            log.info(ex.getMessage(), ex);
            throw new BaseException("创建样品编号存储送样单中json字段发生错误");
        }

        if (StringUtil.isNotNull(dto.getSamplingPersonIds())) {
            List<DtoSamplingPersonConfig> cfgs = new ArrayList<>();
            List<DtoPerson> personList = dto.getSamplingPersonIds().size() > 0 ?
                    personService.findAllDeleted(dto.getSamplingPersonIds()) : new ArrayList<>();
            for (DtoPerson person : personList) {
                DtoSamplingPersonConfig cfg = new DtoSamplingPersonConfig();
                cfg.setObjectId(dto.getId());
                cfg.setObjectType(EnumSamplingType.送样单.getValue());
                cfg.setSamplingPersonId(person.getId());
                cfg.setSamplingPerson(person.getCName());
                cfgs.add(cfg);
            }
            samplingPersonConfigService.save(cfgs);
        }
        if (StringUtil.isNotNull(dto.getCarIds())) {
            List<DtoSamplingCarConfig> cfgs = new ArrayList<>();
            for (String carId : dto.getCarIds()) {
                DtoSamplingCarConfig cfg = new DtoSamplingCarConfig();
                cfg.setObjectId(dto.getId());
                cfg.setObjectType(EnumSamplingCarType.送样单.getValue());
                cfg.setCarId(carId);
                cfgs.add(cfg);
            }
            samplingCarConfigService.save(cfgs);
        }

        EnumReceiveType receiveType = EnumReceiveType.getByValue(dto.getReceiveType());
        switch (receiveType) {
            case 内部送样:
            case 现场送样:
                statusForRecordService.createStatus(dto.getId(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue());
                DtoCode code = codeService.findByCode(ProCodeHelper.LIM_FLOW_RECEIVE_SAMPLE);
                if (code != null && "1".equals(code.getDictValue())) {
                    if (EnumReceiveType.内部送样.getValue().equals(dto.getReceiveType())) {
                        //判断是否只有现场数据
                        List<DtoSample> sampleList = sampleService.findByReceiveId(dto.getId());
                        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                        if (sampleIds.size() > 0) {
                            List<DtoAnalyseData> analyseDataList = analyseDataService.findDataBySampleIds(sampleIds);
                            if (analyseDataList.stream().anyMatch(p -> !p.getIsCompleteField())) {
                                statusForRecordService.createStatus(dto.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                            }
                        }
                    }
                }
                dto.setInfoStatus(EnumReceiveInfoStatus.信息登记中.getValue());
                break;

            case 委托现场送样:
                statusForRecordService.createStatus(dto.getId(), EnumLIM.EnumReceiveRecordModule.委托现场送样.getValue());
                dto.setInfoStatus(EnumReceiveInfoStatus.新建.getValue());
                break;

            case 外部送样:
                //外部送样信息状态默认为已确认
                dto.setInfoStatus(EnumReceiveInfoStatus.已确认.getValue());
                break;
        }

        repository.save(dto);

        if (dto.getReceiveType().equals(EnumReceiveType.内部送样.getValue()) || dto.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue())) {
            proService.checkProject(Collections.singletonList(dto.getProjectId()));
        }

        newLogService.createLog(dto.getId(), String.format("创建了送样单:%s", dto.getRecordCode()), "",
                EnumLogType.送样单信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.创建送样单.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        newLogService.createLog(dto.getProjectId(), String.format("创建了送样单:%s", dto.getRecordCode()), "",
                EnumLogType.项目送样单.getValue(), EnumLogObjectType.项目.getValue(), EnumLogOperateType.创建送样单.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        return dto;
    }

    /**
     * 生成送样单的时候，当送样单中存在现场测试项目时，需要在“仪器使用记录-现场仪器”中，针对每个现场测试项目，自动生成一条仪器使用记录
     *
     * @param receiveSubSampleRecordId 送样单ID
     */
    @Override
    @Transactional
    public void autoGnerateXcInstrumentUseRecord(String receiveSubSampleRecordId) {
        //需要根据测试项目上“是否填写仪器记录”判定，判定是否自动生成
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2SampleList = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(receiveSubSampleRecordId);
        List<String> sampleIds = receiveSubSampleRecord2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        List<String> testIds = analyseDataList.stream().filter(p -> !p.getIsOutsourcing()).map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        if (!testIds.isEmpty()) {
            List<DtoTest> testList = testService.findAll(testIds);
            //存在现场测试项目 + 填写仪器记录
            List<String> autoGenerateTestIdList = testList.stream().filter(t -> Boolean.TRUE.equals(t.getIsCompleteField()) && Boolean.TRUE.equals(t.getIsInsUseRecord()))
                    .map(DtoTest::getId).collect(Collectors.toList());
            List<String> existEnvironmentalRecordIdList = environmentalRecordRepository.findByObjectId(receiveSubSampleRecordId).stream()
                    .map(DtoEnvironmentalRecord::getId).collect(Collectors.toList());
            if (!existEnvironmentalRecordIdList.isEmpty()) {
                //兼容老数据，重建旧仪器使用记录与测试项目关系
                reBuildOldEnvironmentalRecord2Test(existEnvironmentalRecordIdList);
                //统计需要生成的测试项目
                List<String> existTestIds = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(existEnvironmentalRecordIdList).stream()
                        .map(DtoEnvironmentalRecord2Test::getTestId).distinct().collect(Collectors.toList());
                autoGenerateTestIdList.removeAll(existTestIds);
            }
            if (!autoGenerateTestIdList.isEmpty()) {
                List<DtoEnvironmentalRecord> environmentalRecordList = new ArrayList<>();
                List<DtoEnvironmentalRecord2Sample> environmentalRecord2SampleList = new ArrayList<>();
                List<DtoEnvironmentalRecord2Test> waitSaveRecord2TestList = new ArrayList<>();
                for (String testId : autoGenerateTestIdList) {
                    DtoEnvironmentalRecord environmentalRecord = new DtoEnvironmentalRecord();
                    environmentalRecord.setObjectId(receiveSubSampleRecordId);
                    environmentalRecord.setObjectType(EnumLIM.EnumEnvRecObjType.现场分析.getValue());
                    environmentalRecordList.add(environmentalRecord);
                    List<String> testSampleIds = analyseDataList.stream().filter(a -> testId.equals(a.getTestId()))
                            .map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                    for (String testSampleId : testSampleIds) {
                        DtoEnvironmentalRecord2Sample environmentalRecord2Sample = new DtoEnvironmentalRecord2Sample();
                        environmentalRecord2Sample.setEnvironmentalRecordId(environmentalRecord.getId());
                        environmentalRecord2Sample.setSampleId(testSampleId);
                        environmentalRecord2SampleList.add(environmentalRecord2Sample);
                    }
                    DtoEnvironmentalRecord2Test environmentalRecord2Test = new DtoEnvironmentalRecord2Test();
                    environmentalRecord2Test.setEnvironmentalRecordId(environmentalRecord.getId());
                    environmentalRecord2Test.setTestId(testId);
                    waitSaveRecord2TestList.add(environmentalRecord2Test);
                }
                environmentalRecord2TestRepository.save(waitSaveRecord2TestList);
                environmentalRecord2SampleRepository.save(environmentalRecord2SampleList);
                environmentalRecordRepository.save(environmentalRecordList);
            }
        }
    }

    /**
     * 处理老数据仪器使用与测试项目关系
     *
     * @param existEnvironmentalRecordIdList 已存在的历史使用记录
     */
    private void reBuildOldEnvironmentalRecord2Test(List<String> existEnvironmentalRecordIdList) {
        List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordService.findByEnvironmentalManageIds(existEnvironmentalRecordIdList);
        List<DtoEnvironmentalRecord2Test> existEnvironmentalRecord2TestList = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(existEnvironmentalRecordIdList);
        List<DtoEnvironmentalRecord2Test> waitSaveList = new ArrayList<>();
        for (String environmentalRecordId : existEnvironmentalRecordIdList) {
            List<String> existTestIds = existEnvironmentalRecord2TestList.stream().filter(e -> environmentalRecordId.equals(e.getEnvironmentalRecordId()))
                    .map(DtoEnvironmentalRecord2Test::getTestId).distinct().collect(Collectors.toList());
            List<String> instrumentTestIds = useRecords.stream().filter(e -> environmentalRecordId.equals(e.getEnvironmentalManageId()))
                    .map(DtoInstrumentUseRecord::getTestIds).distinct().collect(Collectors.toList());
            instrumentTestIds.removeAll(existTestIds);
            for (String testId : instrumentTestIds) {
                DtoEnvironmentalRecord2Test environmentalRecord2Test = new DtoEnvironmentalRecord2Test();
                environmentalRecord2Test.setEnvironmentalRecordId(environmentalRecordId);
                environmentalRecord2Test.setTestId(testId);
                waitSaveList.add(environmentalRecord2Test);
            }
        }
        environmentalRecord2TestRepository.save(waitSaveList);
    }


    /**
     * 加载送样单中修改的项目受检单位信息
     *
     * @param project 项目实体
     * @param dto     送样单实体
     */
    private void loadProjectInspectedData(DtoProject project, DtoReceiveSampleRecord dto) {
        if (StringUtil.isNotNull(dto.getInspectedLinkMan())) {
            project.setInspectedLinkMan(dto.getInspectedLinkMan());
        }
        if (StringUtil.isNotNull(dto.getInspectedLinkPhone())) {
            project.setInspectedLinkPhone(dto.getInspectedLinkPhone());
        }
        if (StringUtil.isNotNull(dto.getInspectedAddress())) {
            project.setInspectedAddress(dto.getInspectedAddress());
        }
    }

    /**
     * 修改送样单
     *
     * @param dto 送样单
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecord updateRecord(DtoReceiveSampleRecord dto, Boolean isUpdateProject) {
        if (StringUtil.isNotNull(dto.getProjectId())) {
            DtoProject project = projectRepository.findOne(dto.getProjectId());
            if (isUpdateProject) {
                //处理项目上的收件单位数据
                loadProjectInspectedData(project, dto);
                projectRepository.save(project);
            }
        }
        DtoReceiveSampleRecord record = this.findOne(dto.getId());
        Map<String, Map<String, Object>> recordChange = proService.getCompare(record, dto);
        List<String> contents = this.getChangeContent(recordChange, EnumReceiveSampleRecordField.送样人员,
                EnumReceiveSampleRecordField.送样日期, EnumReceiveSampleRecordField.采样日期, EnumReceiveSampleRecordField.备注);
        dto.setJson(record.getJson());
        //修改了采样人才修改对应表数据
        if (!record.getSamplingPersonIds().equals(dto.getSamplingPersonIds())) {
            samplingPersonConfigRepository.deleteByObjectId(dto.getId());
            List<DtoSamplingPersonConfig> cfgs = new ArrayList<>();
            List<DtoPerson> personList = StringUtil.isNotNull(dto.getSamplingPersonIds()) && dto.getSamplingPersonIds().size() > 0 ?
                    personService.findAllDeleted(dto.getSamplingPersonIds()) : new ArrayList<>();
            for (DtoPerson person : personList) {
                DtoSamplingPersonConfig cfg = new DtoSamplingPersonConfig();
                cfg.setObjectId(dto.getId());
                cfg.setObjectType(EnumSamplingType.送样单.getValue());
                cfg.setSamplingPersonId(person.getId());
                cfg.setSamplingPerson(person.getCName());
                cfgs.add(cfg);
            }
            if (cfgs.size() > 0) {
                samplingPersonConfigService.save(cfgs);
            }
            contents.add(String.format("</br>采样人员由'%s',修改为'%s'", String.join("、", record.getSamplingPersonNames()),
                    cfgs.stream().map(DtoSamplingPersonConfig::getSamplingPerson).collect(Collectors.joining("、"))));
        }
        //修改了采样车辆才修改对应表数据
        if (!record.getCarIds().equals(dto.getCarIds())) {
            samplingCarConfigRepository.deleteByObjectId(dto.getId());
            List<DtoSamplingCarConfig> cfgs = new ArrayList<>();
            for (String carId : dto.getCarIds()) {
                DtoSamplingCarConfig cfg = new DtoSamplingCarConfig();
                cfg.setObjectType(EnumSamplingCarType.送样单.getValue());
                cfg.setObjectId(dto.getId());
                cfg.setCarId(carId);
                cfgs.add(cfg);
            }
            String carNames = "";
            if (cfgs.size() > 0) {
                List<DtoCarManage> carList = carManageService.findAll(dto.getCarIds());
                carNames = carList.stream().map(DtoCarManage::getCarCode).collect(Collectors.joining("、"));
                samplingCarConfigService.save(cfgs);
            }

            contents.add(String.format("</br>采样车辆由'%s',修改为'%s'", String.join("、", record.getCarNames()), carNames));
        }
        //修改了采样时间才修改对应表数据
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(record.getId());
        updateSamplingTime(record, dto, sampleList);
        record.setSamplingPersonIds(dto.getSamplingPersonIds());
        record.setCarIds(dto.getCarIds());
        record.setSenderId(dto.getSenderId());
        record.setSenderName(dto.getSenderName());
        dto.setSendTime(dto.getSendTime());
        dto.setSamplingTime(dto.getSamplingTime());
        //添加修改接样日期日志
        if (StringUtil.isNotNull(dto.getReceiveSampleDate()) && StringUtil.isNotNull(record.getReceiveSampleDate())) {
            String updateRecordDate = DateUtil.dateToString(dto.getReceiveSampleDate(), DateUtil.FULL);
            String recordDate = DateUtil.dateToString(record.getReceiveSampleDate(), DateUtil.FULL);
            if (!recordDate.equals(updateRecordDate)) {
                contents.add(String.format("</br>接样日期由'%s',修改为'%s'", recordDate, updateRecordDate));
                //更新样品有效期 去除修改接样日期修改样品有效期功能 BUG2025080800413
//                List<String> sampleIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
//                List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
//                if (StringUtil.isNotEmpty(analyseDataList)) {
//                    List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
//                    List<DtoTest> allTests = StringUtil.isNotEmpty(testIds) ? testService.findAll(testIds) : new ArrayList<>();
//                    Map<String, DtoTest> testMap = allTests.stream().collect(Collectors.toMap(DtoTest::getId, t -> t));
//                    Calendar cal = Calendar.getInstance();
//                    analyseDataList.forEach(a -> {
//                        DtoTest test = testMap.getOrDefault(a.getTestId(), null);
//                        if (StringUtil.isNotNull(test) && StringUtil.isNotNull(test.getValidTime()) && test.getValidTime().compareTo(BigDecimal.valueOf(0)) > 0) {
//                            cal.clear();
//                            cal.setTime(dto.getReceiveSampleDate());
//                            cal.add(Calendar.DATE, (int) Math.ceil(test.getValidTime().doubleValue() / 24));
//                            a.setRequireDeadLine(cal.getTime());
//                        }
//                    });
//                    updateRequireDeadLine(dto, analyseDataList, testMap);
//                    analyseDataRepository.save(analyseDataList);
//                }
            }
        }
        // 更新样品分组信息
        sampleGroupService.updateByReceiveInfo(dto);
        comRepository.merge(dto);

        if (contents.size() > 0) {
            newLogService.createLog(dto.getId(), String.format("修改了送样单%s信息,具体如下:%s", record.getRecordCode(), String.join(";", contents)), "",
                    EnumLogType.送样单信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.修改送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }

        return dto;
    }

    protected void updateRequireDeadLine(DtoReceiveSampleRecord dto, List<DtoAnalyseData> analyseDataList, Map<String, DtoTest> testMap) {

    }

    /**
     * 修改送样单
     *
     * @param dto 送样单
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecord updateRecord(DtoReceiveSampleRecord dto) {
        return updateRecord(dto, false);
    }

    /**
     * 采样准备创建送样单
     *
     * @param project           项目
     * @param samples           样品
     * @param samplingTime      采样时间
     * @param samplingPersonId  采样负责人id
     * @param samplingPerson    采样负责人
     * @param samplingPersonIds 采样人员
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecord createReceiveRecord(DtoProject project,
                                                      List<DtoSample> samples,
                                                      Date samplingTime,
                                                      String samplingPersonId,
                                                      String samplingPerson,
                                                      List<String> samplingPersonIds) {
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.setProjectId(project.getId());
        record.setReceiveType(EnumReceiveType.内部送样.getValue());
        record.setInfoStatus(EnumReceiveInfoStatus.信息登记中.getValue());
        record.setSenderId(samplingPersonId);
        record.setSenderName(samplingPerson);
        record.setSendTime(samplingTime);
        record.setSamplingTime(samplingTime);
        record.setSamplingPersonIds(samplingPersonIds);
        record.setSampleNum(samples.size());
        if (StringUtil.isNotEmpty(samples)) {
            List<String> sampleTypeIds = samples.parallelStream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            if (sampleTypeIds.size() > 1) {
                log.info("===============异常，检测类型个数大于1了,送样单编号： " + record.getRecordCode() +
                        "; sampleTypeIds: " + sampleTypeIds + "===============");
                sampleTypeIds = sampleTypeIds.parallelStream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
                if (StringUtil.isEmpty(sampleTypeIds)) {
                    sampleTypeIds = Collections.singletonList(samples.get(0).getSampleTypeId());
                }
            } else {
                log.info("===============异常，检测类型个数等于1了,送样单编号： " + record.getRecordCode() +
                        "; sampleTypeIds: " + sampleTypeIds + "===============");
            }
            record.setSampleTypeIds(sampleTypeIds.get(0));
        }
        //设置样品分析数据的分析时间为采样时间
        analyseDataService.setAnalyzeTimeForSample(samples, samplingTime);
        return this.save(record);
    }

    @Transactional
    @Override
    public DtoReceiveSampleRecord createReceiveRecordToPhone(DtoProject project,
                                                             List<DtoSample> samples,
                                                             Date samplingTime,
                                                             String samplingPersonId,
                                                             String samplingPerson,
                                                             List<String> samplingPersonIds) {
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.setProjectId(project.getId());
        record.setReceiveType(EnumReceiveType.内部送样.getValue());
        record.setInfoStatus(EnumReceiveInfoStatus.信息登记中.getValue());
        record.setSenderId(samplingPersonId);
        record.setSenderName(samplingPerson);
        record.setSendTime(samplingTime);
        record.setSamplingTime(samplingTime);
        record.setSamplingPersonIds(samplingPersonIds);
        record.setSampleNum(samples.size());
        if (StringUtil.isNotEmpty(samples)) {
            record.setSampleTypeIds(samples.get(0).getSampleTypeId());
        }
        return this.save(record);
    }

    /**
     * 创建内部送样单
     *
     * @param temp 内部送样单
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecord createInnerReceiveRecord(DtoInnerRecordTemp temp) {
        DtoProject project = projectRepository.findOne(temp.getProjectId());
        List<DtoSample> sampleList = sampleRepository.findAll(temp.getSampleIds());
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.setProjectId(project.getId());
        record.setReceiveType(EnumReceiveType.内部送样.getValue());
        record.setInfoStatus(EnumReceiveInfoStatus.信息登记中.getValue());
        record.setSenderId(temp.getSenderId());
        record.setSenderName(temp.getSenderName());
        record.setSendTime(temp.getSendTime());
        record.setSamplingTime(temp.getSamplingTime());
        record.setSampleNum(sampleList.size());
        record.setSampleTypeIds(sampleList.get(0).getSampleTypeId());
        record.setSamplingPersonIds(temp.getSamplingPersonIds());
        record.setCarIds(temp.getCarIds());
        record.setRemark(temp.getRemark());
        for (DtoSample sample : sampleList) {
            sample.setReceiveId(record.getId());
            sample.setRemark(temp.getRemark());
            sample.setSamplingTimeBegin(temp.getSamplingTime());
            sample.setSamplingTimeEnd(temp.getSamplingTime());
            comRepository.merge(sample);
        }
        //设置样品现场分析数据的分析时间为采样时间
        analyseDataService.setAnalyzeTimeForSample(sampleList, temp.getSamplingTime());
        DtoReceiveSampleRecord dto = this.save(record);

        proService.checkReceiveSampleRecords(Collections.singletonList(dto));

        //#region 操作日志
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : sampleList) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.样品采样.toString());
            log.setLogType(EnumLogType.样品流程.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment(String.format("将样品%s加入送样单%s。", sample.getCode(), dto.getRecordCode()));
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumLogType.样品流程.getValue());
        //#endregion
        return dto;
    }

    /**
     * 加入送样单
     *
     * @param receiveId 送样单id
     * @param sampleIds 样品id集合
     */
    @Transactional
    @Override
    public void joinRecord(String receiveId, List<String> sampleIds) {
        DtoReceiveSampleRecord record = repository.findOne(receiveId);
        if (!record.getReceiveType().equals(EnumReceiveType.内部送样.getValue())) {
            throw new BaseException("内部送样才可以选择样品！");
        }
        List<String> samIds = sampleRepository.findByReceiveId(receiveId).stream()
                .map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);

        for (DtoSample sample : sampleList) {
            sample.setReceiveId(receiveId);
            sample.setModifyDate(new Date());
            sample.setModifier(PrincipalContextUser.getPrincipal().getUserId());
        }
        sampleRepository.save(sampleList);
//        List<DtoSample> sampleDatas = new ArrayList<>();
//        for (String sampleId : sampleIds) {
//            DtoSample sampleData = new DtoSample();
//            sampleData.setId(sampleId);
//            sampleData.setReceiveId(receiveId);
//            sampleData.setModifyDate(new Date());
//            sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
//            sampleDatas.add(sampleData);
//        }
//        if (sampleDatas.size() > 0) {
////            comRepository.updateBatch(sampleDatas);
//            commonBatchRepository.batchUpdate(sampleDatas);
//        }

        proService.checkSamples(sampleList);

        //新增的指标
        Set<String> tIds = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds)
                .stream().map(DtoAnalyseData::getTestId).collect(Collectors.toSet());
        DtoProject project = projectService.findOne(record.getProjectId());

        //当前送样单下的指标
        Set<String> allTestIds = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(samIds)
                .stream().map(DtoAnalyseData::getTestId).collect(Collectors.toSet());
        sampleJudgeDataService.createJudgeDataBySampleTest(sampleList, tIds, allTestIds, project);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.送样单选择样品, record.getProjectId(), receiveId);
                    }
                }
        );

        //#region 操作日志
        List<DtoLog> sampleLogList = new ArrayList<>();
        List<DtoLog> recordLogList = new ArrayList<>();
        for (DtoSample sample : sampleList) {
            DtoLog sampleLog = new DtoLog();
            sampleLog.setId(UUIDHelper.NewID());
            sampleLog.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            sampleLog.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            sampleLog.setOperateTime(new Date());
            sampleLog.setOperateInfo(EnumLogOperateType.样品采样.toString());
            sampleLog.setLogType(EnumLogType.样品流程.getValue());
            sampleLog.setObjectId(sample.getId());
            sampleLog.setObjectType(EnumLogObjectType.样品.getValue());
            sampleLog.setComment(String.format("将样品%s加入送样单%s。", sample.getCode(), record.getRecordCode()));
            sampleLog.setOpinion("");
            sampleLog.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            sampleLog.setRemark("");
            sampleLogList.add(sampleLog);

            DtoLog recordLog = new DtoLog();
            recordLog.setId(UUIDHelper.NewID());
            recordLog.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            recordLog.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            recordLog.setOperateTime(new Date());
            recordLog.setOperateInfo(EnumLogOperateType.修改送样单.toString());
            recordLog.setLogType(EnumLogType.送样单样品信息.getValue());
            recordLog.setObjectId(receiveId);
            recordLog.setObjectType(EnumLogObjectType.送样单.getValue());
            recordLog.setComment(String.format("将样品%s加入送样单%s。", sample.getCode(), record.getRecordCode()));
            recordLog.setOpinion("");
            recordLog.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            recordLog.setRemark("");
            recordLogList.add(recordLog);
        }
        newLogService.createLog(sampleLogList, EnumLogType.样品流程.getValue());
        newLogService.createLog(recordLogList, EnumLogType.送样单样品信息.getValue());
        //#endregion
    }

    /**
     * 剔除送样单样品
     *
     * @param receiveId 送样单id
     * @param sampleIds 样品id集合
     */
    @Transactional
    @Override
    public Boolean removeSample(String receiveId, List<String> sampleIds) {
        DtoReceiveSampleRecord record = repository.findOne(receiveId);
        if (!record.getReceiveType().equals(EnumReceiveType.内部送样.getValue())) {
            throw new BaseException("内部送样才可以剔除样品！");
        }
        //读取该送样单下的所选的待剔除样品
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        List<DtoSample> removeSampleList = sampleList.stream().filter(p -> sampleIds.contains(p.getId())).collect(Collectors.toList());

        if (removeSampleList.stream().anyMatch(p -> EnumSampleStatus.样品检毕.name().equals(p.getStatus()))) {
            List<String> samIds = removeSampleList.stream().filter(p -> EnumSampleStatus.样品检毕.name().equals(p.getStatus()))
                    .map(DtoSample::getId).collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(samIds);
            // 剔除分析分包数据后，如果存在样品检毕则给出提示
            analyseDataList = analyseDataList.stream().filter(p -> !p.getIsSamplingOut()).collect(Collectors.toList());
            if (analyseDataList.size() > 0) {
                throw new BaseException("存在已检毕样品，请确认！");
            }
        }

        //筛选出质控样id，需要进行删除而不是剔除
        List<String> qcSampleIds = removeSampleList.stream().filter(p -> !p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).
                map(DtoSample::getId).collect(Collectors.toList());

        // 判定质控样数据状态是否已加入实验室检测单并提交
        schemeService.checkQcAnalyseDataStatus(qcSampleIds);

        //对于原样，进行剔除操作
        List<DtoSample> sampleDatas = new ArrayList<>();
        for (DtoSample sample : removeSampleList) {
            if (sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                DtoSample sampleData = new DtoSample();
                sampleData.setId(sample.getId());
                sampleData.setReceiveId(UUIDHelper.GUID_EMPTY);
                sampleData.setSamplingStatus(EnumSamplingStatus.采样中.getValue());
                sampleData.setModifyDate(new Date());
                sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                sampleDatas.add(sampleData);
            }
        }
        if (sampleDatas.size() > 0) {
//            comRepository.updateBatch(sampleDatas);
            commonBatchRepository.batchUpdate(sampleDatas);
            //删除现场领养单和被剔除的原样样品的关联关系
            List<DtoReceiveSubSampleRecord> receiveSubSampleRecordList = receiveSubSampleRecordRepository.findByReceiveIdIn(Collections.singletonList(receiveId));
            List<DtoReceiveSubSampleRecord2Sample> delAllSubRecord2SampleList = new ArrayList<>();
            receiveSubSampleRecordList.forEach(subRecord -> {
                String xcSubId = subRecord.getId();
                List<String> deleteSampleIdList = sampleDatas.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
                List<DtoReceiveSubSampleRecord2Sample> subRecord2SampleList = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(xcSubId);
                subRecord2SampleList = subRecord2SampleList.stream().filter(p -> deleteSampleIdList.contains(p.getSampleId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(subRecord2SampleList)) {
                    delAllSubRecord2SampleList.addAll(subRecord2SampleList);
                }
            });

            if (delAllSubRecord2SampleList.size() > 0) {
                receiveSubSampleRecord2SampleRepository.logicDeleteById(delAllSubRecord2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getId).collect(Collectors.toList()), new Date());
            }
            comRepository.clear();
        }
        //清除对应比对数据
        sampleJudgeDataService.deleteJudgeDataByTest(sampleIds, new ArrayList<>(), Boolean.FALSE);
        if (qcSampleIds.size() > 0) {
            schemeService.deleteOutsideSample(qcSampleIds, receiveId);
        }
        //插入日志
        this.createRemoveSampleLogs(record, removeSampleList);

        if (removeSampleList.size() == sampleList.size()) {
            //送样单下所有样品被移除，需纠正送样单状态
            proService.checkReceiveSampleRecord(Collections.singletonList(receiveId));
            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.送样单剔除样品, record.getProjectId());
                        }
                    }
            );
            return true;
        } else {
            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.送样单剔除样品, record.getProjectId(), receiveId);
                        }
                    }
            );
            return false;
        }
    }

    /**
     * 插入移出送样单的日志
     *
     * @param record  送样单
     * @param samples 移除的样品
     */
    private void createRemoveSampleLogs(DtoReceiveSampleRecord record, List<DtoSample> samples) {
        List<String> redFolderNames = new ArrayList<>();
        for (DtoSample sample : samples) {
            redFolderNames.add(sampleService.getSampleName(sample, ""));
        }
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : samples.stream().filter(p -> !p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).collect(Collectors.toList())) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.删除样品.toString());
            log.setLogType(EnumLogType.样品信息.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment("删除了样品");
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumLogType.样品信息.getValue());
        }
        newLogService.createLog(record.getId(), String.format("在送样单%s中,删除了样品</br>%s", record.getRecordCode(), String.join(",</br>", redFolderNames)), "",
                EnumLogType.送样单样品信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.删除样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        DtoReceiveSampleRecord record = repository.findOne(idStr);
        if (StringUtil.isNull(record)) {
            return 0;
        }
        if ((record.getReceiveType().equals(EnumReceiveType.内部送样.getValue()) || record.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue())) &&
                (record.getInfoStatus() > EnumReceiveInfoStatus.信息登记中.getValue() || !record.getStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.toString()))) {
            throw new BaseException("存在已经提交过的内部送样单，无法进行删除！");
        }
        newLogService.createLog(record.getProjectId(), "删除了送样单：" + record.getRecordCode(),
                "", EnumLogType.项目送样单.getValue(), EnumLogObjectType.项目.getValue(), EnumLogOperateType.删除送样单.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        newLogService.createLog(idStr, "删除送样单",
                "", EnumLogType.送样单信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.删除送样单.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        if (record.getReceiveType().equals(EnumReceiveType.外部送样.getValue()) ||
                record.getReceiveType().equals(EnumReceiveType.现场送样.getValue())) {
            return projectService.logicDeleteById(record.getProjectId());
        } else {
            List<DtoSample> sampleList = sampleRepository.findByReceiveId(idStr);
            List<DtoSample> sampleDatas = new ArrayList<>();
            for (DtoSample sample : sampleList) {
                DtoSample sampleData = new DtoSample();
                sampleData.setId(sample.getId());
                sampleData.setReceiveId(UUIDHelper.GUID_EMPTY);
                sampleData.setIsDeleted(record.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue()) ||
                        !sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue()));
                sampleDatas.add(sampleData);
            }
            if (sampleDatas.size() > 0) {
//                comRepository.updateBatch(sampleDatas);
                sampleRepository.save(sampleDatas);
            }
            if (record.getReceiveType().equals(EnumReceiveType.内部送样.getValue()) && sampleList.stream().anyMatch(p -> p.getSampleCategory().equals(EnumSampleCategory.原样.getValue()))) {
                analyseDataRepository.updateLocalStatusBySample(sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).map(DtoSample::getId).collect(Collectors.toList()),
                        EnumAnalyseDataStatus.未测.toString(), EnumAnalyseDataStatus.未测.getValue(), false, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }
            List<DtoReceiveSubSampleRecord> subRecords = receiveSubSampleRecordRepository.findByReceiveId(idStr);
            if (StringUtil.isNotNull(subRecords) && subRecords.size() > 0) {
                List<String> subIds = subRecords.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
                receiveSubSampleRecord2SampleRepository.deleteByReceiveSubSampleRecordIdIn(subIds, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                receiveSubSampleRecordService.logicDeleteById(subIds);
            }
            Integer delete = super.logicDeleteById(id);
            if (record.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue()) && StringUtil.isNotEmpty(record.getProjectId())) {
                //保证事务提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumProAction.委托现场送样单删除, record.getProjectId());
                            }
                        }
                );
            }
            return delete;
        }
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> receiveIds = new ArrayList<>();
        for (Object id : ids) {
            receiveIds.add(String.valueOf(id));
        }
        List<DtoReceiveSampleRecord> recordList = this.findAll(receiveIds);
        if (recordList.stream().anyMatch(record -> (record.getReceiveType().equals(EnumReceiveType.内部送样.getValue()) || record.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue())) &&
                (record.getInfoStatus() > EnumReceiveInfoStatus.信息登记中.getValue() || !UUIDHelper.GUID_EMPTY.equals(record.getCheckerId()) || !record.getStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.toString())))) {
            throw new BaseException("存在已经提交过的内部送样单，无法进行删除！");
        }
        List<String> outProjectIds = recordList.stream().filter(p -> p.getReceiveType().equals(EnumReceiveType.外部送样.getValue()) ||
                p.getReceiveType().equals(EnumReceiveType.现场送样.getValue())).map(DtoReceiveSampleRecord::getProjectId).distinct().collect(Collectors.toList());

        Integer outDel = 0;
        if (outProjectIds.size() > 0) {
            outDel = projectService.logicDeleteById(outProjectIds);
        }

        List<String> innerReceiveIds = recordList.stream().filter(p -> p.getReceiveType().equals(EnumReceiveType.内部送样.getValue())).map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
        if (innerReceiveIds.size() > 0) {
            List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(innerReceiveIds);
            for (DtoSample sample : sampleList) {
                sample.setReceiveId(UUIDHelper.GUID_EMPTY);
                sample.setIsDeleted(!sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue()));
                comRepository.merge(sample);
            }
            List<String> samIds = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).map(DtoSample::getId).collect(Collectors.toList());
            if (samIds.size() > 0) {
                analyseDataRepository.updateLocalStatusBySample(samIds,
                        EnumAnalyseDataStatus.未测.toString(), EnumAnalyseDataStatus.未测.getValue(), false, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }
        }

        List<String> innerLocalReceiveIds = recordList.stream().filter(p -> p.getReceiveType().equals(EnumReceiveType.委托现场送样.getValue())).map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
        if (innerLocalReceiveIds.size() > 0) {
            List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(innerLocalReceiveIds);
            for (DtoSample sample : sampleList) {
                sample.setReceiveId(UUIDHelper.GUID_EMPTY);
                sample.setIsDeleted(true);
                comRepository.merge(sample);
            }
        }

        Integer innerDel = 0;
        innerReceiveIds.addAll(innerLocalReceiveIds);
        if (innerReceiveIds.size() > 0) {
            List<DtoReceiveSubSampleRecord> subRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(innerReceiveIds);
            if (StringUtil.isNotNull(subRecords) && subRecords.size() > 0) {
                List<String> subIds = subRecords.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
                receiveSubSampleRecord2SampleRepository.deleteByReceiveSubSampleRecordIdIn(subIds, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                receiveSubSampleRecordService.logicDeleteById(subIds);
            }
            innerDel = super.logicDeleteById(innerReceiveIds);
        }

        //#region 操作日志
        List<DtoLog> recordLogList = new ArrayList<>();
        for (String receiveId : receiveIds) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.删除送样单.toString());
            log.setLogType(EnumLogType.送样单信息.getValue());
            log.setObjectId(receiveId);
            log.setObjectType(EnumLogObjectType.送样单.getValue());
            log.setComment("删除送样单");
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            recordLogList.add(log);
        }
        newLogService.createLog(recordLogList, EnumLogType.送样单信息.getValue());

        List<DtoLog> projectLogList = new ArrayList<>();
        for (DtoReceiveSampleRecord record : recordList) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.删除送样单.toString());
            log.setLogType(EnumLogType.项目送样单.getValue());
            log.setObjectId(record.getProjectId());
            log.setObjectType(EnumLogObjectType.项目.getValue());
            log.setComment("删除了送样单：" + record.getRecordCode());
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            projectLogList.add(log);
        }
        newLogService.createLog(projectLogList, EnumLogType.项目送样单.getValue());
        //#endregion

        return outDel + innerDel;
    }

    //#region 送样单提交

    /**
     * 提交送样单
     *
     * @param ids  送样单id集合
     * @param type 提交类型
     */
    @Transactional
    @Override
    public List<String> submitReceiveRecord(List<String> ids, String type, String nextPersonId, String nextPerson, String opinion, Date receiveSampleDate, String recipientId)
            throws Exception {
        List<String> checkSampleIds = new ArrayList<>();
        for (String id : ids) {
            checkSampleIds.addAll(this.submitReceiveRecord(id, type, nextPersonId, nextPerson, opinion, receiveSampleDate, recipientId));
        }
        return checkSampleIds;
    }

    /**
     * 提交送样单
     *
     * @param id           送样单id
     * @param type         提交类型
     * @param nextPersonId 下一步操作人
     * @param nextPerson   下一步操作人
     * @param opinion      意见
     */
    @Transactional
    public List<String> submitReceiveRecord(String id, String type, String nextPersonId, String nextPerson, String opinion, Date receiveSampleDate, String recipientId)
            throws Exception {
        if (type.equals(EnumReceiveSubmitType.委托现场提交.getCode())) {
            return this.submitLocalSendSample(id, nextPersonId, nextPerson, opinion);
        } else if (type.equals(EnumReceiveSubmitType.数据录入提交.getCode())) {
            try {
                DtoReceiveSampleRecord record = repository.findOne(id);
                Boolean isXC = false;
                //找到对应的送样单下的数据，是否存在现场数据
                DtoReceiveSubSampleRecord subSampleRecord = receiveSubSampleRecordService.findByReceiveIdAndType(id, EnumPRO.EnumSubRecordType.现场.getValue());
                if (StringUtil.isNotNull(subSampleRecord)) {
                    isXC = true;
                }
                SimpleDateFormat date = new SimpleDateFormat("yyyy.MM.dd");
                String samplingTime = date.format(record.getSamplingTime());
                signatureService.sig(id, Collections.singletonList(UUIDHelper.GUID_EMPTY), EnumSigType.测试人员.getValue(), "", samplingTime, isXC);
                signatureService.sig(id, Collections.singletonList(UUIDHelper.GUID_EMPTY), EnumSigType.采样人.getValue(), "", samplingTime, isXC);
                //判断是否有陪同人，只获取厂方人员的签名
                String docType = "PRO_RecordSignature_QM";
                List<String> docPathList = documentRepository.findByFolderId(id).stream().filter(p ->
                        docType.equals(p.getDocTypeId()) && p.getPath().contains("厂方"))
                        .map(DtoDocument::getPath).distinct().collect(Collectors.toList());
                //todo 调用一次文件获取的方式，方法需要做调整
                String sendTime = date.format(record.getSendTime());
                if (docPathList.size() > 0) {
                    signatureService.sig(id, docPathList, EnumSigType.陪同人.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
                }
                signatureService.sig(id, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.送样者.getValue(), "", sendTime, isXC);
                signatureService.sig(id, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.分析人.getValue(), "", sendTime, isXC);
                sigSubmitExcel(id, isXC);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                throw new Exception(ex.getMessage());
//                throw new Exception("采样单签名存在问题！");
            }
            return this.submitLocalData(id, nextPersonId, nextPerson, opinion);
        } else if (type.equals(EnumReceiveSubmitType.样品交接提交.getCode())) {
            String dateStr = StringUtil.isNotNull(receiveSampleDate) ? DateUtil.dateToString(receiveSampleDate, DateUtil.FULL_NO_SECOND) : "";
//            signatureService.sig(id, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.接样者.getValue(), "", dateStr);
            sigSubmitExcelForSampleReceive(id, receiveSampleDate);
            return this.submitInnerSample(id, receiveSampleDate, recipientId, opinion);
        } else if (type.equals(EnumReceiveSubmitType.质控项目提交.getCode())) {
            return this.submitQMSample(id, receiveSampleDate);
        }
        return new ArrayList<>();
    }

    /**
     * 供项目个性化使用
     *
     * @param receiveId 送样单id
     */
    protected void sigSubmitExcel(String receiveId, Boolean isXC) {

    }

    protected void sigSubmitExcelForSampleReceive(String receiveId, Date receiveSampleDate) {

    }

    /**
     * 委托现场提交
     *
     * @param id           送样单id
     * @param nextPersonId 下一步操作人id
     * @param nextPerson   下一步操作人
     * @param opinion      提交意见
     */
    @Transactional
    public List<String> submitLocalSendSample(String id, String nextPersonId, String nextPerson, String opinion) {
        DtoReceiveSampleRecord record = repository.findOne(id);
        if (record.getInfoStatus().equals(EnumReceiveInfoStatus.新建.getValue())) {
            //修改信息状态
            record.setInfoStatus(EnumReceiveInfoStatus.信息登记中.getValue());
            statusForRecordService.modifyStatus(record, nextPersonId, nextPerson, opinion);
            repository.submitRecordInfo(id, EnumReceiveInfoStatus.信息登记中.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
            //comRepository.merge(record);

            //插入送样单日志
            String comment = String.format("将送样单%s流转至数据录入。", record.getRecordCode());
            newLogService.createLog(record.getId(), comment,
                    "", EnumLogType.送样单流程.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.提交送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

            //插入提交记录
            submitRecordService.createSubmitRecord(record.getId(), EnumSubmitObjectType.送样单.getValue(), EnumSubmitType.委托现场提交.getValue(),
                    nextPerson, opinion, "", "");

            //缓存首页数据
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.现场委托送样.getValue(), EnumLIM.EnumHomeTaskModule.现场任务.getValue()
            );

            List<DtoSample> sampleList = sampleRepository.findByReceiveId(id);
            List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            //现场任务和样品交接并行，不卡先后顺序
            flowToReceiveSample(sampleIdList);
        }

        return new ArrayList<>();
    }

    /**
     * 数据录入提交
     *
     * @param id           送样单id
     * @param nextPersonId 下一步操作人
     * @param nextPerson   下一步操作人
     * @param opinion      意见
     */
    @Transactional
    public List<String> submitLocalData(String id, String nextPersonId, String nextPerson, String opinion) {
        DtoReceiveSampleRecord record = repository.findOne(id);
        List<String> checkSampleIds = new ArrayList<>();
        if (record.getInfoStatus().equals(EnumReceiveInfoStatus.信息登记中.getValue())) {
            //获取现场领样单
            DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.现场.getValue());
            if (StringUtil.isNotNull(subRecord)) {
                //根据领样单读取现场数据，筛选为未提交的数据，并修改数据状态为已测
                List<DtoAnalyseData> analyseDataList = receiveSubSampleRecordService.findLocalDataBySubId(subRecord.getId());
                analyseDataList = analyseDataList.stream().filter(p -> p.getDataStatus().equals(EnumAnalyseDataStatus.未测.getValue()) ||
                        p.getDataStatus().equals(EnumAnalyseDataStatus.在测.getValue()) ||
                        p.getDataStatus().equals(EnumAnalyseDataStatus.拒绝.getValue())).collect(Collectors.toList());
                //送样人id
                String senderId = record.getSenderId();
                //登记人id
                String recorderId = record.getRecorderId();
                String anaId = !StringUtils.isNotNullAndEmpty(senderId) ||
                        senderId.equals(UUIDHelper.GUID_EMPTY) ? recorderId : senderId;
                String name = personService.findPersonNameById(anaId);
                //修改数据状态
                this.updateLocalAnalyseStatus(analyseDataList,
                        EnumAnalyseDataStatus.已测.getValue(),
                        EnumAnalyseDataStatus.已测.toString(),
                        false, anaId, name);
                comRepository.clear();

                //若存在现场领样单，修改领样单状态
                receiveSubSampleRecordRepository.updateStatus(Collections.singletonList(subRecord.getId()),
                        EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue(),
                        EnumReceiveSubRecordStatusName.复核中.toString());
            }

            //修改送样单状态
            DtoReceiveSampleRecord dtoRecord = new DtoReceiveSampleRecord();
            BeanUtils.copyProperties(record, dtoRecord);
            dtoRecord.setInfoStatus(EnumReceiveInfoStatus.信息复核中.getValue());
            statusForRecordService.modifyStatus(dtoRecord, nextPersonId, nextPerson, opinion);
            repository.submitRecordInfo(Collections.singletonList(record.getId()), EnumReceiveInfoStatus.信息复核中.getValue(),
                    EnumReceiveUploadStatus.已数据同步.getValue(), nextPersonId,
                    PrincipalContextUser.getPrincipal().getUserId(), new Date());

            DtoProject project = projectRepository.findOne(record.getProjectId());
            if (project.getStatus().equals(EnumProjectStatus.项目登记中.toString())) {
                try {
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(record.getProjectId());
                    dtoWorkflowSign.setSignal("projectLaunch");
                    dtoWorkflowSign.setNextOperatorId(nextPersonId);
                    dtoWorkflowSign.setNextOperator(nextPerson);
                    dtoWorkflowSign.setOption(opinion);
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String status = workflowService.submitSign(dtoWorkflowSign);
                    if (StringUtils.isNotNullAndEmpty(status)) {
                        projectRepository.updateProjectStatus(Collections.singletonList(project.getId()), status, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        statusForProjectService.modifyStatus(EnumProjectStatus.项目登记中.toString(), status, dtoWorkflowSign, project);
                    }

                    //保证事物提交之后才执行
                    TransactionSynchronizationManager.registerSynchronization(
                            new TransactionSynchronizationAdapter() {
                                @Override
                                public void afterCommit() {
                                    newLogService.createProjectStatusUpdateLog(Collections.singletonList(project.getId()), opinion, nextPersonId, nextPerson);
                                    //环保企业通推送项目信息
                                    environmentEnterpriseService.pushProjectData(Collections.singletonList(project), EnumProjectStatus.开展中.name());
                                }
                            }
                    );
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                    throw new BaseException("异常错误");
                }
            }

            //样品状态置为已经完成取样
            sampleRepository.updateSamplingStatus(record.getId(), EnumSamplingStatus.已经完成取样.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
            List<DtoSample> sampleList = this.findLocalSampleByReceiveId(record.getId());
            if (sampleList.size() > 0) {//修改样品状态为在检并纠正样品状态
                sampleRepository.updateSampleStatus(sampleList.stream().map(DtoSample::getId).collect(Collectors.toList()),
                        EnumSamplingStatus.已经完成取样.getValue(), EnumSampleStatus.样品在检.toString(),
                        PrincipalContextUser.getPrincipal().getUserId(), new Date());

                checkSampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                //TODO 需要注释纠正样品状态
                //proService.checkSamples(sampleList);
            } else {//纠正送样单状态
                proService.checkReceiveSampleRecord(Collections.singletonList(record.getId()));
            }

            //插入送样单提交日志
            this.createAuditLog(record, EnumLogOperateType.提交送样单.toString(), "submit", true, nextPerson, opinion);

            //插入提交记录
            submitRecordService.createSubmitRecord(record.getId(), EnumSubmitObjectType.送样单.getValue(), EnumSubmitType.数据录入提交.getValue(),
                    nextPerson, opinion, "", "");

            //缓存首页数据
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.现场任务.getValue(), EnumLIM.EnumHomeTaskModule.现场数据复核.getValue()
            );

            //若设置了自动跳过样品交接需进行跳过
            if (record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()) && proService.switchIsOpen(ProCodeHelper.SKIP_SAMPLE_RECEIVE)) {
                this.submitInnerSample(record.getId(), new Date(), UUIDHelper.GUID_EMPTY, "");
            }
        }

        return checkSampleIds;
    }

    /**
     * 样品交接提交
     *
     * @param id 送样单id
     */
    @Transactional
    public List<String> submitInnerSample(String id, Date receiveSampleDate, String recipientId, String opinion) {
        DtoReceiveSampleRecord record = repository.findOne(id);
        //获取实验室领样单
        DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.分析.getValue());
        //若送样单状态为新建且存在实验室领样单才进行交接操作
        if (record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()) && StringUtil.isNotNull(subRecord)) {
            // 分批接样时，接样日期不传，从分组信息里随机取一条日期
            if (record.getIsBatchReceive()) {
                List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findByReceiveId(id);
                Optional<DtoSampleGroup> sampleGroupOptional = sampleGroupList.stream().findFirst();
                if (sampleGroupOptional.isPresent()) {
                    // 替换接样人接样日期
                    receiveSampleDate = sampleGroupOptional.get().getReceiveSampleDate();
                    String personId = personRepository.findPersonIdByCName(sampleGroupOptional.get().getRecipientName());
                    recipientId = StringUtil.isNotEmpty(personId) ? personId : UUIDHelper.GUID_EMPTY;
                }
            } else {
                // 更新样品分组信息
                record.setRecipientId(recipientId);
                record.setReceiveSampleDate(receiveSampleDate);
                sampleGroupService.updateByReceiveInfo(record);
            }
            List<DtoReceiveSubSampleRecord2Sample> subRecord2SampleList = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(subRecord.getId());
            List<String> sampleIds = subRecord2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
            repository.sampleReceive(record.getId(), new Date(), EnumLIM.EnumReceiveRecordStatus.已经送样.toString(), EnumLIM.EnumReceiveRecordStatus.已经送样.getValue(),
                    PrincipalContextUser.getPrincipal().getUserId(), new Date(), receiveSampleDate, recipientId, opinion);
            //如果不存在实验室指标的话，不会流转到样品交接，故样品交接提交一定存在实验室指标，故为对应状态
            if (sampleIds.size() > 0) {
                sampleRepository.updateSampleStatus(sampleIds, EnumInnerReceiveStatus.可以领取.getValue(), EnumAnalyzeStatus.可以分析.getValue(), EnumSamplingStatus.已经完成取样.getValue(),
                        EnumSampleStatus.样品未领样.toString(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }
            //样品交接状态置为已处理
            statusForRecordService.completeStatus(record.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());

            //纠正送样单状态
            proService.checkReceiveSampleRecord(Collections.singletonList(record.getId()));

            //插入送样单日志
            String comment = String.format("提交了送样单%s。", record.getRecordCode());
            newLogService.createLog(record.getId(), comment,
                    "", EnumLogType.送样单流程.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.提交送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

            //插入提交记录
            submitRecordService.createSubmitRecord(record.getId(), EnumSubmitObjectType.送样单.getValue(), EnumSubmitType.样品交接提交.getValue(),
                    "", "", "", "");

            //缓存首页数据
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.样品交接.getValue(), EnumLIM.EnumHomeTaskModule.样品分配.getValue()
            );

            //异步修改数据上的要求完成时间（只有样品交接的时候才会去改分包的最后的数据状态）
            updateRequireDeadLineByReceiveId(record.getId(), PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getOrgId(), true);
        }
        //自动提交样品分配
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
                        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
                            //按岗位分配开关开启时，跳过样品分配
                            receiveSubSampleRecordService.assign(Collections.singletonList(subRecord.getId()));
                        } else {
                            if (autoSubmitSampleArrangeInd() && StringUtil.isNotNull(subRecord)) {
                                autoSubmitSampleArrange(subRecord.getId());
                            }
                        }
                    }
                });
        return new ArrayList<>();
    }

    @Transactional
    @Override
    public void reloadRecordJsonByIds(List<String> receiveIds) {
        List<DtoReceiveSampleRecord> recordList = repository.findAll(receiveIds);
        List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(receiveIds);
        recordList.forEach(record -> {
            List<DtoSample> samList = sampleList.stream().filter(p -> record.getId().equals(p.getReceiveId())).collect(Collectors.toList());
            if (samList.size() > 0) {
                Set<String> samTypeIds = samList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toSet());
                DtoRecordJson json = record.getJson() != null
                        ? JsonIterator.deserialize(record.getJson(), DtoRecordJson.class)
                        : new DtoRecordJson();
                json.setSampleTypeIds(String.join(",", samTypeIds));
                record.setJson(JsonStream.serialize(json));
            }
        });
        repository.save(recordList);
    }

    /**
     * 自动提交样品分配
     *
     * @param subId 领样单id
     */
    @Transactional
    public void autoSubmitSampleArrange(String subId) {
        DtoSampleAssignTemp dtoSampleAssignTemp = receiveSubSampleRecordService.findSampleAssignInfoById(subId, Boolean.TRUE);
        List<String> testIdList = new ArrayList<>();
        List<Map<String, Object>> tests = dtoSampleAssignTemp.getTest();
        for (Map<String, Object> obj : tests) {
            if (StringUtil.isNotNull(obj.get("id"))) {
                testIdList.add(obj.get("id").toString());
            }
        }
        //找出领样单中默认的分配分析人员人员
        List<Map<String, Object>> personMap = dtoSampleAssignTemp.getPerson();
        if (StringUtil.isNotEmpty(personMap) && StringUtil.isNotEmpty(testIdList)) {
            for (Map<String, Object> map : personMap) {
                for (String testId : testIdList) {
                    if (StringUtil.isNotNull(map.get(testId))) {
                        Map<String, Object> anaObject = (Map<String, Object>) map.get(testId);
                        Object analystId = anaObject.get("analystId");
                        if (StringUtil.isNull(analystId) || "".equals(analystId.toString())
                                || UUIDHelper.GUID_EMPTY.equals(analystId.toString())) {
                            return;
                        }
                    }
                }
            }
        }
        receiveSubSampleRecordService.assign(Collections.singletonList(subId));
    }

    /**
     * 是否自动提交样品分配，默认不自动
     *
     * @return 布尔值, true：自动提交；false: 不自动提交
     */
    protected boolean autoSubmitSampleArrangeInd() {
        DtoCode codeStr = codeService.findByCode(ProCodeHelper.SKIP_SAMPLE_RECEIVE);
        return StringUtil.isNotNull(codeStr) && codeStr.getDictValue().equals("1");
    }

    /**
     * 质控送样提交
     *
     * @param id 送样单id
     */
    @Transactional
    public List<String> submitQMSample(String id, Date receiveSampleDate) {
        DtoReceiveSampleRecord record = repository.findOne(id);
        DtoProject dtoProject = projectRepository.findOne(record.getProjectId());
        DtoProjectType dtoProjectType = projectTypeService.findOne(dtoProject.getProjectTypeId());
        //是否不编制方案标记
        boolean withoutSolutionIndicator = Boolean.FALSE;
        if (EnumWorkflowCode.质控项目无方案.getValue().equals(dtoProjectType.getWorkflowId())) {
            withoutSolutionIndicator = Boolean.TRUE;
        }
        //若送样单状态为新建且存在实验室领样单才进行交接操作
        if (EnumLIM.EnumReceiveRecordStatus.新建.getValue().equals(record.getReceiveStatus())) {
            List<DtoSample> sampleList = sampleRepository.findByReceiveId(record.getId());
            repository.sampleReceive(record.getId(), new Date(), EnumLIM.EnumReceiveRecordStatus.已经送样.toString(), EnumLIM.EnumReceiveRecordStatus.已经送样.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date(), receiveSampleDate);
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());

            //如果不存在实验室指标的话，不会流转到样品交接，故样品交接提交一定存在实验室指标，故为对应状态
            if (!withoutSolutionIndicator) {
                sampleRepository.updateSampleStatus(sampleIds, EnumInnerReceiveStatus.可以领取.getValue(), EnumAnalyzeStatus.可以分析.getValue(), EnumSamplingStatus.已经完成取样.getValue(),
                        EnumSampleStatus.样品未领样.toString(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }


            DtoProject project = projectRepository.findOne(record.getProjectId());
            if (project.getStatus().equals(EnumProjectStatus.项目登记中.toString())) {
                try {
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(record.getProjectId());
                    if (withoutSolutionIndicator) {
                        dtoWorkflowSign.setSignal("projectTestEnd");
                    } else {
                        dtoWorkflowSign.setSignal("projectLaunch");
                    }

                    dtoWorkflowSign.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    dtoWorkflowSign.setNextOperator("");
                    dtoWorkflowSign.setOption("");
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String status = workflowService.submitSign(dtoWorkflowSign);
                    if (StringUtils.isNotNullAndEmpty(status)) {
                        projectRepository.updateProjectStatus(Collections.singletonList(project.getId()), status, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        statusForQCProjectService.modifyStatus(EnumProjectStatus.项目登记中.toString(), status, dtoWorkflowSign, project);
                    }

                    //保证事物提交之后才执行
                    TransactionSynchronizationManager.registerSynchronization(
                            new TransactionSynchronizationAdapter() {
                                @Override
                                public void afterCommit() {
                                    newLogService.createProjectStatusUpdateLog(Collections.singletonList(project.getId()), "", UUIDHelper.GUID_EMPTY, "");
                                    if ("projectLaunch".equals(dtoWorkflowSign.getSignal())) {
                                        //环保企业通推送项目信息
                                        environmentEnterpriseService.pushProjectData(Collections.singletonList(project), EnumProjectStatus.开展中.name());
                                    }
                                }
                            }
                    );
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                    throw new BaseException("异常错误");
                }
            }

            if (!withoutSolutionIndicator) {
                //纠正送样单状态
                proService.checkReceiveSampleRecord(Collections.singletonList(record.getId()));

                //插入送样单日志
                String comment = String.format("提交了送样单%s。", record.getRecordCode());
                newLogService.createLog(record.getId(), comment,
                        "", EnumLogType.送样单流程.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.提交送样单.toString(),
                        PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

                //插入提交记录
                submitRecordService.createSubmitRecord(record.getId(), EnumSubmitObjectType.送样单.getValue(), EnumSubmitType.质控项目提交.getValue(),
                        "", "", "", "");

                //缓存首页数据
                homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getOrgId(),
                        EnumLIM.EnumHomeTaskModule.样品交接.getValue(), EnumLIM.EnumHomeTaskModule.样品分配.getValue()
                );

                //异步修改数据上的要求完成时间
                updateRequireDeadLineByReceiveId(record.getId(), PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getOrgId(), false);

                //质控任务（编方案已配置所有的样品的分析人并且开启了跳过分配开关）就跳过样品分配步骤
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                if (canSkipAssign(sampleIds)) {
                                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.分析.getValue());
                                    if (subRecord != null) {
                                        receiveSubSampleRecordService.assign(Collections.singletonList(subRecord.getId()));
                                    }
                                }
                            }
                        });
            }
        }
        return new ArrayList<>();
    }

    /**
     * 质控任务能否跳过样品分配
     *
     * @param sampleIds 样品标识列表
     * @return 判断结果
     */
    private boolean canSkipAssign(List<String> sampleIds) {
        List<DtoAnalyseData> list = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        int withoutAnalystCount = (int) list.stream().filter(d -> d.getAnalystId() == null || UUIDHelper.GUID_EMPTY.equals(d.getAnalystId())).count();
        return autoSubmitSampleArrangeInd() && withoutAnalystCount == 0;
    }

    /**
     * 提交送样单处理数据要求完成时间+默认人员
     *
     * @param id     送样单id
     * @param userId 人员id
     * @param orgId  组织机构id
     */
    @Transactional
//    @Async
    public void updateRequireDeadLineByReceiveId(String id, String userId, String orgId, boolean isPerson) {
        DtoReceiveSampleRecord record = repository.findOne(id);
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select a");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample s where 1=1");
        stringBuilder.append(" and a.sampleId = s.id");
        stringBuilder.append(" and s.receiveId = :receiveId");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and a.orgId = :orgId");
        stringBuilder.append(" and s.orgId = :orgId");
        values.put("orgId", orgId);
        values.put("receiveId", id);
        List<DtoAnalyseData> analyseDataList = comRepository.find(stringBuilder.toString(), values);
        Set<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        //获取所有样品
        List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));

        List<String> testIdList = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIdList);
        //将list转为map
        Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));
        //获取所有测试人员配置
        List<DtoPerson2Test> person2Tests = person2TestRepository.findByIsDefaultPersonTrueAndTestIdIn(testIdList);
        //放置人员名称
        this.fillPersonName(person2Tests);
        for (DtoAnalyseData analyseData : analyseDataList) {
            //去除修改接样日期修改样品有效期功能 BUG2025080800413
//            Calendar cal = Calendar.getInstance();
//            cal.setTime(record.getReceiveTime());
//            ConfigModel configModel = getDefaultDateConfig();
//            if (StringUtil.isNotNull(configModel)) {
//                cal.add(Calendar.DATE, Integer.parseInt(configModel.getConfigValue()));
//            }
//            Date requireDeadLine = cal.getTime();
//            if (testMap.containsKey(analyseData.getTestId())) {
//                DtoTest test = testMap.get(analyseData.getTestId());
//                if (StringUtil.isNotNull(test.getValidTime()) && test.getValidTime().compareTo(BigDecimal.valueOf(0)) > 0) {
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(record.getReceiveTime());
//                    calendar.add(Calendar.DATE, (int) Math.ceil(test.getValidTime().doubleValue() / 24));
//                    requireDeadLine = calendar.getTime();
//                }
//            }
            if (analyseData.getIsOutsourcing() || analyseData.getIsSamplingOut()) {
                analyseDataRepository.innerAnalyseStatus(EnumAnalyseDataStatus.已确认.getValue(), EnumAnalyseDataStatus.已确认.toString(), true,
                        analyseData.getAnalystId(), analyseData.getAnalystName(), userId, new Date(), analyseData.getId());
            } else if (!analyseData.getIsCompleteField()) {
                //获取对应的样品
                DtoSample sample = sampleMap.get(analyseData.getSampleId());
                //获取对应的测试项目
                DtoTest test = testMap.get(analyseData.getTestId());
                //获取检测类型小类id
                String sampleTypeId = StringUtil.isNotNull(sample) ? sample.getSampleTypeId() : "";
                //获取检测类型大类id
                String bigSampleTypeId = StringUtil.isNotNull(test) ? test.getSampleTypeId() : "";
                //获取测试项目id
                String testId = StringUtil.isNotNull(test) ? test.getId() : "";
                //根据小类查询默认分析人员
                DtoPerson2Test defaultPerson = person2Tests.stream().filter(p -> sampleTypeId.equals(p.getSampleTypeId()) && testId.equals(p.getTestId())).findFirst().orElse(null);
                //如果小类查询不到则查询大类默认人员配置
                if (StringUtil.isNull(defaultPerson)) {
                    defaultPerson = person2Tests.stream().filter(p -> bigSampleTypeId.equals(p.getSampleTypeId()) && testId.equals(p.getTestId())).findFirst().orElse(null);
                }
                String personId = analyseData.getAnalystId();
                String cName = analyseData.getAnalystName();
                //如果数据已添加工作单或者已确认，或者没有配置默认人员则，不设置默认分析人员
                if (!(StringUtil.isNull(defaultPerson) || !UUIDHelper.GUID_EMPTY.equals(analyseData.getWorkSheetFolderId()) ||
                        analyseData.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()) || !isPerson)) {
                    personId = defaultPerson.getPersonId();
                    cName = defaultPerson.getCName();
                }
                analyseDataRepository.innerAnalyseStatus(analyseData.getDataStatus(), analyseData.getStatus(), analyseData.getIsDataEnabled(),
                        personId, cName, userId, new Date(), analyseData.getId());
            }
        }
        updateRequireDeadLine(record, analyseDataList, testMap);
    }

    /**
     * 默认样品有效期
     *
     * @return 返回有效期
     */
    @Override
    public ConfigModel getDefaultDateConfig() {
        return configService.findConfig("sys.default.Date");
    }

    /**
     * 核查能否提交送样单
     *
     * @param ids  送样单id集合
     * @param type 提交类型
     */
    @Override
    public void canSubmitReceiveRecord(List<String> ids, String type) {
        List<DtoReceiveSampleRecord> records = repository.findAll(ids);
        Integer submitType = EnumReceiveSubmitType.getValueByCode(type);
        List<DtoSample> sampleList = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        List<DtoParamsData> paramsDataList = new ArrayList<>();
        List<DtoInstrumentUseRecord> useRecordList = new ArrayList<>();
        List<DtoEnvironmentalRecord> environmentalRecordList = new ArrayList<>();
        if ((submitType & EnumReceiveCheckType.核查样品数据.getValue()) > 0) {
            //读取送样单下的样品
            sampleList = sampleRepository.findByReceiveIdIn(ids);
            //读取送样单下的样品参数数据、样品分析数据
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            analyseDataList = sampleIds.size() > 0 ? analyseDataRepository.findBySampleIdIn(sampleIds) : new ArrayList<>();
            analyseDataList = analyseDataList.stream().filter(p -> !(p.getIsDeleted() && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.作废.getValue()))).collect(Collectors.toList());
            //读取送样单下的现场领样单，及过滤出来的现场分析仪器使用记录
            List<DtoReceiveSubSampleRecord> subRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(ids);
            subRecords = subRecords.stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0).collect(Collectors.toList());
            //获取领样单id与送样单id的关联
            Map<String, String> subMap = subRecords.stream().collect(Collectors.toMap(DtoReceiveSubSampleRecord::getId, DtoReceiveSubSampleRecord::getReceiveId));

            useRecordList = subMap.size() > 0 ? instrumentUseRecordService.findByObjectIdInAndObjectType(new ArrayList<>(subMap.keySet()), EnumLIM.EnumEnvRecObjType.现场分析.getValue())
                    : new ArrayList<>();
            //现场采样仪器也放进去
            useRecordList.addAll(instrumentUseRecordService.findByObjectIdInAndObjectType(ids, EnumLIM.EnumInsUseObjType.采样.getValue()));
            if (!useRecordList.isEmpty()) {
                List<String> environmentalRecordIdList = useRecordList.stream().map(DtoInstrumentUseRecord::getEnvironmentalManageId).distinct().collect(Collectors.toList());
                environmentalRecordList = environmentalRecordRepository.findAll(environmentalRecordIdList);
            }
            //将仪器使用记录的领样单关联id改为送样单id
            for (DtoInstrumentUseRecord useRecord : useRecordList) {
                useRecord.setObjectId(subMap.getOrDefault(useRecord.getObjectId(), ""));
            }
        }

        if ((submitType & EnumReceiveCheckType.核查参数数据.getValue()) > 0) {
            List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            paramsDataList = paramsDataService.findBySampleIds(sampleIdList);
        }

        if ((submitType & EnumReceiveCheckType.核查送样人员.getValue()) > 0) {
            this.checkSender(records);
        }
        if ((submitType & EnumReceiveCheckType.核查样品数据.getValue()) > 0) {
            this.checkSample(records, sampleList, analyseDataList);
        }
        if ((submitType & EnumReceiveCheckType.核查环境记录.getValue()) > 0) {
            this.checkInstrumentUseRecord(records, sampleList, analyseDataList, useRecordList, environmentalRecordList);
        }
        if ((submitType & EnumReceiveCheckType.核查参数数据.getValue()) > 0) {
            String msg = this.checkParamsDataBySampleType(records, paramsDataList, analyseDataList, sampleList, true);
            if (StringUtils.isNotNullAndEmpty(msg)) {
                throw new BaseException(msg);
            }
        }
        if ((submitType & EnumReceiveCheckType.核查现场数据.getValue()) > 0) {
            this.checkLocalData(records, sampleList, analyseDataList);
        }
        if ((submitType & EnumReceiveCheckType.核查分析人员.getValue()) > 0 && !EnumReceiveSubmitType.质控项目提交.getCode().equals(type)) {
            this.checkAnalystId(records, sampleList, analyseDataList);
        }
    }


    /**
     * 修改样品采样时间
     *
     * @param record     送样单
     * @param dto        修改送样单
     * @param sampleList 样品集合
     */
    protected void updateSamplingTime(DtoReceiveSampleRecord record, DtoReceiveSampleRecord dto, List<DtoSample> sampleList) {
        if (!record.getSamplingTime().equals(dto.getSamplingTime())) {
            //采样类任务才修改样品的采样时间
            if (record.getReceiveType().equals(EnumReceiveType.内部送样.getValue())) {
                sampleList.forEach(p -> {
                    p.setSamplingTimeBegin(dto.getSamplingTime());
                });
                sampleRepository.save(sampleList);
            }
            //更新样品有效期
            updateRequireDeadLine(dto, sampleList);
        }
    }

    /**
     * 更新样品有效期
     *
     * @param dto        送样单
     * @param sampleList 样品数据
     */
    private void updateRequireDeadLine(DtoReceiveSampleRecord dto, List<DtoSample> sampleList) {
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> allTests = StringUtil.isNotEmpty(testIds) ? testService.findAll(testIds) : new ArrayList<>();
            Map<String, DtoTest> testMap = allTests.stream().collect(Collectors.toMap(DtoTest::getId, t -> t));
            Calendar cal = Calendar.getInstance();
            analyseDataList.forEach(a -> {
                DtoTest test = testMap.getOrDefault(a.getTestId(), null);
                if (StringUtil.isNotNull(test) && StringUtil.isNotNull(test.getValidTime()) && test.getValidTime().compareTo(BigDecimal.valueOf(0)) > 0) {
                    cal.clear();
                    if (StringUtil.isNotNull(dto.getSamplingTime())) {
                        cal.setTime(dto.getSamplingTime());
                    } else {
                        cal.setTime(new Date());
                    }
                    cal.add(Calendar.DATE, (int) Math.ceil(test.getValidTime().doubleValue() / 24));
                    a.setRequireDeadLine(cal.getTime());
                }
                if (a.getIsCompleteField()){
                    a.setAnalyzeTime(dto.getSamplingTime());
                }
            });
            updateRequireDeadLine(dto, analyseDataList, testMap);
            analyseDataRepository.save(analyseDataList);
        }
    }

    /**
     * 核查能否提交送样单
     *
     * @param id   送样单id（项目登记那是项目id）
     * @param type 提交类型
     */
    @Override
    public void canSubmitReceiveRecord(String id, String type) {
        DtoReceiveSampleRecord record = repository.findOne(id);
        Integer submitType = EnumReceiveSubmitType.getValueByCode(type);
        Boolean flag = (submitType & EnumReceiveCheckType.核查样品数据.getValue()) > 0;
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(record.getId());
        DtoReceiveSubSampleRecord subRecord = flag ? receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.现场.getValue()) : null;
        List<String> sampleIdList = StringUtil.isNotNull(sampleList) ? sampleList.stream().map(DtoSample::getId).collect(Collectors.toList()) : new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = (flag && sampleIdList.size() > 0) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList) : new ArrayList<>();

        List<DtoParamsData> paramsDataList = new ArrayList<>();
        if ((submitType & EnumReceiveCheckType.核查参数数据.getValue()) > 0) {
            paramsDataList = paramsDataService.findBySampleIds(sampleIdList);
        }

        if ((submitType & EnumReceiveCheckType.核查送样人员.getValue()) > 0) {
            this.checkSenderSingle(record, false);
        }
        if ((submitType & EnumReceiveCheckType.核查样品数据.getValue()) > 0) {
            this.checkSampleSingle(sampleList, analyseDataList, false);
        }
        if (StringUtil.isNotNull(subRecord) && (submitType & EnumReceiveCheckType.核查环境记录.getValue()) > 0) {
            List<String> testIds = analyseDataList.stream().filter(DtoAnalyseData::getIsCompleteField).map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> testList = testService.findRedisByIds(testIds);
            List<DtoInstrumentUseRecord> useRecordList = instrumentUseRecordService.findByObjectIdAndObjectType(subRecord.getId(), EnumLIM.EnumEnvRecObjType.现场分析.getValue());
            List<String> environmentalRecordIdList = useRecordList.stream().map(DtoInstrumentUseRecord::getEnvironmentalManageId).distinct().collect(Collectors.toList());
            List<DtoEnvironmentalRecord> environmentalRecordList = environmentalRecordRepository.findAll(environmentalRecordIdList);
            this.checkInstrumentUseRecordSingle(testList, useRecordList, environmentalRecordList, false);
        }
        if ((submitType & EnumReceiveCheckType.核查参数数据.getValue()) > 0) {
            String msg = this.checkParamsDataBySampleType(Collections.singletonList(record), paramsDataList, analyseDataList, sampleList, false);
            if (StringUtils.isNotNullAndEmpty(msg)) {
                throw new BaseException(msg);
            }
        }
        if (StringUtil.isNotNull(subRecord) && (submitType & EnumReceiveCheckType.核查现场数据.getValue()) > 0) {
            this.checkLocalDataSingle(analyseDataList, false);
        }
        if ((submitType & EnumReceiveCheckType.核查分析人员.getValue()) > 0) {
            this.checkAnalystIdSingle(analyseDataList, false);
        }
    }

    /**
     * 核查送样人是否为当前人员
     *
     * @param records 送样单列表
     */
    private void checkSender(List<DtoReceiveSampleRecord> records) {
        List<String> recordCodes = new ArrayList<>();
        for (DtoReceiveSampleRecord record : records) {
            if (StringUtils.isNotNullAndEmpty(this.checkSenderSingle(record, true))) {
                recordCodes.add(record.getRecordCode());
            }
        }
        if (recordCodes.size() > 0) {
            String msg = records.size() == 1 ? "无法提交其他送样人的送样单！" : String.format("%s不是您的送样单，无法进行提交！", String.join(",", recordCodes));
            throw new BaseException(msg);
        }
    }

    /**
     * 核查送样人是否为当前人员
     *
     * @param record    送样单
     * @param returnMsg 是否返回消息
     */
    private String checkSenderSingle(DtoReceiveSampleRecord record, Boolean returnMsg) {
        String userId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (!record.getSenderId().equals(userId)) {
            String msg = "无法提交其他送样人的送样单！";
            if (returnMsg) {
                return msg;
            }
            throw new BaseException(msg);
        }
        return "";
    }

    /**
     * 核查送样单下是否有样品
     *
     * @param records         送样单列表
     * @param sampleList      样品列表
     * @param analyseDataList 分析数据列表
     */
    private void checkSample(List<DtoReceiveSampleRecord> records,
                             List<DtoSample> sampleList,
                             List<DtoAnalyseData> analyseDataList) {
        List<String> recordCodes = new ArrayList<>();
        for (DtoReceiveSampleRecord record : records) {
            List<DtoSample> samList = sampleList.stream().filter(p -> p.getReceiveId().equals(record.getId())
                    && !EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
            List<String> samIdList = samList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoAnalyseData> dataList = analyseDataList.stream().filter(p -> samIdList.contains(p.getSampleId())).collect(Collectors.toList());
            if (StringUtils.isNotNullAndEmpty(this.checkSampleSingle(samList, dataList, true))) {
                recordCodes.add(record.getRecordCode());
            }
        }
        if (recordCodes.size() > 0) {
            throw new BaseException(records.size() == 1 ?
                    "送样单中无样品或样品无数据！" :
                    String.format("送样单%s中无样品或样品无数据！", String.join(",", recordCodes)));
        }
    }

    /**
     * 核查送样单下是否有样品
     *
     * @param sampleList      样品列表
     * @param analyseDataList 分析数据列表
     * @param returnMsg       是否返回消息
     */
    private String checkSampleSingle(List<DtoSample> sampleList,
                                     List<DtoAnalyseData> analyseDataList,
                                     Boolean returnMsg) {
        Set<String> existSampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        if (StringUtil.isNull(sampleList) || sampleList.size() == 0 ||
                existSampleIds.size() < sampleList.size()) {
            String msg = "送样单中无样品！";
            if (existSampleIds.size() < sampleList.size()) {
                for (DtoSample sample : sampleList) {
                    if (!existSampleIds.contains(sample.getId())) {
                        msg = String.format("样品%s未配置测试项目，请确认！", sample.getCode());
                    }
                }
            }
            if (returnMsg) {
                return msg;
            }
            throw new BaseException(msg);
        }
        return "";
    }

    /**
     * 核查送样单下的参数数据是否填写
     *
     * @param paramsDataList 未填参数数据列表
     * @param sampleList     样品列表
     */
    private String checkParamsDataBySampleType(List<DtoReceiveSampleRecord> records,
                                               List<DtoParamsData> paramsDataList,
                                               List<DtoAnalyseData> analyseDataList,
                                               List<DtoSample> sampleList, Boolean isSimple) {
        List<String> receiveIds = records.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
        sampleList = sampleList.stream().filter(p -> !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());

        List<String> msgList = new ArrayList<>();
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        for (String sampleTypeId : sampleTypeIds) {//遍历检测类型，按检测类型处理
            //获取该检测类型下的所有配置
            //List<DtoParamsConfig> paramsConfigList = paramsConfigService.findBySampleTypeId(sampleTypeId, new ArrayList<>());//传空集合会返回所有的
            List<DtoParamsConfig> paramsConfigList = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
            //分析项目配置
            List<DtoParamsConfig> itemConfigs = paramsConfigList.stream().filter(p -> p.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())).collect(Collectors.toList());
            Map<String, DtoParamsConfig> itemCfgMap = itemConfigs.stream().collect(Collectors.toMap(DtoParamsConfig::getId, cfg -> cfg));

            //DtoParamsCheckTemp sourceTemp = this.findParamsCheckTemp(paramsConfigList);

            List<String> exceptIds = new ArrayList<>();//排除的送样单id，即存在未填写的单子
            for (String receiveId : receiveIds) {
                List<DtoSample> samples = sampleList.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId) && p.getReceiveId().equals(receiveId)).collect(Collectors.toList());
                if (samples.size() > 0) {
                    for (DtoSample sample : samples) {
                        List<String> paramsNames = new ArrayList<>();
                        DtoParamsCheckTemp temp = this.findParamsCheckTemp(paramsConfigList);//new DtoParamsCheckTemp(sourceTemp.getCfgMap().size(), sourceTemp.getItemMap().size());
                        Boolean[][] flagArr = temp.getFlagArr();
                        List<DtoParamsData> pdList = paramsDataList.stream().filter(p -> p.getObjectId().equals(sample.getId())).collect(Collectors.toList());

                        //获取该样品下没有的指标，将之全部置为false
                        List<String> analyseItemIds = new ArrayList<>(temp.getItemMap().keySet());
                        analyseItemIds.removeAll(analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())).map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList()));
                        for (String analyseItemId : analyseItemIds) {
                            if (!UUIDHelper.GUID_EMPTY.equals(analyseItemId)) {
                                for (Integer i = 0; i < temp.getCfgMap().size(); i++) {
                                    flagArr[i][temp.getItemMap().get(analyseItemId)] = false;
                                }
                            }
                        }

                        for (DtoParamsData pd : pdList) {//遍历参数数据，若参数为空且必填，则为true，其余情况均为false
                            if (pd.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY) && temp.getCfgMap().containsKey(pd.getParamsConfigId())) {
                                flagArr[temp.getCfgMap().get(pd.getParamsConfigId())][0] = !StringUtils.isNotNullAndEmpty(pd.getParamsValue()) &&
                                        flagArr[temp.getCfgMap().get(pd.getParamsConfigId())][0];
                            } else if (!pd.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY) && itemCfgMap.containsKey(pd.getParamsConfigId())) {
                                DtoParamsConfig pc = itemCfgMap.get(pd.getParamsConfigId());
                                flagArr[temp.getCfgMap().get(pc.getParentId())][temp.getItemMap().get(pc.getAnalyzeItemId())] =
                                        !StringUtils.isNotNullAndEmpty(pd.getParamsValue()) &&
                                                flagArr[temp.getCfgMap().get(pc.getParentId())][temp.getItemMap().get(pc.getAnalyzeItemId())];
                            }
                        }

                        for (Integer row = 0; row < temp.getCfgMap().size(); row++) {//遍历二维数组，值为true则表示未填写参数，多张单子的时候直接跳出并移除该送样单，单张单子需拼装返回值
                            for (Integer column = 0; column < temp.getItemMap().size(); column++) {
                                if (flagArr[row][column]) {
                                    if (isSimple) {
                                        exceptIds.add(receiveId);
                                        break;
                                    } else {
                                        paramsNames.add(temp.getParamsNameArr()[row][column]);
                                    }
                                }
                            }
                        }
                        if (exceptIds.contains(receiveId)) {
                            break;
                        }
                        if (paramsNames.size() > 0) {
                            String msg = String.format("%s参数未填:%s", sample.getCode(), String.join(",", paramsNames));
                            msgList.add(msg);
                        }
                    }
                }
            }
            receiveIds.removeAll(exceptIds);
        }

        if (records.size() != receiveIds.size()) {
            return String.format("送样单%s中存在未填写的参数！",
                    String.join(",", records.stream().filter(p -> !receiveIds.contains(p.getId())).map(DtoReceiveSampleRecord::getRecordCode).collect(Collectors.toList())));
        }
        if (msgList.size() > 0) {
            return String.join("</br>", msgList);
        }
        return "";
    }

    @Override
    public DtoParamsCheckTemp findParamsCheckTemp(List<DtoParamsConfig> paramsConfigList) {
        //父级配置为横坐标
        List<DtoParamsConfig> parentConfigs = paramsConfigList.stream().filter(p -> p.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
        Map<String, Integer> cfgMap = new HashMap<>();
        for (DtoParamsConfig cfg : parentConfigs) {
            cfgMap.put(cfg.getId(), cfgMap.size());
        }
        //指标为纵坐标
        List<String> analyseItemIds = paramsConfigList.stream().sorted(Comparator.comparing(DtoParamsConfig::getAnalyzeItemId)).map(DtoParamsConfig::getAnalyzeItemId).distinct().collect(Collectors.toList());
        Map<String, Integer> itemMap = new HashMap<>();
        for (String analyseItemId : analyseItemIds) {
            itemMap.put(analyseItemId, itemMap.size());
        }

        DtoParamsCheckTemp temp = new DtoParamsCheckTemp(cfgMap.size(), itemMap.size());
        temp.setCfgMap(cfgMap);
        temp.setItemMap(itemMap);

        Boolean[][] flagArr = temp.getFlagArr();
        String[][] paramsNameArr = temp.getParamsNameArr();
        for (DtoParamsConfig paramsConfig : paramsConfigList.stream().filter(p -> p.getIsRequired() && p.getIsShow()).collect(Collectors.toList())) {
            if (paramsConfig.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY)) {//根据上面的排序，若存在空id，则空id一定排在第一位（默认不存在空或null的情形）
                flagArr[cfgMap.get(paramsConfig.getId())][0] = true;
                paramsNameArr[cfgMap.get(paramsConfig.getId())][0] = paramsConfig.getAlias();
            } else {
                flagArr[cfgMap.get(paramsConfig.getParentId())][itemMap.get(paramsConfig.getAnalyzeItemId())] = true;
                paramsNameArr[cfgMap.get(paramsConfig.getParentId())][itemMap.get(paramsConfig.getAnalyzeItemId())] = paramsConfig.getAlias();
            }
        }

        temp.setFlagArr(flagArr);
        temp.setParamsNameArr(paramsNameArr);
        return temp;
    }

    /**
     * 核查送样单下的现场仪器使用记录是否填写
     *
     * @param records 送样单列表
     */
    private void checkInstrumentUseRecord(List<DtoReceiveSampleRecord> records,
                                          List<DtoSample> sampleList,
                                          List<DtoAnalyseData> analyseDataList,
                                          List<DtoInstrumentUseRecord> useRecordList,
                                          List<DtoEnvironmentalRecord> environmentalRecordList) {
        List<String> recordCodes = new ArrayList<>();
        //现场数据过滤分包的指标
        List<String> testIds = analyseDataList.stream().filter(p -> p.getIsCompleteField()
                && !p.getIsOutsourcing() && !p.getIsSamplingOut()).map(DtoAnalyseData::getTestId)
                .distinct().collect(Collectors.toList());
        if (testIds.size() > 0) {
            List<DtoTest> testList = testService.findRedisByIds(testIds).stream().filter(DtoTest::getIsInsUseRecord).collect(Collectors.toList());
            for (DtoReceiveSampleRecord record : records) {
                List<String> sampleIds = sampleList.stream().filter(p -> p.getReceiveId().equals(record.getId())).map(DtoSample::getId).collect(Collectors.toList());
                List<String> testIdList = analyseDataList.stream().filter(p -> sampleIds.contains(p.getSampleId()))
                        .map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
                String msg = this.checkInstrumentUseRecordSingle(testList.stream().filter(p -> testIdList.contains(p.getId())).collect(Collectors.toList()),
                        useRecordList.stream().filter(p -> p.getObjectId().equals(record.getId())).collect(Collectors.toList()), environmentalRecordList, true);
                if (StringUtils.isNotNullAndEmpty(msg)) {
                    recordCodes.add(record.getRecordCode());
                }
            }
        }
        if (recordCodes.size() > 0) {
            throw new BaseException(records.size() == 1 ?
                    "仪器使用记录未填写！" :
                    String.format("送样单%s中仪器使用记录未填写！", String.join(",", recordCodes)));
        }
    }

    /**
     * 核查送样单下的现场仪器使用记录是否填写
     *
     * @param testList      现场指标
     * @param useRecordList 仪器记录
     * @param returnMsg     是否返回消息
     */
    private String checkInstrumentUseRecordSingle(List<DtoTest> testList,
                                                  List<DtoInstrumentUseRecord> useRecordList,
                                                  List<DtoEnvironmentalRecord> environmentalRecordList,
                                                  Boolean returnMsg) {
        //List<String> useTestIdList = testList.stream().filter(DtoTest::getIsInsUseRecord).map(DtoTest::getId).collect(Collectors.toList());
        List<String> itemIds = testList.stream().filter(DtoTest::getIsInsUseRecord).map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        List<String> testIdList = useRecordList.stream().map(DtoInstrumentUseRecord::getTestIds).filter(StringUtils::isNotNullAndEmpty).collect(Collectors.toList());
        testIdList = Arrays.stream(String.join(",", testIdList).split(",")).distinct().collect(Collectors.toList());
        List<String> remItemIds = new ArrayList<>();
        if (testIdList.size() > 0) {
            remItemIds = testService.findAll(testIdList).stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        }
        //useTestIdList.removeAll(testIdList);
        itemIds.removeAll(remItemIds);
        //通过判断分析项目确定是否都已添加仪器设备

        if (itemIds.size() > 0) {
            String msg = "环境与设备信息未填写！";
            if (returnMsg) {
                return msg;
            }
            throw new BaseException(msg);
        }
        // 判断开始使用时间，结束使用时间是否填写
        List<String> environmentalrecordIdList = useRecordList.stream().map(DtoInstrumentUseRecord::getEnvironmentalManageId).distinct().collect(Collectors.toList());
        environmentalRecordList = environmentalRecordList.stream().filter(e -> environmentalrecordIdList.contains(e.getId())).collect(Collectors.toList());
        if (!environmentalrecordIdList.isEmpty()) {
            Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
            for (DtoEnvironmentalRecord environmentalRecord : environmentalRecordList) {
                if (EnumLIM.EnumEnvRecObjType.现场分析.getValue().equals(environmentalRecord.getObjectType()) &&
                        (environmentalRecord.getStartTime() == null || environmentalRecord.getEndTime() == null ||
                                date1753.compareTo(environmentalRecord.getStartTime()) == 0 ||
                                date1753.compareTo(environmentalRecord.getEndTime()) == 0)) {
                    String msg = "现场仪器使用记录未填写完整！";
                    if (returnMsg) {
                        return msg;
                    }
                    throw new BaseException(msg);
                }
            }
        }
        return "";
    }

    /**
     * 核查送样单下的现场数据是否填写
     *
     * @param records 送样单列表
     */
    private void checkLocalData(List<DtoReceiveSampleRecord> records,
                                List<DtoSample> sampleList,
                                List<DtoAnalyseData> analyseDataList) {
        List<String> recordCodes = new ArrayList<>();
        for (DtoReceiveSampleRecord record : records) {
            List<String> sampleIds = sampleList.stream().filter(p -> p.getReceiveId().equals(record.getId())).map(DtoSample::getId).collect(Collectors.toList());
            if (StringUtils.isNotNullAndEmpty(this.checkLocalDataSingle(
                    analyseDataList.stream().filter(p -> sampleIds.contains(p.getSampleId())).collect(Collectors.toList()), true))) {
                recordCodes.add(record.getRecordCode());
            }
        }
        if (recordCodes.size() > 0) {
            throw new BaseException(records.size() == 1 ?
                    "数据未填写完整！" :
                    String.format("送样单%s中数据未填写完整！", String.join(",", recordCodes)));
        }
    }

    /**
     * 核查送样单下的现场数据是否填写
     *
     * @param analyseDataList 分析数据
     * @param returnMsg       是否返回消息
     */
    private String checkLocalDataSingle(List<DtoAnalyseData> analyseDataList, Boolean returnMsg) {
        if (analyseDataList.stream().filter(p -> p.getIsCompleteField() && !p.getIsOutsourcing() && !p.getIsSamplingOut())
                .anyMatch(p -> !StringUtils.isNotNullAndEmpty(p.getTestOrignValue()) ||
                        !StringUtils.isNotNullAndEmpty(p.getTestValue()))) {
            String msg = "数据未填写完整！";
            if (returnMsg) {
                return msg;
            }
            throw new BaseException(msg);
        }
        return "";
    }

    /**
     * 核查送样单下的分析数据是否设置了分析人
     *
     * @param records 送样单列表
     */
    private void checkAnalystId(List<DtoReceiveSampleRecord> records,
                                List<DtoSample> sampleList,
                                List<DtoAnalyseData> analyseDataList) {
        List<String> recordCodes = new ArrayList<>();
        for (DtoReceiveSampleRecord record : records) {
            List<String> sampleIds = sampleList.stream().filter(p -> p.getReceiveId().equals(record.getId())).map(DtoSample::getId).collect(Collectors.toList());
            if (StringUtils.isNotNullAndEmpty(this.checkAnalystIdSingle(
                    analyseDataList.stream().filter(p -> sampleIds.contains(p.getSampleId())).collect(Collectors.toList()), true))) {
                recordCodes.add(record.getRecordCode());
            }
        }
        if (recordCodes.size() > 0) {
            throw new BaseException(records.size() == 1 ?
                    "存在未设置分析人的样品！" :
                    String.format("送样单%s中存在未设置分析人的样品！", String.join(",", recordCodes)));
        }
    }

    /**
     * 核查送样单下的分析数据是否设置了分析人
     *
     * @param analyseDataList 分析数据
     * @param returnMsg       是否返回消息
     */
    private String checkAnalystIdSingle(List<DtoAnalyseData> analyseDataList, Boolean returnMsg) {
        if (analyseDataList.stream().anyMatch(p -> !StringUtils.isNotNullAndEmpty(p.getAnalystId()) || UUIDHelper.GUID_EMPTY.equals(p.getAnalystId()))) {
            String msg = "存在未设置分析人的样品！";
            if (returnMsg) {
                return msg;
            }
            throw new BaseException(msg);
        }
        return "";
    }

    //#endregion

    //#region 送样单复核审核

    /**
     * 复核送样单
     *
     * @param receiveIds 送样单id集合
     * @param isPass     是否通过
     * @param opinion    意见
     */
    @Transactional
    @Override
    public List<String> checkRecords(List<String> receiveIds, Boolean isPass, String opinion) {
        if (!isRecordAudit()) {
            return recordAuditCheckRecords(receiveIds, isPass, opinion);
        } else {
            List<String> result = recordAuditCheckRecords(receiveIds, isPass, opinion);
            if (isPass) {
                return this.auditRecords(receiveIds, isPass, opinion, null, false);
            }
            return result;
        }
    }

    /**
     * 复核送样单
     *
     * @param receiveIds 送样单id集合
     * @param isPass     是否通过
     * @param opinion    意见
     */
    private List<String> recordAuditCheckRecords(List<String> receiveIds, Boolean isPass, String opinion) {
        List<String> checkSampleIds = new ArrayList<>();
        for (String receiveId : receiveIds) {
            checkSampleIds.addAll(this.checkRecord(receiveId, isPass, opinion));
        }
        return checkSampleIds;
    }

    /**
     * 复核送样单
     *
     * @param receiveId 送样单id
     * @param isPass    是否通过
     * @param opinion   意见
     */
    @Transactional
    public List<String> checkRecord(String receiveId, Boolean isPass, String opinion) {
        DtoReceiveSampleRecord record = repository.findOne(receiveId);
        List<String> checkSampleIds = new ArrayList<>();
        if (record.getInfoStatus().equals(EnumReceiveInfoStatus.信息复核中.getValue())) {
            //读取现场领样单
            DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(receiveId, EnumSubRecordType.现场.getValue());
            if (StringUtil.isNotNull(subRecord)) {
                //读取领样单下的现场数据并修改数据状态
                List<DtoAnalyseData> analyseDataList = receiveSubSampleRecordService.findLocalDataBySubId(subRecord.getId());
                analyseDataList = analyseDataList.stream().filter(p -> p.getDataStatus().equals(EnumAnalyseDataStatus.已测.getValue())).collect(Collectors.toList());
                if (analyseDataList.size() > 0) {
                    checkSampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                    //修改数据状态
                    this.updateLocalAnalyseStatus(analyseDataList, isPass ? EnumAnalyseDataStatus.复核通过.getValue() : EnumAnalyseDataStatus.拒绝.getValue(),
                            isPass ? EnumAnalyseDataStatus.复核通过.toString() : EnumAnalyseDataStatus.拒绝.toString(), false);
                    comRepository.clear();
                }

                //修改领样单状态
                if (isPass) {
                    receiveSubSampleRecordRepository.checkLocalSubRecord(Collections.singletonList(subRecord.getId()),
                            EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue()
                                    | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue(),
                            EnumReceiveSubRecordStatusName.待数据确认.toString(), PrincipalContextUser.getPrincipal().getUserId(),
                            PrincipalContextUser.getPrincipal().getUserName(), new Date());
                } else {
                    receiveSubSampleRecordRepository.updateStatus(Collections.singletonList(subRecord.getId()), EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue(), EnumReceiveSubRecordStatusName.测试中.toString());
                }
            }

            //修改送样单状态
            DtoReceiveSampleRecord dtoRecord = new DtoReceiveSampleRecord();
            BeanUtils.copyProperties(record, dtoRecord);
            dtoRecord.setInfoStatus(isPass ? EnumReceiveInfoStatus.信息审核中.getValue() : EnumReceiveInfoStatus.信息登记中.getValue());
            if (isPass) {
                repository.checkPassRecordInfo(Collections.singletonList(record.getId()), EnumReceiveInfoStatus.信息审核中.getValue(),
                        EnumReceiveUploadStatus.已数据同步.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                //审核的签名
                this.signRecord(receiveId);
//                try {
//                    signatureService.sig(receiveId, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.审核人员_日期.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
//                } catch (Exception ex) {
//                    throw new BaseException(ex.getMessage());
//                }
            } else {
                repository.refuseRecordInfo(Collections.singletonList(record.getId()), EnumReceiveInfoStatus.信息登记中.getValue(),
                        EnumReceiveUploadStatus.未提交.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                //获取移除签名Map
                Map<String, Integer> mapType = new HashMap<>();
                List<String> perIds = samplingPersonConfigRepository.findByObjectTypeAndObjectId(EnumPRO.EnumSamplingType.送样单.getValue(), record.getId())
                        .stream().map(DtoSamplingPersonConfig::getSamplingPersonId).distinct().collect(Collectors.toList());
                //处理移除签名的标识
                dealMapType(mapType, perIds.size());
//                super.setPic(designer, "", SqEnumPRO.EnumSigType.送样日期.getName(), SqEnumPRO.EnumSigType.送样日期.getName(), isChenge, isEnd, signIndex, timeStr);
//                super.setPic(designer, "", SqEnumPRO.EnumSigType.送样时间.getName(), SqEnumPRO.EnumSigType.送样时间.getName(), isChenge, isEnd, signIndex, timeStr);
                signatureService.removeWorkSheetSig(record.getId(), mapType);
            }
            statusForRecordService.modifyStatus(dtoRecord, UUIDHelper.GUID_EMPTY, "", opinion);

            //刷新首页缓存
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.现场数据复核.toString(),
                    isPass ? EnumLIM.EnumHomeTaskModule.现场数据审核.toString() : EnumLIM.EnumHomeTaskModule.现场任务.toString()
            );

//            if (checkSampleIds.size() > 0) {
//                comRepository.clear();
//                //TODO 之后注释
//                proService.checkSample(checkSampleIds);
//            }
        }
        this.createAuditLog(record, EnumLogOperateType.更新送样单状态.toString(), "check", isPass, "", opinion);

        return checkSampleIds;
    }

    /**
     * 处理签名移除标识Map
     *
     * @param mapType 移除签名标识Map
     * @param size    采样人数量
     */
    protected void dealMapType(Map<String, Integer> mapType, Integer size) {
        Integer removeSamplingPersonSize = size > 3 ? 3 : size - 1;
        //采样人
        mapType.put(EnumSigType.测试人员.getName(), removeSamplingPersonSize);
        mapType.put(EnumSigType.采样人.getName(), removeSamplingPersonSize);
        mapType.put(EnumSigType.检测人员.getName(), removeSamplingPersonSize);
        mapType.put(EnumSigType.监测人员.getName(), removeSamplingPersonSize);
        //单个签名类型
        mapType.put(EnumSigType.陪同人.getName(), 1);
        mapType.put(EnumSigType.分析人.getName(), 1);
        mapType.put(EnumSigType.校核人.getName(), 1);
        mapType.put(EnumSigType.审核人.getName(), 1);
        mapType.put(EnumSigType.送样者.getName(), 1);
        //日期类型
        mapType.put(EnumSigType.采样日期.getName(), 1);
        mapType.put(EnumSigType.分析日期.getName(), 1);
        mapType.put(EnumSigType.监测日期.getName(), 1);
        mapType.put(EnumSigType.检测日期.getName(), 1);
        mapType.put(EnumSigType.送样时间.getName(), 1);
        mapType.put(EnumSigType.送样日期.getName(), 1);
    }

    /**
     * 审核送样单
     *
     * @param receiveIds 送样单id集合
     * @param isPass     是否通过
     * @param opinion    意见
     * @param isReport   是否编制报告
     */
    @Transactional
    @Override
    public List<String> auditRecords(List<String> receiveIds, Boolean isPass, String opinion, String type, Boolean isReport) {
        List<String> checkSampleIds = new ArrayList<>();
        if (isReport == null) {
            isReport = true;
        }
        if (reportDetailService.isBackSampleByReceiveIds(receiveIds, isPass) && isReport) {
            throw new BaseException("样品已关联出具报告，请联系编制报告人进行退回！");
        }

        // 审核完成后，先进行数据标记，需要根据状态处理
        this.markersData(receiveIds, isPass);

        for (String receiveId : receiveIds) {
            checkSampleIds.addAll(this.auditRecord(receiveId, isPass, opinion, type));
        }
        repository.findAll(receiveIds).stream().map(DtoReceiveSampleRecord::getProjectId).distinct()
                .forEach(v -> proService.sendProMessage(EnumProAction.状态纠正, v));
        return checkSampleIds;
    }

    /**
     * 数据标记
     *
     * @param receiveIds 送样单ids
     * @param isPass     是否通过
     */
    private void markersData(List<String> receiveIds, Boolean isPass) {
        if (StringUtil.isNotEmpty(receiveIds)) {
            if (!isPass) {
                // 送样单状态为已确认，退回时才会标记数据
                List<DtoReceiveSampleRecord> receiveSampleRecords = repository.findAll(receiveIds);
                List<String> receiveSampleIds = receiveSampleRecords.stream().filter(p -> p.getInfoStatus().equals(EnumReceiveInfoStatus.已确认.getValue()))
                        .map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
                markersDataService.updateValidate(new DtoMarkersData(receiveSampleIds, false));
            } else {
                // 查询所有的采样单，只有包含采样单附件的数据才会标记
                List<DtoDocument> dtoDocuments = documentRepository.findByFolderIdInAndDocTypeIdAndIsDeletedFalseOrderByCreateDateDesc(receiveIds, BaseCodeHelper.DOCUMENT_SAMPLE_RECORD);
                List<String> receiveIdList = dtoDocuments.stream().map(DtoDocument::getFolderId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(receiveIdList)) {
                    // 审核通过，更新测试项目验证
                    markersDataService.updateValidate(new DtoMarkersData(receiveIdList, true));
                }
            }
        }
    }

    /**
     * 审核送样单
     *
     * @param receiveId 送样单id
     * @param isPass    是否通过
     * @param opinion   意见
     */
    @Transactional
    public List<String> auditRecord(String receiveId, Boolean isPass, String opinion, String type) {
        List<String> checkSampleIds = new ArrayList<>();
        DtoReceiveSampleRecord record = repository.findOne(receiveId);
        boolean isRecord = false;
        if (!isRecordAudit()) {
            if (record.getInfoStatus().equals(EnumReceiveInfoStatus.信息审核中.getValue()) || record.getInfoStatus().equals(EnumReceiveInfoStatus.已确认.getValue())) {
                isRecord = true;
            }
        } else {
            isRecord = true;
        }
        if (isRecord) {
            DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(receiveId, EnumSubRecordType.现场.getValue());
            List<DtoAnalyseData> analyseDataList = new ArrayList<>();
            if (StringUtil.isNotNull(subRecord)) {
                analyseDataList = receiveSubSampleRecordService.findLocalDataBySubId(subRecord.getId());
                if (!(StringUtils.isNotNullAndEmpty(type) && BaseCodeHelper.RECEIVESAMPLERECORD_BACK_TYPE.equals(type))) {
                    analyseDataList = analyseDataList.stream().filter(p -> !p.getIsDataEnabled()).collect(Collectors.toList());
                }
                if (!isPass) {
                    analyseDataService.judgeIsBackAnaData(analyseDataList, false);
                }
                //修改数据状态
                this.updateLocalAnalyseStatus(analyseDataList, isPass ? EnumAnalyseDataStatus.复核通过.getValue() : EnumAnalyseDataStatus.拒绝.getValue(),
                        isPass ? EnumAnalyseDataStatus.复核通过.toString() : EnumAnalyseDataStatus.拒绝.toString(), isPass);
                comRepository.clear();
                if (isPass) {
                    receiveSubSampleRecordRepository.auditLocalSubRecord(Collections.singletonList(subRecord.getId()),
                            EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()
                                    | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue()
                                    | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()
                                    | EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue(),
                            EnumReceiveSubRecordStatusName.已经确认.toString(), PrincipalContextUser.getPrincipal().getUserId(),
                            PrincipalContextUser.getPrincipal().getUserName(), new Date());
                } else {
                    receiveSubSampleRecordRepository.updateStatus(Collections.singletonList(subRecord.getId()),
                            EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue(),
                            EnumReceiveSubRecordStatusName.测试中.toString());
                }
            }

            //避免已经确认的再进行通过，这种情况下不改送样单状态表
            if (!(record.getInfoStatus().equals(EnumReceiveInfoStatus.已确认.getValue()) && isPass)) {
                DtoReceiveSampleRecord dtoRecord = new DtoReceiveSampleRecord();
                BeanUtils.copyProperties(record, dtoRecord);
                dtoRecord.setInfoStatus(isPass ? EnumReceiveInfoStatus.已确认.getValue() : EnumReceiveInfoStatus.信息登记中.getValue());
                statusForRecordService.modifyStatus(dtoRecord, UUIDHelper.GUID_EMPTY, "", opinion);
            }
            if (isPass) {
                //修改送样单状态
                repository.auditPassRecordInfo(Collections.singletonList(record.getId()), EnumReceiveInfoStatus.已确认.getValue(),
                        EnumReceiveUploadStatus.已数据同步.getValue(), PrincipalContextUser.getPrincipal().getUserId(),
                        new Date(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                //复核的签名
                this.signRecord(receiveId);
//                try {
//                    signatureService.sig(receiveId, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.校核人员_日期.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
//                } catch (Exception ex) {
//                    throw new BaseException(ex.getMessage());
//                }
                //审核送样单记录相应的工作量绩效
                performanceStatisticForLocalDataService.createLocalAnalyseStatistic(record, analyseDataList, PrincipalContextUser.getPrincipal());
                performanceStatisticForSampleDataService.createSampleStatistic(record, PrincipalContextUser.getPrincipal());
            } else {
                //修改送样单状态
                repository.refuseRecordInfo(Collections.singletonList(record.getId()), EnumReceiveInfoStatus.信息登记中.getValue(),
                        EnumReceiveUploadStatus.未提交.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                List<String> sampleIdList = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                //修改样品状态
                if (StringUtil.isNotEmpty(sampleIdList)) {
                    sampleRepository.updateSampleStatus(sampleIdList, EnumSampleStatus.样品在检.name(), EnumAnalyzeStatus.正在分析.getValue(),
                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }
                List<String> perIds = samplingPersonConfigRepository.findByObjectTypeAndObjectId(EnumPRO.EnumSamplingType.送样单.getValue(), record.getId())
                        .stream().map(DtoSamplingPersonConfig::getSamplingPersonId).distinct().collect(Collectors.toList());
                //移除签名
                Map<String, Integer> mapType = new HashMap<>();
                dealMapType(mapType, perIds.size());
                mapType.put(EnumPRO.EnumSigType.校核人员_日期.getName(), 1);
                signatureService.removeWorkSheetSig(record.getId(), mapType);
            }

            //刷新首页缓存
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.现场数据审核.toString(),
                    isPass ? "" : EnumLIM.EnumHomeTaskModule.现场任务.toString());

            checkSampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
//            if (checkSampleIds.size() > 0) {
//                comRepository.clear();
//                //TODO 之后需要注释
//                proService.checkSample(checkSampleIds);
//            }
            if (isPass) {
                this.createAuditDataLog(analyseDataList);
            }
        }
        this.createAuditLog(record, EnumLogOperateType.更新送样单状态.toString(), "audit", isPass, "", opinion);


        return checkSampleIds;
    }

    /**
     * 创建审核通过的数据日志
     *
     * @param analyseDataList 现场测试项目数据
     */
    private void createAuditDataLog(List<DtoAnalyseData> analyseDataList) {
        List<DtoLog> anaDataLogs = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            String comment = String.format("数据审核通过：审核时数据为%s", analyseData.getTestValue());
            DtoLog dataLog = new DtoLog();
            dataLog.setRemark(analyseData.getTestValue());
            dataLog.setComment(comment);
            dataLog.setLogType(EnumPRO.EnumLogType.数据审核.getValue());
            dataLog.setObjectId(analyseData.getId());
            dataLog.setObjectType(EnumPRO.EnumLogObjectType.数据.getValue());
            dataLog.setOperateInfo(EnumPRO.EnumLogOperateType.审核数据.name());
            dataLog.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            dataLog.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            anaDataLogs.add(dataLog);
        }
        //保存审核的数据日志
        if (StringUtil.isNotEmpty(anaDataLogs)) {
            newLogService.createLog(anaDataLogs, EnumPRO.EnumLogType.数据审核.getValue());
        }
    }

    @Override
    public DtoReceiveSampleRecord findAttachPath(String id) {
        return repository.findOne(id);
    }

    /**
     * 添加审核操作日志
     *
     * @param record         送样单
     * @param logOperateType 类型
     * @param action         动作
     * @param isPass         是否通过
     * @param nextPerson     下一步操作人
     * @param opinion        意见
     */
    protected void createAuditLog(DtoReceiveSampleRecord record,
                                  String logOperateType,
                                  String action,
                                  Boolean isPass,
                                  String nextPerson,
                                  String opinion) {

        List<DtoDocument> documentList = new ArrayList<>();
        //送样单当前状态
        Integer currentStatus = record.getInfoStatus();

        //#region文档日志关联
        //暂时注释，需要的时候放出来
//        PageBean<DtoDocument> pageBean = new PageBean<>();
//        pageBean.setRowsPerPage(Integer.MAX_VALUE);
//        DocumentCriteria criteria = new DocumentCriteria();
//        if (recordList.size() > 1) {
//            criteria.setFolderIds(recordList.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList()));
//        } else {
//            criteria.setFolderId(recordList.get(0).getId());
//        }
//        criteria.setDocTypeId(BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD);
//        documentService.findByPage(pageBean, criteria);
//        if (StringUtil.isNotNull(pageBean.getData())) {
//            documentList = pageBean.getData();
//        }
        //#endregion

        List<DtoLog> logList = new ArrayList<>();
        List<DtoDocument2Log> d2lList = new ArrayList<>();

        String comment = "";
        if (action.equals("submit")) {
            comment = String.format("提交了送样单%s%s%s。", record.getRecordCode(),
                    StringUtils.isNotNullAndEmpty(nextPerson) ? "，复核人为：" + nextPerson : "",
                    StringUtils.isNotNullAndEmpty(opinion) ? "，意见为：" + opinion : "");
        } else if (action.equals("check")) {
            comment = String.format("复核了送样单%s,结果：%s,意见：%s。", record.getRecordCode(),
                    isPass ? "通过" : "不通过",
                    opinion);
        } else if (EnumReceiveInfoStatus.已确认.getValue().equals(currentStatus) && !isPass) {
            //现场任务审核后退回
            comment = String.format("退回了送样单%s,意见：%s。", record.getRecordCode(), opinion);
        } else {
            comment = String.format("审核了送样单%s,结果：%s,意见：%s。", record.getRecordCode(),
                    isPass ? "通过" : "不通过",
                    opinion);
        }
        DtoLog log = new DtoLog();
        log.setId(UUIDHelper.NewID());
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
        log.setOperateTime(new Date());
        log.setOperateInfo(logOperateType);
        log.setLogType(EnumLogType.送样单流程.getValue());
        log.setObjectId(record.getId());
        log.setObjectType(EnumLogObjectType.送样单.getValue());
        log.setComment(comment);
        log.setOpinion(opinion);
        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        log.setRemark("");
        logList.add(log);
        for (DtoDocument doc : documentList) {
            DtoDocument2Log d2l = new DtoDocument2Log();
            d2l.setDocumentId(doc.getId());
            d2l.setLogId(log.getId());
            d2lList.add(d2l);
        }

        newLogService.createLog(logList, EnumLogType.送样单流程.getValue());
        if (d2lList.size() > 0) {
            document2LogRepository.save(d2lList);
        }
    }
    //#endregion

    /**
     * 复制送样单
     *
     * @param oldProject   被复制的项目
     * @param samplingTime 采样时间
     * @param newProject   新的项目信息
     */
    @Transactional
    @Override
    public void copyRecord(DtoProject oldProject, Date samplingTime, DtoProject newProject, Boolean isCopySample) {
        DtoReceiveSampleRecord copyRecord = this.findOutsideSendSampleById(oldProject.getId());
        DtoReceiveSampleRecord dto = new DtoReceiveSampleRecord();
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        BeanUtils.copyProperties(copyRecord, record);
        record.setId(UUIDHelper.NewID());
        record.setProjectId(newProject.getId());
        record.setSamplingTime(samplingTime);
        record.setSendTime(samplingTime);
        record.setReceiveTime(dto.getReceiveTime());
        record.setRecorderId(dto.getRecorderId());
        record.setUploadStatus(dto.getUploadStatus());
        record.setUploadTime(dto.getUploadTime());
        record.setValidAnalyzeId(dto.getValidAnalyzeId());
        record.setCheckerId(dto.getCheckerId());
        record.setCheckTime(dto.getCheckTime());
        record.setValidCheckerId(dto.getValidCheckerId());
        record.setAuditorId(dto.getAuditorId());
        record.setAuditTime(dto.getAuditTime());
        record.setValidAuditerId(dto.getValidAuditerId());
        record.setBackOpinion(dto.getBackOpinion());
        record.setReceiveSampleDate(null);
        record.setCreator(dto.getCreator());
        record.setCreateDate(dto.getCreateDate());
        record.setDomainId(dto.getDomainId());
        record.setModifier(dto.getModifier());
        record.setModifyDate(dto.getModifyDate());
        DtoReceiveSampleRecord receiveSampleRecord = this.save(record);
        if (StringUtil.isNull(isCopySample) || isCopySample) {
            schemeService.copyOutsideProjectSample(oldProject.getId(), copyRecord.getId(), receiveSampleRecord, newProject);
        }
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.复制项目, receiveSampleRecord.getProjectId(), receiveSampleRecord.getId());
                    }
                }
        );
    }

    /**
     * 获取送样单id下的实验室指标
     *
     * @param receiveId 送样单id
     * @return 返回对应送样单id下的实验室指标
     */
    @Override
    public List<DtoTest> findAnalyseTest(String receiveId) {
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList);
        List<DtoTest> testList = new ArrayList<>();
        analyseDataList.stream().filter(p -> !p.getIsCompleteField() && !p.getIsOutsourcing()).sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName))
                .collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList())).forEach((testId, list) -> {
            DtoTest test = new DtoTest();
            test.setId(testId);
            test.setAnalyzeItemId(list.get(0).getAnalyseItemId());
            test.setRedAnalyzeItemName(list.get(0).getRedAnalyzeItemName());
            testList.add(test);
        });
        return testList;
    }

    @Transactional
    @Override
    public String createReceiveSampleRecordCode() {
        return serialNumberService.createNewNumber();
    }

    /* 找到送样单的相关任务信息
     * @param receiveIds 送样单ids
     * @return 返回相关的任务信息
     */
    @Override
    public List<DtoReceiveSampleRecordTemp> findReceiveSampleRecordList(List<String> receiveIds) {
        if (receiveIds.size() > 0) {
            PageBean<DtoReceiveSampleRecordTemp> pb = new PageBean<>();
            pb.setEntityName("DtoReceiveSampleRecord a, DtoProject b,DtoProjectPlan c");
            StringBuilder condition = new StringBuilder();
            condition.append("select new com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordTemp(");
            condition.append("a.projectId,a.id,c.qcGrade,c.judgment,b.customerName,a.infoStatus)");
            pb.setSelect(condition.toString());
            condition = new StringBuilder();
            condition.append(" and a.projectId = b.id");
            condition.append(" and b.id = c.projectId");
            condition.append(" and a.id in :receiveIds");
            Map<String, Object> values = new HashMap<>();
            values.put("receiveIds", receiveIds);
            pb.setCondition(condition.toString());
            return comRepository.find(pb.getAutoQuery(), values);
        }
        return new ArrayList<>();
    }

    /**
     * 获取送样单下的现场样品
     *
     * @param receiveId 送样单id
     * @return 领样单下的样品
     */
    private List<DtoSample> findLocalSampleByReceiveId(String receiveId) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoSample> pb = new PageBean<>();
        pb.setEntityName("DtoSample s,DtoReceiveSubSampleRecord2Sample r2s,DtoReceiveSubSampleRecord sub");
        pb.setSelect("select s");
        pb.addCondition(" and s.isDeleted = 0");
        pb.addCondition(" and r2s.isDeleted = 0");
        pb.addCondition(" and sub.isDeleted = 0");
        pb.addCondition(" and s.id = r2s.sampleId");
        pb.addCondition(" and r2s.receiveSubSampleRecordId = sub.id");
        pb.addCondition(" and bitand(sub.subStatus , :status) > 0");
        pb.addCondition(" and sub.receiveId = :receiveId");
        values.put("status", EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue());
        values.put("receiveId", receiveId);
        return comRepository.find(pb.getAutoQuery(), values);
    }

    private DtoReceiveSampleRecord getRecordDetail(DtoReceiveSampleRecord record) {
        DtoProject project = projectService.findOne(record.getProjectId());
        if (StringUtil.isNotNull(project)) {
            record.setInspectedLinkMan(project.getInspectedLinkMan());
            record.setInspectedLinkPhone(project.getInspectedLinkPhone());
            record.setInspectedAddress(project.getInspectedAddress());
        }
        List<DtoSamplingPersonConfig> personCfgs = samplingPersonConfigRepository.findByObjectTypeAndObjectId(EnumSamplingType.送样单.getValue(), record.getId());
        List<DtoSamplingCarConfig> carCfgs = samplingCarConfigRepository.findByObjectTypeAndObjectId(EnumSamplingCarType.送样单.getValue(), record.getId());
        if (StringUtil.isNotNull(personCfgs) && personCfgs.size() > 0) {
            record.setSamplingPersonIds(personCfgs.stream().map(DtoSamplingPersonConfig::getSamplingPersonId).distinct().collect(Collectors.toList()));
            record.setSamplingPersonNames(personCfgs.stream().map(DtoSamplingPersonConfig::getSamplingPerson).distinct().collect(Collectors.toList()));
            record.setSamplingPersonNames(personCfgs.stream().map(DtoSamplingPersonConfig::getSamplingPerson).distinct().collect(Collectors.toList()));
            record.setSamplingPersonList(personCfgs);
        }
        if (StringUtil.isNotNull(carCfgs) && carCfgs.size() > 0) {
            record.setCarIds(carCfgs.stream().map(DtoSamplingCarConfig::getCarId).distinct().collect(Collectors.toList()));
            List<DtoCarManage> carList = carManageService.findAll(record.getCarIds());
            record.setCarNames(carList.stream().map(DtoCarManage::getCarCode).collect(Collectors.toList()));
        }
        //获取接样人
        String recipientName = "";
        if (StringUtil.isNotEmpty(record.getRecipientId()) && !UUIDHelper.GUID_EMPTY.equals(record.getRecipientId())) {
            DtoPerson person = personService.findOne(record.getRecipientId());
            if (StringUtil.isNotNull(person)) {
                recipientName = person.getCName();
            }
        }
        record.setRecipientName(recipientName);
        //BUG2024113001694 【重要】【2024-11-29】【陈军】【安徽】【样品交接】针对送样类任务，提交到样品交接，填写采样人员后，在样品交接单中未显示，需要处理；
        // 在样品交接提交后，送样人员、采样人员均未显示，需要处理。（2个问题）
        // 送样类项目 交接单的采样人和送样人 取项目登记上的
        DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
        String projectTypeCode = (String) JsonIterator.deserialize(projectType.getConfig(), Map.class).get("projectRegisterPage");
        if (EnumProjectType.送样类.getValue().equals(projectTypeCode)) {
            record.setSamplingPersonIds(Collections.singletonList(UUIDHelper.GUID_EMPTY));
            record.setSamplingPersonNames(Collections.singletonList(project.getSendSamplePerson()));
        }
        return record;
    }

    /**
     * 修改分析数据状态
     *
     * @param analyseDataList 分析数据
     * @param dataStatus      数据状态
     * @param status          状态
     * @param isDataEnabled   是否确认
     */
    protected void updateLocalAnalyseStatus(List<DtoAnalyseData> analyseDataList, Integer dataStatus, String status, Boolean isDataEnabled) {
        updateLocalAnalyseStatus(analyseDataList, dataStatus, status, isDataEnabled, UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 統一修现场数据的状态
     *
     * @param analyseDataList 数据了列表
     * @param dataStatus      状态
     * @param status          状态
     * @param isDataEnabled   是否出证
     * @param analystId       分析人员id
     * @param analystName     分析室人员名称
     */
    private void updateLocalAnalyseStatus(List<DtoAnalyseData> analyseDataList,
                                          Integer dataStatus, String status,
                                          Boolean isDataEnabled,
                                          String analystId,
                                          String analystName) {
        for (DtoAnalyseData analyseData : analyseDataList) {
            //【2023-07-27】-潘长城【需求】 送样单提交时，不修改现场数据分析人【刘庄卓修改】
//            if (StringUtils.isNotNullAndEmpty(analystId)
//                    && !analystId.equals(UUIDHelper.GUID_EMPTY)) {
//                anaData.setAnalystName(analystName);
//                anaData.setAnalystId(analystId);
//            }
            analyseData.setDataStatus(dataStatus);
            analyseData.setStatus(status);
            analyseData.setIsDataEnabled(isDataEnabled);
            analyseData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            analyseData.setModifyDate(new Date());
        }
        if (analyseDataList.size() > 0) {
            analyseDataRepository.save(analyseDataList);
        }
    }

    private List<String> getChangeContent(Map<String, Map<String, Object>> map, EnumReceiveSampleRecordField... fields) {
        String format = "</br>%s由'%s',修改为'%s'";
        List<String> contents = new ArrayList<>();
        for (EnumReceiveSampleRecordField field : fields) {
            if (map.containsKey(field.getValue())) {
                String from = StringUtil.isNull(map.get(field.getValue()).get("from")) ? "" : map.get(field.getValue()).get("from").toString();
                String to = StringUtil.isNull(map.get(field.getValue()).get("to")) ? "" : map.get(field.getValue()).get("to").toString();
                contents.add(String.format(format, field.toString(), from, to));
            }
        }
        return contents;
    }

    //region 打印交接

    /**
     * 扫码显示样品
     *
     * @param codeStr 扫码信息
     * @return 返回分组信息
     */
    @Override
    public DtoSampleGroup scanCode(String codeStr) {
        String samCode = codeStr.split(",")[0];
        String groupId = codeStr.split(",")[1];
        DtoSample sample = sampleRepository.findByCode(samCode).stream().findFirst().orElse(null);
        DtoSampleGroup sampleGroup;
        //判断是否有当前扫码的样品
        if (StringUtil.isNotNull(sample) && StringUtils.isNotNullAndEmpty(groupId)) {
            sampleGroup = sampleGroupRepository.findBySampleIdAndSampleTypeGroupId(sample.getId(), groupId);
            DtoSampleType sampleType = sampleTypeService.findOne(sample.getSampleTypeId());
            sampleGroup.setSampleCode(sample.getCode());
            sampleGroup.setRedFolderName(sample.getRedFolderName());
            sampleGroup.setSampleTypeName(sampleType.getTypeName());
        } else {
            throw new BaseException("不存在扫码样品或样品分组不正确，请确认！");
        }
        return sampleGroup;
    }

    /**
     * 扫码确认
     *
     * @param groupIds 样品分组的ids
     */
    @Transactional
    @Override
    public void scanAffirmByGroupIds(List<String> groupIds) {
        List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findAll(groupIds);
        for (DtoSampleGroup sampleGroup : sampleGroupList) {
            sampleGroup.setHasScanned(true);
            sampleGroup.setScanner(PrincipalContextUser.getPrincipal().getUserId());
            sampleGroup.setScannedTime(new Date());
            sampleGroup.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sampleGroup.setModifyDate(new Date());
        }
        sampleGroupService.update(sampleGroupList);
    }

    /**
     * 获取分组信息
     *
     * @param receiveId 送样单id
     * @return 返回分组信息
     */
    @Override
    public Map<String, Object> getSampleGroupByReceiveId(String receiveId) {
        Map<String, Object> retMap = new HashMap<>();
        Map<String, List<DtoSampleGroup>> groupMap = new HashMap<>();
        List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findByReceiveId(receiveId);
        List<DtoSample> sampleList = sampleRepository.findByIds(sampleGroupList.stream().map(DtoSampleGroup::getSampleId).distinct().collect(Collectors.toList()));
        List<DtoSampleType> sampleTypeList = sampleTypeService.findAll(sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList()));

        for (DtoSampleGroup sampleGroup : sampleGroupList) {
            DtoSample sample = sampleList.stream().filter(p -> sampleGroup.getSampleId().equals(p.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(sample)) {
                sampleGroup.setSampleTypeId(sample.getSampleTypeId());
                sampleGroup.setSampleCode(sample.getCode());
                sampleGroup.setRedFolderName(sample.getRedFolderName());
                DtoSampleType sampleType = sampleTypeList.stream().filter(p -> sample.getSampleTypeId().equals(p.getId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(sampleType)) {
                    sampleGroup.setSampleTypeName(sampleType.getTypeName());
                }
                sampleGroup.setIsSamDeleted(false);
            } else {
                sampleGroup.setIsSamDeleted(true);
            }
        }

        List<DtoSampleGroup> scanGroupList = sampleGroupList.stream().filter(p -> p.getHasScanned() && !p.getIsSamDeleted()).collect(Collectors.toList());
        List<DtoSampleGroup> noScanGroupList = sampleGroupList.stream().filter(p -> !p.getHasScanned() && !p.getIsSamDeleted()).collect(Collectors.toList());

        groupMap.put("已扫码", scanGroupList);
        groupMap.put("未扫码", noScanGroupList);
        Map<String, Integer> countMap = new HashMap<>();
        countMap.put("未扫码个数", noScanGroupList.size());
        countMap.put("已扫码个数", scanGroupList.size());
        countMap.put("扫码总数", sampleGroupList.size());
        retMap.put("data", groupMap);
        retMap.put("count", countMap);
        return retMap;
    }

    @Override
    public List<DtoSampleTypeGroup> getAllSampleGroupByReceiveId(String receiveId) {
        List<String> sampleTypeGroupIds = sampleGroupRepository.findByReceiveId(receiveId).stream().map(DtoSampleGroup::getSampleTypeGroupId).distinct()
                .collect(Collectors.toList());
        return sampleTypeGroupIds.isEmpty() ? new ArrayList<>() : sampleTypeGroupRepository.findByIdIn(sampleTypeGroupIds);
    }

    /**
     * 更新样品分配中所有分析人员为默认人员
     *
     * @param updateAnalyst 接收实体
     */
    @Override
    @Transactional
    public void updateDefaultAnalyst(DtoUpdateAnalyst updateAnalyst) {
        //获取送样单id
        String receiveId = updateAnalyst.getReceiveId();
        //获取所有样品id
        List<String> sampleIds = updateAnalyst.getSampleIds();
        //需要更新的数据集合
        List<DtoAnalyseData> updateAnalyseDataList = new ArrayList<>();
        //更新日志
        List<DtoLog> logList = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIds)) {
            //获取所有样品
            List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
            Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));

            //获取所有分析数据以及测试项目id
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
            //测试项目id
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            //获取所有的测试项目
            List<DtoTest> testList = testService.findRedisByIds(testIds);
            Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));
            //获取所有测试人员配置
            List<DtoPerson2Test> person2Tests = person2TestRepository.findByIsDefaultPersonTrueAndTestIdIn(testIds);
            //放置人员名称
            this.fillPersonName(person2Tests);
            //修改所有的分析人员
            for (DtoAnalyseData analyseData : analyseDataList) {
                //获取原分析人员
                String originalPerson = StringUtil.isNotEmpty(analyseData.getAnalystName()) ? analyseData.getAnalystName() : "";
                //获取对应的样品
                DtoSample sample = sampleMap.get(analyseData.getSampleId());
                //获取对应的测试项目
                DtoTest test = testMap.get(analyseData.getTestId());
                //获取检测类型小类id
                String sampleTypeId = StringUtil.isNotNull(sample) ? sample.getSampleTypeId() : "";
                //获取检测类型大类id
                String bigSampleTypeId = StringUtil.isNotNull(test) ? test.getSampleTypeId() : "";
                //获取测试项目id
                String testId = StringUtil.isNotNull(test) ? test.getId() : "";
                //根据小类查询默认分析人员
                DtoPerson2Test defaultPerson = person2Tests.stream().filter(p -> sampleTypeId.equals(p.getSampleTypeId()) && testId.equals(p.getTestId())).findFirst().orElse(null);
                //如果小类查询不到则查询大类默认人员配置
                if (StringUtil.isNull(defaultPerson)) {
                    defaultPerson = person2Tests.stream().filter(p -> bigSampleTypeId.equals(p.getSampleTypeId()) && testId.equals(p.getTestId())).findFirst().orElse(null);
                }
                //如果数据已添加工作单或者已确认，或者没有配置默认人员则，不设置默认分析人员
                if (StringUtil.isNull(defaultPerson) ||
                        !UUIDHelper.GUID_EMPTY.equals(analyseData.getWorkSheetFolderId()) ||
                        analyseData.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())) {
                    continue;
                }
                analyseData.setAnalystId(defaultPerson.getPersonId());
                analyseData.setAnalystName(defaultPerson.getCName());
                //添加至需要保存的数据集合中
                updateAnalyseDataList.add(analyseData);
                //处理数据创建日志
                logList.add(this.creatUpdateAnalystLog(analyseData, sample, originalPerson, receiveId));
            }
            //保存数据
            if (StringUtil.isNotEmpty(updateAnalyseDataList)) {
                analyseDataRepository.save(updateAnalyseDataList);
            }
            //批量插入日志
            if (StringUtil.isNotEmpty(logList)) {
                newLogService.createLog(logList, EnumPRO.EnumLogType.实验室领样单分配.getValue());
            }
        }

    }

    @Override
    public List<DtoSampleType> findSampleTypeForRecord(String receiveId) {
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        List<String> sampleTypeIdList = sampleList.stream().map(DtoSample::getSampleTypeId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        return StringUtil.isNotEmpty(sampleTypeIdList) ? sampleTypeRepository.findAll(sampleTypeIdList) : new ArrayList<>();
    }

    @Override
    public Boolean checkSampleCodeByReceiveId(String receiveId) {
        boolean retValue = false;
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        sampleList = sampleList.stream().filter(p -> !StringUtil.isNotEmpty(p.getCode())).collect(Collectors.toList());
        if (sampleList.size() > 0) {
            retValue = true;
        }
        return retValue;
    }

    @Override
    @Transactional
    public DtoReceiveSampleRecordParamTemplate saveParamTemplate(DtoReceiveSampleRecordParamTemplate template) {
        List<DtoReceiveSampleRecordParamTemplate> oldTemplateList = receiveSampleRecordParamTemplateRepository.findByTemplateName(template.getTemplateName());
        if (StringUtil.isNotEmpty(oldTemplateList)) {
            throw new BaseException("该模板已存在，请确认后再操作！");
        }
        receiveSampleRecordParamTemplateRepository.save(template);
        List<DtoReceiveSampleRecordParamInfo> paramList = template.getParamList();
        if (StringUtil.isNotEmpty(paramList)) {
            //设置排序值,模板id
            for (int i = 0; i < paramList.size(); i++) {
                paramList.get(i).setOrderNum(i + 1);
                paramList.get(i).setTemplateId(template.getId());
            }
            receiveSampleRecordParamInfoRepository.save(paramList);
        }
        return null;
    }

    @Override
    @Transactional
    public DtoReceiveSampleRecordParamTemplate updateParamTemplate(DtoReceiveSampleRecordParamTemplate template) {
        DtoReceiveSampleRecordParamTemplate oldTemplate = receiveSampleRecordParamTemplateRepository.findOne(template.getId());
        if (StringUtil.isNull(oldTemplate)) {
            throw new BaseException("模板不存在！");
        }
        List<DtoReceiveSampleRecordParamTemplate> templateList = receiveSampleRecordParamTemplateRepository.findByTemplateName(template.getTemplateName());
        templateList = templateList.stream().filter(p -> !template.getId().equals(p.getId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(templateList)) {
            throw new BaseException("该模板已存在，请确认后再操作！");
        }
        //保存原有的模板基础信息
        oldTemplate.setTemplateName(template.getTemplateName());
        oldTemplate.setSampleTypeId(template.getSampleTypeId());
        receiveSampleRecordParamTemplateRepository.save(oldTemplate);
        //删除原有的模板参数列表
        List<DtoReceiveSampleRecordParamInfo> oldInfoList = receiveSampleRecordParamInfoRepository.findByTemplateId(oldTemplate.getId());
        if (StringUtil.isNotEmpty(oldInfoList)) {
            receiveSampleRecordParamInfoRepository.delete(oldInfoList);
        }
        //插入新的参数
        List<DtoReceiveSampleRecordParamInfo> paramList = template.getParamList();
        if (StringUtil.isNotEmpty(paramList)) {
            //设置排序值,模板id
            for (int i = 0; i < paramList.size(); i++) {
                paramList.get(i).setId(UUIDHelper.NewID());
                paramList.get(i).setOrderNum(i + 1);
                paramList.get(i).setTemplateId(oldTemplate.getId());
            }
            receiveSampleRecordParamInfoRepository.save(paramList);
        }
        return template;
    }

    @Override
    public List<DtoReceiveSampleRecordParamTemplate> queryParamTemplate(BaseCriteria criteria) {
        PageBean<DtoReceiveSampleRecordParamTemplate> pb = new PageBean<>();
        pb.setSelect("select t");
        pb.setEntityName("DtoReceiveSampleRecordParamTemplate t");
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        ReceiveSampleRecordParamTemplateCriteria templateCriteria = (ReceiveSampleRecordParamTemplateCriteria) criteria;
        setSampleTypeIdForCriteria(templateCriteria);
        comRepository.findByPage(pb, templateCriteria);
        List<DtoReceiveSampleRecordParamTemplate> templateList = pb.getData();
        if (StringUtil.isNotEmpty(templateList)) {
            List<String> userIdList = templateList.stream().map(DtoReceiveSampleRecordParamTemplate::getCreator).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            List<DtoPerson> personList = StringUtil.isNotEmpty(userIdList) ? personService.findAll(userIdList) : new ArrayList<>();
            Map<String, DtoPerson> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, dto -> dto));
            List<String> sampleTypeIdList = templateList.stream().map(DtoReceiveSampleRecordParamTemplate::getSampleTypeId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIdList) ? sampleTypeRepository.findAll(sampleTypeIdList) : new ArrayList<>();
            Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, dto -> dto));
            for (DtoReceiveSampleRecordParamTemplate template : templateList) {
                template.setCreateUserName(personMap.containsKey(template.getCreator()) ? personMap.get(template.getCreator()).getCName() : "");
                template.setSampleTypeName(sampleTypeMap.containsKey(template.getSampleTypeId()) ? sampleTypeMap.get(template.getSampleTypeId()).getTypeName() : "");
            }
        }
        return pb.getData();
    }

    /**
     * 为参数模板查询条件对象设置样品类型id列表，同时清除receiveId查询条件（参数模板仅关联根据检测类型，不关联送样单id 参照：BUG2023112198576）
     *
     * @param templateCriteria 参数模板查询条件对象
     */
    private void setSampleTypeIdForCriteria(ReceiveSampleRecordParamTemplateCriteria templateCriteria) {
        String receiveId = templateCriteria.getReceiveId();
        receiveId = StringUtil.isNull(receiveId) ? UUIDHelper.GUID_EMPTY : receiveId;
        DtoReceiveSampleRecord record = repository.findOne(receiveId);
        String json = StringUtil.isNotNull(record) ? record.getJson() : "";
        List<String> sampleTypeIdList = new ArrayList<>();
        try {
            Map<String, Object> jsonMap = JsonUtil.toObject(json, Map.class);
            String sampleTypeIds = jsonMap.getOrDefault("sampleTypeIds", "").toString();
            if (StringUtil.isNotEmpty(sampleTypeIds)) {
                sampleTypeIdList = Arrays.asList(sampleTypeIds.split(","));
            }
        } catch (IOException e) {
            log.error("查询参数模板出错！", e);
            throw new BaseException("查询参数模板出错！");
        }
        templateCriteria.setReceiveId(null);
        templateCriteria.setSampleTypeIdList(sampleTypeIdList);
    }

    @Override
    public List<DtoReceiveSampleRecordParamInfo> getTemplateParam(BaseCriteria criteria) {
        ReceiveSampleRecordParamInfoCriteria infoCriteria = (ReceiveSampleRecordParamInfoCriteria) criteria;
        List<DtoParamsConfig> paramsConfigList = paramsConfigRepository.findByObjId(infoCriteria.getSampleTypeId());
        paramsConfigList = paramsConfigList.stream().filter(p -> !p.getIsDeleted() && (EnumLIM.EnumParamsType.样品参数.getValue().equals(p.getParamsType())
                || EnumLIM.EnumParamsType.点位参数.getValue().equals(p.getParamsType()) || EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()))).collect(Collectors.toList());
        List<DtoReceiveSampleRecordParamInfo> paramInfoList = new ArrayList<>();
        //获取点位参数样品参数公共参数
        if (StringUtil.isNotEmpty(paramsConfigList)) {
            for (DtoParamsConfig paramsConfig : paramsConfigList) {
                paramInfoList.add(initParamInfo(UUIDHelper.GUID_EMPTY, paramsConfig.getId(), paramsConfig.getAlias(), null, paramsConfig.getParamsType()));
            }
        }
        //获取送样单下的现场数据测试项目
        String receiveId = infoCriteria.getReceiveId();
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIdList) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList) : new ArrayList<>();
        List<String> fieldTestIdList = analyseDataList.stream().filter(AnalyseData::getIsCompleteField).map(DtoAnalyseData::getTestId).distinct()
                .collect(Collectors.toList());
        List<DtoTest> fieldTestList = StringUtil.isNotEmpty(fieldTestIdList) ? testService.findAll(fieldTestIdList) : new ArrayList<>();
        if (StringUtil.isNotEmpty(fieldTestList)) {
            for (DtoTest test : fieldTestList) {
                paramInfoList.add(initParamInfo(UUIDHelper.GUID_EMPTY, test.getId(), test.getRedAnalyzeItemName(), null, EnumLIM.EnumParamsType.分析项目参数.getValue()));
            }
        }
        if (StringUtil.isNotEmpty(infoCriteria.getParamName())) {
            paramInfoList = paramInfoList.stream().filter(p -> p.getParamName().contains(infoCriteria.getParamName())).collect(Collectors.toList());
        }
        return paramInfoList;
    }

    /**
     * 初始化模板参数对象
     *
     * @param templateId 模板id
     * @param paramId    参数id
     * @param paramName  参数名称
     * @param type       参数类型
     * @return 模板参数对象
     */
    private DtoReceiveSampleRecordParamInfo initParamInfo(String templateId, String paramId, String paramName, Integer orderNum, int type) {
        DtoReceiveSampleRecordParamInfo info = new DtoReceiveSampleRecordParamInfo();
        info.setTemplateId(templateId);
        info.setParamId(paramId);
        info.setParamName(paramName);
        info.setOrderNum(orderNum);
        info.setType(type);
        return info;
    }

    @Override
    @Transactional
    public int deleteParamTemplate(List<String> ids) {
        if (StringUtil.isNotEmpty(ids)) {
            List<DtoReceiveSampleRecordParamTemplate> templateList = receiveSampleRecordParamTemplateRepository.findAll(ids);
            if (StringUtil.isNotEmpty(templateList)) {
                List<String> templateIdList = templateList.stream().map(DtoReceiveSampleRecordParamTemplate::getId).collect(Collectors.toList());
                List<DtoReceiveSampleRecordParamInfo> paramInfoList = receiveSampleRecordParamInfoRepository.findByTemplateIdIn(templateIdList);
                receiveSampleRecordParamTemplateRepository.delete(templateList);
                if (StringUtil.isNotEmpty(paramInfoList)) {
                    receiveSampleRecordParamInfoRepository.delete(paramInfoList);
                }
                return templateList.size();
            }
        }
        return 0;
    }

    @Override
    public DtoReceiveSampleRecordParamTemplate findParamTemplate(String id) {
        DtoReceiveSampleRecordParamTemplate template = receiveSampleRecordParamTemplateRepository.findOne(id);
        if (StringUtil.isNull(template)) {
            throw new BaseException("模板不存在！");
        }
        List<DtoReceiveSampleRecordParamInfo> paramInfoList = receiveSampleRecordParamInfoRepository.findByTemplateId(template.getId());
        paramInfoList.sort(Comparator.comparing(DtoReceiveSampleRecordParamInfo::getOrderNum));
        template.setParamList(new ArrayList<>(paramInfoList));
        return template;
    }

    @Override
    @Transactional
    public void paramTemplateExport(String templateId, String receiveId, HttpServletResponse response) {
        String mergePlaceHolder = "\uFEFF";
        DtoReceiveSampleRecordParamTemplate template = findParamTemplate(templateId);
        List<DtoReceiveSampleRecordParamInfo> paramInfoList = template.getParamList();
        paramInfoList.forEach(p -> p.setOrderNum(p.getOrderNum() + 2));
        // 添加样品编号，点位名称固定列
        paramInfoList.add(initParamInfo(template.getId(), "sampleCode", "样品编号", 0, -1));
        paramInfoList.add(initParamInfo(template.getId(), "folderName", "点位名称", 1, -1));
        if (StringUtil.isNotEmpty(paramInfoList)) {
            //获取样品点位数据
            List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
            sampleList.sort(Comparator.comparing(DtoSample::getCode));
            sampleList = sampleService.sortPrepareSample(sampleList, false);
            List<String> folderIdList = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
            Map<String, String> folderId2HolderMap = new HashMap<>();
            String holderForFolder = mergePlaceHolder;
            for (String folderId : folderIdList) {
                folderId2HolderMap.put(folderId, holderForFolder);
                holderForFolder += mergePlaceHolder;
            }

            //建立动态表头
            List<ExcelExportEntity> excelExportEntityList = new ArrayList<>(paramInfoList.size());
            List<String> needMergePublicKeyList = new ArrayList<>(), needMergeFolderKeyList = new ArrayList<>();
            for (DtoReceiveSampleRecordParamInfo paramInfo : paramInfoList) {
                ExcelExportEntity entity = new ExcelExportEntity();
                entity.setName(paramInfo.getParamName());
                entity.setKey(paramInfo.getParamId());
                entity.setOrderNum(paramInfo.getOrderNum());
                if (EnumLIM.EnumParamsType.公共参数.getValue().equals(paramInfo.getType())) {
                    //公共参数列需要合并单元格
                    entity.setMergeVertical(true);
                    entity.setNeedMerge(true);
                    needMergePublicKeyList.add(paramInfo.getParamId());
                } else if (EnumLIM.EnumParamsType.点位参数.getValue().equals(paramInfo.getType())) {
                    //点位参数列按照同点位样品合并单元格
                    entity.setMergeVertical(true);
                    entity.setNeedMerge(true);
                    needMergeFolderKeyList.add(paramInfo.getParamId());
                }
                excelExportEntityList.add(entity);
            }
            List<Map<String, Object>> mapList = new ArrayList<>();
            for (DtoSample sample : sampleList) {
                Map<String, Object> map = new HashMap<>();
                map.put("sampleCode", sample.getCode());
                map.put("folderName", sample.getRedFolderName());
                needMergeFolderKeyList.forEach(p -> map.put(p, folderId2HolderMap.getOrDefault(sample.getSampleFolderId(), mergePlaceHolder)));
                needMergePublicKeyList.forEach(p -> map.put(p, mergePlaceHolder));
                mapList.add(map);
            }
            //导出模板
            ExportParams params = new ExportParams();
            params.setStyle(ExcelStyle.class);
            params.setSheetName("用户信息");
            Workbook workbook = ExcelExportUtil.exportExcel(params, excelExportEntityList, mapList);
            PoiExcelUtils.downLoadExcel("现场数据模板", response, workbook);
        }
    }

    @Override
    @Transactional
    public void paramTemplateImport(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) {
        List<Map<String, Object>> mapList;
        PoiExcelUtils.verifyFileType(file);
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        params.setStartSheetIndex(0);
        try {
            mapList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, params);
            if (StringUtil.isNotEmpty(mapList)) {
                String fstSampleCode = mapList.get(0).getOrDefault("样品编号", "").toString();
                List<DtoSample> sampleList = sampleRepository.findByCode(fstSampleCode);
                if (StringUtil.isNotEmpty(sampleList)) {
                    String sampleTypeId = sampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getSampleTypeId())
                            && !UUIDHelper.GUID_EMPTY.equals(p.getSampleTypeId())).map(DtoSample::getSampleTypeId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
                    String receiveId = sampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId())
                            && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).map(DtoSample::getReceiveId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
                    List<DtoSample> sampleForRecord = sampleRepository.findByReceiveId(receiveId);
                    List<String> sampleIdForRecord = sampleForRecord.stream().map(DtoSample::getId).collect(Collectors.toList());
                    Map<String, Integer> rowNumMap = new HashMap<>();
                    for (int i = 0; i < sampleForRecord.size(); i++) {
                        rowNumMap.put(sampleForRecord.get(i).getCode(), i + 1);
                    }
                    List<DtoAnalyseData> analyseDataForRecord = StringUtil.isNotEmpty(sampleIdForRecord)
                            ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdForRecord) : new ArrayList<>();
                    analyseDataForRecord = analyseDataForRecord.stream().filter(AnalyseData::getIsCompleteField).collect(Collectors.toList());
                    Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataForRecord.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
                    List<DtoParamsData> paramsDataForRecord = StringUtil.isNotEmpty(sampleIdForRecord)
                            ? paramsDataRepository.findByObjectIdInAndObjectType(sampleIdForRecord, EnumParamsDataType.样品.getValue()) : new ArrayList<>();
                    Map<String, List<DtoParamsData>> paramsDataMap = paramsDataForRecord.stream().collect(Collectors.groupingBy(DtoParamsData::getObjectId));
                    List<DtoParamsConfig> paramsConfigList = paramsConfigRepository.findByObjId(sampleTypeId);
                    paramsConfigList = paramsConfigList.stream().filter(p -> EnumLIM.EnumParamsType.样品参数.getValue().equals(p.getParamsType())
                            || EnumLIM.EnumParamsType.点位参数.getValue().equals(p.getParamsType())
                            || EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType())).collect(Collectors.toList());
                    Map<String, DtoParamsConfig> paramsConfigMap = paramsConfigList.stream().collect(Collectors.toMap(DtoParamsConfig::getId, dto -> dto));
                    Map<String, List<DtoSample>> sampleMapForRecord = sampleForRecord.stream().collect(Collectors.groupingBy(DtoSample::getCode));
                    List<DtoReceiveSampleRecordParamTemplate> templateList = receiveSampleRecordParamTemplateRepository.findBySampleTypeId(sampleTypeId);
                    List<String> templateIdList = templateList.stream().map(DtoReceiveSampleRecordParamTemplate::getId).collect(Collectors.toList());
                    List<DtoReceiveSampleRecordParamInfo> allParamInfoList = receiveSampleRecordParamInfoRepository.findByTemplateIdIn(templateIdList);
                    //校验导入的excel文件的格式与模板上配置的是否正确，格式正确则返回匹配的模板
                    DtoReceiveSampleRecordParamTemplate template = getActTemplate(mapList.get(0), templateList, allParamInfoList);
                    if (StringUtil.isNull(template)) {
                        throw new BaseException("现场数据模板不存在！");
                    }
//                    DtoReceiveSampleRecordParamTemplate template = templateList.get(0);
                    List<DtoReceiveSampleRecordParamInfo> paramInfoList = allParamInfoList.stream().filter(p -> template.getId().equals(p.getTemplateId()))
                            .collect(Collectors.toList());
//                    List<DtoReceiveSampleRecordParamInfo> paramInfoList = receiveSampleRecordParamInfoRepository.findByTemplateId(template.getId());
                    Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap = paramInfoList.stream()
                            .collect(Collectors.toMap(DtoReceiveSampleRecordParamInfo::getParamName, dto -> dto, (k1, k2) -> k2));
                    //对导入数据进行校验，校验不通过则抛出错误信息，并下载错误信息文件
                    checkImportData(file, response, params, sampleForRecord, analyseDataMap, paramsConfigMap, paramInfoMap, rowNumMap);
                    //导入参数数据及分析项目检测结果数据到数据库中
                    importData(mapList, analyseDataMap, paramsDataMap, paramsConfigMap, sampleMapForRecord, paramInfoMap);
                }
            }
        } catch (Exception e) {
            log.error("现场数据导入失败！", e);
            if (e instanceof BaseException) {
                throw new BaseException(e.getMessage());
            } else {
                throw new BaseException("现场数据导入失败！");
            }
        }
    }


    /**
     * 刷新现场样品
     * 用于现场领样单绑定样品丢失问题
     *
     * @param receiveIds 送样单ids
     */
    @Transactional
    @Override
    public void refreshLocalSample(List<String> receiveIds) {
        List<DtoReceiveSubSampleRecord> receiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
        // 筛选现场领样单
        receiveSubSampleRecords = receiveSubSampleRecords.stream().filter(p -> p.getCode().contains(EnumPRO.EnumSubRecordType.现场.getValue())).collect(Collectors.toList());
        receiveIds = receiveSubSampleRecords.stream().map(DtoReceiveSubSampleRecord::getReceiveId).distinct().collect(Collectors.toList());
        List<String> receiveSubIds = receiveSubSampleRecords.stream().map(DtoReceiveSubSampleRecord::getId).distinct().collect(Collectors.toList());
        // 已绑定现场领样单的样品
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2SampleList = StringUtil.isNotEmpty(receiveSubIds) ?
                receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordIdIn(receiveSubIds) : new ArrayList<>();
        Map<String, List<DtoReceiveSubSampleRecord2Sample>> receiveSub2SampleMap = receiveSubSampleRecord2SampleList.stream().collect(Collectors.groupingBy(DtoReceiveSubSampleRecord2Sample::getReceiveSubSampleRecordId));
        // 送样单下所有样品
        List<DtoSample> sampleList = StringUtil.isNotEmpty(receiveIds) ? sampleRepository.findByReceiveIdIn(receiveIds) : new ArrayList<>();
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        // 分析数据,筛选是现场指标，并且不分包的分析数据，并过滤对应的样品
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdIn(sampleIds) : new ArrayList<>();
        analyseDataList = analyseDataList.stream().filter(p -> p.getIsCompleteField() && !p.getIsOutsourcing()).collect(Collectors.toList());
        sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<String> finalSampleIds = sampleIds;
        sampleList.removeIf(p -> !finalSampleIds.contains(p.getId()));
        Map<String, List<DtoSample>> sampleMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getReceiveId));
        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        List<DtoReceiveSubSampleRecord2Sample> r2sList = new ArrayList<>();
        for (DtoReceiveSubSampleRecord subSampleRecord : receiveSubSampleRecords) {
            // 筛选领样单下的已绑定的样品
            List<DtoReceiveSubSampleRecord2Sample> record2Samples = receiveSub2SampleMap.get(subSampleRecord.getId());
            List<String> existsSampleIds = record2Samples.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).collect(Collectors.toList());
            // 当前送样单中所有的是样品
            List<DtoSample> samples = sampleMap.getOrDefault(subSampleRecord.getReceiveId(), new ArrayList<>());
            // 剔除已存在的样品
            samples.removeIf(p -> existsSampleIds.contains(p.getId()));
            for (DtoSample sample : samples) {
                List<DtoAnalyseData> dtoAnalyseDataList = analyseDataMap.getOrDefault(sample.getId(), new ArrayList<>());
                if (dtoAnalyseDataList.stream().anyMatch(p -> p.getIsCompleteField() && !p.getIsOutsourcing())) {
                    DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                    r2s.setReceiveSubSampleRecordId(subSampleRecord.getId());
                    r2s.setSampleId(sample.getId());
                    r2sList.add(r2s);
                }
            }
        }
        if (StringUtil.isNotEmpty(r2sList)) {
            receiveSubSampleRecord2SampleRepository.save(r2sList);
        }
    }

    /**
     * 刷新分析项目参数
     * 解决参数由样品参数改为分项目项目参数后，数据丢失问题
     */
    @Override
    @Transactional
    public void refreshParams() {
        // 根据修改的字段类型查询日志
        List<DtoTestOperateLog> testOperateLogs = testOperateLogRepository.findByTableNameAndOperateField("tb_lim_paramsconfig", "paramsType");
        // 根据TableId分组，分组下获取操作日期最晚的一条
        Map<String, DtoTestOperateLog> operateLogMap = testOperateLogs.stream().collect(Collectors.groupingBy(DtoTestOperateLog::getTableId,
                Collectors.collectingAndThen(Collectors.toList(), list -> list.stream()
                        .max(Comparator.comparing(DtoTestOperateLog::getOperatorDate)).orElse(null))));
        // 筛选由样品参数变为分析项目参数的参数配置数据
        List<DtoTestOperateLog> operateLogList = new ArrayList<>();
        for (Map.Entry<String, DtoTestOperateLog> logEntry : operateLogMap.entrySet()) {
            DtoTestOperateLog operateLog = logEntry.getValue();
            if (StringUtil.isNotNull(operateLog) && operateLog.getOldValue().equals("2") && operateLog.getNewValue().equals("3")) {
                operateLogList.add(operateLog);
            }
        }
        List<String> paramsConfigIds = operateLogList.stream().map(DtoTestOperateLog::getTableId).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigList = StringUtil.isNotEmpty(paramsConfigIds) ? paramsConfigRepository.findAll(paramsConfigIds) : new ArrayList<>();
        if (StringUtil.isNotEmpty(paramsConfigList)) {
            List<String> paramIds = paramsConfigList.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());
            List<DtoParams> paramsList = paramsRepository.findAll(paramIds);
            for (DtoParamsConfig config : paramsConfigList) {
                Optional<DtoParams> paramsOptional = paramsList.stream().filter(p -> p.getId().equals(config.getParamsId()))
                        .findFirst();
                paramsOptional.ifPresent(p -> config.setParamsName(p.getParamName()));
            }
        }
        // 改动的参数配置相关的参数数据
        List<DtoParamsData> paramsDataList = paramsDataRepository.findByParamsConfigIdIn(paramsConfigIds);
        List<String> sampleIds = paramsDataList.stream().filter(p -> p.getObjectType().equals(EnumParamsDataType.样品.getValue())).map(DtoParamsData::getObjectId).distinct().collect(Collectors.toList());
        Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));

        // 样品数据 过滤登记中的样品
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = StringUtil.isNotEmpty(receiveIds) ? repository.findAll(receiveIds) : new ArrayList<>();
        List<String> finalReceiveIds = receiveSampleRecords.stream().filter(p -> !p.getInfoStatus().equals(EnumReceiveInfoStatus.信息登记中.getValue()))
                .map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
        sampleList = sampleList.stream().filter(p -> finalReceiveIds.contains(p.getReceiveId())).collect(Collectors.toList());
        // 分析数据
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIdList) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList) : new ArrayList<>();

        //样品类型
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findRedisByIds(sampleTypeIds) : new ArrayList<>();
        List<String> bigTypeIds = sampleTypeList.stream().map(DtoSampleType::getId).collect(Collectors.toList());
        List<DtoSampleType> bigTypes = StringUtil.isNotEmpty(bigTypeIds) ? sampleTypeService.findRedisByIds(bigTypeIds) : new ArrayList<>();
        //找到分组Ids
        List<String> groupIds = bigTypes.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFieldTaskGroupId())
                && !p.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getFieldTaskGroupId)
                .distinct().collect(Collectors.toList());
        List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(groupIds)) {
            dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(groupIds, EnumLIM.EnumGroupType.分组.getValue());
            if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                group2TestList = sampleTypeGroup2TestService.
                        findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
            }
        }

        // 待新增的分析项目参数数据
        List<DtoParamsData> insertParamsDatas = new ArrayList<>();
        // 遍历修改的参数
        for (DtoTestOperateLog operateLog : operateLogList) {
            String paramsConfigId = operateLog.getTableId();
            Optional<DtoParamsConfig> paramsConfigOptional = paramsConfigList.stream().filter(p -> p.getId().equals(paramsConfigId)).findFirst();
            if (paramsConfigOptional.isPresent()) {
                DtoParamsConfig paramsConfig = paramsConfigOptional.get();
                for (DtoSample sample : sampleList) {
                    //所有数据
                    List<DtoAnalyseData> dataList = analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())).collect(Collectors.toList());
                    //所有测试项目
                    List<String> allTestIds = dataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
                    Optional<DtoSampleType> typeOptional = sampleTypeList.stream().filter(p -> p.getId().equals(sample.getSampleTypeId())).findFirst();
                    List<DtoSampleTypeGroup> sampleTypeGroupList = new ArrayList<>();
                    if (typeOptional.isPresent()) {
                        Optional<DtoSampleType> bigType = bigTypes.stream().filter(p -> p.getId().equals(typeOptional.get().getId())).findFirst();
                        if (bigType.isPresent()) {
                            sampleTypeGroupList = dtoSampleGroups.stream().filter(p -> p.getParentId().equals(bigType.get().getFieldTaskGroupId())).collect(Collectors.toList());
                        }
                    }

                    //测试项目分组
                    String valueKey;
                    //现场任务分组
                    String paramsName = paramsConfig.getParamsName();
                    String aliasName = paramsConfig.getAlias();
                    for (DtoSampleTypeGroup group : sampleTypeGroupList) {
                        List<String> groupTestIds = group2TestList.stream().filter(p -> p.getSampleTypeGroupId().equals(group.getId()))
                                .map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
                        //已有分组的测试项目去掉
                        allTestIds = allTestIds.stream().filter(p -> !groupTestIds.contains(p)).collect(Collectors.toList());
                        List<String> anaName = dataList.stream().filter(p -> groupTestIds.contains(p.getTestId()))
                                .map(DtoAnalyseData::getRedAnalyzeItemName).sorted(Comparator.comparing(p -> p))
                                .distinct().collect(Collectors.toList());
                        //存在分析项目才添加
                        if (anaName.size() > 0) {
                            String pName = String.format("%s-%s", group.getGroupName(), paramsName);
                            String aName = String.format("%s-%s", group.getGroupName(), aliasName);
                            DtoParamsConfig groupConfig = new DtoParamsConfig();
                            BeanUtils.copyProperties(paramsConfig, groupConfig);
                            groupConfig.setParamsName(pName);
                            groupConfig.setAlias(aName);
                            groupConfig.setGroupId(group.getId());
                            valueKey = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), group.getId());
                            String value = allParamsDataMap.getOrDefault(valueKey, paramsConfig.getDefaultValue());
                            //参数体积类型，需要获取分组配置上的内容
                            if ((aliasName.equals("体积类型") || paramsName.equals("体积类型")) && !StringUtil.isNotEmpty(value)) {
                                value = group.getVolumeType();
                                //需要保存体积类型的数据
                                sampleService.saveParamsDataVolume(sample, groupConfig, value, group.getId());
                                allParamsDataMap.put(valueKey, StringUtil.isNotEmpty(value) ? value : "");
                            }
                            // 先拿到旧数据中的样品参数数据
                            String oldSampleParamsKey = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                            String sampleParamsValue = allParamsDataMap.getOrDefault(oldSampleParamsKey, value);

                            if (!allParamsDataMap.containsKey(valueKey)) {
                                value = sampleParamsValue;
                                //处理初始化参数值
                                DtoParamsData paramsData = new DtoParamsData();
                                paramsData.setObjectId(sample.getId());
                                paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                                paramsData.setParamsConfigId(paramsConfig.getId());
                                paramsData.setParamsName(pName);
                                paramsData.setParamsValue(value);
                                paramsData.setDimension(paramsConfig.getDimension());
                                paramsData.setDimensionId(paramsConfig.getDimensionId());
                                paramsData.setOrderNum(paramsConfig.getOrderNum());
                                paramsData.setGroupId(group.getId());
                                insertParamsDatas.add(paramsData);
                            } else if (allParamsDataMap.get(valueKey).equals("") && !sampleParamsValue.equals("")) {
                                Optional<DtoParamsData> pd = paramsDataList.stream().filter(p -> p.getObjectId().equals(sample.getId())
                                        && p.getParamsConfigId().equals(paramsConfig.getId()) && group.getId().equals(p.getGroupId())).findFirst();
                                if (pd.isPresent()) {
                                    DtoParamsData paramsData = pd.get();
                                    paramsData.setParamsValue(sampleParamsValue);
                                    insertParamsDatas.add(paramsData);
                                }
                            }
                        }
                    }

                }
            }
        }
        if (StringUtil.isNotEmpty(insertParamsDatas)) {
            paramsDataRepository.save(insertParamsDatas);
        }
    }


    /**
     * 校验导入的excel文件的格式与模板上配置的是否正确，格式正确则返回匹配的模板
     *
     * @param map              导入的第一行数据
     * @param templateList     模板列表
     * @param allParamInfoList 模板参数列表
     * @return 模板
     */
    protected DtoReceiveSampleRecordParamTemplate getActTemplate(Map<String, Object> map, List<DtoReceiveSampleRecordParamTemplate> templateList,
                                                                 List<DtoReceiveSampleRecordParamInfo> allParamInfoList) {
        DtoReceiveSampleRecordParamTemplate actTemplate = null;
        Map<String, List<DtoReceiveSampleRecordParamInfo>> paramInfoMap = allParamInfoList.stream().collect(Collectors.groupingBy(DtoReceiveSampleRecordParamInfo::getTemplateId));
        for (DtoReceiveSampleRecordParamTemplate template : templateList) {
            List<DtoReceiveSampleRecordParamInfo> infoList = paramInfoMap.getOrDefault(template.getId(), new ArrayList<>());
            if (checkTemplateFormat(map, infoList)) {
                actTemplate = template;
                break;
            }
        }
        return actTemplate;
    }

    /**
     * 导入参数数据及分析项目检测结果数据到数据库中
     *
     * @param mapList            从文件中获取的数据
     * @param paramsDataMap      参数数据映射
     * @param sampleMapForRecord 送样单下的样品映射
     * @param paramsConfigMap    参数配置对象
     * @param paramInfoMap       模板参数对象
     * @param analyseDataMap     分析数据映射
     */
    protected void importData(List<Map<String, Object>> mapList, Map<String, List<DtoAnalyseData>> analyseDataMap, Map<String, List<DtoParamsData>> paramsDataMap,
                              Map<String, DtoParamsConfig> paramsConfigMap, Map<String, List<DtoSample>> sampleMapForRecord,
                              Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap) {
        List<DtoParamsData> updateParamsDataList = new ArrayList<>(), insertParamsDataList = new ArrayList<>();
        List<DtoAnalyseData> updateAnaDataList = new ArrayList<>();
        //获取点位参数的参数名称和参数值映射（由于模板上点位参数列会按照同一点位样品进行合并单元格，导致导入的点位参数只有同点位第一个样品的那一行有数据，其他行的点位参数数据为null）
        Map<String, Map<String, Object>> folderParamMap = getFolderParamMap(mapList, paramInfoMap, sampleMapForRecord);
        //获取公共参数的参数名称和参数值映射（由于模板上点位参数列会合并单元格，导致导入的公共参数只有第一行有数据，其他行的公共参数数据为null）
        Map<String, Object> publicParamMap = getPublicParamMap(mapList.get(0), paramInfoMap);
        //遍历导入的数据，根据模板配置的参数类型决定导入样品参数点位参数还是测试项目
        for (Map<String, Object> map : mapList) {
            if (!VerifyUtils.checkEmptyRow(map)) {
                String loopCode = StringUtil.isNotNull(map.get("样品编号")) ? map.get("样品编号").toString() : "";
                DtoSample loopSample = sampleMapForRecord.containsKey(loopCode) ? sampleMapForRecord.get(loopCode).get(0) : null;
                if (StringUtil.isNotNull(loopSample)) {
                    String folderId = loopSample.getSampleFolderId();
                    map.remove("样品编号");
                    map.remove("点位名称");
                    //遍历行数据的每一列
                    for (Map.Entry<String, Object> entry : map.entrySet()) {
                        String paramName = entry.getKey(), paramValue = StringUtil.isNotNull(entry.getValue()) ? entry.getValue().toString() : "";
                        if (!paramInfoMap.containsKey(paramName)) {
                            throw new BaseException("现场数据模板模板中未配置参数：" + paramName);
                        }
                        DtoReceiveSampleRecordParamInfo loopParamInfo = paramInfoMap.get(paramName);
                        //根据不同的参数类型,导入不同的数据
                        if (EnumLIM.EnumParamsType.样品参数.getValue().equals(loopParamInfo.getType())
                                || EnumLIM.EnumParamsType.点位参数.getValue().equals(loopParamInfo.getType())
                                || EnumLIM.EnumParamsType.公共参数.getValue().equals(loopParamInfo.getType())) {
                            if (EnumLIM.EnumParamsType.公共参数.getValue().equals(loopParamInfo.getType())) {
                                paramValue = publicParamMap.getOrDefault(paramName, "").toString();
                            } else if (EnumLIM.EnumParamsType.点位参数.getValue().equals(loopParamInfo.getType()) && folderParamMap.containsKey(paramName)) {
                                Map<String, Object> folderId2ValMap = folderParamMap.get(paramName);
                                paramValue = (folderId2ValMap.containsKey(folderId) && StringUtil.isNotNull(folderId2ValMap.get(folderId)))
                                        ? folderId2ValMap.get(folderId).toString() : paramValue;
                            }
                            //导入参数数据
                            importParam(loopParamInfo, paramValue, loopSample, paramsDataMap, paramsConfigMap, updateParamsDataList, insertParamsDataList);
                        } else {
                            //导入分析项目检测结果
                            importAnalyseData(loopParamInfo, paramValue, loopSample, analyseDataMap, updateAnaDataList);
                        }
                    }
                } else {
                    throw new BaseException("送样单中找不到样品：" + loopCode);
                }
            }
        }
        if (StringUtil.isNotEmpty(updateParamsDataList)) {
            paramsDataRepository.save(updateParamsDataList);
        }
        if (StringUtil.isNotEmpty(insertParamsDataList)) {
            paramsDataRepository.save(insertParamsDataList);
        }
        if (StringUtil.isNotEmpty(updateAnaDataList)) {
            analyseDataRepository.save(updateAnaDataList);
        }
    }

    /**
     * 获取点位参数的参数名称和参数值映射（由于模板上点位参数列会按照同一点位样品进行合并单元格，导致导入的点位参数只有同点位第一个样品的那一行有数据，其他行的点位参数数据为null）
     *
     * @param mapList      所有行数据
     * @param paramInfoMap 模板参数对象映射
     * @return 点位参数的参数名称和参数值映射
     */
    private Map<String, Map<String, Object>> getFolderParamMap(List<Map<String, Object>> mapList, Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap,
                                                               Map<String, List<DtoSample>> sampleMap) {
        Map<String, Map<String, Object>> folderParamMap = new HashMap<>();
        for (Map<String, Object> map : mapList) {
            String loopCode = StringUtil.isNotNull(map.get("样品编号")) ? map.get("样品编号").toString() : "";
            DtoSample loopSample = sampleMap.containsKey(loopCode) ? sampleMap.get(loopCode).get(0) : null;
            if (StringUtil.isNotNull(loopSample)) {
                String folderId = loopSample.getSampleFolderId();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    String paramName = entry.getKey();
                    String paramValue = StringUtil.isNotNull(entry.getValue()) ? entry.getValue().toString().trim().replace("\uFEFF", "") : "";
                    if (!paramName.equals("样品编号") && !paramName.equals("点位名称")) {
                        if (!paramInfoMap.containsKey(paramName)) {
                            throw new BaseException("现场数据模板模板中未配置参数：" + paramName);
                        }
                        DtoReceiveSampleRecordParamInfo loopParamInfo = paramInfoMap.get(paramName);
                        if (EnumLIM.EnumParamsType.点位参数.getValue().equals(loopParamInfo.getType())) {
                            if (!folderParamMap.containsKey(paramName)) {
                                folderParamMap.put(paramName, new HashMap<>());
                            }
                            Map<String, Object> folderId2ValMap = folderParamMap.get(paramName);
                            if (!folderId2ValMap.containsKey(folderId)) {
                                folderId2ValMap.put(folderId, paramValue);
                            }
                        }
                    }
                }
            }
        }
        return folderParamMap;
    }

    /**
     * 获取公共参数的参数名称和参数值映射（由于模板上公共参数列会合并单元格，导致导入的公共参数只有第一行有数据，其他行的数据为null）
     *
     * @param map          第一行数据
     * @param paramInfoMap 模板参数对象映射
     * @return 公共参数的参数名称和参数值映射
     */
    private Map<String, Object> getPublicParamMap(Map<String, Object> map, Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap) {
        Map<String, Object> folderParamMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String paramName = entry.getKey();
            String paramValue = StringUtil.isNotNull(entry.getValue()) ? entry.getValue().toString().trim().replace("\uFEFF", "") : "";
            if (!paramName.equals("样品编号") && !paramName.equals("点位名称")) {
                if (!paramInfoMap.containsKey(paramName)) {
                    throw new BaseException("现场数据模板中未配置参数：" + paramName);
                }
                DtoReceiveSampleRecordParamInfo loopParamInfo = paramInfoMap.get(paramName);
                if (EnumLIM.EnumParamsType.公共参数.getValue().equals(loopParamInfo.getType())) {
                    folderParamMap.put(paramName, paramValue);
                }
            }
        }
        return folderParamMap;
    }

    /**
     * 校验导入的excel文件的格式是否正确
     *
     * @param map           行数据映射
     * @param paramInfoList 模板参数对象列表
     */
    private boolean checkTemplateFormat(Map<String, Object> map, List<DtoReceiveSampleRecordParamInfo> paramInfoList) {
        List<String> paramNameList = paramInfoList.stream().map(DtoReceiveSampleRecordParamInfo::getParamName).collect(Collectors.toList());
        paramNameList.add("样品编号");
        paramNameList.add("点位名称");
        if (map.keySet().size() != paramNameList.size()) {
            return false;
        }
        for (String paramName : paramNameList) {
            if (!map.containsKey(paramName)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 对导入数据进行校验，校验不通过则抛出错误信息，并下载错误信息文件
     *
     * @param file            文件对象
     * @param response        响应对象
     * @param params          excel导入参数对象
     * @param sampleForRecord 送样单下的样品
     * @param paramsConfigMap 参数配置对象
     * @param paramInfoMap    模板参数对象
     * @parma analyseDataMap 分析数据映射
     */
    protected void checkImportData(MultipartFile file, HttpServletResponse response, ImportParams params, List<DtoSample> sampleForRecord,
                                   Map<String, List<DtoAnalyseData>> analyseDataMap, Map<String, DtoParamsConfig> paramsConfigMap,
                                   Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap, Map<String, Integer> rowNumMap) throws Exception {
        params.setNeedVerify(true);
        params.setVerifyHandler(new FieldDataVerifyHandle(sampleForRecord, paramsConfigMap, paramInfoMap, analyseDataMap, this.dataValidatorList, rowNumMap));
        ExcelImportResult<DtoImportConsumable> result = ExcelImportUtil.importExcelMore(file.getInputStream(), Map.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "现场数据导入错误信息");
//            failWorkbook.removeSheetAt(1);
            PoiExcelUtils.downLoadExcel("现场数据导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入！");
        }
    }

    /**
     * 导入样品参数点位参数
     *
     * @param loopParamInfo        模板参数对象
     * @param paramValue           参数值
     * @param loopSample           样品对象
     * @param paramsDataMap        参数映射
     * @param paramsConfigMap      参数配置映射
     * @param updateParamsDataList 需要修改的参数列表
     * @param insertParamsDataList 需要插入的参数列表
     */
    private void importParam(DtoReceiveSampleRecordParamInfo loopParamInfo, String paramValue, DtoSample loopSample, Map<String, List<DtoParamsData>> paramsDataMap,
                             Map<String, DtoParamsConfig> paramsConfigMap, List<DtoParamsData> updateParamsDataList, List<DtoParamsData> insertParamsDataList) {
        List<DtoParamsData> paramsDataForSample = paramsDataMap.getOrDefault(loopSample.getId(), new ArrayList<>());
        DtoParamsData paramsData = paramsDataForSample.stream().filter(p -> loopParamInfo.getParamId().equals(p.getParamsConfigId())).findFirst().orElse(null);
        if (StringUtil.isNotNull(paramsData)) {
            //存在对应参数，进行修改
            paramsData.setParamsValue(paramValue);
            updateParamsDataList.add(paramsData);
        } else {
            //不存在则新增
            DtoParamsConfig config = paramsConfigMap.getOrDefault(loopParamInfo.getParamId(), null);
            DtoParamsData newData = new DtoParamsData();
            newData.setObjectId(loopSample.getId());
            newData.setObjectType(EnumParamsDataType.样品.getValue());
            newData.setParamsConfigId(loopParamInfo.getParamId());
            newData.setParamsName(loopParamInfo.getParamName());
            newData.setParamsValue(paramValue);
            newData.setDimension(StringUtil.isNotNull(config) ? config.getDimension() : "");
            newData.setDimensionId(StringUtil.isNotNull(config) ? config.getDimensionId() : UUIDHelper.GUID_EMPTY);
            newData.setOrderNum(StringUtil.isNotNull(config) ? config.getOrderNum() : 0);
            newData.setGroupId(UUIDHelper.GUID_EMPTY);
            insertParamsDataList.add(newData);
        }
    }

    /**
     * 导入分析数据
     *
     * @param loopParamInfo     模板参数对象
     * @param paramValue        参数值
     * @param loopSample        样品对象
     * @param analyseDataMap    分析数据映射
     * @param updateAnaDataList 需要修改的分析数据列表
     */
    private void importAnalyseData(DtoReceiveSampleRecordParamInfo loopParamInfo, String paramValue, DtoSample loopSample, Map<String, List<DtoAnalyseData>> analyseDataMap,
                                   List<DtoAnalyseData> updateAnaDataList) {
        List<DtoAnalyseData> analyseDataListForSample = analyseDataMap.getOrDefault(loopSample.getId(), new ArrayList<>());
        DtoAnalyseData analyseDataForSample = analyseDataListForSample.stream().filter(p -> loopParamInfo.getParamId().equals(p.getTestId())).findFirst().orElse(null);
        if (StringUtil.isNotNull(analyseDataForSample)) {
            analyseDataForSample.setTestOrignValue(paramValue);
            analyseDataForSample.setTestValue(paramValue);
            updateAnaDataList.add(analyseDataForSample);
        }
    }


    /**
     * 创建检测人员修改日志
     *
     * @param analyseData    分析数据
     * @param sample         样品数据
     * @param originalPerson 原检测人员
     * @param subReceiveId   领样单id
     * @return
     */
    private DtoLog creatUpdateAnalystLog(DtoAnalyseData analyseData, DtoSample sample, String originalPerson, String subReceiveId) {
        //进行日志记录
        String sampleCode = sample.getCode();
        String comment = String.format("批量更换了样品%s的指标%s的检测人员为默认检测人员，原检测人员为%s，更换为%s。",
                sampleCode,
                analyseData.getRedAnalyzeItemName(),
                originalPerson,
                StringUtils.isNotNullAndEmpty(analyseData.getAnalystName()) ? analyseData.getAnalystName() : "");
        DtoLog log = new DtoLog();
        log.setId(UUIDHelper.NewID());
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
        log.setOperateTime(new Date());
        log.setOperateInfo(EnumPRO.EnumLogOperateType.修改检测人员.toString());
        log.setLogType(EnumPRO.EnumLogType.实验室领样单分配.getValue());
        log.setObjectId(subReceiveId);
        log.setObjectType(EnumPRO.EnumLogObjectType.实验室领样单.getValue());
        log.setComment(comment);
        log.setOpinion("");
        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        log.setRemark("");
        return log;
    }

    /**
     * 放置测试人员的人员名称
     *
     * @param person2Tests 测试人员信息
     */
    private void fillPersonName(List<DtoPerson2Test> person2Tests) {
        //获取所有人员信息
        List<String> personIds = person2Tests.stream().map(DtoPerson2Test::getPersonId).distinct().collect(Collectors.toList());
        //获取所有人员信息
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        Map<String, DtoPerson> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, person -> person));
        //设置对应的人员名称
        for (DtoPerson2Test person2Test : person2Tests) {
            DtoPerson defaultPerson = personMap.getOrDefault(person2Test.getPersonId(), null);
            if (StringUtil.isNotNull(defaultPerson)) {
                person2Test.setCName(defaultPerson.getCName());
                person2Test.setDomainId(defaultPerson.getDomainId());
            }
        }
    }

    /**
     * 签名
     *
     * @param receiveId 送样单id
     */
    protected void signRecord(String receiveId) {
        //审核的签名
        try {
            comRepository.clear();
            DtoReceiveSampleRecord record = repository.findOne(receiveId);
            if (record.getInfoStatus().equals(EnumReceiveInfoStatus.信息审核中.getValue())) {
                signatureService.sig(receiveId, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.校核人员_日期.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
                signatureService.sig(receiveId, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.校核人.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
            } else if (record.getInfoStatus().equals(EnumReceiveInfoStatus.已确认.getValue())) {
                signatureService.sig(receiveId, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.审核人.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
                signatureService.sig(receiveId, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.审核人员_日期.getValue(), "", DateUtil.nowTime("yyyy.MM.dd"));
            }
        } catch (Exception ex) {
            throw new BaseException(ex.getMessage());
        }
    }
    //endregion

    /**
     * 委托现场送样单提交后，如有实验室指标则同时流转到样品交接(需要根据配置开关决定)
     *
     * @param sampleIds 样品id列表
     */
    private void flowToReceiveSample(List<String> sampleIds) {
        DtoCode code = codeService.findByCode(ProCodeHelper.LIM_FLOW_RECEIVE_SAMPLE);
        if (code != null && "1".equals(code.getDictValue())) {
            //获取样品相关的数据
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
            //找出非现场和非分包的数据
            analyseDataList = analyseDataList.parallelStream().filter(p -> !p.getIsCompleteField()
                    && !p.getIsOutsourcing() && !p.getIsSamplingOut()).collect(Collectors.toList());
            //对实验室的数据进行处理，让其相关的送样单流转到样品交接
            if (StringUtil.isNotEmpty(analyseDataList)) {
                sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
                //获取样品相关的送样单id
                List<String> receiveIds = new ArrayList<>();
                for (DtoSample sample : sampleList) {
                    String loopReceiveId = sample.getReceiveId();
                    if (!receiveIds.contains(loopReceiveId)) {
                        receiveIds.add(loopReceiveId);
                    }
                }

                //获取相关送样单
                List<DtoReceiveSampleRecord> receiveSampleRecordList = repository.findAll(receiveIds);
                //流转到样品交接
                for (DtoReceiveSampleRecord dtoReceiveSampleRecord : receiveSampleRecordList) {
                    if (EnumReceiveType.委托现场送样.getValue().equals(dtoReceiveSampleRecord.getReceiveType())) {
                        DtoStatusForRecord record = statusForRecordRepository.findByReceiveIdAndModule(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                        if (!StringUtil.isNotNull(record)) {
                            statusForRecordService.createStatus(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                        }
                    }
                }
            }
        }
    }

    /**
     * 配置现场任务审核
     * true：一审 false：二审
     *
     * @return 返回判断
     */
    @Override
    public boolean isRecordAudit() {
        boolean value = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys.receiveRecord.audit");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            value = Boolean.parseBoolean(configModel.getConfigValue());
        }
        return value;
    }

    /**
     * 地图底图是否使用天地图
     * true 是，false 否
     *
     * @return 返回判断
     */
    @Override
    public boolean isWorldEarth() {
        boolean value = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys.map.worldearth");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            value = Boolean.parseBoolean(configModel.getConfigValue());
        }
        return value;
    }

    @Override
    public List<Map<String, Object>> queryInsFolderPeriodSelectList(String flowCalibrationId, String receiveId) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<DtoSample> samples = sampleRepository.findByReceiveId(receiveId);
        List<String> sampleFolderIds = samples.stream().map(DtoSample::getSampleFolderId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<DtoSampleFolder> sampleFolderList = StringUtil.isNotEmpty(sampleFolderIds) ? sampleFolderRepository.findAll(sampleFolderIds) : new ArrayList<>();
        List<String> keys = samples.stream().filter(v -> StringUtil.isNotEmpty(v.getSampleFolderId()) && !UUIDHelper.GUID_EMPTY.equals(v.getSampleFolderId())
                && v.getCycleOrder() != null).map(v -> v.getSampleFolderId() + "_" + v.getCycleOrder()).distinct().collect(Collectors.toList());
        List<String> existKeys = flowCalibration2FrequencyRepository.findByFlowCalibrationIdIn(Collections.singletonList(flowCalibrationId)).stream()
                .filter(v -> StringUtil.isNotEmpty(v.getSampleFolderId()) && v.getPeriodCount() != null)
                .map(v -> v.getSampleFolderId() + "_" + v.getPeriodCount()).distinct().collect(Collectors.toList());
        keys.removeAll(existKeys);
        keys.forEach(v -> {
            Map<String, Object> map = new HashMap<>();
            String[] data = v.split("_");
            map.put("sampleFolderId", data[0]);
            map.put("period", data[1]);
            map.put("watchSpot", "");
            sampleFolderList.stream().filter(f -> f.getId().equals(data[0])).findFirst().ifPresent(f -> map.put("watchSpot", f.getWatchSpot()));
            list.add(map);
        });
        return list;
    }

    @Override
    public void deliveryReceiptModify(String id, String opinion) {
        String comment = String.format("%s修改了交接单", PrincipalContextUser.getPrincipal().getUserName());
        if (StringUtils.isNotNullAndEmpty(opinion)) {
            comment += String.format(",原因:%s", opinion);
        }
        comment += "。";
        newLogService.createLog(id, comment,
                "", EnumLogType.送样单流程.getValue(), EnumLogObjectType.送样单.getValue(), "修改交接单",
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

    }

    @Autowired
    @Lazy
    public void setReportDetailService(ReportDetailService reportDetailService) {
        this.reportDetailService = reportDetailService;
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

    @Autowired
    @Lazy
    public void setIConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    @Lazy
    public void setEnvironmentEnterpriseService(EnvironmentEnterpriseService environmentEnterpriseService) {
        this.environmentEnterpriseService = environmentEnterpriseService;
    }
}