package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.pro.criteria.AnalyseAwaitCriteria;
import com.sinoyd.lims.pro.criteria.AnalyseDataCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.service.AnalyseDataCacheService;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * AnalyseData服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: AnalyseData服务")
 @RestController
 @RequestMapping("api/pro/analyseData")
 public class AnalyseDataController extends BaseJpaController<DtoAnalyseData, String,AnalyseDataService> {

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    @Lazy
    private AnalyseDataCacheService analyseDataCacheService;

    @Autowired
    @Lazy
    private ProService proService;

    @ApiOperation(value = "分页动态条件查询AnalyseData", notes = "分页动态条件查询AnalyseData")
    @GetMapping
    public RestResponse<List<DtoAnalyseData>> findByPage(AnalyseDataCriteria analyseDataCriteria) {
        PageBean<DtoAnalyseData> pageBean = super.getPageBean();
        RestResponse<List<DtoAnalyseData>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, analyseDataCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询AnalyseData
     *
     * @param id 主键id
     * @return RestResponse<DtoAnalyseData>
     */
    @ApiOperation(value = "按主键查询AnalyseData", notes = "按主键查询AnalyseData")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoAnalyseData> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoAnalyseData> restResponse = new RestResponse<>();
        DtoAnalyseData analyseData = service.findOne(id);
        restResponse.setData(analyseData);
        restResponse.setRestStatus(StringUtil.isNull(analyseData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * /*** 删除样品指标
     *
     * @param dtoAnalyseDataDelete 删除实体
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除样品指标", notes = "删除样品指标")
    @DeleteMapping
    public RestResponse<Boolean> delete(@RequestBody DtoAnalyseDataDelete dtoAnalyseDataDelete) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        proService.deleteAnalyseData(dtoAnalyseDataDelete.getSampleId(), dtoAnalyseDataDelete.getTestIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 删除样品指标--移动端删除（由于移动端调用delete无法接收到）
     *
     * @param dtoAnalyseDataDelete 删除实体
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除样品指标", notes = "删除样品指标")
    @PostMapping("/deleteDataPhone")
    public RestResponse<Boolean> deleteDataPhone(@RequestBody DtoAnalyseDataDelete dtoAnalyseDataDelete) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        proService.deleteAnalyseData(dtoAnalyseDataDelete.getSampleId(), dtoAnalyseDataDelete.getTestIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 新增AnalyseData
     *
     * @param analyseDataAdd 添加实体
     * @return RestResponse<DtoAnalyseData>
     */
    @ApiOperation(value = "新增AnalyseData", notes = "新增AnalyseData")
    @PostMapping
    public RestResponse<Boolean> addSampleTest(@RequestBody DtoAnalyseDataAdd analyseDataAdd) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        proService.addSampleTest(analyseDataAdd.getSampleId(), analyseDataAdd.getTestIds(), analyseDataAdd.getIsAddForFolder());
        return restResponse;
    }

    @ApiOperation(value = "替换绑定的测试项目", notes = "替换绑定的测试项目")
    @PutMapping("/repeatSampleTest")
    public RestResponse<List<DtoTest>> repeatSampleTest(@RequestBody DtoAnalyseDataAdd analyseDataAdd) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        restResponse.setData(proService.repeatSampleTest(analyseDataAdd.getSampleId(), analyseDataAdd.getTestIds(), analyseDataAdd.getIsAddForFolder()));
        return restResponse;
    }


    /**
     * 分包数据
     *
     * @param analyseDatas 实体列表
     * @return RestResponse<DtoAnalyseData>
     */
    @ApiOperation(value = "修改分析数据", notes = "修改分析数据")
    @PutMapping
    public RestResponse<Boolean> update(@RequestBody List<DtoAnalyseData> analyseDatas) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.subAnalyseData(analyseDatas);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 批量分包数据
     *
     * @param map 实体列表
     * @return RestResponse<DtoAnalyseData>
     */
    @ApiOperation(value = "批量分包数据", notes = "批量分包数据")
    @PutMapping("/batchSub")
    public RestResponse<Boolean> updateBatch(@RequestBody Map<String, List<String>> map) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        List<String> sampleIdList = map.get("sampleIdList");
        List<String> subTestIdList = map.get("subTestIdList");
        List<String> notSubTestIdList = map.get("notSubTestIdList");
        List<String> outSamplingTestIdList = map.get("outSamplingTestIdList");
        service.batchSubAnalyseData(sampleIdList, subTestIdList, notSubTestIdList, outSamplingTestIdList);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 查询样品分包情况
     *
     * @param sampleIdList 样品id列表
     * @return RestResponse<DtoAnalyseData>
     */
    @ApiOperation(value = "查询样品分包情况", notes = "查询样品分包情况")
    @PostMapping("/sampleSubInfo")
    public RestResponse<Map<String, List<DtoTest>>> getSampleSub(@RequestBody List<String> sampleIdList) {
        RestResponse<Map<String, List<DtoTest>>> restResponse = new RestResponse<>();
        Map<String, List<DtoTest>> resMap = service.getSampleSubInfo(sampleIdList);
        restResponse.setData(resMap);
        return restResponse;
    }

    /**
     * 修改分析人
     *
     * @param dto 实体
     * @return RestResponse<DtoAnalyseData>
     */
    @ApiOperation(value = "修改分析人", notes = "修改分析人")
    @PutMapping("/analyst")
    public RestResponse<Boolean> changeAnalysePerson(@RequestBody @Validated DtoAnalyseChange dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.changeAnalysePerson(dto.getIds(), dto.getAnalystId(), dto.getAnalystName());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 获取样品分析进度
     *
     * @param sampleId 样品id
     * @return RestResponse<List < DtoAnalyseDataInquiry>>
     */
    @ApiOperation(value = "获取样品分析进度", notes = "获取样品分析进度")
    @GetMapping("/sampleInquiry")
    public RestResponse<List<DtoAnalyseDataInquiry>> findInquiry(@RequestParam(name = "sampleId") String sampleId) {
        RestResponse<List<DtoAnalyseDataInquiry>> restResp = new RestResponse<>();
        restResp.setData(service.findInquiry(sampleId));
        restResp.setRestStatus(StringUtil.isEmpty(restResp.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setCount(StringUtil.isEmpty(restResp.getData()) ? 0 : restResp.getData().size());
        return restResp;
    }

    /**
     * 获取数据
     *
     * @param sampleId 样品id
     * @return RestResponse<List < DtoAnalyseData>>
     */
    @ApiOperation(value = "获取数据", notes = "获取数据")
    @GetMapping("/getDataListBySampleId")
    public RestResponse<List<DtoAnalyseData>> getAnalyseDataListBySampleId(@RequestParam(name = "sampleId") String sampleId) {
        RestResponse<List<DtoAnalyseData>> restResp = new RestResponse<>();
        restResp.setData(service.getAnalyseDataListBySampleId(sampleId));
        restResp.setRestStatus(StringUtil.isEmpty(restResp.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setCount(StringUtil.isEmpty(restResp.getData()) ? 0 : restResp.getData().size());
        return restResp;
    }


    @ApiOperation(value = "根据人员id查询统计数据", notes = "根据人员id查询统计数据")
    @GetMapping("/analyseStatistics")
    public RestResponse<DtoAnalyseWorkView> findAnalyseStatistics(@RequestParam(name = "personId") String personId) {
        RestResponse<DtoAnalyseWorkView> restResp = new RestResponse<>();
        restResp.setData(analyseDataCacheService.findAnalyseStatistics(personId));

        return restResp;
    }

    @ApiOperation(value = "根据人员id查询待检任务", notes = "根据人员id查询待检任务")
    @GetMapping("/worksheetSample")
    public RestResponse<DtoAnalyseProgress> findAnalyseProgress(@RequestParam(name = "personId") String personId) {
        RestResponse<DtoAnalyseProgress> restResp = new RestResponse<>();
        restResp.setData(analyseDataCacheService.findAnalyseProgress(personId));

        return restResp;
    }

    @ApiOperation(value = "根据人员id查询待检任务", notes = "根据人员id查询待检任务")
    @GetMapping("/awaitAnalyse")
    public RestResponse<List<DtoAnalyseAwait>> findWaitAnalyseDataByPersonIdAndTestId(AnalyseAwaitCriteria criteria) {
        RestResponse<List<DtoAnalyseAwait>> restResp = new RestResponse<>();
        List<DtoAnalyseAwait> data = analyseDataCacheService.findWaitAnalyseDataByPersonIdAndTestId(criteria);
        restResp.setData(data);
        restResp.setRestStatus(StringUtil.isEmpty(data) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setCount(data.size());
        return restResp;
    }

    @ApiOperation(value = "保存现场数据", notes = "保存现场数据")
    @PostMapping("/localData")
    public RestResponse<String> saveAnalyseData(@RequestBody DtoLocalDataSave dtoLocalDataSave) {
        RestResponse<String> restResp = new RestResponse<>();
        service.saveLocalData(dtoLocalDataSave.getSubId(), dtoLocalDataSave.getSortId(), dtoLocalDataSave.getAnalyseData());
        return restResp;
    }

    @ApiOperation(value = "保存检测单相关数据", notes = "保存检测单相关数据")
    @PostMapping("/save")
    public RestResponse<String> saveAnalyseData(@RequestBody DtoWorkSheetSave dtoWorkSheetSave) {
        RestResponse<String> restResp = new RestResponse<>();
        service.saveAnalyseData(dtoWorkSheetSave);
        return restResp;
    }

    @ApiOperation(value = "计算检测结果检测单数据", notes = "计算检测结果检测单数据")
    @PostMapping("/calculate/value")
    public RestResponse<List<Map<String, Object>>> calculateAnalyticalResult(@RequestBody DtoAnalyseDataCalculation dtoAnalyseDataCalculation) {
        DtoTestQCRangeResult rangeResult = new DtoTestQCRangeResult();
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.calculateAnalyticalResult(dtoAnalyseDataCalculation, rangeResult);
        restResponse.setData(mapList);
        if (StringUtil.isNotNull(rangeResult.getIsPass()) && !rangeResult.getIsPass()) {
            restResponse.setMsg(rangeResult.getRangeConfig());
        } else {
            restResponse.setMsg("");
        }
        return restResponse;
    }

    @ApiOperation(value = "计算带公式数据", notes = "计算带公式数据")
    @PostMapping("/calculate/formula")
    public RestResponse<List<Map<String, Object>>> analyticalFormula(@RequestBody DtoAnalyseDataCalculation dtoAnalyseDataCalculation) {
        DtoTestQCRangeResult rangeResult = new DtoTestQCRangeResult();
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.analyticalFormula(dtoAnalyseDataCalculation, rangeResult);
        restResponse.setData(mapList);
        if (StringUtil.isNotNull(rangeResult.getIsPass()) && !rangeResult.getIsPass()) {
            restResponse.setMsg(rangeResult.getRangeConfig());
        } else {
            restResponse.setMsg("");
        }
        return restResponse;
    }

    @ApiOperation(value = "计算检测结果检测单数据", notes = "计算检测结果检测单数据")
    @PostMapping("/calculate/all")
    public RestResponse<List<Map<String, Object>>> analyticalFormulaAll(@RequestBody DtoWorkSheetProperty dtoWorkSheetProperty) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.analyticalFormula(dtoWorkSheetProperty);
        restResponse.setData(mapList);
        return restResponse;
    }

    @ApiOperation(value = "更换公式", notes = "更换公式")
    @PostMapping("/formulaChange")
    public RestResponse<DtoWorkSheetProperty> changeAnalyseDataFormula(@RequestBody DtoAnalyseDataChangeFormula dtoAnalyseDataChangeFormula) {
        RestResponse<DtoWorkSheetProperty> restResponse = new RestResponse<>();
        DtoWorkSheetProperty workSheetProperty = workSheetFolderService.changeAnalyseDataFormula(dtoAnalyseDataChangeFormula);
        restResponse.setData(workSheetProperty);
        return restResponse;
    }

    @ApiOperation(value = "更换参数数据", notes = "更换参数数据")
    @PostMapping("/paramsDataChange")
    public RestResponse<List<Map<String, Object>>> changeAnalyseDataFormula(@RequestBody List<String> anaIds) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.refreshAnalyseDataParams(anaIds);
        restResponse.setData(mapList);
        return restResponse;
    }

    @ApiOperation(value = "自动同步仪器解析数据", notes = "自动同步仪器解析数据")
    @PostMapping("/syncInstrumentParseData/{workSheetFolderId}")
    public RestResponse<List<Map<String, Object>>> syncInstrumentParseData(@PathVariable("workSheetFolderId") String workSheetFolderId, @RequestBody Map<String, String> map) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        String sampleId = map.getOrDefault("sampleId", "");
        List<Map<String, Object>> mapList = service.syncInstrumentParseData(workSheetFolderId, null, sampleId);
        restResponse.setData(mapList);
        return restResponse;
    }

    @ApiOperation(value = "手动同步仪器解析数据", notes = "手动同步仪器解析数据")
    @PostMapping("/manualSyncInstrumentParseData/{workSheetFolderId}")
    public RestResponse<List<Map<String, Object>>> syncInstrumentParseData(@PathVariable("workSheetFolderId") String workSheetFolderId, @RequestBody List<String> parseDataIds) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.syncInstrumentParseData(workSheetFolderId, parseDataIds, null);
        restResponse.setData(mapList);
        return restResponse;
    }

    @ApiOperation(value = "分析项目关系提醒", notes = "分析项目关系提醒")
    @PostMapping("/relationRemind")
    public RestResponse<String> analyseDataRelationRemind(@RequestBody DtoAnalyseDataTemp analyseDataTemp) {
        RestResponse<String> restResponse = new RestResponse<>();
        String result = service.analyseDataRelationRemind(analyseDataTemp);
        restResponse.setData(result);
        return restResponse;
    }

    @ApiOperation(value = "复核人员获取", notes = "复核人员获取")
    @PostMapping("/checkPerson")
    public RestResponse<List<DtoKeyValue>> getCheckPerson(@RequestBody DtoAnalyseCheckPerson checkPerson) {
        RestResponse<List<DtoKeyValue>> restResponse = new RestResponse<>();
        List<DtoKeyValue> result = service.getCheckPerson(checkPerson.getPersonId(), checkPerson.getWorkSheetTests());
        restResponse.setData(result);
        return restResponse;
    }

    @ApiOperation(value = "审核人员获取", notes = "审核人员获取")
    @PostMapping("/auditPerson")
    public RestResponse<List<DtoKeyValue>> getCheckPerson(@RequestBody DtoAnalyseAuditPerson auditPerson) {
        RestResponse<List<DtoKeyValue>> restResponse = new RestResponse<>();
        List<DtoKeyValue> result = service.getAuditorPerson(auditPerson.getCheckPersonId(), auditPerson.getAnalystId(), auditPerson.getTestIds());
        restResponse.setData(result);
        return restResponse;
    }

    @ApiOperation(value = "剔除分析数据", notes = "剔除分析数据")
    @DeleteMapping("/dataRemove")
    public RestResponse<List<DtoAnalyseData>> delete(@RequestBody DtoAnalyseRemove dtoAnalyseRemove) {
        RestResponse<List<DtoAnalyseData>> restResponse = new RestResponse<>();
        restResponse.setData(service.removeDataFromWorkSheet(dtoAnalyseRemove.getAnalyseDataIds(), dtoAnalyseRemove.getWorkSheetFolderId(), true, true));
        return restResponse;
    }

    @ApiOperation(value = "剔除样品数据", notes = "剔除样品数据")
    @DeleteMapping("/sampleDataRemove")
    public RestResponse<List<String>> deleteSample(@RequestBody Map map) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.removeSampleFromWorkSheet((List<String>) map.get("sampleIds"), (String) map.get("workSheetFolderId")));
        return restResponse;
    }

    @ApiOperation(value = "现场数据录入移除质控样", notes = "现场数据录入移除质控样")
    @DeleteMapping("/dataRemoveBySubRecord")
    public RestResponse<String> deleteBySubRecord(@RequestBody Map map) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.removeDataFromSubRecord((List<String>) map.get("ids"), (String) map.get("subId"));
        return restResponse;
    }

    @ApiOperation(value = "提交分析数据", notes = "提交分析数据")
    @PostMapping("/submit")
    public RestResponse<String> submitAnalyseData(@RequestBody DtoAnalyseSubmit dtoAnalyseSubmit) {
        RestResponse<String> restResponse = new RestResponse<>();
        try {
            service.submitAnalyseData(dtoAnalyseSubmit.getWorkSheetFolder(),
                    dtoAnalyseSubmit.getIsCheckInstrumentUseRecord(), dtoAnalyseSubmit.getWorkSheetProperties(), dtoAnalyseSubmit.getAnalyzeTime());
        } catch (Exception ex) {
            restResponse.setRestStatus(ERestStatus.ERROR);
            if (ex instanceof TransactionSystemException) {
                restResponse.setMsg("未维护电子签名，请到人员管理中维护！");
            } else {
                restResponse.setMsg(ex.getMessage());
            }
        }
        return restResponse;
    }

    @ApiOperation(value = "退回分析数据", notes = "退回分析数据")
    @PostMapping("/back")
    public RestResponse<String> backData(@RequestBody DtoWorkSheetCheck dtoWorkSheetCheck) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.backWorkSheetFolderForData(dtoWorkSheetCheck.getIds(), dtoWorkSheetCheck.getOpinion());
        return restResponse;
    }

    @ApiOperation(value = "待检样品数据", notes = "待检样品数据")
    @GetMapping("/await")
    public RestResponse<List<Map<String, Object>>> findAnalystWorkSheetSampleList(AnalyseAwaitCriteria analyseAwaitCriteria) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> awaitList = service.findAnalystWorkSheetSampleList(analyseAwaitCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(awaitList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(awaitList);
        restResponse.setCount(awaitList.size());
        return restResponse;
    }

    @ApiOperation(value = "根据人员id查询待检测样品类型", notes = "根据人员id查询待检测样品类型")
    @GetMapping("/person")
    public RestResponse<List<DtoSampleType>> findSampleTypeByPerson(@RequestParam(name = "personId") String personId) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        List<DtoSampleType> sampleTypes = service.findSampleTypeList(personId);
        restResponse.setRestStatus(StringUtil.isEmpty(sampleTypes) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(sampleTypes);
        restResponse.setCount(sampleTypes.size());
        return restResponse;
    }

    @ApiOperation(value = "根据人员id查询待审核样品类型", notes = "根据人员id查询待审核样品类型")
    @GetMapping("/check/person")
    public RestResponse<List<DtoSampleType>> findCheckSampleTypeByPerson(@RequestParam(name = "personId") String personId) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        List<DtoSampleType> sampleTypes = service.findCheckSampleTypeList(personId);
        restResponse.setRestStatus(StringUtil.isEmpty(sampleTypes) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(sampleTypes);
        restResponse.setCount(sampleTypes.size());
        return restResponse;
    }

    @ApiOperation(value = "修改有效位小数位", notes = "修改有效位小数位")
    @PutMapping("/digit")
    public RestResponse<String> modifyDecimalDigit(@RequestBody DtoAnalyseData dtoAnalyseData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.modifyDecimalDigit(dtoAnalyseData);
        return restResponse;
    }

    @ApiOperation(value = "表头参数计算", notes = "表头参数计算")
    @PostMapping("/paramsCalculate")
    public RestResponse<List<DtoParamsData>> paramsCalculate(@RequestBody DtoWorkSheetProperty dtoWorkSheetProperty) {
        RestResponse<List<DtoParamsData>> restResponse = new RestResponse<>();
        restResponse.setData(service.paramsCalculate(dtoWorkSheetProperty.getParamsData(), dtoWorkSheetProperty.getWorksheetParamsData()));
        return restResponse;
    }

    @ApiOperation(value = "判断现场平行是否要进行原样计算", notes = "判断现场平行是否要进行原样计算")
    @PostMapping("/outParallelCalcAsSampleSwitch")
    public RestResponse<Boolean> outParallelCalcAsSampleSwitch() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.outParallelCalcAsSampleSwitch());
        return restResponse;
    }

    @ApiOperation(value = "计算现场平行样出证结果是否显示", notes = "计算现场平行样出证结果是否显示")
    @PostMapping("/outParallelSwitch")
    public RestResponse<Boolean> outParallelSwitch() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.outParallelSwitch());
        return restResponse;
    }

    @ApiOperation(value = "是否历史数据", notes = "是否历史数据")
    @GetMapping("/isHistoryValue")
    public RestResponse<Boolean> isHistoryValue() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.isHistoryValue());
        return restResponse;
    }

    @ApiOperation(value = "提交分析数据时判断质控样合格情况", notes = "提交分析数据时判断质控样合格情况")
    @GetMapping("/checkQcPass")
    public RestResponse<String> checkQcPass(String workSheetFolderId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.checkQcPass(workSheetFolderId));
        return restResponse;
    }


    @ApiOperation(value = "更新数据有效位、小数位", notes = "更新数据有效位、小数位")
    @PostMapping("/dataByTest/{objectId}")
    public RestResponse<String> uploadDataByTest(@PathVariable("objectId") String objectId) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.uploadDataByTest(objectId);
        return restResponse;
    }

    @ApiOperation(value = "提交时判断工作单是否存在记录单", notes = "提交时判断工作单是否存在记录单")
    @GetMapping("/workSheetForm")
    public RestResponse<String> checkWorkSheetForm(String workSheetFolderId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.checkWorkSheetForm(workSheetFolderId));
        return restResponse;
    }

    @ApiOperation(value = "当前工作单创建人是否为有证人员", notes = "当前工作单创建人是否为有证人员")
    @GetMapping("/certifiedPerson")
    public RestResponse<Boolean> certifiedPersonByTestIds(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.certifiedPersonByTestIds(workSheetFolderId));
        return restResponse;
    }

    /**
     * 比对评价计算
     *
     * @param recordId 送样单
     * @return 评价结果
     */
    @ApiOperation(value = "比对评价计算", notes = "比对评价计算")
    @PostMapping("/comparison/{recordId}/{isReport}")
    public RestResponse<Boolean> comparisonEvaluate(@PathVariable("recordId") String recordId, @PathVariable("isReport") Boolean isReport) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.comparisonEvaluate(recordId, isReport);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 查询分析项目关系数据
     *
     * @param id 分析数据id
     */
    @ApiOperation(value = "查询分析项目关系数据", notes = "查询分析项目关系数据")
    @GetMapping("/relation/{id}")
    public RestResponse<List<DtoAnalyseData>> relation(@PathVariable("id") String id) {
        RestResponse<List<DtoAnalyseData>> restResponse = new RestResponse<>();
        restResponse.setData(service.relation(id));
        return restResponse;
    }

    /**
     * 更新领样时间
     *
     * @param dtoAnalyseDataUpdate 分析数据更新实体
     * @return RestResponse
     */
    @ApiOperation(value = "更新领样时间", notes = "更新领样时间")
    @PostMapping("/updateDate")
    public RestResponse<String> updateSampleReceiveDate(@RequestBody DtoAnalyseDataUpdate dtoAnalyseDataUpdate) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateSampleReceiveDate(dtoAnalyseDataUpdate);
        return restResponse;
    }

    /**
     * 历史数据
     *
     * @param analyseDataHistoryVo 查询参数vo
     * @return RestResponse
     */
    @ApiOperation(value = "历史数据", notes = "历史数据")
    @GetMapping("/history")
    public RestResponse<List<DtoAnalyseDataHistory>> getHistoryData(DtoAnalyseDataHistoryVo analyseDataHistoryVo) {
        RestResponse<List<DtoAnalyseDataHistory>> restResponse = new RestResponse<>();
        restResponse.setData(service.getHistoryData(analyseDataHistoryVo));
        return restResponse;
    }

    /**
     * 分析数据异常数据处理
     *
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "分析数据异常数据处理", notes = "分析数据异常数据处理")
    @PostMapping("/exceptionalData")
    public RestResponse<Boolean> exceptionalData() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.exceptionalData();
        restResponse.setData(true);
        return restResponse;
    }
}

