package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentGatherDataCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.criteria.WorkSheetFolderAwaitCriteria;
import com.sinoyd.lims.pro.criteria.WorkSheetFolderCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * WorkSheetFolder服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: WorkSheetFolder服务")
@RestController
@RequestMapping("api/pro/workSheetFolder")
public class WorkSheetFolderController extends BaseJpaController<DtoWorkSheetFolder, String, WorkSheetFolderService> {


    @ApiOperation(value = "分页查询检测单", notes = "分页查询检测单")
    @GetMapping
    public RestResponse<List<DtoWorkSheetFolder>> findByPage(WorkSheetFolderCriteria workSheetFolderCriteria) {
        PageBean<DtoWorkSheetFolder> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheetFolder>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, workSheetFolderCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * "根据id批量删除WorkSheetFolder
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除WorkSheetFolder", notes = "根据id批量删除WorkSheetFolder")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.delete(ids);
        restResp.setCount(count);
        return restResp;
    }


    @ApiOperation(value = "分页动态条件查询AnalyseData", notes = "分页动态条件查询AnalyseData")
    @GetMapping("/detail")
    public RestResponse<List<DtoAnalyseDataTemp>> findByWorkSheetFolderId(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoAnalyseDataTemp>> restResponse = new RestResponse<>();
        List<DtoAnalyseDataTemp> dataList = service.findAnalyseDataByWorkSheetFolderId(workSheetFolderId);
        restResponse.setRestStatus(StringUtil.isEmpty(dataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(dataList);
        restResponse.setCount(dataList.size());
        return restResponse;
    }

    @ApiOperation(value = "批量创建检测单", notes = "批量创建检测单")
    @PostMapping("/batch")
    public RestResponse<List<DtoWorkSheetFolder>> createWorkSheetFolderBatch(@RequestBody DtoWorkSheetCreate dtoWorkSheetCreates) {

        RestResponse<List<DtoWorkSheetFolder>> restResponse = new RestResponse<>();
        List<DtoWorkSheetFolder> dataList = service.createWorkSheetFolderBatch(dtoWorkSheetCreates);
        restResponse.setRestStatus(StringUtil.isEmpty(dataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(dataList);
        restResponse.setCount(dataList.size());
        return restResponse;
    }


    @ApiOperation(value = "创建检测单", notes = "创建检测单")
    @PostMapping
    public RestResponse<DtoWorkSheetFolder> createWorkSheetFolder(@RequestBody DtoWorkSheetCreate dtoWorkSheetCreates) {

        RestResponse<DtoWorkSheetFolder> restResponse = new RestResponse<>();
        DtoWorkSheetFolder workSheetFolder = service.createWorkSheetFolder(dtoWorkSheetCreates);
        restResponse.setRestStatus(StringUtil.isNull(workSheetFolder) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(workSheetFolder);
        restResponse.setCount(1);
        return restResponse;
    }

    @ApiOperation(value = "同分析方法创建检测单", notes = "同分析方法创建检测单")
    @PostMapping("/sameMethod")
    public RestResponse<DtoWorkSheetFolder> createWorkSheetFolderBySameMethod(@RequestBody DtoWorkSheetCreate dtoWorkSheetCreates) {

        RestResponse<DtoWorkSheetFolder> restResponse = new RestResponse<>();
        DtoWorkSheetFolder workSheetFolder = service.createWorkSheetFolderWithSameMethod(dtoWorkSheetCreates);
        restResponse.setRestStatus(StringUtil.isNull(workSheetFolder) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(workSheetFolder);
        restResponse.setCount(1);
        return restResponse;
    }

    @ApiOperation(value = "加入检测单", notes = "加入检测单")
    @PostMapping("/join")
    public RestResponse<String> joinOnceWorkSheetFolder(@RequestBody DtoWorkSheetJoin dtoWorkSheetJoin) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.joinOnceWorkSheetFolder(dtoWorkSheetJoin);
        return restResponse;
    }

    @ApiOperation(value = "保存排序值", notes = "保存排序值")
    @PostMapping("/saveSortId")
    public RestResponse<String> saveSortId(@RequestBody DtoWorkSheetJoin dtoWorkSheetJoin) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.saveSortId(dtoWorkSheetJoin.getWorkSheetFolderId(), dtoWorkSheetJoin.getSortId());
        return restResponse;
    }

    @ApiOperation(value = "打开检测单数据", notes = "打开检测单数据")
    @RequestMapping("/data")
    public RestResponse<List<DtoWorkSheetProperty>> openWorkSheetAnalyseDataById(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoWorkSheetProperty>> restResponse = new RestResponse<>();
        restResponse.setData(service.openWorkSheetAnalyseDataById(workSheetFolderId));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "打开检测单数据", notes = "打开检测单数据")
    @RequestMapping("/dataBySampleId")
    public RestResponse<List<DtoWorkSheetProperty>> openWorkSheetAnalyseDataByIdAndSampleId(@RequestParam(name = "workSheetFolderId") String workSheetFolderId,
                                                                                            @RequestParam(name = "sampleId") String sampleId,
                                                                                            @RequestParam(name = "isAll") Boolean isAll) {
        RestResponse<List<DtoWorkSheetProperty>> restResponse = new RestResponse<>();
        restResponse.setData(service.openWorkSheetAnalyseDataById(workSheetFolderId, sampleId, isAll));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "获取检测单样品信息及是否按样品录入", notes = "获取检测单样品信息及是否按样品录入")
    @RequestMapping("/sampleInfo")
    public RestResponse<List<Map<String, Object>>> getIsSample(@RequestParam(name = "workSheetFolderId") String workSheetFolderId,
                                                               @RequestParam(name = "sampleId") String sampleId,
                                                               @RequestParam(name = "isAll") Boolean isAll) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<DtoWorkSheetProperty> workSheetPropertyList = service.getIsSample(workSheetFolderId, sampleId, isAll);
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (DtoWorkSheetProperty property : workSheetPropertyList) {
            Map<String, Object> map = new HashMap<>();
            map.put("isSample", property.getIsSample());
            map.put("samples", property.getSamples());
            mapList.add(map);
        }
        restResponse.setData(mapList);
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "打开所有检测单数据", notes = "打开所有检测单数据")
    @RequestMapping("/dataAll")
    public RestResponse<List<DtoWorkSheetProperty>> openWorkSheetAllAnalyseDataById(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoWorkSheetProperty>> restResponse = new RestResponse<>();
        restResponse.setData(service.openWorkSheetAllAnalyseDataById(workSheetFolderId));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "复核检测单", notes = "复核检测单")
    @PostMapping("/check")
    public RestResponse<List<String>> checkPassWorkSheet(@RequestBody DtoWorkSheetCheck dtoWorkSheetCheck) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        List<String> checkSampleIds = service.checkPassWorkSheet(dtoWorkSheetCheck.getIds(),
                dtoWorkSheetCheck.getStatus(), dtoWorkSheetCheck.getOpinion(), dtoWorkSheetCheck.getAuditorId(), dtoWorkSheetCheck.getAuditorName());
        restResponse.setData(checkSampleIds);
        return restResponse;
    }

    @ApiOperation(value = "确认检测单", notes = "确认检测单")
    @PostMapping("/confirm")
    public RestResponse<List<String>> confirmPassWorkSheet(@RequestBody DtoWorkSheetCheck dtoWorkSheetCheck) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        List<String> checkSampleIds = service.confirmPassWorkSheet(dtoWorkSheetCheck.getIds(),
                dtoWorkSheetCheck.getStatus(), dtoWorkSheetCheck.getOpinion(), dtoWorkSheetCheck.getAuditorId(), dtoWorkSheetCheck.getAuditorName());
        restResponse.setData(checkSampleIds);
        return restResponse;
    }

    @ApiOperation(value = "审核检测单", notes = "审核检测单")
    @PostMapping("/audit")
    public RestResponse<List<String>> auditWorkSheet(@RequestBody DtoWorkSheetCheck dtoWorkSheetCheck) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        List<String> checkSampleIds = service.auditWorkSheet(dtoWorkSheetCheck.getIds(),
                dtoWorkSheetCheck.getStatus(), dtoWorkSheetCheck.getOpinion());
        restResponse.setData(checkSampleIds);
        return restResponse;
    }

    @ApiOperation(value = "退回检测单", notes = "退回检测单")
    @PostMapping("/back")
    public RestResponse<String> backWorkSheet(@RequestBody DtoWorkSheetCheck dtoWorkSheetCheck) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.backWorkSheet(dtoWorkSheetCheck.getIds(), dtoWorkSheetCheck.getOpinion(), dtoWorkSheetCheck.getIsReport());
        return restResponse;
    }

    @ApiOperation(value = "查询检测单默认仪器", notes = "查询检测单默认仪器")
    @GetMapping("/instrument")
    public RestResponse<List<DtoInstrument>> findDefaultInstrument(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoInstrument>> restResponse = new RestResponse<>();
        List<DtoInstrument> dataList = service.findDefaultInstrument(workSheetFolderId);
        restResponse.setRestStatus(StringUtil.isEmpty(dataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(dataList);
        restResponse.setCount(dataList.size());
        return restResponse;
    }

    @ApiOperation(value = "切换原始记录单", notes = "切换原始记录单")
    @PostMapping("/changeRecord")
    public RestResponse<List<DtoParamsData>> changeWorkSheetParams(@RequestBody DtoWorkSheetChangeRecord dtoWorkSheetChangeRecord) {
        RestResponse<List<DtoParamsData>> restResponse = new RestResponse<>();
        List<DtoParamsData> dtoParamsData =
                service.changeWorkSheetParams(dtoWorkSheetChangeRecord.getRecordId(), dtoWorkSheetChangeRecord.getWorkSheetFolderId(), dtoWorkSheetChangeRecord.getTestIds());
        restResponse.setRestStatus(StringUtil.isNotEmpty(dtoParamsData) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        restResponse.setData(dtoParamsData);
        return restResponse;
    }

    /**
     * 计算参数数据
     *
     * @param dtoWorkSheetChangeRecord 参数对象
     * @return
     */
    @ApiOperation(value = "切换原始记录单", notes = "切换原始记录单")
    @PostMapping("/calculate/params")
    public RestResponse<List<DtoParamsData>> calculateParamsData(@RequestBody DtoWorkSheetChangeRecord dtoWorkSheetChangeRecord) {
        RestResponse<List<DtoParamsData>> restResponse = new RestResponse<>();
        List<DtoParamsData> dtoParamsData =
                service.calculateParamsData(dtoWorkSheetChangeRecord);
        restResponse.setRestStatus(StringUtil.isNotEmpty(dtoParamsData) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        restResponse.setData(dtoParamsData);
        return restResponse;
    }

    @ApiOperation(value = "待检测工作单", notes = "待检测工作单")
    @GetMapping("/await")
    public RestResponse<List<Map<String, Object>>> findAwaitWorkSheetFolder(WorkSheetFolderAwaitCriteria workSheetFolderAwaitCriteria) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> awaitList = service.findAwaitWorkSheetFolder(workSheetFolderAwaitCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(awaitList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(awaitList);
        restResponse.setCount(awaitList.size());
        return restResponse;
    }

    @ApiOperation(value = "待审核工作单", notes = "待审核工作单")
    @GetMapping("/check")
    public RestResponse<List<Map<String, Object>>> findCheckWorkSheetFolder(WorkSheetFolderAwaitCriteria workSheetFolderAwaitCriteria) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> awaitList = service.findCheckWorkSheetFolder(workSheetFolderAwaitCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(awaitList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(awaitList);
        restResponse.setCount(awaitList.size());
        return restResponse;
    }

    @ApiOperation(value = "已完成工作单", notes = "已完成工作单")
    @GetMapping("/complete")
    public RestResponse<List<Map<String, Object>>> findCompleteWorkSheetFolder(WorkSheetFolderAwaitCriteria workSheetFolderAwaitCriteria) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> awaitList = service.findCompleteWorkSheetFolder(workSheetFolderAwaitCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(awaitList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(awaitList);
        restResponse.setCount(awaitList.size());
        return restResponse;
    }

    @ApiOperation(value = "刷新工作单表头参数", notes = "刷新工作单表头参数")
    @GetMapping(path = "/refresh/{workSheetFolderId}")
    public RestResponse<List<DtoWorkSheetParamData>> refreshWorkSheetParams(@PathVariable(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoWorkSheetParamData>> restResponse = new RestResponse<>();
        List<DtoWorkSheetParamData> data = service.refreshWorksheetParamsData(workSheetFolderId);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(data.size());
        return restResponse;
    }

    @ApiOperation(value = "", notes = "")
    @GetMapping("/evaluation")
    public RestResponse<List<DtoAnalyseDataEvaluation>> findAnalyseDataEvaluation(DtoAnalyseDataEvaluationVo vo) {
        PageBean<DtoAnalyseData> pageBean = super.getPageBean();
        RestResponse<List<DtoAnalyseDataEvaluation>> restResponse = new RestResponse<>();
        List<DtoAnalyseDataEvaluation> analyseDataEvaluation = service.findAnalyseDataEvaluation(pageBean, vo);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(analyseDataEvaluation);
        restResponse.setCount(analyseDataEvaluation.size());
        return restResponse;
    }

    /**
     * 提交检测单前的校验
     *
     * @param worksheetFolderId 检测单id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "提交检测单前的校验", notes = "提交检测单前的校验")
    @GetMapping("/check/{worksheetFolderId}")
    public RestResponse<Boolean> checkFillInstrumentInfo(@PathVariable("worksheetFolderId") String worksheetFolderId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.validationForSubmit(worksheetFolderId);
        return restResponse;
    }


    @ApiOperation(value = "获取工作单下样品id", notes = "获取工作单下样品id")
    @GetMapping(path = "/sampleIds/{workSheetFolderId}")
    public RestResponse<List<String>> getSampleIds(@PathVariable(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        List<String> data = service.findWorkSheetFolderSampleIds(workSheetFolderId);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(data.size());
        return restResponse;
    }


    @ApiOperation(value = "获取使用该曲线的所有工作单", notes = "获取使用该曲线的所有工作单")
    @GetMapping("/curve/{curveId}")
    public RestResponse<List<DtoWorkSheetFolder>> findWorksheetByCurves(@PathVariable(name = "curveId") String curveId) {
        RestResponse<List<DtoWorkSheetFolder>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.findWorksheetByCurves(curveId));
        return restResponse;
    }


    @ApiOperation(value = "批量添加室内质控", notes = "批量添加室内质控")
    @PostMapping("/innerQcSamples")
    public RestResponse<String> batchAddInnerQcSample(@RequestBody WorkSheetQualityControlBatchOperationVo vo) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.batchAddQcSample(vo);
        return restResponse;
    }

    @ApiOperation(value = "查询默认复核人员", notes = "查询默认复核人员")
    @PostMapping("/defaultAudit")
    public RestResponse<DtoKeyValue> findDefaultPerson(@RequestBody DtoAnalyseCheckPerson checkPerson) {
        RestResponse<DtoKeyValue> restResponse = new RestResponse<>();
        restResponse.setData(service.findDefaultAuditPerson(checkPerson));
        return restResponse;

    }

    @ApiOperation(value = "获取实验室分析代办任务", notes = "获取实验室分析代办任务")
    @GetMapping("/todoCount/{personId}")
    public RestResponse<Map<String, Object>> findTodoCount(@PathVariable(name = "personId") String personId) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.findTodoCount(personId));
        return restResponse;
    }

    @GetMapping(path = "/checkOriginalRecord")
    public RestResponse<Boolean> checkOriginalRecord() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.checkOriginalRecord();
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }


    @ApiOperation(value = "删除同分析项目替代样", notes = "删除同分析项目替代样")
    @DeleteMapping("/removeReplaceSample")
    public RestResponse<List<String>> delete(@RequestBody DtoAnalyseRemove analyseRemove) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.removeReplaceSampleWithSameAnalyzeItem(analyseRemove));
        return restResponse;
    }

    @ApiOperation(value = "重新设置检测单样品排序值", notes = "重新设置检测单样品排序值")
    @PostMapping("/resetSortNum/{worksheetFolderId}")
    public RestResponse<Void> resetSortNum(@PathVariable(name = "worksheetFolderId") String worksheetFolderId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.resetSortNum(worksheetFolderId);
        return restResponse;
    }


    /**
     * 刷新检测单签名
     *
     * @param worksheetFolderId
     * @return
     */
    @ApiOperation(value = "刷新检测单签名", notes = "刷新检测单签名")
    @PostMapping("/refreshSignature/{worksheetFolderId}")
    public RestResponse<Void> refreshSignature(@PathVariable(name = "worksheetFolderId") String worksheetFolderId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.refreshSignature(worksheetFolderId);
        return restResponse;
    }

    @ApiOperation(value = "判断原始记录单多份生成参数是否开启", notes = "计算现场平行样出证结果是否显示")
    @PostMapping("/workSheetMultiGenerate")
    public RestResponse<Boolean> outParallelSwitch() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.workSheetMultiGenerate());
        return restResponse;
    }

    /**
     * 配置实验室审核
     * 1：一审 2： 二审
     * true：一审 false：二审
     *
     * @return 返回判定
     */
    @ApiOperation(value = "配置实验室审核", notes = "配置实验室审核")
    @GetMapping("/approvalStep")
    public RestResponse<Boolean> workSheetApprovalStep() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.workSheetApprovalStep());
        return restResponse;
    }

    /**
     * 转交检测单
     *
     * @param dtoWorkSheetCheck 转交传输参数
     */
    @ApiOperation(value = "转交检测单", notes = "转交检测单")
    @PostMapping("/transfer")
    public RestResponse<Void> transfer(@RequestBody DtoWorkSheetCheck dtoWorkSheetCheck) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.transfer(dtoWorkSheetCheck);
        return restResponse;
    }


    /**
     * 根据工作单id获取分析指标
     *
     * @param workSheetFolderId 送样单id
     * @return RestResponse<List < DtoTest>>
     */
    @ApiOperation(value = "根据工作单id获取分析指标", notes = "根据工作单id获取分析指标")
    @GetMapping("/analyseTests")
    public RestResponse<List<DtoTest>> findAnalyseTests(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> testList = service.findAnalyseTest(workSheetFolderId);
        restResponse.setData(testList);
        restResponse.setRestStatus(testList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 导入工作单前置数据
     */
    @ApiOperation(value = "导入工作单前置数据", notes = "导入工作单前置数据")
    @PostMapping("/importPretreatment/{workSheetFolderId}")
    public RestResponse<Void> importPretreatment(MultipartFile file,
                                                 @PathVariable("workSheetFolderId") String workSheetFolderId,
                                                 HttpServletResponse response) {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.importPretreatment(file, workSheetFolderId, response);
        return restResponse;

    }

    /**
     * 清空原始记录单
     */
    @ApiOperation(value = "清空原始记录单", notes = "清空原始记录单")
    @DeleteMapping("clearRecord/{workSheetFolderId}")
    public RestResponse<Void> clearRecord(@PathVariable("workSheetFolderId") String workSheetFolderId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.clearRecord(workSheetFolderId);
        return restResponse;

    }

}

