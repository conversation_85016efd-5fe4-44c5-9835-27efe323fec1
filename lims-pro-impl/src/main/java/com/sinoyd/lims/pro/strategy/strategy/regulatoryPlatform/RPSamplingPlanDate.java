package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.base.utils.XmlUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RPTaskPlanDateVO;
import com.sinoyd.lims.pro.vo.RPTaskPlanMonitorVO;
import com.sinoyd.lims.pro.vo.RPTaskPlanVO;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 上海监管平台采样计划：方案点位采样日期策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Component
public class RPSamplingPlanDate extends AbsRegulatoryPlatformRemote<RPTaskPlanVO> {

    @Override
    protected RPTaskPlanVO getPushInstance(RegulatoryPlatformPushVO pushVo) {
        RPTaskPlanVO result = new RPTaskPlanVO();
        result.setRpProjectId("");
        List<RPTaskPlanMonitorVO> planMonitors = new ArrayList<>();
        List<RPTaskPlanDateVO> dateList = new ArrayList<>();
        for (Map<String, String> planDate : pushVo.getSamplingPlan()) {
            dateList.add(new RPTaskPlanDateVO(planDate.get("samplingPlanId"), planDate.get("samplingDate")));
        }
        RPTaskPlanMonitorVO planMonitor = new RPTaskPlanMonitorVO();
        planMonitor.setDateList(dateList);
        planMonitors.add(planMonitor);
        result.setMonitorTaskPlans(planMonitors);
        return result;
    }

    @Override
    protected String loadPushSoapBody(String username, String password, RPTaskPlanVO pushData) {
        return RPSoapBodyUtil.loadSoapBody(username, password)
                .replace("${body_labels}", this.loadBodyFields(pushData));
    }

    /**
     * 加载请求体字段标签
     *
     * @param pushData 推送数据
     * @return 字段标签
     */
    private String loadBodyFields(RPTaskPlanVO pushData) {
        StringBuilder monitorTaskPlanListXml = new StringBuilder();
        if (StringUtil.isNotEmpty(pushData.getMonitorTaskPlans())) {
            pushData.getMonitorTaskPlans()
                    .forEach(plan -> monitorTaskPlanListXml.append(RPSoapBodyUtil.loadBodyFields(plan)).append("\n"));
        }
        //方案请求体标签模板
        return XmlUtil.readXml("classpath:RegulatoryPlatform/RPTaskMonitorPlan.xml")
                .replace("${TaskPlanMethodName}", insertMethodName())
                .replace("${rpProjectId}", pushData.getRpProjectId())
                .replace("${monitorTaskPlanList}", monitorTaskPlanListXml.toString());
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划_采样日期.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划_采样日期.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划_采样日期.getDeleteMethod();
    }
}
