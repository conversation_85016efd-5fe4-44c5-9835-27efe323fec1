package com.sinoyd.lims.pro.util;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;

/**
 *  访问webservice通用接口
 *
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/11/15
 */
@Slf4j
@Component
public class WebServiceUtil {

    private CloseableHttpClient httpClient;
    private RequestConfig requestConfig;

    public WebServiceUtil() {
        init();
    }

    /**
     * http请求设置
     */
    public void init() {
        this.requestConfig = RequestConfig.custom()
                .setConnectTimeout(180000)
                .setConnectionRequestTimeout(180000)
                .setSocketTimeout(180000)
                .build();
        this.httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig).build();
    }

    /**
     * 关闭http请求
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("=============关闭http请求失败===============");
                throw new BaseException("=============关闭http请求失败===============");
            }
        }
    }

    /**
     * webservice访问接口
     *
     * @param reqData  接口参数
     * @param url      接口地址
     * @return         返回字符串
     * @throws Exception
     */
    public String doRequest(String reqData, String url) {
        long t1 = System.currentTimeMillis();
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            //设置请求头
            httpPost.setHeader("Content-Type", "text/xml;charset=utf-8");
            StringEntity stringEntity = new StringEntity(reqData, StandardCharsets.UTF_8);
            httpPost.setEntity(stringEntity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity);
        } catch (Exception e) {
            e.printStackTrace();
            if(e instanceof SocketTimeoutException){
                log.info("耗时："+(System.currentTimeMillis()-t1));
                throw new BaseException("Http 请求超时: " + url);
            }else{
                throw new BaseException("Http 请求失败: " + url);
            }
        }
    }

}
