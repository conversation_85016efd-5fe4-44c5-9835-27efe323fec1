package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumStatus;


/**
 * 报告查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告类型
     */
    private String reportTypeId;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 当前人员id
     */
    private String currentPersonId;

    /**
     * 报告年份
     */
    private String reportYear;

    /**
     * 报告编号
     */
    private String code;

    /**
     * 关键字（报告编号、项目编号、项目名称、委托方）
     */
    private String key;

    /**
     * 关键字 项目名称/项目编号/委托方/受检方
     */
    private String expressKey;

    /**
     * 模块编码
     */
    private String module = EnumLIM.EnumReportModule.报告编制.getCode();

    /**
     * 模块编码列表
     */
    private List<String> modules = new ArrayList<>();

    /**
     * 状态
     */
    private Integer status = EnumStatus.所有.getValue();

    /**
     * 发放状态
     */
    private Integer grantStatus = -1;

    /**
     * 报告状态
     */
    private List<String> reportStatusList;

    /**
     * 是否报告查询
     */
    private Boolean isReportQuery = false;

    /**
     * 是否项目办结模块查询，
     */
    private Boolean isProjectFinishModule = false;

    /**
     * 报告发放人
     */
    private String sender;

    /**
     * 报告发放模块   未处理/已处理
     */
    private Boolean isExpress;

    /**
     * 报告标识集合
     */
    private List<String> ids;

    /**
     * 报告签发日期
     */
    private String startSignDate;

    private String endSignDate;

    /**
     * 查询主键ids
     */
    private List<String> selectedIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and r.projectId = p.id");
        condition.append(" and p.id = pl.projectId");
        condition.append(" and r.id = s.reportId");
        //BUG2024051701443 【优化】【2024-5-21】【郭宇航】【报告审核/签发】报告在审核或者签发状态时，项目因为某些原因办结了，目前还是会显示。加个功能，隐藏掉已经办结的项目的报告
        // 过滤报告编制，项目办结使用的报告签发的module,
        if (!isReportQuery && !module.equals(EnumLIM.EnumReportModule.报告编制.getCode()) && !isProjectFinishModule) {
            condition.append(" and p.status != '已办结'");
        }


        if(StringUtil.isNotEmpty(this.ids)){
            condition.append(" and r.id in :ids");
            values.put("ids", this.ids);
        }
        if (StringUtil.isNotEmpty(this.reportTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.reportTypeId)) {
            condition.append(" and r.reportTypeId = :reportTypeId");
            values.put("reportTypeId", this.reportTypeId);
        }

        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }

        if (StringUtil.isNotEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            condition.append(" and r.projectId = :projectId");
            values.put("projectId", this.projectId);
        }

        if (StringUtil.isNotEmpty(this.currentPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.currentPersonId)) {
            condition.append(" and s.currentPersonId = :currentPersonId");
            values.put("currentPersonId", this.currentPersonId);
        }

        if (StringUtil.isNotEmpty(this.reportYear)) {
            condition.append(" and r.reportYear = :reportYear");
            values.put("reportYear", this.reportYear);
        }

        if (StringUtil.isNotEmpty(this.reportStatusList)) {
            condition.append(" and r.status in :reportStatusList");
            values.put("reportStatusList", this.reportStatusList);
        }

        if (StringUtil.isNotEmpty(this.grantStatus.toString())) {
            if (this.grantStatus == -1) {
                condition.append(" and r.grantStatus in('1','2','3','4')");
            } else {
                condition.append(" and r.grantStatus = :grantStatus");
                values.put("grantStatus", this.grantStatus);
            }
        }

        if (StringUtil.isNotEmpty(this.module)) {
            condition.append(" and s.module = :module");
            values.put("module", this.module);
        }
        if (StringUtil.isNotEmpty(this.modules)) {
            condition.append(" and s.module in :modules");
            values.put("modules", this.modules);
        }

        if (!EnumStatus.所有.getValue().equals(this.status)) {
            condition.append(" and s.status = :status");
            values.put("status", this.status);
        }

        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (r.code like :key or p.projectCode like :key or p.projectName like :key or p.customerName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.code)) {
            condition.append(" and r.code like :code");
            values.put("code", "%" + this.code + "%");
        }
        if (StringUtil.isNotEmpty(this.expressKey)) {
            condition.append(" and (p.projectCode like :expressKey or p.projectName like :expressKey or p.customerName like :expressKey or p.inspectedEnt like :expressKey)");
            values.put("expressKey", "%" + this.expressKey + "%");
        }
        if(isExpress!=null){
            //有报告发放记录的，默认到“已处理”状态中
            if(isExpress){
                condition.append(" and exists(select 1 from DtoExpressageInfo2Report e2r where r.id = e2r.reportId )");
            }else{
                condition.append(" and not exists(select 1 from DtoExpressageInfo2Report e2r where r.id = e2r.reportId )");
            }
        }
        if (StringUtil.isNotEmpty(startSignDate)) {
            Date from = DateUtil.stringToDate(this.startSignDate, DateUtil.YEAR);
            condition.append(" and r.modifyDate >= :startSignDate");
            values.put("startSignDate", from);
        }
        if (StringUtil.isNotEmpty(endSignDate)) {
            Date to = DateUtil.stringToDate(this.endSignDate, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and r.modifyDate < :endSignDate");
            values.put("endSignDate", c.getTime());
        }
        if (StringUtil.isNotEmpty(this.selectedIds)) {
            condition.append(" and r.id in :selectedIds");
            values.put("selectedIds", this.selectedIds);
        }
        return condition.toString();
    }
}