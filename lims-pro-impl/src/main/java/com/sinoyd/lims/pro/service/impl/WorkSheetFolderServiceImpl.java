package com.sinoyd.lims.pro.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.XmlConfig;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.*;
import com.sinoyd.base.listener.LIMSEvent;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.EvaluationCriteriaRepository;
import com.sinoyd.base.repository.rcc.EvaluationLevelRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.base.utils.reflection.DynamicBean;
import com.sinoyd.base.utils.reflection.ReflectUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.*;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.pro.vo.AnalyseDataCalculationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * WorkSheetFolder操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@Service
@Slf4j
public class WorkSheetFolderServiceImpl extends BaseJpaServiceImpl<DtoWorkSheetFolder, String, WorkSheetFolderRepository> implements WorkSheetFolderService {

    @Autowired
    @Qualifier("workSheet")
    @Lazy
    private SerialNumberService serialNumberService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private AnalyzeMethodService analyzeMethodService;

    @Autowired
    @Lazy
    private WorkSheetCalibrationCurveService workSheetCalibrationCurveService;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    @Lazy
    private WorkSheetService workSheetService;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private ParamsFormulaService paramsFormulaService;

    @Autowired
    @Lazy
    private QualityControlService qualityControlService;

    @Autowired
    private QualityManageRepository qualityManageRepository;

    @Autowired
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    @Autowired
    @Lazy
    private ParamsTestFormulaService paramsTestFormulaService;

    @Autowired
    @Lazy
    private CurveService curveService;

    @Autowired
    private WorkSheetCalibrationCurveRepository workSheetCalibrationCurveRepository;

    @Autowired
    @Lazy
    private ParamsDataService paramsDataService;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    @Lazy
    private AnalyseDataCacheService analyseDataCacheService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    protected Project2WorkSheetFolderRepository project2WorkSheetFolderRepository;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private PerformanceStatisticForWorkSheetDataService performanceStatisticForWorkSheetDataService;

    @Autowired
    private WorkSheetReagentRepository workSheetReagentRepository;

    @Autowired
    @Lazy
    private RecordConfigParamsConfigService recordConfigParamsConfigService;

    @Autowired
    @Lazy
    private RecordConfigService recordConfigService;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    @Lazy
    private CalculationService calculationService;

    @Autowired
    private ParamsConfigRepository paramsConfigRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private AnalyzeItemSortDetailRepository analyzeItemSortDetailRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private SampleTypeGroupRepository sampleTypeGroupRepository;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private SampleTypeGroup2TestService sampleTypeGroup2TestService;

    @Autowired
    @Lazy
    private ProjectApprovalService projectApprovalService;

    @Autowired
    @Lazy
    private TestPostService testPostService;

    @Autowired
    @Lazy
    private EnvironmentalUseService environmentalUseService;

    @Autowired
    @Lazy
    private SignatureService signatureService;

    @Autowired
    @Lazy
    private ReportDetailRepository reportDetailRepository;

    @Autowired
    @Lazy
    private ReportRepository reportRepository;

    @Autowired
    @Lazy
    private SampleGroupService sampleGroupService;

    @Autowired
    private SolutionCalibrationRepository solutionCalibrationRepository;

    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;

    private EvaluationRecordRepository evaluationRecordRepository;

    private EvaluationCriteriaRepository evaluationCriteriaRepository;

    private EvaluationLevelRepository evaluationLevelRepository;

    private EnvironmentalRecordRepository environmentalRecordRepository;

    private AnalyzeItemRepository analyzeItemRepository;

    private InstrumentUseRecordRepository instrumentUseRecordRepository;

    private InstrumentRepository instrumentRepository;

    private CodeService codeService;

    private RedisTemplate redisTemplate;

    private SampleService sampleService;

    private QualityControlEvaluateService qualityControlEvaluateService;

    private QualityControlLimitRepository qualityControlLimitRepository;

    private QualityControlEvaluateRepository qualityControlEvaluateRepository;

    private DimensionRepository dimensionRepository;

    private JdbcTemplate jdbcTemplate;

    private Person2TestRepository person2TestRepository;

    protected ProjectPlanRepository projectPlanRepository;

    private TestExpandService testExpandService;

    private ReportDetailService reportDetailService;

    protected ProjectRepository projectRepository;

    private TestPost2TestRepository testPost2TestRepository;

    private TestPost2PersonRepository testPost2PersonRepository;

    private SampleOrderService sampleOrderService;

    private IConfigService configService;

    private MarkersDataService markersDataService;

    private DocumentRepository documentRepository;


    @Override
    public void findByPage(PageBean<DtoWorkSheetFolder> pb, BaseCriteria baseCriteria) {
        pb.setEntityName("DtoWorkSheetFolder a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, baseCriteria);

        WorkSheetFolderCriteria workSheetFolderCriteria = (WorkSheetFolderCriteria) baseCriteria;
        //说明要显示分析项目名称
        if (StringUtil.isNotNull(workSheetFolderCriteria.getIsShowAnalyzeItemName()) && workSheetFolderCriteria.getIsShowAnalyzeItemName()) {
            List<DtoWorkSheetFolder> workSheetFolders = pb.getData();
            List<String> workSheetFolderIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getId).distinct().collect(Collectors.toList());
            List<String> analyzeMethodIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            if (workSheetFolderIds.size() > 0) {
                this.setWorkSheetFolderGrade(workSheetFolders);
                //获取项目关联信息
                List<DtoProject2WorkSheetFolder> project2WorkSheetFolders = project2WorkSheetFolderRepository.findByWorkSheetFolderIdIn(workSheetFolderIds);
                List<String> projectIds = project2WorkSheetFolders.stream().map(DtoProject2WorkSheetFolder::getProjectId).distinct().collect(Collectors.toList());
                List<DtoProject> projects = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
                //检测单相关的数据
                List<DtoWorkSheet> workSheetList = workSheetRepository.findByParentIdIn(workSheetFolderIds);
                //分析方法相关的数据
                List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodService.findAllDeleted(analyzeMethodIds);
                Iterator<DtoWorkSheetFolder> iterator = workSheetFolders.iterator();
                List<DtoWorkSheetFolder> newWorkSheetFolders = new ArrayList<>();
                while (iterator.hasNext()) {
                    DtoWorkSheetFolder dtoWorkSheetFolder = iterator.next();
                    List<DtoWorkSheet> wsList = workSheetList.stream().filter(p -> p.getParentId().equals(dtoWorkSheetFolder.getId())).collect(Collectors.toList());
                    String redAnalyzeItemName = wsList.stream().map(DtoWorkSheet::getRedAnalyzeItemName).collect(Collectors.joining(","));

                    dtoWorkSheetFolder.setRedAnalyzeItemName(redAnalyzeItemName);
                    dtoWorkSheetFolder.setRedAnalyzeMethodName(analyzeMethodList.stream().filter(p -> p.getId().equals(dtoWorkSheetFolder.getAnalyzeMethodId()) && StringUtils.isNotNullAndEmpty(p.getMethodName())).map(DtoAnalyzeMethod::getMethodName).findFirst().orElse(""));
                    dtoWorkSheetFolder.setRedCountryStandard(analyzeMethodList.stream().filter(p -> p.getId().equals(dtoWorkSheetFolder.getAnalyzeMethodId()) && StringUtils.isNotNullAndEmpty(p.getCountryStandard())).map(DtoAnalyzeMethod::getCountryStandard).findFirst().orElse(""));
                    dtoWorkSheetFolder.setIsSample(analyzeMethodList.stream().filter(p -> p.getId().equals(dtoWorkSheetFolder.getAnalyzeMethodId()) && StringUtils.isNotNullAndEmpty(p.getCountryStandard())).map(DtoAnalyzeMethod::getIsInputBySample).findFirst().orElse(false));
                    List<String> projectIds2WorkSheetFolder = project2WorkSheetFolders.stream().filter(p -> p.getWorkSheetFolderId().equals(dtoWorkSheetFolder.getId())).map(DtoProject2WorkSheetFolder::getProjectId).distinct().collect(Collectors.toList());
                    dtoWorkSheetFolder.setProjectCode(projects.stream().filter(p -> projectIds2WorkSheetFolder.contains(p.getId())).map(DtoProject::getProjectCode).collect(Collectors.joining(",")));
                    newWorkSheetFolders.add(dtoWorkSheetFolder);
                }
                pb.setData(newWorkSheetFolders);
            }
        }
    }


    @Override
    public List<Map<String, Object>> findAwaitWorkSheetFolder(BaseCriteria baseCriteria) {
        WorkSheetFolderAwaitCriteria workSheetFolderAwaitCriteria = (WorkSheetFolderAwaitCriteria) baseCriteria;
        workSheetFolderAwaitCriteria.setIsShowCustomer(getIsShowCustomer());
        StringBuilder stringBuilder = new StringBuilder("select w from DtoWorkSheetFolder w where 1=1 and w.isDeleted = 0 ");
        stringBuilder.append(" and w.analystId=:analystId");
        stringBuilder.append(" and w.workStatus in :workStatus");
        stringBuilder.append(baseCriteria.getCondition());
        List<Integer> workStatus = Arrays.asList(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue(),
                EnumPRO.EnumWorkSheetStatus.新建.getValue(),
                EnumPRO.EnumWorkSheetStatus.已经保存.getValue());
        Map<String, Object> values = baseCriteria.getValues();
        values.put("analystId", workSheetFolderAwaitCriteria.getPersonId());
        values.put("workStatus", workStatus);
//        List<Integer> statusList = Arrays.asList(EnumPRO.EnumAnalyseDataStatus.未测.getValue(),
//                EnumPRO.EnumAnalyseDataStatus.在测.getValue(), EnumPRO.EnumAnalyseDataStatus.拒绝.getValue());
        //待检检测单
        List<Map<String, Object>> awaitWorkSheetList = dealWorkSheetTask(comRepository.find(stringBuilder.toString(), values), new ArrayList<>());
        awaitWorkSheetList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingByWorksheetCode, Comparator.reverseOrder()));
        return awaitWorkSheetList;
    }

    @Override
    public List<Map<String, Object>> findCheckWorkSheetFolder(BaseCriteria baseCriteria) {
        Date t1 = new Date();
        WorkSheetFolderAwaitCriteria workSheetFolderAwaitCriteria = (WorkSheetFolderAwaitCriteria) baseCriteria;
        workSheetFolderAwaitCriteria.setIsShowCustomer(getIsShowCustomer());
        StringBuilder stringBuilder = new StringBuilder("select w from DtoWorkSheetFolder w where 1=1 and w.isDeleted = 0");
        stringBuilder.append(" and (w.certificatorId =:analystId or w.checkerId =:analystId or w.auditorId =:analystId)");
        stringBuilder.append(" and ((w.workStatus =:workStatus1 and w.checkerId =:checkerId) " +
                "or (w.workStatus =:workStatus2 and w.auditorId =:auditorId) " +
                "or (w.workStatus =:workStatus3 and w.certificatorId =:certificatorId))");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        values.put("analystId", workSheetFolderAwaitCriteria.getPersonId());
        values.put("workStatus1", EnumPRO.EnumWorkSheetStatus.已经提交.getValue());
        values.put("workStatus2", EnumPRO.EnumWorkSheetStatus.复核通过.getValue());
        values.put("workStatus3", EnumPRO.EnumWorkSheetStatus.确认检测单.getValue());
        values.put("checkerId", workSheetFolderAwaitCriteria.getPersonId());
        values.put("auditorId", workSheetFolderAwaitCriteria.getPersonId());
        values.put("certificatorId", workSheetFolderAwaitCriteria.getPersonId());
        List<Integer> statusList = Arrays.asList(EnumPRO.EnumAnalyseDataStatus.已测.getValue(),
                EnumPRO.EnumWorkSheetStatus.确认检测单.getValue(), EnumPRO.EnumAnalyseDataStatus.复核通过.getValue());
        Date t2 = new Date();
        log.info("============================================检测单查询用时：" + (t2.getTime() - t1.getTime()) + "ms========================================");
        //待审核检测单
        List<Map<String, Object>> auditWorkSheetList = dealWorkSheetTask(comRepository.find(stringBuilder.toString(), values), statusList);
        auditWorkSheetList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingBySortTime).reversed()
                .thenComparing(AnalyseDataCacheServiceImpl::comparingByRedAnalyzeItemName));
        return auditWorkSheetList;
    }

    @Override
    public List<Map<String, Object>> findCompleteWorkSheetFolder(BaseCriteria baseCriteria) {
        WorkSheetFolderAwaitCriteria workSheetFolderAwaitCriteria = (WorkSheetFolderAwaitCriteria) baseCriteria;
        workSheetFolderAwaitCriteria.setIsShowCustomer(getIsShowCustomer());
        StringBuilder stringBuilder = new StringBuilder("select w from DtoWorkSheetFolder w where 1=1 and w.isDeleted = 0");
        stringBuilder.append(" and w.analystId=:analystId");
        stringBuilder.append(" and w.workStatus = :workStatus");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        values.put("analystId", workSheetFolderAwaitCriteria.getPersonId());
        values.put("workStatus", EnumPRO.EnumWorkSheetStatus.审核通过.getValue());
        List<Integer> statusList = Collections.singletonList(EnumPRO.EnumAnalyseDataStatus.已确认.getValue());
        //已完成检测单
        List<Map<String, Object>> completeWorkSheetList = dealWorkSheetTask(comRepository.find(stringBuilder.toString(), values), statusList);
        completeWorkSheetList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingByAnalyzeTime).reversed()
                .thenComparing(AnalyseDataCacheServiceImpl::comparingByRedAnalyzeItemName));
        return completeWorkSheetList;
    }

    @Override
    public String createWorkSheetCode() {
        return serialNumberService.createNewNumber();
    }

    /**
     * 核对检测单
     *
     * @param workSheetFolderIds 检测单id
     */
    @Transactional
    @Override
    public void checkWorkSheetFolder(List<String> workSheetFolderIds) {
        if (workSheetFolderIds.size() > 0) {
            Map<String, Object> values = new HashMap<>();
            StringBuilder stringBuilder = new StringBuilder("select distinct a.workSheetFolderId,a.dataStatus");
            stringBuilder.append(" from DtoAnalyseData a where 1=1");
            if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                stringBuilder.append(" and a.orgId = :orgId");
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
            stringBuilder.append(" and a.isDeleted = 0 ");
            stringBuilder.append(" and a.workSheetFolderId in :workSheetFolderIds ");
            values.put("workSheetFolderIds", workSheetFolderIds);
            List<Object[]> analyseDatas = comRepository.find(stringBuilder.toString(), values);
            List<String> wsfIds = analyseDatas.stream().map(p -> (String) p[0]).distinct().collect(Collectors.toList());
            List<String> passIds = analyseDatas.stream().filter(p -> EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals((Integer) p[1])).
                    map(p -> (String) p[0]).distinct().collect(Collectors.toList());

            if (workSheetFolderIds.size() > wsfIds.size()) {
                //将已经不存在的检测单进行删除
                workSheetFolderIds.removeAll(wsfIds);
                this.logicDeleteById(workSheetFolderIds);
            }
            if (passIds.size() > 0) {
                List<DtoWorkSheetFolder> folders = repository.findAll(passIds);
                //遍历存在
                for (DtoWorkSheetFolder folder : folders) {
                    if (!folder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.审核通过.getValue())) {
                        if (analyseDatas.stream().noneMatch(p -> ((String) p[0]).equals(folder.getId()) && !EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals((Integer) p[1]))) {
                            //若该检测单下不存在未确认的数据，则将检测单自动通过
                            repository.updateStatus(Collections.singletonList(folder.getId()), EnumPRO.EnumWorkSheetStatus.审核通过.toString(), EnumPRO.EnumWorkSheetStatus.审核通过.getValue(),
                                    PrincipalContextUser.getPrincipal().getUserId(), new Date(), "", folder.getAnalyzeTime());
                            workflowService.endInstance(folder.getId(), "");
                            newLogService.createLog(folder.getId(), "检测单下的分析数据均已审核通过，检测单置为审核通过。", "",
                                    EnumPRO.EnumLogType.检测单流程.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.修改检测单.toString(),
                                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
                        }
                    }
                }
            }

            //移除检测单下所有的关联
            project2WorkSheetFolderRepository.deleteByWorkSheetFolderIdIn(workSheetFolderIds);

            values.clear();
            stringBuilder = new StringBuilder("select distinct a.workSheetFolderId,p.id");
            stringBuilder.append(" from DtoAnalyseData a,DtoSample s,DtoProject p where 1=1");
            if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                stringBuilder.append(" and a.orgId = :orgId");
                stringBuilder.append(" and s.orgId = :orgId");
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
            stringBuilder.append(" and a.isDeleted = 0 ");
            stringBuilder.append(" and s.isDeleted = 0");
            stringBuilder.append(" and p.isDeleted = 0");
            stringBuilder.append(" and a.sampleId = s.id");
            stringBuilder.append(" and s.projectId = p.id");
            stringBuilder.append(" and a.workSheetFolderId in :workSheetFolderIds ");
            values.put("workSheetFolderIds", workSheetFolderIds);
            List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);
            List<DtoProject2WorkSheetFolder> p2wList = new ArrayList<>();
            for (Object[] data : datas) {
                DtoProject2WorkSheetFolder p2w = new DtoProject2WorkSheetFolder();
                p2w.setWorkSheetFolderId((String) data[0]);
                p2w.setProjectId((String) data[1]);
                p2wList.add(p2w);
            }
            comRepository.insertBatch(p2wList);

        }
    }

    @Override
    public List<DtoAnalyseDataTemp> findAnalyseDataByWorkSheetFolderId(String workSheetFolderId) {
        WorkSheetCriteria workSheetCriteria = new WorkSheetCriteria();
        workSheetCriteria.setWorkSheetFolderId(workSheetFolderId);
        PageBean<DtoAnalyseDataTemp> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        findAnalyseDataByPage(pageBean, workSheetCriteria);
        // 各自样品的加标样，紧接着各自的原样，如果平行、加标和加原样同时都有，那么先加原样再平行最后加标，最后放标样
        return pageBean.getData();
    }

    @Override
    @Transactional
    public void checkOriginalRecord() {
        List<DtoAnalyseOriginalRecord> recordList = analyseOriginalRecordRepository.findAll();
        List<DtoAnalyseOriginalRecord> analyseOriginalRecordList = new ArrayList<>();
        recordList.forEach(p -> {
            TypeLiteral<List<DtoTestFormulaVo>> typeLiteral = new TypeLiteral<List<DtoTestFormulaVo>>() {
            };
            if (StringUtil.isNotEmpty(p.getJson())) {
                if (!p.getJson().contains("\"detectionLimit\":\"")) {
                    List<DtoTestFormulaVo> formulaVoList = JsonIterator.deserialize(p.getJson(), typeLiteral);
                    List<DtoAnalyseOriginalJson> jsonList = new ArrayList<>();
                    AtomicReference<Boolean> isTrue = new AtomicReference<>(Boolean.FALSE);
                    formulaVoList.forEach(vo -> {
                        DtoAnalyseOriginalJson analyseOriginalJson = new DtoAnalyseOriginalJson();
                        analyseOriginalJson.setAlias(vo.getAlias());
                        analyseOriginalJson.setDefaultValue(vo.getDefaultValue());
                        analyseOriginalJson.setIsMust(vo.getIsMust());
                        analyseOriginalJson.setIsEditable(vo.getIsEditable());
                        analyseOriginalJson.setOrderNum(vo.getOrderNum());
                        analyseOriginalJson.setCalculationMode(vo.getCalculationMode());
                        if (StringUtil.isNotNull(vo.getDetectionLimit())) {
                            analyseOriginalJson.setDetectionLimit(vo.getDetectionLimit().toString());
                            isTrue.set(Boolean.TRUE);
                        } else {
                            analyseOriginalJson.setDetectionLimit(null);
                        }
                        jsonList.add(analyseOriginalJson);
                    });
                    p.setJson(JsonStream.serialize(jsonList));
                    if (isTrue.get()) {
                        analyseOriginalRecordList.add(p);
                    }
                }
            }
        });
        analyseOriginalRecordRepository.save(analyseOriginalRecordList);
    }

    @Transactional
    @Override
    public List<DtoWorkSheetFolder> createWorkSheetFolderBatch(DtoWorkSheetCreate dtoWorkSheetCreate) {
        List<String> analyseDataIds = dtoWorkSheetCreate.getAnalyseDataIds();
        String analystId = dtoWorkSheetCreate.getAnalystId();
        String analystName = dtoWorkSheetCreate.getAnalystName();
        Date analyzeTime = StringUtil.isNotNull(dtoWorkSheetCreate.getAnalyzeTime()) ? dtoWorkSheetCreate.getAnalyzeTime() : new Date();
        List<String> testIds = dtoWorkSheetCreate.getTestIds();
        if (StringUtil.isNull(testIds) || testIds.size() == 0) {
            throw new BaseException("请选择相应的测试项目!");
        }
        //查询分析数据，样品，采样单相关数据，将返回结果进行封装
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp(");
        stringBuilder.append("a.id,a.sampleId,b.code,b.redFolderName,b.inspectedEnt,");
        stringBuilder.append("b.samplingTimeBegin,a.testId,a.isQm,b.receiveId,c.projectId,b.sampleTypeId,a.analyseItemId,a.redAnalyzeItemName,");
        stringBuilder.append("a.redCountryStandard,a.redAnalyzeMethodName,a.analyzeMethodId,b.innerReceiveStatus,a.isDataEnabled,a.gatherCode,a.qcInfo)");
        stringBuilder.append(" from DtoAnalyseData a, DtoSample b,DtoReceiveSampleRecord c where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            stringBuilder.append(" and b.orgId = :orgId");
            stringBuilder.append(" and c.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and b.isDeleted = 0 and a.isDeleted = 0 ");
        stringBuilder.append(" and a.sampleId = b.id");
        stringBuilder.append(" and b.receiveId = c.id");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and a.isCompleteField = 0");
        //按岗位分配开关开启时，创建检测单不按照分析人过滤，分析人默认为当前人员
        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            if (StringUtil.isNull(PrincipalContextUser.getPrincipal())) {
                throw new BaseException("当前登录人信息获取出错");
            }
            analystId = PrincipalContextUser.getPrincipal().getUserId();
            analystName = PrincipalContextUser.getPrincipal().getUserName();
        } else {
            stringBuilder.append(" and a.analystId = :analystId");
            values.put("analystId", analystId);
        }
        stringBuilder.append(" and (a.workSheetId = :workSheetId or a.workSheetId = '' or a.workSheetId = null)");
        stringBuilder.append(" and a.dataStatus = :dataStatus "); //数据状态
        stringBuilder.append(" and b.innerReceiveStatus = :innerReceiveStatus "); //样品领样状态
        stringBuilder.append(" and a.testId in :testIds ");


        values.put("workSheetId", UUIDHelper.GUID_EMPTY);
        values.put("dataStatus", EnumPRO.EnumAnalyseDataStatus.未测.getValue());
        values.put("innerReceiveStatus", EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
        values.put("testIds", testIds);

        if (StringUtil.isNotNull(analyseDataIds) && analyseDataIds.size() > 0) {
            stringBuilder.append(" and a.id in :analyseDataIds ");
            values.put("analyseDataIds", analyseDataIds);
        }
        //查询结果封装到临时对象中
        List<DtoAnalyseDataTemp> analyseDataList = comRepository.find(stringBuilder.toString(), values);
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = StringUtil.isNotEmpty(sampleIds) ? sampleService.findBySampleIds(sampleIds) : new ArrayList<>();
        List<OrderReviseVO> orderReviseVOList = getQualityList();
        samples.forEach(sample -> sample.setSortNum(sampleService.getSortOrder(sample, orderReviseVOList)));
        sampleRepository.save(samples);
        //如果数据没有，就不需要创建检测单了
        if (analyseDataList.size() == 0) {
            return new ArrayList<>();
        }

        //找到当前人员上一张同测试项目的工作单
        Optional<String> methodId = testRepository.findByIdIn(testIds).stream().map(DtoTest::getAnalyzeMethodId).findFirst();
        DtoWorkSheetFolder oldfolder = new DtoWorkSheetFolder();
        if (methodId.isPresent()) {
            Optional<DtoWorkSheetFolder> optional = repository.findByAnalystIdAndAnalyzeMethodIdOrderByAnalyzeTimeDesc(analystId, methodId.get()).stream().findFirst();
            if (optional.isPresent()) {
                oldfolder = optional.get();
            }
        }

        List<DtoRecordConfigParams> recordConfigParams = recordConfigParamsConfigService.findTestParamsByTestIds(testIds);

        //去质控任务中判断是否有仪器比对数据
        List<DtoAnalyseDataTemp> qcAnaList = analyseDataList.stream().filter(DtoAnalyseDataTemp::getIsQm).distinct().collect(Collectors.toList());
        if (StringUtil.isNotNull(qcAnaList) && qcAnaList.size() > 0) {//有仪器比对数据

            //剩余的数据
            List<String> qcAnaIds = qcAnaList.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());

            List<String> qcTestIds = qcAnaList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());

            List<DtoWorkSheetFolder> qcWorkSheetFolders = createWorkSheetFolder(qcTestIds, qcAnaList, recordConfigParams, analystId, analystName, analyzeTime, oldfolder);
            List<DtoWorkSheetFolder> workSheetFolders = new ArrayList<>(qcWorkSheetFolders);

            List<DtoAnalyseDataTemp> otherAnaList = analyseDataList.stream().filter(p -> !qcAnaIds.contains(p.getId())).collect(Collectors.toList());

            if (StringUtil.isNotNull(otherAnaList) && otherAnaList.size() > 0) {
                List<String> otherTestIds = otherAnaList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
                workSheetFolders.addAll(createWorkSheetFolder(otherTestIds, otherAnaList, recordConfigParams, analystId, analystName, analyzeTime, oldfolder));
            }
            return workSheetFolders;
        } else {
            return createWorkSheetFolder(testIds, analyseDataList, recordConfigParams, analystId, analystName, analyzeTime, oldfolder);
        }
    }

    @Transactional
    @Override
    public DtoWorkSheetFolder createWorkSheetFolder(DtoWorkSheetCreate dtoWorkSheetCreate) {
        // 系统开关，是否检查领样日期
        checkSampleReceiveDate(dtoWorkSheetCreate.getAnalyseDataIds());
        //校验当前数据是否处在方案申请流程中
        JudgeProjectApprove(dtoWorkSheetCreate.getAnalyseDataIds());
        //当分析方法需要制备时，判断所选样品是否完成制备
        judgeIsPreparation(dtoWorkSheetCreate.getIsPreparation(), dtoWorkSheetCreate.getAnalyseDataIds());
        List<String> testIds = new ArrayList<>();
        testIds.add(dtoWorkSheetCreate.getTestId());
        dtoWorkSheetCreate.setTestIds(testIds);
        List<DtoWorkSheetFolder> workSheetFolders = createWorkSheetFolderBatch(dtoWorkSheetCreate);
        return workSheetFolders.size() > 0 ? workSheetFolders.get(0) : null;
    }

    /**
     * 当分析方法需要制备时，判断所选样品是否完成制备
     *
     * @param isPreParation  是否需要制备
     * @param analyseDataIds 测试项目数据id集合
     */
    private void judgeIsPreparation(Boolean isPreParation, List<String> analyseDataIds) {
        if (StringUtil.isNotNull(isPreParation) && isPreParation) {
            List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(analyseDataIds) ? analyseDataRepository.findAll(analyseDataIds) : new ArrayList<>();
            //获取样品数据
            List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
            //判断是否存在未制备完成的样品（只针对原样）
            sampleList = sampleList.stream().filter(p -> !EnumPRO.EnumPreParedStatus.已制备.getValue().equals(p.getPreparedStatus()) && EnumPRO.EnumSampleCategory.原样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleList)) {
                throw new BaseException("所选样品中包含未制备样品！请检查后重试");
            }
        }
    }

    /**
     * 判断当前数据是否处于方案变更申请流程中
     *
     * @param analyseDataIds 数据id
     */
    private void JudgeProjectApprove(List<String> analyseDataIds) {
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(analyseDataIds) ? analyseDataRepository.findAll(analyseDataIds) : new ArrayList<>();
        //获取样品数据
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
        List<String> projectIds = sampleList.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
        projectApprovalService.checkCondition(projectIds);
    }

    @Transactional
    @Override
    public void saveSortId(String workSheetFolderId, String sortId) {
        DtoWorkSheetFolder workSheetFolder = repository.findOne(workSheetFolderId);
        workSheetFolder.setSortId(sortId);
        this.update(workSheetFolder);
    }

    @Transactional
    @Override
    public DtoWorkSheetFolder createWorkSheetFolderWithSameMethod(DtoWorkSheetCreate dtoWorkSheetCreate) {
        // 系统开关，是否检查领样日期
        checkSampleReceiveDate(dtoWorkSheetCreate.getAnalyseDataIds());
        //校验当前数据是否处在方案申请流程中
        JudgeProjectApprove(dtoWorkSheetCreate.getAnalyseDataIds());
        //当分析方法需要制备时，判断所选样品是否完成制备
        judgeIsPreparation(dtoWorkSheetCreate.getIsPreparation(), dtoWorkSheetCreate.getAnalyseDataIds());
        List<DtoAnalyseData> analyseData = analyseDataRepository.findAll(dtoWorkSheetCreate.getAnalyseDataIds());
        //获取任意一个是项目项目，用来筛选分析方法
        DtoTest test = testRepository.findOne(analyseData.get(0).getTestId());
        //获取所有包含测试项目的样品id
        List<String> sampleIds = analyseData.parallelStream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        //获取样品下的所有测试项目
        List<DtoAnalyseData> analyseDataOfSamples = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        //获取所有测试项目
        List<String> allTestIds = analyseDataOfSamples.parallelStream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        //获取分析方法相同的测试项目
        List<DtoTest> sameMethodTests = testRepository.findAll(allTestIds).parallelStream()
                .filter(t -> t.getAnalyzeMethodId().equals(test.getAnalyzeMethodId())).collect(Collectors.toList());
        List<String> sameMethodTestIds = sameMethodTests.parallelStream().map(DtoTest::getId).distinct().collect(Collectors.toList());

        // 过滤按岗位费分配时，同方法中存在其他岗位的数据
        List<String> finalSameMethodTestIds = new ArrayList<>();
        List<String> analyseDataIds = new ArrayList<>();
        // 根据是否按照岗位分配进行过滤
        filterByPost(finalSameMethodTestIds, analyseDataIds, analyseDataOfSamples, sameMethodTestIds, dtoWorkSheetCreate);
        //将同方法，同分析人员，同样品的数据放到容器中一期生成工作单
        dtoWorkSheetCreate.setTestIds(finalSameMethodTestIds);
        dtoWorkSheetCreate.setAnalyseDataIds(analyseDataIds);
        List<DtoWorkSheetFolder> workSheetFolders = createWorkSheetFolderBatch(dtoWorkSheetCreate);
        return workSheetFolders.size() > 0 ? workSheetFolders.get(0) : null;
    }

    @Transactional
    @Override
    public void joinOnceWorkSheetFolder(DtoWorkSheetJoin workSheetJoin) {
        //校验当前数据是否处在方案申请流程中
        JudgeProjectApprove(workSheetJoin.getAnalyseDataIds());
        //当分析方法需要制备时，判断所选样品是否完成制备
        judgeIsPreparation(workSheetJoin.getIsPreparation(), workSheetJoin.getAnalyseDataIds());
        //检测单id
        String workSheetFolderId = workSheetJoin.getWorkSheetFolderId();

        DtoWorkSheetFolder dtoWorkSheetFolder = repository.findOne(workSheetFolderId);
        List<DtoLog> logs = new ArrayList<>();
        if (StringUtil.isNotNull(dtoWorkSheetFolder)) {
            //数据ids
            List<String> analyseDataIds = workSheetJoin.getAnalyseDataIds();

            if (analyseDataIds.size() > 0) {
                List<DtoAnalyseData> analyseDataList = analyseDataRepository.findAll(analyseDataIds);

                //寻找样品ids
                List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());

                List<DtoSample> samples = new ArrayList<>();
                List<DtoReceiveSampleRecord> receiveSampleRecords = new ArrayList<>();
                if (sampleIds.size() > 0) {
                    samples = sampleService.findBySampleIds(sampleIds);
                    List<OrderReviseVO> orderReviseVOList = getQualityList();
                    samples.forEach(sample -> sample.setSortNum(sampleService.getSortOrder(sample, orderReviseVOList)));
                    sampleRepository.save(samples);
                    //送样单ids
                    List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).collect(Collectors.toList());
                    if (receiveIds.size() > 0) { //送样单ids
                        receiveSampleRecords = receiveSampleRecordService.findAll(receiveIds);
                    }
                }
                //判断"是否同方法批量加入"开关是否开启,开启时将所选样品中同方法的分析数据找出来，统一加到选定的检测单中
                if (StringUtil.isNotNull(workSheetJoin.getIsSameMethodBatchAdd()) && workSheetJoin.getIsSameMethodBatchAdd()) {
                    DtoTest anyTest = testRepository.findOne(analyseDataList.get(0).getTestId());
                    String analyzeMethodId = StringUtil.isNotNull(anyTest) ? anyTest.getAnalyzeMethodId() : UUIDHelper.GUID_EMPTY;
                    //获取所选分析数据对应的样品下的所有分析数据列表
                    List<DtoAnalyseData> analyseDataOfSampleList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
                    //获取分析数据对应的所有测试项目
                    List<String> allTestIdList = analyseDataOfSampleList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
                    //获取分析方法相同的测试项目
                    List<DtoTest> sameMethodTestList = testRepository.findAll(allTestIdList).stream()
                            .filter(p -> p.getAnalyzeMethodId().equals(analyzeMethodId)).collect(Collectors.toList());
                    List<String> sameMethodTestIdList = sameMethodTestList.stream().map(DtoTest::getId).distinct().collect(Collectors.toList());
                    //获取分析方法相同，分析人员相同的分析数据
                    analyseDataList = analyseDataOfSampleList.stream().filter(a -> sameMethodTestIdList.contains(a.getTestId())
                            && a.getAnalystId().equals(dtoWorkSheetFolder.getAnalystId())).collect(Collectors.toList());
                    //过滤掉已经创建检测单的数据
                    analyseDataList = analyseDataList.stream().filter(p -> p.getWorkSheetId().equals(UUIDHelper.GUID_EMPTY)
                            || StringUtil.isEmpty(p.getWorkSheetId())).collect(Collectors.toList());
                }

                //测试项目id
                List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());

                List<DtoRecordConfigParams> recordConfigParams = StringUtil.isNotEmpty(testIds)
                        ? recordConfigParamsConfigService.findTestParamsByTestIds(testIds) : new ArrayList<>();

                List<DtoTest> dtoTests = testService.findRedisByIds(testIds);

                //子检测单数据
                List<DtoWorkSheet> dtoWorkSheets = workSheetRepository.findByParentId(workSheetFolderId);

                //样品数据是否存储
                Map<String, DtoSample> sampleIdMap = new HashMap<>();

                //项目与检测单信息
                List<DtoProject2WorkSheetFolder> project2WorkSheetFolders = project2WorkSheetFolderRepository.findByWorkSheetFolderId(workSheetFolderId);

                List<DtoProject2WorkSheetFolder> newProject2WorkSheetFolders = new ArrayList<>();

                for (DtoTest dtoTest : dtoTests) {
                    List<DtoParamsData> paramsDataList = new ArrayList<>();
                    List<String> codes = new ArrayList<>();
                    String analyseItemId = dtoTest.getAnalyzeItemId();//分析项目id
                    String redAnalyzeItemName = dtoTest.getRedAnalyzeItemName();//分析项目名称
                    String redAnalyzeMethodName = dtoTest.getRedAnalyzeMethodName();//方法名称
                    String redCountryStandard = dtoTest.getRedCountryStandard();//标准
                    String analyzeMethodId = dtoTest.getAnalyzeMethodId();//方法id
                    String workSheetId = "";//检测单id
                    Optional<DtoWorkSheet> optional = dtoWorkSheets.stream().filter(p -> p.getTestId().equals(dtoTest.getId())).findFirst();
                    Map<String, DtoWorkSheet> dtoWorkSheetMap = new HashMap<>();
                    List<DtoAnalyseData> anaList = analyseDataList.stream().filter(p -> p.getTestId().equals(dtoTest.getId())).collect(Collectors.toList());
                    if (optional.isPresent()) { //说明这个检测单存在该测试项目
                        DtoWorkSheet dtoWorkSheet = optional.get();
                        workSheetId = dtoWorkSheet.getId();
                    } else { // 如果不存在，则新增一个小的工作单信息
                        DtoWorkSheet dtoWorkSheet = new DtoWorkSheet();
                        dtoWorkSheet.setTestId(dtoTest.getId());
                        dtoWorkSheet.setRedAnalyzeItemName(redAnalyzeItemName);
                        dtoWorkSheet.setAnalyseItemId(analyseItemId);
                        dtoWorkSheet.setParentId(workSheetFolderId);
                        String testId = dtoTest.getId();
                        List<DtoRecordConfigParams> recordConfigParamsList = recordConfigParams.stream().filter(p -> testId.equals(p.getTestId())).collect(Collectors.toList());//参数
                        String recordId = recordConfigParamsList.size() > 0 ? recordConfigParamsList.get(0).getRecordId() : UUIDHelper.GUID_EMPTY;
                        dtoWorkSheet.setRecordId(recordId);
                        //如果存在多个原始记录单配置，需要确定用的是哪个原始记录单表头参数
                        recordConfigParamsList = recordConfigParamsList.stream().filter(p -> recordId.equals(p.getRecordId())).collect(Collectors.toList());
                        workSheetId = dtoWorkSheet.getId();
                        paramsDataList.addAll(createWorkSheetParams(recordConfigParamsList, dtoWorkSheet.getId()));
                        dtoWorkSheetMap.put(workSheetId, dtoWorkSheet);
                    }
                    Boolean isSaveWorkSheet = false;
                    if (StringUtils.isNotNullAndEmpty(workSheetId)) { //小的workSheetId才做
                        for (DtoAnalyseData dtoAnalyseData : anaList) {
                            //没有检测单的时候才加入
                            if (dtoAnalyseData.getWorkSheetId().equals(UUIDHelper.GUID_EMPTY) || !StringUtils.isNotNullAndEmpty(dtoAnalyseData.getWorkSheetId())) {
                                DtoSample sample = samples.stream().filter(p -> p.getId().equals(dtoAnalyseData.getSampleId())).findFirst().orElse(null);
                                dtoAnalyseData.setGatherCode(sample.getCode());

                                dtoAnalyseData.setWorkSheetFolderId(workSheetFolderId);
                                dtoAnalyseData.setWorkSheetId(workSheetId);
                                dtoAnalyseData.setRedAnalyzeItemName(redAnalyzeItemName);
                                dtoAnalyseData.setRedAnalyzeMethodName(redAnalyzeMethodName);
                                dtoAnalyseData.setRedCountryStandard(redCountryStandard);
                                dtoAnalyseData.setAnalyseItemId(analyseItemId);
                                dtoAnalyseData.setAnalyzeTime(dtoWorkSheetFolder.getAnalyzeTime());
                                dtoAnalyseData.setAnalyzeMethodId(analyzeMethodId);
                                dtoAnalyseData.setAnalystId(dtoWorkSheetFolder.getAnalystId());
                                dtoAnalyseData.setAnalystName(dtoWorkSheetFolder.getAnalystName());
                                dtoAnalyseData.setStatus(EnumPRO.EnumAnalyseDataStatus.在测.name());
                                dtoAnalyseData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.在测.getValue());
                                comRepository.merge(dtoAnalyseData); //直接修改
                                DtoSample dtoSample = new DtoSample();
                                dtoSample.setId(dtoAnalyseData.getSampleId());
                                dtoSample.setStatus(EnumPRO.EnumSampleStatus.样品在检.name());
                                dtoSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.正在分析.getValue());
                                dtoSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                                if (!sampleIdMap.containsKey(dtoAnalyseData.getSampleId())) {
                                    comRepository.merge(dtoSample);//直接修改
                                    sampleIdMap.put(dtoAnalyseData.getSampleId(), dtoSample);
                                }
                                isSaveWorkSheet = true;
                                //DtoSample sample = samples.stream().filter(p -> p.getId().equals(dtoAnalyseData.getSampleId())).findFirst().orElse(null);
                                if (StringUtil.isNotNull(sample)) {
                                    DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecords.stream().filter(p -> p.getId().equals(sample.getReceiveId())).findFirst().orElse(null);
                                    if (StringUtil.isNotNull(receiveSampleRecord)) {
                                        String projectId = receiveSampleRecord.getProjectId();
                                        DtoProject2WorkSheetFolder dtoProject2WorkSheetFolder = project2WorkSheetFolders.stream().filter(p -> p.getWorkSheetFolderId().equals(workSheetFolderId) && p.getProjectId().equals(projectId)).findFirst().orElse(null);
                                        if (StringUtil.isNull(dtoProject2WorkSheetFolder)) {
                                            dtoProject2WorkSheetFolder = new DtoProject2WorkSheetFolder();
                                            dtoProject2WorkSheetFolder.setWorkSheetFolderId(workSheetFolderId);
                                            dtoProject2WorkSheetFolder.setProjectId(projectId);
                                            newProject2WorkSheetFolders.add(dtoProject2WorkSheetFolder);//作为新增数据
                                            project2WorkSheetFolders.add(dtoProject2WorkSheetFolder); //防止数据重复
                                        }
                                    }
                                    codes.add(sample.getCode());
                                }
                            }
                        }
                        if (isSaveWorkSheet) {
                            DtoWorkSheet workSheet = dtoWorkSheetMap.get(workSheetId);
                            if (StringUtil.isNotNull(workSheet)) {
                                workSheetRepository.save(workSheet);
                            }
                            if (StringUtil.isNotEmpty(paramsDataList)) {
                                comRepository.insertBatch(paramsDataList);
                            }
                            if (newProject2WorkSheetFolders.size() > 0) {
                                comRepository.insertBatch(newProject2WorkSheetFolders);
                                List<DtoLog> projectWorkSheetLogList = new ArrayList<>();
                                for (DtoProject2WorkSheetFolder p2w : project2WorkSheetFolders) {
                                    DtoLog log = new DtoLog();
                                    log.setId(UUIDHelper.NewID());
                                    log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                                    log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                                    log.setOperateTime(new Date());
                                    log.setOperateInfo(EnumPRO.EnumLogOperateType.修改检测单.toString());
                                    log.setLogType(EnumPRO.EnumLogType.项目检测单.getValue());
                                    log.setObjectId(p2w.getProjectId());
                                    log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
                                    log.setComment("加入了检测单：" + dtoWorkSheetFolder.getWorkSheetCode());
                                    log.setOpinion("");
                                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                                    log.setRemark("");
                                    projectWorkSheetLogList.add(log);
                                }
                                newLogService.createLog(projectWorkSheetLogList, EnumPRO.EnumLogType.项目检测单.getValue());
                            }
                        } else {
                            throw new BaseException("选择的数据已经加入过检测单了，无法再次加入请确认!");
                        }
                        String comment = String.format("将样品:%s的检测项目:%s加入检测单。", String.join(",", codes), dtoTest.getRedAnalyzeItemName());
                        DtoLog workSheetLog = new DtoLog();
                        workSheetLog.setComment(comment);
                        workSheetLog.setLogType(EnumPRO.EnumLogType.检测单增删样品.getValue());
                        workSheetLog.setObjectId(dtoWorkSheetFolder.getId());
                        workSheetLog.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                        workSheetLog.setOperateInfo(EnumPRO.EnumLogOperateType.修改检测单.name());
                        logs.add(workSheetLog);
                    }
                }
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单增删样品.getValue());

                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.加入检测单, "", "", Collections.singletonList(dtoWorkSheetFolder.getAnalystId()));
                            }
                        }
                );
            }
        }
    }

    @Override
    public List<DtoWorkSheetProperty> openWorkSheetAllAnalyseDataById(String workSheetFolderId) {
        return openWorkSheetById(workSheetFolderId, UUIDHelper.GUID_EMPTY, true);
    }

    @Override
    public List<DtoWorkSheetProperty> openWorkSheetAnalyseDataById(String workSheetFolderId) {
        return openWorkSheetById(workSheetFolderId, UUIDHelper.GUID_EMPTY, false);
    }

    @Override
    public List<DtoWorkSheetProperty> openWorkSheetAnalyseDataById(String workSheetFolderId, String sampleId, Boolean isAll) {
        return openWorkSheetById(workSheetFolderId, sampleId, isAll);
    }

    @Override
    public List<DtoWorkSheetProperty> getIsSample(String workSheetFolderId, String sampleId, Boolean isAll) {
        List<DtoWorkSheetProperty> workSheetProperties = new ArrayList<>();
        DtoWorkSheetFolder dtoWorkSheetFolder = repository.findOne(workSheetFolderId);
        Boolean isSample = false;
        //防止数据已删除程序出错，需要判断是否为null
        List<DtoSample> sortedSampleList = new ArrayList<>();
        List<OrderReviseVO> orderReviseVOList = getQualityList();
        if (StringUtil.isNotNull(dtoWorkSheetFolder)) {
            DtoAnalyzeMethod method = analyzeMethodService.findOne(dtoWorkSheetFolder.getAnalyzeMethodId());
            //是否按照样品录入(选择按照样品录入的时候，可同时完成一定是否)
            isSample = method.getIsInputBySample();
            //所有的分析数据集
            List<DtoAnalyseDataTemp> analyseDataTemps = findDataByWorksheetFolderId(Boolean.FALSE, Boolean.FALSE, workSheetFolderId);
            this.setGroupSampleId(analyseDataTemps);
            //找到所有的样品(排除替代样)
            List<String> sIds = analyseDataTemps.stream().filter(p -> !p.getQcType().equals(new QualityReplace().qcTypeValue()))
                    .map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> sampleList = sampleService.findBySampleIds(sIds);
//            Map<String, String> codeMap = analyseDataTemps.stream().collect(Collectors.groupingBy(DtoAnalyseDataTemp::getSampleId,
//                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSampleCode())));

            Date start = new Date();
//            sampleList.forEach(p -> {
//                if (!StringUtil.isNotEmpty(p.getSortOrder())) {
//                    Date s1 = new Date();
//                    p.setSortOrder(getSortNumber(p, analyseDataTemps, codeMap));
//                    Date s2 = new Date();
//                    System.out.println(String.format("......样品: %s 排序耗时 %d 毫秒.....", p.getCode(), (s2.getTime() - s1.getTime())));
//                }
//            });

            List<Future<Map<String, String>>> asyncResultList = new ArrayList<>();
            List<DtoSample> list = null;
            final int batchSize = 50;
            for (DtoSample dto : sampleList) {
                if (list == null) {
                    list = new ArrayList<>();
                }
                if (list.size() < batchSize) {
                    list.add(dto);
                } else if (list.size() == batchSize) {
                    //多线程处理排序
                    asyncResultList.add(sampleOrderService.initSampleOrderNum(list, analyseDataTemps, orderReviseVOList));
                    list = new ArrayList<>();
                    list.add(dto);
                }
            }
            //如果存在最后一批样，需要单独去排序处理
            if (StringUtil.isNotEmpty(list)) {
                asyncResultList.add(sampleOrderService.initSampleOrderNum(list, analyseDataTemps, orderReviseVOList));
            }
            //处理多线程处理的结果
            try {
                for (Future<Map<String, String>> asyncResult : asyncResultList) {
                    while (true) {
                        if (asyncResult.isDone() && !asyncResult.isCancelled()) {
                            Map<String, String> map = asyncResult.get();
                            if (StringUtil.isNotEmpty(map)) {
                                for (Map.Entry<String, String> entry : map.entrySet()) {
                                    Optional<DtoSample> optional = sampleList.stream().filter(p -> p.getId().equals(entry.getKey()))
                                            .findFirst();
                                    optional.ifPresent(p -> p.setSortOrder(entry.getValue()));
                                }
                            }
                            break;
                        } else {
                            //防止CPU高速轮询被耗空
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BaseException("......多线程处理工作单下样品排序出错......");
            }

            Date end = new Date();
            log.debug("-------------样品排序总共耗时: " + (end.getTime() - start.getTime()) + "毫秒");
            sortedSampleList.forEach(sample -> {
                if (analyseDataTemps.stream().filter(a -> a.getSampleId().equals(sample.getId())).allMatch(a -> StringUtils.isNotNullAndEmpty(a.getTestValue()))) {
                    sample.setDataFlag(true);
                } else {
                    sample.setDataFlag(false);
                }
            });

            sortedSampleList = sampleList.stream().sorted(Comparator.comparing(DtoSample::getSortOrder)
                    .thenComparing(DtoSample::getCode)).collect(Collectors.toList());
        }
        DtoWorkSheetProperty property = new DtoWorkSheetProperty();
        property.setSamples(sortedSampleList);
        property.setIsSample(isSample);
        workSheetProperties.add(property);
        return workSheetProperties;
    }

    @Override
    public void findAnalyseDataByPage(PageBean<DtoAnalyseDataTemp> pageBean, BaseCriteria baseCriteria) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select new com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp(");
        stringBuilder.append("a.id,a.analyzeTime,a.dataInputTime,a.requireDeadLine,a.dimension,a.dimensionId,a.examLimitValue,a.dataStatus,");
        stringBuilder.append("a.qcId,a.qcType,a.qcGrade,a.isQm,a.mostDecimal,a.mostSignificance,");
        stringBuilder.append("a.testId,a.redAnalyzeItemName,a.analyseItemId,a.analyzeMethodId,a.redAnalyzeMethodName,a.redCountryStandard,");
        stringBuilder.append("a.testValue,a.testOrignValue,a.testValueDstr,a.testValueD,a.workSheetId,a.workSheetFolderId,");
        stringBuilder.append("a.grade,b.inspectedEnt,b.inspectedEntId,b.redFolderName,b.samplingTimeBegin,b.code,b.cycleOrder,");
        stringBuilder.append("b.timesOrder,b.sampleTypeId,c.parentId,b.receiveId,b.id,c.typeName,b.sampleCategory,b.associateSampleId," +
                "a.isDataEnabled,a.gatherCode,a.qcInfo,a.seriesValue,a.isSci,a.pxAverageValue)");
        pageBean.setEntityName("DtoAnalyseData a, DtoSample b,DtoSampleType c");
        pageBean.setSelect(stringBuilder.toString());
        comRepository.findByPage(pageBean, baseCriteria);
    }

    private List<DtoAnalyseDataTemp> findDataByWorksheetFolderId(Boolean isOutsourcing, Boolean isCompleteField, String workSheetFolderId) {
        //创建查询sql语句
        StringBuilder stringBuilder = new StringBuilder("select a.id,a.analyzeTime,a.dataInputTime,a.requireDeadLine,a.dimension,a.dimensionId," +
                "a.examLimitValue,a.dataStatus,a.qcId,a.qcType,a.qcGrade,a.isQm,a.mostDecimal,a.mostSignificance," +
                "a.testId,a.redAnalyzeItemName,a.analyseItemId,a.analyzeMethodId,a.redAnalyzeMethodName,a.redCountryStandard," +
                "a.testValue,a.testOrignValue,a.testValueDstr,a.testValueD,a.workSheetId,a.workSheetFolderId," +
                "a.grade,b.inspectedEnt,b.inspectedEntId,b.redFolderName,b.samplingTimeBegin,b.code,b.cycleOrder," +
                "b.timesOrder,b.sampleTypeId,c.parentId,b.receiveId,a.sampleId,c.typeName,b.sampleCategory,b.associateSampleId," +
                "a.isDataEnabled,a.gatherCode,a.qcInfo,a.seriesValue,a.isSci, a.pxAverageValue,a.lowerLimit from");
        stringBuilder.append(" TB_PRO_AnalyseData a,TB_PRO_Sample b,TB_BASE_SampleType c");
        stringBuilder.append(" where 1=1 and b.isDeleted = 0 and a.isDeleted = 0 and b.sampleTypeId = c.id");
        stringBuilder.append(" and a.isOutsourcing = ? and a.isCompleteField = ? and a.workSheetFolderId = ?");
        stringBuilder.append(" and a.sampleId = b.id ");
        //执行sql语句
        Object[] values = new Object[]{isOutsourcing, isCompleteField, workSheetFolderId};
        return jdbcTemplate.query(stringBuilder.toString(), values, (resultSet, i) -> new DtoAnalyseDataTemp(resultSet.getString("id"),
                new Date(resultSet.getTimestamp("analyzeTime").getTime()), new Date(resultSet.getTimestamp("dataInputTime").getTime()),
                new Date(resultSet.getTimestamp("requireDeadLine").getTime()),
                resultSet.getString("dimension"), resultSet.getString("dimensionId"), resultSet.getString("examLimitValue"),
                resultSet.getInt("dataStatus"), resultSet.getString("qcId"), resultSet.getInt("qcType"),
                resultSet.getInt("qcGrade"), resultSet.getBoolean("isQm"), resultSet.getInt("mostDecimal"),
                resultSet.getInt("mostSignificance"), resultSet.getString("testId"), resultSet.getString("redAnalyzeItemName"),
                resultSet.getString("analyseItemId"), resultSet.getString("analyzeMethodId"), resultSet.getString("redAnalyzeMethodName"),
                resultSet.getString("redCountryStandard"), resultSet.getString("testValue"), resultSet.getString("testOrignValue"),
                resultSet.getString("testValueDstr"), resultSet.getBigDecimal("testValueD"), resultSet.getString("workSheetId"),
                resultSet.getString("workSheetFolderId"), resultSet.getInt("grade"), resultSet.getString("inspectedEnt"),
                resultSet.getString("inspectedEntId"), resultSet.getString("redFolderName"), new Date(resultSet.getTimestamp("samplingTimeBegin").getTime()),
                resultSet.getString("code"), resultSet.getInt("cycleOrder"), resultSet.getInt("timesOrder"),
                resultSet.getString("sampleTypeId"), resultSet.getString("parentId"), resultSet.getString("receiveId"),
                resultSet.getString("sampleId"), resultSet.getString("typeName"), resultSet.getInt("sampleCategory"),
                resultSet.getString("associateSampleId"), resultSet.getBoolean("isDataEnabled"), resultSet.getString("gatherCode"),
                resultSet.getString("qcInfo"), resultSet.getString("seriesValue"), resultSet.getBoolean("isSci"),
                resultSet.getString("pxAverageValue"), resultSet.getString("lowerLimit")
        ));
    }

    @Override
    public DtoWorkSheetProperty changeAnalyseDataFormula(DtoAnalyseDataChangeFormula dtoAnalyseDataChangeFormula) {

        //公式
        String formulaId = dtoAnalyseDataChangeFormula.getFormulaId();
        //相应的分析数据
        List<String> analyseDataIds = dtoAnalyseDataChangeFormula.getAnalyzeDataIds();

        //相应的公式数据
        DtoParamsFormula paramsFormula = paramsFormulaService.findOne(formulaId);

        String formula = paramsFormula.getFormula();

        //已经保存过的数据
        List<DtoAnalyseOriginalRecord> analyseOriginalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIds);

        PageBean<DtoAnalyseDataTemp> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        WorkSheetCriteria workSheetCriteria = new WorkSheetCriteria();

        workSheetCriteria.setAnalyzeDataIds(analyseDataIds);

        findAnalyseDataByPage(pageBean, workSheetCriteria);

        //查询出的数据
        List<DtoAnalyseDataTemp> analyseDataTemps = pageBean.getData();
        this.setGroupSampleId(analyseDataTemps);

        Map<String, String> codeMap = analyseDataTemps.stream().
                collect(Collectors.groupingBy(DtoAnalyseDataTemp::getSampleId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSampleCode())));

        //小型的工作ids
        List<String> workSheetIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getWorkSheetId).distinct().collect(Collectors.toList());//小型的检测单Id

        String workSheetFolderId = "";

        //所有的测试项目ids
        List<String> testIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());

        List<DtoReceiveSampleRecordTemp> receiveSampleRecords = new ArrayList<>(); //主要是用来获取质控任务的相关数据
        List<String> qmAnaIds = analyseDataTemps.stream().filter(p -> StringUtil.isNotNull(p.getIsQm()) && p.getIsQm()).map(DtoAnalyseDataTemp::getId).collect(Collectors.toList());
        List<DtoQualityManage> qualityManages = new ArrayList<>();
        if (qmAnaIds.size() > 0) {
            //送样单ids
            List<String> receiveIds = analyseDataTemps.stream().filter(p -> qmAnaIds.contains(p.getId())).map(DtoAnalyseDataTemp::getReceiveId).distinct().collect(Collectors.toList());
            receiveSampleRecords = receiveSampleRecordService.findReceiveSampleRecordList(receiveIds);
            qualityManages = qualityManageRepository.findByAnaIdIn(qmAnaIds);
        }

        //测试项目下所有的公式（这里只管找相关的公式数据，排除假删即可，不需要考虑是否启用公式，因为这是人为去更改公式的）
        List<DtoParamsFormula> formulaList = new ArrayList<>();
        List<DtoParamsTestFormula> paramsTestFormulas = new ArrayList<>();
        if (testIds.size() > 0) {
            formulaList = paramsFormulaService.findByObjectIds(testIds);
            //所有的公式ids
            List<String> fIds = formulaList.stream().map(DtoParamsFormula::getId).distinct().collect(Collectors.toList());
            paramsTestFormulas = paramsTestFormulaService.findByObjIds(fIds);
        }
        //找到质控相应的数据
        List<String> qcIds = analyseDataTemps.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getQcId()) && !p.getQcId().equals(UUIDHelper.GUID_EMPTY))
                .map(DtoAnalyseDataTemp::getQcId).distinct().collect(Collectors.toList());
        List<DtoQualityControl> qualityControls = new ArrayList<>();
        //qualityControls的关联样品id与样品对象的映射关系
        Map<String, DtoSample> asoSamId2SamMap = new HashMap<>();
        if (qcIds.size() > 0) {
            qualityControls = qualityControlService.findAll(qcIds);
            List<String> associateSampleIdList = qualityControls.stream().map(DtoQualityControl::getAssociateSampleId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(associateSampleIdList)) {
                List<DtoSample> associateSampleList = sampleRepository.findByIds(associateSampleIdList);
                asoSamId2SamMap = associateSampleList.stream().collect(Collectors.toMap(DtoSample::getId, sample -> sample));
            }
        }
        //寻找公式上的参数数据，只有有公式或者录入过公式数据的才进行匹配
        List<DtoParamsData> sampleParamsDataList = new ArrayList<>();
        List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves = new ArrayList<>();
        if (analyseOriginalRecords.size() > 0 || formulaList.size() > 0) {
            List<String> sampleIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
            sampleParamsDataList = paramsDataService.findObjectIdsAndObjectType(sampleIds, EnumPRO.EnumParamsDataType.样品.getValue());
            //只有公式中参数有k和b才查询
            workSheetCalibrationCurves = getWorkSheetCalibrationCurve(paramsTestFormulas, workSheetIds);
        }
        List<Map<String, Object>> anaMapList = new ArrayList<>();
        List<DtoAnalyseOriginalRecord> analyseOriginalRecordList = new ArrayList<>();
        Map<String, List<DtoTestFormulaParamsConfig>> testFormulaParamsConfigMap = new HashMap<>();
        String oldFormula = "";//;老的公式
        List<String> sampleTypeIdForTemps = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIdForTemps) ? sampleTypeService.findAll(sampleTypeIdForTemps) : new ArrayList<>();
        List<String> allGroupIds = sampleTypeList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFieldTaskGroupId())
                && !p.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getFieldTaskGroupId)
                .distinct().collect(Collectors.toList());
        List<DtoSampleTypeGroup> allSampleTypeGroups = StringUtil.isNotEmpty(allGroupIds)
                ? sampleTypeGroupRepository.findByParentIdInAndGroupType(allGroupIds, EnumLIM.EnumGroupType.分组.getValue()) : new ArrayList<>();
        List<DtoSampleTypeGroup2Test> allGroup2TestList = StringUtil.isNotEmpty(allSampleTypeGroups)
                ? sampleTypeGroup2TestService.findBySampleTypeGroupIds(allSampleTypeGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList())) : new ArrayList<>();
        Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, dto -> dto));
        for (DtoAnalyseDataTemp dtoAnalyseDataTemp : analyseDataTemps) {
            if (!StringUtils.isNotNullAndEmpty(workSheetFolderId)) {
                workSheetFolderId = dtoAnalyseDataTemp.getWorkSheetFolderId();
            }
            String anaId = dtoAnalyseDataTemp.getId();
            String testId = dtoAnalyseDataTemp.getTestId();
            String newFormulaId = formulaId;
            //判断是否是同一个测试项目，如果不是新的公式id需要对应新的测试项目
            if (!paramsFormula.getObjectId().equals(testId)) {
                Optional<DtoParamsFormula> optionalParamsFormula = formulaList.stream().filter(p -> p.getId().equals(formula) && p.getObjectId().equals(testId))
                        .max(Comparator.comparing(DtoParamsFormula::getConfigDate));
                if (optionalParamsFormula.isPresent()) {
                    newFormulaId = optionalParamsFormula.get().getId();
                }
            }
            //获取旧的当前数据的原始记录数据
            Optional<DtoAnalyseOriginalRecord> optionalOriginalRecord = analyseOriginalRecords.stream()
                    .filter(p -> p.getAnalyseDataId().equals(anaId)).findFirst();
            //说明数据存在，先判断公式是否改变
            List<DtoParamsTestFormula> paramsTestFormulaList = new ArrayList<>();
            //是否有原始数据
            Boolean isOriginal = false;
            //公式是否改变
            Boolean isChangeFormula = true;
            DtoAnalyseOriginalRecord dtoAnalyseOriginalRecord = new DtoAnalyseOriginalRecord();
            String oldJson = "";
            if (optionalOriginalRecord.isPresent()) {
                TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
                };
                DtoAnalyseOriginalRecord analyseOriginalRecord = optionalOriginalRecord.get();
                oldFormula = analyseOriginalRecord.getTestFormula();
                oldJson = analyseOriginalRecord.getJson();
                //新的公式参数
                String nfId = newFormulaId;
                List<DtoParamsTestFormula> lastNewParamsTestFormula = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(nfId)).collect(Collectors.toList());
                //说明公式未改变，只需要验证参数是否变化--那为什么不用id判断呢？
                if (nfId.equals(analyseOriginalRecord.getTestFormulaId())) {
                    newFormulaId = analyseOriginalRecord.getTestFormulaId();
                    //老的公式参数
                    List<DtoParamsTestFormula> oldParamsTestFormula = JsonIterator.deserialize(analyseOriginalRecord.getJson(), typeLiteral);

                    Set<String> lastAlias = lastNewParamsTestFormula.stream().map(DtoParamsTestFormula::getAlias).collect(Collectors.toSet());

                    Set<String> olAlias = oldParamsTestFormula.stream().map(DtoParamsTestFormula::getAlias).collect(Collectors.toSet());
                    //取差集
                    lastAlias.removeAll(olAlias);
//                    说明完全一致，不需要进行处理
//                    if (lastNewParamsTestFormula.size() == oldParamsTestFormula.size() && lastAlias.size() == 0) {
//                        paramsTestFormulaList = oldParamsTestFormula;
//                    } else { //说明参数有不一致的地方
                    for (DtoParamsTestFormula srcParamsTestFormula : lastNewParamsTestFormula) {
                        DtoParamsTestFormula targetParamsTestFormula = new DtoParamsTestFormula();
                        BeanUtils.copyProperties(srcParamsTestFormula, targetParamsTestFormula);
                        paramsTestFormulaList.add(targetParamsTestFormula);
                    }
                    for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulaList) {
                        DtoParamsTestFormula old = oldParamsTestFormula.stream().filter(p -> p.getAlias().equals(paramsTestFormula.getAlias())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(old) && StringUtil.isNotEmpty(old.getDefaultValue())) {
                            //把值更新成老的
                            paramsTestFormula.setDefaultValue(old.getDefaultValue());
                        }
                    }
//                    for (DtoParamsTestFormula paramsTestFormula : lastNewParamsTestFormula) {
//                        DtoParamsTestFormula old = oldParamsTestFormula.stream().filter(p -> p.getAlias().equals(paramsTestFormula.getAlias())).findFirst().orElse(null);
//                        if (StringUtil.isNotNull(old)) {
//                            //把值更新成老的
//                            paramsTestFormula.setDefaultValue(old.getDefaultValue());
//                        }
//                    }
//                    //把最新的赋值给变量
//                    paramsTestFormulaList = lastNewParamsTestFormula;
//                    }
                    isChangeFormula = false;
                    isOriginal = true;//只有在公式相等的情况下，才算有原始数据，否则要重新匹配
                }
                dtoAnalyseOriginalRecord.setId(analyseOriginalRecord.getId());
            }

            if (isChangeFormula) {
                String nfId = newFormulaId;
                Optional<DtoParamsFormula> optionalParamsFormula = formulaList.stream().filter(p -> p.getId().equals(nfId) && p.getObjectId().equals(testId))
                        .max(Comparator.comparing(DtoParamsFormula::getConfigDate));
                if (optionalParamsFormula.isPresent()) { //是否配置公式，一般在调用公式的时候，已经判断是否有相同公式了，所以这边肯定会执行的
                    DtoParamsFormula dtoParamsFormula = optionalParamsFormula.get();
                    newFormulaId = dtoParamsFormula.getId();
                    paramsTestFormulaList = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(dtoParamsFormula.getId())).collect(Collectors.toList());
                } else { //否则用传过来的公式配置
                    paramsTestFormulaList = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(formulaId)).collect(Collectors.toList());
                }
            }
            dtoAnalyseDataTemp.setFormula(formula);
            dtoAnalyseDataTemp.setFormulaId(newFormulaId);

            getAnalyseDataTemp(dtoAnalyseDataTemp, qualityControls, receiveSampleRecords, qualityManages, asoSamId2SamMap);
            Map<String, Object> map = getAnalyseDataMap(analyseDataTemps, dtoAnalyseDataTemp, codeMap);

            Boolean isRed = false;

            List<DtoAnalyseOriginalJson> analyseOriginalJsons = new ArrayList<>();
            if (StringUtils.isNotNullAndEmpty(formula)) {
                List<DtoTestFormulaParamsConfig> dtoTestFormulaParamsConfigList = new ArrayList<>();
                DtoSolutionCalibration solutionCalibration = solutionCalibrationRepository.findByWorkSheetFolderId(workSheetFolderId);
                //获取到公式参数
                isRed = getParamsTestFormula(paramsTestFormulaList, sampleParamsDataList, workSheetCalibrationCurves,
                        dtoTestFormulaParamsConfigList, formula, isOriginal, dtoAnalyseDataTemp, map, analyseOriginalJsons,
                        sampleTypeMap, allSampleTypeGroups, allGroup2TestList, solutionCalibration);
                testFormulaParamsConfigMap.put(formula, dtoTestFormulaParamsConfigList);
                //如果修改后的新公式参数包含原来旧公式中的参数，则旧公式参数的数据值赋值给新公式参数
                if (isChangeFormula && StringUtil.isNotEmpty(oldJson)) {
                    TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
                    };
                    List<DtoAnalyseOriginalJson> oldAnalyseOriginalJsons = JsonIterator.deserialize(oldJson, typeLiteral);
                    for (DtoAnalyseOriginalJson oldAnalyseOriginalJson : oldAnalyseOriginalJsons) {
                        //把新公式中包含的旧公式参数数据放进去
                        for (DtoAnalyseOriginalJson analyseOriginalJson : analyseOriginalJsons) {
                            if (analyseOriginalJson.getAlias().contains(oldAnalyseOriginalJson.getAlias())) {
                                analyseOriginalJson.setDefaultValue(oldAnalyseOriginalJson.getDefaultValue());
                            }
                        }
                        //方法返回中放入值，用于前端回显
                        if (map.containsKey(oldAnalyseOriginalJson.getAlias())) {
                            map.put(oldAnalyseOriginalJson.getAlias(), oldAnalyseOriginalJson.getDefaultValue());
                        }
                    }
                }
            }
            //数据不一致标红
            map.put("isRedSampleCode", isRed);
            anaMapList.add(map);
            dtoAnalyseOriginalRecord.setTestFormulaId(newFormulaId);
            dtoAnalyseOriginalRecord.setTestFormula(formula);
            dtoAnalyseOriginalRecord.setAnalyseDataId(anaId);
            dtoAnalyseOriginalRecord.setJson(JsonStream.serialize(analyseOriginalJsons));
            analyseOriginalRecordList.add(dtoAnalyseOriginalRecord);
        }
        List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs = new ArrayList<>();
        if (testFormulaParamsConfigMap.containsKey(formula)) {
            testFormulaParamsConfigs = testFormulaParamsConfigMap.get(formula).stream().sorted(Comparator.comparing(DtoTestFormulaParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        }
        DtoWorkSheetProperty workSheetProperty = new DtoWorkSheetProperty();
        workSheetProperty.setAnalyseData(anaMapList);
        workSheetProperty.setParamsConfig(testFormulaParamsConfigs);
        workSheetProperty.setIsAlterFormula(true);
        workSheetProperty.setIsAlterCurve(true);
        //将公式
        if (analyseOriginalRecordList.size() > 0) {
            analyseOriginalRecordRepository.save(analyseOriginalRecordList);
        }
        String comment = String.format("更新公式为:%s", formula);
        if (StringUtils.isNotNullAndEmpty(oldFormula)) {
            if (!oldFormula.equals(formula)) {
                comment = String.format("公式由:%s,更新为:%s", oldFormula, formula);
            }
        }
        DtoLog log = new DtoLog();
        log.setComment(comment);
        log.setLogType(EnumPRO.EnumLogType.检测单基本信息.getValue());
        log.setObjectId(workSheetFolderId);
        log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
        log.setOperateInfo(EnumPRO.EnumLogOperateType.更新检测单公式.name());
        newLogService.createLog(log);
        return workSheetProperty;
    }

    @Transactional
    @Override
    public Integer delete(List<String> ids) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(ids);
        //由于删除工作单会自动刷新缓存，这边删除数据就不做同样的动作了
        analyseDataService.deleteWorkSheetAnalyseData(analyseDataList, false);
        //删除检测单数据
        List<DtoProject2WorkSheetFolder> p2wList = project2WorkSheetFolderRepository.findByWorkSheetFolderIdIn(ids);
        //删除检测单数据
        project2WorkSheetFolderRepository.deleteByWorkSheetFolderIdIn(ids);

        List<DtoWorkSheetFolder> workSheetFolders = repository.findAll(ids);
        List<DtoLog> logs = new ArrayList<>();
        for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
            String comment = String.format("删除了检测单%s。", workSheetFolder.getWorkSheetCode());
            DtoLog log = new DtoLog();
            log.setComment(comment);
            log.setLogType(EnumPRO.EnumLogType.检测单增删.getValue());
            log.setObjectId(workSheetFolder.getId());
            log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.删除检测单.name());
            log.setOperatorId(workSheetFolder.getCheckerId());
            log.setOperatorName(workSheetFolder.getCheckerName());
            logs.add(log);
        }
        newLogService.createLog(logs, EnumPRO.EnumLogType.检测单增删.getValue());

        List<DtoLog> p2wLogs = new ArrayList<>();
        for (DtoProject2WorkSheetFolder p2w : p2wList) {
            String workSheetCode = workSheetFolders.stream().filter(p -> p.getId().equals(p2w.getWorkSheetFolderId())).map(DtoWorkSheetFolder::getWorkSheetCode).findFirst().orElse("");
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.删除检测单.toString());
            log.setLogType(EnumPRO.EnumLogType.项目检测单.getValue());
            log.setObjectId(p2w.getProjectId());
            log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
            log.setComment("删除了检测单：" + workSheetCode);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            p2wLogs.add(log);
        }
        newLogService.createLog(p2wLogs, EnumPRO.EnumLogType.项目检测单.getValue());
        workSheetReagentRepository.deleteByWorksheetFolderIdIn(ids, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        environmentalRecordRepository.deleteByObjectIdIn(ids);
        instrumentUseRecordRepository.deleteByObjectIdIn(ids, PrincipalContextUser.getPrincipal().getUserId(), new Date());

        Integer count = super.logicDeleteById(ids);
        //保证事物提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        List<String> analystIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList());
                        //如果没有工作单，把当前人员传入进去，不然多于的工作单，在缓存中无法删除
                        if (analystIds.size() == 0) {
                            analystIds.add(PrincipalContextUser.getPrincipal().getUserId());
                        }
                        proService.sendProMessage(EnumPRO.EnumProAction.删除检测单, "", "", analystIds);
                    }
                }
        );
        return count;
    }

    @Transactional
    @Override
    public List<String> checkPassWorkSheet(List<String> ids, Boolean status, String opinion, String auditorId, String auditorName) {
        //检测单数据
        List<DtoWorkSheetFolder> workSheetFolders = repository.findAll(ids);
        //分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(ids);
        if (status) {
            try {
                List<DtoLog> logs = new ArrayList<>();
                List<DtoAnalyseData> anaList = analyseDataList.stream().filter(p -> p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue())).collect(Collectors.toList());
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    //工作流提交
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setSignal("checkPass");
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String comment = String.format("复核了检测单%s,结果：通过,意见：%s%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion,
                            StringUtils.isNotNullAndEmpty(auditorName) ? (",下一步操作人:" + auditorName) : auditorName
                    );
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.复核检测单.name());
                    log.setOperatorId(workSheetFolder.getCheckerId());
                    log.setOperatorName(workSheetFolder.getCheckerName());
                    logs.add(log);
                    workflowService.submitSign(dtoWorkflowSign);
                }
                List<String> anaIds = anaList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<String> sampleIds = anaList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                if (anaIds.size() > 0) {
                    analyseDataRepository.updateAnalyseDataStatus(anaIds, EnumPRO.EnumAnalyseDataStatus.复核通过.name(),
                            EnumPRO.EnumAnalyseDataStatus.复核通过.getValue(), false, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }
                auditorId = StringUtils.isNotNullAndEmpty(auditorId) ? auditorId : UUIDHelper.GUID_EMPTY;
                Date newDate = new Date();
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    workSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.复核通过.name());
                    workSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.复核通过.getValue());
                    workSheetFolder.setAuditorId(auditorId);
                    workSheetFolder.setAuditorName(auditorName);
                    workSheetFolder.setCheckDate(newDate);
                    workSheetFolder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    workSheetFolder.setModifyDate(newDate);
                }
                if (StringUtil.isNotEmpty(workSheetFolders)) {
                    repository.save(workSheetFolders);
                }

//                repository.updateStatus(ids, EnumPRO.EnumWorkSheetStatus.复核通过.name(),
//                        EnumPRO.EnumWorkSheetStatus.复核通过.getValue(), auditorId, auditorName, new Date(),
//                        PrincipalContextUser.getPrincipal().getUserId(), new Date());
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                List<String> checkIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList());
                checkIds.add(auditorId);
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.检测单复核通过, "", "", workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        checkIds.stream().distinct().collect(Collectors.toList()));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, workSheetFolders.size());
                            }
                        }
                );

                return sampleIds;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                System.out.println(ex.getMessage());
            }
        } else {
            try {
                List<DtoLog> logs = new ArrayList<>();
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    //工作流提交
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setSignal("checkNotPass");
                    dtoWorkflowSign.setIsActivate(true);
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String comment = String.format("复核了检测单%s,结果：不通过,意见：%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion);
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setOpinion(opinion);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.复核检测单.name());
                    log.setOperatorId(workSheetFolder.getCheckerId());
                    log.setOperatorName(workSheetFolder.getCheckerName());
                    logs.add(log);
                    workflowService.submitSign(dtoWorkflowSign);
                }
                List<String> anaIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                //List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);
                if (anaIds.size() > 0) {
                    analyseDataRepository.updateAnalyseDataStatus(anaIds,
                            EnumPRO.EnumAnalyseDataStatus.拒绝.name(),
                            EnumPRO.EnumAnalyseDataStatus.拒绝.getValue(),
                            false,
                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }
//                if (sampleList.size() > 0) {
//                    comRepository.clear();
//                    proService.checkSamples(sampleList);
//                }

                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    workSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.name());
                    workSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue());
                    workSheetFolder.setAuditorId(UUIDHelper.GUID_EMPTY);
                    workSheetFolder.setAuditorName("");
                    workSheetFolder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    workSheetFolder.setModifyDate(new Date());
                    workSheetFolder.setBackOpinion(opinion);
                    workSheetFolder.setBackTimes(workSheetFolder.getBackTimes() + 1);
                    dealBackWorkSheetFolder(workSheetFolder);
                }
                if (StringUtil.isNotEmpty(workSheetFolders)) {
                    repository.save(workSheetFolders);
                }

//                repository.updateStatus(ids, EnumPRO.EnumWorkSheetStatus.检测单拒绝.name(),
//                        EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue(), UUIDHelper.GUID_EMPTY, "",
//                        PrincipalContextUser.getPrincipal().getUserId(), new Date(), opinion);
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.检测单退回, "", "", workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        workSheetFolders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList()));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, workSheetFolders.size());
                            }
                        }
                );
                return sampleIds;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                System.out.println(ex.getMessage());
            }
        }

        return new ArrayList<>();
    }

    @Transactional
    @Override
    public List<String> confirmPassWorkSheet(List<String> ids, Boolean status, String opinion, String checkId, String checkName) {
        //检测单数据
        List<DtoWorkSheetFolder> workSheetFolders = repository.findAll(ids);
        //分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(ids);
        if (status) {
            try {
                List<DtoLog> logs = new ArrayList<>();
                List<DtoAnalyseData> anaList = analyseDataList.stream().filter(p -> p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue())).collect(Collectors.toList());
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    //工作流确认
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setSignal("confirmPass");
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String comment = String.format("提交了检测单%s,结果：通过,意见：%s%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion,
                            StringUtils.isNotNullAndEmpty(checkName) ? (",下一步操作人:" + checkName) : checkName
                    );
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.确认检测单.name());
                    log.setOperatorId(workSheetFolder.getCertificatorId());
                    log.setOperatorName(workSheetFolder.getCertificatorName());
                    logs.add(log);
                    workflowService.submitSign(dtoWorkflowSign);
                }
                List<String> sampleIds = anaList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                checkId = StringUtils.isNotNullAndEmpty(checkId) ? checkId : UUIDHelper.GUID_EMPTY;

                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    workSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.已经提交.name());
                    workSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.已经提交.getValue());
                    workSheetFolder.setCheckerId(checkId);
                    workSheetFolder.setCheckerName(checkName);
                    workSheetFolder.setCheckDate(new Date());
                    workSheetFolder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    workSheetFolder.setModifyDate(new Date());
                }
                if (StringUtil.isNotEmpty(workSheetFolders)) {
                    repository.save(workSheetFolders);
                }

//                repository.updateStatus(ids, EnumPRO.EnumWorkSheetStatus.已经提交.name(),
//                        EnumPRO.EnumWorkSheetStatus.已经提交.getValue(), checkId, checkName, new Date(),
//                        PrincipalContextUser.getPrincipal().getUserId(), new Date());

                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                List<String> checkIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList());
                checkIds.add(checkId);
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.确认检测单, "", "", workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        checkIds.stream().distinct().collect(Collectors.toList()));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, workSheetFolders.size());
                            }
                        }
                );

                return sampleIds;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                System.out.println(ex.getMessage());
            }
        } else {
            try {
                List<DtoLog> logs = new ArrayList<>();
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    //工作流提交
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setSignal("checkNotPass");
                    dtoWorkflowSign.setIsActivate(true);
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String comment = String.format("提交了检测单%s,结果：不通过,意见：%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion);
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setOpinion(opinion);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.确认检测单.name());
                    log.setOperatorId(workSheetFolder.getCertificatorId());
                    log.setOperatorName(workSheetFolder.getCertificatorName());
                    logs.add(log);
                    workflowService.submitSign(dtoWorkflowSign);
                }
                List<String> anaIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                if (anaIds.size() > 0) {
                    analyseDataRepository.updateAnalyseDataStatus(anaIds,
                            EnumPRO.EnumAnalyseDataStatus.拒绝.name(),
                            EnumPRO.EnumAnalyseDataStatus.拒绝.getValue(),
                            false,
                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }

                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    workSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.name());
                    workSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue());
                    workSheetFolder.setAuditorId(UUIDHelper.GUID_EMPTY);
                    workSheetFolder.setAuditorName("");
                    workSheetFolder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    workSheetFolder.setModifyDate(new Date());
                    workSheetFolder.setBackOpinion(opinion);
                    workSheetFolder.setBackTimes(workSheetFolder.getBackTimes() + 1);
                }
                if (StringUtil.isNotEmpty(workSheetFolders)) {
                    repository.save(workSheetFolders);
                }

//                repository.updateStatus(ids, EnumPRO.EnumWorkSheetStatus.检测单拒绝.name(),
//                        EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue(), UUIDHelper.GUID_EMPTY, "",
//                        PrincipalContextUser.getPrincipal().getUserId(), new Date(), opinion);
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.检测单退回, "", "", workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        workSheetFolders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList()));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, workSheetFolders.size());
                            }
                        }
                );
                return sampleIds;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                System.out.println(ex.getMessage());
            }
        }

        return new ArrayList<>();
    }


    @Transactional
    @Override
    public List<String> auditWorkSheet(List<String> ids, Boolean status, String opinion) {
        //检测单数据
        List<DtoWorkSheetFolder> workSheetFolders = repository.findAll(ids);
        //分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(ids);
        if (status) {
            List<DtoLog> logs = new ArrayList<>();
            //数据日志
            List<DtoLog> anaDataLogs = new ArrayList<>();
            try {
                List<DtoAnalyseData> anaList = analyseDataList.stream().filter(p -> p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())).collect(Collectors.toList());
                if (anaList.size() == 0) {
                    anaList = this.getAnalyseList(analyseDataList);
                }
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    //工作流提交
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setSignal("auditPass");
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String comment = String.format("审核了检测单%s,结果：通过,意见：%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion
                    );
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.审核检测单.name());
                    log.setOperatorId(workSheetFolder.getAuditorId());
                    log.setOperatorName(workSheetFolder.getAuditorName());
                    dealWorkSheetFolderLog(log);
                    logs.add(log);
                    workflowService.submitSign(dtoWorkflowSign);
                    //添加数据日志
                    List<DtoAnalyseData> analyseDataOfWorkSheet = analyseDataList.stream().filter(p -> workSheetFolder.getId().equals(p.getWorkSheetFolderId())).collect(Collectors.toList());
                    for (DtoAnalyseData analyseData : analyseDataOfWorkSheet) {
                        DtoLog dataLog = new DtoLog();
                        String dataComment = String.format("数据审核通过：审核时数据为%s", analyseData.getTestValue());
                        dataLog.setRemark(analyseData.getTestValue());
                        dataLog.setComment(dataComment);
                        dataLog.setLogType(EnumPRO.EnumLogType.数据审核.getValue());
                        dataLog.setObjectId(analyseData.getId());
                        dataLog.setObjectType(EnumPRO.EnumLogObjectType.数据.getValue());
                        dataLog.setOperateInfo(EnumPRO.EnumLogOperateType.审核数据.name());
                        dataLog.setOperatorId(workSheetFolder.getCheckerId());
                        dataLog.setOperatorName(workSheetFolder.getCheckerName());
                        anaDataLogs.add(dataLog);
                    }
                }
                List<String> anaIds = anaList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<String> sampleIds = anaList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);
                List<String> projectIds = sampleList.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
                if (anaIds.size() > 0) {
                    analyseDataRepository.updateAnalyseDataStatus(anaIds,
                            EnumPRO.EnumAnalyseDataStatus.已确认.name(),
                            EnumPRO.EnumAnalyseDataStatus.已确认.getValue(),
                            true,
                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }

                Date newDate = new Date();
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    workSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.审核通过.name());
                    workSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.审核通过.getValue());
                    workSheetFolder.setAuditDate(newDate);
                    workSheetFolder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    workSheetFolder.setModifyDate(newDate);
                }
                if (StringUtil.isNotEmpty(workSheetFolders)) {
                    repository.save(workSheetFolders);
                }
//                if (sampleList.size() > 0) {
//                    comRepository.clear();
//                    proService.checkSamples(sampleList);
//                }
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                if (StringUtil.isNotEmpty(anaDataLogs)) {
                    newLogService.createLog(anaDataLogs, EnumPRO.EnumLogType.数据审核.getValue());
                }

                // 审核完成，数据标记
                this.markersData(ids, true);
                //提交后移除当前人员redis中的工作单记录
                removeWorkSheetInfoOfChecker(workSheetFolders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList()));
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.检测单审核通过, "", "", workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        workSheetFolders.stream().map(DtoWorkSheetFolder::getAuditorId).collect(Collectors.toList()));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, workSheetFolders.size());
                                for (String projectId : projectIds) {
                                    proService.sendProMessage(EnumPRO.EnumProAction.检测单审核通过, projectId);
                                }
                            }
                        }
                );
                performanceStatisticForWorkSheetDataService.createAnalyseStatistic(workSheetFolders, analyseDataList);
                return sampleIds;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                System.out.println(ex.getMessage());
                throw new BaseException("异常错误");
            }
        } else {
            try {
                List<DtoLog> logs = new ArrayList<>();
                for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                    //工作流提交
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setSignal("checkNotPass");
                    dtoWorkflowSign.setIsActivate(true);
                    dtoWorkflowSign.setIsAutoStatus(false);
                    String comment = String.format("审核了检测单%s,结果：不通过,意见：%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion);
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setOpinion(opinion);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.审核检测单.name());
                    log.setOperatorId(workSheetFolder.getAuditorId());
                    log.setOperatorName(workSheetFolder.getAuditorName());
                    dealWorkSheetFolderLog(log);
                    logs.add(log);
                    workflowService.submitSign(dtoWorkflowSign);
                    workSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.name());
                    workSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue());
                    workSheetFolder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    workSheetFolder.setAuditDate(new Date());
                    workSheetFolder.setModifyDate(new Date());
                    workSheetFolder.setBackOpinion(opinion);
                    workSheetFolder.setBackTimes(workSheetFolder.getBackTimes() + 1);
                    dealBackWorkSheetFolder(workSheetFolder);
                }
                List<String> anaIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                //List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);
                if (anaIds.size() > 0) {
                    analyseDataRepository.updateAnalyseDataStatus(anaIds,
                            EnumPRO.EnumAnalyseDataStatus.拒绝.name(),
                            EnumPRO.EnumAnalyseDataStatus.拒绝.getValue(),
                            false,
                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }
                if (StringUtil.isNotEmpty(workSheetFolders)) {
                    repository.save(workSheetFolders);
                }
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.检测单退回, "", "", workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        workSheetFolders.stream().map(DtoWorkSheetFolder::getAuditorId).collect(Collectors.toList()));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, workSheetFolders.size());
                            }
                        }
                );

                return sampleIds;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                System.out.println(ex.getMessage());
                throw new BaseException("异常错误");
            }
        }
    }


    /**
     * 数据标记
     *
     * @param workSheetFolderIds 送样单ids
     * @param isPass             是否通过
     */
    private void markersData(List<String> workSheetFolderIds, Boolean isPass) {
        if (StringUtil.isNotEmpty(workSheetFolderIds)) {
            if (!isPass) {
                markersDataService.updateValidate(new DtoMarkersData(workSheetFolderIds, false));
            } else {
                // 查询所有的采样单，只有包含采样单附件的数据才会标记
                List<DtoDocument> dtoDocuments = documentRepository.findByFolderIdInAndDocTypeIdAndIsDeletedFalseOrderByCreateDateDesc(workSheetFolderIds, BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD);
                List<String> ids = dtoDocuments.stream().map(DtoDocument::getFolderId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(ids)) {
                    // 审核通过，更新测试项目验证
                    markersDataService.updateValidate(new DtoMarkersData(ids, true));
                }
            }
        }
    }


    /**
     * 配置实验室审核
     * 1：一审 2： 二审
     * true：一审 false：二审
     *
     * @return 返回判定
     */
    @Override
    public Boolean workSheetApprovalStep() {
        boolean value = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys_pro_workSheet_approvalSteps");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            if ("1".equals(configModel.getConfigValue())) {
                value = Boolean.TRUE;
            }
        }
        return value;
    }

    @Override
    @Transactional
    public void transfer(DtoWorkSheetCheck dtoWorkSheetCheck) {
        // 转交人id
        String transferorId = dtoWorkSheetCheck.getAuditorId();
        String transferorName = dtoWorkSheetCheck.getAuditorName();
        List<String> worksheetFolderIds = dtoWorkSheetCheck.getIds();
        DtoWorkSheetFolder workSheetFolder = repository.findOne(worksheetFolderIds.get(0));
        if (workSheetFolder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.已经提交.getValue())) {
            workSheetFolder.setCheckerId(transferorId);
            workSheetFolder.setCheckerName(transferorName);
        } else if (workSheetFolder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.复核通过.getValue())) {
            workSheetFolder.setAuditorId(transferorId);
            workSheetFolder.setAuditorName(transferorName);
        } else if (workSheetFolder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.确认检测单.getValue())) {
            workSheetFolder.setCertificatorId(transferorId);
            workSheetFolder.setCertificatorName(transferorName);
        }
        repository.save(workSheetFolder);
    }


    @Override
    public List<DtoTest> findAnalyseTest(String workSheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        List<DtoTest> testList = new ArrayList<>();
        analyseDataList.stream().filter(p -> !p.getQcType().equals(EnumLIM.EnumQCType.替代物.getValue()))
                .sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName))
                .collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList())).forEach((testId, list) -> {
            DtoTest test = new DtoTest();
            test.setId(testId);
            test.setAnalyzeItemId(list.get(0).getAnalyseItemId());
            test.setRedAnalyzeItemName(list.get(0).getRedAnalyzeItemName());
            testList.add(test);
        });
        return testList;
    }

    protected void dealWorkSheetFolderLog(DtoLog log) {

    }

    private void removeWorkSheetInfoOfChecker(Collection<String> checkerIds) {
        if (StringUtil.isNotEmpty(checkerIds)) {
            for (String checkerId : checkerIds) {
                if (StringUtil.isNotEmpty(checkerId) && !UUIDHelper.GUID_EMPTY.equals(checkerId)) {
                    redisTemplate.opsForHash().delete(EnumPRO.EnumPRORedis.getRedisKey(EnumPRO.EnumPRORedis.PRO_OrgId_AuditWorkSheetDetail.getValue()), checkerId);
                }
            }
        }
    }

    @Override
    public DtoWorkSheetFolder findAttachPath(String id) {
        DtoWorkSheetFolder folder = repository.findOne(id);
        DtoAnalyzeMethod method = analyzeMethodService.findOne(folder.getAnalyzeMethodId());
        if (StringUtil.isNotNull(method)) {
            folder.setRedAnalyzeMethodName(method.getMethodName());
            folder.setRedCountryStandard(method.getCountryStandard());
        }
        return folder;
    }

    @Transactional
    @Override
    public void backWorkSheet(List<String> ids, String opinion, Boolean isReport) {
        //检测单数据
        List<DtoWorkSheetFolder> workSheetFolders = repository.findAll(ids);
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdIn(ids);
        Set<String> samIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        List<DtoReportDetail> reportDetails = reportDetailRepository.findByObjectIdInAndObjectType(samIds, EnumPRO.EnumReportDetailType.样品.getValue());
        if (StringUtil.isNotEmpty(reportDetails) && isReport) {
            List<String> reportIds = reportDetails.stream().map(DtoReportDetail::getReportId).distinct().collect(Collectors.toList());
            List<DtoReport> reportList = StringUtil.isNotEmpty(reportIds) ? reportRepository.findAll(reportIds) : new ArrayList<>();
            List<String> projectIds = reportList.stream().map(DtoReport::getProjectId).collect(Collectors.toList());
            List<DtoProject> projectList = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
            throw new BaseException(String.format("样品已关联出具报告，出具报告的项目：%s,请联系编制报告人进行退回！",
                    projectList.stream().map(DtoProject::getProjectCode).collect(Collectors.joining("、"))));
        }
        List<String> projectIds = project2WorkSheetFolderRepository.findByWorkSheetFolderIdIn(ids).stream()
                .map(DtoProject2WorkSheetFolder::getProjectId).distinct().collect(Collectors.toList());
        List<String> workIds = workSheetFolders.stream().filter(p -> EnumPRO.EnumWorkSheetStatus.审核通过.name().equals(p.getStatus()))
                .map(DtoWorkSheetFolder::getId).collect(Collectors.toList());

        try {
            //需要退回的数据ids
            List<String> backIds = new ArrayList<>();
            List<DtoWorkSheetFolder> backFolderList = new ArrayList<>();
            List<DtoLog> logs = new ArrayList<>();
            for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                if (!workSheetFolder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.新建.getValue())
                        && !workSheetFolder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.已经保存.getValue())
                        && !workSheetFolder.getWorkStatus().equals(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue())) {
                    //工作流提交
                    DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                    dtoWorkflowSign.setObjectId(workSheetFolder.getId());
                    dtoWorkflowSign.setIsActivate(true);
                    dtoWorkflowSign.setSignal("checkNotPass");
                    String comment = String.format("退回了检测单%s,结果：不通过,意见：%s。",
                            workSheetFolder.getWorkSheetCode(),
                            opinion);

                    workflowService.submitSign(dtoWorkflowSign);
                    backIds.add(workSheetFolder.getId());
                    backFolderList.add(workSheetFolder);
                    DtoLog log = new DtoLog();
                    log.setComment(comment);
                    log.setOpinion(opinion);
                    log.setLogType(EnumPRO.EnumLogType.检测单流程.getValue());
                    log.setObjectId(workSheetFolder.getId());
                    log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.退回检测单.name());
                    logs.add(log);
                }
            }
            if (backIds.size() > 0) {
                //分析数据
                List<DtoAnalyseData> anaList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(backIds);
                //判断退回数据是否有关联报告
                analyseDataService.judgeIsBackAnaData(anaList, false);
                List<String> anaIds = anaList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                if (anaIds.size() > 0) {
                    List<String> sampleIds = anaList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                    //已报告关联的样品id
                    List<String> certSampleIds = proService.updateReportSample(sampleIds);
                    analyseDataRepository.updateAnalyseDataStatus(anaIds,
                            EnumPRO.EnumAnalyseDataStatus.拒绝.name(),
                            EnumPRO.EnumAnalyseDataStatus.拒绝.getValue(),
                            false,
                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
                    if (certSampleIds.size() > 0) {
                        List<String> analyseDataIds = anaList.stream().filter(p -> certSampleIds.contains(p.getSampleId())).map(DtoAnalyseData::getId).collect(Collectors.toList());
                        if (analyseDataIds.size() > 0) {
                            analyseDataRepository.updateDataChangeStatus(analyseDataIds, EnumPRO.EnumDataChangeStatus.修改.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        }
                    }
                    if (sampleIds.size() > 0) {
                        //需要手动清除缓存数据
                        comRepository.clear();
                        proService.checkSample(sampleIds);
                    }
                }
                // 筛选审核完成再退回的工作单，数据标记
                this.markersData(workIds, false);

                for (DtoWorkSheetFolder folder : backFolderList) {
                    folder.setStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.name());
                    folder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue());
                    folder.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    folder.setModifyDate(new Date());
                    folder.setBackOpinion(opinion);
                    folder.setBackTimes(folder.getBackTimes() + 1);
                    dealBackWorkSheetFolder(folder);
                }
                if (StringUtil.isNotEmpty(backFolderList)) {
                    repository.save(backFolderList);
                }
                newLogService.createLog(logs, EnumPRO.EnumLogType.检测单流程.getValue());
                //保证事物提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                proService.sendProMessage(EnumPRO.EnumProAction.检测单退回, "", "", workSheetFolders.stream()
                                                .filter(p -> backIds.contains(p.getId())).map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()),
                                        workSheetFolders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList()));
                                projectIds.forEach(v -> proService.sendProMessage(EnumPRO.EnumProAction.检测单退回, v));
                                analyseDataCacheService.saveAuditTimes(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : UUIDHelper.GUID_EMPTY, backIds.size());
                            }
                        }
                );
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new BaseException(ex.getMessage());
        }
    }

    /**
     * 查询检测单下默认的质控仪器
     *
     * @param workSheetFolderId 检测单id
     * @return 质控仪器
     */
    @Override
    public List<DtoInstrument> findDefaultInstrument(String workSheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        List<DtoInstrument> instrumentList = new ArrayList<>();
        if (analyseDataList.stream().anyMatch(DtoAnalyseData::getIsQm)) {
            Map<String, Object> values = new HashMap<>();
            PageBean<DtoInstrument> pb = new PageBean<>();
            pb.setEntityName("DtoInstrument i,DtoQualityManage q,DtoAnalyseData a");
            pb.setSelect("select i");
            pb.setRowsPerPage(Integer.MAX_VALUE);
            pb.addCondition(" and i.id = q.instrumentId and a.isDeleted = 0 ");
            pb.addCondition(" and q.anaId = a.id");
            pb.addCondition(" and a.workSheetFolderId = :workSheetFolderId");
            values.put("workSheetFolderId", workSheetFolderId);
            comRepository.findByPage(pb, values);
            instrumentList = new ArrayList<>(new HashSet<>(pb.getData()));
        }
        if (analyseDataList.stream().anyMatch(p -> !p.getIsQm())) {
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(testIds)) {
                StringBuilder sb = new StringBuilder("select wsf.id from DtoWorkSheetFolder wsf,DtoWorkSheet ws ");
                sb.append(" where wsf.id = ws.parentId and wsf.workStatus > :workSheetStatus ");
                sb.append(" and ws.testId in :testIds ");
                sb.append(" order by wsf.createDate desc");
                Map<String, Object> values = new HashMap<>();
                values.put("workSheetStatus", EnumPRO.EnumWorkSheetStatus.已经提交.getValue());
                values.put("testIds", testIds);
                List<String> folderIdList = comRepository.find(sb.toString(), values);

                if (StringUtil.isNotEmpty(folderIdList)) {
                    String objId = folderIdList.get(0);
                    PageBean<DtoInstrument> pageBean = new PageBean<>();
                    pageBean.setEntityName("DtoInstrument i,DtoInstrumentUseRecord u");
                    pageBean.setSelect("select i");
                    pageBean.setRowsPerPage(Integer.MAX_VALUE);
                    pageBean.addCondition(" and i.id = u.instrumentId");

                    pageBean.addCondition(" and u.objectId = :objId");
                    values.clear();
                    values.put("objId", objId);
                    comRepository.findByPage(pageBean, values);
                    instrumentList.addAll(new ArrayList<>(new HashSet<>(pageBean.getData())));
                }
            } else {
                return instrumentList;
            }
        }
        List<DtoInstrument> recentInstrument = this.getRecentInstrumentOfTestIds(workSheetFolderId);
        instrumentList.addAll(recentInstrument);
        instrumentList = instrumentList.stream()
                .distinct()
                .sorted(Comparator.comparing(DtoInstrument::getInstrumentName))
                .collect(Collectors.toList());
        return instrumentList;
    }

    /**
     * 根据工作单id查询最近使用的仪器
     *
     * @param workSheetFolderId 工作单id
     * @return List<DtoInstrument>
     */
    private List<DtoInstrument> getRecentInstrumentOfTestIds(String workSheetFolderId) {
        List<DtoInstrument> instruments = new ArrayList<>();
        if (StringUtil.isNotEmpty(workSheetFolderId)) {
            List<DtoWorkSheet> workSheets = workSheetRepository.findByParentId(workSheetFolderId);
            List<String> testIds = workSheets.stream().map(DtoWorkSheet::getTestId).distinct().collect(Collectors.toList());
            List<DtoWorkSheet> allWorkSheets = workSheetRepository.findByTestIdIn(testIds);
            List<String> workSheetFolderIds = allWorkSheets.parallelStream()
                    .map(DtoWorkSheet::getParentId)
                    .distinct()
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(workSheetFolderIds)) {
                List<DtoWorkSheetFolder> workSheetFolders = repository.findAll(workSheetFolderIds);
                //只获取指定状态的工作单
                List<Integer> workSheetStatus = Arrays.asList(EnumPRO.EnumWorkSheetStatus.审核通过.getValue(),
                        EnumPRO.EnumWorkSheetStatus.复核通过.getValue(),
                        EnumPRO.EnumWorkSheetStatus.已经提交.getValue());
                workSheetFolders = workSheetFolders.stream()
                        .filter(w -> workSheetStatus.contains(w.getWorkStatus()))
                        .collect(Collectors.toList());
                workSheetFolders.sort(Comparator.comparing(DtoWorkSheetFolder::getCreateDate).reversed());
                workSheetFolderIds = workSheetFolders.stream()
                        .map(DtoWorkSheetFolder::getId)
                        .collect(Collectors.toList());
                //仅获取最近一张工作单中使用的仪器
                if (StringUtil.isNotEmpty(workSheetFolderIds)) {
                    List<DtoInstrumentUseRecord> instrumentUseRecords = instrumentUseRecordRepository.findByObjectIdAndObjectType(workSheetFolderIds.get(0), EnumLIM.EnumInsUseObjType.实验室分析.getValue());
                    List<String> instrumentIds = instrumentUseRecords.stream()
                            .map(DtoInstrumentUseRecord::getInstrumentId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(instrumentIds)) {
                        instruments = instrumentRepository.findAll(instrumentIds);
                    }
                }
            }
        }
        return instruments;
    }

    /**
     * 切换原始记录单
     *
     * @param recordId          原始记录单id
     * @param workSheetFolderId 工作单id
     * @param testIds           测试项目id
     * @return
     */
    @Transactional
    @Override
    public List<DtoParamsData> changeWorkSheetParams(String recordId,
                                                     String workSheetFolderId,
                                                     List<String> testIds) {
        // 原始记录单id找相应的数据
        List<DtoRecordConfigParams> recordConfigParamsList = recordConfigParamsConfigService.findTestParamsByRecordId(testIds, recordId);
        // 原先的数据要清除 TB_PRO_ParamsData里面的数据要删除  --workSheetId(根据大工作单id和测试项目找到小工作单)
        List<DtoWorkSheet> allWorkSheetList = workSheetRepository.findByParentId(workSheetFolderId);
        List<DtoWorkSheet> dtoWorkSheets = workSheetRepository.findByParentIdAndTestIdIn(workSheetFolderId, testIds);
        Set<String> oldRecordIds = dtoWorkSheets.stream().map(DtoWorkSheet::getRecordId).collect(Collectors.toSet());
        //找到对应老的recordId
        List<DtoWorkSheet> workSheetList = allWorkSheetList.stream().filter(p -> oldRecordIds.contains(p.getRecordId())).collect(Collectors.toList());
        List<String> dtoWorkSheetIds = dtoWorkSheets.stream().map(DtoWorkSheet::getId).collect(Collectors.toList());
        // 获取工作单上此的参数数据信息
        List<DtoParamsData> dtoParamsDatas = paramsDataRepository.findByObjectIdIn(dtoWorkSheetIds);
        List<String> dtoParamsDataIds = dtoParamsDatas.stream().map(DtoParamsData::getId).collect(Collectors.toList());
        // 删除工作单上次的参数数据信息
        if (StringUtil.isNotEmpty(dtoParamsDataIds)) {
            paramsDataRepository.logicDeleteById(dtoParamsDataIds, new Date());
        }
        //根据原始记录单配置配置进行插入参数数据表中
        List<DtoParamsData> dtoParamsDataList = new ArrayList<>();
        // 如果原始记录单配置不为空
        if (StringUtil.isNotEmpty(recordConfigParamsList)) {
            for (String testId : testIds) {
                // 查询小工作单对象
                DtoWorkSheet dtoWorkSheet = dtoWorkSheets.stream().filter(item -> item.getTestId().equals(testId)).findFirst().orElse(null);
                if (StringUtil.isNotNull(dtoWorkSheet)) {
                    for (DtoRecordConfigParams dtoRecordConfigParams : recordConfigParamsList) {
                        // 创建新的参数数据对象
                        DtoParamsData dtoParamsData = new DtoParamsData();
                        dtoParamsData.setObjectId(dtoWorkSheet.getId());
                        dtoParamsData.setOrderNum(dtoRecordConfigParams.getOrderNum());
                        dtoParamsData.setObjectType(EnumPRO.EnumParamsDataType.检测单.getValue());
                        dtoParamsData.setParamsConfigId(dtoRecordConfigParams.getId());
                        dtoParamsData.setParamsName(dtoRecordConfigParams.getAlias());
                        dtoParamsData.setDimension(dtoRecordConfigParams.getDimension());
                        dtoParamsData.setDimensionId(dtoRecordConfigParams.getDimensionId());
                        dtoParamsData.setDataSource(dtoRecordConfigParams.getDataSource());
                        dtoParamsData.setDefaultControl(dtoRecordConfigParams.getDefaultControl());
                        dtoParamsData.setFormula(dtoRecordConfigParams.getFormula());
                        dtoParamsData.setIsEnabled(dtoRecordConfigParams.getIsEnabled());
                        dtoParamsData.setIsRequired(dtoRecordConfigParams.getIsRequired());
                        dtoParamsData.setParamsConfigId(dtoRecordConfigParams.getId());
                        //参数值设置为原始记录单配置的默认值
                        dtoParamsData.setParamsValue(dtoRecordConfigParams.getDefaultValue());
                        dtoParamsDataList.add(dtoParamsData);
                    }
                }
            }
        }
        // 更新小工作单关联的原始记录单id
        for (DtoWorkSheet dtoWorkSheet : workSheetList) {
            dtoWorkSheet.setRecordId(recordId);
        }
        if (StringUtil.isNotEmpty(workSheetList)) {
            workSheetService.save(workSheetList);
        }
        // 如果新增的参数数据集合不为空,则进行新增
        List<DtoParamsData> paramsDataList = new ArrayList<>();
        if (StringUtil.isNotEmpty(dtoParamsDataList)) {
            // 保存相关参数数据
            paramsDataService.save(dtoParamsDataList);
            // 当有多个测试项目时,随机选取第一个的参数配置进行返回
            paramsDataList = dtoParamsDataList.stream().filter(entity -> entity.getObjectId().equals(dtoWorkSheets.get(0).getId())).collect(Collectors.toList());
            //再返回新的数据即可
            for (DtoParamsData dtoParamsData : paramsDataList) {
                DtoRecordConfigParams recordConfigParams = recordConfigParamsList.stream().filter(p -> p.getId()
                        .equals(dtoParamsData.getParamsConfigId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(recordConfigParams)) {
                    setParamsData(dtoParamsData, recordConfigParams);
                }
            }

        }
        paramsDataList.sort(Comparator.comparing(DtoParamsData::getOrderNum, Comparator.reverseOrder()));
        return paramsDataList;
    }

    /**
     * 参数数据变动根据原始记录单个性化配置计算参数的结果
     *
     * @param dtoWorkSheetChangeRecord 接受实体
     */
    @Transactional
    @Override
    public List<DtoParamsData> calculateParamsData(DtoWorkSheetChangeRecord dtoWorkSheetChangeRecord) {
        // 获取参数数据集合
        List<DtoParamsData> dtoParamsDataList = dtoWorkSheetChangeRecord.getParamsDataList();
        // 如果参数为空则返回null
        if (StringUtil.isEmpty(dtoParamsDataList)) {
            return null;
        }
        // 获取所有的部分修约公式
        List<String> formulaIds = dtoParamsDataList.stream().filter(item -> StringUtil.isNotNull(item.getFormulaId())).map(DtoParamsData::getFormulaId).collect(Collectors.toList());
        List<String> paramsConfigIds = dtoParamsDataList.stream().map(DtoParamsData::getParamsConfigId).collect(Collectors.toList());
        // 根据公式Id查询所有的部分修约公式
        List<DtoParamsPartFormula> dtoParamsPartFormulaList = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
        // 根据参数配置Id查询参数配置
        List<DtoParamsConfig> dtoParamsConfigList = paramsConfigRepository.findByIdIn(paramsConfigIds);
        for (DtoParamsData dtoParamsData : dtoParamsDataList) {
            // 参数开启公式时进行计算
            if (dtoParamsData.getIsEnabled()) {
                String formula = dtoParamsData.getFormula();
                String formulaId = dtoParamsData.getFormulaId();
                // 获取配置的部分公式
                List<DtoParamsPartFormula> dtoParamsPartFormulas = dtoParamsPartFormulaList.stream().filter(item -> item.getFormulaId().equals(dtoParamsData.getFormulaId())).collect(Collectors.toList());
                Map<String, Object> mapPart = new HashMap<>();
                // 进行部分公式计算及修约
                for (DtoParamsPartFormula dtoParamsPartFormula : dtoParamsPartFormulas) {
                    if (StringUtil.isNotEmpty(dtoParamsPartFormula.getFormula())) {
                        // 获取计算用的map数据
                        Map<String, Object> mapPartParmas = getCalculationMap(dtoParamsPartFormula.getFormula(), dtoParamsDataList);
                        // 根据部分公式进行计算
                        String resultPart = calculationService.calculationExpression(dtoParamsPartFormula.getFormula(), mapPartParmas).toString();
                        if (StringUtils.isNotNullAndEmpty(resultPart)) {
                            // 进行部分公式计算结果的修约
                            String resultStrPart = proService.getDecimal(dtoParamsPartFormula.getMostSignificance(), dtoParamsPartFormula.getMostDecimal(), resultPart);
                            // 部分公式计算完成进行替换,生成新的计算公式
                            formula = formula.replace(dtoParamsPartFormula.getFormula(), "[" + resultPart + "]");
                            // 部分公式获得的结果
                            mapPart.put(resultPart, MathUtil.getBigDecimal(resultStrPart));
                        }
                    }
                }
                // 先根据公式找生成参数的map集合,然后根据公式计算出结果
                Map<String, Object> map = getCalculationMap(formula, dtoParamsDataList);
                // 合并部分修约公式
                map.putAll(mapPart);
                // 根据公式进行计算
                String result = calculationService.calculationExpression(formula, map).toString();
                DtoParamsConfig dtoParamsConfig = dtoParamsConfigList.stream().filter(item -> item.getId().equals(dtoParamsData.getId())).findFirst().orElse(null);
                String resultStr = result;
                // 判断参数配置是否空
                if (StringUtil.isNotNull(dtoParamsConfig)) {
                    // 根据有效位数和小数位数计算修约之后的结果
                    resultStr = proService.getDecimal(dtoParamsConfig.getMostSignificance(), dtoParamsConfig.getMostDecimal(), result);
                }
                // 封装结果,返回给前端
                dtoParamsData.setParamsValue(resultStr);
            }
        }
        return dtoParamsDataList;
    }

    private List<DtoWorkSheetFolder> createWorkSheetFolder(List<String> testAllIds, List<DtoAnalyseDataTemp> anaAllList,
                                                           List<DtoRecordConfigParams> recordConfigParams, String analystId,
                                                           String analystName, Date analyzeTime, DtoWorkSheetFolder oldFolder) {
        List<DtoProject2WorkSheetFolder> project2WorkSheetFolders = new ArrayList<>();

        List<DtoWorkSheetFolder> workSheetFolders = new ArrayList<>();

        //小的要保存的检测单
        List<DtoWorkSheet> workSheets = new ArrayList<>();

        //参数数据
        List<DtoParamsData> paramsDataList = new ArrayList<>();

        //样品类型

        List<DtoTest> dtoTests = testService.findRedisByIds(testAllIds);

        //相应的方法id
        List<String> methodIds = dtoTests.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());

        //获取所有的分析方法（含删除的方法）
        List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodService.findAllDeleted(methodIds);

        List<DtoLog> logs = new ArrayList<>();

        //对方法处理
        for (DtoAnalyzeMethod method : analyzeMethods) {

            //该方法下的测试项目
            List<String> testIds = dtoTests.stream().filter(p -> p.getAnalyzeMethodId().equals(method.getId())).map(DtoTest::getId).distinct().collect(Collectors.toList());

            //测试项目相关map对象
            Map<String, DtoTest> testMap = dtoTests.stream().collect(Collectors.toMap(DtoTest::getId, data -> data));

            //该方法下的所有数据
            List<DtoAnalyseDataTemp> anaList = anaAllList.stream().filter(p -> testIds.contains(p.getTestId())).distinct().collect(Collectors.toList());
            List<DtoSample> sampleList = sampleService.findAll(anaList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList()));
            updateTestMsg(anaList, dtoTests, sampleList);
            Map<String, Object> returnMap = createWorkSheetFolder(testMap, anaList, recordConfigParams, project2WorkSheetFolders, method.getId(), analystId, analystName, analyzeTime);
            workSheetFolders.add((DtoWorkSheetFolder) returnMap.get("workSheetFolder"));
            workSheets.addAll((List<DtoWorkSheet>) returnMap.get("workSheets"));
            paramsDataList.addAll((List<DtoParamsData>) returnMap.get("paramsData"));
            logs.addAll((List<DtoLog>) returnMap.get("logs"));
        }
        // 获取上一张检测单的环境仪器使用记录
        List<DtoEnvironmentalRecord> environmentalRecordList = environmentalRecordRepository.findByObjectId(oldFolder.getId());
        List<String> environmentalManageIds = environmentalRecordList.stream().map(DtoEnvironmentalRecord::getId).collect(Collectors.toList());
        List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordRepository.findByEnvironmentalManageIdIn(environmentalManageIds);
        List<String> instrumentIds = useRecords.stream().map(DtoInstrumentUseRecord::getInstrumentId).distinct().collect(Collectors.toList());
        List<DtoInstrument> instrumentList = StringUtil.isNotEmpty(instrumentIds) ? instrumentRepository.findAll(instrumentIds) : new ArrayList<>();
        // 删选已经过期、报废和停用的仪器,不正常的仪器
        List<String> notNormalInsIds = instrumentList.stream().filter(p -> p.getState().equals(EnumBase.EnumInstrumentStatus.报废.getValue()) ||
                p.getState().equals(EnumBase.EnumInstrumentStatus.停用.getValue()) ||
                (p.getOriginType() != -1 && p.getOriginEndDate().compareTo(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR)) != 0 && p.getOriginEndDate().compareTo(new Date()) < 0)).map(DtoInstrument::getId).collect(Collectors.toList());
        useRecords = useRecords.stream().filter(p -> !notNormalInsIds.contains(p.getInstrumentId())).collect(Collectors.toList());

        List<DtoEnvironmentalRecord> insertEnvironmentalRecordList = new ArrayList<>();
        //保存检测单数据
        for (DtoWorkSheetFolder folder : workSheetFolders) {
            folder.setSortId(oldFolder.getSortId());
            // 根据当前人员上一张同测试项目的工作单，配置环境与仪器使用记录
            for (DtoEnvironmentalRecord environmentalRecord : environmentalRecordList) {
                List<DtoInstrumentUseRecord> instrumentUseRecords = useRecords.stream().filter(p -> environmentalRecord.getId().equals(p.getEnvironmentalManageId())).collect(Collectors.toList());
                if (StringUtil.isEmpty(instrumentUseRecords)) {
                    continue;
                }
                DtoEnvironmentalRecord insert = new DtoEnvironmentalRecord();
                insert.setObjectId(folder.getId());
                insert.setObjectType(EnumLIM.EnumEnvRecObjType.实验室分析.getValue());
                insert.setUsePersonId(environmentalRecord.getUsePersonId());
                insert.setStartTime(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
                insert.setEndTime(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
                // 新增仪器记录
                List<DtoInstrumentUseRecord> insertInstrumentUseRecordList = new ArrayList<>();
                for (DtoInstrumentUseRecord instrumentUseRecord : instrumentUseRecords) {
                    if (insertInstrumentUseRecordList.stream().noneMatch(p -> p.getInstrumentId().equals(instrumentUseRecord.getInstrumentId()))) {
                        DtoInstrumentUseRecord insertUseRecord = new DtoInstrumentUseRecord();
                        BeanUtils.copyProperties(instrumentUseRecord, insertUseRecord, "id", "objectId", "environmentalManageId", "startTime", "endTime", "temperature", "humidity", "pressure");
                        insertUseRecord.setObjectId(folder.getId());
                        insertUseRecord.setEnvironmentalManageId(insert.getId());
                        insertUseRecord.setStartTime(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
                        insertUseRecord.setEndTime(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
                        insertInstrumentUseRecordList.add(insertUseRecord);
                    }
                }
                insert.setInstrumentUseRecord(insertInstrumentUseRecordList);
                insertEnvironmentalRecordList.add(insert);
            }
        }
        List<DtoWorkSheetFolder> newWorkSheetFolderList = comRepository.insertBatch(workSheetFolders);

        //保存子检测单数据
        comRepository.insertBatch(workSheets);

        //保存校准曲线数据
        workSheetCalibrationCurveService.createWorkSheetCarveCheck(workSheets);

        //保存参数数据
        comRepository.insertBatch(paramsDataList);

        //保存项目检测单数据
        comRepository.insertBatch(project2WorkSheetFolders);

        // 保存环境与仪器使用机记录
        if (insertEnvironmentalRecordList.size() > 0) {
            insertEnvironmentalRecordList.forEach(p -> {
                environmentalUseService.save(p);
                if (p.getInstrumentUseRecord().size() > 0) {
                    List<DtoInstrumentUseRecord> recordList = new ArrayList<>();
                    for (DtoInstrumentUseRecord instrumentRecord : p.getInstrumentUseRecord()) {
                        for (String testId : testAllIds) {
                            DtoInstrumentUseRecord instrRecord = new DtoInstrumentUseRecord();
                            BeanUtils.copyProperties(instrumentRecord, instrRecord, "id", "testIds");
                            instrRecord.setTestIds(testId);
                            recordList.add(instrRecord);
                        }
                    }
                    instrumentUseRecordRepository.save(recordList);
                }
            });
        }

        List<DtoLog> projectWorkSheetLogList = new ArrayList<>();
        for (DtoProject2WorkSheetFolder p2w : project2WorkSheetFolders) {
            String workSheetCode = workSheetFolders.stream().filter(p -> p.getId().equals(p2w.getWorkSheetFolderId())).map(DtoWorkSheetFolder::getWorkSheetCode).findFirst().orElse("");
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.创建检测单.toString());
            log.setLogType(EnumPRO.EnumLogType.项目检测单.getValue());
            log.setObjectId(p2w.getProjectId());
            log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
            log.setComment("创建了检测单：" + workSheetCode);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            projectWorkSheetLogList.add(log);
        }
        newLogService.createLog(projectWorkSheetLogList, EnumPRO.EnumLogType.项目检测单.getValue());
        newLogService.createLog(logs, EnumPRO.EnumLogType.检测单增删样品.getValue());
        // 创建嗅辨任务
        createOdTask(dtoTests, workSheetFolders, workSheets);
        //保证事物提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.创建检测单, "", "", newWorkSheetFolderList.stream().map(DtoWorkSheetFolder::getAnalystId).distinct().collect(Collectors.toList()));
                    }
                }
        );
        return newWorkSheetFolderList;
    }

    private void updateTestMsg(List<DtoAnalyseDataTemp> analyseDataList, List<DtoTest> testList, List<DtoSample> sampleList) {
        if (analyseDataList.size() > 0) {
            List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId)
                    .filter(sampleTypeId -> !UUIDHelper.GUID_EMPTY.equals(sampleTypeId))
                    .distinct().collect(Collectors.toList());
            List<String> testIds = testList.stream().map(DtoTest::getId).distinct().collect(Collectors.toList());
            List<DtoTestExpand> expandList = testExpandService.findByTestIds(testIds).stream()
                    .filter(p -> sampleTypeIds.contains(p.getSampleTypeId())).collect(Collectors.toList());
            List<String> dimensionIds = testList.stream().map(DtoTest::getDimensionId).distinct().collect(Collectors.toList());
            dimensionIds.addAll(expandList.stream().map(DtoTestExpand::getDimensionId).distinct().collect(Collectors.toList()));
            dimensionIds = dimensionIds.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            List<DtoDimension> dimensionList = dimensionIds.size() > 0 ? dimensionRepository.findAll(dimensionIds) : new ArrayList<>();
            analyseDataList.forEach(p -> {
                Optional<DtoSample> sampleOptional = sampleList.stream().filter(s -> s.getId().equals(p.getSampleId())).findFirst();
                String sampleTypeId = UUIDHelper.GUID_EMPTY;
                if (sampleOptional.isPresent()) {
                    sampleTypeId = sampleOptional.get().getSampleTypeId();
                }
                String finalSampleTypeId = sampleTypeId;
                Optional<DtoTestExpand> expandOptional = expandList.stream().filter(e -> p.getTestId().equals(e.getTestId())
                        && finalSampleTypeId.equals(e.getSampleTypeId())).findFirst();
                if (expandOptional.isPresent()) {
                    p.setMostSignificance(expandOptional.get().getMostSignificance());
                    p.setMostDecimal(expandOptional.get().getMostDecimal());
                    p.setDimensionId(expandOptional.get().getDimensionId());
                    p.setExamLimitValue(expandOptional.get().getExamLimitValue());
                    Optional<DtoDimension> dimensionOptional = dimensionList.stream()
                            .filter(d -> d.getId().equals(p.getDimensionId())).findFirst();
                    dimensionOptional.ifPresent(d -> p.setDimension(d.getDimensionName()));
                } else {
                    Optional<DtoTest> testOptional = testList.stream().filter(t -> p.getTestId().equals(t.getId())).findFirst();
                    if (testOptional.isPresent()) {
                        p.setMostSignificance(testOptional.get().getMostSignificance());
                        p.setMostDecimal(testOptional.get().getMostDecimal());
                        p.setDimensionId(testOptional.get().getDimensionId());
                        p.setExamLimitValue(testOptional.get().getExamLimitValue());
                        Optional<DtoDimension> dimensionOptional = dimensionList.stream()
                                .filter(d -> d.getId().equals(p.getDimensionId())).findFirst();
                        dimensionOptional.ifPresent(d -> p.setDimension(d.getDimensionName()));
                    }
                }
            });
        }
    }


    private Map<String, Object> createWorkSheet(Map<String, DtoTest> testMap,
                                                List<DtoAnalyseDataTemp> anaList,
                                                List<DtoRecordConfigParams> recordConfigParams,
                                                List<DtoProject2WorkSheetFolder> project2WorkSheetFolders,
                                                String analystId,
                                                String analystName,
                                                Date analyzeTime,
                                                DtoWorkSheetFolder dtoWorkSheetFolder,
                                                List<DtoLog> logs) {
        //返回对象接收
        Map<String, Object> returnMap = new HashMap<>();

        //小的检测单
        List<DtoWorkSheet> workSheets = new ArrayList<>();

        //参数数据
        List<DtoParamsData> paramsDataList = new ArrayList<>();

        //用来存储判断公式拆分在哪个选项卡内
        Map<String, DtoWorkSheet> workSheetMap = new HashMap<>();

        //样品数据是否存储
        Map<String, DtoSample> sampleIdMap = new HashMap<>();
        List<String> codes = new ArrayList<>();
        List<String> analyzeItemNameList = new ArrayList<>();

        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        List<DtoSample> sampleDatas = new ArrayList<>();

        List<String> recordIds = recordConfigParams.stream().map(DtoRecordConfigParams::getRecordId).distinct().collect(Collectors.toList());
        List<DtoRecordConfig> recordConfigs = StringUtil.isNotEmpty(recordIds) ? recordConfigService.findAll(recordIds) : new ArrayList<>();
        List<String> existsRecordIds = recordConfigs.stream().filter(r -> !r.getIsDeleted()).map(DtoRecordConfig::getId).collect(Collectors.toList());
        recordConfigParams = recordConfigParams.stream().filter(r -> existsRecordIds.contains(r.getRecordId())).collect(Collectors.toList());

        List<String> testIds = anaList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
        List<DtoWorkSheet> workSheetList = StringUtil.isNotEmpty(testIds) ? workSheetRepository.findByTestIdIn(testIds).stream()
                .filter(w -> StringUtils.isNotNullAndEmpty(w.getRecordId()) && !UUIDHelper.GUID_EMPTY.equals(w.getRecordId())).sorted(Comparator.comparing(DtoWorkSheet::getModifyDate, Comparator.reverseOrder())).collect(Collectors.toList()) : new ArrayList<>();
        Map<String, String> testId2RecordIdMap = new HashMap<>();
        if (StringUtil.isNotEmpty(workSheetList)) {
            DtoWorkSheet workSheet = workSheetList.get(0);
            testId2RecordIdMap.put(workSheet.getTestId(), workSheet.getRecordId());
        }
        for (DtoAnalyseDataTemp anaData : anaList) {
            //检测单与项目的关系
            String projectId = anaData.getProjectId();
            //检测单id
            String workSheetFolderId = dtoWorkSheetFolder.getId();
            DtoProject2WorkSheetFolder dtoProject2WorkSheetFolder = project2WorkSheetFolders.stream().filter(p -> p.getProjectId().equals(projectId) && p.getWorkSheetFolderId().equals(workSheetFolderId)).findFirst().orElse(null);
            if (StringUtil.isNull(dtoProject2WorkSheetFolder)) {
                dtoProject2WorkSheetFolder = new DtoProject2WorkSheetFolder();
                dtoProject2WorkSheetFolder.setProjectId(projectId);
                dtoProject2WorkSheetFolder.setWorkSheetFolderId(workSheetFolderId);
                project2WorkSheetFolders.add(dtoProject2WorkSheetFolder);
            }
            //判断数据所属小的检测单
            String testId = anaData.getTestId(); //测试项目id
            String analyseItemId = anaData.getAnalyseItemId();//分析项目id
            String redAnalyzeItemName = anaData.getRedAnalyzeItemName();//分析项目名称
            String redAnalyzeMethodName = anaData.getRedAnalyzeMethodName();//方法名称
            String redCountryStandard = anaData.getRedCountryStandard();//标准
            String analyzeMethodId = anaData.getAnalyzeMethodId();//方法id
            String lowerLimit = anaData.getLowerLimit();
            if (testMap.containsKey(testId)) {
                DtoTest dtoTest = testMap.get(testId);
                analyseItemId = dtoTest.getAnalyzeItemId();
                redAnalyzeItemName = dtoTest.getRedAnalyzeItemName();
                redAnalyzeMethodName = dtoTest.getRedAnalyzeMethodName();
                redCountryStandard = dtoTest.getRedCountryStandard();
                analyzeMethodId = dtoTest.getAnalyzeMethodId();
                if (!StringUtil.isNotNull(lowerLimit)) {
                    lowerLimit = dtoTest.getLowerLimit();
                    if (StringUtil.isNotNull(dtoTest.getTestExpandList())) {
                        Optional<DtoTestExpand> testExpandOptional = dtoTest.getTestExpandList().stream()
                                .filter(p -> anaData.getSampleTypeId().equals(p.getSampleTypeId())).findFirst();
                        if (testExpandOptional.isPresent()) {
                            lowerLimit = testExpandOptional.get().getLowerLimit();
                        }
                    }
                }
            }
            DtoWorkSheet dtoWorkSheet = new DtoWorkSheet();
            if (!workSheetMap.containsKey(testId)) { //不存在
                List<DtoRecordConfigParams> recordConfigParamsList = recordConfigParams.stream().filter(p -> testId.equals(p.getTestId())).collect(Collectors.toList());//参数
                String recordId = recordConfigParamsList.size() > 0 ? recordConfigParamsList.get(0).getRecordId() : UUIDHelper.GUID_EMPTY;
                if (testId2RecordIdMap.containsKey(testId) && recordConfigParamsList.stream().anyMatch(r -> r.getRecordId().equals(testId2RecordIdMap.get(testId)))) {
                    recordId = testId2RecordIdMap.get(testId);
                }
                String finalRecordId = recordId;
                //如果存在多个原始记录单配置，需要确定用的是哪个原始记录单表头参数
                recordConfigParamsList = recordConfigParamsList.stream().filter(p -> finalRecordId.equals(p.getRecordId())).collect(Collectors.toList());
                dtoWorkSheet.setTestId(anaData.getTestId());
                dtoWorkSheet.setAnalyseItemId(anaData.getAnalyseItemId());
                dtoWorkSheet.setRedAnalyzeItemName(anaData.getRedAnalyzeItemName());
                dtoWorkSheet.setParentId(dtoWorkSheetFolder.getId());
                dtoWorkSheet.setRecordId(recordId);
                workSheetMap.put(testId, dtoWorkSheet);//把不存在的加入到检测单中
                workSheets.add(dtoWorkSheet);
                paramsDataList.addAll(createWorkSheetParams(recordConfigParamsList, dtoWorkSheet.getId()));
            } else { //已经存在，修正里面的数据，说明几个测试项目数据共用一个worksheet
                dtoWorkSheet = workSheetMap.get(testId);
            }
            DtoAnalyseData analyseData = new DtoAnalyseData();
            analyseData.setId(anaData.getId());
            analyseData.setWorkSheetFolderId(dtoWorkSheetFolder.getId());
            analyseData.setWorkSheetId(dtoWorkSheet.getId());
            analyseData.setRedAnalyzeItemName(redAnalyzeItemName);
            analyseData.setRedAnalyzeMethodName(redAnalyzeMethodName);
            analyseData.setRedCountryStandard(redCountryStandard);
            analyseData.setAnalyseItemId(analyseItemId);
            analyseData.setAnalyzeTime(analyzeTime);
            analyseData.setAnalyzeMethodId(analyzeMethodId);
            analyseData.setAnalystId(analystId);
            analyseData.setAnalystName(analystName);
            analyseData.setStatus(EnumPRO.EnumAnalyseDataStatus.在测.name());
            analyseData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.在测.getValue());
            analyseData.setGatherCode(anaData.getSampleCode());
            //根据配置调整数据有效位数、量纲、检出限
            analyseData.setMostSignificance(anaData.getMostSignificance());
            analyseData.setMostDecimal(anaData.getMostDecimal());
            analyseData.setExamLimitValue(anaData.getExamLimitValue());
            analyseData.setDimensionId(anaData.getDimensionId());
            analyseData.setDimension(anaData.getDimension());
            analyseData.setLowerLimit(lowerLimit);
            anaDatas.add(analyseData);
            //comRepository.merge(analyseData); //直接修改

            DtoSample dtoSample = new DtoSample();
            dtoSample.setId(anaData.getSampleId());
            dtoSample.setStatus(EnumPRO.EnumSampleStatus.样品在检.name());
            dtoSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.正在分析.getValue());
//            if (anaData.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue())) {
//
//            }
            dtoSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
            if (!sampleIdMap.containsKey(anaData.getSampleId())) {
                //comRepository.merge(dtoSample);//直接修改
                sampleDatas.add(dtoSample);
                sampleIdMap.put(anaData.getSampleId(), dtoSample);
            }
            if (!codes.contains(anaData.getSampleCode())) {
                codes.add(anaData.getSampleCode());
            }
            if (!analyzeItemNameList.contains(redAnalyzeItemName)) {
                analyzeItemNameList.add(redAnalyzeItemName);
            }
        }
        if (anaDatas.size() > 0) {
            //comRepository.updateBatch(anaDatas);
            analyseDataRepository.save(anaDatas);
        }
        if (sampleDatas.size() > 0) {
//            comRepository.updateBatch(sampleDatas);
            sampleRepository.save(sampleDatas);
        }
        String anaComment = String.format("将样品:%s的检测项目:%s加入检测单。", String.join(",", codes), String.join(",", analyzeItemNameList));
        DtoLog anaWorkSheetLog = new DtoLog();
        anaWorkSheetLog.setComment(anaComment);
        anaWorkSheetLog.setLogType(EnumPRO.EnumLogType.检测单增删样品.getValue());
        anaWorkSheetLog.setObjectId(dtoWorkSheetFolder.getId());
        anaWorkSheetLog.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
        anaWorkSheetLog.setOperateInfo(EnumPRO.EnumLogOperateType.修改检测单.name());
        logs.add(anaWorkSheetLog);
        returnMap.put("workSheets", workSheets);
        returnMap.put("paramsData", paramsDataList);
        returnMap.put("logs", logs);
        return returnMap;
    }

    /**
     * 创建工作单
     *
     * @param methodId    方法if
     * @param analystId   分析人id
     * @param analystName 分析人员
     * @param analyzeTime 分析时间
     * @return 返回数据
     */
    private Map<String, Object> createWorkSheetFolder(String methodId,
                                                      String analystId,
                                                      String analystName,
                                                      Date analyzeTime) {
        DtoWorkSheetFolder dtoWorkSheetFolder = new DtoWorkSheetFolder();
        List<DtoLog> logs = new ArrayList<>();
        Map<String, Object> returnMap = new HashMap<>();
        dtoWorkSheetFolder.setCreateTime(new Date());
        dtoWorkSheetFolder.setWorkSheetCode(createWorkSheetCode());
        dtoWorkSheetFolder.setAnalystId(analystId);
        dtoWorkSheetFolder.setAnalystName(analystName);
        dtoWorkSheetFolder.setAnalyzeTime(analyzeTime);
        dtoWorkSheetFolder.setAnalyzeMethodId(methodId);
        dtoWorkSheetFolder.setStatus(EnumPRO.EnumWorkSheetStatus.新建.name());
        dtoWorkSheetFolder.setWorkStatus(EnumPRO.EnumWorkSheetStatus.新建.getValue());
        workflowService.createInstance(EnumPRO.EnumWorkflowCode.检测单.getValue(), dtoWorkSheetFolder.getId());
        //创建工作流
        returnMap.put("workSheetFolder", dtoWorkSheetFolder);

        String comment = String.format("创建了检测单:%s。", dtoWorkSheetFolder.getWorkSheetCode());
        DtoLog workSheetLog = new DtoLog();
        workSheetLog.setComment(comment);
        workSheetLog.setLogType(EnumPRO.EnumLogType.检测单增删.getValue());
        workSheetLog.setObjectId(dtoWorkSheetFolder.getId());
        workSheetLog.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
        workSheetLog.setOperateInfo(EnumPRO.EnumLogOperateType.创建检测单.name());
        logs.add(workSheetLog);
        returnMap.put("logs", logs);
        return returnMap;
    }


    /**
     * 创建工作单
     *
     * @param testMap                  测试项目的集合
     * @param anaList                  要创建在一张工作单的数据
     * @param recordConfigParams       参数配置
     * @param project2WorkSheetFolders 工作单相关的检测单
     * @param methodId                 方法if
     * @param analystId                分析人员id
     * @param analystName              分页人员名称
     * @param analyzeTime              分析时间
     * @return 返回数据
     */
    private Map<String, Object> createWorkSheetFolder(Map<String, DtoTest> testMap,
                                                      List<DtoAnalyseDataTemp> anaList,
                                                      List<DtoRecordConfigParams> recordConfigParams,
                                                      List<DtoProject2WorkSheetFolder> project2WorkSheetFolders,
                                                      String methodId,
                                                      String analystId,
                                                      String analystName,
                                                      Date analyzeTime) {
        //返回对象接收
        Map<String, Object> workSheetFolderMap = createWorkSheetFolder(methodId, analystId, analystName, analyzeTime);
        //要创建的工作单
        DtoWorkSheetFolder dtoWorkSheetFolder = (DtoWorkSheetFolder) workSheetFolderMap.get("workSheetFolder");
        //日志
        List<DtoLog> logs = (List<DtoLog>) workSheetFolderMap.get("logs");
        Map<String, Object> returnMap = createWorkSheet(testMap,
                anaList, recordConfigParams,
                project2WorkSheetFolders,
                analystId,
                analystName,
                analyzeTime,
                dtoWorkSheetFolder, logs);
        returnMap.put("workSheetFolder", dtoWorkSheetFolder);
        return returnMap;
    }

    /**
     * 创建检测单参数
     *
     * @param recordConfigParamsList 参数配置
     * @param workSheetId            检测单小id
     * @return 返回创建的工作参数
     */
    private List<DtoParamsData> createWorkSheetParams(List<DtoRecordConfigParams> recordConfigParamsList, String workSheetId) {
        //参数数据
        List<DtoParamsData> paramsDataList = new ArrayList<>();
        for (DtoRecordConfigParams recordConfigParams : recordConfigParamsList) {
            DtoParamsData paramsData = new DtoParamsData();
            paramsData.setObjectId(workSheetId);
            paramsData.setParamsConfigId(recordConfigParams.getId());
            paramsData.setParamsName(recordConfigParams.getAlias());
            paramsData.setDimension(recordConfigParams.getDimension());
            paramsData.setDimensionId(recordConfigParams.getDimensionId());
            paramsData.setParamsValue(recordConfigParams.getDefaultValue());
            paramsData.setObjectType(EnumPRO.EnumParamsDataType.检测单.getValue());
            paramsData.setOrderNum(recordConfigParams.getOrderNum());
            paramsDataList.add(paramsData);
        }
        return paramsDataList;
    }


    /**
     * 打开检测单下的数据
     *
     * @param workSheetFolderId 检测单id
     * @param isAll             是否显示所有的数据
     * @return 返回检测单数据
     */
    private List<DtoWorkSheetProperty> openWorkSheetById(String workSheetFolderId, String sampleId, Boolean isAll) {
        log.info("=================当前主线程:" + Thread.currentThread().getName() + "=========================");
        log.info("1." + DateUtil.nowTime("yyyy-MM-dd HH:mm:ss:ssss"));
        Date time1 = new Date();
        //定义返回容器存放工作单封装对象，每个封装对象对应一个子级工作单(Worksheet)
        List<DtoWorkSheetProperty> workSheetProperties = new ArrayList<>();
        //根据id查找父级工作单
        DtoWorkSheetFolder dtoWorkSheetFolder = repository.findOne(workSheetFolderId);
        List<String> samIds = new ArrayList<>();
        Boolean isSample = false;
        Boolean isCrossDay = false;
        Date finishTime = dtoWorkSheetFolder.getFinishTime();
        //拓展字段
        Map<String, Object> expandValue = new HashMap<>();
        //防止数据已删除程序出错，需要判断是否为null
        if (StringUtil.isNotNull(dtoWorkSheetFolder)) {
            //获取分析方法
            DtoAnalyzeMethod method = analyzeMethodService.findOne(dtoWorkSheetFolder.getAnalyzeMethodId());
            //先判断方法是否同时完成。同时完成的，放到一个选项卡，否则按测试项目拆分选项卡
            Boolean isCompleteTogether = method.getIsCompleteTogether();
            //是否按照样品录入(选择按照样品录入的时候，可同时完成一定是否)
            isSample = method.getIsInputBySample();
            //是否跨天完成
            isCrossDay = method.getIsCrossDay();
            //所有的分析数据集
            List<DtoAnalyseDataTemp> analyseDataTemps = findDataByWorksheetFolderId(Boolean.FALSE, Boolean.FALSE, workSheetFolderId);
            //pageBean.getData();
            log.info("2." + DateUtil.nowTime("yyyy-MM-dd HH:mm:ss:ssss"));
            Date time2 = new Date();
            log.info("=======================================查询工作单数据用时：" + (time2.getTime() - time1.getTime()) + "ms========================================");
            this.setGroupSampleId(analyseDataTemps);
            //设置原样的类型，用于空白样的平行和加标样排序
            this.setAssociateSampleType(analyseDataTemps);
            // 工作单默认赋值分析项目排序
            this.setWorkSheetSortDefault(dtoWorkSheetFolder);

            List<String> allSampleIdList = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> allSampleList = sampleService.findBySampleIds(allSampleIdList);

            //找到所有的样品(排除替代样)
            List<String> sIds = analyseDataTemps.stream().filter(p -> p.getQcType().equals(new QualityReplace().qcTypeValue()))
                    .map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> sampleList = allSampleList.stream().filter(p -> !sIds.contains(p.getId())).collect(Collectors.toList());
            List<String> samParamIds = new ArrayList<>();
            Set<String> recIds = sampleList.stream().map(DtoSample::getReceiveId).collect(Collectors.toSet());
            if (recIds.size() > 0) {
                // 第三方取消开关控制
//                if (analyseDataService.isReceiveParamsAudio()) {
//                    List<String> recordIds = receiveSampleRecordService.findAll(recIds).stream()
//                            .filter(p -> p.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认.getValue()))
//                            .map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
//                    if (recordIds.size() > 0) {
//                        samParamIds = sampleList.stream().filter(p -> recordIds.contains(p.getReceiveId())).map(DtoSample::getId).collect(Collectors.toList());
//                    }
//                } else {
//                    samParamIds = sampleList.stream().filter(p -> recIds.contains(p.getReceiveId())).map(DtoSample::getId).collect(Collectors.toList());
//                }
                samParamIds = sampleList.stream().filter(p -> recIds.contains(p.getReceiveId())).map(DtoSample::getId).collect(Collectors.toList());

            }
            if (isSample) {
                if (!StringUtil.isNotEmpty(sampleId) || UUIDHelper.GUID_EMPTY.equals(sampleId)) {
                    sampleId = sampleList.get(0).getId();
                }
                //根据样品编号找到需要展示的样品
                String finalSampleId = sampleId;
                String samCode = sampleList.stream().filter(p -> p.getId().equals(finalSampleId)).map(DtoSample::getCode).findFirst().orElse("");
                List<String> sampleIds = sampleList.stream().filter(p -> p.getCode().equals(samCode)).map(DtoSample::getId).collect(Collectors.toList());
                samIds.addAll(sampleIds);
            }
            //获取样品id和项目id的映射
            Map<String, String> sampleId2ProjectIdMap = getSampleId2ProjectIdMap(allSampleList);
            //获取样品id和项目要求完成日期的映射
            Map<String, String> projectId2DeadLineMap = getProjectId2DeadLineMap(new ArrayList<>(sampleId2ProjectIdMap.values()));

            //主要是用来获取质控任务的相关数据
            List<DtoReceiveSampleRecordTemp> receiveSampleRecords = new ArrayList<>();
            List<String> qmAnaIds = analyseDataTemps.stream().filter(p -> StringUtil.isNotNull(p.getIsQm())
                    && p.getIsQm()).map(DtoAnalyseDataTemp::getId).collect(Collectors.toList());
            List<DtoQualityManage> qualityManages = new ArrayList<>();
            if (qmAnaIds.size() > 0) {
                //送样单ids
                List<String> receiveIds = analyseDataTemps.stream().filter(p -> qmAnaIds.contains(p.getId())).map(DtoAnalyseDataTemp::getReceiveId).distinct().collect(Collectors.toList());
                receiveSampleRecords = receiveSampleRecordService.findReceiveSampleRecordList(receiveIds);
                if (receiveSampleRecords.size() > 0) {
                    //现场参数需要过滤已审核的送样单
                    receiveSampleRecords = receiveSampleRecords.stream()
                            .filter(p -> p.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认.getValue())).collect(Collectors.toList());
                }
                qualityManages = qualityManageRepository.findByAnaIdIn(qmAnaIds);
            }
            //找到质控相应的数据
            List<String> qcIds = analyseDataTemps.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getQcId()) && !p.getQcId().equals(UUIDHelper.GUID_EMPTY))
                    .map(DtoAnalyseDataTemp::getQcId).distinct().collect(Collectors.toList());
            List<DtoQualityControl> qualityControls = new ArrayList<>();
            //qualityControls的关联样品id与样品对象的映射关系
            Map<String, DtoSample> asoSamId2SamMap = new HashMap<>();
            if (qcIds.size() > 0) {
                qualityControls = qualityControlService.findAll(qcIds);
                //找到对应样品的替代样
                List<String> tdQCIds = qualityControls.stream().filter(p -> samIds.contains(p.getAssociateSampleId())
                        && p.getQcType().equals(new QualityReplace().qcTypeValue())).map(DtoQualityControl::getId).collect(Collectors.toList());
                if (tdQCIds.size() > 0) {
                    //找到替代样的样品编号
                    List<String> tdSampleIds = analyseDataTemps.stream().filter(p -> tdQCIds.contains(p.getQcId()))
                            .map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
                    samIds.addAll(tdSampleIds);
                }
                List<String> associateSampleIdList = qualityControls.stream().map(DtoQualityControl::getAssociateSampleId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(associateSampleIdList)) {
                    List<DtoSample> associateSampleList = sampleRepository.findByIds(associateSampleIdList);
                    asoSamId2SamMap = associateSampleList.stream().collect(Collectors.toMap(DtoSample::getId, sample -> sample));
                }
            }
            //分析数据ids
            List<String> anaAllIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());
            //所有的测试项目ids
            List<String> testIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
            //相关的测试项目
            List<DtoTest> dtoTests = testService.findRedisByIds(testIds);
            Date time4 = new Date();
            //记录单相关的参数
            List<DtoRecordConfigParams> recordConfigParams = recordConfigParamsConfigService.findTestParamsByTestIds(testIds);
            Date time5 = new Date();
            log.info("=======================================查询记录单参数用时：" + (time5.getTime() - time4.getTime()) + "ms========================================");
            //原始记录单配置
            List<DtoRecordConfigTest> recordConfigs = new ArrayList<>();
            if (testIds.size() > 0) {
                recordConfigs = recordConfigService.findRecordConfigByTestIds(testIds);
            }
            Date time6 = new Date();
            //启用公式的测试项目
            List<String> tIds = dtoTests.stream().filter(p -> StringUtil.isNotNull(p.getIsUseFormula()) && p.getIsUseFormula()).map(DtoTest::getId).collect(Collectors.toList());
            //要找到测试项目的公式（最新的配置的数据，已排除加删及过滤了不启用公式的数据）
            List<DtoParamsFormula> formulaList = new ArrayList<>();
            List<DtoParamsTestFormula> paramsTestFormulas = new ArrayList<>();
            if (tIds.size() > 0) {
                Date time14 = new Date();
                List<DtoAnalyseData> anaDataListByTest = analyseDataRepository.findByTestIdInAndWorkSheetFolderIdAndIsDeletedFalse(testIds, workSheetFolderId);
                anaDataListByTest = anaDataListByTest.stream().filter(p -> "".equals(p.getTestValue())).collect(Collectors.toList());
                //需要根据测试项目找到最新一次的公式
                Map<String, String> t2aMap = anaDataListByTest.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId,
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.stream().
                                max(Comparator.comparing(DtoAnalyseData::getDataInputTime)).orElse(new DtoAnalyseData()).getId())));
                Map<String, String> t2fMap = new HashMap<>();
                Date time15 = new Date();
                log.info("=======================================查询最后一次公式用时：" + (time15.getTime() - time14.getTime()) + "ms========================================");
                //找到数据应的公式
                if (t2aMap.size() > 0) {
                    List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(new ArrayList<>(t2aMap.values()));
                    Map<String, List<DtoAnalyseOriginalRecord>> originalGroups = originalRecordList.stream().collect(Collectors.groupingBy(DtoAnalyseOriginalRecord::getAnalyseDataId));
                    for (String tId : t2aMap.keySet()) {
                        String anaId = t2aMap.get(tId);
//                        Optional<DtoAnalyseOriginalRecord> recordOptional = originalRecordList.stream().filter(p -> p.getAnalyseDataId().equals(anaId)).findFirst();
                        Optional<DtoAnalyseOriginalRecord> recordOptional = StringUtil.isNotEmpty(originalGroups) ?
                                originalGroups.getOrDefault(anaId, new ArrayList<>()).stream().findFirst() : Optional.empty();
                        recordOptional.ifPresent(dtoAnalyseOriginalRecord -> t2fMap.put(tId, dtoAnalyseOriginalRecord.getTestFormulaId()));
                    }
                }
                //判断是否所有测试项目都有公式
                List<String> outTestIds = new ArrayList<>();
                for (String tId : tIds) {
                    if (!t2fMap.containsKey(tId)) {
                        outTestIds.add(tId);
                    }
                }
                if (t2fMap.size() > 0) {
                    formulaList = paramsFormulaService.findAll(t2fMap.values());
                }
                if (outTestIds.size() > 0) {
                    formulaList.addAll(paramsFormulaService.findByObjectIds(outTestIds));
                }
                //所有的公式ids
                List<String> fIds = formulaList.stream().map(DtoParamsFormula::getId).distinct().collect(Collectors.toList());
                paramsTestFormulas = paramsTestFormulaService.findByObjIds(fIds);
                Date time16 = new Date();
                log.info("=======================================处理公式参数用时：" + (time16.getTime() - time15.getTime()) + "ms========================================");
            }
            Date time7 = new Date();
            log.info("=======================================处理工作单公式用时：" + (time7.getTime() - time6.getTime()) + "ms========================================");
            //分析数据相关的公式数据
            List<DtoAnalyseOriginalRecord> analyseOriginalRecords = new ArrayList<>();
            if (anaAllIds.size() > 0) {
                analyseOriginalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(anaAllIds);
            }
            //子级工作单id
            List<String> workSheetIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getWorkSheetId).distinct().collect(Collectors.toList());//小型的检测单Id
            //检测单相关的参数数据
            List<DtoParamsData> paramsDataList = new ArrayList<>();
            //小类的相关检测单
            List<DtoWorkSheet> workSheets = new ArrayList<>();
            if (StringUtil.isNotEmpty(workSheetIds)) {
                paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(workSheetIds, EnumPRO.EnumParamsDataType.检测单.getValue());
                workSheets = workSheetRepository.findAll(workSheetIds);
            }
            //寻找公式上的参数数据，只有有公式或者录入过公式数据的才进行匹配
            List<DtoParamsData> sampleParamsDataList = new ArrayList<>();
            List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves = new ArrayList<>();
            if (analyseOriginalRecords.size() > 0 || formulaList.size() > 0) {
                sampleParamsDataList = paramsDataRepository.findByObjectIdInAndObjectType(samParamIds, EnumPRO.EnumParamsDataType.样品.getValue());
                Set<String> paramsConfigIds = paramsDataList.stream().map(DtoParamsData::getParamsConfigId).collect(Collectors.toSet());
                List<DtoParamsConfig> configList = StringUtil.isNotEmpty(paramsConfigIds) ? paramsConfigRepository.findAll(paramsConfigIds) : new ArrayList<>();
                sampleParamsDataList.forEach(p -> {
                    Optional<DtoParamsConfig> config = configList.stream().filter(c -> p.getParamsConfigId().equals(c.getId())).findFirst();
                    config.ifPresent(c -> {
                        p.setAnalyzeItemId(c.getAnalyzeItemId());
                        p.setAlias(c.getAlias());
                    });
                });
                workSheetCalibrationCurves = getWorkSheetCalibrationCurve(paramsTestFormulas, workSheetIds);
            }
            List<DtoAnalyzeItemSortDetail> sortDetails = analyzeItemSortDetailRepository.findBySortId(dtoWorkSheetFolder.getSortId());
            Date time8 = new Date();
            log.info("=======================================处理工作单参数曲线用时：" + (time8.getTime() - time7.getTime()) + "ms========================================");
            List<Map<String, Object>> anaMapList = new ArrayList<>();
            Map<String, List<DtoTestFormulaParamsConfig>> testFormulaParamsConfigMap = new HashMap<>();
            List<String> sampleTypeIdForTemps = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIdForTemps) ? sampleTypeService.findAll(sampleTypeIdForTemps) : new ArrayList<>();
            List<String> allGroupIds = sampleTypeList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFieldTaskGroupId())
                    && !p.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getFieldTaskGroupId)
                    .distinct().collect(Collectors.toList());
            List<DtoSampleTypeGroup> allSampleTypeGroups = StringUtil.isNotEmpty(allGroupIds)
                    ? sampleTypeGroupRepository.findByParentIdInAndGroupType(allGroupIds, EnumLIM.EnumGroupType.分组.getValue()) : new ArrayList<>();
            List<DtoSampleTypeGroup2Test> allGroup2TestList = StringUtil.isNotEmpty(allSampleTypeGroups)
                    ? sampleTypeGroup2TestService.findBySampleTypeGroupIds(allSampleTypeGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList())) : new ArrayList<>();
            Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, dto -> dto));
            SampleGroupCriteria sampleGroupCriteria = new SampleGroupCriteria();
            sampleGroupCriteria.setSampleIds(allSampleIdList);
            sampleGroupCriteria.setTestIds(testIds);
            List<DtoSampleItemGroupTag> sampleItemGroupTagList = sampleGroupService.findSampleItemGroupTag(sampleGroupCriteria);
            Date time9 = new Date();
            log.info("=======================================获取样品分组数据用时：" + (time9.getTime() - time8.getTime()) + "ms========================================");
            DtoSolutionCalibration solutionCalibration = solutionCalibrationRepository.findByWorkSheetFolderId(workSheetFolderId);
            long s1 = 0;
            long s2 = 0;
            long s3 = 0;

            // 多线程调用时，无法使用this
            WorkSheetFolderService workSheetFolderService = SpringContextAware.getBean(WorkSheetFolderService.class);
            List<Future<List<Map<String, Object>>>> analyseDataResultList = new ArrayList<>();
            List<DtoAnalyseDataTemp> list = null;
            final int batchSize = 50;
            for (DtoAnalyseDataTemp analyseDataTemp : analyseDataTemps) {
                if (list == null) {
                    list = new ArrayList<>();
                }
                if (list.size() < batchSize) {
                    list.add(analyseDataTemp);
                } else if (list.size() == batchSize) {
                    //多线程处理排序
                    analyseDataResultList.add(workSheetFolderService.processSingleAnalyseData(list, analyseOriginalRecords, formulaList,
                            paramsTestFormulas, qualityControls, receiveSampleRecords, qualityManages, asoSamId2SamMap,
                            sampleItemGroupTagList, analyseDataTemps, sampleId2ProjectIdMap, projectId2DeadLineMap,
                            testFormulaParamsConfigMap, sampleParamsDataList, workSheetCalibrationCurves, sampleTypeMap,
                            allSampleTypeGroups, allGroup2TestList, solutionCalibration, sortDetails));
                    list = new ArrayList<>();
                    list.add(analyseDataTemp);
                }
            }
            //如果存在最后一批样，需要单独去排序处理
            if (StringUtil.isNotEmpty(list)) {
                analyseDataResultList.add(workSheetFolderService.processSingleAnalyseData(list, analyseOriginalRecords, formulaList,
                        paramsTestFormulas, qualityControls, receiveSampleRecords, qualityManages, asoSamId2SamMap,
                        sampleItemGroupTagList, analyseDataTemps, sampleId2ProjectIdMap, projectId2DeadLineMap,
                        testFormulaParamsConfigMap, sampleParamsDataList, workSheetCalibrationCurves, sampleTypeMap,
                        allSampleTypeGroups, allGroup2TestList, solutionCalibration, sortDetails));
            }
            //处理多线程处理的结果
            try {
                for (Future<List<Map<String, Object>>> analyseDataResult : analyseDataResultList) {
                    while (true) {
                        if (analyseDataResult.isDone() && !analyseDataResult.isCancelled()) {
                            anaMapList.addAll(analyseDataResult.get());
                            break;
                        } else {
                            //防止CPU高速轮询被耗空
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BaseException("......多线程处理分析数据出错......");
            }

            log.info("=================================================s1:" + s1 + "ms=================================================");
            log.info("=================================================s2:" + s2 + "ms=================================================");
            log.info("=================================================s3:" + s3 + "ms=================================================");
            Date time10 = new Date();
            log.info("=======================================循环处理工作单数据用时：" + (time10.getTime() - time9.getTime()) + "ms========================================");
            anaMapList.forEach(anaMap -> {
                sampleList.stream().filter(s -> s.getId().equals(anaMap.get("sampleId").toString())).findFirst().ifPresent(s -> {
                    anaMap.put("orderNum", s.getSortNum());
                });
            });

            Date time100 = new Date();
            log.info("=======================================获取orderNum用时：" + (time100.getTime() - time10.getTime()) + "ms========================================");
            //对sampleList进行排序
            List<DtoSample> sortedSampleList = new ArrayList<>();
            Map<String, String> samMap = new HashMap<>();
            List<DtoSample> finalSortedSampleList = sortedSampleList;
            sampleList.forEach(p -> {
                if (!samMap.containsKey(p.getCode())) {
                    samMap.put(p.getCode(), p.getId());
                    finalSortedSampleList.add(p);
                }
            });
            sortedSampleList = finalSortedSampleList.stream().sorted(Comparator.comparing(DtoSample::getSortNum)
                    .thenComparing(DtoSample::getCode)).collect(Collectors.toList());
            Date time11 = new Date();
            //判断方法是否按样品录入
            if (isCompleteTogether || isSample) {
                //可同时完成的需要判断公式是否一致，不一致也要按选项卡区分
                workSheetProperties = openWorkSheetByFormula(analyseDataTemps, dtoWorkSheetFolder,
                        workSheets, anaMapList, paramsDataList, recordConfigParams, recordConfigs,
                        testFormulaParamsConfigMap, isAll, samIds, sortedSampleList);
            } else {
                //不能同时完成的，肯定要按选项卡进行拆分数据录入
                workSheetProperties = openWorkSheetByWorkSheetId(analyseDataTemps, dtoWorkSheetFolder,
                        workSheets, anaMapList, paramsDataList, recordConfigParams, recordConfigs,
                        testFormulaParamsConfigMap, isAll, samIds, sortedSampleList);
            }
            Date time12 = new Date();
            log.info("=======================================获取工作单打开的数据结果：" + (time12.getTime() - time11.getTime()) + "ms========================================");
        }
        Date time13 = new Date();
        //保证编辑页面的选项卡与列表中的分析项目一致
        List<DtoWorkSheetTest> workSheetTests = workSheetProperties.stream().map(DtoWorkSheetProperty::getTest).collect(Collectors.toList());
        //将子级选项卡按照列表中的顺序进行排序，如列表中分析项目为铜锌铅镉，子级选项卡顺序也应为铜锌铅镉
        this.setAnalyseItemOrderNumForWorksheetTest(workSheetTests, dtoWorkSheetFolder.getSortId());
        workSheetProperties.sort(Comparator.comparing(a -> a.getTest().getAnalyseItemOrderNum(), Comparator.reverseOrder()));
        Boolean finalIsSample = isSample;
        workSheetProperties.forEach(p -> {
            p.setSortId(dtoWorkSheetFolder.getSortId());
            p.setIsSample(finalIsSample);
        });
        //添加是否跨天完成字段返回
        List<DtoWorkSheetProperty> resultData = new ArrayList<>();
        String finishTimeStr = DateUtil.dateToString(finishTime, DateUtil.YEAR);
        for (DtoWorkSheetProperty workSheetProperty : workSheetProperties) {
            expandValue.put("isCrossDay", isCrossDay);
            expandValue.put("finishTime", finishTimeStr);
            if ("1753-01-01".equals(finishTimeStr)) {
                expandValue.put("finishTime", DateUtil.dateToString(workSheetProperty.getAnalyzeTime(), DateUtil.YEAR));
            }
            DynamicBean dynamicBean = ReflectUtil.getDynamicBean(workSheetProperty, expandValue);
            workSheetProperty = (DtoWorkSheetProperty) ReflectUtil.getTarget(dynamicBean);
            resultData.add(workSheetProperty);
        }
        Date time3 = new Date();
        log.info("=======================================处理跨天字段返回：" + (time3.getTime() - time13.getTime()) + "ms========================================");
        log.info("=======================================方法总耗时：" + (time3.getTime() - time1.getTime()) + "ms=============================");
        return resultData;
    }


    /**
     * 多线程处理分析数据构建
     *
     * @param analyseDataTempList 分析数据临时实体集合
     * @return
     */
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    @Override
    public Future<List<Map<String, Object>>> processSingleAnalyseData(List<DtoAnalyseDataTemp> analyseDataTempList,
                                                                      List<DtoAnalyseOriginalRecord> analyseOriginalRecords,
                                                                      List<DtoParamsFormula> formulaList,
                                                                      List<DtoParamsTestFormula> paramsTestFormulas,
                                                                      List<DtoQualityControl> qualityControls,
                                                                      List<DtoReceiveSampleRecordTemp> receiveSampleRecords,
                                                                      List<DtoQualityManage> qualityManages,
                                                                      Map<String, DtoSample> asoSamId2SamMap,
                                                                      List<DtoSampleItemGroupTag> sampleItemGroupTagList,
                                                                      List<DtoAnalyseDataTemp> analyseDataTemps,
                                                                      Map<String, String> sampleId2ProjectIdMap,
                                                                      Map<String, String> projectId2DeadLineMap,
                                                                      Map<String, List<DtoTestFormulaParamsConfig>> testFormulaParamsConfigMap,
                                                                      List<DtoParamsData> sampleParamsDataList,
                                                                      List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves,
                                                                      Map<String, DtoSampleType> sampleTypeMap,
                                                                      List<DtoSampleTypeGroup> allSampleTypeGroups,
                                                                      List<DtoSampleTypeGroup2Test> allGroup2TestList,
                                                                      DtoSolutionCalibration solutionCalibration,
                                                                      List<DtoAnalyzeItemSortDetail> sortDetails) {
        List<Map<String, Object>> anaMapList = new ArrayList<>();

        for (DtoAnalyseDataTemp dtoAnalyseDataTemp : analyseDataTempList) {

            String anaId = dtoAnalyseDataTemp.getId();
            String testId = dtoAnalyseDataTemp.getTestId();
            String sampleTypeId = dtoAnalyseDataTemp.getSampleTypeId();
            String bigSampleTypeId = dtoAnalyseDataTemp.getBigSampleTypeId();

            String formula = "";
            String formulaId = UUIDHelper.GUID_EMPTY;
            //优先判断数据上是否存储过公式，如果存储过，那么就是用该公式
            //如果未存储过，否启用公式
            //如果启用，判断数据上是否配置小类公式，
            //如果小类未配置公式，判断大类是否配置公式
            Boolean isOriginal = false;//是否有原始数据
            List<DtoParamsTestFormula> paramsTestFormulaList = new ArrayList<>();
            Optional<DtoAnalyseOriginalRecord> optionalOriginalRecord = analyseOriginalRecords.stream()
                    .filter(p -> p.getAnalyseDataId().equals(anaId)).findFirst();
            if (optionalOriginalRecord.isPresent()) {
                TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
                };
                DtoAnalyseOriginalRecord analyseOriginalRecord = optionalOriginalRecord.get();
                formula = analyseOriginalRecord.getTestFormula();
                formulaId = analyseOriginalRecord.getTestFormulaId();
                if (StringUtil.isNotEmpty(analyseOriginalRecord.getJson())) {
                    paramsTestFormulaList = JsonIterator.deserialize(analyseOriginalRecord.getJson(), typeLiteral);
                }
                isOriginal = true;
            } else {
                Optional<DtoParamsFormula> optionalParamsFormula = formulaList.stream()
                        .filter(p -> p.getSampleTypeId().equals(sampleTypeId) && p.getObjectId().equals(testId))
                        .max(Comparator.comparing(DtoParamsFormula::getConfigDate));
                if (optionalParamsFormula.isPresent()) { //小类是否有公式
                    DtoParamsFormula dtoParamsFormula = optionalParamsFormula.get();
                    formula = dtoParamsFormula.getFormula();
                    formulaId = dtoParamsFormula.getId();
                    paramsTestFormulaList = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(dtoParamsFormula.getId())).collect(Collectors.toList());

                } else { //大类是否有公式
                    Optional<DtoParamsFormula> optionalBigParamsFormula = formulaList.stream()
                            .filter(p -> p.getSampleTypeId().equals(bigSampleTypeId) && p.getObjectId().equals(testId))
                            .max(Comparator.comparing(DtoParamsFormula::getConfigDate));
                    if (optionalBigParamsFormula.isPresent()) {
                        DtoParamsFormula dtoParamsFormula = optionalBigParamsFormula.get();
                        formulaId = dtoParamsFormula.getId();
                        formula = dtoParamsFormula.getFormula();
                        paramsTestFormulaList = paramsTestFormulas.stream().filter(p -> p.getObjId().equals(dtoParamsFormula.getId())).collect(Collectors.toList());
                    }
                }
            }
            List<DtoParamsTestFormula> finalParamsTestFormulas = paramsTestFormulas;
            paramsTestFormulaList.forEach(pf -> {
                Optional<DtoParamsTestFormula> testFormula = finalParamsTestFormulas.stream()
                        .filter(p -> pf.getAlias().equals(p.getAlias())).findFirst();
                testFormula.ifPresent(p -> {
                    pf.setDimension(p.getDimension());
                    pf.setDetectionLimit(p.getDetectionLimit());
                    pf.setCalculationMode(p.getCalculationMode());
                    pf.setSlashValue(p.getSlashValue());
                });
            });

            dtoAnalyseDataTemp.setFormula(formula);
            dtoAnalyseDataTemp.setFormulaId(formulaId);
            getAnalyseDataTemp(dtoAnalyseDataTemp, qualityControls, receiveSampleRecords, qualityManages, asoSamId2SamMap);
            //处理待分组标识样品编号
            DtoSampleItemGroupTag sampleItemGroupTag = sampleItemGroupTagList.stream().filter(d -> d.getSampleId().equals(dtoAnalyseDataTemp.getSampleId())
                    && d.getTestId().equals(dtoAnalyseDataTemp.getTestId())).findFirst().orElse(null);
            dtoAnalyseDataTemp.setSampleCodeWithTag(dtoAnalyseDataTemp.getSampleCode());
            if (sampleItemGroupTag != null) {
                if (StringUtil.isNotEmpty(sampleItemGroupTag.getSampleCodeTag())) {
                    if (!sampleItemGroupTag.getSampleCategory().equals(EnumPRO.EnumSampleCategory.串联样.getValue())) {
                        dtoAnalyseDataTemp.setSampleCodeWithTag(dtoAnalyseDataTemp.getSampleCode() + "-" + sampleItemGroupTag.getSampleCodeTag());
                    } else {
                        String yySampleId = sampleItemGroupTag.getAssociateSampleId();
                        Optional<DtoSampleItemGroupTag> yySampleGroupTag = sampleItemGroupTagList.stream().filter(group -> yySampleId.equals(group.getSampleId())).findFirst();
                        if (yySampleGroupTag.isPresent()) {
                            Optional<DtoAnalyseDataTemp> tempOptional = analyseDataTemps.stream().filter(data -> data.getSampleId().equals(yySampleId)).findFirst();
                            if (tempOptional.isPresent()) {
                                String clCode = dtoAnalyseDataTemp.getSampleCode().replace(tempOptional.get().getSampleCode(), "");
                                dtoAnalyseDataTemp.setSampleCodeWithTag(tempOptional.get().getSampleCode() + "-" + sampleItemGroupTag.getSampleCodeTag() + clCode);
                            }
                        }
                    }
                }
            }
            if (new QualityReplace().qcTypeValue().equals(dtoAnalyseDataTemp.getQcType())) {
                String yySampleId = dtoAnalyseDataTemp.getAssociateSampleId();
                Optional<DtoSampleItemGroupTag> yySampleGroupTag = sampleItemGroupTagList.stream().filter(group -> yySampleId.equals(group.getSampleId())).findFirst();
                if (yySampleGroupTag.isPresent()) {
                    Optional<DtoAnalyseDataTemp> tempOptional = analyseDataTemps.stream().filter(data -> data.getSampleId().equals(yySampleId)).findFirst();
                    tempOptional.ifPresent(analyseDataTemp -> dtoAnalyseDataTemp.setSampleCodeWithTag(analyseDataTemp.getSampleCode() + "-" + yySampleGroupTag.get().getSampleCodeTag()));
                } else {
                    Optional<DtoAnalyseDataTemp> tempOptional = analyseDataTemps.stream().filter(data -> data.getSampleId().equals(yySampleId)).findFirst();
                    tempOptional.ifPresent(analyseDataTemp -> dtoAnalyseDataTemp.setSampleCodeWithTag(analyseDataTemp.getSampleCode()));
                }
            }
            //给数据加入排序值
            Map<String, Object> map = getAnalyseDataMap(dtoAnalyseDataTemp);
            if (new QualityReplace().qcTypeValue().equals(dtoAnalyseDataTemp.getQcType())) {
                map.put("sampleCodeWithTag", dtoAnalyseDataTemp.getSampleCode());
            }
            map.put("analyseDataTemp", dtoAnalyseDataTemp);
            String smpId = map.get("sampleId").toString();
            //获取项目上的要求完成日期
            String deadLineStr = "";
            if (sampleId2ProjectIdMap.containsKey(smpId)) {
                String projectId = sampleId2ProjectIdMap.get(smpId);
                deadLineStr = projectId2DeadLineMap.getOrDefault(projectId, "");
            }
            map.put("deadLine", deadLineStr);
            map.put("sortNumber", 0);
            Boolean isRed = false;

            if (StringUtils.isNotNullAndEmpty(formulaId)) {
                List<DtoTestFormulaParamsConfig> dtoTestFormulaParamsConfigList = new ArrayList<>();
                if (testFormulaParamsConfigMap.containsKey(formulaId)) {
                    dtoTestFormulaParamsConfigList = testFormulaParamsConfigMap.get(formulaId);
                }


                //获取到公式参数
                isRed = getParamsTestFormula(paramsTestFormulaList, sampleParamsDataList, workSheetCalibrationCurves, dtoTestFormulaParamsConfigList,
                        formula, isOriginal, dtoAnalyseDataTemp, map, new ArrayList<>(), sampleTypeMap, allSampleTypeGroups, allGroup2TestList, solutionCalibration);
                testFormulaParamsConfigMap.put(formulaId, dtoTestFormulaParamsConfigList);

            }
            //数据不一致标红
            map.put("isRedSampleCode", isRed);
            Optional<DtoAnalyzeItemSortDetail> analyzeItemOptional = sortDetails.parallelStream().filter(a -> dtoAnalyseDataTemp.getAnalyseItemId().equals(a.getAnalyzeItemId())).findFirst();
            analyzeItemOptional.ifPresent(a -> map.put("sortNumber", a.getOrderNum()));
            anaMapList.add(map);
        }
        return new AsyncResult<>(anaMapList);
    }


    @Override
    @Transactional
    public void resetSortNum(String workSheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderId(workSheetFolderId);
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleService.findBySampleIds(sampleIds) : new ArrayList<>();
        List<OrderReviseVO> orderReviseVOList = getQualityList();
        sampleList.forEach(sample -> sample.setSortNum(sampleService.getSortOrder(sample, orderReviseVOList)));
    }

    @Override
    @Transactional
    public void refreshSignature(String worksheetFolderId) {
        DtoWorkSheetFolder workSheetFolder = repository.findOne(worksheetFolderId);
        if (EnumPRO.EnumWorkSheetStatus.审核通过.getValue().equals(workSheetFolder.getWorkStatus())) {
            //签名
            Map<String, Integer> mapType = new HashMap<>();
            //移除记录单签名的类型，去除签名上岗证人员等于分析者
            mapType.put(EnumPRO.EnumSigType.分析者.getName(), 2);
            mapType.put(EnumPRO.EnumSigType.复核者.getName(), 1);
            mapType.put(EnumPRO.EnumSigType.审核者.getName(), 1);
            //有参数分析日期，不能去除分析日期
            mapType.put(EnumPRO.EnumSigType.校核日期.getName(), 1);
            mapType.put(EnumPRO.EnumSigType.复核日期.getName(), 1);
            //原始记录单根据工作单状态签名
            signatureService.removeWorkSheetSig(worksheetFolderId, mapType);

            //重新设置签名
            try {
                Integer type = EnumPRO.EnumSigType.分析者.getValue();
                //分析日期获取检测单的分析日期
                String timeStr = DateUtil.dateToString(workSheetFolder.getAnalyzeTime(), DateUtil.YEAR);
                signatureService.sig(worksheetFolderId, Collections.singletonList(workSheetFolder.getAnalystId()), type,
                        BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD, timeStr);
                // 当工作单存在上岗证人员时。为上岗证人员签名
                if (StringUtil.isNotNull(workSheetFolder) && StringUtil.isNotEmpty(workSheetFolder.getCertificatorId())
                        && !UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getCertificatorId())) {
                    type = EnumPRO.EnumSigType.上岗证人员.getValue();
                    signatureService.sig(worksheetFolderId, Collections.singletonList(workSheetFolder.getCertificatorId()), type,
                            BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD, timeStr);
                }
                type = EnumPRO.EnumSigType.复核者.getValue();
                timeStr = DateUtil.dateToString(workSheetFolder.getCheckDate(), DateUtil.YEAR);
                signatureService.sig(worksheetFolderId, Collections.singletonList(workSheetFolder.getCheckerId()), type,
                        BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD, timeStr);
                type = EnumPRO.EnumSigType.审核者.getValue();
                timeStr = DateUtil.dateToString(workSheetFolder.getAuditDate(), DateUtil.YEAR);
                signatureService.sig(worksheetFolderId, Collections.singletonList(workSheetFolder.getAuditorId()), type,
                        BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD, timeStr);
            } catch (Exception ex) {
                log.info("更新签名发生异常:" + ex.getMessage());
                throw new BaseException(ex.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public void clearRecord(String workSheetFolderId) {
        List<DtoDocument> documentExtendTypeYyjldbg = documentRepository.findByFolderIdAndDocTypeIdOrderByCreateDateDesc(workSheetFolderId, "BASE_DocumentExtendType_YYJLDBG");
        if (StringUtil.isNotEmpty(documentExtendTypeYyjldbg)){
            documentRepository.logicDeleteById(documentExtendTypeYyjldbg.stream().map(DtoDocument::getId).collect(Collectors.toList()), new Date());
        }
    }

    /**
     * 获取样品id和项目id的映射
     *
     * @param allSampleList 样品列表
     * @return 样品id和项目id的映射
     */
    private Map<String, String> getSampleId2ProjectIdMap(List<DtoSample> allSampleList) {
        sampleService.addAssSample(allSampleList);
        Map<String, String> map = new HashMap<>();
        List<DtoSample> qcSampleList = new ArrayList<>();
        for (DtoSample sample : allSampleList) {
            if (StringUtil.isNotEmpty(sample.getProjectId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getProjectId())) {
                map.put(sample.getId(), sample.getProjectId());
            } else {
                qcSampleList.add(sample);
            }
        }
        Map<String, DtoSample> sampleMap = allSampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        for (DtoSample qcSample : qcSampleList) {
            List<String> assIdList = new ArrayList<>();
            assIdList.add(qcSample.getAssociateSampleId());
            String assId = qcSample.getAssociateSampleId();
            while (!map.containsKey(assId)) {
                DtoSample temp = sampleMap.getOrDefault(assId, null);
                if (StringUtil.isNull(temp)) {
                    break;
                }
                assId = temp.getAssociateSampleId();
                if (assIdList.contains(assId)) {
                    break;
                }
                assIdList.add(assId);
            }
            if (map.containsKey(assId)) {
                map.put(qcSample.getId(), map.get(assId));
            }
        }
        return map;
    }

    /**
     * 获取项目id和要求完成日期的映射
     *
     * @param projectIdList 项目id列表
     * @return 项目id和要求完成日期的映射
     */
    private Map<String, String> getProjectId2DeadLineMap(List<String> projectIdList) {
        Map<String, String> map = new HashMap<>();
        List<DtoProjectPlan> planList = StringUtil.isNotEmpty(projectIdList) ? projectPlanRepository.findByProjectIdIn(projectIdList) : new ArrayList<>();
        Map<String, List<DtoProjectPlan>> planMap = planList.stream().collect(Collectors.groupingBy(DtoProjectPlan::getProjectId));
        for (String projectId : projectIdList) {
            if (planMap.containsKey(projectId)) {
                Date deadLine = planMap.get(projectId).get(0).getDeadLine();
                if (StringUtil.isNotNull(deadLine)) {
                    String deadLineStr = DateUtil.dateToString(deadLine, DateUtil.FULL);
                    if (StringUtil.isNotEmpty(deadLineStr) && !deadLineStr.contains("1753")) {
                        map.put(projectId, deadLineStr);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 按分析项目排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    protected static Integer comparingByItemSort(Map<String, Object> map) {
        return (Integer) map.get("sortNumber");
    }

    /**
     * 按样品类型
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    protected static Integer comparingBySampleCategory(Map<String, Object> map) {
        return (Integer) map.get("sampleCategory");
    }

    /**
     * 当可以同时完成的时候要判断公式是否一致，不一致需要创建到一个选项卡中
     *
     * @param analyseDataTemps           数据
     * @param dtoWorkSheetFolder         工作单
     * @param workSheets                 小类工作单信息
     * @param anaMapList                 已处理的数据
     * @param paramsDataList             参数数据
     * @param recordConfigParams         配置信息
     * @param testFormulaParamsConfigMap 公式相关的参数
     * @param isAll                      是否获取所有的
     * @return 返回工作单组合信息
     */
    private List<DtoWorkSheetProperty> openWorkSheetByFormula(List<DtoAnalyseDataTemp> analyseDataTemps,
                                                              DtoWorkSheetFolder dtoWorkSheetFolder, List<DtoWorkSheet> workSheets,
                                                              List<Map<String, Object>> anaMapList,
                                                              List<DtoParamsData> paramsDataList, List<DtoRecordConfigParams> recordConfigParams,
                                                              List<DtoRecordConfigTest> recordConfigList,
                                                              Map<String, List<DtoTestFormulaParamsConfig>> testFormulaParamsConfigMap,
                                                              Boolean isAll, List<String> sampleIds,
                                                              List<DtoSample> samList) {
        List<DtoWorkSheetProperty> workSheetProperties = new ArrayList<>();
        //已经按状态过滤了数据
        List<String> fList;
        if (!isAll) {
            fList = analyseDataTemps.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                    && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))
                    .map(DtoAnalyseDataTemp::getFormula).distinct().collect(Collectors.toList());
        } else {
            fList = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getFormula).distinct().collect(Collectors.toList());
        }
        List<String> testIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findByIdIn(testIds) : new ArrayList<>();
        //获取测试测试项目公式参数部分公式
        List<String> formulaIds = analyseDataTemps.parallelStream().map(DtoAnalyseDataTemp::getFormulaId).distinct().collect(Collectors.toList());
        List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
        //拆分相应的选项卡
        for (String formula : fList) {
            DtoWorkSheetProperty workSheetProperty = new DtoWorkSheetProperty();
            List<DtoAnalyseDataTemp> formulaDataAllList = analyseDataTemps.stream().filter(p -> formula.equals(p.getFormula())).collect(Collectors.toList());
            //所有的数据
            List<DtoAnalyseDataTemp> formulaDataList = formulaDataAllList;
            if (!isAll) {
                formulaDataList = formulaDataAllList.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                        && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))
                        .distinct().collect(Collectors.toList());
            }
            //是否能更换公式,是否能更换曲线，判断当前选项卡数据个数是否完整
            Boolean isAlter = formulaDataList.size() == formulaDataAllList.size();
            DtoWorkSheetTest dtoWorkSheetTest = new DtoWorkSheetTest();
            dtoWorkSheetTest.setRedAnalyzeItemName("数据录入");
            dtoWorkSheetTest.setRedAnalyzeItemId(UUIDHelper.GUID_EMPTY);
            String workSheetId = UUIDHelper.GUID_EMPTY;//小的检测单id
            String recordId = UUIDHelper.GUID_EMPTY;//记录单id
            String testId = UUIDHelper.GUID_EMPTY;//测试项目id
            String sampleTypeId = UUIDHelper.GUID_EMPTY;//样品类型id
            String bigSampleTypeId = UUIDHelper.GUID_EMPTY;//大类的检测类型id
            String formulaId = UUIDHelper.GUID_EMPTY;//公式id
            String redAnalyzeMethodName = "";//分析方法名称
            String redCountryStandard = "";
            List<String> workSheetTestIds = new ArrayList<>();
            List<DtoRecordConfigTest> recordConfigTests = new ArrayList<>();
            if (formulaDataList.size() > 0) {
                DtoAnalyseDataTemp dtoAnalyseDataTemp = formulaDataList.get(0);
                workSheetTestIds = formulaDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
                //表头参数对应的检测单id
                workSheetId = paramsDataList.stream().map(DtoParamsData::getObjectId).distinct().findFirst().orElse(UUIDHelper.GUID_EMPTY);
                //workSheetId = dtoAnalyseDataTemp.getWorkSheetId();
                testId = dtoAnalyseDataTemp.getTestId();
                sampleTypeId = dtoAnalyseDataTemp.getSampleTypeId();
                bigSampleTypeId = dtoAnalyseDataTemp.getBigSampleTypeId();
                redAnalyzeMethodName = dtoAnalyseDataTemp.getRedAnalyzeMethodName();
                redCountryStandard = dtoAnalyseDataTemp.getRedCountryStandard();
                //如果只有一个测试项目的，显示当前数据的测试项目名称
                if (formulaDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList()).size() == 1) {
                    dtoWorkSheetTest.setRedAnalyzeItemName(dtoAnalyseDataTemp.getRedAnalyzeItemName());
                    dtoWorkSheetTest.setRedAnalyzeItemId(dtoWorkSheetTest.getRedAnalyzeItemId());
                }
                formulaId = dtoAnalyseDataTemp.getFormulaId();
                dtoAnalyseDataTemp.setBigSampleTypeId(bigSampleTypeId);
                DtoWorkSheet workSheet = workSheets.stream().filter(p -> p.getId().equals(dtoAnalyseDataTemp.getWorkSheetId()))
                        .findFirst().orElse(null);
                if (StringUtil.isNotNull(workSheet)) {
                    recordId = workSheet.getRecordId();
                }
                //测试项目相关的记录单数据
                recordConfigTests = recordConfigList.stream().filter(p -> p.getTestId().equals(dtoAnalyseDataTemp.getTestId())).collect(Collectors.toList());
            }
            dtoWorkSheetTest.setFormulaId(formulaId);
            if (StringUtil.isNotEmpty(formulaId) && !UUIDHelper.GUID_EMPTY.equals(formulaId)) {
                List<DtoParamsPartFormula> dtoParamsPartFormulas = paramsPartFormulasAll.parallelStream()
                        .filter(p -> dtoWorkSheetTest.getFormulaId().equals(p.getFormulaId()) && StringUtil.isNotEmpty(p.getFormula()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(dtoParamsPartFormulas)) {
                    List<String> paramsPartFormulas = dtoParamsPartFormulas.parallelStream().map(DtoParamsPartFormula::getFullFormula).collect(Collectors.toList());
                    dtoWorkSheetTest.setParamsFormulaList(paramsPartFormulas);
                }
            }
            dtoWorkSheetTest.setFormula(formula);
            dtoWorkSheetTest.setId(testId);
            dtoWorkSheetTest.setSampleTypeId(sampleTypeId);
            dtoWorkSheetTest.setBigSampleTypeId(bigSampleTypeId);
            dtoWorkSheetTest.setWorkSheetTestIds(workSheetTestIds);
            final List<String> finalTestIds = workSheetTestIds;
            tests.stream().filter(p -> finalTestIds.contains(p.getId()) && StringUtil.isNotEmpty(p.getTips())).findFirst().ifPresent(p -> dtoWorkSheetTest.setTips(p.getTips()));

            List<DtoParamsData> paramsData = getWorkSheetParamsByFormula(workSheetId, recordId, dtoWorkSheetFolder, testId, paramsDataList, recordConfigParams);
            List<Map<String, Object>> mapList;
            List<Map<String, Object>> surplusMapList = new ArrayList<>();
            if (!isAll) {
                mapList = anaMapList.stream().filter(p -> formula.equals(p.get("formula")) &&
                        !p.get("dataStatus").equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue()) &&
                        !p.get("dataStatus").equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).collect(Collectors.toList());
            } else {
                mapList = anaMapList.stream().filter(p -> formula.equals(p.get("formula"))).collect(Collectors.toList());
            }
            if (sampleIds.size() > 0) {
                //按照样品显示数据（根据样品编号？）
                surplusMapList = mapList.stream().filter(p -> !sampleIds.contains(p.get("sampleId"))).collect(Collectors.toList());
                mapList = mapList.stream().filter(p -> sampleIds.contains(p.get("sampleId"))).collect(Collectors.toList());
            }
            //对数据进行排序
            this.sortAnalyseDataOfWorkSheetFolder(mapList);
            List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs = new ArrayList<>();
            if (testFormulaParamsConfigMap.containsKey(formulaId)) {
                testFormulaParamsConfigs = testFormulaParamsConfigMap.get(formulaId).stream().sorted(Comparator.comparing(DtoTestFormulaParamsConfig::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
            }
            //参数排序
            if (StringUtil.isNotEmpty(paramsData)) {
                paramsData = paramsData.stream().sorted(Comparator.comparing(DtoParamsData::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
            }
            if (testFormulaParamsConfigs.size() > 0) {
                testFormulaParamsConfigs = testFormulaParamsConfigs.stream().sorted(Comparator.comparing(DtoTestFormulaParamsConfig::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
            }
            samList.forEach(sample -> {
                if (analyseDataTemps.stream().filter(a -> sample.getCode().equals(a.getGatherCode())).allMatch(a -> StringUtils.isNotNullAndEmpty(a.getTestValue()))) {
                    sample.setDataFlag(true);
                } else {
                    sample.setDataFlag(false);
                }
            });
            //设置隐藏信息 -- 嘉兴存在点位名称被修改的情况，临时先去掉
            //setFolderMsg(mapList, samList);
            //测试项目相关的配置数据
            workSheetProperty.setRecordConfigs(recordConfigTests);
            workSheetProperty.setRecordId(recordId);
            workSheetProperty.setAnalyseData(mapList);
            workSheetProperty.setSurplusAnalyseData(surplusMapList);
            workSheetProperty.setParamsConfig(testFormulaParamsConfigs);
            workSheetProperty.setWorksheetParamsData(paramsData);
            workSheetProperty.setTest(dtoWorkSheetTest);
            workSheetProperty.setIsAlterFormula(isAlter);
            workSheetProperty.setIsAlterCurve(isAlter);
            workSheetProperty.setWorkSheetFolderId(dtoWorkSheetFolder.getId());
            workSheetProperty.setRedAnalyzeMethodName(redAnalyzeMethodName);
            workSheetProperty.setRedCountryStandard(redCountryStandard);
            workSheetProperty.setAnalyzeTime(dtoWorkSheetFolder.getAnalyzeTime());
            workSheetProperty.setAnalystId(dtoWorkSheetFolder.getAnalystId());
            workSheetProperty.setAnalystName(dtoWorkSheetFolder.getAnalystName());
            workSheetProperty.setAuditorId(dtoWorkSheetFolder.getAuditorId());
            workSheetProperty.setAuditorName(dtoWorkSheetFolder.getAuditorName());
            workSheetProperty.setSamples(samList);
            workSheetProperties.add(workSheetProperty);
        }
        return workSheetProperties;
    }

    /**
     * 获取检测单样品信息
     *
     * @param analyseDataTemps 数据
     * @param samList          样品列表
     * @param isAll            是否获取所有的
     * @return 返回工作单组合信息
     */
    private List<DtoWorkSheetProperty> getWorkSheetSampleByFormula(List<DtoAnalyseDataTemp> analyseDataTemps, List<DtoSample> samList, Boolean isAll) {
        List<DtoWorkSheetProperty> workSheetProperties = new ArrayList<>();
        //已经按状态过滤了数据
        List<String> fList;
        if (!isAll) {
            fList = analyseDataTemps.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                    && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))
                    .map(DtoAnalyseDataTemp::getFormula).distinct().collect(Collectors.toList());
        } else {
            fList = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getFormula).distinct().collect(Collectors.toList());
        }
        //获取测试测试项目公式参数部分公式
        List<String> formulaIds = analyseDataTemps.parallelStream().map(DtoAnalyseDataTemp::getFormulaId).distinct().collect(Collectors.toList());
        List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
        //拆分相应的选项卡
        for (String formula : fList) {
            DtoWorkSheetProperty workSheetProperty = new DtoWorkSheetProperty();
            List<DtoAnalyseDataTemp> formulaDataAllList = analyseDataTemps.stream().filter(p -> formula.equals(p.getFormula())).collect(Collectors.toList());
            //所有的数据
            List<DtoAnalyseDataTemp> formulaDataList = formulaDataAllList;
            if (!isAll) {
                formulaDataList = formulaDataAllList.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                        && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))
                        .distinct().collect(Collectors.toList());
            }
            DtoWorkSheetTest dtoWorkSheetTest = new DtoWorkSheetTest();
//            dtoWorkSheetTest.setRedAnalyzeItemName("数据录入");
            dtoWorkSheetTest.setRedAnalyzeItemId(UUIDHelper.GUID_EMPTY);
//            String testId = UUIDHelper.GUID_EMPTY;//测试项目id
//            String sampleTypeId = UUIDHelper.GUID_EMPTY;//样品类型id
            String bigSampleTypeId = UUIDHelper.GUID_EMPTY;//大类的检测类型id
            String formulaId = UUIDHelper.GUID_EMPTY;//公式id
//            List<String> workSheetTestIds = new ArrayList<>();
            if (formulaDataList.size() > 0) {
                DtoAnalyseDataTemp dtoAnalyseDataTemp = formulaDataList.get(0);
//                workSheetTestIds = formulaDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
//                testId = dtoAnalyseDataTemp.getTestId();
//                sampleTypeId = dtoAnalyseDataTemp.getSampleTypeId();
//                bigSampleTypeId = dtoAnalyseDataTemp.getBigSampleTypeId();
                //如果只有一个测试项目的，显示当前数据的测试项目名称
                if (formulaDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList()).size() == 1) {
//                    dtoWorkSheetTest.setRedAnalyzeItemName(dtoAnalyseDataTemp.getRedAnalyzeItemName());
                    dtoWorkSheetTest.setRedAnalyzeItemId(dtoWorkSheetTest.getRedAnalyzeItemId());
                }
                formulaId = dtoAnalyseDataTemp.getFormulaId();
                dtoAnalyseDataTemp.setBigSampleTypeId(bigSampleTypeId);
            }
            dtoWorkSheetTest.setFormulaId(formulaId);
            if (StringUtil.isNotEmpty(formulaId) && !UUIDHelper.GUID_EMPTY.equals(formulaId)) {
                List<DtoParamsPartFormula> dtoParamsPartFormulas = paramsPartFormulasAll.parallelStream()
                        .filter(p -> dtoWorkSheetTest.getFormulaId().equals(p.getFormulaId()) && StringUtil.isNotEmpty(p.getFormula()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(dtoParamsPartFormulas)) {
                    List<String> paramsPartFormulas = dtoParamsPartFormulas.parallelStream().map(DtoParamsPartFormula::getFullFormula).collect(Collectors.toList());
                    dtoWorkSheetTest.setParamsFormulaList(paramsPartFormulas);
                }
            }
            dtoWorkSheetTest.setFormula(formula);
//            dtoWorkSheetTest.setId(testId);
//            dtoWorkSheetTest.setSampleTypeId(sampleTypeId);
//            dtoWorkSheetTest.setBigSampleTypeId(bigSampleTypeId);
//            dtoWorkSheetTest.setWorkSheetTestIds(workSheetTestIds);
            workSheetProperty.setTest(dtoWorkSheetTest);
            workSheetProperty.setSamples(samList);
            workSheetProperties.add(workSheetProperty);
        }
        return workSheetProperties;
    }

    /**
     * 设置隐藏点位以及委托方信息
     *
     * @param mapList 检测数据
     * @param samList 样品数据
     */
    private void setFolderMsg(List<Map<String, Object>> mapList, List<DtoSample> samList) {
        DtoCode isShowFolder = codeService.findByCode("PRO_IS_SHOW_FOLDER");
        if (StringUtil.isNotNull(isShowFolder)) {
            //对测试项目的点位以及委托方进行隐藏
            mapList.forEach(p -> {
                if ("1".equals(isShowFolder.getDictValue())) {
                    if ((p.get("qcType").equals(EnumLIM.EnumQCType.空白.getValue()) && p.get("qcGrade").equals(EnumLIM.EnumQCGrade.外部质控.getValue()))
                            || (p.get("qcType").equals(EnumLIM.EnumQCType.运输空白.getValue()) && p.get("qcGrade").equals(EnumLIM.EnumQCGrade.外部质控.getValue()))) {
                        p.put("inspectedEnt", "/");
                        p.put("inspectedEntId", "/");
                    } else {
                        p.put("inspectedEnt", "/");
                        p.put("redFolderName", "/");
                        p.put("inspectedEntId", "/");
                    }
                }
            });
            //对样品总的点位以及委托方进行隐藏
            if (StringUtil.isNotEmpty(samList)) {
                if ("1".equals(isShowFolder.getDictValue())) {
                    samList.forEach(p -> {
                        p.setRedFolderName("/");
                        p.setInspectedEnt("/");
                        p.setInspectedEntId("/");
                    });
                }
            }
        }
    }

    /**
     * 当可以同时完成的时候要判断公式是否一致，不一致需要创建到一个选项卡中
     *
     * @param analyseDataTemps           数据
     * @param dtoWorkSheetFolder         工作单
     * @param workSheets                 小类工作单信息
     * @param anaMapList                 已处理的数据
     * @param paramsDataList             参数数据
     * @param recordConfigParams         配置信息
     * @param testFormulaParamsConfigMap 公式相关的参数
     * @param isAll                      是否获取所有的
     * @return 返回工作单组合信息
     */
    private List<DtoWorkSheetProperty> openWorkSheetByWorkSheetId(List<DtoAnalyseDataTemp> analyseDataTemps,
                                                                  DtoWorkSheetFolder dtoWorkSheetFolder,
                                                                  List<DtoWorkSheet> workSheets,
                                                                  List<Map<String, Object>> anaMapList,
                                                                  List<DtoParamsData> paramsDataList,
                                                                  List<DtoRecordConfigParams> recordConfigParams,
                                                                  List<DtoRecordConfigTest> recordConfigList,
                                                                  Map<String, List<DtoTestFormulaParamsConfig>> testFormulaParamsConfigMap,
                                                                  Boolean isAll, List<String> sampleIds,
                                                                  List<DtoSample> samList) {
        List<DtoWorkSheetProperty> workSheetProperties = new ArrayList<>();
        List<String> workSheetAllIds;
        if (!isAll) {
            workSheetAllIds = analyseDataTemps.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                    && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).map(DtoAnalyseDataTemp::getWorkSheetId)
                    .distinct().collect(Collectors.toList());
        } else {
            workSheetAllIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getWorkSheetId)
                    .distinct().collect(Collectors.toList());
        }
        List<String> testIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findByIdIn(testIds) : new ArrayList<>();
        //获取测试测试项目公式参数部分公式
        List<String> formulaIds = analyseDataTemps.parallelStream().map(DtoAnalyseDataTemp::getFormulaId).distinct().collect(Collectors.toList());
        List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
        for (String workSheetId : workSheetAllIds) {
            DtoWorkSheetProperty workSheetProperty = new DtoWorkSheetProperty();
            //记录单id
            String recordId = UUIDHelper.GUID_EMPTY;
            DtoWorkSheet workSheet = workSheets.stream().filter(p -> p.getId().equals(workSheetId))
                    .findFirst().orElse(null);
            if (StringUtil.isNotNull(workSheet)) {
                recordId = workSheet.getRecordId();
            }
            List<DtoAnalyseDataTemp> workSheetDataAllList = analyseDataTemps.stream()
                    .filter(p -> workSheetId.equals(p.getWorkSheetId())).collect(Collectors.toList());
            //所有的数据
            List<DtoAnalyseDataTemp> workSheetDataList = workSheetDataAllList;
            if (!isAll) {
                workSheetDataList = workSheetDataAllList.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                        && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))
                        .distinct().collect(Collectors.toList());
            }
            //是否能更换公式,是否能更换曲线，判断当前选项卡数据个数是否完整
            Boolean isAlter = workSheetDataList.size() == workSheetDataAllList.size();
            if (workSheetDataList.size() > 0) {
                //过滤掉替代样，避免前端选项卡的分析项目名称变为替代物的分析项目
                List<DtoAnalyseDataTemp> workSheetDataNotReplaceList = workSheetDataList.stream().
                        filter(p -> !new QualityReplace().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                DtoAnalyseDataTemp dtoAnalyseDataTemp = workSheetDataList.get(0);
                List<String> workSheetTestIds = workSheetDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
                String testId = dtoAnalyseDataTemp.getTestId();
                String formula = dtoAnalyseDataTemp.getFormula();
                String redAnalyzeMethodName = dtoAnalyseDataTemp.getRedAnalyzeMethodName();
                String redCountryStandard = dtoAnalyseDataTemp.getRedCountryStandard();

                DtoWorkSheetTest dtoWorkSheetTest = new DtoWorkSheetTest();
                List<DtoParamsPartFormula> dtoParamsPartFormulas = paramsPartFormulasAll.parallelStream()
                        .filter(p -> dtoAnalyseDataTemp.getFormulaId().equals(p.getFormulaId()) && StringUtil.isNotEmpty(p.getFormula()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(dtoParamsPartFormulas)) {
                    List<String> paramsPartFormulas = dtoParamsPartFormulas.parallelStream().map(DtoParamsPartFormula::getFullFormula).collect(Collectors.toList());
                    dtoWorkSheetTest.setParamsFormulaList(paramsPartFormulas);
                }
                dtoWorkSheetTest.setFormulaId(dtoAnalyseDataTemp.getFormulaId());
                dtoWorkSheetTest.setFormula(formula);
                dtoWorkSheetTest.setId(testId);
                dtoWorkSheetTest.setSampleTypeId(dtoAnalyseDataTemp.getSampleTypeId());
                dtoWorkSheetTest.setWorkSheetTestIds(workSheetTestIds);
                dtoWorkSheetTest.setBigSampleTypeId(dtoAnalyseDataTemp.getBigSampleTypeId());
                dtoWorkSheetTest.setRedAnalyzeItemName(StringUtil.isNotEmpty(workSheetDataNotReplaceList) ?
                        workSheetDataNotReplaceList.get(0).getRedAnalyzeItemName() : dtoAnalyseDataTemp.getRedAnalyzeItemName());
                dtoWorkSheetTest.setRedAnalyzeItemId(dtoAnalyseDataTemp.getAnalyseItemId());
                tests.stream().filter(p -> workSheetTestIds.contains(p.getId()) && StringUtil.isNotEmpty(p.getTips())).findFirst().ifPresent(p -> dtoWorkSheetTest.setTips(p.getTips()));
                List<DtoParamsData> paramsData = getWorkSheetParams(workSheetId, recordId, dtoWorkSheetFolder,
                        testId, paramsDataList, recordConfigParams);
                List<Map<String, Object>> mapList;
                List<Map<String, Object>> surplusMapList = new ArrayList<>();
                if (!isAll) {
                    mapList = anaMapList.stream().filter(p -> workSheetId.equals(p.get("workSheetId")) &&
                            !p.get("dataStatus").equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue()) &&
                            !p.get("dataStatus").equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).collect(Collectors.toList());
                } else {
                    //不按样品显示数据
                    mapList = anaMapList.stream().filter(p -> workSheetId.equals(p.get("workSheetId"))).collect(Collectors.toList());
                }
                if (sampleIds.size() > 0) {
                    //按照样品显示数据
                    surplusMapList = mapList.stream().filter(p -> !sampleIds.contains(p.get("sampleId"))).collect(Collectors.toList());
                    mapList = mapList.stream().filter(p -> sampleIds.contains(p.get("sampleId"))).collect(Collectors.toList());
                }
                //排序
                this.sortAnalyseDataOfWorkSheetFolder(mapList);
                List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs = new ArrayList<>();
                if (testFormulaParamsConfigMap.containsKey(dtoAnalyseDataTemp.getFormulaId())) {
                    testFormulaParamsConfigs = testFormulaParamsConfigMap.get(dtoAnalyseDataTemp.getFormulaId()).stream().sorted(Comparator.comparing(DtoTestFormulaParamsConfig::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
                }
                //参数排序
                if (paramsData.size() > 0) {
                    paramsData.sort(Comparator.comparing(DtoParamsData::getOrderNum, Comparator.reverseOrder()));
                }
                if (testFormulaParamsConfigs.size() > 0) {
                    testFormulaParamsConfigs = testFormulaParamsConfigs.stream().sorted(Comparator.comparing(DtoTestFormulaParamsConfig::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
                }
                //设置隐藏信息 -- 嘉兴存在点位名称被修改的情况，临时先去掉
                //setFolderMsg(mapList,samList);
                //测试项目相关的配置数据
                workSheetProperty.setRecordConfigs(recordConfigList.stream().filter(p -> p.getTestId().equals(testId)).collect(Collectors.toList()));
                workSheetProperty.setRecordId(recordId);
                workSheetProperty.setAnalyseData(mapList);
                workSheetProperty.setSurplusAnalyseData(surplusMapList);
                workSheetProperty.setParamsConfig(testFormulaParamsConfigs);
                workSheetProperty.setWorksheetParamsData(paramsData);
                workSheetProperty.setTest(dtoWorkSheetTest);
                workSheetProperty.setIsAlterFormula(isAlter);
                workSheetProperty.setIsAlterCurve(isAlter);
                workSheetProperty.setWorkSheetFolderId(dtoWorkSheetFolder.getId());
                workSheetProperty.setRedAnalyzeMethodName(redAnalyzeMethodName);
                workSheetProperty.setRedCountryStandard(redCountryStandard);
                workSheetProperty.setAnalyzeTime(dtoWorkSheetFolder.getAnalyzeTime());
                workSheetProperty.setAnalystId(dtoWorkSheetFolder.getAnalystId());
                workSheetProperty.setAnalystName(dtoWorkSheetFolder.getAnalystName());
                workSheetProperty.setAuditorId(dtoWorkSheetFolder.getAuditorId());
                workSheetProperty.setAuditorName(dtoWorkSheetFolder.getAuditorName());
                workSheetProperty.setSamples(samList);
                workSheetProperties.add(workSheetProperty);
            }
        }
        return workSheetProperties;
    }

    /**
     * 获取检测单样品数据
     *
     * @param analyseDataTemps 数据
     * @param isAll            是否获取所有的
     * @return 返回工作单组合信息
     */
    private List<DtoWorkSheetProperty> getWorkSheetSampleByWorkSheetId(List<DtoAnalyseDataTemp> analyseDataTemps, List<DtoSample> samList, Boolean isAll) {
        List<DtoWorkSheetProperty> workSheetProperties = new ArrayList<>();
        List<String> workSheetAllIds;
        if (!isAll) {
            workSheetAllIds = analyseDataTemps.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                    && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).map(DtoAnalyseDataTemp::getWorkSheetId)
                    .distinct().collect(Collectors.toList());
        } else {
            workSheetAllIds = analyseDataTemps.stream().map(DtoAnalyseDataTemp::getWorkSheetId)
                    .distinct().collect(Collectors.toList());
        }
        //获取测试测试项目公式参数部分公式
        List<String> formulaIds = analyseDataTemps.parallelStream().map(DtoAnalyseDataTemp::getFormulaId).distinct().collect(Collectors.toList());
        List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);
        for (String workSheetId : workSheetAllIds) {
            DtoWorkSheetProperty workSheetProperty = new DtoWorkSheetProperty();
            List<DtoAnalyseDataTemp> workSheetDataAllList = analyseDataTemps.stream().filter(p -> workSheetId.equals(p.getWorkSheetId())).collect(Collectors.toList());
            //所有的数据
            List<DtoAnalyseDataTemp> workSheetDataList = workSheetDataAllList;
            if (!isAll) {
                workSheetDataList = workSheetDataAllList.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                        && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))
                        .distinct().collect(Collectors.toList());
            }
            if (workSheetDataList.size() > 0) {
                DtoAnalyseDataTemp dtoAnalyseDataTemp = workSheetDataList.get(0);
                String formula = dtoAnalyseDataTemp.getFormula();
                DtoWorkSheetTest dtoWorkSheetTest = new DtoWorkSheetTest();
                List<DtoParamsPartFormula> dtoParamsPartFormulas = paramsPartFormulasAll.parallelStream()
                        .filter(p -> dtoAnalyseDataTemp.getFormulaId().equals(p.getFormulaId()) && StringUtil.isNotEmpty(p.getFormula()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(dtoParamsPartFormulas)) {
                    List<String> paramsPartFormulas = dtoParamsPartFormulas.parallelStream().map(DtoParamsPartFormula::getFullFormula).collect(Collectors.toList());
                    dtoWorkSheetTest.setParamsFormulaList(paramsPartFormulas);
                }
                dtoWorkSheetTest.setFormulaId(dtoAnalyseDataTemp.getFormulaId());
                dtoWorkSheetTest.setFormula(formula);
                dtoWorkSheetTest.setRedAnalyzeItemId(dtoAnalyseDataTemp.getAnalyseItemId());
                //测试项目相关的配置数据
                workSheetProperty.setTest(dtoWorkSheetTest);
                workSheetProperty.setSamples(samList);
                workSheetProperties.add(workSheetProperty);
            }
        }
        return workSheetProperties;
    }

    /**
     * 获取原始记录单参数
     *
     * @param workSheetId               小类的id
     * @param recordId                  原始记录单配置id
     * @param workSheetFolder           工作单信息
     * @param testId                    测试项目id
     * @param paramsDataAllList         参数数据
     * @param recordConfigParamsAllList 配置信息
     * @return 返回参数数据
     */
    private List<DtoParamsData> getWorkSheetParams(String workSheetId,
                                                   String recordId,
                                                   DtoWorkSheetFolder workSheetFolder, String testId,
                                                   List<DtoParamsData> paramsDataAllList, List<DtoRecordConfigParams> recordConfigParamsAllList) {
        List<DtoParamsData> paramsDataList = paramsDataAllList.stream().filter(p -> p.getObjectId().equals(workSheetId)).collect(Collectors.toList());
        List<DtoRecordConfigParams> recordConfigParamsList = recordConfigParamsAllList.stream().filter(p -> p.getTestId()
                .equals(testId) && recordId.equals(p.getRecordId())).collect(Collectors.toList());
        //只有新建的检测单才获取上一张测试项目的数据
        if (EnumPRO.EnumWorkSheetStatus.新建.getValue().equals(workSheetFolder.getWorkStatus())) {
            //查询最后一张工作单
            PageBean<DtoWorkSheet> pb = new PageBean<>();
            pb.setRowsPerPage(1);
            pb.setEntityName("DtoWorkSheetFolder a, DtoWorkSheet b");
            pb.setSelect("select b");
            String condition = " and a.id = b.parentId" +
                    " and a.workStatus >= :workStatus" +
                    " and b.testId = :testId" +
                    " and a.analystId = :analystId" +
                    " and b.recordId = :recordId";
            Map<String, Object> values = new HashMap<>();
            values.put("workStatus", EnumPRO.EnumWorkSheetStatus.已经提交.getValue());
            values.put("testId", testId);
            values.put("analystId", workSheetFolder.getAnalystId());
            values.put("recordId", recordId);
            pb.setCondition(condition);
            pb.setSort("createTime-");
            comRepository.findByPage(pb, values);
            List<DtoWorkSheet> workSheets = pb.getData();
            if (StringUtil.isNotEmpty(workSheets)) {
                //获取工作单id
                String wId = workSheets.get(0).getId();
                //获取上一张工作单中的参数数据
                List<DtoParamsData> dataList = paramsDataRepository.findByObjectTypeAndObjectId(EnumPRO.EnumParamsDataType.检测单.getValue(), wId);
                for (DtoParamsData dtoParamsData : paramsDataList) {
                    //根据当前的原始记录单筛选上一张工作单中的参数数据
                    Optional<DtoParamsData> dtoParamsDataOptional = dataList.stream().filter(p -> p.getParamsConfigId().equals(dtoParamsData.getParamsConfigId()) && p.getObjectId().equals(wId)).findFirst();
                    //设置参数的值
                    dtoParamsDataOptional.ifPresent(data -> dtoParamsData.setParamsValue(data.getParamsValue()));
                    //获取参数原始记录设置，填充新参数的值
                    Optional<DtoRecordConfigParams> recordConfigParams = recordConfigParamsList.stream().filter(p -> p.getId()
                            .equals(dtoParamsData.getParamsConfigId())).findFirst();
                    recordConfigParams.ifPresent(p -> setParamsData(dtoParamsData, p));
                }
                return paramsDataList;
            }
        }
        List<String> paramsConfigIds = recordConfigParamsList.stream().map(DtoRecordConfigParams::getId).collect(Collectors.toList());
        // 筛选不存在的表头参数，作为已经被删除的参数
        List<String> isDelParamsConfigIds = paramsDataList.stream().map(DtoParamsData::getParamsConfigId).filter(p -> !paramsConfigIds.contains(p)).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigs = StringUtil.isNotEmpty(isDelParamsConfigIds) ? paramsConfigRepository.findAllDeleted(isDelParamsConfigIds) : new ArrayList<>();
        for (DtoParamsData dtoParamsData : paramsDataList) {
            Optional<DtoRecordConfigParams> recordConfigParams = recordConfigParamsList.stream().filter(p -> p.getId()
                    .equals(dtoParamsData.getParamsConfigId())).findFirst();
            if (recordConfigParams.isPresent()) {
                setParamsData(dtoParamsData, recordConfigParams.get());
            } else {
                paramsConfigs.stream().filter(p -> p.getId()
                        .equals(dtoParamsData.getParamsConfigId())).findFirst().ifPresent(config -> {
                    dtoParamsData.setDefaultControl(config.getDefaultControl());
                });
            }
        }
        return paramsDataList;
    }

    /**
     * 获取原始记录单参数（多个测试项目可同时完成的情况）
     *
     * @param workSheetId               小类的id
     * @param recordId                  原始记录单配置id
     * @param workSheetFolder           工作单信息
     * @param testId                    测试项目id
     * @param paramsDataAllList         参数数据
     * @param recordConfigParamsAllList 配置信息
     * @return 返回参数数据
     */
    private List<DtoParamsData> getWorkSheetParamsByFormula(String workSheetId,
                                                            String recordId,
                                                            DtoWorkSheetFolder workSheetFolder, String testId,
                                                            List<DtoParamsData> paramsDataAllList, List<DtoRecordConfigParams> recordConfigParamsAllList) {
        List<DtoParamsData> paramsDataList = paramsDataAllList.stream().filter(p -> p.getObjectId().equals(workSheetId)).collect(Collectors.toList());
        List<DtoRecordConfigParams> recordConfigParamsList = recordConfigParamsAllList.stream().filter(p -> p.getTestId()
                .equals(testId) && recordId.equals(p.getRecordId())).collect(Collectors.toList());
        //只有新建的检测单才获取上一张测试项目的数据
        if (EnumPRO.EnumWorkSheetStatus.新建.getValue().equals(workSheetFolder.getWorkStatus())) {
            //查询最后一张工作单
            PageBean<String> pb = new PageBean<>();
            pb.setRowsPerPage(1);
            pb.setEntityName("DtoWorkSheetFolder a, DtoWorkSheet b");
            pb.setSelect("select a.id");
            String condition = " and a.id = b.parentId" +
                    " and a.workStatus >= :workStatus" +
                    " and b.testId = :testId" +
                    " and a.analystId = :analystId" +
                    " and b.recordId = :recordId";
            Map<String, Object> values = new HashMap<>();
            values.put("workStatus", EnumPRO.EnumWorkSheetStatus.已经提交.getValue());
            values.put("testId", testId);
            values.put("analystId", workSheetFolder.getAnalystId());
            values.put("recordId", recordId);
            pb.setCondition(condition);
            pb.setSort("createTime-");
            comRepository.findByPage(pb, values);
            List<String> workSheetFolderIdList = pb.getData();
            if (StringUtil.isNotEmpty(workSheetFolderIdList)) {
                //获取工作单id
                String workSheetFolderId = workSheetFolderIdList.get(0);
                List<DtoWorkSheet> workSheetList = workSheetRepository.findByParentId(workSheetFolderId);
                List<String> workSheetIdList = workSheetList.stream().map(DtoWorkSheet::getId).collect(Collectors.toList());
                //获取上一张工作单中的参数数据
                List<DtoParamsData> dataList = StringUtil.isNotEmpty(workSheetIdList)
                        ? paramsDataRepository.findByObjectIdInAndObjectType(workSheetIdList, EnumPRO.EnumParamsDataType.检测单.getValue()) : new ArrayList<>();
                String wId = StringUtil.isNotEmpty(dataList) ? dataList.get(0).getObjectId() : "";
                for (DtoParamsData dtoParamsData : paramsDataList) {
                    //根据当前的原始记录单筛选上一张工作单中的参数数据
                    Optional<DtoParamsData> dtoParamsDataOptional = dataList.stream().filter(p -> p.getParamsName().equals(dtoParamsData.getParamsName()) && p.getObjectId().equals(wId)).findFirst();
                    //设置参数的值
                    dtoParamsDataOptional.ifPresent(data -> dtoParamsData.setParamsValue(data.getParamsValue()));
                    //获取参数原始记录设置，填充新参数的值
                    Optional<DtoRecordConfigParams> recordConfigParams = recordConfigParamsList.stream().filter(p -> p.getId()
                            .equals(dtoParamsData.getParamsConfigId())).findFirst();
                    recordConfigParams.ifPresent(p -> {
                        //没有历史数据，取参数默认值
                        if (StringUtil.isEmpty(dtoParamsData.getParamsValue())) {
                            dtoParamsData.setParamsValue(p.getDefaultValue());
                        }
                        setParamsData(dtoParamsData, p);
                    });
                }
                return paramsDataList;
            } else {
                //没有历史数据，取参数默认值
                for (DtoParamsData dtoParamsData : paramsDataList) {
                    Optional<DtoRecordConfigParams> recordConfigParams = recordConfigParamsList.stream().filter(p -> p.getId()
                            .equals(dtoParamsData.getParamsConfigId())).findFirst();
                    recordConfigParams.ifPresent(p -> {
                        dtoParamsData.setParamsValue(p.getDefaultValue());
                        setParamsData(dtoParamsData, p);
                    });
                }
            }
        }
        List<String> paramsConfigIds = recordConfigParamsList.stream().map(DtoRecordConfigParams::getId).collect(Collectors.toList());
        // 筛选不存在的表头参数，作为已经被删除的参数
        List<String> isDelParamsConfigIds = paramsDataList.stream().map(DtoParamsData::getParamsConfigId).filter(p -> !paramsConfigIds.contains(p)).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigs = StringUtil.isNotEmpty(isDelParamsConfigIds) ? paramsConfigRepository.findAllDeleted(isDelParamsConfigIds) : new ArrayList<>();
        for (DtoParamsData dtoParamsData : paramsDataList) {
            Optional<DtoRecordConfigParams> recordConfigParams = recordConfigParamsList.stream().filter(p -> p.getId()
                    .equals(dtoParamsData.getParamsConfigId())).findFirst();
            if (recordConfigParams.isPresent()) {
                setParamsData(dtoParamsData, recordConfigParams.get());
            } else {
                paramsConfigs.stream().filter(p -> p.getId()
                        .equals(dtoParamsData.getParamsConfigId())).findFirst().ifPresent(config -> {
                    dtoParamsData.setDefaultControl(config.getDefaultControl());
                });
            }
        }

        return paramsDataList;
    }

    /**
     * 设置数据上的必填及数据源
     *
     * @param dtoParamsData      参数数据
     * @param recordConfigParams 参数配置
     */
    private void setParamsData(DtoParamsData dtoParamsData, DtoRecordConfigParams recordConfigParams) {
        dtoParamsData.setIsRequired(recordConfigParams.getIsRequired());
        dtoParamsData.setDataSource(recordConfigParams.getDataSource());
        dtoParamsData.setDefaultControl(recordConfigParams.getDefaultControl());
        dtoParamsData.setFormula(recordConfigParams.getFormula());
        dtoParamsData.setIsEnabled(recordConfigParams.getIsEnabled());
        dtoParamsData.setFormulaId(recordConfigParams.getFormulaId());
        dtoParamsData.setAlias(recordConfigParams.getAlias());
        dtoParamsData.setOrderNum(recordConfigParams.getOrderNum());
        dtoParamsData.setParamsName(recordConfigParams.getAlias());
        dtoParamsData.setDimensionId(recordConfigParams.getDimensionId());
        dtoParamsData.setDimension(recordConfigParams.getDimension());
    }

    /**
     * 返回map 数据源
     *
     * @param dtoAnalyseDataTemp 分析数据
     * @param codeMap            分析数据Map
     * @return 返回map 数据源
     */
    @Override
    public Map<String, Object> getAnalyseDataMap(DtoAnalyseDataTemp dtoAnalyseDataTemp, Map<String, String> codeMap) {
        return this.getAnalyseDataMap(null, dtoAnalyseDataTemp, codeMap);
    }

    @Override
    public Map<String, Object> getAnalyseDataMap(List<DtoAnalyseDataTemp> groupAnalyseDataTemps,
                                                 DtoAnalyseDataTemp dtoAnalyseDataTemp, Map<String, String> codeMap) {
        return getAnalyseDataMap(groupAnalyseDataTemps, dtoAnalyseDataTemp, codeMap, Boolean.FALSE);
    }

    /**
     * 此方法为实验室分析工作单中分析项目的排序值计算逻辑，具体的排序规则如下，如有修改记得更新规则说明
     * 室内空白->曲线校核样->原样->加原样/串联样->原样的质控样(平行，加标等)->加原样/串联样的质控样(平行，加标等)->标样
     *
     * @param dtoAnalyseDataTemp 分析数据
     * @param codeMap            分析数据Map
     * @return 返回map 数据源
     */
    protected Map<String, Object> getAnalyseDataMap(List<DtoAnalyseDataTemp> groupAnalyseDataTemps, DtoAnalyseDataTemp dtoAnalyseDataTemp,
                                                    Map<String, String> codeMap, Boolean isSample) {
        Map<String, Object> map = getAnalyseDataMap(dtoAnalyseDataTemp);
        String orderNum = "";
        if (!isSample) {
            orderNum = getSortNumber(groupAnalyseDataTemps, dtoAnalyseDataTemp, codeMap);
        }
        map.put("orderNum", orderNum);
        return map;
    }

    protected Map<String, Object> getAnalyseDataMap(DtoAnalyseDataTemp dtoAnalyseDataTemp) {
        String orderNum = "";
        Map<String, Object> map = new HashMap<>();
        map.put("id", dtoAnalyseDataTemp.getId());
        map.put("analyzeItemId", dtoAnalyseDataTemp.getAnalyseItemId());
        map.put("analyzeMethodId", dtoAnalyseDataTemp.getAnalyzeMethodId());
        map.put("associateSampleId", dtoAnalyseDataTemp.getAssociateSampleId());
        map.put("dataInputTime", dtoAnalyseDataTemp.getDataInputTime());
        map.put("dimension", dtoAnalyseDataTemp.getDimension());
        map.put("dimensionId", dtoAnalyseDataTemp.getDimensionId());
        map.put("examLimitValue", dtoAnalyseDataTemp.getExamLimitValue());
        map.put("grade", dtoAnalyseDataTemp.getGrade());
        map.put("inspectedEnt", dtoAnalyseDataTemp.getInspectedEnt());
        map.put("inspectedEntId", dtoAnalyseDataTemp.getInspectedEntId());
        map.put("isQC", dtoAnalyseDataTemp.getIsQC());
        map.put("mostDecimal", dtoAnalyseDataTemp.getMostDecimal());
        map.put("mostSignificance", dtoAnalyseDataTemp.getMostSignificance());
        map.put("qcCode", dtoAnalyseDataTemp.getQcCode());
        map.put("qcGrade", dtoAnalyseDataTemp.getQcGrade());
        map.put("qcId", dtoAnalyseDataTemp.getQcId());
        map.put("qcRegisterGrade", dtoAnalyseDataTemp.getQcRegisterGrade());
        map.put("qcRegisterJudement", dtoAnalyseDataTemp.getQcRegisterJudement());
        map.put("qcTestValue", dtoAnalyseDataTemp.getQcTestValue());
        map.put("qcType", dtoAnalyseDataTemp.getQcType());
        map.put("qcValue", dtoAnalyseDataTemp.getQcValue());
        map.put("uncertainType", dtoAnalyseDataTemp.getUncertainType());
        map.put("rangeLow", dtoAnalyseDataTemp.getRangeLow());
        map.put("rangeHigh", dtoAnalyseDataTemp.getRangeHigh());
        map.put("qcVolume", dtoAnalyseDataTemp.getQcVolume());
        map.put("qmQcType", dtoAnalyseDataTemp.getQmQcType());
        map.put("qmRange", dtoAnalyseDataTemp.getQmRange());
        map.put("realSampleTestValue", dtoAnalyseDataTemp.getRealSampleTestValue());
        map.put("receiveId", dtoAnalyseDataTemp.getReceiveId());
        map.put("redAnalyzeItemName", dtoAnalyseDataTemp.getRedAnalyzeItemName());
        map.put("redAnalyzeMethodName", dtoAnalyseDataTemp.getRedAnalyzeMethodName());
        map.put("redCountryStandard", dtoAnalyseDataTemp.getRedCountryStandard());
        map.put("redFolderName", getRedFolderName(dtoAnalyseDataTemp));
        map.put("requireDeadLine", dtoAnalyseDataTemp.getRequireDeadLine());
        map.put("sampleCode", dtoAnalyseDataTemp.getSampleCode());
        if (StringUtil.isNotEmpty(dtoAnalyseDataTemp.getSampleCodeWithTag())) {
            map.put("sampleCodeWithTag", dtoAnalyseDataTemp.getSampleCodeWithTag());
        } else {
            map.put("sampleCodeWithTag", dtoAnalyseDataTemp.getSampleCode());
        }
        if (StringUtil.isNotEmpty(dtoAnalyseDataTemp.getGatherCode())) {
            //保存后显示修改后的数据
            map.put("gatherCode", dtoAnalyseDataTemp.getGatherCode());
        } else {
            //初始化的情况
            map.put("gatherCode", dtoAnalyseDataTemp.getSampleCodeWithTag());
        }
        map.put("sampleId", dtoAnalyseDataTemp.getSampleId());
        map.put("sampleRemark", dtoAnalyseDataTemp.getSampleRemark());
        map.put("sampleTypeId", dtoAnalyseDataTemp.getSampleTypeId());
        map.put("sampleTypeName", dtoAnalyseDataTemp.getSampleTypeName());
        map.put("sampleTime", dtoAnalyseDataTemp.getSampleTime());
        map.put("testId", dtoAnalyseDataTemp.getTestId());
        map.put("testOrignValue", dtoAnalyseDataTemp.getTestOrignValue());
        map.put("testValue", dtoAnalyseDataTemp.getTestValue());
        map.put("testValueD", dtoAnalyseDataTemp.getTestValueD());
        map.put("testValueDstr", dtoAnalyseDataTemp.getTestValueDstr());
        map.put("workSheetId", dtoAnalyseDataTemp.getWorkSheetId());
        map.put("dataStatus", dtoAnalyseDataTemp.getDataStatus());
        map.put("formula", dtoAnalyseDataTemp.getFormula());
        map.put("formulaId", dtoAnalyseDataTemp.getFormulaId());
        map.put("sampleCategory", dtoAnalyseDataTemp.getSampleCategory());
        map.put("isQm", dtoAnalyseDataTemp.getIsQm());
        map.put("orderNum", orderNum);
        map.put("isDataEnabled", dtoAnalyseDataTemp.getIsDataEnabled());
        map.put("groupSampleId", dtoAnalyseDataTemp.getGroupSampleId());
        map.put("qcInfo", dtoAnalyseDataTemp.getQcInfo());
        map.put("casCode", dtoAnalyseDataTemp.getQcCode());
        map.put("compoundName", dtoAnalyseDataTemp.getQcVolume());
        map.put("addition", dtoAnalyseDataTemp.getQcValue());
        map.put("seriesValue", dtoAnalyseDataTemp.getSeriesValue());
        map.put("qcValidDate", dtoAnalyseDataTemp.getQcValidDate());
        map.put("qcStandardDate", dtoAnalyseDataTemp.getQcStandardDate());
        map.put("qcStandardId", dtoAnalyseDataTemp.getQcStandardId());
        map.put("qcVolumeDimensionId", dtoAnalyseDataTemp.getQcVolumeDimensionId());
        map.put("qcValueDimensionId", dtoAnalyseDataTemp.getQcValueDimensionId());
        map.put("qcTestValueDimensionId", dtoAnalyseDataTemp.getQcTestValueDimensionId());
        map.put("realSampleTestValueDimensionId", dtoAnalyseDataTemp.getRealSampleTestValueDimensionId());
        map.put("qcConcentrationDimensionId", dtoAnalyseDataTemp.getQcConcentrationDimensionId());
        map.put("isSci", dtoAnalyseDataTemp.getIsSci());
        map.put("pxAverageValue", dtoAnalyseDataTemp.getPxAverageValue());
        map.put("lowerLimit", dtoAnalyseDataTemp.getLowerLimit());
        return map;
    }

    protected String getSortNumber(List<DtoAnalyseDataTemp> groupAnalyseDataTemps, DtoAnalyseDataTemp dtoAnalyseDataTemp,
                                   Map<String, String> codeMap) {
        String orderNum = "30-" + dtoAnalyseDataTemp.getSampleCode() + "-30";
        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
            Integer qcType = dtoAnalyseDataTemp.getQcType();
            if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.串联样.getQcType();
            }
            if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
            }
            DtoQualityConfig qualityConfig = QualityTaskFactory.getInstance().getQcSample(qcType).getQualityConfig();
            if (!(EnumLIM.EnumQCGrade.外部质控.getValue().equals(dtoAnalyseDataTemp.getQcGrade())
                    && (new QualityBlank().qcTypeValue().equals(qcType)
                    || new QualityParallel().qcTypeValue().equals(qcType) || new QualityTransportBlank().qcTypeValue().equals(qcType)))) {
                //是否跟随原样
                if (qualityConfig.getIsFollowSample()) {
                    //原样的上方还是下方(true 是下 false 是 上)
                    Integer qcNumber = qualityConfig.getOrderNumber();
                    orderNum = String.format("%s-%s-%s", "30", codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""), qcNumber);
                    //质控信息是否关联
                    Optional<DtoAnalyseDataTemp> assDataTemp = groupAnalyseDataTemps.stream().filter(p -> p.getSampleId()
                            .equals(dtoAnalyseDataTemp.getAssociateSampleId())).findFirst();
                    if (assDataTemp.isPresent()) {
                        //关联的样品不是原样同时不是全程序空白和现场平行
                        Integer assType = assDataTemp.get().getQcType();
                        if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.串联样.getQcType();
                        }
                        if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
                        }
                        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(assDataTemp.get().getSampleCategory())
                                && !(EnumLIM.EnumQCGrade.外部质控.getValue().equals(assDataTemp.get().getQcGrade())
                                && (new QualityBlank().qcTypeValue().equals(assType)
                                || new QualityParallel().qcTypeValue().equals(assType)
                                || new QualityTransportBlank().qcTypeValue().equals(assType)))) {
                            DtoQualityConfig assQc = QualityTaskFactory.getInstance().getQcSample(assType).getQualityConfig();
                            Integer number = assQc.getOrderNumber();
                            //原样为不关联样品
                            if (!assQc.getExtension()) {
                                if (!EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
                                    orderNum = String.format("%s-%s-%s", number, codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""),
                                            qualityConfig.getOrderNumber());
                                } else {
                                    if (assQc.getIsFollowSample()) {
                                        orderNum = String.format("%s-%s-%s", "30", codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""), number);
                                    } else {
                                        orderNum = String.format("%s-%s-%s", number, codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""),
                                                qualityConfig.getOrderNumber());
                                    }
                                }
                            } else {
                                orderNum = String.format("%s-%s", orderNum, number);
                            }
                        }
                    }
                } else {
                    //原样的上方还是下方(true 是下 false 是 上)
                    orderNum = String.format("%s-%s-%s", qualityConfig.getOrderNumber(), dtoAnalyseDataTemp.getSampleCode(), "30");
                }
            }
        }
        return orderNum;
    }

    protected String getSortNumber(DtoSample sample, List<DtoAnalyseDataTemp> tempList, Map<String, String> codeMap) {
        String orderNum = "30-" + sample.getCode() + "-30";
        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())) {
            Integer qcType = sample.getQcType();
            if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(sample.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.串联样.getQcType();
            }
            if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(sample.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
            }
            DtoQualityConfig qualityConfig = QualityTaskFactory.getInstance().getQcSample(qcType).getQualityConfig();
            if (!(EnumLIM.EnumQCGrade.外部质控.getValue().equals(sample.getQcGrade())
                    && (new QualityBlank().qcTypeValue().equals(qcType)
                    || new QualityParallel().qcTypeValue().equals(qcType) || new QualityTransportBlank().qcTypeValue().equals(qcType)))) {
                //是否跟随原样
                if (qualityConfig.getIsFollowSample()) {
                    //原样的上方还是下方(true 是下 false 是 上)
                    Integer qcNumber = qualityConfig.getOrderNumber();
                    Optional<DtoAnalyseDataTemp> tempOptional = tempList.stream().filter(p -> p.getSampleId().equals(sample.getId())).findFirst();
                    String code = tempOptional.isPresent() ? codeMap.getOrDefault(tempOptional.get().getGroupSampleId(), "") : sample.getCode();
//                    tempOptional.ifPresent(p -> sample.setCode(codeMap.getOrDefault(p.getGroupSampleId(), "")));
                    orderNum = String.format("%s-%s-%s", "30", code, qcNumber);
                    //质控信息是否关联
                    Optional<DtoAnalyseDataTemp> assDataTemp = tempList.stream().filter(p -> p.getSampleId()
                            .equals(sample.getAssociateSampleId())).findFirst();
                    if (assDataTemp.isPresent()) {
                        //关联的样品不是原样同时不是全程序空白和现场平行
                        Integer assType = assDataTemp.get().getQcType();
                        if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.串联样.getQcType();
                        }
                        if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
                        }
                        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(assDataTemp.get().getSampleCategory())
                                && !(EnumLIM.EnumQCGrade.外部质控.getValue().equals(assDataTemp.get().getQcGrade())
                                && (new QualityBlank().qcTypeValue().equals(assType)
                                || new QualityParallel().qcTypeValue().equals(assType)
                                || new QualityTransportBlank().qcTypeValue().equals(assType)))) {
                            DtoQualityConfig assQc = QualityTaskFactory.getInstance().getQcSample(assType).getQualityConfig();
                            Integer number = assQc.getOrderNumber();
                            //原样为不关联样品
                            if (!assQc.getExtension()) {
                                if (!EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(sample.getSampleCategory())) {
                                    orderNum = String.format("%s-%s-%s", number, code, qualityConfig.getOrderNumber());
                                } else {
                                    if (assQc.getIsFollowSample()) {
                                        orderNum = String.format("%s-%s-%s", "30", code, number);
                                    } else {
                                        orderNum = String.format("%s-%s-%s", number, code, qualityConfig.getOrderNumber());
                                    }
                                }
                            } else {
                                orderNum = String.format("%s-%s", orderNum, number);
                            }
                        }
                    }
                } else {
                    //原样的上方还是下方(true 是下 false 是 上)
                    orderNum = String.format("%s-%s-%s", qualityConfig.getOrderNumber(), sample.getCode(), "30");
                }
            }
        }
        return orderNum;
    }

    /**
     * 获取点位名称
     *
     * @param dtoAnalyseDataTemp 分析数据对象
     * @return 点位名称
     */
    protected String getRedFolderName(DtoAnalyseDataTemp dtoAnalyseDataTemp) {
        return dtoAnalyseDataTemp.getRedFolderName();
    }

    @Override
    public void setGroupSampleId(List<DtoAnalyseDataTemp> analyseDataTemps) {
        HashMap<String, String> associateMap = new HashMap<>();
        for (DtoAnalyseDataTemp analyseDataTemp : analyseDataTemps) {
            if (StringUtils.isNotNullAndEmpty(analyseDataTemp.getAssociateSampleId()) &&
                    !UUIDHelper.GUID_EMPTY.equals(analyseDataTemp.getAssociateSampleId()) &&
                    !analyseDataTemp.getQcType().equals((new QualityBlank().qcTypeValue())) &&
                    //外部质控不需要往上穷举关联样，当原样处理
                    !EnumLIM.EnumQCGrade.外部质控.getValue().equals(analyseDataTemp.getQcGrade())
                    || (EnumLIM.EnumQCGrade.外部质控.getValue().equals(analyseDataTemp.getQcGrade())
                    && analyseDataTemp.getQcType().equals((new QualityBlank().qcTypeValue())) && UUIDHelper.GUID_EMPTY.equals(analyseDataTemp.getQcId()))) {
                analyseDataTemp.setGroupSampleId(analyseDataTemp.getAssociateSampleId());
                associateMap.put(analyseDataTemp.getSampleId(), analyseDataTemp.getAssociateSampleId());
            } else {
                analyseDataTemp.setGroupSampleId(analyseDataTemp.getSampleId());
            }
        }
        Boolean flag = true;
        while (flag) {
            flag = false;
            for (DtoAnalyseDataTemp analyseDataTemp : analyseDataTemps) {
                if (associateMap.containsKey(analyseDataTemp.getGroupSampleId())) {
                    analyseDataTemp.setGroupSampleId(associateMap.get(analyseDataTemp.getGroupSampleId()));
                    flag = true;
                }
            }
        }
    }

    /**
     * 设置原样的样品类型(给工作单中分析项目由于给空白样的质控样排序)
     *
     * @param analyseDataTemps 工作单中的分析项目
     */
    private void setAssociateSampleType(List<DtoAnalyseDataTemp> analyseDataTemps) {
        //获取原样
        List<String> associateSampleIds = analyseDataTemps.parallelStream()
                .filter(a -> StringUtil.isNotEmpty(a.getAssociateSampleId())
                        && !UUIDHelper.GUID_EMPTY.equals(a.getAssociateSampleId()))
                .map(DtoAnalyseDataTemp::getAssociateSampleId)
                .distinct()
                .collect(Collectors.toList());
        //先全部设置默认值为-1
        analyseDataTemps.forEach(a -> a.setAssociateSampleType(-1));
        if (StringUtil.isNotEmpty(associateSampleIds)) {
            //获取原样的分析数据
            List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(associateSampleIds);
            //遍历原样id
            for (String associateSampleId : associateSampleIds) {
                //获取具体的原样分析数据
                DtoAnalyseData analyseDataOfAssociateSample = analyseData.parallelStream()
                        .filter(a -> a.getSampleId().equals(associateSampleId) && a.getQcType() != -1)
                        .findFirst()
                        .orElse(null);
                //获取工作单分析数据中的此原样的数据
                List<DtoAnalyseDataTemp> analyseDataTempsOfAnalyseData = analyseDataTemps.parallelStream()
                        .filter(a -> a.getAssociateSampleId().equals(associateSampleId))
                        .collect(Collectors.toList());
                //设置原样类型值
                if (StringUtil.isNotNull(analyseDataOfAssociateSample)) {
                    analyseDataTempsOfAnalyseData.forEach(a -> a.setAssociateSampleType(analyseDataOfAssociateSample.getQcType()));
                }
            }
        }
    }

    /**
     * 打开检测单或者更换公式需要获取相关曲线
     *
     * @param paramsTestFormulas 公式参数
     * @param workSheetIds       检测单ids
     * @return 返回相应的数据
     */
    private List<DtoWorkSheetCalibrationCurve> getWorkSheetCalibrationCurve(List<DtoParamsTestFormula> paramsTestFormulas, List<String> workSheetIds) {
        List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves = new ArrayList<>();
        //只有公式中参数有k和b才查询
        if (paramsTestFormulas.stream().filter(p -> p.getAlias().equals("k") || p.getAlias().equals("b")).collect(Collectors.toList()).size() > 0
                && workSheetIds.size() > 0) {
            workSheetCalibrationCurves = workSheetCalibrationCurveRepository.findByWorksheetIdIn(workSheetIds);
            List<String> standardCurveIds = workSheetCalibrationCurves.stream().map(DtoWorkSheetCalibrationCurve::getStandardCurveId).distinct().collect(Collectors.toList());
            List<DtoCurve> curveList = curveService.findAll(standardCurveIds);
            for (DtoWorkSheetCalibrationCurve dtoWorkSheetCalibrationCurve : workSheetCalibrationCurves) {
                Optional<DtoCurve> optionalDtoCurve = curveList.stream().filter(p -> p.getId().equals(dtoWorkSheetCalibrationCurve.getStandardCurveId())).findFirst();
                if (optionalDtoCurve.isPresent()) {
                    DtoCurve dtoCurve = optionalDtoCurve.get();
                    dtoWorkSheetCalibrationCurve.setC(dtoCurve.getCValue());
                    dtoWorkSheetCalibrationCurve.setK(dtoCurve.getKValue());
                    dtoWorkSheetCalibrationCurve.setB(dtoCurve.getBValue());
                }
            }
        }
        return workSheetCalibrationCurves;
    }


    /**
     * 获取到分析数据
     *
     * @param dtoAnalyseDataTemp   分析数据
     * @param qualityControls      质控数据
     * @param receiveSampleRecords 送样单数据
     * @param qualityManages       质控任务数据
     * @param asoSamId2SamMap      质控任务数据
     * @return 返回相应的数据
     */
    private void getAnalyseDataTemp(DtoAnalyseDataTemp dtoAnalyseDataTemp,
                                    List<DtoQualityControl> qualityControls,
                                    List<DtoReceiveSampleRecordTemp> receiveSampleRecords,
                                    List<DtoQualityManage> qualityManages, Map<String, DtoSample> asoSamId2SamMap) {
        String qcId = dtoAnalyseDataTemp.getQcId();
        String receiveId = dtoAnalyseDataTemp.getReceiveId();
        String anaId = dtoAnalyseDataTemp.getId();
        //是否是质控任务的判断处理
        if (dtoAnalyseDataTemp.getIsQC())//如果是质控样
        {
            Optional<DtoQualityControl> optionalQualityControl = qualityControls.stream().filter(p -> p.getId().equals(qcId)).findFirst();
            if (optionalQualityControl.isPresent()) {
                DtoQualityControl dtoQualityControl = optionalQualityControl.get();
                dtoAnalyseDataTemp.setAssociateSampleId(dtoQualityControl.getAssociateSampleId());
                dtoAnalyseDataTemp.setQcTestValue(dtoQualityControl.getQcTestValue());
                dtoAnalyseDataTemp.setRealSampleTestValue(dtoQualityControl.getRealSampleTestValue());
                dtoAnalyseDataTemp.setQcValue(dtoQualityControl.getQcValue());
                dtoAnalyseDataTemp.setUncertainType(dtoQualityControl.getUncertainType());
                dtoAnalyseDataTemp.setRangeLow(dtoQualityControl.getRangeLow());
                dtoAnalyseDataTemp.setRangeHigh(dtoQualityControl.getRangeHigh());
                dtoAnalyseDataTemp.setQcVolume(dtoQualityControl.getQcVolume());
                dtoAnalyseDataTemp.setQcCode(dtoQualityControl.getQcCode());
                dtoAnalyseDataTemp.setQcValidDate(dtoQualityControl.getQcValidDate());
                dtoAnalyseDataTemp.setQcStandardDate(dtoQualityControl.getQcStandardDate());
                dtoAnalyseDataTemp.setQcStandardId(dtoQualityControl.getQcStandardId());
                dtoAnalyseDataTemp.setQcVolumeDimensionId(dtoQualityControl.getQcVolumeDimensionId());
                dtoAnalyseDataTemp.setQcValueDimensionId(dtoQualityControl.getQcValueDimensionId());
                dtoAnalyseDataTemp.setQcTestValueDimensionId(dtoQualityControl.getQcTestValueDimensionId());
                dtoAnalyseDataTemp.setRealSampleTestValueDimensionId(dtoQualityControl.getRealSampleTestValueDimensionId());
                dtoAnalyseDataTemp.setQcConcentrationDimensionId(dtoQualityControl.getQcConcentrationDimensionId());
                if (dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && dtoQualityControl.getQcType().equals((new QualityStandard().qcTypeValue()))) {
                    dtoAnalyseDataTemp.setSampleRemark("标准编号：" + dtoQualityControl.getQcCode() + " 标准值±不确定值：" + dtoQualityControl.getQcValue());
                } else if (dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && dtoQualityControl.getQcType().equals((new CurveCheck().qcTypeValue()))) {
                    dtoAnalyseDataTemp.setSampleRemark("标准溶液加入体积：" + dtoQualityControl.getQcVolume() + " 标准物加入量：" + dtoQualityControl.getQcValue());
                } else if (dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && dtoQualityControl.getQcType().equals((new QualityReplace().qcTypeValue()))) {
                    //替代样的备注获取对应原样的样品编号
                    DtoSample oldSample = asoSamId2SamMap.get(dtoQualityControl.getAssociateSampleId());
                    dtoAnalyseDataTemp.setSampleRemark(StringUtil.isNotNull(oldSample) ? "原样编号：" + oldSample.getCode() : "");
                }
            }
        }
        //说明是质控任务的数据
        dtoAnalyseDataTemp.setIsQm(false);
        if (StringUtil.isNotNull(dtoAnalyseDataTemp.getIsQm()) && dtoAnalyseDataTemp.getIsQm()) {
            dtoAnalyseDataTemp.setIsQm(true);
            Optional<DtoReceiveSampleRecordTemp> optionalDtoReceiveSampleRecordTemp = receiveSampleRecords.stream().filter(p -> p.getId().equals(receiveId)).findFirst();
            if (optionalDtoReceiveSampleRecordTemp.isPresent()) {
                DtoReceiveSampleRecordTemp temp = optionalDtoReceiveSampleRecordTemp.get();
                dtoAnalyseDataTemp.setQcRegisterGrade(EnumLIM.EnumQCGrade.getName(temp.getQcGrade()));
                dtoAnalyseDataTemp.setQcRegisterJudement(temp.getJudgment());
                if (StringUtils.isNotNullAndEmpty(dtoAnalyseDataTemp.getInspectedEnt())) {
                    dtoAnalyseDataTemp.setInspectedEnt(temp.getCustomerName());
                }
                this.writeQmQcType(temp, qualityManages, dtoAnalyseDataTemp, anaId);
//                if (temp.getQcGrade().equals(EnumPRO.EnumPorjectQCGrade.内部质控.getValue())
//                        || temp.getQcGrade().equals(EnumPRO.EnumPorjectQCGrade.外部质控.getValue())
//                        || temp.getQcGrade().equals(EnumPRO.EnumPorjectQCGrade.分包质控.getValue())) {
//                    Optional<DtoQualityManage> optionalQualityManage = qualityManages.stream().filter(p -> p.getAnaId().equals(anaId)).findFirst();
//                    if (optionalQualityManage.isPresent()) {
//                        DtoQualityManage dtoQualityManage = optionalQualityManage.get();
//                        dtoAnalyseDataTemp.setQmQcType(dtoQualityManage.getQmType());
//                    }
//                }
            }
        }
    }

    /**
     * 设置工作单的质控任务类型
     *
     * @param temp               送样单数据
     * @param qualityManages     质控类型
     * @param dtoAnalyseDataTemp 测试项目数据
     * @param anaId              测试项目id
     */
    protected void writeQmQcType(DtoReceiveSampleRecordTemp temp, List<DtoQualityManage> qualityManages, DtoAnalyseDataTemp dtoAnalyseDataTemp, String anaId) {
        if (temp.getQcGrade().equals(EnumPRO.EnumPorjectQCGrade.内部质控.getValue())
                || temp.getQcGrade().equals(EnumPRO.EnumPorjectQCGrade.外部质控.getValue())
                || temp.getQcGrade().equals(EnumPRO.EnumPorjectQCGrade.分包质控.getValue())) {
            Optional<DtoQualityManage> optionalQualityManage = qualityManages.stream().filter(p -> p.getAnaId().equals(anaId)).findFirst();
            if (optionalQualityManage.isPresent()) {
                DtoQualityManage dtoQualityManage = optionalQualityManage.get();
                dtoAnalyseDataTemp.setQmQcType(dtoQualityManage.getQmType());
            }
        }
    }

    /**
     * 获取公式参数数据
     *
     * @param paramsTestFormulas             公式参数
     * @param sampleParamsDataList           样品参数
     * @param workSheetCalibrationCurves     校准曲线
     * @param dtoTestFormulaParamsConfigList 公式参数配置
     * @param formula                        公式
     * @param isOriginal                     是否有原始数据
     * @param dtoAnalyseDataTemp             分析数据
     * @param map                            转成数据的map对象
     * @param sampleTypeMap                  样品类型映射
     * @return 返回公式参数是否改变
     */
    private Boolean getParamsTestFormula(List<DtoParamsTestFormula> paramsTestFormulas,
                                         List<DtoParamsData> sampleParamsDataList,
                                         List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves,
                                         List<DtoTestFormulaParamsConfig> dtoTestFormulaParamsConfigList,
                                         String formula,
                                         Boolean isOriginal,
                                         DtoAnalyseDataTemp dtoAnalyseDataTemp,
                                         Map<String, Object> map,
                                         List<DtoAnalyseOriginalJson> analyseOriginalJsons,
                                         Map<String, DtoSampleType> sampleTypeMap,
                                         List<DtoSampleTypeGroup> allSampleTypeGroups,
                                         List<DtoSampleTypeGroup2Test> allGroup2TestList,
                                         DtoSolutionCalibration solutionCalibration) {
        Boolean isRed = false;
        String sampleId = dtoAnalyseDataTemp.getSampleId();
        String analyseItemId = dtoAnalyseDataTemp.getAnalyseItemId();
        String workSheetId = dtoAnalyseDataTemp.getWorkSheetId();

        //找到样品类型
        List<DtoSampleType> bigTypes = sampleTypeMap.containsKey(dtoAnalyseDataTemp.getSampleTypeId())
                ? Collections.singletonList(sampleTypeMap.get(dtoAnalyseDataTemp.getSampleTypeId())) : new ArrayList<>();
//        List<DtoSampleType> bigTypes = sampleTypeService.findAll(Collections.singleton(dtoAnalyseDataTemp.getSampleTypeId()));
        //找到分组Ids
        List<String> groupIds = bigTypes.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFieldTaskGroupId())
                && !p.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getFieldTaskGroupId)
                .distinct().collect(Collectors.toList());
        List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(groupIds)) {
            dtoSampleGroups = allSampleTypeGroups.stream().filter(p -> groupIds.contains(p.getParentId())).collect(Collectors.toList());
//            dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(groupIds, EnumLIM.EnumGroupType.分组.getValue());
            if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                List<String> sampleGroupIdList = dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList());
                group2TestList = allGroup2TestList.stream().filter(p -> sampleGroupIdList.contains(p.getSampleTypeGroupId())).collect(Collectors.toList());
//                group2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
            }
        }
        String groupId = UUIDHelper.GUID_EMPTY;

        Optional<DtoSampleTypeGroup2Test> group2Test = group2TestList.stream().filter(p -> dtoAnalyseDataTemp.getTestId().equals(p.getTestId())).findFirst();
        if (group2Test.isPresent()) {
            groupId = group2Test.get().getSampleTypeGroupId();
        }
        String finalGroupId = groupId;
        Optional<DtoSampleTypeGroup> group = dtoSampleGroups.stream().filter(p -> p.getId().equals(finalGroupId)).findFirst();

        //公式参数倒序排序
        List<DtoParamsTestFormula> paramsTestFormulaList = paramsTestFormulas.stream().sorted(Comparator.comparing(DtoParamsTestFormula::getOrderNum, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        // 存储现场参数和实验室数据不一致
        List<String> isRedParams = new ArrayList<>();
        for (DtoParamsTestFormula dtoParamsTestFormula : paramsTestFormulaList) {
            String value = dtoParamsTestFormula.getDefaultValue();
            String aliasName = dtoParamsTestFormula.getAlias();
            if (group.isPresent()) {
                aliasName = String.format("%s-%s", group.get().getGroupName(), aliasName);
                groupId = group.get().getId();
            }
            String finalAliasName = aliasName;
            if (isOriginal) { //是否有原始数据，需要判断数据参数是否一致，不一致需要把isRedSample标红
                //todo 没有考虑分组的情况
                //分析项目参数匹配
                Optional<DtoParamsData> optionalParamsData = sampleParamsDataList.stream().filter(p -> p.getObjectId().equals(sampleId)
                        && p.getParamsName().equals(finalAliasName) && finalGroupId.equals(p.getGroupId())).findFirst();
                if (optionalParamsData.isPresent()) {
                    DtoParamsData paramsData = optionalParamsData.get();
                    if (StringUtil.isNotNull(paramsData.getParamsValue()) && !paramsData.getParamsValue().equals(dtoParamsTestFormula.getDefaultValue())) {
                        isRed = true;
                        isRedParams.add(dtoParamsTestFormula.getAlias());
                    }
                } else {
                    //否则匹配样品上的数据
                    Optional<DtoParamsData> optional = sampleParamsDataList.stream().filter(p -> p.getObjectId().equals(sampleId)
                            && p.getParamsName().equals(dtoParamsTestFormula.getAlias()) && UUIDHelper.GUID_EMPTY.equals(p.getGroupId())).findFirst();
                    if (optional.isPresent()) {
                        DtoParamsData paramsData = optional.get();
                        if (StringUtil.isNotNull(paramsData.getParamsValue()) && !paramsData.getParamsValue().equals(dtoParamsTestFormula.getDefaultValue())) {
                            isRed = true;
                            isRedParams.add(dtoParamsTestFormula.getAlias());
                        }
                    }
                }
                map.put(dtoParamsTestFormula.getAlias(), StringUtil.isNotEmpty(dtoParamsTestFormula.getDefaultValue()) ? dtoParamsTestFormula.getDefaultValue() : "");
            } else {
                //否则原始数据要从样品参数及公式参数上自动匹配
                if (dtoParamsTestFormula.getAlias().equalsIgnoreCase("k")
                        || dtoParamsTestFormula.getAlias().equalsIgnoreCase("b")
                        || dtoParamsTestFormula.getAlias().equalsIgnoreCase("标定浓度均值")
                        || dtoParamsTestFormula.getAlias().equalsIgnoreCase("空白")) {
                    Optional<DtoWorkSheetCalibrationCurve> optionalDtoWorkSheetCalibrationCurve = workSheetCalibrationCurves.stream().
                            filter(p -> p.getWorksheetId().equals(workSheetId)).findFirst();
                    if (optionalDtoWorkSheetCalibrationCurve.isPresent()) {
                        DtoWorkSheetCalibrationCurve standard = optionalDtoWorkSheetCalibrationCurve.get();
                        if (dtoParamsTestFormula.getAlias().equalsIgnoreCase("k")) {
                            value = standard.getK();
                        } else if (dtoParamsTestFormula.getAlias().equalsIgnoreCase("b")) {
                            value = standard.getB();
                        }
                    }
                    if (solutionCalibration != null) {
                        if (StringUtil.isNotEmpty(solutionCalibration.getAverageConcentration())
                                && (dtoParamsTestFormula.getAlias().equalsIgnoreCase("k")
                                || dtoParamsTestFormula.getAlias().equalsIgnoreCase("标定浓度均值"))) {
                            value = solutionCalibration.getAverageConcentration();
                        }
                        if (StringUtil.isNotEmpty(solutionCalibration.getBlankAvg())
                                && dtoParamsTestFormula.getAlias().equalsIgnoreCase("空白")) {
                            value = solutionCalibration.getBlankAvg();
                        }
                    }
                } else {
                    //分析项目参数匹配
                    Optional<DtoParamsData> optionalParamsData = sampleParamsDataList.stream().filter(p -> p.getObjectId().equals(sampleId)
                            && p.getParamsName().equals(finalAliasName) && finalGroupId.equals(p.getGroupId())).findFirst();
                    if (optionalParamsData.isPresent()) {
                        DtoParamsData paramsData = optionalParamsData.get();
                        value = paramsData.getParamsValue();
                    } else {
                        //否则匹配样品上的数据
                        Optional<DtoParamsData> optional = sampleParamsDataList.stream().filter(p -> p.getObjectId().equals(sampleId)
                                && p.getParamsName().equals(dtoParamsTestFormula.getAlias()) && UUIDHelper.GUID_EMPTY.equals(p.getGroupId())).findFirst();
                        if (optional.isPresent()) {
                            DtoParamsData paramsData = optional.get();
                            value = paramsData.getParamsValue();
                        }
                    }
                    if (StringUtil.isNull(value)) {
                        value = "";
                    }
                }
                map.put(dtoParamsTestFormula.getAlias(), value);
            }
            //参数中不包含改参数，需要加进来
            if (dtoTestFormulaParamsConfigList.stream().noneMatch(p -> StringUtils.isNotNullAndEmpty(p.getAlias()) &&
                    p.getAlias().equals(dtoParamsTestFormula.getAlias()))) {
                DtoTestFormulaParamsConfig dtoTestFormulaParamsConfig = new DtoTestFormulaParamsConfig();
                dtoTestFormulaParamsConfig.setOrderNum(dtoParamsTestFormula.getOrderNum());
                dtoTestFormulaParamsConfig.setIsEditable(dtoParamsTestFormula.getIsEditable());
                dtoTestFormulaParamsConfig.setAlias(dtoParamsTestFormula.getAlias());
                dtoTestFormulaParamsConfig.setIsMust(dtoParamsTestFormula.getIsMust());
                dtoTestFormulaParamsConfig.setIsCalculate(formula.contains("[" + dtoParamsTestFormula.getAlias() + "]"));
                dtoTestFormulaParamsConfig.setDetectionLimit(StringUtil.isNotEmpty(dtoParamsTestFormula.getDetectionLimit()) ? dtoParamsTestFormula.getDetectionLimit() : null);
                dtoTestFormulaParamsConfig.setCalculationMode(dtoParamsTestFormula.getCalculationMode());
                dtoTestFormulaParamsConfig.setDimension(dtoParamsTestFormula.getDimension());
                dtoTestFormulaParamsConfig.setSlashValue(dtoParamsTestFormula.getSlashValue());
                dtoTestFormulaParamsConfigList.add(dtoTestFormulaParamsConfig);
            }
            //这边是更换公式之后，这些数据要重新保存
            DtoAnalyseOriginalJson analyseOriginalJson = new DtoAnalyseOriginalJson();
            analyseOriginalJson.setAlias(dtoParamsTestFormula.getAlias());
            analyseOriginalJson.setDefaultValue(value);
            analyseOriginalJson.setIsMust(dtoParamsTestFormula.getIsMust());
            analyseOriginalJson.setIsEditable(dtoParamsTestFormula.getIsEditable());
            analyseOriginalJson.setOrderNum(dtoParamsTestFormula.getOrderNum());
            analyseOriginalJsons.add(analyseOriginalJson);
        }
        map.put("isRed", isRedParams);
        return isRed;
    }

    /**
     * 工作单中分析项目的排序规则
     *
     * @param analyseDataList
     */
    protected void sortAnalyseDataOfWorkSheetFolder(List<Map<String, Object>> analyseDataList) {
        analyseDataList.sort(Comparator.comparing(WorkSheetFolderServiceImpl::comparingByOrderNum)
                .thenComparing(WorkSheetFolderServiceImpl::comparingBySampleCode)
                .thenComparing(WorkSheetFolderServiceImpl::comparingBySampleCategory)
                .thenComparing(WorkSheetFolderServiceImpl::comparingByItemSort, Comparator.reverseOrder())
                .thenComparing(WorkSheetFolderServiceImpl::comparingByRedAnalyzeItemName));
    }

    /**
     * 按排序值排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    protected static String comparingByOrderNum(Map<String, Object> map) {
        return (String) map.get("orderNum");
    }

    /**
     * 按照分析项目名称进行排序
     *
     * @param map 集合对象
     * @return 返回分析项目
     */
    protected static String comparingByRedAnalyzeItemName(Map<String, Object> map) {
        return (String) map.get("redAnalyzeItemName");
    }

    /**
     * 按样品编号排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    protected static String comparingBySampleCode(Map<String, Object> map) {
        return (String) map.get("sampleCode");
    }

    /**
     * 根据公式生成相关的计算参数
     *
     * @param formula           公式
     * @param dtoParamsDataList 参数及数据
     * @return
     */
    private Map<String, Object> getCalculationMap(String formula, List<DtoParamsData> dtoParamsDataList) {
        Map<String, Object> map = new HashMap<>();
        for (DtoParamsData dtoParamsData : dtoParamsDataList) {
            String alias = dtoParamsData.getAlias();
            if (StringUtil.isNotEmpty(alias) && formula.contains(alias)) {
                String paramsValue = dtoParamsData.getParamsValue();
                if (MathUtil.isNumeral(paramsValue)) {
                    map.put(alias, MathUtil.getBigDecimal(paramsValue));
                }
            }
        }
        return map;
    }


    /**
     * 处理待检工作单，复核，已确认的工作单数据
     *
     * @param workSheetFolders 工作单ids
     * @return 返回数据
     */
    private List<Map<String, Object>> dealWorkSheetTask(List<DtoWorkSheetFolder> workSheetFolders, List<Integer> dataStatus) {
        Date t1 = new Date();
        List<Map<String, Object>> awaitWorkSheetList = new ArrayList<>();
        List<String> workSheetFolderIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getId).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(workSheetFolderIds) ? analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds) : new ArrayList<>();
        Date d1 = new Date();
        log.info("===================================查询分析数据用时：" + (d1.getTime() - t1.getTime()) + "ms=======================================");
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
        Date d2 = new Date();
        log.info("===================================查询样品用时：" + (d2.getTime() - d1.getTime()) + "ms=======================================");
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = StringUtil.isNotEmpty(receiveIds) ? receiveSampleRecordService.findAll(receiveIds) : new ArrayList<>();
        List<String> projectIds = receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getProjectId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
        List<String> analyzeMethodIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        Date d3 = new Date();
        log.info("===================================查询送样单和项目用时：" + (d3.getTime() - d2.getTime()) + "ms=======================================");
//        List<DtoLog> logList = StringUtil.isNotEmpty(workSheetFolderIds) ? logForWorkSheetRepository.findByObjectIdIn(workSheetFolderIds).parallelStream().map(DtoLog::new).filter(p -> EnumPRO.EnumLogOperateType.提交检测单.name().equals(p.getOperateInfo())).collect(Collectors.toList()) : new ArrayList<>();
//        List<DtoLog> logList = newLogService.getLogByWorkSheetIds(workSheetFolderIds).stream()
//                .filter(p -> EnumPRO.EnumLogOperateType.提交检测单.name().equals(p.getOperateInfo()))
//                .collect(Collectors.toList());
        Date d4 = new Date();
        log.info("===================================查询检测单日志数据用时：" + (d4.getTime() - d3.getTime()) + "ms=======================================");
        if (workSheetFolderIds.size() > 0) {
            //检测单相关的数据
            List<DtoWorkSheet> workSheetList = workSheetRepository.findByParentIdIn(workSheetFolderIds);
            this.setAnalyseItemOrderNum(workSheetList);
            //分析方法相关的数据
            List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodService.findAllDeleted(analyzeMethodIds);
            List analyseNumList;
            if (StringUtil.isNotEmpty(dataStatus)) {
                analyseNumList = analyseDataRepository.groupAnalyseDataNumByWorkSheetFolderId(workSheetFolderIds, dataStatus);
            } else {
                analyseNumList = analyseDataRepository.groupAnalyseDataNumByWorkSheetFolderIdIgnoreStatus(workSheetFolderIds);
            }

            List grades = analyseDataRepository.findGradeByWorkSheetFolderId(workSheetFolderIds);
            Date t2 = new Date();
            log.info("=============================================准备数据用时：" + (t2.getTime() - d4.getTime()) + "ms====================================");
            Iterator<DtoWorkSheetFolder> iterator = workSheetFolders.iterator();
            while (iterator.hasNext()) {
                Map<String, Object> awaitWorkSheet = new HashMap<>();
                DtoWorkSheetFolder workSheetFolder = iterator.next();
                List<DtoWorkSheet> wsList = workSheetList.stream().filter(p -> p.getParentId().equals(workSheetFolder.getId())).collect(Collectors.toList());
//                this.setAnalyseItemOrderNum(wsList);
                String redAnalyzeItemName = wsList.stream()
                        .sorted(Comparator.comparing(DtoWorkSheet::getAnalyseItemOrderNum, Comparator.reverseOrder()))
                        .map(DtoWorkSheet::getRedAnalyzeItemName)
                        .collect(Collectors.joining(","));
                //数据数
                Integer sampleNum = (Integer) analyseNumList.stream().filter(p -> ((Object[]) p)[1].equals(workSheetFolder.getId()))
                        .map(p -> Integer.parseInt(((Object[]) p)[0].toString())).findFirst().orElse(0);
                //数据数
                Integer grade = (Integer) grades.stream().filter(p -> ((Object[]) p)[1].equals(workSheetFolder.getId()))
                        .map(p -> Integer.parseInt(((Object[]) p)[0].toString())).findFirst().orElse(0);
                awaitWorkSheet.put("id", workSheetFolder.getId());
                awaitWorkSheet.put("workSheetCode", workSheetFolder.getWorkSheetCode());
                awaitWorkSheet.put("grade", grade);
                awaitWorkSheet.put("redAnalyzeItemName", redAnalyzeItemName);
                awaitWorkSheet.put("redAnalyzeMethodId", workSheetFolder.getAnalyzeMethodId());
                awaitWorkSheet.put("certificatorId", workSheetFolder.getCertificatorId());
                awaitWorkSheet.put("certificatorName", workSheetFolder.getCertificatorName());
                awaitWorkSheet.put("redAnalyzeMethodName", analyzeMethodList.stream().filter(p -> p.getId().equals(workSheetFolder.getAnalyzeMethodId())
                        && StringUtils.isNotNullAndEmpty(p.getMethodName())).map(DtoAnalyzeMethod::getMethodName).findFirst().orElse(""));
                awaitWorkSheet.put("redCountryStandard", analyzeMethodList.stream().filter(p -> p.getId().equals(workSheetFolder.getAnalyzeMethodId())
                        && StringUtils.isNotNullAndEmpty(p.getCountryStandard())).map(DtoAnalyzeMethod::getCountryStandard).findFirst().orElse(""));
                awaitWorkSheet.put("isSample", analyzeMethodList.stream().filter(p -> p.getId().equals(workSheetFolder.getAnalyzeMethodId())
                        && StringUtils.isNotNullAndEmpty(p.getCountryStandard())).map(DtoAnalyzeMethod::getIsInputBySample).findFirst().orElse(false));
                awaitWorkSheet.put("analystId", workSheetFolder.getAnalystId());
                awaitWorkSheet.put("analystName", workSheetFolder.getAnalystName());
                awaitWorkSheet.put("auditorId", workSheetFolder.getAuditorId());
                awaitWorkSheet.put("auditorName", workSheetFolder.getAuditorName());
                awaitWorkSheet.put("checkerId", workSheetFolder.getCheckerId());
                awaitWorkSheet.put("checkerName", workSheetFolder.getCheckerName());
                awaitWorkSheet.put("createTime", DateUtil.dateToString(workSheetFolder.getCreateTime(), DateUtil.YEAR));
                awaitWorkSheet.put("analyzeTime", DateUtil.dateToString(workSheetFolder.getAnalyzeTime(), DateUtil.YEAR));
                awaitWorkSheet.put("auditorTime", DateUtil.dateToString(workSheetFolder.getAuditDate(), DateUtil.FULL));
                awaitWorkSheet.put("checkedTime", DateUtil.dateToString(workSheetFolder.getCheckDate(), DateUtil.FULL));
                List<Date> times = new ArrayList<>();
                times.add(workSheetFolder.getAuditDate());
                times.add(workSheetFolder.getCheckDate());
                times.add(workSheetFolder.getSubmitTime());
//                Optional<DtoLog> logOptional = logList.stream().filter(p -> workSheetFolder.getId().equals(p.getObjectId()))
//                        .max(Comparator.comparing(DtoLog::getOperateTime));
//                logOptional.ifPresent(log -> times.add(log.getOperateTime()));
                awaitWorkSheet.put("sortTime", times.stream().max(Date::compareTo).get().toString());
                awaitWorkSheet.put("notPassRemark", workSheetFolder.getBackOpinion());
                awaitWorkSheet.put("remark", workSheetFolder.getRemark());
                awaitWorkSheet.put("status", workSheetFolder.getStatus());
                awaitWorkSheet.put("sortId", workSheetFolder.getSortId());
                awaitWorkSheet.put("backTimes", workSheetFolder.getBackTimes());
                awaitWorkSheet.put("sampleNum", sampleNum);
                List<String> sampleIdsOfWorkSheetFolder = analyseDataList.stream().filter(a -> workSheetFolder.getId().equals(a.getWorkSheetFolderId())).map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<String> receiveIdsOfWorkSheetFolder = sampleList.stream().filter(s -> sampleIdsOfWorkSheetFolder.contains(s.getId())).map(DtoSample::getReceiveId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
                List<String> projectIdsOfWorkSheetFolder = receiveSampleRecords.stream().filter(s -> receiveIdsOfWorkSheetFolder.contains(s.getId())).map(DtoReceiveSampleRecord::getProjectId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
                awaitWorkSheet.put("projectCode", projectList.stream().filter(p -> projectIdsOfWorkSheetFolder.contains(p.getId())).map(DtoProject::getProjectCode).collect(Collectors.joining("、")));
                awaitWorkSheetList.add(awaitWorkSheet);
            }
            Date t3 = new Date();
            log.info("=============================================循环方法用时：" + (t3.getTime() - t2.getTime()) + "ms==========================================");
        }
        return awaitWorkSheetList;
    }

    /**
     * 获取分析项目的排序值，用于列表中多个分析项目的排序
     *
     * @param workSheets 工作单
     */
    private void setAnalyseItemOrderNum(List<DtoWorkSheet> workSheets) {
        List<String> analyseItemIds = workSheets.parallelStream().map(DtoWorkSheet::getAnalyseItemId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(analyseItemIds)) {
            List<DtoAnalyzeItem> analyzeItems = analyzeItemRepository.findAll(analyseItemIds);
            workSheets.forEach(w -> {
                Optional<DtoAnalyzeItem> analyzeItemOptional = analyzeItems.parallelStream().filter(a -> w.getAnalyseItemId().equals(a.getId())).findFirst();
                analyzeItemOptional.ifPresent(a -> w.setAnalyseItemOrderNum(a.getOrderNum()));
            });
        }
    }

    /**
     * 获取分析项目的排序值，工作单选项卡的排序
     *
     * @param worksheetTest 工作单
     */
    private void setAnalyseItemOrderNumForWorksheetTest(List<DtoWorkSheetTest> worksheetTest, String sortId) {
        List<String> analyseItemIds = worksheetTest.parallelStream().map(DtoWorkSheetTest::getRedAnalyzeItemId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(analyseItemIds)) {
            List<DtoAnalyzeItemSortDetail> sortDetails = analyzeItemSortDetailRepository.findBySortId(sortId);
            worksheetTest.forEach(w -> {
                Optional<DtoAnalyzeItemSortDetail> analyzeItemOptional = sortDetails.parallelStream().filter(a -> w.getRedAnalyzeItemId().equals(a.getAnalyzeItemId())).findFirst();
                analyzeItemOptional.ifPresent(a -> w.setAnalyseItemOrderNum(a.getOrderNum()));
            });
        }
    }

    /**
     * 设置检测单的级别
     *
     * @param workSheetFolders 工作单信息
     */
    private void setWorkSheetFolderGrade(List<DtoWorkSheetFolder> workSheetFolders) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select p2w.workSheetFolderId,p.grade");
        stringBuilder.append(" from DtoProject2WorkSheetFolder p2w,DtoProject p where 1=1");
        stringBuilder.append(" and p2w.projectId = p.id");
        stringBuilder.append(" and p2w.workSheetFolderId in :workSheetFolderIds");
        stringBuilder.append(" and p.isDeleted = 0");
        stringBuilder.append(" and p.grade in :grades");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and p.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        values.put("workSheetFolderIds", workSheetFolders.stream().map(DtoWorkSheetFolder::getId).collect(Collectors.toList()));
        values.put("grades", Arrays.asList(EnumPRO.EnumProjectGrade.紧急.getValue(), EnumPRO.EnumProjectGrade.特急.getValue()));
        List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);
        //特急的单子
        List<String> exUrgentIds = datas.stream().filter(p -> p[1].equals(EnumPRO.EnumProjectGrade.特急.getValue())).map(p -> (String) p[0]).collect(Collectors.toList());
        //紧急的单子
        List<String> urgentIds = datas.stream().filter(p -> p[1].equals(EnumPRO.EnumProjectGrade.紧急.getValue())).map(p -> (String) p[0]).collect(Collectors.toList());
        for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
            if (exUrgentIds.contains(workSheetFolder.getId())) {
                workSheetFolder.setGrade(EnumPRO.EnumProjectGrade.特急.getValue());
            } else if (exUrgentIds.contains(workSheetFolder.getId())) {
                workSheetFolder.setGrade(EnumPRO.EnumProjectGrade.紧急.getValue());
            } else {
                workSheetFolder.setGrade(EnumPRO.EnumProjectGrade.一般.getValue());
            }
        }
    }

    /**
     * 刷新工作单的表头参数
     *
     * @param workSheetFolderId 工作单id
     */
    @Override
    public List<DtoWorkSheetParamData> refreshWorksheetParamsData(String workSheetFolderId) {
        //获取所有子级工作单
        List<DtoWorkSheet> workSheets = workSheetRepository.findByParentId(workSheetFolderId);
        List<String> workSheetIds = new ArrayList<>();
        List<String> recordConfigIds = new ArrayList<>();
        List<String> testIds = new ArrayList<>();
        //病例所有子级工作单获取相关id
        for (DtoWorkSheet sheet : workSheets) {
            workSheetIds.add(sheet.getId());
            if (!UUIDHelper.GUID_EMPTY.equals(sheet.getRecordId())) {
                recordConfigIds.add(sheet.getRecordId());
            }
            testIds.add(sheet.getTestId());
        }
        //记录单相关的参数
        List<DtoRecordConfigParams> recordConfigParams = recordConfigParamsConfigService.findTestParamsByTestIds(testIds);
        //获取工作单当前绑定的参数数据
        List<DtoParamsData> oldParamData = paramsDataRepository.findByObjectIdInAndObjectType(workSheetIds, EnumPRO.EnumParamsDataType.检测单.getValue());
        //获取当前工作单最新的原始记录单配置的表头参数
        List<DtoParamsConfig> paramsConfigs = paramsConfigRepository.findByObjIdInAndType(recordConfigIds, EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());
        //获取当前工作单原始记录单中各个测试项目与参数的关联关系
        List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByRecordIdIn(recordConfigIds);
        //获取工作中所有的测试项目
        List<DtoTest> tests = testRepository.findAll(testIds);
        //获取工作单中的方法，因为所有的大工作单中的分析方法都是同一个，所以直接取第一个
        String methodId = tests.get(0).getAnalyzeMethodId();
        DtoAnalyzeMethod method = analyzeMethodService.findOne(methodId);
        //定义最终返回结果，根据分析方法配置的是否同时完成决定返回结构
        List<DtoWorkSheetParamData> result = new ArrayList<>();
        //如果可以同时完成，页面上只有一个选项卡，则只需要返回一个数据结构，如果是多个则需要根据测试项目进行分组
        if (method.getIsCompleteTogether()) {
            //如果同时完成，则只需要返回工作单下所有的参数数据即可
            DtoWorkSheetParamData workSheetParamData = new DtoWorkSheetParamData();
            //因为不用根据测试项目进行分组，所以直接将测试项目id置为空uuid
            workSheetParamData.setTestId(UUIDHelper.GUID_EMPTY);
            //定义返回的参数数据容器
            List<DtoParamsData> paramsDataTemp = new ArrayList<>();
            String workSheetId = workSheetIds.get(0);
            Optional<Map.Entry<String, List<DtoParamsData>>> entryOptional = oldParamData.stream().collect(Collectors.groupingBy(DtoParamsData::getObjectId)).entrySet().stream().max(Comparator.comparingInt(a -> a.getValue().size()));
            if (entryOptional.isPresent()) {
                workSheetId = entryOptional.get().getKey();
            }
            //遍历原始记录单处配置的最新参数来组装数据
            for (DtoParamsConfig paramsConfig : paramsConfigs) {
                //首先从当前工作单中已经存的参数数据中查找数据
                DtoParamsData paramsData = oldParamData.parallelStream().filter(p -> p.getParamsConfigId().equals(paramsConfig.getId())).findFirst().orElse(null);
                //如果参数没有设置DefaultControl也不返回，这个字段用来决定这个参数的值是什么类型
                if (paramsData != null && StringUtil.isNotNull(paramsConfig.getDefaultControl())) {
                    //如果当前工作单中已经存在了当前参数则直接将当前参数返回
                    paramsData.setDefaultControl(paramsConfig.getDefaultControl());
                    paramsData.setDimensionId(paramsConfig.getDimensionId());
                    paramsData.setDimension(paramsConfig.getDimension());
                    if (!paramsData.getObjectId().equals(workSheetId)) {
                        paramsData.setObjectId(workSheetId);
                    }
                } else {
                    //如果当前工作单中没有这个参数数据证明是原始记录单那边新配置的参数，使用参数配置来创建参数数据对象
                    paramsData = new DtoParamsData();
                    paramsData.initParamDataByParamConfig(paramsConfig);
                    //设置参数绑定为工作单参数
                    paramsData.setObjectType(EnumPRO.EnumParamsDataType.检测单.getValue());
                    //因为工作单查询时会获取所有的子级工作单绑定的参数，所以这里将参数绑定随意一个子级工作单即可
                    paramsData.setObjectId(workSheetId);
                }
                DtoParamsData finalParamsData = paramsData;
                Optional<DtoRecordConfigParams> configParams = recordConfigParams.stream().filter(p -> p.getId()
                        .equals(finalParamsData.getParamsConfigId())).findFirst();
                configParams.ifPresent(p -> setParamsData(finalParamsData, p));
                //将组装完成的参数输入放入临时容器中
                paramsDataTemp.add(finalParamsData);
            }
            //将参数按照排序值进行排序
            paramsDataTemp.sort(Comparator.comparing(DtoParamsData::getOrderNum, Comparator.reverseOrder()));
            workSheetParamData.setParamsData(paramsDataTemp);
            result.add(workSheetParamData);
        } else {
            //如果分析方法没有设置可同时完成，此时页面上会显示多个测试项目选项卡，所以返回的数据需要根据测试项目进行分组
            for (DtoWorkSheet workSheet : workSheets) {
                List<DtoRecordConfigParams> recordConfigParamsList = recordConfigParams.stream().filter(p -> p.getTestId()
                        .equals(workSheet.getTestId()) && p.getRecordId().equals(workSheet.getRecordId())).collect(Collectors.toList());
                //定义当前工作单的返回容器对象
                DtoWorkSheetParamData workSheetParamData = new DtoWorkSheetParamData();
                //设置当前工作单的测试项目id，前端会根据此id进行数据匹配
                workSheetParamData.setTestId(workSheet.getTestId());
                //筛选当前工作单的目前已经绑定的参数数据
                List<DtoParamsData> oldParamsDataOfWorkSheetItem = oldParamData.parallelStream().filter(p -> p.getObjectId().equals(workSheet.getId())).collect(Collectors.toList());
                //获取当前工作单的原始记录单中配置的测试项目与参数绑定关系对象集合 -- 表头参数不需要通过测试项目去过滤 2022-3-11
                //List<DtoParams2ParamsFormula> params2ParamsFormulasOfWorkSheetItem = params2ParamsFormulas.parallelStream().filter(p -> p.getObjectId().equals(workSheet.getTestId())).collect(Collectors.toList());
                //定义容器用来存储当前工作单最后组装完成的表头参数
                List<DtoParamsData> paramsDataOfWorkSheetItem = new ArrayList<>();
                //遍历原始记录单配置的关联关系来组装数据
                for (DtoParamsConfig paramsConfig : paramsConfigs) {
                    //从当前工作单已经绑定的参数数据中查询数据
                    DtoParamsData paramsData = oldParamsDataOfWorkSheetItem.parallelStream().filter(p -> p.getParamsConfigId().equals(paramsConfig.getId())).findFirst().orElse(null);
                    if (StringUtil.isNull(paramsData)) {
                        //如果没有数据则使用原始记录单参数配置初始化参数数据
                        paramsData = new DtoParamsData();
                        Optional<DtoParamsConfig> paramsConfigOptional = paramsConfigs.parallelStream().filter(p -> p.getId().equals(paramsConfig.getId())).findFirst();
                        paramsConfigOptional.ifPresent(paramsData::initParamDataByParamConfig);
                        //给参数绑定当前的工作单对象
                        paramsData.setObjectId(workSheet.getId());
                        paramsData.setObjectType(EnumPRO.EnumParamsDataType.检测单.getValue());
                    } else {
                        //如果当前参数已经存在则设置DefaultControl后直接放入返回容器中
                        DtoParamsData finalParamsData = paramsData;
                        Optional<DtoParamsConfig> paramsConfigOptional = paramsConfigs.parallelStream().filter(p -> p.getId().equals(finalParamsData.getParamsConfigId())).findFirst();
                        paramsConfigOptional.ifPresent(p -> {
                            finalParamsData.setDefaultControl(p.getDefaultControl());
                            finalParamsData.setDimensionId(p.getDimensionId());
                            finalParamsData.setDimension(p.getDimension());
                        });
                    }
                    DtoParamsData finalParamsData = paramsData;
                    Optional<DtoRecordConfigParams> configParams = recordConfigParamsList.stream().filter(p -> p.getId()
                            .equals(finalParamsData.getParamsConfigId())).findFirst();
                    configParams.ifPresent(p -> setParamsData(finalParamsData, p));
                    paramsDataOfWorkSheetItem.add(finalParamsData);
                }
                //对参数进行排序
                paramsDataOfWorkSheetItem.sort(Comparator.comparing(DtoParamsData::getOrderNum, Comparator.reverseOrder()));
                workSheetParamData.setParamsData(paramsDataOfWorkSheetItem);
                result.add(workSheetParamData);
            }
        }
        return result;
    }

    @Override
    public List<DtoAnalyseDataEvaluation> findAnalyseDataEvaluation(PageBean<DtoAnalyseData> pageBean, DtoAnalyseDataEvaluationVo analyseDataEvaluation) {
        //获取参数
        AnalyseDataEvaluationCriteria analyseDataEvaluationCriteria = new AnalyseDataEvaluationCriteria();
        BeanUtils.copyProperties(analyseDataEvaluation, analyseDataEvaluationCriteria);
        pageBean.setEntityName("DtoAnalyseData a,DtoSample s");
        pageBean.setSelect("select new com.sinoyd.lims.pro.dto.DtoAnalyseData(a.id,s.id,s.code," +
                "a.testValue,a.redAnalyzeItemName, a.dimension, a.examLimitValue)");
        comRepository.findByPage(pageBean, analyseDataEvaluationCriteria);
        List<DtoAnalyseData> analyseData = pageBean.getData();
        //获取样品
        List<String> sampleIds = analyseData.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findAll(sampleIds);
        //获取所有评价标准
        List<String> analyseDataIds = analyseData.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        List<DtoEvaluationRecord> evaluationRecords = evaluationRecordRepository.findByObjectIdInAndObjectType(analyseDataIds, EnumPRO.EnumEvaluationType.分析数据.getValue());
        List<String> evaluationIds = evaluationRecords.stream().map(DtoEvaluationRecord::getEvaluationId).distinct().collect(Collectors.toList());
        List<String> evaluationLevelIds = evaluationRecords.stream().map(DtoEvaluationRecord::getEvaluationLevelId).distinct().collect(Collectors.toList());
        //评价信息不从评价标准获取，只从方案配置的评价内容获取
        List<DtoEvaluationCriteria> evaluationCriteriaList = new ArrayList<>();
        List<DtoEvaluationLevel> evaluationLevelList = new ArrayList<>();
        //获取评价
        if (StringUtil.isNotEmpty(evaluationIds)) {
            evaluationCriteriaList = evaluationCriteriaRepository.findAll(evaluationIds);
        }
        //获取评价等级
        if (StringUtil.isNotEmpty(evaluationLevelIds)) {
            evaluationLevelList = evaluationLevelRepository.findAll(evaluationLevelIds);
        }
        // 获取量纲
        List<String> dimensionIds = evaluationRecords.stream().map(DtoEvaluationRecord::getDimensionId).distinct().collect(Collectors.toList());
        List<DtoDimension> dimensionList = StringUtil.isNotEmpty(dimensionIds) ? dimensionRepository.findAll(dimensionIds) : new ArrayList<>();

        // 检测类型
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(samples) ? sampleTypeService.findRedisByIds(samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList())) : new ArrayList<>();
        Boolean isEmissionRate = StringUtil.isNotEmpty(sampleTypes) ? sampleTypes.stream().anyMatch(DtoSampleType::getIsEmissionRate) : Boolean.FALSE;
        List<DtoAnalyseDataEvaluation> result = new ArrayList<>();
        for (DtoAnalyseData dtoAnalyseData : analyseData) {
            DtoAnalyseDataEvaluation dataEvaluation = new DtoAnalyseDataEvaluation();
            //填充分析项目
            dataEvaluation.setAnalyseItemName(dtoAnalyseData.getRedAnalyzeItemName());
            //填充出征结果
            dataEvaluation.setTestValue(dtoAnalyseData.getTestValue());
            //获取检测结果修约后的值
            dataEvaluation.setTestValueDecimal(dtoAnalyseData.getTestValueD());
            //获取样品编号
            Optional<DtoSample> sampleOptional = samples.stream()
                    .filter(s -> dtoAnalyseData.getSampleId().equals(s.getId())).findFirst();
            sampleOptional.ifPresent(s -> dataEvaluation.setSampleCode(s.getCode()));
            //获取评价标准
            Optional<DtoEvaluationRecord> evaluationRecordOptional = evaluationRecords.stream()
                    .filter(e -> EnumPRO.EnumEvaluationType.分析数据.getValue().equals(e.getObjectType())
                            && e.getObjectId().equals(dtoAnalyseData.getId())).findFirst();
            List<DtoEvaluationCriteria> finalEvaluationCriteriaList = evaluationCriteriaList;
            List<DtoEvaluationLevel> finalEvaluationLevelList = evaluationLevelList;
            //填充评价标准
//            List<DtoEvaluationValue> finalEvaluationValues = evaluationValues;
            evaluationRecordOptional.ifPresent(e -> {
                //获取评价标准
                Optional<DtoEvaluationCriteria> evaluationCriteria = finalEvaluationCriteriaList.stream()
                        .filter(c -> c.getId().equals(e.getEvaluationId())).findFirst();
                //获取评价等级
                Optional<DtoEvaluationLevel> evaluationLevel = finalEvaluationLevelList.stream()
                        .filter(c -> c.getId().equals(e.getEvaluationLevelId())).findFirst();
                evaluationCriteria.ifPresent(c -> dataEvaluation.setEvaluation(c.getName()));
                evaluationLevel.ifPresent(l -> dataEvaluation.setEvaluationLevel(l.getName()));
                // 量纲
                dimensionList.stream().filter(p -> p.getId().equals(e.getDimensionId())).findFirst().ifPresent(dimension -> {
                    dataEvaluation.setDimensionName(dimension.getDimensionName());
                });
                //先获取填充的上下限值
                if (StringUtil.isNotNull(e.getLowerLimitValue())) {
                    dataEvaluation.setLowerLimit(e.getLowerLimitValue());
                }
                if (StringUtil.isNotNull(e.getUpperLimitValue())) {
                    dataEvaluation.setUpperLimit(e.getUpperLimitValue());
                }
                //根据上下限和出证结果计算是否合格，ND为合格，无出证结果不显示
                String lowerLimtiSymble = StringUtil.isNotEmpty(e.getLowerLimitSymble()) ? e.getLowerLimitSymble() : "";
                String upperLimitSymble = StringUtil.isNotEmpty(e.getUpperLimitSymble()) ? e.getUpperLimitSymble() : "";
                dataEvaluation.setLowerLimitSymble(lowerLimtiSymble);
                dataEvaluation.setUpperLimitSymble(upperLimitSymble);
                if (StringUtil.isNotEmpty(dataEvaluation.getTestValue()) && !"ND".equals(dataEvaluation.getTestValue())
                        && !dataEvaluation.getTestValue().contains("<") && !dataEvaluation.getTestValue().contains("＜")
                        && !dataEvaluation.getTestValue().contains("L")) {
                    if (StringUtil.isNotEmpty(dataEvaluation.getLowerLimit())
                            && MathUtil.isNumeral(dataEvaluation.getTestValue())) {
                        BigDecimal lower = new BigDecimal(dataEvaluation.getLowerLimit());
                        BigDecimal testValDec = new BigDecimal(dataEvaluation.getTestValue());
                        //出证结果小于下限或者出证结果等于下限且下限符号为 “>”时，判定为不合格
                        if (testValDec.compareTo(lower) < 0 || (testValDec.compareTo(lower) == 0 && ">".equals(lowerLimtiSymble))) {
                            dataEvaluation.setIsQualified("不合格");
                        }
                    }
                    if (StringUtil.isNotEmpty(dataEvaluation.getUpperLimit())
                            && MathUtil.isNumeral(dataEvaluation.getTestValue())) {
                        BigDecimal upper = new BigDecimal(dataEvaluation.getUpperLimit());
                        BigDecimal testValDec = new BigDecimal(dataEvaluation.getTestValue());
                        //出证结果大于上限或者出证结果等于上限且上限符号为 “<”时，判定为不合格
                        if (testValDec.compareTo(upper) > 0 || (testValDec.compareTo(upper) == 0) && "<".equals(upperLimitSymble)) {
                            dataEvaluation.setIsQualified("不合格");
                        }
                    }
                    if (StringUtil.isEmpty(dataEvaluation.getIsQualified())) {
                        dataEvaluation.setIsQualified("合格");
                    }
                } else if ("ND".equals(dataEvaluation.getTestValue()) || dataEvaluation.getTestValue().contains("<")
                        || dataEvaluation.getTestValue().contains("＜") || dataEvaluation.getTestValue().contains("L")) {
                    dataEvaluation.setIsQualified("合格");
                } else {
                    dataEvaluation.setIsQualified("");
                }
                // 是否排放速率赋值
                dataEvaluation.setIsEmissionRate(isEmissionRate);
                if (isEmissionRate) {
                    // 允许排放速率
                    dataEvaluation.setAllowEmissionRate(e.getEmissionRate());
                    String pfSpeed = e.getEmissionRateValue();
                    dataEvaluation.setEmissionRate(e.getEmissionRateValue());
                    // 排放速率判定
                    if (!"/".equals(pfSpeed) && StringUtil.isNotEmpty(pfSpeed) && MathUtil.isNumeral(pfSpeed)) {
                        if (StringUtil.isNotEmpty(e.getEmissionRate()) && MathUtil.isNumeral(e.getEmissionRate())) {
                            BigDecimal emissionRateBig = new BigDecimal(e.getEmissionRate());
                            BigDecimal pfSpeedBig = new BigDecimal(pfSpeed);
                            if (StringUtil.isNotEmpty(dataEvaluation.getTestValue()) && !"ND".equals(dataEvaluation.getTestValue())
                                    && !dataEvaluation.getTestValue().contains("<") && !dataEvaluation.getTestValue().contains("＜")
                                    && !dataEvaluation.getTestValue().contains("L")) {
                                if (pfSpeedBig.compareTo(emissionRateBig) < 0) {
                                    dataEvaluation.setEmissionRateJudge("合格");
                                } else {
                                    dataEvaluation.setEmissionRateJudge("不合格");
                                }
                            } else {
                                dataEvaluation.setEmissionRateJudge("合格");
                            }
                        }
                    } else {
                        dataEvaluation.setEmissionRateJudge("");
                        if (StringUtil.isNotEmpty(dataEvaluation.getTestValue()) && !"ND".equals(dataEvaluation.getTestValue())
                                && !dataEvaluation.getTestValue().contains("<") && !dataEvaluation.getTestValue().contains("＜")
                                && !dataEvaluation.getTestValue().contains("L")) {
                            dataEvaluation.setEmissionRateJudge("");
                        } else {
                            if (StringUtil.isNotEmpty(pfSpeed) && StringUtil.isNotEmpty(e.getEmissionRate())) {
                                dataEvaluation.setEmissionRateJudge("合格");
                            }
                        }
                    }
                }
            });
            result.add(dataEvaluation);
        }
        //是否合格筛选
        if (analyseDataEvaluation.getQualifiedType() != -1) {
            if (analyseDataEvaluation.getQualifiedType() == 1) {
                result = result.stream().filter(a -> "合格".equals(a.getIsQualified())).collect(Collectors.toList());
            } else {
                result = result.stream().filter(a -> "不合格".equals(a.getIsQualified())).collect(Collectors.toList());
            }
        }
        //先按照测试项目排序，再按照样品编号倒序排序
        result.sort(Comparator.comparing(DtoAnalyseDataEvaluation::getAnalyseItemName)
                .thenComparing(DtoAnalyseDataEvaluation::getSampleCode));
        return result;
    }

    @Override
    public void validationForSubmit(String workSheetFolderId) {
        validateForFillingInstrumentInfo(workSheetFolderId);
    }

    @Override
    public List<String> findWorkSheetFolderSampleIds(String workSheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        return sampleIds;
    }

    /**
     * 校验仪器信息是否填写
     *
     * @param workSheetFolderId 检测单id
     */
    private void validateForFillingInstrumentInfo(String workSheetFolderId) {
        boolean flag = Boolean.FALSE;
        //判断是否存在测试项目配置了必须填写填写仪器记录，如果有 flag  = true
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderId(workSheetFolderId);
        List<String> testIds = analyseDataList.parallelStream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findAll(testIds);
        for (DtoTest test : testList) {
            if (test.getIsInsUseRecord()) {
                flag = Boolean.TRUE;
                break;
            }
        }
        //存在测试项目配置了必须填写填写仪器记录， 则要判断是否填写了仪器记录，如果没有则不允许提交
        if (flag) {
            List<DtoEnvironmentalRecord> environmentalRecordList = environmentalRecordRepository.findByObjectId(workSheetFolderId);
            if (StringUtil.isEmpty(environmentalRecordList)) {
                throw new BaseException("未填写环境及仪器使用记录，无法提交！");
            }
        }
    }

    /**
     * 分析数据录入，跳过复核步骤的时候，过滤审核
     *
     * @param analyseDataList 分析数据
     * @return 过滤的分析数据
     */
    protected List<DtoAnalyseData> getAnalyseList(List<DtoAnalyseData> analyseDataList) {
        return analyseDataList.stream().filter(p -> p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue())).collect(Collectors.toList());
    }


    /**
     * 根据曲线Id获取工作单
     *
     * @param curveId 曲线Id
     * @return 工作单
     */
    @Override
    public List<DtoWorkSheetFolder> findWorksheetByCurves(String curveId) {
        //获取所有的曲线数据
        List<DtoWorkSheetFolder> folders = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select s from DtoWorkSheetCalibrationCurve s");
        sql.append(" where 1=1 and s.isDeleted = 0");
        sql.append(" and s.standardCurveId = :standardId");
        Map<String, Object> value = new HashMap<>();
        if (StringUtil.isNotEmpty(curveId)) {
            value.put("standardId", curveId);
        }
        List<DtoWorkSheetCalibrationCurve> worksheetCurves = comRepository.find(sql.toString(), value);

        //根据曲线信息获取子工作单Id
        List<DtoWorkSheet> workSheets = new ArrayList<>();
        if (StringUtil.isNotEmpty(worksheetCurves)) {
            List<String> worksheetIds = worksheetCurves.stream().map(DtoWorkSheetCalibrationCurve::getWorksheetId).distinct().collect(Collectors.toList());
            //获取所有子工作单
            workSheets = workSheetRepository.findAll(worksheetIds);
        }
        //获取所有工作单Id
        List<String> folderIds = workSheets.stream().map(DtoWorkSheet::getParentId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(folderIds)) {
            //查询所有工作单
            folders = repository.findAll(folderIds);
        }
        return folders;
    }

    /**
     * 批量新增质控样
     *
     * @param vo 数据传输对象
     */
    @Override
    @Transactional
    public void batchAddQcSample(WorkSheetQualityControlBatchOperationVo vo) {
        List<DtoSample> qcSamples = sampleService.createQcSamples(vo.getSampleIds(), vo.getQcType(),
                EnumLIM.EnumQCGrade.内部质控.getValue(), vo.getWorkSheetFolderId(), vo.getQualityControls());
        List<DtoQualityControl> qualityControls;
        if (QualityTaskFactory.getInstance().getQcSample(vo.getQcType()).qcTypeName().equals("替代")) {
            qualityControls = initQualityControlForReplace(vo, qcSamples);
        } else {
            qualityControls = initQualityControlOfInnerQcSample(vo, qcSamples);
        }
        createAnalyzeData(vo, qcSamples, qualityControls);
        if (StringUtil.isNotEmpty(qcSamples)) {
            sampleRepository.save(qcSamples);
        }
        if (StringUtil.isNotEmpty(qualityControls)) {
            qualityControlService.save(qualityControls);
        }
    }

    /**
     * 获取工作单下的测试项目对应的默认复核人
     *
     * @param analyseCheckPerson 接收实体
     * @return 默认复核人
     */
    @Override
    public DtoKeyValue findDefaultAuditPerson(DtoAnalyseCheckPerson analyseCheckPerson) {
        String personId = analyseCheckPerson.getPersonId();
        List<DtoWorkSheetTest> auditPersonTests = analyseCheckPerson.getWorkSheetTests();
        //默认复核人
        DtoKeyValue keyValue = new DtoKeyValue();
        //获取到工作单下的测试项目id
        List<String> testIds = auditPersonTests.stream().map(DtoWorkSheetTest::getId).distinct().collect(Collectors.toList());
        //获取工作单下的检测类型id
        List<String> sampleTypeIds = auditPersonTests.stream().map(DtoWorkSheetTest::getSampleTypeId).distinct().collect(Collectors.toList());
        //测试项目大类id
        List<String> bigSampleTypeIds = auditPersonTests.stream().map(DtoWorkSheetTest::getBigSampleTypeId).distinct().collect(Collectors.toList());
        //获取测试项目检测人员
        List<DtoPerson2Test> personToTests = person2TestRepository.findByTestIdInAndSampleTypeIdIn(testIds, sampleTypeIds);
        //获取默认复核人员
        DtoPerson2Test defaultPerson = personToTests.stream().filter(p -> p.getIsDefaultAuditPerson() && !personId.equals(p.getPersonId())).findFirst().orElse(null);
        //如果对应小类未配置检测人员数据，则取默认配置
        if (StringUtil.isNull(defaultPerson)) {
            personToTests.addAll(person2TestRepository.findByTestIdInAndSampleTypeIdIn(testIds, bigSampleTypeIds));
            defaultPerson = personToTests.stream().filter(p -> p.getIsDefaultAuditPerson() && !personId.equals(p.getPersonId())).findFirst().orElse(null);
        }
        if (StringUtil.isNotNull(defaultPerson)) {
            String personName = personService.findPersonNameById(defaultPerson.getPersonId());
            keyValue.setKey(defaultPerson.getPersonId());
            keyValue.setValue(personName);
        }
        return keyValue;
    }

    @Override
    public Map<String, Object> findTodoCount(String personId) {
        Map<String, Object> todoCountMap = new HashMap<>();
        // 待检测数据
        List<Map<String, Object>> awaitSampleCount = this.getAwaitSampleCount(personId);
        // 在检检测单数量
        Map<String, Object> awaitCount = this.getAwaitCount(personId);
        // 审核中数量
        Map<String, Object> auditCount = this.getAuditCount(personId);
        todoCountMap.put("awaitSampleCount", awaitSampleCount.size());
        todoCountMap.put("awaitWorkSheetCount", awaitCount.getOrDefault("num", 0));
        todoCountMap.put("auditWorkSheet", auditCount.getOrDefault("num", 0));
        return todoCountMap;
    }


    /**
     * BUG2024050901251
     * 【实验室分析】数据录入页面中添加一个“剔除”下拉框，将“剔除样品”和“剔除数据”放到“剔除”下拉框中，“剔除”下拉框中再添加一个“剔除替代样”按钮。
     * 添加批量删除替代样功能，在删除时要判断所勾选的样品是否为替代样，如果所勾选的为替代样点击剔除替代样按钮则将该检测单所有样品内和所勾选替代样分析项目相同的替代样全部删除
     *
     * @param analyseRemove 参数容器
     */
    @Override
    @Transactional
    public List<String> removeReplaceSampleWithSameAnalyzeItem(DtoAnalyseRemove analyseRemove) {
        String workSheetFolderId = analyseRemove.getWorkSheetFolderId();
        List<Map<String, Object>> analyseDatas = analyseRemove.getAnalyseDatas();
        List<String> removeIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(workSheetFolderId) && StringUtil.isNotEmpty(analyseDatas)) {
            QualityReplace qualityReplace = new QualityReplace();
            List<String> redAnalyzeItemNames = new ArrayList<>();
            analyseDatas.forEach(a -> {
                if (!qualityReplace.qcTypeValue().equals(a.get("qcType"))) {
                    throw new BaseException("勾选样品不是替代物！");
                }
                if (a.get("redAnalyzeItemName") != null && StringUtil.isNotEmpty((String) a.get("redAnalyzeItemName"))) {
                    redAnalyzeItemNames.add((String) a.get("redAnalyzeItemName"));
                }
            });
            if (!redAnalyzeItemNames.isEmpty()) {
                List<DtoAnalyseDataTemp> analyseDataTemps = findDataByWorksheetFolderId(Boolean.FALSE, Boolean.FALSE, workSheetFolderId);
                //过滤出所有同分析项目替代物
                removeIds = analyseDataTemps.stream().filter(p -> p.getQcType().equals(qualityReplace.qcTypeValue())
                        && redAnalyzeItemNames.contains(p.getRedAnalyzeItemName())).map(DtoAnalyseDataTemp::getId).collect(Collectors.toList());
                analyseDataService.removeDataFromWorkSheet(removeIds, workSheetFolderId, true, false);
            }
        }
        return removeIds;
    }

    /**
     * 获取待检测待办数据
     *
     * @param personId 人员id
     * @return 待检测数据
     */
    private List<Map<String, Object>> getAwaitSampleCount(String personId) {
        //判断是否按岗位分配
        boolean allocateByPost = false;
        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            allocateByPost = true;
        }
        StringBuilder awaitSampleSql = new StringBuilder()
                .append("SELECT a.id, a.testId FROM TB_PRO_AnalyseData a, TB_PRO_Sample s, TB_PRO_ReceiveSampleRecord r")
                .append(" WHERE  1 = 1 AND r.isDeleted = 0 AND a.isDeleted = 0  ")
                .append(" and a.orgId = ? and s.STATUS <> ? AND a.sampleId = s.id and s.receiveId = r.id ")
                .append(" AND s.isDeleted = 0  AND a.workSheetId = ? AND a.workSheetFolderId = ? ")
                .append(" AND a.isOutsourcing = 0 AND a.isCompleteField = 0 AND a.isSamplingOut = 0  ")
                .append("  AND a.dataStatus = ? AND s.innerReceiveStatus = ? ")
                .append(" AND s.orgId = ? AND r.orgId = ?");
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        Object[] array = new Object[]{orgId, EnumPRO.EnumSampleStatus.样品作废.toString(), UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumAnalyseDataStatus.未测.getValue(), EnumPRO.EnumInnerReceiveStatus.已经领取.getValue(), orgId, orgId};
        List<Object> paramsList = new ArrayList<>(Arrays.asList(array));
        if (!allocateByPost) {
            awaitSampleSql.append(" AND a.analystId = ?");
            paramsList.add(personId);
        }
        List<Map<String, Object>> awaitSampleCount = jdbcTemplate.queryForList(awaitSampleSql.toString(), paramsList.toArray());

        //按岗位分配开关开启时，需要需要根据人员id配置的所有岗位进行过滤
        if (allocateByPost) {
            List<DtoTestPost2Test> allTestPost2TestList = testPost2TestRepository.findAll();
            List<String> allPost2TestIds = allTestPost2TestList.stream().map(DtoTestPost2Test::getTestId).collect(Collectors.toList());
            //获取前端传递的岗位id查询条件，为空则查询该人员配置的所有岗位
            List<String> testPostIdList = testPostService.findByPerson(PrincipalContextUser.getPrincipal().getUserId()).stream().map(DtoTestPost::getId).collect(Collectors.toList());
            testPostIdList.add("其他");

            // 按岗位分配模式下，待检测列表中，右侧选择岗位下拉框默认新增一个“其他”数据源，用于检索未配置岗位的测试项目；
            if (StringUtil.isNotEmpty(testPostIdList)) {
                // 定义是否包含其他岗位
                boolean containsOthers = testPostIdList.contains("其他");
                List<DtoTestPost2Test> testPost2TestList = StringUtil.isNotEmpty(testPostIdList) ?
                        allTestPost2TestList.stream().filter(p -> testPostIdList.contains(p.getTestPostId())).collect(Collectors.toList())
                        : new ArrayList<>();
                List<String> testIdList = testPost2TestList.stream().map(DtoTestPost2Test::getTestId).distinct().collect(Collectors.toList());
                awaitSampleCount = awaitSampleCount.stream().filter(p -> testIdList.contains(p.get("testId").toString())).collect(Collectors.toList());

                if (containsOthers) {
                    // 用于检索未配置岗位的测试项目
                    List<Map<String, Object>> notConfigDatas = awaitSampleCount.stream().filter(p -> !allPost2TestIds.contains(p.get("testId").toString())).collect(Collectors.toList());
                    awaitSampleCount.addAll(notConfigDatas);
                }
            }
        }
        return awaitSampleCount;
    }

    /**
     * 获取检测中待办数量
     *
     * @param personId 人员id
     * @return 待检数量
     */
    private Map<String, Object> getAwaitCount(String personId) {
        // 在检检测单数量
        StringBuilder awaitSql = new StringBuilder().append("select count(id) as num from")
                .append(" TB_PRO_WorkSheetFolder a")
                .append(" where 1 = 1")
                .append(" and a.workStatus in (1,2,6)")
                .append(" and a.isDeleted = 0")
                .append(" and a.analystId = ?");
        return jdbcTemplate.queryForMap(awaitSql.toString(), new Object[]{personId});
    }

    /**
     * 获取审核中待办数量
     *
     * @param personId 人员id
     * @return 审核数量
     */
    private Map<String, Object> getAuditCount(String personId) {
        // 在检检测单数量
        StringBuilder auditSql = new StringBuilder().append("select count(b.id) as num from")
                .append(" (select a.orgId, a.id,a.checkerId as personId,a.isDeleted from TB_PRO_WorkSheetFolder a ")
                .append(" where 1 = 1 and a.workStatus = 8 union")
                .append(" select a.orgId, a.id, a.auditorId as personId,a.isDeleted from TB_PRO_WorkSheetFolder a ")
                .append(" where 1 = 1 and a.workStatus = 24 union")
                .append(" select a.orgId, a.id,a.certificatorId as personId,a.isDeleted from TB_PRO_WorkSheetFolder a ")
                .append(" where 1 = 1 and a.workStatus = 16) b")
                .append(" where b.isDeleted = 0 and b.personId = ?");
        return jdbcTemplate.queryForMap(auditSql.toString(), new Object[]{personId});
    }

    /**
     * 创建分析数据
     *
     * @param vo              数据传输对象
     * @param qcSamples       质控样
     * @param qualityControls 质控数据
     */
    private void createAnalyzeData(WorkSheetQualityControlBatchOperationVo vo, List<DtoSample> qcSamples, List<DtoQualityControl> qualityControls) {
        List<DtoAnalyseData> analyseData = analyseDataRepository.findByWorkSheetFolderId(vo.getWorkSheetFolderId()).stream().filter(a -> vo.getSampleIds().contains(a.getSampleId())).collect(Collectors.toList());
        List<String> analyzeDataIds = analyseData.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyzeDataIds);
        List<DtoAnalyseOriginalRecord> qcOriginalList = new ArrayList<>();
        List<DtoAnalyseData> qcAnalyzeDataList = new ArrayList<>();
        List<DtoQualityControlEvaluate> evaluateList = new ArrayList<>();
        List<String> testIdList = analyseData.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoQualityControlLimit> qualityControlLimitList = StringUtil.isNotEmpty(testIdList) ? qualityControlLimitRepository.findByTestIdIn(testIdList) : new ArrayList<>();
        List<DtoDimension> dimensionList = dimensionRepository.findAll();
        Map<String, DtoDimension> dimensionMap = dimensionList.stream().collect(Collectors.toMap(DtoDimension::getId, dto -> dto));
        for (DtoSample qcSample : qcSamples) {
            Optional<DtoAnalyseData> analyseDataOptional = analyseData.stream().filter(a -> a.getSampleId().equals(qcSample.getAssociateSampleId())).findFirst();
            DtoAnalyseData qcAnalyzeData = new DtoAnalyseData();
//            DtoAnalyseOriginalRecord qcAnalyseOriginalRecord = new DtoAnalyseOriginalRecord();
            analyseDataOptional.ifPresent(a -> {
                qcAnalyzeData.setWorkSheetId(a.getWorkSheetId());
                qcAnalyzeData.setTestId(a.getTestId());
                qcAnalyzeData.setRedAnalyzeMethodName(a.getRedAnalyzeMethodName());
                qcAnalyzeData.setRedCountryStandard(a.getRedCountryStandard());
                qcAnalyzeData.setAnalyseItemId(a.getAnalyseItemId());
                qcAnalyzeData.setAnalyzeMethodId(a.getAnalyzeMethodId());
                qcAnalyzeData.setMostSignificance(a.getMostSignificance());
                qcAnalyzeData.setMostDecimal(a.getMostDecimal());
                qcAnalyzeData.setExamLimitValue(a.getExamLimitValue());
                qcAnalyzeData.setDimensionId(a.getDimensionId());
                qcAnalyzeData.setDimension(a.getDimension());
                qcAnalyzeData.setAnalystId(a.getAnalystId());
                qcAnalyzeData.setAnalystName(a.getAnalystName());
                qcAnalyzeData.setRequireDeadLine(a.getRequireDeadLine());
                qcAnalyzeData.setGatherCode(qcSample.getCode());
                qcAnalyzeData.setLowerLimit(a.getLowerLimit());
                //如果是替代物，采集编号需要与原样保持一致
                if (new QualityReplace().qcTypeValue().equals(vo.getQcType())) {
                    qcAnalyzeData.setGatherCode(a.getGatherCode());
                    qcAnalyzeData.setExamLimitValue("");
                }
                Optional<DtoAnalyseOriginalRecord> analyseOriginalRecord = originalRecordList.stream().filter(o -> o.getAnalyseDataId().equals(a.getId())).findFirst();
                analyseOriginalRecord.ifPresent(o -> {
                    DtoAnalyseOriginalRecord qcAnalyseOriginalRecord = new DtoAnalyseOriginalRecord();
                    qcAnalyseOriginalRecord.setJson(o.getJson());
                    qcAnalyseOriginalRecord.setTestFormula(o.getTestFormula());
                    qcAnalyseOriginalRecord.setTestFormulaId(o.getTestFormulaId());
                    qcAnalyseOriginalRecord.setAnalyseDataId(qcAnalyzeData.getId());
                    qcOriginalList.add(qcAnalyseOriginalRecord);
                });
            });
//            qcAnalyseOriginalRecord.setAnalyseDataId(qcAnalyzeData.getId());
//            qcOriginalList.add(qcAnalyseOriginalRecord);
            Optional<DtoQualityControl> qualityControlOptional;
            if ((QualityTaskFactory.getInstance().getQcSample(vo.getQcType()).qcTypeName().equals("替代"))) {
                //添加替代样时，qualityControl不仅要根据关联样品id过滤，还要根据qcSample的qcId过滤
                qualityControlOptional = qualityControls.stream().filter(q -> q.getAssociateSampleId().equals(qcSample.getAssociateSampleId())
                        && qcSample.getQcId().equals(q.getId())).findFirst();
            } else {
                qualityControlOptional = qualityControls.stream().filter(q -> q.getAssociateSampleId().equals(qcSample.getAssociateSampleId())).findFirst();
            }
            String[] qcValueArr = new String[]{""};
            qualityControlOptional.ifPresent(q -> {
                if (QualityTaskFactory.getInstance().getQcSample(vo.getQcType()).qcTypeName().equals("替代")) {
                    qcAnalyzeData.setRedAnalyzeItemName(q.getQcVolume());
                    qcAnalyzeData.setQcId(q.getId());
                    qcAnalyzeData.setDimensionId(q.getQcValueDimensionId());
                    DtoDimension dimension = dimensionMap.get(q.getQcValueDimensionId());
                    qcAnalyzeData.setDimension(StringUtil.isNotNull(dimension) ? dimension.getDimensionName() : "");
                    qcAnalyzeData.setMostDecimal(q.getMostDecimal());
                    qcAnalyzeData.setMostSignificance(q.getMostSignificance());
                }
                qcSample.setQcId(q.getId());
                qcValueArr[0] = q.getQcValue();
            });
            qcAnalyzeData.setWorkSheetFolderId(vo.getWorkSheetFolderId());
            qcAnalyzeData.setSampleId(qcSample.getId());
            qcAnalyzeData.setQcType(vo.getQcType());
            qcAnalyzeData.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            qcAnalyzeData.setStatus("未测");
            qcAnalyzeData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.未测.getValue());
            qcAnalyzeData.setAnalyzeTime(new Date());
            qcAnalyzeData.setDataInputTime(new Date());
            qcAnalyzeDataList.add(qcAnalyzeData);
            //初始化质控评价信息
            evaluateList.add(qualityControlEvaluateService.initQualityControlEvaluate(qcAnalyzeData.getId(), qcAnalyzeData.getTestId(), qcAnalyzeData.getQcType(),
                    qcAnalyzeData.getQcGrade(), qualityControlLimitList, qcValueArr[0], StringUtil.isNotEmpty(qcAnalyzeData.getQcId()) ? qcAnalyzeData.getQcId() : UUIDHelper.GUID_EMPTY));
        }
        if (StringUtil.isNotNull(qcAnalyzeDataList)) {
            analyseDataRepository.save(qcAnalyzeDataList);
        }
        if (StringUtil.isNotEmpty(evaluateList)) {
            qualityControlEvaluateRepository.save(evaluateList);
        }
        if (StringUtil.isNotNull(qcOriginalList)) {
            analyseOriginalRecordRepository.save(qcOriginalList);
        }
    }

    /**
     * 创建质控数据
     *
     * @param vo        数据传输对象
     * @param qcSamples 质控样列表
     * @return 完成创建的质控数据
     */
    private List<DtoQualityControl> initQualityControlOfInnerQcSample(WorkSheetQualityControlBatchOperationVo vo, List<DtoSample> qcSamples) {
        List<DtoQualityControl> result = new ArrayList<>();
        List<DtoQualityControl> qualityControlsParams = vo.getQualityControls();
        for (DtoSample qcSample : qcSamples) {
            for (DtoQualityControl qualityControlsParam : qualityControlsParams) {
                DtoQualityControl qualityControl = new DtoQualityControl();
                BeanUtils.copyProperties(qualityControlsParam, qualityControl, "id");
                qualityControl.setAssociateSampleId(qcSample.getAssociateSampleId());
                qualityControl.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
                qualityControl.setQcType(vo.getQcType());
                qualityControl.setQcTime(new Date());
                result.add(qualityControl);
            }
        }
        return result;
    }

    /**
     * 创建质控数据(仅用于添加替代样)
     *
     * @param vo        数据传输对象
     * @param qcSamples 质控样列表
     * @return 完成创建的质控数据
     */
    private List<DtoQualityControl> initQualityControlForReplace(WorkSheetQualityControlBatchOperationVo vo, List<DtoSample> qcSamples) {
        List<DtoQualityControl> result = new ArrayList<>();
        List<DtoQualityControl> qualityControlsParams = vo.getQualityControls();
        for (DtoSample qcSample : qcSamples) {
            DtoQualityControl qualityControl = new DtoQualityControl();
            DtoQualityControl qualityControlsParam = qualityControlsParams.stream().filter(p -> p.getId().equals(qcSample.getQcId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(qualityControlsParam)) {
                BeanUtils.copyProperties(qualityControlsParam, qualityControl, "id");
            }
            qualityControl.setAssociateSampleId(qcSample.getAssociateSampleId());
            qualityControl.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            qualityControl.setQcType(vo.getQcType());
            qualityControl.setQcTime(new Date());
            //重新设置qcSample的qcId
            qcSample.setQcId(qualityControl.getId());
            result.add(qualityControl);
        }
        return result;
    }

    /**
     * 判断是否显示客户信息
     *
     * @return 是否显示
     */
    private Boolean getIsShowCustomer() {
        Boolean result = true;
        DtoCode isShowFolder = codeService.findByCode("PRO_IS_SHOW_FOLDER");
        if (StringUtil.isNotNull(isShowFolder)) {
            if ("1".equals(isShowFolder.getDictValue())) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 检查系统开关，是否检查领样日期
     *
     * @param analyseDataIds 分析数据ids
     */
    private void checkSampleReceiveDate(List<String> analyseDataIds) {
        DtoCode byCode = codeService.findByCode(ProCodeHelper.IsCheck_SampleReceiveDate);
        if (StringUtil.isNotNull(byCode)) {
            if ("1".equals(byCode.getDictValue())) {
                List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(analyseDataIds) ? analyseDataRepository.findAll(analyseDataIds) : new ArrayList<>();
                Date targetDate = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
                List<DtoAnalyseData> result = analyseDataList.stream().filter(p -> p.getSampleReceiveDate().compareTo(targetDate) == 0).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(result)) {
                    throw new BaseException("领样日期为空");
                }
            }
        }
    }

    /**
     * 过滤按照岗位分配时，其他岗位的测试项目
     *
     * @param finalSameMethodTestIds 最终的测试项目
     * @param analyseDataIds         最终的分析数据
     * @param analyseDataOfSamples   所有分析数据
     * @param sameMethodTestIds      测试项目ids
     * @param dtoWorkSheetCreate     请求参数
     */
    private void filterByPost(List<String> finalSameMethodTestIds,
                              List<String> analyseDataIds,
                              List<DtoAnalyseData> analyseDataOfSamples,
                              List<String> sameMethodTestIds,
                              DtoWorkSheetCreate dtoWorkSheetCreate) {
        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            // 测试项目绑定的所有岗位
            List<DtoTestPost2Test> allTestPost2TestList = testPost2TestRepository.findByTestIdIn(sameMethodTestIds);
            List<String> testPostIds = allTestPost2TestList.stream().map(DtoTestPost2Test::getTestPostId).distinct().collect(Collectors.toList());
            // 根据岗位id 获取所有配置检测人员
            List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByTestPostIdIn(testPostIds);
            // 筛选存在当前检测人员所在的岗位
            List<String> containPostIds = testPost2PersonList.stream()
                    .filter(p -> p.getPersonId().equals(dtoWorkSheetCreate.getAnalystId()))
                    .map(DtoTestPost2Person::getTestPostId)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> containPersonIds = testPost2PersonList.stream().filter(p -> containPostIds.contains(p.getTestPostId())).map(DtoTestPost2Person::getPersonId).distinct().collect(Collectors.toList());
            // 筛选可一检测的可以检测的测试项目
            List<String> containTestIds = allTestPost2TestList.stream().filter(p -> containPostIds.contains(p.getTestPostId())).map(DtoTestPost2Test::getTestId).collect(Collectors.toList());
            sameMethodTestIds.removeIf(p -> !containTestIds.contains(p));
            //获取分析方法相同，分析人员为当前岗位下所有人员, 存在没有默认检测人员的情况
            containPersonIds.add(UUIDHelper.GUID_EMPTY);
            List<DtoAnalyseData> sameMethodAnalyseData = analyseDataOfSamples.parallelStream()
                    .filter(a -> sameMethodTestIds.contains(a.getTestId())
                            && containPersonIds.contains(a.getAnalystId())
                            && a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.未测.getValue())).collect(Collectors.toList());
            analyseDataIds.addAll(sameMethodAnalyseData.parallelStream().map(DtoAnalyseData::getId).collect(Collectors.toList()));
        } else {
            //获取分析方法相同，分析人员相同的分析数据
            List<DtoAnalyseData> sameMethodAnalyseData = analyseDataOfSamples.parallelStream()
                    .filter(a -> sameMethodTestIds.contains(a.getTestId())
                            && a.getAnalystId().equals(dtoWorkSheetCreate.getAnalystId())
                            && a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.未测.getValue())).collect(Collectors.toList());
            analyseDataIds.addAll(sameMethodAnalyseData.parallelStream().map(DtoAnalyseData::getId).collect(Collectors.toList()));
        }
        finalSameMethodTestIds.addAll(sameMethodTestIds);
    }

    /**
     * 工作单默认填充分析项目排序
     *
     * @param dtoWorkSheetFolder 工作单实体
     * @return 工作单
     */
    private DtoWorkSheetFolder setWorkSheetSortDefault(DtoWorkSheetFolder dtoWorkSheetFolder) {
        if (dtoWorkSheetFolder.getWorkStatus().compareTo(EnumPRO.EnumWorkSheetStatus.已经提交.getValue()) < 0) {
            // 获取同方法上一个工作单的分析项目排序
            PageBean<DtoWorkSheetFolder> pb = new PageBean<>();
            pb.setRowsPerPage(1);
            pb.setEntityName("DtoWorkSheetFolder a");
            pb.setSelect("select a");
            String condition = " and a.workStatus >= :workStatus" +
                    " and a.analyzeMethodId = :analyzeMethodId";
            Map<String, Object> values = new HashMap<>();
            values.put("workStatus", EnumPRO.EnumWorkSheetStatus.已经保存.getValue());
            values.put("analyzeMethodId", dtoWorkSheetFolder.getAnalyzeMethodId());
            pb.setCondition(condition);
            pb.setSort("modifyDate-");
            comRepository.findByPage(pb, values);
            List<DtoWorkSheetFolder> workSheetSortIdMapList = pb.getData();
            if (StringUtil.isNotEmpty(workSheetSortIdMapList)) {
                DtoWorkSheetFolder workSheetFolder = workSheetSortIdMapList.get(0);
                if (!workSheetFolder.getSortId().equals(dtoWorkSheetFolder.getSortId())
                        && !workSheetFolder.getId().equals(dtoWorkSheetFolder.getId())
                        && !UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getSortId())) {
                    dtoWorkSheetFolder.setSortId(workSheetFolder.getSortId());
                    dtoWorkSheetFolder = super.save(dtoWorkSheetFolder);
                }
            }
        }
        return dtoWorkSheetFolder;
    }

    protected List<OrderReviseVO> getQualityList() {
        XmlConfig xmlConfig = SpringContextAware.getBean(XmlConfig.class);
        return xmlConfig.getQcRulesConfigVO().getQualityReviseVo().getOrderAndRevise();
    }

    /**
     * 判断原始记录单多份生成参数是否开启
     *
     * @return 多份生成参数值
     */
    @Override
    public Boolean workSheetMultiGenerate() {
        boolean value = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys_pro_workSheet_multiGenerate");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            value = Boolean.parseBoolean(configModel.getConfigValue());
        }
        return value;
    }

    /**
     * 处理工作单退回，审核复核不通过情况下的处理
     *
     * @param folder 工作单
     */
    private void dealBackWorkSheetFolder(DtoWorkSheetFolder folder) {
        folder.setCheckDate(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
        folder.setCheckerId(UUIDHelper.GUID_EMPTY);
        folder.setCheckerName("");
        folder.setAuditDate(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
        folder.setAuditorId(UUIDHelper.GUID_EMPTY);
        folder.setAuditorName("");
    }

    /**
     * 创建嗅辨任务
     *
     * @param testList         测试项目集合
     * @param workSheetFolders 检测单集合
     * @param workSheets       子检测单集合
     */
    private void createOdTask(List<DtoTest> testList, List<DtoWorkSheetFolder> workSheetFolders, List<DtoWorkSheet> workSheets) {
        List<DtoTest> tests = testList.stream().filter(DtoTest::getIsOd).collect(Collectors.toList());
        // 是否嗅辨开关开启的测试项目
        if (StringUtil.isNotEmpty(tests)) {
            List<String> testIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
            // 过滤子检测单
            workSheets = workSheets.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
            List<String> workSheetFolderIds = workSheets.stream().map(DtoWorkSheet::getParentId).collect(Collectors.toList());
            // 检测单
            workSheetFolders = workSheetFolders.stream().filter(p -> workSheetFolderIds.contains(p.getId())).collect(Collectors.toList());

            // 数据审核后，发布保存关联数据事件
            Map<String, Object> map = new HashMap<>();
            map.put("testIds", testIds);
            map.put("workSheetFolderList", workSheetFolders);
            // 发布创建嗅辨任务监听器
            SpringContextAware.getApplicationContext().publishEvent(
                    new LIMSEvent<>(map, EnumLIM.EnumOdsEvent.CREATE_TASK.name(), EnumLIM.EnumOdsEvent.ADD.name()));
        }
    }

    @Override
    @Transactional
    public void importPretreatment(MultipartFile file, String workSheetFolderId, HttpServletResponse response) {

        DtoImportWorkSheetHeaderVo workSheetHeaderVo = importTemplateBased(file);
        DtoWorkSheetFolder workSheetFolder = repository.findOne(workSheetFolderId);
        if (!workSheetFolder.getWorkSheetCode().equals(workSheetHeaderVo.getWorkSheetCode())) {
            throw new BaseException("导入数据工作单号与当前工作单不匹配");
        }
        List<DtoImportWorkSheetSampleVo> sampleVoList = workSheetHeaderVo.getSampleVoList();
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderId(workSheetFolderId);

        List<String> analyseDataIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toList());
        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        List<DtoAnalyseOriginalRecord> analyseOriginalRecords = StringUtil.isNotEmpty(analyseDataIds) ?
                analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIds) : new ArrayList<>();
        Map<String, DtoAnalyseOriginalRecord> analyseOriginalRecordMap = analyseOriginalRecords.stream().collect(Collectors.toMap(DtoAnalyseOriginalRecord::getAnalyseDataId, p -> p, (p1, p2) -> p1));

        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        List<String> sampleCodeList = sampleList.stream().map(DtoSample::getCode).collect(Collectors.toList());
        // 剔除无效数据
        sampleVoList.removeIf(p -> !sampleCodeList.contains(p.getSampleCode()));

        if (StringUtil.isNotEmpty(sampleVoList)) {
            TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
            };
            List<DtoAnalyseOriginalRecord> updateRecordList = new ArrayList<>();
            for (DtoSample sample : sampleList) {
                Optional<DtoImportWorkSheetSampleVo> voOptional = sampleVoList.stream().filter(p -> p.getSampleCode().equals(sample.getCode())).findFirst();
                if (voOptional.isPresent()) {
                    DtoImportWorkSheetSampleVo workSheetSampleVo = voOptional.get();
                    Map<String, Object> dataMap = workSheetSampleVo.getDataMap();
                    List<DtoAnalyseData> dtoAnalyseDataList = analyseDataMap.getOrDefault(sample.getId(), new ArrayList<>());
                    for (DtoAnalyseData analyseData : dtoAnalyseDataList) {
                        DtoAnalyseOriginalRecord analyseOriginalRecord = analyseOriginalRecordMap.get(analyseData.getId());
                        if (null != analyseOriginalRecord) {
                            List<DtoParamsTestFormula> paramsTestFormula = JsonIterator.deserialize(analyseOriginalRecord.getJson(), typeLiteral);
                            // 公式参数中匹配相同名称数据
                            for (DtoParamsTestFormula params : paramsTestFormula) {
                                String alias = params.getAlias();
                                if (dataMap.containsKey(alias) && StringUtil.isNotNull(dataMap.get(alias))) {
                                    params.setDefaultValue(dataMap.get(alias).toString());
                                }
                            }
                            String json = JsonStream.serialize(paramsTestFormula);
                            analyseOriginalRecord.setJson(json);
                            updateRecordList.add(analyseOriginalRecord);
                        }
                    }
                }
            }
            // 更新参数
            if (StringUtil.isNotEmpty(updateRecordList)) {
                analyseOriginalRecordRepository.save(updateRecordList);
            }
        }
    }

    /**
     * 获取导入数据基本信息
     *
     * @param file
     * @return
     */
    private DtoImportWorkSheetHeaderVo importTemplateBased(MultipartFile file) {
        PoiExcelUtils.verifyFileType(file);
        DtoImportWorkSheetHeaderVo workSheetHeaderVo = new DtoImportWorkSheetHeaderVo();
        try {
            workSheetHeaderVo = parseHeader(file);
            // 2. 解析数据行
            ImportParams dataParams = new ImportParams();
            //设置标题区域
            dataParams.setTitleRows(2);
            //设置表头开始行
            dataParams.setHeadRows(1);

            List<DtoImportWorkSheetSampleVo> dataList =
                    ExcelImportUtil.importExcel(
                            file.getInputStream(),
                            DtoImportWorkSheetSampleVo.class,
                            dataParams
                    );
            //删除空行
            dataList.removeIf(p -> StringUtil.isEmpty(p.getSampleCode()));
            //判断文件中是否存在数据
            if (StringUtil.isEmpty(dataList)) {
                throw new BaseException("文件中无数据，请检查后导入");
            }
            dataList.forEach(DtoImportWorkSheetSampleVo::fillMap);
            workSheetHeaderVo.setSampleVoList(StringUtil.isNotEmpty(dataList) ? dataList : new ArrayList<>());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return workSheetHeaderVo;
    }

    /**
     * 解析头部信息
     *
     * @param file 文件
     * @return 头部信息
     * @throws Exception
     */
    private DtoImportWorkSheetHeaderVo parseHeader(MultipartFile file) throws Exception {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        // 创建表头对象
        DtoImportWorkSheetHeaderVo header = new DtoImportWorkSheetHeaderVo();

        // 解析第一行 (检测单号行)
        header.setWorkSheetCode(ImportUtils.getRegionValue(sheet, 0, 1));

        // 解析第二行 (分析方法行)
        header.setAnalyzeMethodName(ImportUtils.getRegionValue(sheet, 1, 1));

        workbook.close();
        return header;
    }

    @Autowired
    public void setParams2ParamsFormulaRepository(Params2ParamsFormulaRepository params2ParamsFormulaRepository) {
        this.params2ParamsFormulaRepository = params2ParamsFormulaRepository;
    }

    @Autowired
    public void setEvaluationRecordRepository(EvaluationRecordRepository evaluationRecordRepository) {
        this.evaluationRecordRepository = evaluationRecordRepository;
    }

    @Autowired
    public void setEvaluationCriteriaRepository(EvaluationCriteriaRepository evaluationCriteriaRepository) {
        this.evaluationCriteriaRepository = evaluationCriteriaRepository;
    }

    @Autowired
    public void setEvaluationLevelRepository(EvaluationLevelRepository evaluationLevelRepository) {
        this.evaluationLevelRepository = evaluationLevelRepository;
    }

    @Autowired
    public void setEnvironmentalRecordRepository(EnvironmentalRecordRepository environmentalRecordRepository) {
        this.environmentalRecordRepository = environmentalRecordRepository;
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setInstrumentUseRecordRepository(InstrumentUseRecordRepository instrumentUseRecordRepository) {
        this.instrumentUseRecordRepository = instrumentUseRecordRepository;
    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    @Lazy
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    @Lazy
    public void setQualityControlEvaluateService(QualityControlEvaluateService qualityControlEvaluateService) {
        this.qualityControlEvaluateService = qualityControlEvaluateService;
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Autowired
    public void setPerson2TestRepository(Person2TestRepository person2TestRepository) {
        this.person2TestRepository = person2TestRepository;
    }

    @Autowired
    public void setProjectPlanRepository(ProjectPlanRepository projectPlanRepository) {
        this.projectPlanRepository = projectPlanRepository;
    }

    @Autowired
    @Lazy
    public void setTestExpandService(TestExpandService testExpandService) {
        this.testExpandService = testExpandService;
    }

    @Autowired
    @Lazy
    public void setReportDetailService(ReportDetailService reportDetailService) {
        this.reportDetailService = reportDetailService;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setTestPost2TestRepository(TestPost2TestRepository testPost2TestRepository) {
        this.testPost2TestRepository = testPost2TestRepository;
    }

    @Autowired
    public void setTestPost2PersonRepository(TestPost2PersonRepository testPost2PersonRepository) {
        this.testPost2PersonRepository = testPost2PersonRepository;
    }

    @Autowired
    @Lazy
    public void setSampleOrderService(SampleOrderService sampleOrderService) {
        this.sampleOrderService = sampleOrderService;
    }

    @Autowired
    @Lazy
    public void setConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    @Lazy
    public void setMarkersDataService(MarkersDataService markersDataService) {
        this.markersDataService = markersDataService;
    }

    @Autowired
    @Lazy
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }
}