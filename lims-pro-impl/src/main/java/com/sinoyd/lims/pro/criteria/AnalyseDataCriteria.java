package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * AnalyseData查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyseDataCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品id集合
     */
    private List<String> sampleIds;

    /**
     * 是否现场
     */
    private Boolean isCompleteField;

    private String workSheetFolderId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and isDeleted = 0 ");
        if (StringUtil.isNotEmpty(this.sampleId)) {
            condition.append(" and sampleId = :sampleId");
            values.put("sampleId", this.sampleId);
        }
        if (StringUtil.isNotEmpty(this.sampleIds)) {
            condition.append(" and sampleId in :sampleIds");
            values.put("sampleIds", this.sampleIds);
        }
        //是否现场
        if (StringUtil.isNotNull(this.isCompleteField)) {
            condition.append(" and isCompleteField = :isCompleteField ");
            values.put("isCompleteField", this.isCompleteField);
        }
        if (StringUtil.isNotEmpty(this.workSheetFolderId)) {
            condition.append(" and workSheetFolderId = :workSheetFolderId");
            values.put("workSheetFolderId", this.workSheetFolderId);
        }
        return condition.toString();
    }
}