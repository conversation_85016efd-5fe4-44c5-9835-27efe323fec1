package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 环境质量统计数据导出查询条件对象
 * <AUTHOR>
 * @version V1.0.0 2021/8/18
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnvironmentStatisticsExportCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ids
     */
    private List<String> projectIds;

    /***
     * 是否检出
     */
    private Boolean enableData;

    /**
     * 是否标红
     */
    private Boolean overRed;

    /**
     * 是否按计划过滤
     */
    private Boolean isPlan;

    /**
     * 分析项目排序
     */
    private String itemSortId;

    /**
     * 点位排序
     */
    private String pointSortId;

    /**
     * 显示类型
     */
    private List<String> evaluationType;

    /**
     * 监测计划Id集合
     */
    private List<String> propertyIds;

    /**
     * 是否按照分析项目排序导出
     */
    Boolean isSortExport;

    @Override
    public String getCondition() {
        if (StringUtil.isNotNull(this.projectIds)) {
            values.put("projectIds", this.projectIds);
        }
        if (StringUtil.isNotNull(this.enableData)) {
            values.put("enableData",this.enableData);
        }
        if (StringUtil.isNotNull(overRed)) {
            values.put("overRed", this.overRed);
        }
        if (StringUtil.isNotEmpty(this.itemSortId)) {
            values.put("itemSortId", this.itemSortId);
        }
        if (StringUtil.isNotEmpty(this.pointSortId)) {
            values.put("pointSortId",this.pointSortId);
        }
        if (StringUtil.isNotEmpty(evaluationType)) {
            values.put("evaluationType", this.evaluationType);
        }
        if (StringUtil.isNotEmpty(this.propertyIds)){
            values.put("propertyIds", this.propertyIds);
        }
        if (StringUtil.isNotNull(isSortExport)) {
            values.put("isSortExport", this.isSortExport);
        }
        return null;
    }
}
