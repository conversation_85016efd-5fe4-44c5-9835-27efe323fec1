package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.RPSamplingMethodVO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 上海监管平台采样方法策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/09
 */
@Component
public class RPSamplingMethod extends AbsRegulatoryPlatformRemote<RPSamplingMethodVO> {

    @Override
    protected void filterCriteria(List<RPSamplingMethodVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //分析方法模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getKey())) {
            list.removeIf(m -> !m.getMethodName().contains(methodCriteria.getKey()));
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样方法.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样方法.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样方法.getDeleteMethod();
    }
}
