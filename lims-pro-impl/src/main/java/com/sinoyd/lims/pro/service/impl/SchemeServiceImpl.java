package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.service.ISampleCodeService;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.CompareJudgeRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.repository.rcc.SerialNumberConfigRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 方案操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/06/11
 * @since V100R001
 */
@Service
@Slf4j
public class SchemeServiceImpl implements SchemeService {

    //#region 注入

    private static final Pattern PATTERN = Pattern.compile("\\d+$");

    @Autowired
    private CommonRepository comRepository;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    private SamplingFrequencyRepository samplingFrequencyRepository;

    @Autowired
    @Lazy
    private SamplingFrequencyService samplingFrequencyService;

    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private QualityManageRepository qualityManageRepository;

    @Autowired
    private QualityControlRepository qualityControlRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    @Autowired
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    @Lazy
    private SubmitRecordService submitRecordService;

    @Autowired
    @Lazy
    private StatusForProjectService statusForProjectService;

    @Autowired
    private QualityControlEvaluateRepository qualityControlEvaluateRepository;

    @Autowired
    private EvaluationRecordRepository evaluationRecordRepository;

    @Autowired
    private SerialNumberConfigRepository serialNumberConfigRepository;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    @Autowired
    private SampleJudgeDataRepository sampleJudgeDataRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private CompareJudgeRepository compareJudgeRepository;

    @Autowired
    private QualityControlLimitRepository qualityControlLimitRepository;

    @Autowired
    @Lazy
    private QCProjectService qcProjectService;

    @Autowired
    @Lazy
    private BusinessSerialNumberService businessSerialNumberService;

    @Autowired
    private ISampleCodeService sampleCodeService;

    //#endregion

    //#region 送样类

    /**
     * 复制外部送样单下的样品数据
     *
     * @param sourceProjectId 源项目id
     * @param sourceReceiveId 源送样单id
     * @param targetRecord    目标送样单
     * @param targetProject   目标项目
     */
    @Override
    @Transactional
    public void copyOutsideProjectSample(String sourceProjectId, String sourceReceiveId, DtoReceiveSampleRecord targetRecord, DtoProject targetProject) {
        //最终需要插入的数据载体
        DtoLoadOutSample target = new DtoLoadOutSample();
        //来源数据载体
        DtoLoadOutSample source = this.findLoadOutSample(sourceProjectId, sourceReceiveId);
        DtoProjectType dtoProjectType = projectTypeService.findOne(targetProject.getProjectTypeId());

        //复制点位数据
        Map<String, String> oldFolderId2NewIdMap = new HashMap<>();
        for (DtoSampleFolder sf : source.getSampleFolder()) {
            DtoSampleFolder targetSampleFolder = new DtoSampleFolder();
            BeanUtils.copyProperties(sf, targetSampleFolder);
            targetSampleFolder.setProjectId(targetProject.getId());
            targetSampleFolder.setId(UUIDHelper.NewID());

            source.putNewSampleFolderId(sf.getId(), targetSampleFolder.getId());
            target.addSampleFolder(targetSampleFolder);
            oldFolderId2NewIdMap.put(sf.getId(), targetSampleFolder.getId());
        }

        //复制评价记录(按点位id存储)
        List<String> oldFolderIdList = source.getSampleFolder().stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(oldFolderIdList)) {
            copyEvaluationRecord(oldFolderIdList, EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue(), oldFolderId2NewIdMap);
        }

        for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
            DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf, source.getNewSampleFolderId(sf.getSampleFolderId()));

            source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId());
            target.addSamplingFrequency(targetSamplingFrequency);
        }
        for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
            DtoSamplingFrequencyTest targetSamplingFrequencyTest = new DtoSamplingFrequencyTest();
            BeanUtils.copyProperties(sft, targetSamplingFrequencyTest);
            targetSamplingFrequencyTest.setSampleFolderId(source.getNewSampleFolderId(sft.getSampleFolderId()));
            targetSamplingFrequencyTest.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId()));
            targetSamplingFrequencyTest.setId(UUIDHelper.NewID());
            target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
        }

        //复制样品数据
        List<DtoSample> sourceSampleList = source.getSample();
        List<String> sampleTypeIds = sourceSampleList.parallelStream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        List<String> parentSampleTypeIds = sampleTypes.parallelStream().map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
        List<DtoSampleType> parentSampleTypes = sampleTypeService.findRedisByIds(parentSampleTypeIds);

        //需要新增的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //需要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();

        Map<String, String> oldSampleId2NewId = new HashMap<>();
        for (DtoSample sam : source.getSample()) {
            DtoSample targetSample = new DtoSample();
            BeanUtils.copyProperties(sam, targetSample);
            targetSample.setSampleFolderId(source.getNewSampleFolderId(sam.getSampleFolderId()));
            targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId()));
            targetSample.setProjectId(targetProject.getId());
            targetSample.setReceiveId(targetRecord.getId());
            targetSample.setId(UUIDHelper.NewID());
            targetSample.setInceptTime(new Date());
            targetSample.setSamplingTimeBegin(targetRecord.getSamplingTime());
            targetSample.setSamplingTimeEnd(targetRecord.getSamplingTime());
            targetSample.setDataChangeStatus(EnumPRO.EnumSampleChangeStatus.未变更.getValue());
            Integer type = source.getSampleTestType(sam.getId());
            if (type.equals(EnumPRO.EnumSampleTestType.无指标.getValue())) {
                targetSample.setStatus(EnumPRO.EnumSampleStatus.样品检毕.name());
                targetSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                targetSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
                targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.分析完成.getValue());
            } else if (type.equals(EnumPRO.EnumSampleTestType.实验室指标.getValue())) {
                targetSample.setStatus(EnumPRO.EnumSampleStatus.样品未领样.name());
                targetSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                targetSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
                targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
            } else if (type.equals(EnumPRO.EnumSampleTestType.现场指标.getValue())) {
                targetSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.name());
                targetSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                targetSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
                targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
            } else if (type.equals(EnumPRO.EnumSampleTestType.全指标.getValue())) {
                targetSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.name());
                targetSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                targetSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
            }

            targetSample.setCreateDate(new Date());
            targetSample.setCreator(PrincipalContextUser.getPrincipal().getUserId());
            targetSample.setModifyDate(new Date());
            targetSample.setModifier(PrincipalContextUser.getPrincipal().getUserId());

            String sampleTypeId = targetSample.getSampleTypeId();
            DtoSampleType dtoSampleType = sampleTypes.parallelStream().filter(p -> sampleTypeId.equals(p.getId())).findFirst().orElse(null);
            DtoSampleType parentSampleType = null;
            if (StringUtil.isNotNull(dtoSampleType)) {
                parentSampleType = parentSampleTypes.parallelStream().filter(p -> StringUtils.isNotNullAndEmpty(dtoSampleType.getParentId())
                        && dtoSampleType.getParentId().equals(p.getId())).findFirst().orElse(null);
            }
            setSampleCode(targetProject, dtoProjectType, dtoSampleType, parentSampleType, targetSample, serialNumberConfigCreateList, serialNumberConfigUpdateList, source, target, sam);
            oldSampleId2NewId.put(sam.getId(), targetSample.getId());
        }

        //复制领样单数据
        for (DtoReceiveSubSampleRecord sub : source.getReceiveSubSampleRecord()) {
            DtoReceiveSubSampleRecord targetReceiveSubSampleRecord = new DtoReceiveSubSampleRecord();
            BeanUtils.copyProperties(sub, targetReceiveSubSampleRecord);
            targetReceiveSubSampleRecord.setProjectId(targetProject.getId());
            targetReceiveSubSampleRecord.setReceiveId(targetRecord.getId());
            if ((sub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0) {
                targetReceiveSubSampleRecord.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue());
                targetReceiveSubSampleRecord.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.测试中.toString());
                targetReceiveSubSampleRecord.setCode(targetRecord.getRecordCode() + EnumPRO.EnumSubRecordType.现场.getValue());
            } else {
                targetReceiveSubSampleRecord.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue());
                targetReceiveSubSampleRecord.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.未领取.toString());
                targetReceiveSubSampleRecord.setCode(targetRecord.getRecordCode() + EnumPRO.EnumSubRecordType.分析.getValue());
            }
            targetReceiveSubSampleRecord.setReceivePersonId(UUIDHelper.GUID_EMPTY);
            targetReceiveSubSampleRecord.setReceiveName("");
            targetReceiveSubSampleRecord.setReceiveTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
            targetReceiveSubSampleRecord.setAuditorId(UUIDHelper.GUID_EMPTY);
            targetReceiveSubSampleRecord.setAuditorName("");
            targetReceiveSubSampleRecord.setAuditTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
            targetReceiveSubSampleRecord.setCheckerId(UUIDHelper.GUID_EMPTY);
            targetReceiveSubSampleRecord.setCheckerName("");
            targetReceiveSubSampleRecord.setCheckTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
            targetReceiveSubSampleRecord.setId(UUIDHelper.NewID());
            source.putNewReceiveSubSampleRecordId(sub.getId(), targetReceiveSubSampleRecord.getId());
            target.addReceiveSubSampleRecord(targetReceiveSubSampleRecord);
        }
        for (DtoReceiveSubSampleRecord2Sample r2s : source.getReceiveSubSampleRecord2Sample()) {
            String newSampleId = source.getNewSampleId(r2s.getSampleId());
            if (StringUtils.isNotNullAndEmpty(newSampleId) && !newSampleId.equals(UUIDHelper.GUID_EMPTY)) {
                DtoReceiveSubSampleRecord2Sample targetReceiveSubSampleRecord2Sample = new DtoReceiveSubSampleRecord2Sample();
                targetReceiveSubSampleRecord2Sample.setId(UUIDHelper.NewID());
                targetReceiveSubSampleRecord2Sample.setSampleId(newSampleId);
                targetReceiveSubSampleRecord2Sample.setReceiveSubSampleRecordId(source.getNewReceiveSubSampleRecordId(r2s.getReceiveSubSampleRecordId()));
                target.addReceiveSubSampleRecord2Sample(targetReceiveSubSampleRecord2Sample);
            }
        }

        //复制分析数据
        Map<String, String> oldAnaId2NewIdMap = new HashMap<>();
        for (DtoAnalyseData analyseData : source.getAnalyseData()) {
            String newSampleId = source.getNewSampleId(analyseData.getSampleId());
            if (StringUtils.isNotNullAndEmpty(newSampleId) && !newSampleId.equals(UUIDHelper.GUID_EMPTY)) {
                DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                targetAnalyseData.setSampleId(newSampleId);
                target.addAnalyseData(targetAnalyseData);
                oldAnaId2NewIdMap.put(analyseData.getId(), targetAnalyseData.getId());

                DtoQualityManage qm = source.getQualityManage().stream().filter(p -> p.getAnaId().equals(analyseData.getId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(qm)) {
                    DtoQualityManage targetQualityManage = new DtoQualityManage();
                    BeanUtils.copyProperties(qm, targetQualityManage);
                    targetQualityManage.setId(UUIDHelper.NewID());
                    targetQualityManage.setAnaId(targetAnalyseData.getId());
                    target.addQualityManage(targetQualityManage);
                }
            }
        }
        //复制评价记录(按分析数据id存储)
        List<String> oldAnaIdList = source.getAnalyseData().stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(oldAnaIdList)) {
            copyEvaluationRecord(oldAnaIdList, EnumPRO.EnumEvaluationType.分析数据.getValue(), EnumPRO.EnumEvaluationPlan.分析数据.getValue(), oldAnaId2NewIdMap);
        }

        //复制比对数据
        copySampleJudgeData(oldSampleId2NewId, target.getAnalyseData());
        //生成比对质控样
        generateQCSampleJudageData(target.getSample(), target.getAnalyseData(), targetProject);

        //复制参数数据
        for (DtoParamsData paramsData : source.getParamsData()) {
            String newSampleId = source.getNewSampleId(paramsData.getObjectId());
            if (StringUtils.isNotNullAndEmpty(newSampleId) && !newSampleId.equals(UUIDHelper.GUID_EMPTY)) {
                DtoParamsData targetParamsData = new DtoParamsData();
                BeanUtils.copyProperties(paramsData, targetParamsData);
                targetParamsData.setId(UUIDHelper.NewID());
                targetParamsData.setObjectId(newSampleId);
                target.addParamsData(targetParamsData);
            }
        }

        if (serialNumberConfigCreateList.size() > 0) {
            serialNumberConfigRepository.batchInsert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            serialNumberConfigRepository.batchUpdate(serialNumberConfigUpdateList);
        }
        //插入数据
        this.persistLoadOutSample(target);
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        if (target.getAnalyseData().size() > 0) {
                            proService.sendProMessage(EnumPRO.EnumProAction.添加测试项目, "", "", target.getAnalyseData().stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()));
                        }
                    }
                }
        );
    }

    /**
     * 复制外部送样样品
     *
     * @param ids          样品id集合
     * @param receiveId    送样单id
     * @param copyTimes    复制次数
     * @param samplingDate 采样日期
     */
    @Transactional
    @Override
    public void copyOutsideSample(List<String> ids, String receiveId, Integer copyTimes, Date samplingDate) {
        List<DtoSample> samples = sampleRepository.findAll(ids);
        //质控样不进行复制
        if (samples.stream().anyMatch(p -> !p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue()))) {
            throw new BaseException("所选样品包含质控样，请重新选择");
        }
        //作废样品不进行复制
        if (samples.stream().anyMatch(p -> p.getStatus().equals(EnumPRO.EnumSampleStatus.样品作废.toString()))) {
            throw new BaseException("所选样品包含作废样品，请重新选择");
        }
        //最终需要插入的数据载体
        DtoLoadOutSample target = new DtoLoadOutSample();
        //样品单独保存
        List<DtoSample> targetSamples = new ArrayList<>();
        //需要新增的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //需要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();

        //读取送样单、项目信息
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(receiveId);
        DtoProject project = projectRepository.findOne(record.getProjectId());
        DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
        //读取项目登记页，判断为质控送样还是外部/现场送样
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");

        //读取对应的复制样品相关信息
        samples.sort(Comparator.comparing(DtoSample::getCode));
        DtoLoadOutSample source = this.findLoadOutSample(samples, receiveId);
        //读取实验室领样单
        DtoReceiveSubSampleRecord anaSub = source.getReceiveSubSampleRecord().stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0).findFirst().orElse(null);

        //作为状态纠正的模板样品id集合，该部分样品不进行样品输入、数据插入和领样单关联插入
        List<String> tempSampleIds = new ArrayList<>();

        //先进行点位数据的复制，该部分数据状态纠正不会用到，于最后统一插入
        this.loadSampleFolder(source, copyTimes, projectTypeCode, target);
        //获取样品及指标类型并按类型进行分组
        List<DtoSampleTestType> sampleTestTypes = receiveSubSampleRecordService.findSampleTestType(ids);
        Map<Integer, List<DtoSampleTestType>> groupSampleTestType = sampleTestTypes.stream().collect(Collectors.groupingBy(DtoSampleTestType::getType));
        //按分组进行遍历

        //获取比对数据
        List<DtoSampleJudgeData> sampleJudgeDataList = StringUtil.isNotEmpty(ids) ? sampleJudgeDataRepository.findBySampleIdIn(ids) : new ArrayList<>();

        //模板的分析数据
        List<DtoAnalyseData> thisAnalyseDataList = new ArrayList<>();

        //模板的样品数据
        List<DtoSample> targetTempSamples = new ArrayList<>();

        //插入的比对数据
        List<DtoSampleJudgeData> saveJudgeDataList = new ArrayList<>();

        //复制并插入该样本的领样单关联
        List<DtoReceiveSubSampleRecord2Sample> thisReceiveSubSampleRecord2SampleList = new ArrayList<>();

        //业务编号记录
        List<DtoBusinessSerialNumber> businessSerialNumberList = new ArrayList<>();

        for (Integer type : groupSampleTestType.keySet()) {
            //提取该分组下的样品
            List<String> groupSampleIds = groupSampleTestType.get(type).stream().map(DtoSampleTestType::getSampleId).collect(Collectors.toList());
            List<DtoSample> groupSamples = source.getSample().stream().filter(p -> groupSampleIds.contains(p.getId())).collect(Collectors.toList());
            //将该组的第一个样品当成模板
            DtoSample temp = groupSamples.get(0);
            //将样品id塞入模板中
            tempSampleIds.add(temp.getId());

            DtoSample targetTemp = this.getTempSample(temp, record, anaSub, type);
            targetTemp.setSamplingFrequencyId(source.getNewSamplingFrequencyId(temp.getSamplingFrequencyId(), "1"));
            if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {//质控类的为新复制出的点位id
                targetTemp.setSampleFolderId(source.getNewSampleFolderId(temp.getSampleFolderId(), "1"));
            } else {//其余类的需修正点位名称
                DtoSampleFolder sourceFolder = source.getSampleFolder().stream().filter(p -> p.getId().equals(temp.getSampleFolderId())).findFirst().orElse(null);
                String folderName = StringUtil.isNotNull(sourceFolder) ? sourceFolder.getWatchSpot()
                        : temp.getRedFolderName().replace(String.format("(%d-%d-%d)", temp.getCycleOrder(),
                        temp.getTimesOrder(), temp.getSampleOrder()), "");
                Integer timePerPeriod = target.getSamplingFrequency().stream().filter(p -> p.getId().equals(targetTemp.getSamplingFrequencyId()))
                        .map(DtoSamplingFrequency::getTimePerPeriod).findFirst().orElse(1);
                Integer samplePerTime = target.getSamplingFrequency().stream().filter(p -> p.getId().equals(targetTemp.getSamplingFrequencyId()))
                        .map(DtoSamplingFrequency::getSamplePerTime).findFirst().orElse(1);
                targetTemp.setRedFolderName(folderName + String.format("(%d-%d-%d)", temp.getCycleOrder(),
                        timePerPeriod, samplePerTime));
                targetTemp.setTimesOrder(timePerPeriod);
                targetTemp.setSampleOrder(samplePerTime);
            }
            Date samplingTimeBegin = targetTemp.getSamplingTimeBegin();
            if (StringUtil.isNotNull(samplingDate)) {
                samplingTimeBegin = samplingDate;
                targetTemp.setSamplingTimeBegin(samplingDate);
            }
            if (EnumPRO.EnumSampleBlindType.密码平行.getValue().equals(targetTemp.getBlindType())
                    || EnumPRO.EnumSampleBlindType.密码加标.getValue().equals(targetTemp.getBlindType())) {
                DtoSample originSample = sampleRepository.findOne(targetTemp.getParentSampleId());
                samplingTimeBegin = originSample.getSamplingTimeBegin();
            }
            source.putNewSampleId(temp.getId(), targetTemp.getId(), "1");
            //需要验证样品编号是否存在，存在才能保存相应的数据
            Integer sampleCount = setSampleCode(project, projectType, targetTemp, samplingTimeBegin, false, serialNumberConfigCreateList, serialNumberConfigUpdateList, businessSerialNumberList);
            if (sampleCount == 0) {
                targetTempSamples.add(targetTemp);
                //复制并插入该样本的分析数据
                for (DtoAnalyseData analyseData : source.getAnalyseData().stream().filter(p -> p.getSampleId().equals(temp.getId())).collect(Collectors.toList())) {
                    this.loadAnalyseData(source, analyseData, targetTemp.getId(), record, thisAnalyseDataList, target.getQualityManage());
                    this.loadSampleJudgeData(analyseData, sampleJudgeDataList, saveJudgeDataList, targetTemp.getId());
                }

                for (DtoReceiveSubSampleRecord2Sample r2s : source.getReceiveSubSampleRecord2Sample().stream().filter(p -> p.getSampleId().equals(temp.getId())).collect(Collectors.toList())) {
                    DtoReceiveSubSampleRecord2Sample targetReceiveSubSampleRecord2Sample = new DtoReceiveSubSampleRecord2Sample();
                    targetReceiveSubSampleRecord2Sample.setSampleId(targetTemp.getId());
                    targetReceiveSubSampleRecord2Sample.setReceiveSubSampleRecordId(r2s.getReceiveSubSampleRecordId());
                    thisReceiveSubSampleRecord2SampleList.add(targetReceiveSubSampleRecord2Sample);
                }
            }
            //复制样品数据，因为需要保证样品编号的连贯性，故单独拆出来
            for (DtoSample sam : groupSamples) {
                DtoSampleFolder sourceFolder = source.getSampleFolder().stream().filter(p -> p.getId().equals(sam.getSampleFolderId())).findFirst().orElse(null);
                String folderName = StringUtil.isNotNull(sourceFolder) ? sourceFolder.getWatchSpot()
                        : sam.getRedFolderName().replace(String.format("(%d-%d-%d)", sam.getCycleOrder(),
                        sam.getTimesOrder(), sam.getSampleOrder()), "");
                for (Integer i = 1; i <= copyTimes; i++) {
                    //等同于第一次的模板样品
                    if (tempSampleIds.contains(sam.getId()) && i.equals(1)) {
                        continue;
                    }
                    DtoSample targetSample = new DtoSample();
                    BeanUtils.copyProperties(sam, targetSample, "id");
                    Date samplingTime = targetSample.getSamplingTimeBegin();
                    if (StringUtil.isNotNull(samplingDate)) {
                        samplingTime = samplingDate;
                        targetSample.setSamplingTimeBegin(samplingDate);
                    }
                    if (EnumPRO.EnumSampleBlindType.密码平行.getValue().equals(sam.getBlindType()) || EnumPRO.EnumSampleBlindType.密码加标.getValue().equals(sam.getBlindType())) {
                        DtoSample originSample = sampleRepository.findOne(sam.getParentSampleId());
                        samplingTime = originSample.getSamplingTimeBegin();
                    }
                    //需要验证样品编号是否存在，存在才能保存相应的数据
                    Integer count = setSampleCode(project, projectType, targetSample, samplingTime, true, serialNumberConfigCreateList, serialNumberConfigUpdateList, businessSerialNumberList);
                    if (count == 0) {
//                        targetSample.setId(UUIDHelper.NewID());
                        targetSample.setInceptTime(new Date());
                        targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(), String.valueOf(i)));
                        if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {//质控类的为新复制出的点位id
                            targetSample.setSampleFolderId(source.getNewSampleFolderId(sam.getSampleFolderId(), String.valueOf(i)));
                        } else {//其余类的需修正点位名称
                            Integer timePerPeriod = target.getSamplingFrequency().stream().filter(p -> p.getId()
                                            .equals(targetSample.getSamplingFrequencyId()))
                                    .map(DtoSamplingFrequency::getTimePerPeriod).findFirst().orElse(1);
                            targetSample.setRedFolderName(folderName + String.format("(%d-%d-%d)",
                                    targetSample.getCycleOrder(), timePerPeriod, targetSample.getSampleOrder()));
                            targetSample.setTimesOrder(timePerPeriod);
                            targetSample.setSampleOrder(1);
                        }
//                        targetSample.setSamplingTimeBegin(record.getSamplingTime());
                        targetSample.setSamplingTimeEnd(record.getSamplingTime());
                        targetSample.setDataChangeStatus(EnumPRO.EnumSampleChangeStatus.未变更.getValue());

                        //将状态信息改为纠正回来的模板状态
                        targetSample.setStatus(targetTemp.getStatus());
                        targetSample.setSamplingStatus(targetTemp.getSamplingStatus());
                        targetSample.setInnerReceiveStatus(targetTemp.getInnerReceiveStatus());
                        targetSample.setAnanlyzeStatus(targetTemp.getAnanlyzeStatus());

                        //biz信息修改
                        targetSample.setCreateDate(new Date());
                        targetSample.setCreator(PrincipalContextUser.getPrincipal().getUserId());
                        targetSample.setModifyDate(new Date());
                        targetSample.setModifier(PrincipalContextUser.getPrincipal().getUserId());

                        source.putNewSampleId(sam.getId(), targetSample.getId(), String.valueOf(i));

                        targetSamples.add(targetSample);
                    }
                }
            }
        }
        //进行其余数据的批量复制
        for (Integer i = 1; i <= copyTimes; i++) {
            //复制领样单关联数据
            for (DtoReceiveSubSampleRecord2Sample r2s : source.getReceiveSubSampleRecord2Sample()) {
                //等同于第一次的模板样品
                if (tempSampleIds.contains(r2s.getSampleId()) && i.equals(1)) {
                    continue;
                }
                String newSampleId = source.getNewSampleId(r2s.getSampleId(), String.valueOf(i));
                if (StringUtils.isNotNullAndEmpty(newSampleId) && !newSampleId.equals(UUIDHelper.GUID_EMPTY)) {
                    DtoReceiveSubSampleRecord2Sample targetReceiveSubSampleRecord2Sample = new DtoReceiveSubSampleRecord2Sample();
                    targetReceiveSubSampleRecord2Sample.setId(UUIDHelper.NewID());
                    targetReceiveSubSampleRecord2Sample.setSampleId(newSampleId);
                    targetReceiveSubSampleRecord2Sample.setReceiveSubSampleRecordId(r2s.getReceiveSubSampleRecordId());
                    target.addReceiveSubSampleRecord2Sample(targetReceiveSubSampleRecord2Sample);
                }
            }

            //复制分析数据
            for (DtoAnalyseData analyseData : source.getAnalyseData()) {
                //等同于第一次的模板样品
                if (tempSampleIds.contains(analyseData.getSampleId()) && i.equals(1)) {
                    continue;
                }
                String newSampleId = source.getNewSampleId(analyseData.getSampleId(), String.valueOf(i));
                if (StringUtils.isNotNullAndEmpty(newSampleId) && !newSampleId.equals(UUIDHelper.GUID_EMPTY)) {
                    this.loadAnalyseData(source, analyseData, newSampleId, record, target.getAnalyseData(), target.getQualityManage());
                    this.loadSampleJudgeData(analyseData, sampleJudgeDataList, saveJudgeDataList, newSampleId);
                }
            }

            //复制参数数据
            for (DtoParamsData paramsData : source.getParamsData()) {
                String newSampleId = source.getNewSampleId(paramsData.getObjectId(), String.valueOf(i));
                if (StringUtils.isNotNullAndEmpty(newSampleId) && !newSampleId.equals(UUIDHelper.GUID_EMPTY)) {
                    DtoParamsData targetParamsData = new DtoParamsData();
                    BeanUtils.copyProperties(paramsData, targetParamsData);
                    targetParamsData.setId(UUIDHelper.NewID());
                    targetParamsData.setObjectId(newSampleId);
                    target.addParamsData(targetParamsData);
                }
            }
        }

        if (!serialNumberConfigCreateList.isEmpty()) {
            serialNumberConfigRepository.batchInsert(serialNumberConfigCreateList);
        }
        if (!serialNumberConfigUpdateList.isEmpty()) {
            serialNumberConfigRepository.batchUpdate(serialNumberConfigUpdateList);
        }
        if (!businessSerialNumberList.isEmpty()) {
            businessSerialNumberService.save(businessSerialNumberList);
        }
        //排除的模板的数据
        if (!targetSamples.isEmpty()) {
            //对样品批量保存
            target.getSample().addAll(targetSamples);
        }
        //模板的样品
        if (!targetTempSamples.isEmpty()) {
            //对样品批量保存
            sampleRepository.batchInsert(targetTempSamples);
        }
        //保存模板的分析数据
        if (thisAnalyseDataList.size() > 0) {
            analyseDataRepository.batchInsert(thisAnalyseDataList);
        }

        if (thisReceiveSubSampleRecord2SampleList.size() > 0) {
            receiveSubSampleRecord2SampleRepository.batchInsert(thisReceiveSubSampleRecord2SampleList);
        }

        if (saveJudgeDataList.size() > 0) {
            sampleJudgeDataRepository.save(saveJudgeDataList);
        }

        comRepository.clear();
        proService.checkSample(targetTempSamples, project);

        this.persistLoadOutSample(target);
        //添加日志信息
        List<String> sampleNames = new ArrayList<>();
        for (DtoSample sample : source.getSample()) {
            sampleNames.add(sample.getCode());
        }
        String comment = String.format("以样品%s为模板复制了%d个样品", String.join(",", sampleNames), source.getSample().size() * copyTimes);
        newLogService.createLog(record.getId(), comment, "", EnumPRO.EnumLogType.送样单样品信息.getValue(), EnumPRO.EnumLogObjectType.送样单.getValue(), EnumPRO.EnumLogOperateType.复制样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        proService.sendProMessageWoTransactional(EnumPRO.EnumProAction.复制外部样品, project.getId(), record.getId());
        if (target.getAnalyseData().size() > 0) {
            proService.sendProMessageWoTransactional(EnumPRO.EnumProAction.添加测试项目, "", "", target.getAnalyseData().stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()));
        }
    }

    /**
     * 删除外部送样单下的样品
     *
     * @param ids       样品id
     * @param receiveId 送样单id
     */
    @Override
    @Transactional
    public void deleteOutsideSample(List<String> ids, String receiveId) {
        deleteOutsideSample(ids, receiveId, false);
    }

    @Override
    @Transactional
    public void deleteOutsideSampleClearSampleCode(List<String> ids, String receiveId) {
        deleteOutsideSample(ids, receiveId, true);
    }

    /**
     * 处理现场质控人员比对项目关联的样品
     *
     * @param originSampleList 样品标识
     */
    private void handleLocalPeopleCompareSample(List<DtoSample> originSampleList) {
        if (StringUtil.isNotEmpty(originSampleList)) {
            List<String> ids = originSampleList.stream().map(DtoSample::getId).collect(toList());
            List<DtoSample> needHandleSampleList = new ArrayList<>();
            List<DtoSample> deleteSampleList = new ArrayList<>();
            List<String> projectIds = originSampleList.stream().map(DtoSample::getProjectId).filter(v -> !UUIDHelper.GUID_EMPTY.equals(v))
                    .collect(toList());
            projectIds.forEach(v -> {
                if (qcProjectService.isLocalPeopleCompare(v)) {
                    needHandleSampleList.addAll(originSampleList.stream().filter(s -> v.equals(s.getProjectId())).collect(toList()));
                }
            });
            if (StringUtil.isNotEmpty(needHandleSampleList)) {
                List<String> codes = needHandleSampleList.stream().map(DtoSample::getCode).filter(StringUtil::isNotEmpty).collect(toList());
                List<DtoSample> sampleList = sampleRepository.findByCodeIn(codes);
                needHandleSampleList.forEach(v -> {
                    List<DtoSample> relatedSamples = sampleList.stream().filter(s -> v.getCode().equals(s.getCode()) && v.getProjectId().equals(s.getProjectId()) &&
                            !v.getId().equals(s.getId())).collect(toList());
                    if (StringUtil.isNotEmpty(relatedSamples)) {
                        deleteSampleList.addAll(relatedSamples);
                    }
                });
                List<DtoSample> finalDeleteSampleList = deleteSampleList.stream().filter(s -> !ids.contains(s.getId())).collect(toList());
                if (StringUtil.isNotEmpty(finalDeleteSampleList)) {
                    Map<String, List<DtoSample>> map = finalDeleteSampleList.stream().filter(s -> StringUtil.isNotEmpty(s.getReceiveId())
                            && !UUIDHelper.GUID_EMPTY.equals(s.getReceiveId())).collect(Collectors.groupingBy(DtoSample::getReceiveId));
                    map.forEach((k, v) -> deleteOutsideSample(v.stream().map(DtoSample::getId).collect(toList()), k));
                }
            }
        }
    }


    /**
     * 获取需要删除的评价标准记录
     *
     * @param osd 删除外部样品的数据结构
     * @return 需要删除的评价标准记录id集合
     */
    private List<String> findDeletedEvaluationRecordIds(DtoOutSampleDelete osd) {
        //查询所有需要删除的样品
        List<DtoSample> sampleDataOfDeleted = StringUtil.isNotEmpty(osd.getSampleIds()) ? sampleRepository.findAll(osd.getSampleIds()) : new ArrayList<>();
        //获取需要删除的测试项目数据
        List<DtoAnalyseData> anaDataOfDeleted = StringUtil.isNotEmpty(osd.getAnalyseDataIds()) ? analyseDataRepository.findAll(osd.getAnalyseDataIds()) : new ArrayList<>();
        //获取到所有的测试项目id集合
        List<String> testIds = anaDataOfDeleted.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        //查询所删除样品的点位id集合
        List<String> sampleFolderIdsOfDeleted = osd.getSampleFolderIds();
        //查询到删除的样品的点位下所有的评价标准
        List<DtoEvaluationRecord> evaRecords = evaluationRecordRepository.findByObjectIdInAndTestIdIn(sampleFolderIdsOfDeleted, testIds);
        //查询所删除样品对应点位下所有的样品
        List<DtoSample> sampleOfFolder = sampleRepository.findBySampleFolderIdIn(sampleFolderIdsOfDeleted);
        //获取点位下所有的测试项目数据
        List<String> sampleIdsOfFolder = sampleOfFolder.stream().map(DtoSample::getId).filter(p -> !osd.getSampleIds().contains(p)).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> anaDataOfSample = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdsOfFolder);
        List<String> deleteEvaRecordIds = new ArrayList<>();
        for (DtoSample sample : sampleDataOfDeleted) {
            //获取删除的指标的评价标准数据
            List<DtoEvaluationRecord> evaluationRecords = evaRecords.stream().filter(p -> sample.getSampleFolderId().equals(p.getObjectId()) && testIds.contains(p.getTestId())).collect(Collectors.toList());
            //判断此测试项目是否为点位下最后一个指标
            for (String testId : testIds) {
                List<DtoAnalyseData> anaDataListOfTest = anaDataOfSample.stream().filter(p -> testId.equals(p.getTestId())).collect(Collectors.toList());
                if (StringUtil.isEmpty(anaDataListOfTest)) {
                    deleteEvaRecordIds.addAll(evaluationRecords.stream().filter(p -> testId.equals(p.getTestId())).map(DtoEvaluationRecord::getId).collect(Collectors.toList()));
                }
            }
        }
        return deleteEvaRecordIds;
    }

    //#region 私有方法

    /**
     * 获取外部送样的样品相关数据复制结构
     *
     * @param projectId 项目id
     * @param receiveId 送样单id
     * @return 外部送样的样品相关数据复制结构
     */
    private DtoLoadOutSample findLoadOutSample(String projectId, String receiveId) {
        DtoLoadOutSample source = new DtoLoadOutSample();
        //获取源样品
        List<DtoSample> sourceSamples = sampleRepository.findByProjectId(projectId);
        sourceSamples.sort(Comparator.comparing(DtoSample::getCode));
        source.setSample(sourceSamples);
        //获取源点位
        List<DtoSampleFolder> sourceSampleFolders = sampleFolderRepository.findByProjectId(projectId);
        source.setSampleFolder(sourceSampleFolders);
        List<DtoSamplingFrequency> sourceSamplingFrequencys = samplingFrequencyRepository.findBySampleFolderIdIn(source.getSampleFolderIds());
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(source.getSampleFolderIds());
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        List<DtoTest> unUseTestList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
            // 根据分析方法状态，剔除停用、废止的方法对应的测试项目
            unUseTestList = testService.filterByMethodStatus(testList);
            if (StringUtil.isNotEmpty(unUseTestList)) {
                testList.removeAll(unUseTestList);
            }
        }
        List<String> unUseTestIdList = unUseTestList.stream().map(DtoTest::getId).collect(toList());
        List<String> testIdList = testList.stream().map(DtoTest::getId).collect(toList());
        // 剔除频次信息中失效的测试项目
        sourceSamplingFrequencyTests.removeIf(p -> !testIdList.contains(p.getTestId()));
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        source.setSamplingFrequency(sourceSamplingFrequencys);
        source.setSamplingFrequencyTest(sourceSamplingFrequencyTests);
        //获取源领样单
        List<DtoReceiveSubSampleRecord> sourceReceiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveId(receiveId);
        source.setReceiveSubSampleRecord(sourceReceiveSubSampleRecords);
        if (source.getReceiveSubSampleRecordIds().size() > 0) {
            List<DtoReceiveSubSampleRecord2Sample> sourceReceiveSubSampleRecord2Samples = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordIdIn(source.getReceiveSubSampleRecordIds());
            source.setReceiveSubSampleRecord2Sample(sourceReceiveSubSampleRecord2Samples);
        }
        if (source.getSampleIds().size() > 0) {
            //获取分析数据
            List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findCopyBySampleIdIn(source.getSampleIds());
            // 剔除分析数据中失效的测试项目
            sourceAnalyseDatas.removeIf(p -> unUseTestIdList.contains(p.getTestId()));
            for (DtoAnalyseData data : sourceAnalyseDatas) {
                Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
                testOptional.ifPresent(p -> {
                    data.setAnalyseItemId(p.getAnalyzeItemId());
                    data.setAnalyzeMethodId(p.getAnalyzeMethodId());
                });
            }
            source.setAnalyseData(sourceAnalyseDatas);
            if (sourceAnalyseDatas.stream().anyMatch(DtoAnalyseData::getIsQm)) {
                List<DtoQualityManage> sourceQualityManages = qualityManageRepository.findByAnaIdIn(source.getAnalyseDataIds());
                source.setQualityManage(sourceQualityManages);
            }
            //获取参数数据
            List<DtoParamsData> sourceParamsDatas = paramsDataRepository.findByObjectIdInAndObjectType(source.getSampleIds(), EnumPRO.EnumParamsDataType.样品.getValue());
            source.setParamsData(sourceParamsDatas);
        }
        return source;
    }

    /**
     * 获取外部送样的样品相关数据复制结构
     *
     * @param sampleList 样品集合
     * @param receiveId  送样单id
     * @return 外部送样的样品相关数据复制结构
     */
    private DtoLoadOutSample findLoadOutSample(List<DtoSample> sampleList, String receiveId) {
        DtoLoadOutSample source = new DtoLoadOutSample();
        //获取源样品
        source.setSample(sampleList);

        //获取源点位
        List<String> sampleFolderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> sourceSampleFolders = sampleFolderRepository.findAll(sampleFolderIds);
        source.setSampleFolder(sourceSampleFolders);
        List<String> samplingFrequencyIds = sampleList.stream().map(DtoSample::getSamplingFrequencyId).distinct().collect(Collectors.toList());
        List<DtoSamplingFrequency> sourceSamplingFrequencys = samplingFrequencyRepository.findAll(samplingFrequencyIds);
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
        }
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        source.setSamplingFrequency(sourceSamplingFrequencys);
        source.setSamplingFrequencyTest(sourceSamplingFrequencyTests);

        //获取源领样单
        List<DtoReceiveSubSampleRecord> sourceReceiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveId(receiveId);
        source.setReceiveSubSampleRecord(sourceReceiveSubSampleRecords);
        if (source.getReceiveSubSampleRecordIds().size() > 0) {
            List<DtoReceiveSubSampleRecord2Sample> sourceReceiveSubSampleRecord2Samples = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordIdIn(source.getReceiveSubSampleRecordIds());
            sourceReceiveSubSampleRecord2Samples = sourceReceiveSubSampleRecord2Samples.stream().filter(p -> source.getSampleIds().contains(p.getSampleId())).collect(Collectors.toList());
            source.setReceiveSubSampleRecord2Sample(sourceReceiveSubSampleRecord2Samples);
        }

        //获取分析数据
        List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(source.getSampleIds());
        for (DtoAnalyseData data : sourceAnalyseDatas) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                data.setAnalyseItemId(p.getAnalyzeItemId());
                data.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        source.setAnalyseData(sourceAnalyseDatas);
        if (sourceAnalyseDatas.stream().anyMatch(DtoAnalyseData::getIsQm)) {
            List<DtoQualityManage> sourceQualityManages = qualityManageRepository.findByAnaIdIn(source.getAnalyseDataIds());
            source.setQualityManage(sourceQualityManages);
        }

        //获取参数数据
        List<DtoParamsData> sourceParamsDatas = paramsDataRepository.findByObjectIdInAndObjectType(source.getSampleIds(), EnumPRO.EnumParamsDataType.样品.getValue());
        source.setParamsData(sourceParamsDatas);

        return source;
    }

    /**
     * 获取删除外部样品的数据结构
     *
     * @param ids        样品id
     * @param receiveId  送样单id
     * @param loadFolder 是否加载点位数据
     * @return 删除外部样品的数据结构
     */
    private DtoOutSampleDelete findOutSampleDelete(List<String> ids, String receiveId, Boolean loadFolder) {
        DtoOutSampleDelete osd = new DtoOutSampleDelete();
        osd.setReceiveId(receiveId);

        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select qc.id");
        stringBuilder.append(" from DtoQualityControl qc where 1=1 and qc.isDeleted = 0");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and qc.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and qc.associateSampleId in :sampleIds ");
        stringBuilder.append(" and qc.qcGrade = :qcGrade ");
        stringBuilder.append(" and qc.qcType = :qcType ");
        values.put("sampleIds", ids);
        values.put("qcGrade", EnumLIM.EnumQCGrade.外部质控.getValue());
        values.put("qcType", new QualityBlank().qcTypeValue());
        List<String> blankQcIds = comRepository.find(stringBuilder.toString(), values);
        values.clear();
        stringBuilder = new StringBuilder("select s.id,s.qcId,s.sampleCategory");
        stringBuilder.append(" from DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.associateSampleId in :sampleIds ");
        if (blankQcIds.size() > 0) {
            stringBuilder.append(" and s.qcId not in :qcIds");
            values.put("qcIds", blankQcIds);
        }

        values.put("sampleIds", ids);
        List<Object[]> associateSamples = comRepository.find(stringBuilder.toString(), values);
        if (associateSamples.size() > 0) {
            //先将原样+原样的放入室内质控样，其余放入质控样
            osd.setAssociateSampleIds(associateSamples.stream().filter(p -> !EnumPRO.EnumSampleCategory.原样加原样.getValue().equals((Integer) p[2])).map(p -> (String) p[0]).collect(Collectors.toList()));
            osd.setInnerAnalyseDataIds(associateSamples.stream().filter(p -> EnumPRO.EnumSampleCategory.原样加原样.getValue().equals((Integer) p[2])).map(p -> (String) p[0]).collect(Collectors.toList()));
            List<String> assoSamIds = associateSamples.stream().map(p -> (String) p[0]).collect(Collectors.toList());
            osd.setSampleIds(ids.stream().filter(p -> !assoSamIds.contains(p)).collect(Collectors.toList()));
            osd.setQcIds(associateSamples.stream().map(p -> (String) p[1]).collect(Collectors.toList()));
            osd.getQcIds().remove(UUIDHelper.GUID_EMPTY);
        } else {
            osd.setSampleIds(ids);
        }

        if (osd.getSampleIds().size() > 0) {
            values.clear();
            stringBuilder = new StringBuilder("select s.sampleFolderId,s.samplingFrequencyId");
            stringBuilder.append(" from DtoSample s where 1=1");
            if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                stringBuilder.append(" and s.orgId = :orgId");
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
            stringBuilder.append(" and s.id in :sampleIds");

            values.put("sampleIds", osd.getSampleIds());
            List<Object[]> objIds = comRepository.find(stringBuilder.toString(), values);

            osd.setSampleFolderIds(objIds.stream().map(p -> (String) p[0]).distinct().collect(Collectors.toList()));
            osd.setSamplingFrequencyIds(objIds.stream().map(p -> (String) p[1]).collect(Collectors.toList()));

            // 筛选样品关联的质控数据
            values.clear();
            stringBuilder = new StringBuilder("select qc.id");
            stringBuilder.append(" from DtoQualityControl qc,DtoSample s ");
            stringBuilder.append(" where 1=1 and qc.id = s.qcId and qc.isDeleted = 0 and s.isDeleted = 0");
            stringBuilder.append(" and s.id in :sampleIds");
            values.put("sampleIds", osd.getSampleIds());
            List<String> qcIds = comRepository.find(stringBuilder.toString(), values);
            if (qcIds.size() > 0) {
                qcIds.addAll(osd.getQcIds());
                osd.setQcIds(qcIds);
            }
        }
        values.clear();
        stringBuilder = new StringBuilder("select a.id,a.workSheetFolderId,a.sampleId,a.qcGrade,a.analystId,a.isCompleteField,a.isOutsourcing,a.isSamplingOut,a.dataStatus");
        stringBuilder.append(" from DtoAnalyseData a where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and a.sampleId in :sampleIds ");
        values.put("sampleIds", osd.getAllSampleIds());
        List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);
        List<DtoAnalyseData> analyseDatas = new ArrayList<>();
        for (Object[] data : datas) {
            DtoAnalyseData analyseData = new DtoAnalyseData();
            analyseData.setId((String) data[0]);
            analyseData.setWorkSheetFolderId((String) data[1]);
            analyseData.setSampleId((String) data[2]);
            analyseData.setQcGrade((Integer) data[3]);
            analyseData.setAnalystId((String) data[4]);
            analyseData.setIsCompleteField((Boolean) data[5]);
            analyseData.setIsOutsourcing((Boolean) data[6]);
            analyseData.setIsSamplingOut((Boolean) data[7]);
            analyseData.setDataStatus((Integer) data[8]);
            analyseDatas.add(analyseData);
        }
        osd.setInnerAnalyseDataIds(analyseDatas.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())).map(DtoAnalyseData::getId).collect(Collectors.toList()));
        if (osd.getInnerAnalyseDataIds().size() > 0) {
            //若存在室内质控的数据，将样品加到室内样中
            osd.getInnerSampleIds().addAll(analyseDatas.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())).map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList()));
        }
        if (osd.getInnerSampleIds().size() > 0) {
            //若有室内样，重新填充室内数据
            osd.setInnerAnalyseDataIds(analyseDatas.stream().filter(p -> osd.getInnerSampleIds().contains(p.getSampleId())).map(DtoAnalyseData::getId).collect(Collectors.toList()));

            //室外质控样排除室内的
            osd.setAssociateSampleIds(osd.getAssociateSampleIds().stream().filter(p -> !osd.getInnerSampleIds().contains(p)).collect(Collectors.toList()));
        }
        //数据id为非室内数据的id
        osd.setAnalyseDataIds(analyseDatas.stream().filter(p -> !osd.getInnerAnalyseDataIds().contains(p.getId())).map(DtoAnalyseData::getId).collect(Collectors.toList()));
        osd.setAnalystIds(analyseDatas.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()));
        HashSet<DtoAnalyseData> set = new HashSet<>();
        for (DtoAnalyseData analyseData : analyseDatas) {
            if (!analyseData.getIsOutsourcing() && !analyseData.getIsSamplingOut()) {
                DtoAnalyseData ana = new DtoAnalyseData();
                ana.setId(UUIDHelper.GUID_EMPTY);
                ana.setIsCompleteField(analyseData.getIsCompleteField());
                ana.setDataStatus(analyseData.getDataStatus());
                set.add(ana);
            }
        }
        osd.setAnalyseDatas(new ArrayList<>(set));

        //当前删除数据所在的检测单id
        List<String> workSheetFolderIds = analyseDatas.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        workSheetFolderIds.remove(UUIDHelper.GUID_EMPTY);
        osd.setWorkSheetFolderIds(workSheetFolderIds);

        return osd;
    }

    /**
     * 插入数据
     *
     * @param load 外部样品数据载体
     */
    private void persistLoadOutSample(DtoLoadOutSample load) {
        //先判断测试项目复制的测试项目是否存在
        if (load.getSamplingFrequencyTest().size() > 0) {
            proService.checkTestIsExists(load.getSamplingFrequencyTest());
        }
        if (load.getSample().size() > 0) {
            sampleRepository.batchInsert(load.getSample());
        }
        if (load.getSampleFolder().size() > 0) {
            sampleFolderRepository.save(load.getSampleFolder());
        }
        if (load.getSamplingFrequency().size() > 0) {
            samplingFrequencyService.save(load.getSamplingFrequency());
        }
        if (load.getSamplingFrequencyTest().size() > 0) {
            samplingFrequencyTestRepository.save(load.getSamplingFrequencyTest());
        }
        if (load.getAnalyseData().size() > 0) {
            analyseDataRepository.batchInsert(load.getAnalyseData());
        }
        if (load.getQualityManage().size() > 0) {
            qualityManageRepository.save(load.getQualityManage());
        }
        if (load.getReceiveSubSampleRecord().size() > 0) {
            receiveSubSampleRecordRepository.save(load.getReceiveSubSampleRecord());
        }
        if (load.getReceiveSubSampleRecord2Sample().size() > 0) {
            receiveSubSampleRecord2SampleRepository.batchInsert(load.getReceiveSubSampleRecord2Sample());
        }
        if (load.getParamsData().size() > 0) {
            paramsDataRepository.save(load.getParamsData());
        }
    }

    /**
     * 加载点位相关数据
     *
     * @param source          来源样品数据装载结构
     * @param copyTimes       复制次数
     * @param projectTypeCode 项目登记页类型
     * @param target          目标
     */
    private void loadSampleFolder(DtoLoadOutSample source, Integer copyTimes, String projectTypeCode, DtoLoadOutSample target) {
        if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {
            for (Integer i = 1; i <= copyTimes; i++) {
                for (DtoSampleFolder sf : source.getSampleFolder()) {
                    DtoSampleFolder targetSampleFolder = new DtoSampleFolder();
                    BeanUtils.copyProperties(sf, targetSampleFolder);
                    targetSampleFolder.setId(UUIDHelper.NewID());

                    source.putNewSampleFolderId(sf.getId(), targetSampleFolder.getId(), String.valueOf(i));
                    target.addSampleFolder(targetSampleFolder);
                }
                for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                    DtoSamplingFrequency targetSamplingFrequency = new DtoSamplingFrequency();
                    BeanUtils.copyProperties(sf, targetSamplingFrequency);
                    targetSamplingFrequency.setSampleFolderId(source.getNewSampleFolderId(sf.getSampleFolderId(), String.valueOf(i)));
                    targetSamplingFrequency.setId(UUIDHelper.NewID());

                    source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i));
                    target.addSamplingFrequency(targetSamplingFrequency);
                }
            }
        } else {
            Map<String, Integer> maxTimesOrderMap = new HashMap<>();
            List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(source.getSample().stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList()));
            source.getSample().stream().collect(Collectors.groupingBy(p -> String.format("%s_%d", p.getSampleFolderId(), p.getCycleOrder()), Collectors.toList())).forEach((key, samps) -> {
                maxTimesOrderMap.put(key, 1);
                DtoSample temp = samps.get(0);
                List<DtoSamplingFrequency> frequencyList = samplingFrequencyList.stream().filter(p -> p.getSampleFolderId().equals(temp.getSampleFolderId())
                        && p.getPeriodCount().equals(temp.getCycleOrder())).collect(Collectors.toList());
                if (frequencyList.stream().max(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod)).isPresent()) {
                    maxTimesOrderMap.put(key, frequencyList.stream().max(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod)).get().getTimePerPeriod());
                }
            });

            //遍历样品，保证自增次数的连续性
            for (DtoSample sample : source.getSample()) {
                String key = String.format("%s_%d", sample.getSampleFolderId(), sample.getCycleOrder());
                Integer maxTimes = maxTimesOrderMap.get(key);
                for (Integer i = 1; i <= copyTimes; i++) {
                    DtoSamplingFrequency targetSamplingFrequency = new DtoSamplingFrequency();
                    targetSamplingFrequency.setSampleFolderId(sample.getSampleFolderId());
                    targetSamplingFrequency.setPeriodCount(sample.getCycleOrder());
                    //【重要】【2022-6-9】【嘉兴】【样品登记】送样、应急、委托现场送样等送样类任务，样品登记时，周期频次固定为：1-1-1
                    //targetSamplingFrequency.setTimePerPeriod(maxTimes + i);
                    targetSamplingFrequency.setTimePerPeriod(sample.getTimesOrder());
                    targetSamplingFrequency.setSamplePerTime(sample.getSampleOrder());

                    targetSamplingFrequency.setId(UUIDHelper.NewID());
                    source.putNewSamplingFrequencyId(sample.getSamplingFrequencyId(), targetSamplingFrequency.getId(), String.valueOf(i));
                    target.addSamplingFrequency(targetSamplingFrequency);
                }
                maxTimesOrderMap.put(key, maxTimes + copyTimes);
            }

            for (Integer i = 1; i <= copyTimes; i++) {
                for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
                    DtoSamplingFrequencyTest targetSamplingFrequencyTest = new DtoSamplingFrequencyTest();
                    BeanUtils.copyProperties(sft, targetSamplingFrequencyTest);
                    targetSamplingFrequencyTest.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId(), String.valueOf(i)));
                    targetSamplingFrequencyTest.setId(UUIDHelper.NewID());
                    target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
                }
            }
        }
    }

    /**
     * 加载分析数据
     *
     * @param source               来源样品数据装载结构
     * @param sourceAnalyseData    源分析数据
     * @param targetSampleId       目标样品id
     * @param record               送样单
     * @param targetAnalyseDatas   目标数据集合
     * @param targetQualityManages 目标质控集合
     */
    private void loadAnalyseData(DtoLoadOutSample source, DtoAnalyseData sourceAnalyseData, String targetSampleId, DtoReceiveSampleRecord record,
                                 List<DtoAnalyseData> targetAnalyseDatas,
                                 List<DtoQualityManage> targetQualityManages) {

        DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(sourceAnalyseData);
        targetAnalyseData.setSampleId(targetSampleId);
        targetAnalyseData.setIsDataEnabled(false);
        if ((targetAnalyseData.getIsSamplingOut()
                && !record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()))
                || targetAnalyseData.getIsOutsourcing()) {
            targetAnalyseData.setStatus(EnumPRO.EnumAnalyseDataStatus.已确认.toString());
            targetAnalyseData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.已确认.getValue());
            targetAnalyseData.setIsDataEnabled(true);
        }

        targetAnalyseDatas.add(targetAnalyseData);

        DtoQualityManage qm = source.getQualityManage().stream().filter(p -> p.getAnaId().equals(sourceAnalyseData.getId())).findFirst().orElse(null);
        if (StringUtil.isNotNull(qm)) {
            DtoQualityManage targetQualityManage = new DtoQualityManage();
            BeanUtils.copyProperties(qm, targetQualityManage);
            targetQualityManage.setId(UUIDHelper.NewID());
            targetQualityManage.setAnaId(targetAnalyseData.getId());
            targetQualityManages.add(targetQualityManage);
        }
    }

    /**
     * 加载比对分析数据
     *
     * @param sourceAnalyseData   源分析数据
     * @param sourceJudgeDataList 源比对数据
     * @param targetJudgeDataList 目标比对数据
     * @param newSampleId         新样品id
     */
    private void loadSampleJudgeData(DtoAnalyseData sourceAnalyseData, List<DtoSampleJudgeData> sourceJudgeDataList, List<DtoSampleJudgeData> targetJudgeDataList, String newSampleId) {
        DtoSampleJudgeData sourceData = sourceJudgeDataList.stream().filter(s -> s.getSampleId().equals(sourceAnalyseData.getSampleId()) && s.getTestId().equals(sourceAnalyseData.getTestId())).findFirst().orElse(null);
        if (StringUtil.isNotNull(sourceData)) {
            DtoSampleJudgeData targetSampleJudgeData = new DtoSampleJudgeData();
            BeanUtils.copyProperties(sourceData, targetSampleJudgeData, "id", "onlineValue", "expectedValue", "qcRateValue", "pass",
                    "checkItemValue", "creator", "createDate", "modifier", "modifyDate", "dataStatus", "standardCode", "testTimeStr", "isNotEvaluate");
            targetSampleJudgeData.setSampleId(newSampleId);
            targetSampleJudgeData.setExpectedValue("/");
            targetSampleJudgeData.setStandardCode("/");
            targetJudgeDataList.add(targetSampleJudgeData);
        }
    }

    /**
     * 获取模板样品
     *
     * @param sourceSample 源样品
     * @param record       送样单
     * @param subRecord    实验室领样单
     * @param type         样品指标类型
     * @return 模板样品
     */
    private DtoSample getTempSample(DtoSample sourceSample, DtoReceiveSampleRecord record, DtoReceiveSubSampleRecord subRecord, Integer type) {
        DtoSample targetTemp = new DtoSample();
        BeanUtils.copyProperties(sourceSample, targetTemp);
        targetTemp.setId(UUIDHelper.NewID());
        targetTemp.setRedFolderName(sourceSample.getRedFolderName());
        targetTemp.setInceptTime(new Date());
        targetTemp.setSamplingTimeBegin(record.getSamplingTime());
        targetTemp.setSamplingTimeEnd(record.getSamplingTime());
        targetTemp.setDataChangeStatus(EnumPRO.EnumSampleChangeStatus.未变更.getValue());
        targetTemp.setSampleCategory(EnumPRO.EnumSampleCategory.原样.getValue());
        targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
        if (type.equals(EnumPRO.EnumSampleTestType.无指标.getValue())) {
            targetTemp.setStatus(EnumPRO.EnumSampleStatus.样品检毕.toString());
            targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
            targetTemp.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
        } else if (type.equals(EnumPRO.EnumSampleTestType.实验室指标.getValue())) {
            if (StringUtil.isNotNull(subRecord) && (subRecord.getSubStatus() & (EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue())) != 0) {
                targetTemp.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                targetTemp.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
            } else {
                targetTemp.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                targetTemp.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不能分析.getValue());
            }
        } else if (type.equals(EnumPRO.EnumSampleTestType.现场指标.getValue())) {
            targetTemp.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
            targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
            targetTemp.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
        } else if (type.equals(EnumPRO.EnumSampleTestType.全指标.getValue())) {
            targetTemp.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
            targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
            if (StringUtil.isNotNull(subRecord) && (subRecord.getSubStatus() & (EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue())) != 0) {
                targetTemp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
            }
            targetTemp.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
        }
        targetTemp.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
        targetTemp.setStoreStatus(EnumPRO.EnumStoreStatus.不能存储.getValue());
        targetTemp.setMakeStatus(EnumPRO.EnumMakeStatus.不需要制样.getValue());

        targetTemp.setCreateDate(new Date());
        targetTemp.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        targetTemp.setModifyDate(new Date());
        targetTemp.setModifier(PrincipalContextUser.getPrincipal().getUserId());
        return targetTemp;
    }

    /**
     * 获取送样单下的点位id
     *
     * @param receiveId 送样单id
     * @return 点位id集合
     */
    private List<String> findReceiveSampleFolderIds(String receiveId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select distinct s.sampleFolderId");
        stringBuilder.append(" from DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0 ");
        stringBuilder.append(" and s.receiveId = :receiveId ");
        values.put("receiveId", receiveId);
        List<String> sampleFolderIds = comRepository.find(stringBuilder.toString(), values);
        sampleFolderIds.remove(UUIDHelper.GUID_EMPTY);
        return sampleFolderIds;
    }

    /**
     * 获取送样单下的点位id
     *
     * @param receiveId 送样单id
     * @return 点位id集合
     */
    private List<DtoAnalyseData> findReceiveAnalyseDatas(String receiveId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select distinct a.isCompleteField,a.dataStatus");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and a.sampleId=s.id");
        stringBuilder.append(" and a.isDeleted = 0 ");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and s.isDeleted = 0 ");
        stringBuilder.append(" and s.receiveId = :receiveId ");
        values.put("receiveId", receiveId);
        List<Object[]> analyseDatas = comRepository.find(stringBuilder.toString(), values);
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        for (Object[] analyseData : analyseDatas) {
            DtoAnalyseData ana = new DtoAnalyseData();
            ana.setId(UUIDHelper.GUID_EMPTY);
            ana.setIsCompleteField((Boolean) analyseData[0]);
            ana.setDataStatus((Integer) analyseData[1]);
            anaDatas.add(ana);
        }
        return anaDatas;
    }

    //#endregion

    //#endregion

    //#region 方案类

    /**
     * 复制方案
     *
     * @param sourceProjectId 源项目id
     * @param targetProjectId 目标项目id
     */
    @Override
    @Transactional
    public void copyScheme(String sourceProjectId, String targetProjectId) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();
        //来源数据载体
        Date to = new Date();
        DtoLoadScheme source = this.findLoadScheme(sourceProjectId);
        //复制点位数据
        //复制点位数据
        Map<String, String> oldFolderId2NewIdMap = new HashMap<>();
        for (DtoSampleFolder sf : source.getSampleFolder()) {
            DtoSampleFolder targetSampleFolder = new DtoSampleFolder();
            BeanUtils.copyProperties(sf, targetSampleFolder);
            targetSampleFolder.setProjectId(targetProjectId);
            targetSampleFolder.setId(UUIDHelper.NewID());
            source.putNewSampleFolderId(sf.getId(), targetSampleFolder.getId());
            target.addSampleFolder(targetSampleFolder);
            oldFolderId2NewIdMap.put(sf.getId(), targetSampleFolder.getId());
        }
        //复制评价记录(按点位id存储)
        List<String> oldFolderIdList = source.getSampleFolder().stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(oldFolderIdList)) {
            copyEvaluationRecord(oldFolderIdList, EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue(), oldFolderId2NewIdMap);
        }

        for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
            DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf, source.getNewSampleFolderId(sf.getSampleFolderId()));

            source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId());
            target.addSamplingFrequency(targetSamplingFrequency);
        }
        for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
            DtoSamplingFrequencyTest targetSamplingFrequencyTest = this.getTargetSamplingFrequencyTest(sft,
                    source.getNewSampleFolderId(sft.getSampleFolderId()),
                    source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId()));

            target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
        }

        DtoProject project = projectRepository.findOne(targetProjectId);
        //复制样品数据
        for (DtoSample sam : source.getSample()) {
            DtoSample targetSample = this.getSchemeCloneSample(sam);
            targetSample.setSampleFolderId(source.getNewSampleFolderId(sam.getSampleFolderId()));
            targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId()));
            targetSample.setInspectedEntId(project.getInspectedEntId());
            targetSample.setInspectedEnt(project.getInspectedEnt());
            targetSample.setProjectId(targetProjectId);

            source.putNewSampleId(sam.getId(), targetSample.getId());
            target.addSample(targetSample);
        }

        //复制分析数据
        Map<String, String> oldAnaId2NewIdMap = new HashMap<>();
        for (DtoAnalyseData analyseData : source.getAnalyseData()) {
            DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
            targetAnalyseData.setSampleId(source.getNewSampleId(analyseData.getSampleId()));
            //如果是现场分析数据，分析人填空
            if (analyseData.getIsCompleteField()) {
                analyseData.setAnalystName("");
                analyseData.setAnalystId(UUIDHelper.GUID_EMPTY);
            }
            target.addAnalyseData(targetAnalyseData);
            oldAnaId2NewIdMap.put(analyseData.getId(), targetAnalyseData.getId());
        }
        //复制评价记录(按分析数据id存储)
        List<String> oldAnaIdList = source.getAnalyseData().stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(oldAnaIdList)) {
            copyEvaluationRecord(oldAnaIdList, EnumPRO.EnumEvaluationType.分析数据.getValue(), EnumPRO.EnumEvaluationPlan.分析数据.getValue(), oldAnaId2NewIdMap);
        }

        this.persistLoadScheme(target);

        Date t1 = new Date();
        System.out.println("t1-t0 " + (t1.getTime() - to.getTime()));
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增点位, targetProjectId);
                    }
                }
        );
    }

    /**
     * 新增点位
     *
     * @param sampleFolder 点位
     * @param testList     点位测试项目
     * @return 点位
     */
    @Override
    @Transactional
    public DtoLoadScheme addSchemeFolder(DtoSampleFolder sampleFolder, List<DtoTest> testList) {
        //用于复制的模板数据载体
        DtoLoadScheme templateScheme = new DtoLoadScheme();
        //用于插入的数据载体
        DtoLoadScheme targetScheme = new DtoLoadScheme();

        DtoProject project = projectRepository.findOne(sampleFolder.getProjectId());
        //新增样品周期频次
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                    Integer finalJ = j;
                    Integer finalV = v;
                    List<DtoTest> addTestList = testList.stream().filter(p -> p.getTimePerPeriod() >= finalJ
                            && p.getSampleOrder() >= finalV).collect(toList());
                    if (testList.size() != 0 && addTestList.size() == 0) {
                        continue;
                    }
                    DtoSamplingFrequency frequency = new DtoSamplingFrequency();
                    frequency.setPeriodCount(i);
                    frequency.setTimePerPeriod(j);
                    frequency.setSamplePerTime(v);
                    frequency.setSampleFolderId(sampleFolder.getId());
                    targetScheme.addSamplingFrequency(frequency);
                    templateScheme.putNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, frequency.getId(),
                            String.valueOf(i), String.valueOf(j), String.valueOf(v));
                    //新增周期频次与测试项目关联数据
                    for (DtoTest test : addTestList) {
                        DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest(test);
                        sft.setSampleFolderId(sampleFolder.getId());
                        sft.setSamplingFrequencyId(frequency.getId());
                        targetScheme.addSamplingFrequencyTest(sft);
                    }
                }
            }
        }
        //新增样品
        DtoSample temp = new DtoSample(true);
        temp.setSamplingFrequencyId(templateScheme.getNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, "1", "1", "1"));
        temp.setSampleFolderId(sampleFolder.getId());
        temp.setCycleOrder(1);
        temp.setTimesOrder(1);
        temp.setSampleOrder(1);
        temp.setProjectId(sampleFolder.getProjectId());
        temp.setRedFolderName(this.getFolderName(sampleFolder, 1, 1, 1));
        log.info("sampleId:" + temp.getId() + " code:" + temp.getCode() + "sampleTypeId:" + sampleFolder.getSampleTypeId());
        temp.setSampleTypeId(sampleFolder.getSampleTypeId());
        temp.setInspectedEntId(project.getInspectedEntId());
        temp.setInspectedEnt(project.getInspectedEnt());
        temp.setInceptTime(new Date());
        temp.setStatus(EnumPRO.EnumSampleStatus.样品未采样.toString());
        temp.setSamplingStatus(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue());
        temp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
        temp.setAnanlyzeStatus(testList.size() > 0 ? EnumPRO.EnumAnalyzeStatus.不能分析.getValue() : EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
        temp.setStoreStatus(EnumPRO.EnumStoreStatus.不能存储.getValue());
        temp.setMakeStatus(EnumPRO.EnumMakeStatus.不需要制样.getValue());
        temp.setSampleCategory(EnumPRO.EnumSampleCategory.原样.getValue());
        temp.setRedAnalyzeItems(proService.getAnalyzeItemsByTest(testList));
        temp.setPack("");
        temp.setSampleWeight("");
        temp.setWeightOrQuantity("");
        temp.setSamColor("");
        temp.setSampleExplain("");
        temp.setVolume("");
        temp.setSamplingPlace("");
        temp.setRemark("");
        temp.setPreTreatmentCases("");
        temp.setUnqualifiedReason("");
        temp.setDisposeMeasure("");
        temp.setLon(sampleFolder.getLon());
        temp.setLat(sampleFolder.getLat());

        templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, temp.getId(), "1", "1", "1");
        temp = sampleRepository.save(temp);

        //根据点位周期来新增样品
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                    if (i.equals(1) && j.equals(1) && v.equals(1)) {
                        continue;
                    }
                    Integer finalJ = j;
                    Integer finalV = v;
                    List<DtoTest> addTestList = testList.stream().filter(p -> p.getTimePerPeriod() >= finalJ
                            && p.getSampleOrder() >= finalV).collect(toList());
                    if (testList.size() != 0 && addTestList.size() == 0) {
                        continue;
                    }
                    DtoSample targetSample = new DtoSample();
                    BeanUtils.copyProperties(temp, targetSample);
                    targetSample.setCycleOrder(i);
                    targetSample.setTimesOrder(j);
                    targetSample.setSampleOrder(v);
                    targetSample.setSamplingFrequencyId(templateScheme.getNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY,
                            String.valueOf(i), String.valueOf(j), String.valueOf(v)));
                    targetSample.setId(UUIDHelper.NewID());
                    targetSample.setRedFolderName(this.getFolderName(sampleFolder, i, j, v));

                    templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, targetSample.getId(), String.valueOf(i),
                            String.valueOf(j), String.valueOf(v));
                    targetScheme.addSample(targetSample);
                }
            }
        }

        //新增分析数据
        if (testList.size() > 0) {
            DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, temp, testList, false);
            addDto.setIsAddAssociateTest(false);
            analyseDataService.addAnalyseData(addDto);

            List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdAndIsDeletedFalse(temp.getId());

            for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
                for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                    for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                        if (i.equals(1) && j.equals(1) && v.equals(1)) {
                            continue;
                        }
                        Integer finalJ = j;
                        Integer finalV = v;
                        Set<String> tIds = testList.stream().filter(p -> p.getTimePerPeriod() >= finalJ
                                && p.getSampleOrder() >= finalV).map(DtoTest::getId).collect(Collectors.toSet());
                        if (tIds.size() == 0) {
                            continue;
                        }
                        List<DtoAnalyseData> dataList = sourceAnalyseDatas.stream().filter(p -> tIds.contains(p.getTestId()))
                                .collect(toList());
                        for (DtoAnalyseData analyseData : dataList) {
                            DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                            targetAnalyseData.setSampleId(templateScheme.getNewSampleId(UUIDHelper.GUID_EMPTY,
                                    String.valueOf(i), String.valueOf(j), String.valueOf(v)));
                            targetScheme.addAnalyseData(targetAnalyseData);
                        }
                    }
                }
            }
        }

        sampleFolderRepository.save(sampleFolder);
        this.persistLoadScheme(targetScheme);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("新增点位%s(%s),%d周期,%d次,%d个样。", sampleFolder.getWatchSpot(), sampleType.getTypeName(),
                sampleFolder.getPeriodCount(), sampleFolder.getTimePerPeriod(), sampleFolder.getSampleOrder());
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增点位, sampleFolder.getProjectId());
                    }
                }
        );

        targetScheme.setSampleFolder(Collections.singletonList(sampleFolder));
        return targetScheme;
    }

    /**
     * 复制点位
     *
     * @param ids       点位id集合
     * @param copyTimes 复制次数
     */
    @Override
    @Transactional
    public DtoLoadScheme copySchemeSampleFolder(List<String> ids, Integer copyTimes, Boolean isApi) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();
        Map<String, List<Integer>> folderId2CycleOrder = new HashMap<>();
        if (isApi) {
            List<String> idList = new ArrayList<>();
            for (String id : ids) {
                String[] strs = id.split(",");
                id = strs[0];
                idList.add(id);
                if (folderId2CycleOrder.containsKey(id)) {
                    folderId2CycleOrder.get(id).add(Integer.valueOf(strs[1]));
                } else {
                    List<Integer> cycleOrders = new ArrayList<>();
                    cycleOrders.add(Integer.valueOf(strs[1]));
                    folderId2CycleOrder.put(id, cycleOrders);
                }
            }
            ids = idList;
        }


        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findLoadSchemeBySampleFolderIds(ids);
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2Samples = new ArrayList<>();
        List<DtoReceiveSubSampleRecord> receiveSubSampleRecords = new ArrayList<>();
        if (isApi) {
            receiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveId(source.getSample().get(0).getReceiveId());
        }
        for (Integer i = 1; i <= copyTimes; i++) {
            //复制点位数据
            for (DtoSampleFolder sf : source.getSampleFolder()) {
                DtoSampleFolder targetSampleFolder = new DtoSampleFolder();
                BeanUtils.copyProperties(sf, targetSampleFolder);
                targetSampleFolder.setId(UUIDHelper.NewID());

                Object[] watchSpotArr = this.getStrSplit(sf.getWatchSpot());
                if (watchSpotArr.length == 2) {
                    targetSampleFolder.setWatchSpot(String.format("%s%d", String.valueOf(watchSpotArr[0]), Integer.valueOf(String.valueOf(watchSpotArr[1])) + i));
                }
                if (isApi) {
                    targetSampleFolder.setWatchSpot(sf.getWatchSpot() + "【复制】");
                }
                if (StringUtils.isNotNullAndEmpty(sf.getFolderCode())) {
                    Object[] folderCodeArr = this.getStrSplit(sf.getFolderCode());
                    if (folderCodeArr.length == 2) {
                        targetSampleFolder.setFolderCode(String.format("%s%d", String.valueOf(folderCodeArr[0]), Integer.valueOf(String.valueOf(folderCodeArr[1])) + i));
                    }
                }

                source.putNewSampleFolderId(sf.getId(), targetSampleFolder.getId(), String.valueOf(i));
                target.addSampleFolder(targetSampleFolder);
            }

            for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                if (isApi && !folderId2CycleOrder.get(sf.getSampleFolderId()).contains(sf.getTimePerPeriod())) {
                    continue;
                }
                DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf, source.getNewSampleFolderId(sf.getSampleFolderId(), String.valueOf(i)));

                source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i));
                target.addSamplingFrequency(targetSamplingFrequency);
            }
            for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
                if (isApi) {
                    DtoSamplingFrequency sf = source.getSamplingFrequency().stream().filter(s -> s.getId().equals(sft.getSamplingFrequencyId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(sf) && folderId2CycleOrder.get(sf.getSampleFolderId()).contains(sf.getTimePerPeriod())) {
                        continue;
                    }
                }
                DtoSamplingFrequencyTest targetSamplingFrequencyTest = this.getTargetSamplingFrequencyTest(sft,
                        source.getNewSampleFolderId(sft.getSampleFolderId(), String.valueOf(i)),
                        source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId(), String.valueOf(i)));

                target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
            }

            //复制样品数据
            for (DtoSample sam : source.getSample()) {
                if (isApi && !folderId2CycleOrder.get(sam.getSampleFolderId()).contains(sam.getCycleOrder())) {
                    continue;
                }
                DtoSample targetSample = this.getSchemeCloneSample(sam);
                if (isApi) {
                    targetSample.setReceiveId(sam.getReceiveId());
                }
                targetSample.setSampleFolderId(source.getNewSampleFolderId(sam.getSampleFolderId(), String.valueOf(i)));
                targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(), String.valueOf(i)));

                DtoSampleFolder targetFolder = target.getSampleFolder().stream().filter(p -> p.getId().equals(targetSample.getSampleFolderId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(targetFolder)) {
                    targetSample.setRedFolderName(this.getFolderName(targetFolder, targetSample.getCycleOrder(),
                            targetSample.getTimesOrder(), targetSample.getSampleOrder()));
                }

                source.putNewSampleId(sam.getId(), targetSample.getId(), String.valueOf(i));
                target.addSample(targetSample);
            }

            //复制分析数据
            for (DtoAnalyseData analyseData : source.getAnalyseData()) {
                if (StringUtil.isNotNull(source.getNewSampleId(analyseData.getSampleId(), String.valueOf(i)))) {
                    DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                    targetAnalyseData.setSampleId(source.getNewSampleId(analyseData.getSampleId(), String.valueOf(i)));

                    target.addAnalyseData(targetAnalyseData);
                }
            }
        }

        if (isApi) {
            for (DtoReceiveSubSampleRecord subRecord : receiveSubSampleRecords) {
                for (DtoSample checkSample : target.getSample()) {
                    if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0 &&
                            target.getAnalyseData().stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && !p.getIsCompleteField())) {
                        DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                        r2s.setSampleId(checkSample.getId());
                        r2s.setReceiveSubSampleRecordId(subRecord.getId());
                        receiveSubSampleRecord2Samples.add(r2s);
                    }
                    if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0 &&
                            target.getAnalyseData().stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && p.getIsCompleteField())) {
                        DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                        r2s.setSampleId(checkSample.getId());
                        r2s.setReceiveSubSampleRecordId(subRecord.getId());
                        receiveSubSampleRecord2Samples.add(r2s);
                    }
                }
            }
            if (receiveSubSampleRecord2Samples.size() > 0) {
                receiveSubSampleRecord2SampleRepository.save(receiveSubSampleRecord2Samples);
            }
        }

        this.persistLoadScheme(target);

        //操作日志插入
        List<String> sampleTypeIds = source.getSampleFolder().stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSampleFolder folder : source.getSampleFolder()) {
            String sampleTypeName = sampleTypes.stream().filter(p -> p.getId().equals(folder.getSampleTypeId())).map(DtoSampleType::getTypeName).findFirst().orElse("");
            String comment = String.format("以点位%s(%s)为模板复制了%d个点位。", folder.getWatchSpot(), sampleTypeName, copyTimes);

            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.复制点位.toString());
            log.setLogType(EnumPRO.EnumLogType.方案点位信息.getValue());
            log.setObjectId(folder.getProjectId());
            log.setObjectType(EnumPRO.EnumLogObjectType.方案.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumPRO.EnumLogType.方案点位信息.getValue());

        //纠正项目状态
        proService.checkProject(Collections.singletonList(source.getSampleFolder().get(0).getProjectId()));
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案复制点位, source.getSampleFolder().get(0).getProjectId());
                    }
                }
        );

        return target;
    }

    /**
     * 添加点位周期频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @return 点位周期频次
     */
    @Override
    @Transactional
    public DtoLoadScheme addSchemePeriodTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod,
                                              Integer samplePeriod) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();

        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findEmptyLoadSchemeBySampleFolderId(sampleFolderId);

        DtoSampleFolder sampleFolder = source.getSampleFolder().get(0);
        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdOrderByPeriodCountDesc(sampleFolderId);
        DtoSamplingFrequency frequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);
        Integer period = StringUtil.isNotNull(frequency) ? frequency.getPeriodCount() + 1 : 1;
        for (Integer i = period; i < period + periodCount; i++) {
            for (Integer j = 1; j <= timePerPeriod; j++) {
                for (Integer k = 1; k <= samplePeriod; k++) {
                    for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                        DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf,
                                sf.getSampleFolderId(), i, j, k);

                        source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i),
                                String.valueOf(j), String.valueOf(k));
                        target.addSamplingFrequency(targetSamplingFrequency);
                    }

                    //复制样品数据
                    for (DtoSample sam : source.getSample()) {
                        DtoSample targetSample = this.getSchemeCloneSample(sam);
                        targetSample.setRedAnalyzeItems("");
                        targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(),
                                String.valueOf(i), String.valueOf(j), String.valueOf(k)));
                        targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
                        targetSample.setCycleOrder(i);
                        targetSample.setTimesOrder(j);
                        targetSample.setSampleOrder(k);
                        targetSample.setRedFolderName(this.getFolderName(sampleFolder, targetSample.getCycleOrder(),
                                targetSample.getTimesOrder(), targetSample.getSampleOrder()));

                        target.addSample(targetSample);
                    }
                }
            }
        }

        this.persistLoadScheme(target);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("增加了点位%s(%s)频次:%d周期%d次%d个样。", sampleFolder.getWatchSpot(), sampleType.getTypeName(),
                periodCount, timePerPeriod, samplePeriod);
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加频次.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增周期次数, sampleFolder.getProjectId());
                    }
                }
        );

        target.setSampleFolder(source.getSampleFolder());
        return target;
    }

    /**
     * 添加方案次数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @return 点位周期频次
     */
    @Override
    @Transactional
    public DtoLoadScheme addSchemeTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();

        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findEmptyLoadSchemeBySampleFolderId(sampleFolderId);

        DtoSampleFolder sampleFolder = source.getSampleFolder().get(0);

        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdAndPeriodCountOrderByTimePerPeriodDesc(sampleFolderId, periodCount);
        DtoSamplingFrequency frequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);
        Integer times = StringUtil.isNotNull(frequency) ? frequency.getTimePerPeriod() + 1 : 1;
        for (Integer i = times; i < times + timePerPeriod; i++) {
            for (Integer j = 1; j <= samplePeriod; j++) {
                for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                    DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf,
                            sf.getSampleFolderId(), periodCount, i, j);

                    source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i),
                            String.valueOf(j));
                    target.addSamplingFrequency(targetSamplingFrequency);
                }

                //复制样品数据
                for (DtoSample sam : source.getSample()) {
                    DtoSample targetSample = this.getSchemeCloneSample(sam);
                    targetSample.setRedAnalyzeItems("");
                    targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(),
                            String.valueOf(i), String.valueOf(j)));
                    targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
                    targetSample.setCycleOrder(periodCount);
                    targetSample.setTimesOrder(i);
                    targetSample.setSampleOrder(j);
                    targetSample.setRedFolderName(this.getFolderName(sampleFolder, targetSample.getCycleOrder(),
                            targetSample.getTimesOrder(), targetSample.getSampleOrder()));

                    target.addSample(targetSample);
                }
            }
        }

        this.persistLoadScheme(target);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("增加了点位%s(%s)第%d周期次数:%d次%d个样。", sampleFolder.getWatchSpot(), sampleType.getTypeName(), periodCount, timePerPeriod,
                samplePeriod);
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加次数.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增次数, sampleFolder.getProjectId());
                    }
                }
        );

        target.setSampleFolder(source.getSampleFolder());
        return target;
    }

    /**
     * 添加方案次数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @return 点位周期频次
     */
    @Override
    @Transactional
    public DtoLoadScheme addSchemeSamplePeriods(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();

        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findEmptyLoadSchemeBySampleFolderId(sampleFolderId);

        DtoSampleFolder sampleFolder = source.getSampleFolder().get(0);

        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository
                .findBySampleFolderIdAndPeriodCountAndTimePerPeriodOrderBySamplePerTimeDesc(sampleFolderId, periodCount, timePerPeriod);
        DtoSamplingFrequency frequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);
        Integer times = StringUtil.isNotNull(frequency) ? frequency.getSamplePerTime() + 1 : 1;
        for (Integer i = times; i < times + samplePeriod; i++) {
            for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf,
                        sf.getSampleFolderId(), periodCount, timePerPeriod, i);
                source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i));
                target.addSamplingFrequency(targetSamplingFrequency);
            }

            //复制样品数据
            for (DtoSample sam : source.getSample()) {
                DtoSample targetSample = this.getSchemeCloneSample(sam);
                targetSample.setRedAnalyzeItems("");
                targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(), String.valueOf(i)));
                targetSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
                targetSample.setCycleOrder(periodCount);
                targetSample.setTimesOrder(timePerPeriod);
                targetSample.setSampleOrder(i);
                targetSample.setRedFolderName(this.getFolderName(sampleFolder, targetSample.getCycleOrder(),
                        targetSample.getTimesOrder(), targetSample.getSampleOrder()));
                target.addSample(targetSample);
            }
        }

        this.persistLoadScheme(target);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("增加了点位%s(%s)第%d周期第%d次样品数:%d个样。", sampleFolder.getWatchSpot(), sampleType.getTypeName(), periodCount, timePerPeriod,
                samplePeriod);
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加次数.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增次数, sampleFolder.getProjectId());
                    }
                }
        );

        target.setSampleFolder(source.getSampleFolder());
        return target;
    }

    /**
     * 复制周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param copyTimes      复制次数
     */
    @Override
    @Transactional
    public DtoLoadScheme copySchemePeriod(String sampleFolderId, Integer periodCount, Integer copyTimes) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();
        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findLoadSchemeByPeriod(sampleFolderId, periodCount);
        DtoSampleFolder sampleFolder = source.getSampleFolder().get(0);

        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdOrderByPeriodCountDesc(sampleFolderId);
        DtoSamplingFrequency maxPeriodFrequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);


        for (Integer i = 1; i <= copyTimes; i++) {
            for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf, sf.getSampleFolderId(),
                        maxPeriodFrequency.getPeriodCount() + i, sf.getTimePerPeriod(), sf.getSamplePerTime());

                source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i));
                target.addSamplingFrequency(targetSamplingFrequency);
            }
            for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
                DtoSamplingFrequencyTest targetSamplingFrequencyTest = this.getTargetSamplingFrequencyTest(sft,
                        sft.getSampleFolderId(),
                        source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId(), String.valueOf(i)));

                target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
            }

            //复制样品数据
            for (DtoSample sam : source.getSample()) {
                DtoSample targetSample = this.getSchemeCloneSample(sam);
                targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(), String.valueOf(i)));
                targetSample.setCycleOrder(maxPeriodFrequency.getPeriodCount() + i);
                targetSample.setRedFolderName(this.getFolderName(sampleFolder, targetSample.getCycleOrder(),
                        targetSample.getTimesOrder(), targetSample.getSampleOrder()));

                source.putNewSampleId(sam.getId(), targetSample.getId(), String.valueOf(i));
                target.addSample(targetSample);
            }

            //复制分析数据
            for (DtoAnalyseData analyseData : source.getAnalyseData()) {
                DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                targetAnalyseData.setSampleId(source.getNewSampleId(analyseData.getSampleId(), String.valueOf(i)));

                target.addAnalyseData(targetAnalyseData);
            }
        }

        this.persistLoadScheme(target);
        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("以点位%s(%s)第%d周期为模板复制了%d周期。", sampleFolder.getWatchSpot(), sampleType.getTypeName(), periodCount, copyTimes);
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.复制周期.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案复制周期, sampleFolder.getProjectId());
                    }
                }
        );

        target.setSampleFolder(source.getSampleFolder());
        return target;
    }

    /**
     * 复制批次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     * @param copyTimes      复制次数
     */
    @Override
    @Transactional
    public DtoLoadScheme copySchemeTimePeriod(String sampleFolderId, Integer periodCount, Integer timePeriod, Integer copyTimes) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();
        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findLoadSchemeByTimePeriod(sampleFolderId, periodCount, timePeriod);
        DtoSampleFolder sampleFolder = source.getSampleFolder().get(0);

        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdAndPeriodCountOrderByTimePerPeriodDesc(sampleFolderId, periodCount);
        DtoSamplingFrequency maxPeriodFrequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);


        for (Integer i = 1; i <= copyTimes; i++) {
            for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf, sf.getSampleFolderId(),
                        sf.getPeriodCount(), maxPeriodFrequency.getTimePerPeriod() + i, sf.getSamplePerTime());

                source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i));
                target.addSamplingFrequency(targetSamplingFrequency);
            }
            for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
                DtoSamplingFrequencyTest targetSamplingFrequencyTest = this.getTargetSamplingFrequencyTest(sft,
                        sft.getSampleFolderId(),
                        source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId(), String.valueOf(i)));

                target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
            }

            //复制样品数据
            for (DtoSample sam : source.getSample()) {
                DtoSample targetSample = this.getSchemeCloneSample(sam);
                targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(), String.valueOf(i)));
                targetSample.setTimesOrder(maxPeriodFrequency.getTimePerPeriod() + i);
                targetSample.setRedFolderName(this.getFolderName(sampleFolder, targetSample.getCycleOrder(),
                        targetSample.getTimesOrder(), targetSample.getSampleOrder()));

                source.putNewSampleId(sam.getId(), targetSample.getId(), String.valueOf(i));
                target.addSample(targetSample);
            }

            //复制分析数据
            for (DtoAnalyseData analyseData : source.getAnalyseData()) {
                DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                targetAnalyseData.setSampleId(source.getNewSampleId(analyseData.getSampleId(), String.valueOf(i)));

                target.addAnalyseData(targetAnalyseData);
            }
        }

        this.persistLoadScheme(target);
        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("以点位%s(%s)第%d周期第%d次为模板复制了%d次。", sampleFolder.getWatchSpot(),
                sampleType.getTypeName(), periodCount, timePeriod, copyTimes);
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.复制周期.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案复制批次, sampleFolder.getProjectId());
                    }
                }
        );

        target.setSampleFolder(source.getSampleFolder());
        return target;
    }

    /**
     * 复制样品数
     *
     * @param sampleFolderId      点位id
     * @param samplingFrequencyId 频次id
     * @param copyTimes           复制次数
     */
    @Override
    @Transactional
    public DtoLoadScheme copySchemeSamplingFrequency(String sampleFolderId, String samplingFrequencyId, Integer copyTimes) {
        //最终需要插入的数据载体
        DtoLoadScheme target = new DtoLoadScheme();

        //读取对应的复制方案相关信息
        DtoLoadScheme source = this.findLoadSchemeBySamplingFrequencyId(samplingFrequencyId);

        DtoSampleFolder sampleFolder = source.getSampleFolder().get(0);
        DtoSamplingFrequency samplingFrequency = source.getSamplingFrequency().get(0);

        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository
                .findBySampleFolderIdAndPeriodCountAndTimePerPeriodOrderBySamplePerTimeDesc(
                        sampleFolderId, samplingFrequency.getPeriodCount(), samplingFrequency.getTimePerPeriod());
        DtoSamplingFrequency maxFrequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);

        for (Integer i = 1; i <= copyTimes; i++) {
            for (DtoSamplingFrequency sf : source.getSamplingFrequency()) {
                DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sf, sf.getSampleFolderId(),
                        sf.getPeriodCount(), sf.getTimePerPeriod(), maxFrequency.getSamplePerTime() + i);

                source.putNewSamplingFrequencyId(sf.getId(), targetSamplingFrequency.getId(), String.valueOf(i));
                target.addSamplingFrequency(targetSamplingFrequency);
            }
            for (DtoSamplingFrequencyTest sft : source.getSamplingFrequencyTest()) {
                DtoSamplingFrequencyTest targetSamplingFrequencyTest = this.getTargetSamplingFrequencyTest(sft,
                        sft.getSampleFolderId(),
                        source.getNewSamplingFrequencyId(sft.getSamplingFrequencyId(), String.valueOf(i)));

                target.addSamplingFrequencyTest(targetSamplingFrequencyTest);
            }

            //复制样品数据
            for (DtoSample sam : source.getSample()) {
                DtoSample targetSample = this.getSchemeCloneSample(sam);
                targetSample.setSamplingFrequencyId(source.getNewSamplingFrequencyId(sam.getSamplingFrequencyId(), String.valueOf(i)));
                targetSample.setSampleOrder(maxFrequency.getSamplePerTime() + i);
                targetSample.setRedFolderName(this.getFolderName(sampleFolder, targetSample.getCycleOrder(),
                        targetSample.getTimesOrder(), targetSample.getSampleOrder()));

                source.putNewSampleId(sam.getId(), targetSample.getId(), String.valueOf(i));
                target.addSample(targetSample);
            }

            //复制分析数据
            for (DtoAnalyseData analyseData : source.getAnalyseData()) {
                DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                targetAnalyseData.setSampleId(source.getNewSampleId(analyseData.getSampleId(), String.valueOf(i)));

                target.addAnalyseData(targetAnalyseData);
            }
        }

        this.persistLoadScheme(target);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("以点位%s(%s)第%d周期第%d次第%d个样为模板复制了%d次。", sampleFolder.getWatchSpot(), sampleType.getTypeName(),
                samplingFrequency.getPeriodCount(), samplingFrequency.getTimePerPeriod(), samplingFrequency.getSamplePerTime(), copyTimes);
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.复制次数.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案复制次数, sampleFolder.getProjectId());
                    }
                }
        );

        target.setSampleFolder(source.getSampleFolder());
        return target;
    }

    /**
     * 根据点位id删除样品信息
     *
     * @param projectId      项目id
     * @param sampleFolderId 点位id
     */
    @Override
    @Transactional
    public void deleteSampleFolder(String projectId, String sampleFolderId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select s.id,s.receiveId");
        stringBuilder.append(" from DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.sampleFolderId = :sampleFolderId ");
        values.put("sampleFolderId", sampleFolderId);
        List<Object[]> samples = comRepository.find(stringBuilder.toString(), values);
        Map<String, List<String>> recMap = new HashMap<>();

        //对需要删除的样品按照送样单id分组
        for (Object[] sample : samples) {
            String sampleId = (String) sample[0];
            String receiveId = (String) sample[1];
            if (!recMap.containsKey(receiveId)) {
                recMap.put(receiveId, new ArrayList<>());
            }
            recMap.get(receiveId).add(sampleId);
        }

        deleteSample(projectId, recMap);
    }

    /**
     * 根据频次id删除样品信息
     *
     * @param projectId            项目id
     * @param samplingFrequencyIds 频次id集合
     */
    @Override
    @Transactional
    public void deleteSamplingFrequency(String projectId, List<String> samplingFrequencyIds) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select s.id,s.receiveId");
        stringBuilder.append(" from DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.samplingFrequencyId in :samplingFrequencyIds ");
        values.put("samplingFrequencyIds", samplingFrequencyIds);
        List<Object[]> samples = comRepository.find(stringBuilder.toString(), values);
        Map<String, List<String>> recMap = new HashMap<>();
        for (Object[] sample : samples) {
            String sampleId = (String) sample[0];
            String receiveId = (String) sample[1];
            if (!recMap.containsKey(receiveId)) {
                recMap.put(receiveId, new ArrayList<>());
            }
            recMap.get(receiveId).add(sampleId);
        }
        deleteSample(projectId, recMap);
    }

    /**
     * 删除项目下的质控样
     *
     * @param projectId 项目id
     * @param sampleIds 质控样id
     */
    @Override
    @Transactional
    public void deleteQCSample(String projectId, List<String> sampleIds) {
        // 判定质控样数据状态是否已加入实验室检测单并提交
        checkQcAnalyseDataStatus(sampleIds);

        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select s.id,s.receiveId");
        stringBuilder.append(" from DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.id in :sampleIds ");
        values.put("sampleIds", sampleIds);
        List<Object[]> samples = comRepository.find(stringBuilder.toString(), values);
        Map<String, List<String>> recMap = new HashMap<>();
        for (Object[] sample : samples) {
            String sampleId = (String) sample[0];
            String receiveId = (String) sample[1];
            if (!recMap.containsKey(receiveId)) {
                recMap.put(receiveId, new ArrayList<>());
            }
            recMap.get(receiveId).add(sampleId);
        }
        deleteSample(projectId, recMap);
        businessSerialNumberService.clearBusinessSerialNumber(EnumPRO.EnumLogObjectType.样品, sampleIds, "sampleCode:" + PrincipalContextUser.getPrincipal().getOrgId());
    }

    @Override
    public void checkQcAnalyseDataStatus(List<String> qcSampleIds) {

        // 判定是否已经加入检测单并提交
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(qcSampleIds);
        List<String> worksheetFolderIds = analyseDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId)
                .filter(p -> !p.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(toList());
        List<DtoWorkSheetFolder> workSheetFolderList = workSheetFolderService.findAll(worksheetFolderIds);
        List<Integer> workStatus = Arrays.asList(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue(),
                EnumPRO.EnumWorkSheetStatus.新建.getValue(),
                EnumPRO.EnumWorkSheetStatus.已经保存.getValue());
        // 筛选出已经提交的工作单ids
        List<String> subWorksheetFolderIds = workSheetFolderList.stream().filter(p -> !workStatus.contains(p.getWorkStatus())).map(DtoWorkSheetFolder::getId).collect(toList());
        if (StringUtil.isNotEmpty(subWorksheetFolderIds)) {
            List<String> remindSampleId = analyseDataList.stream().filter(p -> subWorksheetFolderIds.contains(p.getWorkSheetFolderId())).map(DtoAnalyseData::getSampleId).distinct().collect(toList());
            String remindSampleCodes = StringUtil.isNotEmpty(remindSampleId) ?
                    sampleRepository.findAll(remindSampleId).stream().map(DtoSample::getCode).collect(Collectors.joining("、")) : "";
            throw new BaseException(String.format("样品: %s已加入检测单并提交,不允许删除", remindSampleCodes));
        }
    }

    /**
     * 提取生成样品编号方法
     *
     * @param targetProject                目标项目
     * @param dtoProjectType               项目类型
     * @param dtoSampleType                检测类型
     * @param parentSampleType             父级检测类型
     * @param targetSample                 目标样品
     * @param serialNumberConfigCreateList 需要新增的序列
     * @param serialNumberConfigUpdateList 需要修改的序列
     * @param source                       来源数据载体
     * @param target                       最终需要插入数据的数据载体
     * @param sam                          源样品
     */
    @Transactional
    public void setSampleCode(DtoProject targetProject, DtoProjectType dtoProjectType, DtoSampleType dtoSampleType, DtoSampleType parentSampleType,
                              DtoSample targetSample, List<DtoSerialNumberConfig> serialNumberConfigCreateList, List<DtoSerialNumberConfig> serialNumberConfigUpdateList,
                              DtoLoadOutSample source, DtoLoadOutSample target, DtoSample sam) {
        DtoGenerateSN targetGenerateSN = sampleCodeService.createSampleCode(targetProject, dtoProjectType, dtoSampleType, parentSampleType,
                null, targetSample.getSamplingTimeBegin(), false, PrincipalContextUser.getPrincipal().getUserId(), false, null);
        targetSample.setCode(targetGenerateSN.getCode());
        //sampleRepository.save(sample);
        //需要新增的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigCreate())) {
            serialNumberConfigCreateList.add(targetGenerateSN.getSerialNumberConfigCreate());
        }
        //需要修改的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigUpdate())) {
            serialNumberConfigUpdateList.add(targetGenerateSN.getSerialNumberConfigUpdate());
        }
        Integer count = sampleRepository.countByCode(targetSample.getCode());
        //生成的样品编号不重复才能进行保存
        if (count == 0) {
            source.putNewSampleId(sam.getId(), targetSample.getId());
            target.addSample(targetSample);
        }
    }

    /**
     * 设置样品编号
     *
     * @param project                      项目
     * @param projectType                  项目类型
     * @param targetTemp                   目标样品
     * @param samplingTimeBegin            采样时间
     * @param serialNumberConfigCreateList 需要新增的序列
     * @param serialNumberConfigUpdateList 需要修改的序列
     */
    protected Integer setSampleCode(DtoProject project, DtoProjectType projectType, DtoSample targetTemp, Date samplingTimeBegin, Boolean isCreate,
                                    List<DtoSerialNumberConfig> serialNumberConfigCreateList, List<DtoSerialNumberConfig> serialNumberConfigUpdateList,
                                    List<DtoBusinessSerialNumber> businessSerialNumberList) {
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(targetTemp.getReceiveId());
        DtoSampleType sampleType = sampleTypeService.findOne(targetTemp.getSampleTypeId());
        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(sampleType.getParentId());
        DtoGenerateSN targetGenerateSN = sampleCodeService.createSampleCode(project, projectType, sampleType,
                dtoSampleTypeParent, null, null, record, samplingTimeBegin, isCreate,
                PrincipalContextUser.getPrincipal().getUserId(), false, UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY, EnumPRO.EnumSampleCategory.原样.getValue(), -1, -1,
                false, "", null);
        targetTemp.setCode(targetGenerateSN.getCode());
        //需要新增的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigCreate())) {
            serialNumberConfigCreateList.add(targetGenerateSN.getSerialNumberConfigCreate());
        }
        //需要修改的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigUpdate())) {
            serialNumberConfigUpdateList.add(targetGenerateSN.getSerialNumberConfigUpdate());
        }

        DtoBusinessSerialNumber dtoBusinessSerialNumber = new DtoBusinessSerialNumber();
        dtoBusinessSerialNumber.setBusinessType(EnumPRO.EnumLogObjectType.样品.name());
        dtoBusinessSerialNumber.setBusinessId(targetTemp.getId());
        dtoBusinessSerialNumber.setBusinessNumber(targetTemp.getCode());
        dtoBusinessSerialNumber.setSerialNumberType(targetGenerateSN.getCurrentSerialNumberType());
        dtoBusinessSerialNumber.setPara0(targetGenerateSN.getCurrentPara0());
        dtoBusinessSerialNumber.setPara1(targetGenerateSN.getCurrentPara1());
        dtoBusinessSerialNumber.setPara2(targetGenerateSN.getCurrentPara2());
        dtoBusinessSerialNumber.setPara3(targetGenerateSN.getCurrentPara3());
        businessSerialNumberList.add(dtoBusinessSerialNumber);
        return sampleRepository.countByCode(targetTemp.getCode());
    }

    //#region 私有方法

    /**
     * 获取委托类的方案相关数据复制结构
     *
     * @param projectId 项目id
     * @return 委托类的方案相关数据复制结构
     */
    private DtoLoadScheme findLoadScheme(String projectId) {
        DtoLoadScheme dto = new DtoLoadScheme();
        //获取源样品，这个样品集合只返回了id,sampleFolderId,samplingFrequencyId,redAnalyzeItems数据
        //如果要获取多个字段数据，需要修改构造及查询方法，这么做是保证查询性能及负载
        List<DtoSample> sourceSamples = sampleRepository.findCopySampleByProjectId(projectId);
        dto.setSample(sourceSamples);
        //获取源点位
        List<DtoSampleFolder> sourceSampleFolders = sampleFolderRepository.findByProjectId(projectId);
        dto.setSampleFolder(sourceSampleFolders);
        List<DtoSamplingFrequency> sourceSamplingFrequencys = samplingFrequencyRepository.findBySampleFolderIdIn(dto.getSampleFolderIds());
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(dto.getSampleFolderIds());
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
            // 根据分析方法状态，剔除停用、废止的方法对应的测试项目
            testService.removeByMethodStatus(testList);
        }
        List<String> testIdList = testList.stream().map(DtoTest::getId).collect(toList());
        // 剔除频次信息中失效的测试项目
        sourceSamplingFrequencyTests.removeIf(p -> !testIdList.contains(p.getTestId()));
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setSamplingFrequency(sourceSamplingFrequencys);
        dto.setSamplingFrequencyTest(sourceSamplingFrequencyTests);
        //获取分析数据
        if (dto.getSampleIds().size() > 0) {
            List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findCopyBySampleIdIn(dto.getSampleIds());
            // 剔除分析数据中失效的测试项目
            sourceAnalyseDatas.removeIf(p -> !testIdList.contains(p.getTestId()));
            for (DtoAnalyseData data : sourceAnalyseDatas) {
                Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
                testOptional.ifPresent(p -> {
                    data.setAnalyseItemId(p.getAnalyzeItemId());
                    data.setAnalyzeMethodId(p.getAnalyzeMethodId());
                });
            }
            dto.setAnalyseData(sourceAnalyseDatas);
        }
        return dto;
    }

    /**
     * 获取委托类的方案相关数据复制结构
     *
     * @param sampleFolderIds 点位id集合
     * @return 委托类的方案相关数据复制结构
     */
    private DtoLoadScheme findLoadSchemeBySampleFolderIds(List<String> sampleFolderIds) {
        DtoLoadScheme dto = new DtoLoadScheme();
        //获取源样品
        List<DtoSample> sourceSamples = sampleRepository.findBySampleFolderIdIn(sampleFolderIds);
        dto.setSample(sourceSamples);

        //获取源点位
        List<DtoSampleFolder> sourceSampleFolders = sampleFolderRepository.findAll(sampleFolderIds);
        dto.setSampleFolder(sourceSampleFolders);
        List<DtoSamplingFrequency> sourceSamplingFrequencys = samplingFrequencyRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
        }
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setSamplingFrequency(sourceSamplingFrequencys);
        dto.setSamplingFrequencyTest(sourceSamplingFrequencyTests);

        //获取分析数据
        List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(dto.getSampleIds());
        for (DtoAnalyseData data : sourceAnalyseDatas) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                data.setAnalyseItemId(p.getAnalyzeItemId());
                data.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setAnalyseData(sourceAnalyseDatas);

        return dto;
    }

    /**
     * 获取委托类的方案相关数据复制结构
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @return 委托类的方案相关数据复制结构
     */
    private DtoLoadScheme findLoadSchemeByPeriod(String sampleFolderId, Integer periodCount) {
        DtoLoadScheme dto = new DtoLoadScheme();
        //获取源点位
        DtoSampleFolder sourceSampleFolder = sampleFolderRepository.findOne(sampleFolderId);
        dto.setSampleFolder(Collections.singletonList(sourceSampleFolder));
        List<DtoSamplingFrequency> sourceSamplingFrequencys = samplingFrequencyRepository.findBySampleFolderIdAndPeriodCount(sampleFolderId, periodCount);
        dto.setSamplingFrequency(sourceSamplingFrequencys);
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(dto.getSamplingFrequencyIds());
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
        }
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setSamplingFrequencyTest(sourceSamplingFrequencyTests);
        //获取源样品
        List<DtoSample> sourceSamples = sampleRepository.findBySamplingFrequencyIdIn(dto.getSamplingFrequencyIds());
        dto.setSample(sourceSamples);
        //获取分析数据
        List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findCopyBySampleIdIn(dto.getSampleIds());
        for (DtoAnalyseData data : sourceAnalyseDatas) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                data.setAnalyseItemId(p.getAnalyzeItemId());
                data.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setAnalyseData(sourceAnalyseDatas);
        return dto;
    }

    /**
     * 获取委托类的方案相关数据复制结构
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @return 委托类的方案相关数据复制结构
     */
    private DtoLoadScheme findLoadSchemeByTimePeriod(String sampleFolderId, Integer periodCount, Integer timePeriod) {
        DtoLoadScheme dto = new DtoLoadScheme();
        //获取源点位
        DtoSampleFolder sourceSampleFolder = sampleFolderRepository.findOne(sampleFolderId);
        dto.setSampleFolder(Collections.singletonList(sourceSampleFolder));
        List<DtoSamplingFrequency> sourceSamplingFrequencys = samplingFrequencyRepository.findBySampleFolderIdAndPeriodCountAndTimePerPeriod(sampleFolderId,
                periodCount, timePeriod);
        dto.setSamplingFrequency(sourceSamplingFrequencys);
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(dto.getSamplingFrequencyIds());
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
        }
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setSamplingFrequencyTest(sourceSamplingFrequencyTests);
        //获取源样品
        List<DtoSample> sourceSamples = sampleRepository.findBySamplingFrequencyIdIn(dto.getSamplingFrequencyIds());
        dto.setSample(sourceSamples);
        //获取分析数据
        List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findCopyBySampleIdIn(dto.getSampleIds());
        for (DtoAnalyseData data : sourceAnalyseDatas) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                data.setAnalyseItemId(p.getAnalyzeItemId());
                data.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setAnalyseData(sourceAnalyseDatas);
        return dto;
    }

    /**
     * 获取委托类的方案相关数据复制结构
     *
     * @param samplingFrequencyId 点位频次id
     * @return 委托类的方案相关数据复制结构
     */
    private DtoLoadScheme findLoadSchemeBySamplingFrequencyId(String samplingFrequencyId) {
        DtoLoadScheme dto = new DtoLoadScheme();
        //获取源样品
        List<DtoSample> sourceSamples = sampleRepository.findBySamplingFrequencyId(samplingFrequencyId);
        dto.setSample(sourceSamples);

        //获取源点位
        DtoSamplingFrequency sourceFrequency = samplingFrequencyRepository.findOne(samplingFrequencyId);
        DtoSampleFolder sourceSampleFolder = sampleFolderRepository.findOne(sourceFrequency.getSampleFolderId());
        dto.setSampleFolder(Collections.singletonList(sourceSampleFolder));
        dto.setSamplingFrequency(Collections.singletonList(sourceFrequency));
        List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySamplingFrequencyId(samplingFrequencyId);
        Set<String> testIds = sourceSamplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findAll(testIds);
        }
        for (DtoSamplingFrequencyTest frequencyTest : sourceSamplingFrequencyTests) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> frequencyTest.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                frequencyTest.setAnalyseItemId(p.getAnalyzeItemId());
                frequencyTest.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setSamplingFrequencyTest(sourceSamplingFrequencyTests);

        //获取分析数据
        List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(dto.getSampleIds());
        for (DtoAnalyseData data : sourceAnalyseDatas) {
            Optional<DtoTest> testOptional = testList.stream().filter(t -> data.getTestId().equals(t.getId())).findFirst();
            testOptional.ifPresent(p -> {
                data.setAnalyseItemId(p.getAnalyzeItemId());
                data.setAnalyzeMethodId(p.getAnalyzeMethodId());
            });
        }
        dto.setAnalyseData(sourceAnalyseDatas);

        return dto;
    }

    /**
     * 获取委托类的方案相关数据复制结构
     *
     * @param sampleFolderId 点位id
     * @return 委托类的方案相关数据复制结构
     */
    private DtoLoadScheme findEmptyLoadSchemeBySampleFolderId(String sampleFolderId) {
        //获取该点位下的随机一个频次，不包含指标信息
        DtoLoadScheme dto = new DtoLoadScheme();

        //获取源点位
        DtoSampleFolder sourceSampleFolder = sampleFolderRepository.findOne(sampleFolderId);
        List<DtoSamplingFrequency> sourceFrequencys = samplingFrequencyRepository.findBySampleFolderId(sampleFolderId);
        dto.setSampleFolder(Collections.singletonList(sourceSampleFolder));
        if (StringUtil.isNotEmpty(sourceFrequencys)) {
            dto.setSamplingFrequency(Collections.singletonList(sourceFrequencys.get(0)));
            //获取源样品
            List<DtoSample> sourceSamples = sampleRepository.findBySamplingFrequencyIdIn(dto.getSamplingFrequencyIds());
            dto.setSample(sourceSamples);
        } else {
            //新增周期频次
            DtoSamplingFrequency samplingFrequency = new DtoSamplingFrequency();
            samplingFrequency.setSampleFolderId(sampleFolderId);
            dto.setSamplingFrequency(Collections.singletonList(samplingFrequency));

            //新增样品
            DtoSample sample = new DtoSample();
            sample.setSampleFolderId(sampleFolderId);
            sample.setProjectId(sourceSampleFolder.getProjectId());
            sample.setSamplingFrequencyId(samplingFrequency.getId());
            sample.setSampleTypeId(sourceSampleFolder.getSampleTypeId());
            dto.setSample(Collections.singletonList(sample));
        }
        return dto;
    }

    /**
     * 插入数据
     *
     * @param load 委托数据载体
     */
    @Override
    public void persistLoadScheme(DtoLoadScheme load) {
        //先判断测试项目复制的测试项目是否存在
        if (load.getSamplingFrequencyTest().size() > 0) {
            proService.checkTestIsExists(load.getSamplingFrequencyTest());
        }
        if (load.getSample().size() > 0) {
            sampleRepository.batchInsert(load.getSample());
        }
        if (load.getSampleFolder().size() > 0) {
            sampleFolderRepository.save(load.getSampleFolder());
        }
        if (load.getSamplingFrequency().size() > 0) {
            samplingFrequencyService.save(load.getSamplingFrequency());
        }
        if (load.getSamplingFrequencyTest().size() > 0) {
            samplingFrequencyTestRepository.save(load.getSamplingFrequencyTest());
        }
        if (load.getAnalyseData().size() > 0) {
            analyseDataRepository.batchInsert(load.getAnalyseData());
            testService.incrementOrderNum(load.getAnalyseData().stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList()));
            dimensionService.incrementOrderNum(load.getAnalyseData().stream().map(DtoAnalyseData::getDimensionId).distinct().collect(toList()));
        }
    }

    /**
     * 获取样品的拷贝
     *
     * @param sourceSample 源样品
     * @return 拷贝样品
     */
    private DtoSample getSchemeCloneSample(DtoSample sourceSample) {
        DtoSample targetSample = new DtoSample();
        BeanUtils.copyProperties(sourceSample, targetSample);
        targetSample.setId(UUIDHelper.NewID());
        targetSample.setCode("");
        targetSample.setReceiveId(UUIDHelper.GUID_EMPTY);
        targetSample.setSamplingPersonId(UUIDHelper.GUID_EMPTY);
        targetSample.setInceptTime(new Date());
        targetSample.setSamplingTimeBegin(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        targetSample.setSamplingTimeEnd(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        targetSample.setStatus(EnumPRO.EnumSampleStatus.样品未采样.toString());
        targetSample.setSamplingConfig(EnumPRO.EnumSamplingConfig.未分配.getValue());
        targetSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue());
        targetSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
        targetSample.setAnanlyzeStatus(StringUtils.isNotNullAndEmpty(sourceSample.getRedAnalyzeItems()) ? EnumPRO.EnumAnalyzeStatus.不能分析.getValue() : EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
        targetSample.setStoreStatus(EnumPRO.EnumStoreStatus.不能存储.getValue());
        targetSample.setMakeStatus(EnumPRO.EnumMakeStatus.不需要制样.getValue());
        targetSample.setDataChangeStatus(EnumPRO.EnumSampleChangeStatus.未变更.getValue());
        targetSample.setCustomerCode("");
        targetSample.setMakeSamPerId(UUIDHelper.GUID_EMPTY);
        targetSample.setIsPrint(EnumPRO.EnumPrintStatus.未打印.getValue());
        targetSample.setIsKeep(false);
        targetSample.setKeepLongTime(0);
        targetSample.setLoseEfficacyTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        targetSample.setPack("");
        targetSample.setSampleWeight("");
        targetSample.setWeightOrQuantity("");
        targetSample.setSamColor("");
        targetSample.setSampleExplain("");
        targetSample.setVolume("");
        targetSample.setSamplingPlace("");
        targetSample.setRemark("");
        targetSample.setPreTreatmentCases("");
        targetSample.setUnqualifiedReason("");
        targetSample.setDisposeMeasure("");
        targetSample.setConsistencyValidStatus(0);
        targetSample.setSignerId(UUIDHelper.GUID_EMPTY);
        targetSample.setSignTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        targetSample.setSamplingPersonId(UUIDHelper.GUID_EMPTY);
        targetSample.setIsOutsourcing(EnumPRO.EnumOutSourcing.不分包.getValue());
        targetSample.setLastNewSubmitTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));

        targetSample.setCreateDate(new Date());
        targetSample.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        targetSample.setModifyDate(new Date());
        targetSample.setModifier(PrincipalContextUser.getPrincipal().getUserId());

        return targetSample;
    }

    /**
     * 获取数据的拷贝
     *
     * @param sourceAnalyseData 源数据
     * @return 拷贝数据
     */
    @Override
    public DtoAnalyseData getSchemeCloneAnalyseData(DtoAnalyseData sourceAnalyseData) {
        DtoAnalyseData targetAnalyseData = new DtoAnalyseData();
        BeanUtils.copyProperties(sourceAnalyseData, targetAnalyseData);
        targetAnalyseData.setId(UUIDHelper.NewID());
        targetAnalyseData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setSubId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setReceiveSubId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setTestValue("");
        targetAnalyseData.setTestValueD(BigDecimal.ZERO);
        targetAnalyseData.setTestOrignValue("");
        targetAnalyseData.setTestValueDstr("");
        targetAnalyseData.setStatus(EnumPRO.EnumAnalyseDataStatus.未测.toString());
        targetAnalyseData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.未测.getValue());
        targetAnalyseData.setDataChangeStatus(EnumPRO.EnumDataChangeStatus.未变更.getValue());
        targetAnalyseData.setAnalyzeTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        targetAnalyseData.setDataInputTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        //数据复制时如果是现场分析数据把分析人质空
        if (sourceAnalyseData.getIsCompleteField()) {
            targetAnalyseData.setAnalystName("");
            targetAnalyseData.setAnalystId(UUIDHelper.GUID_EMPTY);
        }
        analyseDataService.defaultDateValue(targetAnalyseData);
        targetAnalyseData.setIsDataEnabled(false);

        targetAnalyseData.setCreateDate(new Date());
        targetAnalyseData.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        targetAnalyseData.setModifyDate(new Date());
        targetAnalyseData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
        return targetAnalyseData;
    }

    /**
     * 获取样品点位名称
     *
     * @param sampleFolder  点位
     * @param periodCount   周期
     * @param timePerPeriod 次数
     * @return 返回样品点位名称
     */
    @Override
    public String getFolderName(DtoSampleFolder sampleFolder, Integer periodCount, Integer timePerPeriod, Integer samplePeriod) {
        String folderName = String.format("%s%s", StringUtils.isNotNullAndEmpty(sampleFolder.getWatchSpot()) ?
                sampleFolder.getWatchSpot() : "", StringUtils.isNotNullAndEmpty(sampleFolder.getFolderCode()) ? "_" + sampleFolder.getFolderCode() : "");
        if (StringUtils.isNotNullAndEmpty(folderName)) {
            folderName = String.format("%s(%d-%d-%d)", folderName, periodCount, timePerPeriod, samplePeriod);
        }
        return folderName;
    }

    /**
     * 按照末尾数字切割字符串
     *
     * @param str 字符串
     * @return 切割后的数组
     */
    private Object[] getStrSplit(String str) {
        Matcher matcher = PATTERN.matcher(str);
        try {
            if (matcher.find()) {
                return new Object[]{str.replace(matcher.group(), ""), Integer.valueOf(matcher.group())};
            } else {
                return new Object[]{str};
            }
        } catch (Exception e) {
            return new Object[]{str};
        }
    }


    @Transactional
    @Override
    public void deleteSample(String projectId, Map<String, List<String>> recMap) {
        HashSet<String> analyst = new HashSet<>();
        List<DtoOutSampleDelete> osdList = new ArrayList<>();
        for (String receiveId : recMap.keySet()) {
            if (UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                //没有送样单id则直接删除
                analyst.addAll(deleteSampleNotReceive(recMap.get(receiveId)));
            } else {
                //有送样单id根据送样单删除
                osdList.add(deleteSampleInReceive(receiveId, recMap.get(receiveId)));
            }
        }

        HashSet<String> workSheetFolder = new HashSet<>();
        List<String> delSampleIdInReceive = new ArrayList<>();
        if (osdList.size() > 0) {
            for (DtoOutSampleDelete osd : osdList) {
                analyst.addAll(osd.getAnalystIds());
                workSheetFolder.addAll(osd.getWorkSheetFolderIds());
                delSampleIdInReceive.addAll(osd.getAllLocalSampleIds());
                delSampleIdInReceive.addAll(osd.getInnerSampleIds());
            }
        }

        //原先存在检测单数据
        HashSet<String> person = new HashSet<>();
        if (workSheetFolder.size() > 0) {
            List<DtoWorkSheetFolder> folders = workSheetFolderRepository.findAll(new ArrayList<>(workSheetFolder));
            person.addAll(folders.stream().map(DtoWorkSheetFolder::getCheckerId).distinct().collect(Collectors.toList()));
            person.addAll(folders.stream().map(DtoWorkSheetFolder::getAuditorId).distinct().collect(Collectors.toList()));
            workSheetFolderService.checkWorkSheetFolder(new ArrayList<>(workSheetFolder));

            //删除样品后，若检测单内的原样和现场质控样都已删除，则删除该检测单
            List<String> deleteFolderIdList = new ArrayList<>();
            List<DtoAnalyseData> anaDateListForFolders = analyseDataRepository.findByWorkSheetFolderIdIn(new ArrayList<>(workSheetFolder));
            List<String> allSampleIdList = anaDateListForFolders.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> allSampleList = StringUtil.isNotEmpty(allSampleIdList) ? sampleRepository.findByIdInAndIsDeletedFalse(allSampleIdList) : new ArrayList<>();
            Map<String, List<DtoAnalyseData>> folderId2AnaDataListMap = anaDateListForFolders.stream().collect(Collectors.groupingBy(DtoAnalyseData::getWorkSheetFolderId));
            for (Map.Entry<String, List<DtoAnalyseData>> entry : folderId2AnaDataListMap.entrySet()) {
                List<String> folderSampleIdList = entry.getValue().stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<DtoSample> folderSampleList = allSampleList.stream().filter(p -> folderSampleIdList.contains(p.getId())).collect(Collectors.toList());
                //过滤掉已经删除的样品
                folderSampleList = folderSampleList.stream().filter(p -> !delSampleIdInReceive.contains(p.getId())).collect(Collectors.toList());
                boolean deleteFlag = true;
                for (DtoSample sample : folderSampleList) {
                    if (EnumPRO.EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())
                            || (!EnumPRO.EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())
                            && EnumLIM.EnumQCGrade.外部质控.getValue().equals(sample.getQcGrade()))) {
                        deleteFlag = false;
                        break;
                    }
                }
                if (deleteFlag) {
                    deleteFolderIdList.add(entry.getKey());
                }
            }
            if (StringUtil.isNotEmpty(deleteFolderIdList)) {
                workSheetFolderService.logicDeleteById(deleteFolderIdList);
            }
        }

        proService.checkProject(Collections.singletonList(projectId));

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.删除测试项目, projectId);
                        for (String receiveId : recMap.keySet()) {
                            proService.sendProMessage(EnumPRO.EnumProAction.删除测试项目, "", receiveId);
                        }
                        proService.sendProMessage(EnumPRO.EnumProAction.删除测试项目, "", "", new ArrayList<>(analyst), new ArrayList<>(person));
                    }
                }
        );
    }

    @Override
    @Transactional
    public void submitScheme(DtoWorkflowSign dtoWorkflowSign) {
        //设置下一步操作人名称
        DtoPerson dtoPerson = personService.findOne(dtoWorkflowSign.getNextOperatorId());
        dtoWorkflowSign.setNextOperator(StringUtil.isNotNull(dtoPerson) ? dtoPerson.getCName() : "");

        dtoWorkflowSign.setIsAutoStatus(false);
        DtoProject dtoProject = projectRepository.findOne(dtoWorkflowSign.getObjectId());
        try {
            String from = dtoProject.getStatus();
            String to = workflowService.submitSign(dtoWorkflowSign);
            //创建提交记录
            submitRecordService.createSubmitRecord(dtoProject.getId(), EnumPRO.EnumSubmitObjectType.方案.getValue(),
                    EnumPRO.EnumSubmitType.方案提交.getValue(), dtoWorkflowSign.getNextOperator(), dtoWorkflowSign.getOption(),
                    from, to);
            //更新项目状态表
            statusForProjectService.modifyStatus(from, to, dtoWorkflowSign, dtoProject);
            //更新项目状态
            dtoProject.setStatus(to);
            projectRepository.save(dtoProject);
            //更新日志
            newLogService.createProjectStatusUpdateLog(Collections.singletonList(dtoWorkflowSign.getObjectId()),
                    dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(), dtoWorkflowSign.getNextOperator());
        } catch (Exception e) {
            throw new BaseException("提交方案发生异常");
        }
    }

    /**
     * 删除未送样的样品
     *
     * @param sampleIds 样品id集合
     * @return 分析人id
     */
    private List<String> deleteSampleNotReceive(List<String> sampleIds) {
        DtoOutSampleDelete osd = findSchemeSampleDeleteNotReceive(sampleIds);

        //假删样品数据
        List<DtoSample> sampleDatas = new ArrayList<>();
        for (String sampleId : osd.getAllLocalSampleIds()) {
            DtoSample sampleData = new DtoSample();
            sampleData.setId(sampleId);
            sampleData.setReceiveId(UUIDHelper.GUID_EMPTY);
            sampleData.setIsDeleted(true);
            sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sampleData.setModifyDate(new Date());
            sampleDatas.add(sampleData);
        }
        if (sampleDatas.size() > 0) {
            comRepository.updateBatch(sampleDatas);
        }
        //删除质控数据
        if (osd.getQcIds().size() > 0) {
            qualityControlRepository.logicDeleteById(osd.getQcIds(), new Date());
        }
        //删除分析数据
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        for (String anaId : osd.getAnalyseDataIds()) {
            DtoAnalyseData anaData = new DtoAnalyseData();
            anaData.setId(anaId);
            anaData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
            anaData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
            anaData.setIsDeleted(true);
            anaData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            anaData.setModifyDate(new Date());
            anaDatas.add(anaData);
        }
        if (anaDatas.size() > 0) {
            comRepository.updateBatch(anaDatas);
        }

        return osd.getAnalystIds();
    }

    /**
     * 删除已送样的样品
     *
     * @param receiveId 送样单id
     * @param sampleIds 样品id集合
     * @return 删除结构
     */
    private DtoOutSampleDelete deleteSampleInReceive(String receiveId, List<String> sampleIds) {
        //获取删除结构
        DtoOutSampleDelete osd = this.findOutSampleDelete(sampleIds, receiveId, false);

        //假删样品数据
        List<DtoSample> sampleDatas = new ArrayList<>();
        for (String sampleId : osd.getAllLocalSampleIds()) {
            DtoSample sampleData = new DtoSample();
            sampleData.setId(sampleId);
            sampleData.setReceiveId(UUIDHelper.GUID_EMPTY);
            sampleData.setIsDeleted(true);
            sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sampleData.setModifyDate(new Date());
            sampleDatas.add(sampleData);
        }
        if (sampleDatas.size() > 0) {
            comRepository.updateBatch(sampleDatas);
        }
        if (osd.getInnerSampleIds().size() > 0) {
            //真删室内样
            sampleRepository.deleteByIds(osd.getInnerSampleIds());
        }
        //删除质控数据
        if (osd.getQcIds().size() > 0) {
            qualityControlRepository.logicDeleteById(osd.getQcIds(), new Date());
        }
        //删除领样单样品关联
        receiveSubSampleRecord2SampleRepository.deleteBySampleIdIn(osd.getAllSampleIds(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
        //删除分析数据
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        for (String anaId : osd.getAnalyseDataIds()) {
            DtoAnalyseData anaData = new DtoAnalyseData();
            anaData.setId(anaId);
            anaData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
            anaData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
            anaData.setIsDeleted(true);
            anaData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            anaData.setModifyDate(new Date());
            anaDatas.add(anaData);
        }
        if (anaDatas.size() > 0) {
            comRepository.updateBatch(anaDatas);
        }
        if (osd.getInnerAnalyseDataIds().size() > 0) {
            //真删室内样数据
            analyseDataRepository.deleteByIds(osd.getInnerAnalyseDataIds());
        }

        List<DtoAnalyseData> analyseDatas = findReceiveAnalyseDatas(receiveId);
        if (analyseDatas.size() < osd.getAnalyseDatas().size()) {
            //若因为删除样品导致状态减少了（是否现场与数据状态的组合），需纠正送样单状态
            //不需要管是否导致领样单增删，纠正送样单那边会处理
            proService.checkReceiveSampleRecord(Collections.singletonList(receiveId));
        }

        return osd;
    }

    /**
     * 获取方案未送样的样品删除结构
     *
     * @param sampleIds
     * @return
     */
    private DtoOutSampleDelete findSchemeSampleDeleteNotReceive(List<String> sampleIds) {
        DtoOutSampleDelete osd = new DtoOutSampleDelete();

        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select qc.id");
        stringBuilder.append(" from DtoQualityControl qc where 1=1 and qc.isDeleted = 0");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and qc.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and qc.associateSampleId in :sampleIds ");
        stringBuilder.append(" and qc.qcGrade = :qcGrade ");
        stringBuilder.append(" and qc.qcType = :qcType ");
        values.put("sampleIds", sampleIds);
        values.put("qcGrade", EnumLIM.EnumQCGrade.外部质控.getValue());
        values.put("qcType", new QualityBlank().qcTypeValue());
        List<String> blankQcIds = comRepository.find(stringBuilder.toString(), values);

        values.clear();
        stringBuilder = new StringBuilder("select s.id,s.qcId,s.sampleCategory");
        stringBuilder.append(" from DtoSample s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.associateSampleId in :sampleIds ");
        if (blankQcIds.size() > 0) {
            stringBuilder.append(" and s.qcId not in :qcIds");
            values.put("qcIds", blankQcIds);
        }

        values.put("sampleIds", sampleIds);
        List<Object[]> associateSamples = comRepository.find(stringBuilder.toString(), values);
        if (associateSamples.size() > 0) {
            //先将原样+原样的放入室内质控样，其余放入质控样
            osd.setAssociateSampleIds(associateSamples.stream().map(p -> (String) p[0]).collect(Collectors.toList()));
            osd.setSampleIds(sampleIds.stream().filter(p -> !osd.getAssociateSampleIds().contains(p)).collect(Collectors.toList()));
            osd.setQcIds(associateSamples.stream().map(p -> (String) p[1]).collect(Collectors.toList()));
            osd.getQcIds().remove(UUIDHelper.GUID_EMPTY);
        } else {
            osd.setSampleIds(sampleIds);
        }

        values.clear();
        stringBuilder = new StringBuilder("select a.id,a.analystId");
        stringBuilder.append(" from DtoAnalyseData a where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and a.sampleId in :sampleIds ");
        values.put("sampleIds", osd.getAllSampleIds());
        List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);

        //数据id
        osd.setAnalyseDataIds(datas.stream().map(p -> (String) p[0]).collect(Collectors.toList()));
        osd.setAnalystIds(datas.stream().map(p -> (String) p[1]).distinct().collect(Collectors.toList()));

        return osd;
    }
    //#endregion

    //#endregion

    /**
     * 获取频次
     *
     * @param sourceSamplingFrequency 频次来源
     * @param targetSampleFolderId    目标点位id
     * @return 拷贝频次
     */
    private DtoSamplingFrequency getTargetSamplingFrequency(DtoSamplingFrequency sourceSamplingFrequency, String targetSampleFolderId) {
        DtoSamplingFrequency targetSamplingFrequency = new DtoSamplingFrequency();
        BeanUtils.copyProperties(sourceSamplingFrequency, targetSamplingFrequency);
        targetSamplingFrequency.setId(UUIDHelper.NewID());
        targetSamplingFrequency.setSampleFolderId(targetSampleFolderId);

        return targetSamplingFrequency;
    }

    /**
     * 获取频次
     *
     * @param sourceSamplingFrequency 频次来源
     * @param targetSampleFolderId    目标点位id
     * @return 拷贝频次
     */
    private DtoSamplingFrequency getTargetSamplingFrequency(DtoSamplingFrequency sourceSamplingFrequency,
                                                            String targetSampleFolderId, Integer periodCount,
                                                            Integer timePerPeriod, Integer sampleOrder) {
        DtoSamplingFrequency targetSamplingFrequency = this.getTargetSamplingFrequency(sourceSamplingFrequency, targetSampleFolderId);
        targetSamplingFrequency.setPeriodCount(periodCount);
        targetSamplingFrequency.setTimePerPeriod(timePerPeriod);
        targetSamplingFrequency.setSamplePerTime(sampleOrder);

        return targetSamplingFrequency;
    }

    /**
     * 获取频次指标
     *
     * @param sourceSamplingFrequencyTest 频次指标来源
     * @param targetSampleFolderId        目标点位id
     * @param targetSamplingFrequencyId   目标频次id
     * @return 拷贝频次指标
     */
    private DtoSamplingFrequencyTest getTargetSamplingFrequencyTest(DtoSamplingFrequencyTest sourceSamplingFrequencyTest, String targetSampleFolderId, String targetSamplingFrequencyId) {
        DtoSamplingFrequencyTest targetSamplingFrequencyTest = new DtoSamplingFrequencyTest();
        BeanUtils.copyProperties(sourceSamplingFrequencyTest, targetSamplingFrequencyTest);
        targetSamplingFrequencyTest.setId(UUIDHelper.NewID());
        targetSamplingFrequencyTest.setSampleFolderId(targetSampleFolderId);
        targetSamplingFrequencyTest.setSamplingFrequencyId(targetSamplingFrequencyId);

        return targetSamplingFrequencyTest;
    }

    /**
     * 复制评价标准记录
     *
     * @param oldObjectIdList   源对象id
     * @param evaluationType    评价标准类型
     * @param evaluationPlan    评价标准类型
     * @param oldObjId2NewIdMap 对象id映射
     */
    private void copyEvaluationRecord(List<String> oldObjectIdList, int evaluationType, int evaluationPlan, Map<String, String> oldObjId2NewIdMap) {
        List<DtoEvaluationRecord> oldRecordList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(oldObjectIdList, evaluationType, evaluationPlan);
        if (StringUtil.isNotEmpty(oldRecordList)) {
            List<DtoEvaluationRecord> newRecordList = new ArrayList<>();
            for (DtoEvaluationRecord record : oldRecordList) {
                DtoEvaluationRecord newRecord = new DtoEvaluationRecord();
                BeanUtils.copyProperties(record, newRecord, "id");
                newRecord.setObjectId(oldObjId2NewIdMap.get(record.getObjectId()));
                newRecordList.add(newRecord);
            }
            evaluationRecordRepository.save(newRecordList);
        }
    }

    /**
     * 复制比对数据
     *
     * @param oldSampleId2NewId 源样品id与复制样品id对应关系
     * @param analyseDataList   复制的分析项目数据
     */
    private void copySampleJudgeData(Map<String, String> oldSampleId2NewId, List<DtoAnalyseData> analyseDataList) {
        List<String> sourceSampleIds = new ArrayList<>(oldSampleId2NewId.keySet());
        List<DtoSampleJudgeData> sourceSampleJudgeDataList = StringUtil.isNotEmpty(sourceSampleIds) ? sampleJudgeDataRepository.findBySampleIdIn(sourceSampleIds) : new ArrayList<>();
        if (StringUtil.isNotEmpty(sourceSampleJudgeDataList)) {
            List<DtoSampleJudgeData> saveJudgeDataList = new ArrayList<>();
            for (String sourceSampleId : sourceSampleIds) {
                String newId = oldSampleId2NewId.getOrDefault(sourceSampleId, UUIDHelper.GUID_EMPTY);
                List<DtoSampleJudgeData> sampleJudgeData2SourceSample = sourceSampleJudgeDataList.stream().filter(s -> s.getSampleId().equals(sourceSampleId)).collect(toList());
                for (DtoSampleJudgeData judgeData : sampleJudgeData2SourceSample) {
                    if (analyseDataList.stream().anyMatch(a -> newId.equals(a.getSampleId()) && a.getTestId().equals(judgeData.getTestId()))) {
                        DtoSampleJudgeData save = new DtoSampleJudgeData();
                        BeanUtils.copyProperties(judgeData, save, "id", "sampleId", "onlineValue", "expectedValue", "qcRateValue", "pass", "creator", "createDate", "modifier", "modifyDate", "dataStatus");
                        save.setSampleId(newId);
                        saveJudgeDataList.add(save);
                    }
                }
            }
            if (StringUtil.isNotEmpty(saveJudgeDataList)) {
                sampleJudgeDataRepository.save(saveJudgeDataList);
            }
        }
    }

    /**
     * 生成比对质控样
     *
     * @param sampleList      原样品
     * @param analyseDataList 原样品分析数据
     */
    private void generateQCSampleJudageData(List<DtoSample> sampleList, List<DtoAnalyseData> analyseDataList, DtoProject project) {
        if (StringUtil.isNotEmpty(sampleList)) {
            List<String> analyzeItemIds = analyseDataList.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
            List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
            List<DtoCompareJudge> compareJudges = StringUtil.isNotEmpty(analyzeItemIds) ? compareJudgeRepository.findByAnalyzeItemIdIn(analyzeItemIds) : new ArrayList<>();
            List<String> compareIds = compareJudges.stream().map(DtoCompareJudge::getId).collect(Collectors.toList());
            List<DtoQualityControlLimit> compareJudgeConfigs = qualityControlLimitRepository.findByTestIdIn(compareIds);
            Map<String, List<DtoSample>> folderId2Sample = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleFolderId));
            List<DtoSampleJudgeData> saveList = new ArrayList<>();
            //要新的序列
            List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
            //要修改的序列
            List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
            List<DtoSample> qcSamples = new ArrayList<>();
            //获取源领样单
            List<DtoQualityControl> qualityControls = new ArrayList<>();
            folderId2Sample.forEach((folderId, samples) -> {
                if (!UUIDHelper.GUID_EMPTY.equals(folderId)) {
                    List<String> sampleIds2Folder = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
                    List<DtoAnalyseData> analyseDataList2Folder = analyseDataList.stream().filter(a -> sampleIds2Folder.contains(a.getSampleId())).collect(Collectors.toList());
                    List<String> analyzaItemIds2Folder = analyseDataList2Folder.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
                    DtoSampleType samType = sampleTypes.stream().filter(s -> s.getId().equals(samples.get(0).getSampleTypeId())).findFirst().get();
                    List<DtoCompareJudge> compareJudges2Folder = compareJudges.stream().filter(c -> analyzaItemIds2Folder.contains(c.getAnalyzeItemId()) && c.getCheckType().equals(samType.getCheckType())).collect(Collectors.toList());
                    for (DtoCompareJudge compareJudge : compareJudges2Folder) {
                        for (int i = 1; i <= compareJudge.getDefaultStandardNum(); i++) {
                            Map<String, Object> returnMap = sampleService.getAssociateSample(1, EnumLIM.EnumQCGrade.外部质控.getValue(),
                                    EnumLIM.EnumQCType.质控样.getValue(), samples.get(0), "", "", "", "", null,
                                    PrincipalContextUser.getPrincipal().getUserId(), null, project, null, "", null);
                            DtoSample qcSample = (DtoSample) returnMap.get("sample");
                            qcSample.setSampleCategory(EnumPRO.EnumSampleCategory.比对评价样.getValue());
                            qcSample.setSampleFolderId(folderId);
                            qcSample.setRedFolderName("质控样");
                            qcSample.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
                            qcSample.setSampleTypeName(samples.get(0).getSampleTypeName());
                            qcSample.setStatus(EnumPRO.EnumSampleStatus.样品在检.name());
                            DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
//                    DtoGenerateSN generateSN = (DtoGenerateSN) returnMap.get("generateSN");
                            DtoGenerateSN generateSN = sampleCodeService.createSampleCode(project,
                                    null,
                                    samples.get(0).getSampleTypeId(),
                                    samples.get(0).getSampleFolderId(),
                                    new Date(),
                                    PrincipalContextUser.getPrincipal().getUserId(),
                                    samples.get(0).getId(),
                                    true,
                                    PrincipalContextUser.getPrincipal().getUserId(), true,
                                    UUIDHelper.GUID_EMPTY,
                                    UUIDHelper.GUID_EMPTY,
                                    EnumPRO.EnumSampleCategory.质控样.getValue(), EnumLIM.EnumQCType.质控样.getValue(), 1, true, "1", null);
                            qcSample.setCode(generateSN.getCode());
                            String testId = analyseDataList2Folder.stream().filter(a -> compareJudge.getAnalyzeItemId().equals(a.getAnalyseItemId())).findFirst().get().getTestId();
                            DtoQualityControlLimit compareJudgeConfig = compareJudgeConfigs.stream().filter(c -> compareJudge.getId().equals(c.getTestId()) && EnumBase.EnumJudgingType.质控样比对.getValue().equals(c.getQcType())).findFirst().orElse(null);
                            if (compareJudgeConfig != null) {
                                DtoAnalyseData analyseData = analyseDataList2Folder.stream().filter(a -> a.getTestId().equals(testId)).findFirst().get();
                                DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
                                sampleJudgeData.setSampleId(qcSample.getId());
                                sampleJudgeData.setTestId(testId);
                                sampleJudgeData.setCheckType(compareJudge.getCheckType());
                                sampleJudgeData.setAllowLimit(compareJudgeConfig.getAllowLimit());
                                sampleJudgeData.setCheckItemValue(compareJudgeConfig.getRangeConfig());
                                sampleJudgeData.setCompareType(compareJudgeConfig.getQcType());
                                sampleJudgeData.setJudgingMethod(compareJudgeConfig.getJudgingMethod());
                                sampleJudgeData.setDimensionId(analyseData.getDimensionId());
                                saveList.add(sampleJudgeData);
                                qcSamples.add(qcSample);
                                qualityControls.add(qc);
                                if (StringUtil.isNotNull(generateSN)) {
                                    DtoSerialNumberConfig serialNumberConfigCreate = generateSN.getSerialNumberConfigCreate();
                                    if (StringUtil.isNotNull(serialNumberConfigCreate)) {
                                        serialNumberConfigCreateList.add(serialNumberConfigCreate);
                                    }
                                    DtoSerialNumberConfig serialNumberConfigUpdate = generateSN.getSerialNumberConfigUpdate();
                                    if (StringUtil.isNotNull(serialNumberConfigUpdate)) {
                                        serialNumberConfigUpdateList.add(serialNumberConfigUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            });
            if (qualityControls.size() > 0) {
                comRepository.insertBatch(qualityControls);
            }
            if (StringUtil.isNotEmpty(saveList)) {
                sampleJudgeDataRepository.save(saveList);
            }
            if (serialNumberConfigCreateList.size() > 0) {
                comRepository.insert(serialNumberConfigCreateList);
            }
            if (serialNumberConfigUpdateList.size() > 0) {
                comRepository.updateBatch(serialNumberConfigUpdateList);
            }
            if (qcSamples.size() > 0) {
                comRepository.insertBatch(qcSamples);
            }
        }
    }

    /**
     * 删除外部送样单下的样品
     *
     * @param ids               样品id
     * @param receiveId         送样单id
     * @param isClearSampleCode 是否清空样品编号
     */
    private void deleteOutsideSample(List<String> ids, String receiveId, boolean isClearSampleCode) {
        //获取删除结构
        DtoOutSampleDelete osd = this.findOutSampleDelete(ids, receiveId, true);

        List<DtoSample> samples = sampleRepository.findAll(ids);

        //假删样品数据
        List<DtoSample> sampleDatas = new ArrayList<>();
        for (String sampleId : osd.getAllLocalSampleIds()) {
            DtoSample sampleData = new DtoSample();
            sampleData.setId(sampleId);
            sampleData.setReceiveId(UUIDHelper.GUID_EMPTY);
            sampleData.setIsDeleted(true);
            sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sampleData.setModifyDate(new Date());
            if (isClearSampleCode) {
                sampleData.setCode("");
            }
            sampleDatas.add(sampleData);
        }
        if (!sampleDatas.isEmpty()) {
            sampleRepository.save(sampleDatas);
        }
        if (!osd.getInnerSampleIds().isEmpty()) {
            //真删室内样
            sampleRepository.deleteByIds(osd.getInnerSampleIds());
        }
        //删除质控数据
        if (!osd.getQcIds().isEmpty()) {
            qualityControlRepository.logicDeleteById(osd.getQcIds(), new Date());
        }
        //删除领样单样品关联
        receiveSubSampleRecord2SampleRepository.deleteBySampleIdIn(osd.getAllSampleIds(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
        //删除分析数据
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        for (String anaId : osd.getAnalyseDataIds()) {
            DtoAnalyseData anaData = new DtoAnalyseData();
            anaData.setIsDeleted(true);
            anaData.setId(anaId);
            anaData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
            anaData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
            anaData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            anaData.setModifyDate(new Date());
            anaDatas.add(anaData);
        }
        //获取需要删除的评价标准记录
        List<String> deleteEvaRecordIds = findDeletedEvaluationRecordIds(osd);
        //删除对应指标的评价标准记录
        if (StringUtil.isNotEmpty(deleteEvaRecordIds)) {
            evaluationRecordRepository.logicDeleteById(deleteEvaRecordIds, new Date());
        }

        if (!anaDatas.isEmpty()) {
            analyseDataRepository.save(anaDatas);
            //删除质控评价记录
            List<String> anaIdList = anaDatas.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            qualityControlEvaluateRepository.deleteByObjectIdIn(anaIdList, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        if (!osd.getInnerAnalyseDataIds().isEmpty()) {
            //真删室内样数据
            analyseDataRepository.deleteByIds(osd.getInnerAnalyseDataIds());
        }
        //删除频次信息
        if (!osd.getSamplingFrequencyIds().isEmpty()) {
            samplingFrequencyService.logicDeleteById(osd.getSamplingFrequencyIds());
            samplingFrequencyTestRepository.deleteBySamplingFrequencyIdIn(osd.getSamplingFrequencyIds());
        }

        //核对是否需要删除多余的点位
        List<String> sampleFolderIds = findReceiveSampleFolderIds(receiveId);
        osd.getSampleFolderIds().removeAll(sampleFolderIds);
        if (!osd.getSampleFolderIds().isEmpty()) {
            // 根据点位id查询样品，过滤其他送样单下与当前点位一致，并且存在未删除的样品数据。
            List<DtoSample> sampleByFolder = sampleRepository.findBySampleFolderIdIn(osd.getSampleFolderIds());
            // 筛选出不删除的点位id
            List<String> delNoFolderIds = sampleByFolder.stream().filter(p -> !p.getIsDeleted() && !ids.contains(p.getId())).map(DtoSample::getSampleFolderId).collect(Collectors.toList());
            List<String> delFolderIds = osd.getSampleFolderIds().stream().filter(p -> !delNoFolderIds.contains(p)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(delFolderIds)) {
                sampleFolderRepository.logicDeleteById(delFolderIds, new Date());
            }
        }
        //原先存在检测单数据
        HashSet<String> person = new HashSet<>();
        if (!osd.getWorkSheetFolderIds().isEmpty()) {
            List<DtoWorkSheetFolder> folders = workSheetFolderRepository.findAll(osd.getWorkSheetFolderIds());
            person.addAll(folders.stream().map(DtoWorkSheetFolder::getCheckerId).distinct().collect(Collectors.toList()));
            person.addAll(folders.stream().map(DtoWorkSheetFolder::getAuditorId).distinct().collect(Collectors.toList()));
            workSheetFolderService.checkWorkSheetFolder(osd.getWorkSheetFolderIds());
        }

        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : samples) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.删除样品.toString());
            log.setLogType(EnumPRO.EnumLogType.样品信息.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
            String comment = "删除了样品" + sampleService.getSampleName(sample, "");
            if (isClearSampleCode) {
                comment = comment + "; 并清除了样品编号";
            }
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumPRO.EnumLogType.样品信息.getValue());

        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(receiveId);
        String comment = String.format("在送样单%s中,%s", record.getRecordCode(), String.format("删除了样品：</br>%s。", String.join(",</br>", samples.stream().map(DtoSample::getCode).collect(Collectors.toList()))));

        List<DtoAnalyseData> analyseDatas = findReceiveAnalyseDatas(receiveId);
        if (analyseDatas.size() < osd.getAnalyseDatas().size()) {
            //若因为删除样品导致状态减少了（是否现场与数据状态的组合），需纠正送样单状态
            //不需要管是否导致领样单增删，纠正送样单那边会处理
            proService.checkReceiveSampleRecord(Collections.singletonList(receiveId));
        }

        //现场质控人员比对删除样品时需同步删除同编号样品
        handleLocalPeopleCompareSample(samples);

        newLogService.createLog(receiveId, comment, "",
                EnumPRO.EnumLogType.送样单样品信息.getValue(), EnumPRO.EnumLogObjectType.送样单.getValue(), EnumPRO.EnumLogOperateType.删除样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //清除编号流水号
        //清除样品编号的时候需要清除缓存
        String redisKey = "sampleCode:" + PrincipalContextUser.getPrincipal().getOrgId();
        businessSerialNumberService.clearBusinessSerialNumber(EnumPRO.EnumLogObjectType.样品,
                sampleDatas.stream().map(DtoSample::getId).collect(Collectors.toList()), redisKey);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.删除测试项目, record.getProjectId(), receiveId);
                        proService.sendProMessage(EnumPRO.EnumProAction.删除测试项目, "", "", osd.getAnalystIds(), new ArrayList<>(person));
                    }
                }
        );
    }
}
