package com.sinoyd.lims.pro.service.impl;


import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.AnalyzeItemSortDetialService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoPointExtendData;
import com.sinoyd.lims.monitor.dto.rcc.DtoWater;
import com.sinoyd.lims.monitor.entity.FixedPointProperty;
import com.sinoyd.lims.monitor.repository.lims.FixedPointPropertyRepository;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortDetilRepository;
import com.sinoyd.lims.monitor.repository.lims.Property2PointRepository;
import com.sinoyd.lims.monitor.repository.lims.PropertyPoint2TestRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedPointExpendRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.rcc.PointExtendDataRepository;
import com.sinoyd.lims.monitor.repository.rcc.WaterRepository;
import com.sinoyd.lims.pro.criteria.EnvironmentStatisticsCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoDataDetail;
import com.sinoyd.lims.pro.dto.customer.DtoDetailDataColumn;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.entity.Project2FixedProperty;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.DataEvaluateService;
import com.sinoyd.lims.pro.service.EnvironmentStatisticsService;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境质量统计操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/04/27
 * @since V100R001
 */
@Service
public class EnvironmentStatisticsServiceImpl extends BaseJpaServiceImpl<DtoProject, String, ProjectRepository> implements EnvironmentStatisticsService {

    @Autowired
    private FixedPointPropertyRepository fixedPointPropertyRepository;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    private Project2FixedPropertyRepository project2FixedPropertyRepository;

    @Autowired
    private FixedPointExpendRepository fixedPointExpendRepository;

    @Autowired
    private Property2PointRepository property2PointRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private DimensionRepository dimensionRepository;

    @Autowired
    private FixedPointSortDetilRepository fixedPointSortDetilRepository;

    @Autowired
    @Lazy
    private AnalyzeItemSortDetialService analyzeItemSortDetialService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleFolderRepository samplefolderRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private EnterpriseService enterpriseService;

    @Autowired
    private WaterRepository waterRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    @Lazy
    private DataEvaluateService dataEvaluateService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    private EvaluationRecordRepository evaluationRecordRepository;

    @Autowired
    private EvaluationValueRepository evaluationValueRepository;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private AnalyzeItemRepository analyzeItemRepository;

    @Autowired
    private PointExtendDataRepository pointExtendDataRepository;

    @Autowired
    private PropertyPoint2TestRepository propertyPoint2TestRepository;

    @Override
    public List<DtoProject> findDtoProject(PageBean<DtoProject> pb, BaseCriteria environmentStatisticsCriteria) {
        EnvironmentStatisticsCriteria criteria = (EnvironmentStatisticsCriteria) environmentStatisticsCriteria;
        //接收前端传的数组监测计划
        List<String> fixedPropertyId = criteria.getFixedPropertyId();
        if (StringUtil.isNotEmpty(fixedPropertyId)) {
            pb.setEntityName("DtoProject a,DtoSampleFolder s,DtoProject2FixedProperty p ");
        } else {
            pb.setEntityName("DtoProject a,DtoSampleFolder s ");
        }
        pb.setSelect("select a ");
        comRepository.findByPage(pb, environmentStatisticsCriteria);
        //将项目去重显示
        List<DtoProject> dtoProjects = new ArrayList<DtoProject>(new HashSet<DtoProject>(pb.getData()));
        dtoProjects.sort(Comparator.comparing(DtoProject::getInputTime, Comparator.reverseOrder()));
        //只显示环境质量的测试项目，通过唯一标识code
        List<DtoProjectType> projectType = projectTypeService.findByTypeCode("HJ");
        if (projectType.size() > 0) {
            List<String> typeIds = projectType.stream().map(DtoProjectType::getId).collect(Collectors.toList());
            dtoProjects = dtoProjects.parallelStream().filter(p -> typeIds.contains(p.getProjectTypeId())).collect(Collectors.toList());
        }
        return dtoProjects;
    }



    /**
     * 已选监测计划列表
     * @param projectId 任务id
     * @return 监测计划集合
     */
    @Override
    public List<DtoFixedPointProperty> getProjectProperty(String projectId) {
        List<String> propertyIds = project2FixedPropertyRepository.findByProjectId(projectId).stream()
                .map(Project2FixedProperty::getFixedPropertyId).collect(Collectors.toList());
        //监测子计划
        List<DtoFixedPointProperty> fixedPointPropertyList = fixedPointPropertyRepository.findByIdIn(propertyIds);
        List<String> parentIds = fixedPointPropertyList.stream().map(FixedPointProperty::getParentId).collect(Collectors.toList());
        //监测计划
        List<DtoFixedPointProperty> parentPropertyList = fixedPointPropertyRepository.findByIdIn(parentIds);
        for (DtoFixedPointProperty fixedPointProperty : fixedPointPropertyList) {
            parentPropertyList.stream().filter(p -> p.getId()
                    .equals(fixedPointProperty.getParentId())).findFirst().ifPresent(property -> fixedPointProperty.setPropertyName(String.format("%s-%s(%d)", property.getPropertyName(),
                    fixedPointProperty.getPropertyName(), fixedPointProperty.getMonth())));
        }
        return fixedPointPropertyList;
    }

    /**
     * 获取例行监测计划信息(树)
     *
     * @param month 月份
     * @param year  年份
     */
    @Override
    public List<TreeNode> getPointProperty(Integer year, Integer month) {
        if (month == null) {
            month = -1;
        }
        return projectService.getPointProperty("", year, month);
    }


    /**
     * 通过任务ids获取详细数据
     *
     * @param dataDetail   任务ids
     * @return 详细数据
     */
    @Override
    public Map<String, Object> findDetailDataByProjectList(DtoDataDetail dataDetail) {

        List<String> projectIds = dataDetail.getProjectIds();
        Boolean enableData = false;
        if (dataDetail.getEnableData() != null) {
            enableData = dataDetail.getEnableData();
        }
        Boolean overRed = false;
        if (dataDetail.getOverRed() != null) {
            overRed = dataDetail.getOverRed();
        }
        String itemSortId = dataDetail.getItemSortId();
        String pointSortId = dataDetail.getPointSortId();
        Boolean initialValue = false;
        Boolean maxValue = false;
        Boolean minValue = false;
        Boolean avgValue = false;

        if (dataDetail.getEvaluationType().size() > 0) {
            initialValue = dataDetail.getEvaluationType().contains("initialValue");
            maxValue = dataDetail.getEvaluationType().contains("maxValue");
            minValue = dataDetail.getEvaluationType().contains("minValue");
            avgValue = dataDetail.getEvaluationType().contains("avgValue");
        }

        Map<String, Object> resultMap = new HashMap<>();
        List<DtoDetailDataColumn> dtoDetailDataTests = new ArrayList<>();
        List<Map<String, Object>> analyseDataMapList = new ArrayList<>();

        //region 数据处理
        //通过项目ids获取样品数据
        List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIds);
        if (dataDetail.getPropertyIds() != null && dataDetail.getPropertyIds().size() > 0) {
            //通过计划过滤点位
            List<String> pointIds = property2PointRepository.findByPropertyIdIn(dataDetail.getPropertyIds()).stream()
                    .map(DtoProperty2Point::getFixedPointId).distinct().collect(Collectors.toList());
            List<String> folderIds = samplefolderRepository.findByProjectId(projectIds).stream()
                    .filter(p -> pointIds.contains(p.getFixedPointId())).map(DtoSampleFolder::getId).collect(Collectors.toList());
            sampleList = sampleList.stream().filter(p -> folderIds.contains(p.getSampleFolderId())).collect(Collectors.toList());
        }
        List<String> samIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());

        //详细数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(samIds);

        //送样单集合
        List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findByProjectIdIn(projectIds);

        //测试项目集合
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        List<String> analyzeItemIds = analyseDataList.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
        if (dataDetail.getPropertyIds() != null && dataDetail.getPropertyIds().size() > 0) {
            List<String> propertyPointIds = property2PointRepository.findByPropertyIdIn(dataDetail.getPropertyIds()).stream()
                    .map(DtoProperty2Point::getId).distinct().collect(Collectors.toList());
            //根据监测计划过滤测试项目
            List<DtoPropertyPoint2Test> propertyPoint2TestList = StringUtil.isNotEmpty(propertyPointIds) ?
                    propertyPoint2TestRepository.findByPropertyPointIdIn(propertyPointIds) :
                    new ArrayList<>();
            testIds = propertyPoint2TestList.stream().map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
            testList = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
            analyzeItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        }
        //获取数据下每个分析项目对应的量纲
        Map<String, DtoTest> anaItemToTest = new HashMap<>();
        testList.forEach(p -> anaItemToTest.put(p.getAnalyzeItemId(), p));
        List<DtoAnalyzeItem> analyzeItemList = StringUtil.isNotEmpty(analyzeItemIds) ? analyzeItemRepository.findAll(analyzeItemIds) : new ArrayList<>();
        //项目集合
        List<DtoProject> projectList = projectRepository.findAll(projectIds);

        //测试项目排序
        List<DtoAnalyzeItemSortDetail> analyzeItemSortDetails = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(itemSortId)) {
            analyzeItemSortDetails = analyzeItemSortDetialService.getSortDetailList(itemSortId);
        }
        //点位排序
        List<DtoFixedPointSortDetil> fixedPointSortDetils = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(pointSortId)) {
            fixedPointSortDetils = fixedPointSortDetilRepository.findBySortId(pointSortId);
        }

        //量纲集合
        List<String> dimensionIds = testList.stream().map(DtoTest::getDimensionId).distinct().collect(Collectors.toList());
        List<DtoDimension> dimensionList = new ArrayList<>();
        if (dimensionIds.size() > 0) {
            dimensionList = dimensionRepository.findAll(dimensionIds);
        }
        //检测类型
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = new ArrayList<>();
        if (sampleTypeIds.size() > 0) {
            sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
        }

        //所有点位
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoEvaluationValue> evaluationValueList = new ArrayList<>();
        List<DtoEvaluationRecord> evaluationRecordList = new ArrayList<>();
        if (overRed) {
            evaluationRecordList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(folderIds, EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue());
            //List<String> evaluationIds = evaluationRecordList.stream().map(DtoEvaluationRecord::getEvaluationId).distinct().collect(Collectors.toList());
            List<String> evaluationLevelIds = evaluationRecordList.stream().map(DtoEvaluationRecord::getEvaluationLevelId).distinct().collect(Collectors.toList());
            evaluationValueList = evaluationValueRepository.findByLevelIdIn(evaluationLevelIds);
        }
        //所有样品关联的企业
        List<String> samEntIds = sampleList.stream().map(DtoSample::getInspectedEntId).distinct().collect(Collectors.toList());
        List<DtoEnterprise> samEntList = new ArrayList<>();
        if (samEntIds.size() > 0) {
            samEntList = enterpriseService.findAll(samEntIds);
        }
        //endregion

        //数据源列处理
        handleSortAnalyseItemTest(dataDetail, analyzeItemSortDetails, analyzeItemList, dtoDetailDataTests, dimensionList, anaItemToTest);

        //region 添加最大值，最小值，平均值
        String splitString = ";";
        //样品关联的所有点位
        List<DtoFixedpoint> fixedpointList;
        List<DtoSampleFolder> sampleFolderList = new ArrayList<>();
        List<String> fixpointIds = new ArrayList<>();
        //点位与水体的关系
        Map<String, DtoWater> water2Point = new HashMap<>();
        if (folderIds.size() > 0) {
            //所有的点位
            sampleFolderList = samplefolderRepository.findAll(folderIds);
            //所有的配置点位+样品类型
            List<String> typeAndPointIds = sampleFolderList.stream().filter(p -> !p.getFixedPointId().equals(UUIDHelper.GUID_EMPTY)).map(p -> p.getFixedPointId() + splitString + p.getSampleTypeId())
                    .distinct().collect(Collectors.toList());
            List<String> folderAndPointIds = sampleFolderList.stream().filter(p -> p.getFixedPointId().equals(UUIDHelper.GUID_EMPTY))
                    .map(p -> p.getId() + splitString + p.getSampleTypeId())
                    .distinct().collect(Collectors.toList());
            //所有配置的点位
            fixpointIds = typeAndPointIds.stream().map(p -> p.split(splitString)[0]).distinct().collect(Collectors.toList());
            fixedpointList = fixedpointRepository.findByIdIn(fixpointIds);

            //所有点位关联的企业
            List<String> entIds = fixedpointList.stream().map(DtoFixedpoint::getEnterpriseId).filter(StringUtils::isNotNullAndEmpty).distinct().collect(Collectors.toList());
            List<DtoEnterprise> enterpriseList = new ArrayList<>();
            if (entIds.size() > 0) {
                enterpriseList = enterpriseService.findAll(entIds);
            }
            typeAndPointIds.addAll(folderAndPointIds);
            //根据点位和样品类型唯一 添加最大值，最小值，平均值
            for (String type2PointId : typeAndPointIds) {
                //配置的点位
                String fixpointId = type2PointId.split(splitString)[0];
                //样品类型
                String sampleTypeId = type2PointId.split(splitString)[1];
                DtoFixedpoint fixedpoint = fixedpointList.stream().filter(p -> p.getId().equals(fixpointId))
                        .findFirst().orElse(null);
                DtoSample sample = new DtoSample();
                if (fixedpoint != null) {//配置的点位
                    DtoEnterprise enterprise = enterpriseList.stream().filter(p -> p.getId().equals(fixedpoint.getEnterpriseId()))
                            .findFirst().orElse(null);
                    if (maxValue) {//最大值
                        sample = new DtoSample();
                        sample.setSampleFolderId(fixedpoint.getId());
                        sample.setCode("maxValue");
                        sample.setRedFolderName(fixedpoint.getPointName());
                        sample.setSampleTypeId(sampleTypeId);
                        sample.setInspectedEnt("");
                        sample.setInspectedEntId(fixedpoint.getEnterpriseId());
                        if (enterprise != null) {
                            sample.setInspectedEnt(enterprise.getName());
                        }
                        sampleList.add(sample);
                    }
                    if (minValue) {//最小值
                        sample = new DtoSample();
                        sample.setSampleFolderId(fixedpoint.getId());
                        sample.setCode("minValue");
                        sample.setRedFolderName(fixedpoint.getPointName());
                        sample.setSampleTypeId(sampleTypeId);
                        sample.setInspectedEnt("");
                        sample.setInspectedEntId(fixedpoint.getEnterpriseId());
                        if (enterprise != null) {
                            sample.setInspectedEnt(enterprise.getName());
                        }
                        sampleList.add(sample);
                    }
                    if (avgValue) {//平均值
                        sample = new DtoSample();
                        sample.setSampleFolderId(fixedpoint.getId());
                        sample.setCode("avgValue");
                        sample.setRedFolderName(fixedpoint.getPointName());
                        sample.setSampleTypeId(sampleTypeId);
                        sample.setInspectedEnt("");
                        sample.setInspectedEntId(fixedpoint.getEnterpriseId());
                        if (enterprise != null) {
                            sample.setInspectedEnt(enterprise.getName());
                        }
                        sampleList.add(sample);
                    }
                } else {//项目创建的点位
                    DtoSampleFolder sampleFolder = sampleFolderList.stream().filter(p -> p.getId().equals(fixpointId)).findFirst().orElse(null);
                    if (sampleFolder != null) {
                        if (maxValue) {//最大值
                            sample = new DtoSample();
                            sample.setSampleFolderId(sampleFolder.getId());
                            sample.setCode("maxValue");
                            sample.setRedFolderName(sampleFolder.getWatchSpot());
                            sample.setSampleTypeId(sampleTypeId);
                            sample.setInspectedEnt("");
                            sample.setInspectedEntId(UUIDHelper.GUID_EMPTY);
                            sampleList.add(sample);
                        }
                        if (minValue) {//最小值
                            sample = new DtoSample();
                            sample.setSampleFolderId(sampleFolder.getId());
                            sample.setCode("minValue");
                            sample.setRedFolderName(sampleFolder.getWatchSpot());
                            sample.setSampleTypeId(sampleTypeId);
                            sample.setInspectedEnt("");
                            sample.setInspectedEntId(UUIDHelper.GUID_EMPTY);
                            sampleList.add(sample);
                        }
                        if (avgValue) {//平均值
                            sample = new DtoSample();
                            sample.setSampleFolderId(sampleFolder.getId());
                            sample.setCode("avgValue");
                            sample.setRedFolderName(sampleFolder.getWatchSpot());
                            sample.setSampleTypeId(sampleTypeId);
                            sample.setInspectedEnt("");
                            sample.setInspectedEntId(UUIDHelper.GUID_EMPTY);
                            sampleList.add(sample);
                        }
                    }
                }
            }
        }
        //获取点位水体信息
        if (fixpointIds.size() > 0) {
            List<DtoPointExtendData> extendData = pointExtendDataRepository.findByFixedPointIdIn(fixpointIds);
            List<String> waterIds = extendData.stream()
                    .filter(p->"waterId".equals(p.getFiledAlias()))
                    .map(DtoPointExtendData::getFiledValue).filter(StringUtil::isNotEmpty)
                    .distinct().collect(Collectors.toList());
            //点位扩展
//            List<DtoFixedPointExpend> fixedPointExpendList = fixedPointExpendRepository.findByFixedPointIdIn(fixpointIds);
//            List<String> waterIds = fixedPointExpendList.stream().map(DtoFixedPointExpend::getWaterId)
//                    .filter(StringUtils::isNotNullAndEmpty).distinct().collect(Collectors.toList());
            List<DtoWater> waterList = new ArrayList<>();
            if (waterIds.size() > 0) {
                waterList = waterRepository.findAll(waterIds);
            }
            for (String pointId : fixpointIds) {
                DtoPointExtendData waterExtendData = extendData.stream().filter(p -> pointId.equals(p.getFixedPointId()) && "waterId".equals(p.getFiledAlias())).findFirst().orElse(null);
//                String waterId = fixedPointExpendList.stream().filter(p -> p.getFixedPointId().equals(pointId))
//                        .map(DtoFixedPointExpend::getWaterId).findFirst().orElse(null);
                String waterId = StringUtil.isNotNull(waterExtendData) ? waterExtendData.getFiledValue() : "";
                DtoWater water = new DtoWater();
                if (StringUtils.isNotNullAndEmpty(waterId)) {
                    water = waterList.stream().filter(p -> p.getId().equals(waterId)).findFirst().orElse(new DtoWater());
                }
                //点位与水体的关系
                water2Point.put(pointId, water);
            }
        }
        //endregion

        //region 样品数据组装
        for (DtoSample sample : sampleList) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", sample.getId());
            map.put("code", "");
            map.put("pointOrder", -1000);
            map.put("valueOrder", -1);
            map.put("waterCode", "");//水体编号
            map.put("waterName", "");//水体名称
            map.put("inspectedEnt", sample.getInspectedEnt());//受检单位
            map.put("inspectedEntId", sample.getInspectedEntId());//受检单位
            map.put("redFolderName", sample.getRedFolderName());//点位名称
            map.put("sampleFolderId", sample.getSampleFolderId());//点位id
            map.put("projectId", sample.getProjectId());//项目id
            DtoWater water = new DtoWater();
            DtoEvaluationValue evaluationValue = new DtoEvaluationValue();
            if (sample.getCode().contains("Value")) {
                water = water2Point.get(sample.getSampleFolderId());
                map.put("code", "--");//样品编号
                map.put("samplingTimeBegin", "--");//采样日期
                map.put("projectCode", "--");//项目编号
                map.put("projectName", "--");//项目名称
                map.put("redAnalyzeItems", "--");//测试项目
                map.put("sendTime", "--");//接样日期
                if (sample.getCode().equals("maxValue")) {
                    map.put("evaluateType", "最大值");//评价类型
                    map.put("valueOrder", 1);
                }
                if (sample.getCode().equals("minValue")) {
                    map.put("evaluateType", "最小值");//评价类型
                    map.put("valueOrder", 2);
                }
                if (sample.getCode().equals("avgValue")) {
                    map.put("evaluateType", "点位均值");//评价类型
                    map.put("valueOrder", 3);
                }
                fixedPointSortDetils.stream().filter(p -> p.getFixedPointId().equals(sample.getSampleFolderId()))
                        .findFirst().ifPresent(fixedPointSort -> map.put("pointOrder", fixedPointSort.getOrderNum()));
            } else {
                //是否显示原始值
                if (initialValue) {
                    DtoEnterprise enterprise = samEntList.stream().filter(p -> p.getId().equals(sample.getInspectedEntId())).findFirst().orElse(null);
                    if (enterprise != null) {
                        map.put("inspectedEnt", enterprise.getName());//受检单位
                    }
                    map.put("code", sample.getCode());//样品编号
                    String samplingTimeBegin = DateUtil.dateToString(sample.getSamplingTimeBegin(), DateUtil.YEAR);//时间
                    map.put("samplingTimeBegin", samplingTimeBegin);//采样日期
                    map.put("evaluateType", "原始值");//评价类型
                    map.put("valueOrder", 0);
                    DtoProject project = projectList.stream().filter(p -> p.getId().equals(sample.getProjectId()))
                            .findFirst().orElse(new DtoProject());
                    map.put("projectCode", project.getProjectCode());//项目编号
                    map.put("projectName", project.getProjectName());//项目名称
                    map.put("redAnalyzeItems", sample.getRedAnalyzeItems());//测试项目
                    map.put("sendTime", "");//接样日期
                    if (receiveSampleRecordList.size() > 0) {
                        DtoReceiveSampleRecord record = receiveSampleRecordList.stream().filter(p -> p.getId().equals(sample.getReceiveId()))
                                .findFirst().orElse(null);
                        if (record != null) {
                            String sendTime = DateUtil.dateToString(record.getSendTime(), DateUtil.YEAR);//时间
                            map.put("sendTime", sendTime);//接样日期
                        }
                    }

                    String folderId = sample.getSampleFolderId();
                    if (overRed) {
                        DtoEvaluationRecord evaluationRecord = evaluationRecordList.stream().filter(p -> p.getObjectId().equals(folderId))
                                .findFirst().orElse(null);
                        if (evaluationRecord != null) {
                            String levelId = evaluationRecord.getEvaluationLevelId();
                            evaluationValue = evaluationValueList.stream().filter(p -> p.getLevelId().equals(levelId)).findFirst().orElse(new DtoEvaluationValue());
                        }
                    }

                    DtoSampleFolder samFolder = sampleFolderList.stream().filter(p -> p.getId().equals(folderId)).findFirst().orElse(null);
                    if (samFolder != null) {
                        map.put("redFolderName", samFolder.getWatchSpot());//点位名称
                    }
                    String pointId = sampleFolderList.stream().filter(p -> p.getId().equals(folderId))
                            .map(DtoSampleFolder::getFixedPointId).findFirst().orElse(null);
                    if (StringUtils.isNotNullAndEmpty(pointId)) {
                        water = water2Point.get(pointId);
                        fixedPointSortDetils.stream().filter(p -> p.getFixedPointId().equals(pointId))
                                .findFirst().ifPresent(fixedPointSort -> map.put("pointOrder", fixedPointSort.getOrderNum()));
                    }
                }
            }
            map.put("sampleTypeId", sample.getSampleTypeId());//检测类型
            DtoSampleType sampleType = sampleTypeList.stream().filter(p -> p.getId().equals(sample.getSampleTypeId()))
                    .findFirst().orElse(null);
            map.put("sampleTypeName", "");//检测类型名称
            if (sampleType != null) {
                map.put("sampleTypeName", sampleType.getTypeName());//检测类型名称
            }
            if (water != null) {
                map.put("waterId", water.getId());//水体id
                map.put("waterCode", water.getWaterCode());//水体编号
                map.put("waterName", water.getWaterName());//水体名称
            }
            for (DtoDetailDataColumn column : dtoDetailDataTests) {
                String testValue = "";
                if (sample.getCode().contains("Value")) {//最大最小平均值
                    List<DtoAnalyseData> anaList = new ArrayList<>();
                    List<BigDecimal> testValueList = new ArrayList<>();
                    //找到复核条件的所有点位和
                    List<String> samFolderIds = sampleFolderList.stream().filter(p -> p.getSampleTypeId().equals(sample.getSampleTypeId())
                            && p.getFixedPointId().equals(sample.getSampleFolderId())).map(DtoSampleFolder::getId).distinct().collect(Collectors.toList());
                    if (samFolderIds.size() == 0) {
                        samFolderIds.add(sample.getSampleFolderId());
                    }
                    if (samFolderIds.size() > 0) {
                        //是否需要超标显示
                        if (overRed) {
                            String samFolderId = samFolderIds.get(0);
                            DtoEvaluationRecord evaluationRecord = evaluationRecordList.stream().filter(p -> p.getObjectId().equals(samFolderId))
                                    .findFirst().orElse(null);
                            if (evaluationRecord != null) {
                                String levelId = evaluationRecord.getEvaluationLevelId();
                                evaluationValue = evaluationValueList.stream().filter(p -> p.getLevelId().equals(levelId)).findFirst().orElse(new DtoEvaluationValue());
                            }
                        }
                        List<String> sIds = sampleList.stream().filter(p -> samFolderIds.contains(p.getSampleFolderId()))
                                .map(DtoSample::getId).distinct().collect(Collectors.toList());
                        anaList = analyseDataList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getTestValue())
                                && sIds.contains(p.getSampleId()) && p.getAnalyseItemId().equals(column.getTestId())).collect(Collectors.toList());
                        if (enableData) {
                            anaList = anaList.stream().filter(AnalyseData::getIsDataEnabled).collect(Collectors.toList());
                        }
                    }
                    if (anaList.size() == 0) {
                        testValue = "--";
                    }
                    testValueList = anaList.stream().filter(p -> MathUtil.isNumeral(p.getTestValue()))
                            .map(p -> MathUtil.getBigDecimal(p.getTestValue())).collect(Collectors.toList());
                    List<BigDecimal> ndValueList = anaList.stream().filter(p -> p.getTestValue().equals("ND") && MathUtil.isNumeral(p.getExamLimitValue()))
                            .map(p -> MathUtil.getBigDecimal(p.getExamLimitValue())).collect(Collectors.toList());

                    if (testValueList.size() > 0) {
                        //最大值
                        if (sample.getCode().equals("maxValue")) {
                            BigDecimal tv = testValueList.stream().max(BigDecimal::compareTo).get();
                            testValue = anaList.stream().filter(p -> MathUtil.isNumeral(p.getTestValue()) && MathUtil.getBigDecimal(p.getTestValue()).equals(tv)).map(DtoAnalyseData::getTestValue).findFirst().orElse("");
                        }
                        //最小值
                        if (sample.getCode().equals("minValue")) {
                            BigDecimal tv = testValueList.stream().min(BigDecimal::compareTo).get();
                            testValue = anaList.stream().filter(p -> MathUtil.isNumeral(p.getTestValue()) && MathUtil.getBigDecimal(p.getTestValue()).equals(tv)).map(DtoAnalyseData::getTestValue).findFirst().orElse("");
                        }
                        //平均值
                        if (sample.getCode().equals("avgValue")) {
                            testValueList.addAll(ndValueList);
                            testValue = testValueList.stream().reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(BigDecimal.valueOf(anaList.size()), 10, BigDecimal.ROUND_HALF_UP).toString();
                            //修约
                            testValue = proService.getDecimal(anaList.get(0).getMostSignificance(), anaList.get(0).getMostDecimal(), testValue);
                        }
                    }
                } else {
                    if (initialValue) {
                        //通过样品查找数据
                        DtoAnalyseData analyseData = analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())
                                && p.getAnalyseItemId().equals(column.getTestId())).findFirst().orElse(null);
                        if (analyseData != null) {
                            //找到当前的分析因子下的评价限值
                            if (overRed) {
                                DtoEvaluationRecord evaluationRecord = evaluationRecordList.stream().filter(p -> p.getObjectId().equals(sample.getSampleFolderId()))
                                        .findFirst().orElse(null);
                                if (evaluationRecord != null) {
                                    String levelId = evaluationRecord.getEvaluationLevelId();
                                    evaluationValue = evaluationValueList.stream()
                                            .filter(p -> p.getLevelId().equals(levelId) && analyseData.getAnalyseItemId().equals(p.getAnalyzeItemId()))
                                            .findFirst().orElse(new DtoEvaluationValue());
                                }
                            }
                            //判断数据是否需要检毕
                            if (enableData) {
                                if (analyseData.getIsDataEnabled()) {
                                    testValue = analyseData.getTestValue();
                                } else {
                                    testValue = "未检毕";
                                }
                            } else {
                                testValue = analyseData.getTestValue();
                            }
                        } else {
                            testValue = "--";
                        }
                    }
                }
                //判断数据是否需要判断超标
                if (overRed) {
                    if (!testValue.equals("--")) {
                        Boolean isPass = dataEvaluateService.getIsPass(evaluationValue, testValue);
                        //数据超标
                        if (!isPass) {
                            testValue = "Red:" + testValue;
                        }
                    }
                }
                map.put(column.getTestId(), testValue);
            }
            if (StringUtils.isNotNullAndEmpty(map.get("code"))) {
                analyseDataMapList.add(map);
            }
        }

        //按照点位排序进行排序
        analyseDataMapList.sort(Comparator.comparing(this::pointSort, Comparator.reverseOrder())
                .thenComparing(this::pointNameSort).thenComparing(this::valueSort)
                .thenComparing(this::codeSort).thenComparing(this::projectCodeSort));
        //endregion
        dtoDetailDataTests.sort(Comparator.comparing(DtoDetailDataColumn::getOrderNum).reversed());
        resultMap.put("test", dtoDetailDataTests);
        resultMap.put("analyseData", analyseDataMapList);
        return resultMap;
    }

    /**
     * 处理详细列
     *
     * @param dataDetail             数据详细
     * @param analyzeItemSortDetails 分析项目排序集合
     * @param testList               测试项目数据
     * @param dtoDetailDataTests     数据列
     * @param dimensionList          量纲数据
     */
    protected void handleSortAnalyseItemTest(DtoDataDetail dataDetail, List<DtoAnalyzeItemSortDetail> analyzeItemSortDetails, List<DtoTest> testList, List<DtoDetailDataColumn> dtoDetailDataTests, List<DtoDimension> dimensionList) {
        for (DtoTest test : testList) {
            DtoDetailDataColumn detailDataColumn = new DtoDetailDataColumn();
            detailDataColumn.setDimensionName("");
            dimensionList.stream()
                    .filter(p -> p.getId().equals(test.getDimensionId())).findFirst()
                    .ifPresent(dimension -> detailDataColumn.setDimensionName(dimension.getDimensionName()));
            detailDataColumn.setOrderNum(-1);
            if (analyzeItemSortDetails.size() > 0) {
                analyzeItemSortDetails.stream()
                        .filter(p -> p.getAnalyzeItemId().equals(test.getAnalyzeItemId())).findFirst().ifPresent(analyzeItemSortDetail -> detailDataColumn.setOrderNum(analyzeItemSortDetail.getOrderNum()));
            }
            detailDataColumn.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
            detailDataColumn.setRedYearSn(test.getRedYearSn());
            detailDataColumn.setTestId(test.getId());
            detailDataColumn.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
            detailDataColumn.setRedCountryStandard(test.getRedCountryStandard());
            dtoDetailDataTests.add(detailDataColumn);
        }

    }

    /**
     * 处理详细列
     *
     * @param dataDetail             数据详细
     * @param analyzeItemSortDetails 分析项目排序集合
     * @param analyzeItemList               分析项目数据
     * @param dtoDetailDataTests     数据列
     * @param dimensionList          量纲数据
     * @param anaItemToTest          分析项目对应的测试项目
     */
    protected void handleSortAnalyseItemTest(DtoDataDetail dataDetail, List<DtoAnalyzeItemSortDetail> analyzeItemSortDetails,
                                             List<DtoAnalyzeItem> analyzeItemList, List<DtoDetailDataColumn> dtoDetailDataTests,
                                             List<DtoDimension> dimensionList,Map<String,DtoTest> anaItemToTest) {
        // 根据是是否按照分析项目排序导出 处理数据
        if (StringUtil.isNotNull(dataDetail.getIsSortExport()) && dataDetail.getIsSortExport()) {
            List<DtoTest> testList = anaItemToTest.values().stream().collect(Collectors.toList());
            List<String> analyzeItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
            List<DtoTest> testListByItem = new ArrayList<>(testList);
            List<String> analyseItemIds = analyzeItemSortDetails.stream().map(DtoAnalyzeItemSortDetail::getAnalyzeItemId).collect(Collectors.toList());
            List<DtoAnalyzeItem> analyzeItemsBySort = StringUtil.isNotEmpty(analyseItemIds) ? analyzeItemRepository.findAll(analyseItemIds) : new ArrayList<>();
            List<DtoTest> testLsitBySort = StringUtil.isNotEmpty(analyseItemIds) ? testRepository.findByAnalyzeItemIdIn(analyseItemIds) : new ArrayList<>();
            //添加统计中不包含的测试项目
            testListByItem.addAll(testLsitBySort.stream().filter(p->!analyzeItemIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList()));
            analyzeItemList.addAll(analyzeItemsBySort);
            analyzeItemList = analyzeItemList.stream().distinct().collect(Collectors.toList());
            List<String> dimensionIds = testListByItem.stream().map(DtoTest::getDimensionId).collect(Collectors.toList());
            dimensionList.addAll(StringUtil.isNotEmpty(dimensionIds) ? dimensionRepository.findAll(dimensionIds) : new ArrayList<>());
            testListByItem.forEach(p->anaItemToTest.put(p.getAnalyzeItemId(),p));
        }

        for (DtoAnalyzeItem analyzeItem : analyzeItemList) {
            DtoDetailDataColumn detailDataColumn = new DtoDetailDataColumn();
            detailDataColumn.setDimensionName("无量纲");
            DtoTest test = anaItemToTest.get(analyzeItem.getId());
            if (test != null){
                dimensionList.stream()
                        .filter(p -> p.getId().equals(test.getDimensionId())).findFirst()
                        .ifPresent(dimension -> detailDataColumn.setDimensionName(StringUtil.isNotEmpty(dimension.getDimensionName()) ? dimension.getDimensionName() : "无量纲"));
                detailDataColumn.setRedYearSn(test.getRedYearSn());
                detailDataColumn.setRedCountryStandard(test.getRedCountryStandard());
            }
            detailDataColumn.setOrderNum(-1);
            if (analyzeItemSortDetails.size() > 0) {
                analyzeItemSortDetails.stream()
                        .filter(p -> p.getAnalyzeItemId().equals(analyzeItem.getId())).findFirst().ifPresent(analyzeItemSortDetail -> detailDataColumn.setOrderNum(analyzeItemSortDetail.getOrderNum()));
            }
            detailDataColumn.setRedAnalyzeMethodName(analyzeItem.getAnalyzeItemName());
            detailDataColumn.setTestId(analyzeItem.getId());
            detailDataColumn.setRedAnalyzeItemName(analyzeItem.getAnalyzeItemName());
            dtoDetailDataTests.add(detailDataColumn);
        }

    }

    //region 数据排序
    private Integer pointSort(Map<String, Object> map){
        return (Integer)map.get("pointOrder");
    }

    private String pointNameSort(Map<String, Object> map){
        return (String)map.get("redFolderName");
    }

    private Integer valueSort(Map<String, Object> map){
        return (Integer)map.get("valueOrder");
    }

    private String codeSort(Map<String, Object> map){
        return (String)map.get("code");
    }

    private String projectCodeSort(Map<String, Object> map){
        return (String)map.get("projectCode");
    }
    //endregion
}