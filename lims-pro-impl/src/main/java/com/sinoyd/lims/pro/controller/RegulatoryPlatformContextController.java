package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import com.sinoyd.lims.strategy.context.IRegulatoryPlatformContextService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 上海监管平台分析方法 Controller
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@RestController
@RequestMapping("/api/pro/regulatoryPlatform")
public class RegulatoryPlatformContextController extends ExceptionHandlerController<IRegulatoryPlatformContextService> {

    /**
     * 分页查询监管平台数据
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping("/page")
    public RestResponse<List<?>> findByPage(BaseRegulatoryPlatformCriteria criteria) {
        RestResponse<List<?>> response = new RestResponse<>();
        PageBean<?> pageBean = super.getPageBean();
        service.findByPage(criteria, pageBean, criteria.getMethodName());
        List<?> data = pageBean.getData();
        response.setRestStatus(StringUtil.isNotEmpty(data) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        response.setData(data);
        response.setCount(pageBean.getRowsCount());
        return response;
    }

    /**
     * 上海监管平台推送
     *
     * @param pushVO 推送参数
     * @return 无返回值
     */
    @PostMapping("/push")
    public RestResponse<Void> pushData(@RequestBody RegulatoryPlatformPushVO pushVO) {
        service.push(pushVO);
        return new RestResponse<>();
    }
}
