package com.sinoyd.lims.pro.strategy.context;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform.AbsRegulatoryPlatformRemote;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import com.sinoyd.lims.strategy.context.IRegulatoryPlatformContextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 上海监管平台远程策略上下文接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Service
@Slf4j
@SuppressWarnings({"rawtypes", "unchecked"})
public class RegulatoryPlatformContextServiceImpl implements IRegulatoryPlatformContextService {

    private Map<String, AbsRegulatoryPlatformRemote> strategyMap;

    @Override
    public JSONObject doRequest(String methodName, Map<String, Object> requestParams){
        return getStrategy(EnumPRO.EnumRegulatoryPlatformMethod.通用类型.getValue())
                .doRequest(methodName, requestParams);
    }

    @Override
    public List findByPage(BaseCriteria criteria, PageBean pageBean, String methodName) {
        return getStrategy(methodName).findByPage(criteria, pageBean);
    }

    @Override
    public void push(RegulatoryPlatformPushVO pushVo) {
        getStrategy(pushVo.getRpType()).push(pushVo);
    }

    /**
     * 根据枚举查询方法获取策略类
     *
     * @param methodName 监管平台查询方法
     * @return 策略类
     */
    private AbsRegulatoryPlatformRemote getStrategy(String methodName) {
        EnumPRO.EnumRegulatoryPlatformMethod rpMethod = EnumPRO.EnumRegulatoryPlatformMethod.getByMethodName(methodName);
        if (rpMethod == null) {
            log.error("方法错误: {}", "无此方法" + methodName);
            throw new RuntimeException("方法错误！");
        }
        return getStrategy(rpMethod.getBeanName(), rpMethod.name());
    }

    /**
     * 根据枚举值获取策略类
     *
     * @param value 枚举值
     * @return 策略类
     */
    private AbsRegulatoryPlatformRemote getStrategy(Integer value) {
        EnumPRO.EnumRegulatoryPlatformMethod rpMethod = EnumPRO.EnumRegulatoryPlatformMethod.getByValue(value);
        if (rpMethod == null) {
            log.error("监管平台类型错误: {}", "无此监管平台类型" + value);
            throw new RuntimeException("监管平台类型错误！");
        }
        return getStrategy(rpMethod.getBeanName(), rpMethod.name());
    }

    /**
     * 根据策略类名获取策略类
     *
     * @param beanName   监管平台类型策略类名
     * @param rpTypeName 监管平台类型
     * @return 策略类
     */
    private AbsRegulatoryPlatformRemote getStrategy(String beanName, String rpTypeName) {
        AbsRegulatoryPlatformRemote strategy = strategyMap.get(beanName);
        if (strategy == null) {
            log.error("监管平台类型未实现: {}", rpTypeName);
            throw new RuntimeException("监管平台类型未实现！");
        }
        return strategy;
    }

    @Autowired
    public void setStrategyMap(Map<String, AbsRegulatoryPlatformRemote> strategyMap) {
        this.strategyMap = strategyMap;
    }
}
