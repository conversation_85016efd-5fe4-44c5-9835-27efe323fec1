package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.*;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseOriginalJson;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlDetail;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * QualityControl操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@Service
public class QualityControlServiceImpl extends BaseJpaServiceImpl<DtoQualityControl, String, QualityControlRepository> implements QualityControlService {

    @Autowired
    @Lazy
    private ParamsPartFormulaService paramsPartFormulaService;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    @Lazy
    private CalculationService calculationService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private TestQCRangeService testQCRangeService;

    @Autowired
    @Lazy
    private TestQCRemindConfig2TestService testQCRemindConfig2TestService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    private QualityControlLimitRepository qualityControlLimitRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    @Lazy
    private TestExpandService testExpandService;

    @Autowired
    @Lazy
    private TestService testService;

    private DimensionService dimensionService;

    private QualityControlEvaluateRepository qualityControlEvaluateRepository;

    private IConfigService configService;

    @Autowired
    private SampleRepository sampleRepository;

    private QualityControlEvaluateService qualityControlEvaluateService;

    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    /**
     * 默认质控比例
     */
    private final Integer DEFAULT_REMIND_PERCENT = 10;

    /**
     * 默认合格率
     */
    private final Integer DEFAULT_PASS_RATE = 100;

    @Override
    public void findByPage(PageBean<DtoQualityControl> pb, BaseCriteria qualityControlCriteria) {
        pb.setEntityName("DtoQualityControl a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, qualityControlCriteria);
    }

    @Transactional
    @Override
    public DtoQualityControl update(DtoQualityControl entity) {
        DtoQualityControl dtoQualityControl = super.update(entity);
        List<String> dimIds = Arrays.asList(entity.getQcConcentrationDimensionId(), entity.getQcValueDimensionId(), entity.getQcVolumeDimensionId(), entity.getQcTestValueDimensionId(), entity.getRealSampleTestValueDimensionId());
        dimIds = dimIds.stream().filter(d -> StringUtils.isNotNullAndEmpty(d) && !UUIDHelper.GUID_EMPTY.equals(d)).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(dimIds)) {
            dimensionService.incrementOrderNum(dimIds);
        }
        boolean updateFlag = false;
        DtoAnalyseData dtoAnalyseData = StringUtil.isNotEmpty(entity.getAnalyseDataId()) ? analyseDataRepository.findOne(entity.getAnalyseDataId()) : null;
        if (StringUtil.isNotNull(dtoAnalyseData) && new QualityMark().qcTypeValue().equals(entity.getQcType())) {
            //更新加标测定值量纲到分析数据列表的量纲上
            String dimId = StringUtil.isNotEmpty(entity.getQcTestValueDimensionId()) ? entity.getQcTestValueDimensionId() : "";
            DtoDimension dimension = dimensionService.findOne(dimId);
            dtoAnalyseData.setDimension(StringUtil.isNotNull(dimension) ? dimension.getDimensionName() : "");
            dtoAnalyseData.setDimensionId(dimId);
            updateFlag = true;
        }
        //更新数据上有效位、小数位
        String analyseDataId = entity.getAnalyseDataId();
        if (proService.switchIsOpen(ProCodeHelper.LIM_JB_EDITABLE) && StringUtil.isNotEmpty(analyseDataId) && !UUIDHelper.GUID_EMPTY.equals(analyseDataId)) {
            if (StringUtil.isNotNull(dtoAnalyseData)) {
                updateFlag = true;
                dtoAnalyseData.setMostDecimal(entity.getMostDecimal() == null ? -1 : entity.getMostDecimal());
                dtoAnalyseData.setMostSignificance(entity.getMostSignificance() == null ? -1 : entity.getMostSignificance());
            }
            dtoQualityControl.setMostDecimal(entity.getMostDecimal());
            dtoQualityControl.setMostSignificance(entity.getMostSignificance());
        }
        if (updateFlag) {
            analyseDataRepository.save(dtoAnalyseData);
        }
        return dtoQualityControl;
    }

    @Override
    public DtoQualityControl calculateJBValue(String qcId, String anaId, String formulaId,
                                              Boolean isCalculate, List<Map<String, Object>> analyseDataAllMap,
                                              List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs) {
        Boolean isForceRenewal = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys.jbValue.judgment");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            isForceRenewal = Boolean.valueOf(configModel.getConfigValue());
        }
        DtoQualityControl dtoQualityControl = repository.findOne(qcId);
        if (StringUtil.isNotNull(dtoQualityControl)) {
            //加标或者空白加标
            if ((dtoQualityControl.getQcType().equals(new QualityMark().qcTypeValue()) ||
                    dtoQualityControl.getQcType().equals(new QualityBlankMark().qcTypeValue())) &&
                    dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())) {
                Optional<Map<String, Object>> analyseDataJBMapOptional = analyseDataAllMap.stream().filter(p -> p.get("id").equals(anaId)).findFirst();
                List<DtoQualityControlLimit> qualityControlLimitList = new ArrayList<>();
                if (analyseDataJBMapOptional.isPresent()) {
                    //测试项目id
                    String testId = (String) analyseDataJBMapOptional.get().get("testId");
                    qualityControlLimitList = qualityControlLimitRepository.findByTestId(testId);
                    //取出原样的数据
                    String associateSampleId = (String) analyseDataJBMapOptional.get().get("associateSampleId");
                    //质控样也会添加加标样，不用做原样的限制
                    Optional<Map<String, Object>> analyseDataYYMapOptional = analyseDataAllMap.stream()
                            .filter(p -> p.get("sampleId").equals(associateSampleId) && p.get("testId").equals(testId)).findFirst();
                    if (analyseDataYYMapOptional.isPresent()) {
                        String testValue = (String) analyseDataYYMapOptional.get().get("testValue");
                        dtoQualityControl.setTestValue(testValue);
                    }
                }
                if (StringUtils.isNotNullAndEmpty(dtoQualityControl.getRealSampleTestValue())
                        && StringUtils.isNotNullAndEmpty(dtoQualityControl.getQcTestValue()) && !isCalculate) {
                    getJbValue(dtoQualityControl, qualityControlLimitList);
                    return dtoQualityControl;
                }
                //是否传入加标数据源
                Integer mostSignificance = -1;
                Integer mostDecimal = -1;
                if (analyseDataJBMapOptional.isPresent()) {
                    DtoParamsPartFormula paramsPartFormula = findJBParamsPartFormula(formulaId);
                    if (StringUtil.isNotNull(paramsPartFormula)) {
                        mostSignificance = paramsPartFormula.getMostSignificance();
                        mostDecimal = paramsPartFormula.getMostDecimal();

                        String formula = paramsPartFormula.getFormula();
                        List<String> qcTestRstList = new ArrayList<>();
                        int cnt = 0;
                        //取出加标数据
                        Map<String, Object> analyseDataJBMap = analyseDataJBMapOptional.get();

                        Map<String, Object> map = analyseDataService.getCalculationMap(formula, analyseDataJBMap, testFormulaParamsConfigs);

                        //计算出结果
                        Object resultObj = calculationService.calculationExpression(formula, map);
                        String result = StringUtil.isNotNull(resultObj) ? String.valueOf(resultObj) : "";
                        if (StringUtil.isNotEmpty(result) && MathUtil.isNumeral(result)) {
                            String reviseRst = result;
                            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                reviseRst = proService.getDecimal(mostSignificance, mostDecimal, result);
                            }
                            qcTestRstList.add(reviseRst);
                            cnt++;
                        }
                        //判断加标样是否有室内平行样
                        String jbSampleId = (String) analyseDataJBMapOptional.get().get("sampleId"), jbTestId = (String) analyseDataJBMap.get("testId");
                        List<Map<String, Object>> analyseDataJbPxMap = analyseDataAllMap.stream().filter(p -> p.get("associateSampleId").equals(jbSampleId)
                                && p.get("testId").equals(jbTestId) && p.get("qcType").equals(new QualityParallel().qcTypeValue())
                                && p.get("sampleCategory").equals(EnumPRO.EnumSampleCategory.质控样.getValue())
                                && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.get("qcGrade"))).collect(Collectors.toList());
                        for (Map<String, Object> jbPxMap : analyseDataJbPxMap) {
                            Map<String, Object> jbPx = analyseDataService.getCalculationMap(formula, jbPxMap, testFormulaParamsConfigs);
                            //计算出结果
                            Object jbPxQcTestValObj = calculationService.calculationExpression(formula, jbPx);
                            String jbPxQcTestVal = String.valueOf(jbPxQcTestValObj);
                            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                jbPxQcTestVal = proService.getDecimal(mostSignificance, mostDecimal, jbPxQcTestVal);
                            }
                            if (StringUtil.isNotEmpty(jbPxQcTestVal) && MathUtil.isNumeral(jbPxQcTestVal)) {
                                qcTestRstList.add(jbPxQcTestVal);
                                cnt++;
                            }
                        }
                        if (StringUtil.isNotEmpty(analyseDataJbPxMap) && cnt > 0) {
                            //加标样存在平行样需要求均
                            BigDecimal sum = new BigDecimal(0);
                            for (String s : qcTestRstList) {
                                sum = sum.add(new BigDecimal(s));
                            }
                            result = sum.divide(new BigDecimal(cnt), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).toString();
                        }
                        List<DtoTest> jbTestList = testService.findRedisByIds(Collections.singletonList(analyseDataJBMap.getOrDefault("testId", "").toString()));
                        DtoTest jbTest = StringUtil.isNotEmpty(jbTestList) ? jbTestList.get(0) : null;
                        if (StringUtil.isNotNull(jbTest) && EnumBase.EnumReviseType.先比较再修约.getValue().equals(jbTest.getReviseType())) {
                            String qcTestValue = checkLimitForJb(result, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                            //修约后的结果
                            qcTestValue = proService.getDecimal(mostSignificance, mostDecimal, qcTestValue);
                            dtoQualityControl.setQcTestValue(qcTestValue);
                        } else {
                            //修约后的结果
                            String qcTestValue = proService.getDecimal(mostSignificance, mostDecimal, result);
                            qcTestValue = checkLimitForJb(qcTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                            //计算测定值
                            dtoQualityControl.setQcTestValue(qcTestValue);
                        }

                        //取出原样的数据
                        String associateSampleId = (String) analyseDataJBMap.get("associateSampleId");

                        //测试项目id
                        String testId = (String) analyseDataJBMap.get("testId");

                        //质控样也会添加加标样，不用做原样的限制
                        Optional<Map<String, Object>> analyseDataYYMapOptional = analyseDataAllMap.stream().filter(p -> p.get("sampleId").equals(associateSampleId) && p.get("testId").equals(testId)).findFirst();
                        String realSampleTestValue = "";
                        List<String> realValueList = new ArrayList<>();
                        if (analyseDataYYMapOptional.isPresent()) {
                            //取出YY数据
                            Map<String, Object> analyseDataYYMap = analyseDataYYMapOptional.get();
                            Map<String, Object> yyMap = analyseDataService.getCalculationMap(formula, analyseDataYYMap, testFormulaParamsConfigs);
                            //计算出结果
                            Object realSampleTestValueObj = calculationService.calculationExpression(formula, yyMap);
                            if (realSampleTestValueObj instanceof String) {
                                realSampleTestValue = (String) realSampleTestValueObj;
                            } else {
                                realSampleTestValue = String.valueOf(realSampleTestValueObj);
                            }
                            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                //先修约后计算
                                realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                            }
                            //先修约后计算
                            realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                            realValueList.add(realSampleTestValue);
                            String testValue = (String) analyseDataYYMap.get("testValue");
                            dtoQualityControl.setTestValue(testValue);
                        }
                        //是否存在对应原样的平行样
                        List<Map<String, Object>> analyseDataPxMap = analyseDataAllMap.stream().filter(p -> p.get("associateSampleId").equals(associateSampleId)
                                && p.get("testId").equals(testId) && p.get("qcType").equals(new QualityParallel().qcTypeValue())
                                && p.get("sampleCategory").equals(EnumPRO.EnumSampleCategory.质控样.getValue())).collect(Collectors.toList());
                        //现场平行样不带入均值计算
                        analyseDataPxMap = analyseDataPxMap.stream().filter(p -> EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.get("qcGrade")))
                                .collect(Collectors.toList());
                        for (Map<String, Object> pxMap : analyseDataPxMap) {
                            String realPxValue = "";
                            Map<String, Object> px = analyseDataService.getCalculationMap(formula, pxMap, testFormulaParamsConfigs);
                            //计算出结果
                            Object realSampleTestValueObj = calculationService.calculationExpression(formula, px);
                            if (realSampleTestValueObj instanceof String) {
                                realPxValue = (String) realSampleTestValueObj;
                            } else {
                                realPxValue = String.valueOf(realSampleTestValueObj);
                            }
                            //先修约后计算
                            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                realPxValue = proService.getDecimal(mostSignificance, mostDecimal, realPxValue);
                            }
                            if (StringUtil.isNotEmpty(realPxValue)) {
                                realValueList.add(realPxValue);
                            }
                        }
                        //存在平行需要求均
                        BigDecimal sum = new BigDecimal(0);
                        Integer count = 0;
                        for (Integer i = realValueList.size() - 1; i >= 0; i--) {
                            if (StringUtils.isNotNullAndEmpty(realValueList.get(i))) {
                                String inPxValue = realValueList.get(i);
                                try {
                                    BigDecimal value = new BigDecimal(inPxValue);
                                    sum = sum.add(value);
                                    count++;
                                } catch (Exception ex) {
                                    System.out.println(ex.getMessage());
                                }
                            }
                        }
                        if (count == 0) {
                            count = 1;
                        }
                        BigDecimal avg;
                        if (sum.compareTo(BigDecimal.ZERO) != 0) {
                            avg = sum.divide(new BigDecimal(count), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
                            if (isForceRenewal) {
                                if (StringUtil.isNotNull(jbTest) && EnumBase.EnumReviseType.先比较再修约.getValue().equals(jbTest.getReviseType())) {
                                    //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                                    realSampleTestValue = checkLimitForJb(avg.toString(), paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                    //最终样值为均值结果
                                    realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                                } else {
                                    //最终样值为均值结果
                                    realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, avg.toString());
                                    //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                                    realSampleTestValue = checkLimitForJb(realSampleTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                }
                            }
                        }
                        if (StringUtil.isNotNull(jbTest) && EnumBase.EnumReviseType.先比较再修约.getValue().equals(jbTest.getReviseType())) {
                            //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                            realSampleTestValue = checkLimitForJb(realSampleTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                            //最终样值为均值结果
                            realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                        } else {
                            //最终样值为均值结果
                            realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                            //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                            realSampleTestValue = checkLimitForJb(realSampleTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                        }
                        //计算样值
                        dtoQualityControl.setRealSampleTestValue(realSampleTestValue);
                    }
                }
                getJbValue(dtoQualityControl, qualityControlLimitList);
            }
        }
        if (StringUtil.isEmpty(dtoQualityControl.getQcValue())) {
            dtoQualityControl.setQcRecoverRate("");
        }
        return dtoQualityControl;
    }

    /**
     * 根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限对加标测定值及样值进行比较处理
     *
     * @param oriValue          原始值
     * @param paramsPartFormula 加标测得量公式
     * @param testId            测试项目id
     * @param sampleTypeId      样品类型id
     * @return 判断检出限处理后的数据
     */
    @Override
    public String checkLimitForJb(String oriValue, DtoParamsPartFormula paramsPartFormula, String testId, String sampleTypeId) {
        if (StringUtil.isNotEmpty(oriValue) && MathUtil.isNumeral(oriValue) && StringUtil.isNotNull(paramsPartFormula)) {
            if (paramsPartFormula.getUseTestLimit()) {
                //使用测试项目上配置的检出限进行判断处理
                if (StringUtil.isNotEmpty(testId)) {
                    String limitValue = getLimitValueForTest(testId, sampleTypeId);
                    if (StringUtil.isNotEmpty(limitValue) && MathUtil.isNumeral(limitValue)) {
                        BigDecimal limitValueDec = new BigDecimal(limitValue);
                        if (new BigDecimal(oriValue).compareTo(limitValueDec) < 0) {
                            //小于检出限，按照配置的计算方式进行处理
                            oriValue = handleLimitVal(oriValue, limitValueDec, paramsPartFormula.getCalculationMode());
                        }
                    }
                }
            } else {
                //使用加标测得量公式上配置的检出限进行判断处理
                String detectionLimit = paramsPartFormula.getDetectionLimit();
                if (MathUtil.isNumeral(detectionLimit) && new BigDecimal(oriValue).compareTo(new BigDecimal(detectionLimit)) < 0) {
                    //小于检出限，按照配置的计算方式进行处理
                    oriValue = handleLimitVal(oriValue, new BigDecimal(detectionLimit), paramsPartFormula.getCalculationMode());
                }
            }
        }
        return oriValue;
    }

    /**
     * 根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是检测单分析数据中配置的检出限对加标测定值及样值进行比较处理
     *
     * @param oriValue          原始值
     * @param paramsPartFormula 加标测得量公式
     * @param examLimitValue     检出限
     * @return 判断检出限处理后的数据
     */
    @Override
    public String checkLimitForJb(String oriValue, DtoParamsPartFormula paramsPartFormula, String examLimitValue) {
        if (StringUtil.isNotEmpty(oriValue) && MathUtil.isNumeral(oriValue) && StringUtil.isNotNull(paramsPartFormula)) {
            if (paramsPartFormula.getUseTestLimit()) {
                if (StringUtil.isNotEmpty(examLimitValue) && MathUtil.isNumeral(examLimitValue)) {
                    BigDecimal limitValueDec = new BigDecimal(examLimitValue);
                    if (new BigDecimal(oriValue).compareTo(limitValueDec) < 0) {
                        //小于检出限，按照配置的计算方式进行处理
                        oriValue = handleLimitVal(oriValue, limitValueDec, paramsPartFormula.getCalculationMode());
                    }
                }
            } else {
                //使用加标测得量公式上配置的检出限进行判断处理
                String detectionLimit = paramsPartFormula.getDetectionLimit();
                if (MathUtil.isNumeral(detectionLimit) && new BigDecimal(oriValue).compareTo(new BigDecimal(detectionLimit)) < 0) {
                    //小于检出限，按照配置的计算方式进行处理
                    oriValue = handleLimitVal(oriValue, new BigDecimal(detectionLimit), paramsPartFormula.getCalculationMode());
                }
            }
        }
        return oriValue;
    }

    /**
     * 获取测试项目上配置的检出限
     *
     * @param testId       原始值
     * @param sampleTypeId 检出限
     * @return 检出限
     */
    private String getLimitValueForTest(String testId, String sampleTypeId) {
        String limitValue = "";
        DtoTest test = testService.findOne(testId);
        if (StringUtil.isNotNull(test)) {
            limitValue = test.getExamLimitValue();
            List<DtoTestExpand> expands = testExpandService.findRedisByTestId(test.getId());
            if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
                Optional<DtoTestExpand> optionalTestExpand = expands.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).findFirst();
                if (!optionalTestExpand.isPresent()) {
                    optionalTestExpand = expands.stream().filter(p -> p.getSampleTypeId().equals(test.getSampleTypeId())).findFirst();
                }
                if (optionalTestExpand.isPresent()) {
                    DtoTestExpand dtoTestExpand = optionalTestExpand.get();
                    if (StringUtil.isNotNull(dtoTestExpand.getExamLimitValue())) {
                        limitValue = dtoTestExpand.getExamLimitValue();
                    }
                }
            }
        }
        return limitValue;
    }

    /**
     * 原始值小于检出限时，按照公式配置的计算方式做相应处理
     *
     * @param oriValue        原始值
     * @param detectionLimit  检出限
     * @param calculationMode 计算方式
     */
    private String handleLimitVal(String oriValue, BigDecimal detectionLimit, Integer calculationMode) {
        if (StringUtil.isNotNull(calculationMode) && !EnumLIM.EnumCalculationMode.原始值.getValue().equals(calculationMode)) {
            if (EnumLIM.EnumCalculationMode.取零.getValue().equals(calculationMode)) {
                oriValue = "0";
            } else if (EnumLIM.EnumCalculationMode.检出限一半.getValue().equals(calculationMode)) {
                oriValue = detectionLimit.divide(new BigDecimal("2"), BigDecimal.ROUND_HALF_EVEN).toString();
            }
        }
        return oriValue;
    }

    @Override
    public Map<String, Object> calculateJBValue(String realSampleTestValue, String qcTestValue, String testValue,
                                                String qcValue, String testId, Integer qcType) {
        List<String> valueList = new ArrayList<>();
        valueList.add(realSampleTestValue);
        valueList.add(qcTestValue);
        valueList.add(qcValue);
        valueList.add(testValue);
        List<DtoQualityControlLimit> limitList = qualityControlLimitRepository.findByTestId(testId).stream()
                .filter(p -> p.getQcType().equals(qcType)).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
        qualityControlLimit.setTestId(testId);
        qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.回收率.getValue());
        qualityControlLimit.setQcType(qcType);
        qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
        limitList.add(qualityControlLimit);
        if (limitList.size() > 0) {
            Map<String, Object> jbMap = new QualityMark().calculateDeviationValue(limitList, valueList);
            DtoQualityControlLimit limit = (DtoQualityControlLimit) jbMap.get("limit");
            if (StringUtil.isNotNull(limit)) {
                String range = String.format("%s不在%s范围内", EnumBase.EnumJudgingMethod.getName(limit.getJudgingMethod()), limit.getAllowLimit());
                String qcAddedValue = (String) jbMap.get("qcAddedValue");
                String qcRecoverRate = (String) jbMap.get("qcRate");
                Integer sign = new QualityMark().getQualityConfig().getMostSignificance();
                Integer md = new QualityMark().getQualityConfig().getMostDecimal();
                String recoverRateStr = proService.getDecimal(sign, md, qcRecoverRate);
                Boolean isPass = new QualityMark().deviationQualified(limit, recoverRateStr);
                isPass = StringUtil.isNotNull(isPass) ? isPass : true;
                map.put("qcAddedValue", qcAddedValue);
                map.put("qcRecoverRate", recoverRateStr);
                map.put("range", range);
                map.put("isPass", isPass);
            }
        }
        return map;
    }

    @Override
    @Transactional
    public void updateJBDimensionBatch(List<DtoQualityControlTemp> qualityControlTempList, String qcVolumeDimensionId,
                                       String qcValueDimensionId, String qcTestValueDimensionId, String realSampleTestValueDimensionId,
                                       String ssConcentrationDimensionId, String constantVolumeDimensionId) {
        List<DtoQualityControl> qualityControlList = new ArrayList<>();
        List<String> qcIdList = qualityControlTempList.stream().map(DtoQualityControlTemp::getQcId).distinct().collect(Collectors.toList());
        qcIdList = qcIdList.stream().filter(p -> StringUtil.isNotEmpty(p)).collect(Collectors.toList());
        List<DtoQualityControl> dtoQualityControlList = StringUtil.isNotEmpty(qcIdList) ? repository.findAll(qcIdList) : new ArrayList<>();
        for (DtoQualityControlTemp temp : qualityControlTempList) {
            DtoQualityControl dtoQualityControl = dtoQualityControlList.stream().filter(p -> p.getId().equals(temp.getQcId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoQualityControl)) {
                if ((dtoQualityControl.getQcType().equals(new QualityMark().qcTypeValue()) ||
                        dtoQualityControl.getQcType().equals(new QualityBlankMark().qcTypeValue())) &&
                        dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())) {
                    if (StringUtil.isNotEmpty(qcVolumeDimensionId)&&!UUIDHelper.GUID_EMPTY.equals(qcVolumeDimensionId)) {
                        dtoQualityControl.setQcVolumeDimensionId(qcVolumeDimensionId);
                    }
                    if (StringUtil.isNotEmpty(qcValueDimensionId)&&!UUIDHelper.GUID_EMPTY.equals(qcValueDimensionId)) {
                        dtoQualityControl.setQcValueDimensionId(qcValueDimensionId);
                    }
                    if (StringUtil.isNotEmpty(qcTestValueDimensionId)&&!UUIDHelper.GUID_EMPTY.equals(qcTestValueDimensionId)) {
                        dtoQualityControl.setQcTestValueDimensionId(qcTestValueDimensionId);
                    }
                    if (StringUtil.isNotEmpty(realSampleTestValueDimensionId)&&!UUIDHelper.GUID_EMPTY.equals(realSampleTestValueDimensionId)) {
                        dtoQualityControl.setRealSampleTestValueDimensionId(realSampleTestValueDimensionId);
                    }
                    if (StringUtil.isNotEmpty(ssConcentrationDimensionId)&&!UUIDHelper.GUID_EMPTY.equals(ssConcentrationDimensionId)){
                        dtoQualityControl.setSsConcentrationDimensionId(ssConcentrationDimensionId);
                    }
                    if (StringUtil.isNotEmpty(constantVolumeDimensionId)&&!UUIDHelper.GUID_EMPTY.equals(constantVolumeDimensionId)){
                        dtoQualityControl.setConstantVolumeDimensionId(constantVolumeDimensionId);
                    }
                    qualityControlList.add(dtoQualityControl);
                }
            }
        }
        if (StringUtil.isNotEmpty(qualityControlList)) {
            repository.save(qualityControlList);
        }
    }

    @Override
    @Transactional
    public List<DtoQualityControl> calculateJBValueBatch(List<DtoQualityControlTemp> qualityControlTempList) {
        List<DtoQualityControl> qualityControlList = new ArrayList<>();
        Map<String, String> anaId2RateMap = new HashMap<>();
        List<String> anaIdList = new ArrayList<>();
        List<String> qcIdList = qualityControlTempList.stream().map(DtoQualityControlTemp::getQcId).distinct().collect(Collectors.toList());
        qcIdList = qcIdList.stream().filter(p -> StringUtil.isNotEmpty(p)).collect(Collectors.toList());
        List<DtoQualityControl> dtoQualityControlList = StringUtil.isNotEmpty(qcIdList) ? repository.findAll(qcIdList) : new ArrayList<>();
        //获取加标公式列表
        List<String> formulaIdList = qualityControlTempList.stream().map(DtoQualityControlTemp::getFormulaId).distinct().collect(Collectors.toList());
        List<DtoParamsPartFormula> paramsPartFormulasAll = StringUtil.isNotEmpty(formulaIdList) ? paramsPartFormulaRepository.findByFormulaIdIn(formulaIdList) : new ArrayList<>();
        //倒序排序
        List<DtoParamsPartFormula> paramsPartFormulasJB = paramsPartFormulasAll.stream()
                .filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue())).collect(Collectors.toList());
        Boolean isForceRenewal = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys.jbValue.judgment");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            isForceRenewal = Boolean.valueOf(configModel.getConfigValue());
        }
        for (DtoQualityControlTemp temp : qualityControlTempList) {
            List<Map<String, Object>> analyseDataAllMap = temp.getAnalyseData();
            List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs = temp.getParamsConfig();
            DtoQualityControl dtoQualityControl = dtoQualityControlList.stream().filter(p -> p.getId().equals(temp.getQcId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoQualityControl)) {
                //加标或者空白加标
                if ((dtoQualityControl.getQcType().equals(new QualityMark().qcTypeValue()) ||
                        dtoQualityControl.getQcType().equals(new QualityBlankMark().qcTypeValue())) &&
                        dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())) {
                    Map<String, Object> analyseDataJBMap = analyseDataAllMap.stream().filter(p -> p.get("id").equals(temp.getAnaId()))
                            .findFirst().orElse(null);
                    List<DtoQualityControlLimit> qualityControlLimitList = new ArrayList<>();
                    //是否传入加标数据源
                    Integer mostSignificance = -1;
                    Integer mostDecimal = -1;
                    if (StringUtil.isNotNull(analyseDataJBMap)) {
                        //测试项目id
                        String testId = (String) analyseDataJBMap.getOrDefault("testId", UUIDHelper.GUID_EMPTY);
                        qualityControlLimitList = qualityControlLimitRepository.findByTestId(testId);
                        DtoParamsPartFormula paramsPartFormula = paramsPartFormulasJB.stream().filter(p -> p.getFormulaId().equals(temp.getFormulaId()))
                                .sorted(Comparator.comparing(DtoParamsPartFormula::getOrderNum).reversed()).findFirst().orElse(null);
                        if (StringUtil.isNotNull(paramsPartFormula)) {
                            mostSignificance = paramsPartFormula.getMostSignificance();
                            mostDecimal = paramsPartFormula.getMostDecimal();
                            String formula = paramsPartFormula.getFormula();
                            List<String> qcTestRstList = new ArrayList<>();
                            int cnt = 0;
                            //取出加标数据
                            Map<String, Object> map = analyseDataService.getCalculationMap(formula, analyseDataJBMap, temp.getParamsConfig());
                            //计算出结果
                            Object resultObj = calculationService.calculationExpression(formula, map);
                            String result = StringUtil.isNotNull(resultObj) ? String.valueOf(resultObj) : "";
                            if (StringUtil.isNotEmpty(result) && MathUtil.isNumeral(result)) {
                                String reviseRst = result;
                                if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                    reviseRst = proService.getDecimal(mostSignificance, mostDecimal, result);
                                }
                                qcTestRstList.add(reviseRst);
                                cnt++;
                            }
                            //判断加标样是否有室内平行样
                            String jbSampleId = (String) analyseDataJBMap.get("sampleId"), jbTestId = (String) analyseDataJBMap.get("testId");
                            List<Map<String, Object>> analyseDataJbPxMap = analyseDataAllMap.stream().filter(p -> p.get("associateSampleId").equals(jbSampleId)
                                    && p.get("testId").equals(jbTestId) && p.get("qcType").equals(new QualityParallel().qcTypeValue())
                                    && p.get("sampleCategory").equals(EnumPRO.EnumSampleCategory.质控样.getValue())
                                    && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.get("qcGrade"))).collect(Collectors.toList());
                            for (Map<String, Object> jbPxMap : analyseDataJbPxMap) {
                                Map<String, Object> jbPx = analyseDataService.getCalculationMap(formula, jbPxMap, testFormulaParamsConfigs);
                                //计算出结果
                                Object jbPxQcTestValObj = calculationService.calculationExpression(formula, jbPx);
                                String jbPxQcTestVal = String.valueOf(jbPxQcTestValObj);
                                if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                    jbPxQcTestVal = proService.getDecimal(mostSignificance, mostDecimal, jbPxQcTestVal);
                                }
                                if (StringUtil.isNotEmpty(jbPxQcTestVal) && MathUtil.isNumeral(jbPxQcTestVal)) {
                                    qcTestRstList.add(jbPxQcTestVal);
                                    cnt++;
                                }
                            }
                            if (StringUtil.isNotEmpty(analyseDataJbPxMap) && cnt > 0) {
                                //加标样存在平行样需要求均
                                BigDecimal sum = new BigDecimal(0);
                                for (String s : qcTestRstList) {
                                    sum = sum.add(new BigDecimal(s));
                                }
                                result = sum.divide(new BigDecimal(cnt), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).toString();
                            }
                            List<DtoTest> jbTestList = testService.findRedisByIds(Collections.singletonList(analyseDataJBMap.getOrDefault("testId", "").toString()));
                            DtoTest jbTest = StringUtil.isNotEmpty(jbTestList) ? jbTestList.get(0) : null;
                            if (StringUtil.isNotNull(jbTest) && EnumBase.EnumReviseType.先比较再修约.getValue().equals(jbTest.getReviseType())) {
                                String qcTestValue = checkLimitForJb(result, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                qcTestValue = proService.getDecimal(mostSignificance, mostDecimal, qcTestValue);
                                dtoQualityControl.setQcTestValue(qcTestValue);
                            } else {
                                //修约后的结果
                                String qcTestValue = proService.getDecimal(mostSignificance, mostDecimal, result);
                                qcTestValue = checkLimitForJb(qcTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                //计算测定值
                                dtoQualityControl.setQcTestValue(qcTestValue);
                            }


                            //取出原样的数据
                            String associateSampleId = (String) analyseDataJBMap.get("associateSampleId");
                            //质控样也会添加加标样，不用做原样的限制
                            Map<String, Object> analyseDataYYMap = analyseDataAllMap.stream()
                                    .filter(p -> p.get("sampleId").equals(associateSampleId) && p.get("testId").equals(testId)).findFirst().orElse(null);
                            String realSampleTestValue = "";
                            List<String> realValueList = new ArrayList<>();
                            if (StringUtil.isNotNull(analyseDataYYMap)) {
                                //取出YY数据
                                Map<String, Object> yyMap = analyseDataService.getCalculationMap(formula, analyseDataYYMap, testFormulaParamsConfigs);
                                //计算出结果
                                Object realSampleTestValueObj = calculationService.calculationExpression(formula, yyMap);
                                if (realSampleTestValueObj instanceof String) {
                                    realSampleTestValue = (String) realSampleTestValueObj;
                                } else {
                                    realSampleTestValue = String.valueOf(realSampleTestValueObj);
                                }
                                if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                    realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                                }
                                realValueList.add(realSampleTestValue);
                                String testValue = (String) analyseDataYYMap.get("testValue");
                                dtoQualityControl.setTestValue(testValue);
                            }
                            //是否存在对应原样的平行样
                            List<Map<String, Object>> analyseDataPxMap = analyseDataAllMap.stream().filter(p -> p.get("associateSampleId").equals(associateSampleId)
                                    && p.get("testId").equals(testId) && p.get("qcType").equals(new QualityParallel().qcTypeValue())
                                    && p.get("sampleCategory").equals(EnumPRO.EnumSampleCategory.质控样.getValue())).collect(Collectors.toList());
                            //现场平行样不带入均值计算
                            analyseDataPxMap = analyseDataPxMap.stream().filter(p -> EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.get("qcGrade")))
                                    .collect(Collectors.toList());
                            for (Map<String, Object> pxMap : analyseDataPxMap) {
                                String realPxValue = "";
                                Map<String, Object> px = analyseDataService.getCalculationMap(formula, pxMap, testFormulaParamsConfigs);
                                //计算出结果
                                Object realSampleTestValueObj = calculationService.calculationExpression(formula, px);
                                if (realSampleTestValueObj instanceof String) {
                                    realPxValue = (String) realSampleTestValueObj;
                                } else {
                                    realPxValue = String.valueOf(realSampleTestValueObj);
                                }
                                if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(paramsPartFormula.getReviseCalculationType())) {
                                    realPxValue = proService.getDecimal(mostSignificance, mostDecimal, realPxValue);
                                }
                                if (StringUtil.isNotEmpty(realPxValue)) {
                                    realValueList.add(realPxValue);
                                }
                            }
                            //存在平行需要求均
                            BigDecimal sum = new BigDecimal(0);
                            int count = 0;
                            for (int i = realValueList.size() - 1; i >= 0; i--) {
                                if (StringUtils.isNotNullAndEmpty(realValueList.get(i))) {
                                    String inPxValue = realValueList.get(i);
                                    try {
                                        BigDecimal value = new BigDecimal(inPxValue);
                                        sum = sum.add(value);
                                        count++;
                                    } catch (Exception ex) {
                                        System.out.println(ex.getMessage());
                                    }
                                }
                            }
                            count = count == 0 ? 1 : count;
                            BigDecimal avg = BigDecimal.ZERO;
                            if (sum.compareTo(BigDecimal.ZERO) != 0) {
                                avg = sum.divide(new BigDecimal(count), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
                                if (isForceRenewal) {
                                    if (StringUtil.isNotNull(jbTest) && EnumBase.EnumReviseType.先比较再修约.getValue().equals(jbTest.getReviseType())) {
                                        //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                                        realSampleTestValue = checkLimitForJb(avg.toString(), paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                        //最终样值为均值结果
                                        realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                                    } else {
                                        //最终样值为均值结果
                                        realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, avg.toString());
                                        //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                                        realSampleTestValue = checkLimitForJb(realSampleTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                    }
                                }
                            }
                            if (StringUtil.isNotNull(jbTest) && EnumBase.EnumReviseType.先比较再修约.getValue().equals(jbTest.getReviseType())) {
                                //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                                realSampleTestValue = checkLimitForJb(realSampleTestValue, paramsPartFormula, analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                                //最终样值为均值结果
                                realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, realSampleTestValue);
                            } else {
                                //最终样值为均值结果
                                realSampleTestValue = proService.getDecimal(mostSignificance, mostDecimal, avg.toString());
                                //比较检出限(根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限进行比较)
                                realSampleTestValue = checkLimitForJb(realSampleTestValue, paramsPartFormula,analyseDataJBMap.getOrDefault("examLimitValue", "").toString());
                            }
                            //计算样值
                            dtoQualityControl.setRealSampleTestValue(realSampleTestValue);
                        }
                    }
                    getJbValue(dtoQualityControl, qualityControlLimitList);
                    if (StringUtil.isEmpty(dtoQualityControl.getQcValue())) {
                        dtoQualityControl.setQcRecoverRate("");
                    }
                    anaId2RateMap.put(temp.getAnaId(), dtoQualityControl.getQcRecoverRate());
                    anaIdList.add(temp.getAnaId());
                    qualityControlList.add(dtoQualityControl);
                }
            }
        }
        //更新qcInfo
        List<DtoAnalyseData> anaDataListForJb = StringUtil.isNotEmpty(anaIdList) ? analyseDataRepository.findAll(anaIdList) : new ArrayList<>();
        if (StringUtil.isNotEmpty(anaDataListForJb)) {
            for (DtoAnalyseData anaData : anaDataListForJb) {
                if (anaId2RateMap.containsKey(anaData.getId())) {
                    anaData.setQcInfo(anaId2RateMap.get(anaData.getId()) + "%");
                }
            }
            analyseDataRepository.save(anaDataListForJb);
        }
        analyseDataRepository.flush();
        return qualityControlList;
    }


    @Override
    public Map<String, Object> calculateJBValue(String realSampleTestValue, String qcTestValue, String qcValue, String qcAddedValue, String formulaId) {
        DtoParamsPartFormula paramsPartFormula = findJBParamsPartFormula(formulaId);

        if (StringUtil.isNotNull(paramsPartFormula)) {
            return this.calculateJBValue(realSampleTestValue, qcTestValue, qcValue, qcAddedValue, paramsPartFormula.getMostSignificance(), paramsPartFormula.getMostDecimal());
        }
        return this.calculateJBValue(realSampleTestValue, qcTestValue, qcValue, qcAddedValue, -1, -1);
    }

    private DtoParamsPartFormula findJBParamsPartFormula(String formulaId) {
        //通过公式看是否配置加标公式
//        List<DtoParamsPartFormula> paramsPartFormulasAll = StringUtils.isNotNullAndEmpty(formulaId) && !UUIDHelper.GUID_EMPTY.equals(formulaId) ?
//                paramsPartFormulaService.findRedisByFormulaId(formulaId) : new ArrayList<>();
        //暂时从数据库获取加标公式
        List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaId(formulaId);

        //倒序排序
        List<DtoParamsPartFormula> paramsPartFormulasJB = paramsPartFormulasAll.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue()))
                .sorted(Comparator.comparing(DtoParamsPartFormula::getOrderNum).reversed())
                .collect(Collectors.toList());

        DtoParamsPartFormula paramsPartFormula = paramsPartFormulasJB.stream().findFirst().orElse(null);
        return paramsPartFormula;
    }

    private Map<String, Object> calculateJBValue(String realSampleTestValue, String qcTestValue, String qcValue, String qcAddedValue, Integer mostSignificance, Integer mostDecimal) {
        Map<String, Object> map = new HashMap<>();
        BigDecimal zz = new BigDecimal(0);//增值
        BigDecimal qcRecoverRate = new BigDecimal(0);//加标回收率
        BigDecimal baseValue1000 = new BigDecimal(1000);//倍数1000
        BigDecimal baseValue100 = new BigDecimal(100);//倍数100
        try {
            BigDecimal realSampleTestValueDecimal = MathUtil.isNumeral(realSampleTestValue) ? new BigDecimal(realSampleTestValue) : new BigDecimal(0);
            BigDecimal qcTestValueDecimal = MathUtil.isNumeral(qcTestValue) ? new BigDecimal(qcTestValue) : new BigDecimal(0);
            BigDecimal qcValueDecimal = MathUtil.isNumeral(qcValue) ? new BigDecimal(qcValue) : new BigDecimal(1);
            if (mostSignificance != -1 && mostDecimal != -1) {
                zz = new BigDecimal(proService.getDecimal(mostSignificance, mostDecimal, qcTestValueDecimal.subtract(realSampleTestValueDecimal).toString()));
            } else {
                zz = (qcTestValueDecimal.subtract(realSampleTestValueDecimal)).multiply(baseValue1000).divide(baseValue1000);
            }
            qcRecoverRate = zz.divide(qcValueDecimal, 20, BigDecimal.ROUND_HALF_UP).multiply(baseValue100);//加标回收率
        } catch (Exception ex) {
            throw new BaseException("加标回收率计算发生错误");
        }
        //保留三位有效位数
        String result = qcRecoverRate.abs().toString(); //绝对值
        //正好是三位整数
        if (result.indexOf(".") == 3) {
            result = qcRecoverRate.setScale(0, RoundingMode.HALF_EVEN).toString();
        } else {
            result = String.format("%s", qcRecoverRate.setScale(1, RoundingMode.HALF_EVEN).toString());
        }
        qcAddedValue = proService.getDecimal(mostSignificance, mostDecimal, zz.toString());
        map.put("qcAddedValue", qcAddedValue);
        map.put("qcRecoverRate", result);
        return map;
    }

    @Override
    public DtoQualityControlTemp getReplaceValue(DtoQualityControlTemp qualityControlTemp) {
        String qcId = qualityControlTemp.getQcId();
        DtoQualityControl dtoQualityControl = repository.findOne(qcId);
        String addition = "";           //加入量
        String casCode = "";            //Cas号
        String compoundName = "";       //化合物名称
        if (StringUtil.isNotNull(dtoQualityControl) &&
                dtoQualityControl.getQcType().equals(new QualityReplace().qcTypeValue()) &&
                dtoQualityControl.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())) {
            casCode = StringUtil.isNotEmpty(dtoQualityControl.getQcCode()) ? dtoQualityControl.getQcCode() : "";
            compoundName = StringUtil.isNotEmpty(dtoQualityControl.getQcVolume()) ? dtoQualityControl.getQcVolume() : "";
            addition = StringUtil.isNotEmpty(dtoQualityControl.getQcValue()) ? dtoQualityControl.getQcValue() : "";

        }
        qualityControlTemp.setCasCode(casCode);
        qualityControlTemp.setCompoundName(compoundName);
        qualityControlTemp.setAddition(addition);

        return qualityControlTemp;
    }

    @Override
    public Map<String, Object> calculateReplaceValue(String testValue, String addition, String formulaId) {
        DtoParamsPartFormula paramsPartFormula = findReplaceParamsPartFormula(formulaId);

        if (StringUtil.isNotNull(paramsPartFormula)) {
            return this.calculateReplaceValue(testValue, addition, paramsPartFormula.getMostSignificance(), paramsPartFormula.getMostDecimal());
        }
        return this.calculateReplaceValue(testValue, addition, -1, -1);
    }

    private DtoParamsPartFormula findReplaceParamsPartFormula(String formulaId) {
        //通过公式看是否配置替代公式
        List<DtoParamsPartFormula> paramsPartFormulasAll = StringUtils.isNotNullAndEmpty(formulaId) && !UUIDHelper.GUID_EMPTY.equals(formulaId) ?
                paramsPartFormulaService.findRedisByFormulaId(formulaId) : new ArrayList<>();

        //倒序排序
        List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulasAll.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.替代公式.getValue()))
                .sorted(Comparator.comparing(DtoParamsPartFormula::getOrderNum).reversed())
                .collect(Collectors.toList());

        DtoParamsPartFormula paramsPartFormula = paramsPartFormulaList.stream().findFirst().orElse(null);

        return paramsPartFormula;
    }

    private Map<String, Object> calculateReplaceValue(String testValue, String addition, Integer mostSignificance, Integer mostDecimal) {
        Map<String, Object> map = new HashMap<>();
        BigDecimal qcRecoverRate;       //替代回收率
        try {
            BigDecimal testValueDec = new BigDecimal(testValue);
            BigDecimal difference;      //出证结果和0的差值
            if (mostSignificance != -1 && mostDecimal != -1) {
                difference = new BigDecimal(proService.getDecimal(mostSignificance, mostDecimal, testValueDec.subtract(new BigDecimal("0")).toString()));
            } else {
                difference = testValueDec.subtract(new BigDecimal("0"));
            }
            //计算替代回收率
            qcRecoverRate = difference.divide(new BigDecimal(addition), 20, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));//加标回收率
        } catch (Exception ex) {
            map.put("qcRecoverRate", "");
            return map;
        }
        //保留三位有效位数
        String result = qcRecoverRate.abs().toString(); //绝对值
        //正好是三位整数
        if (result.indexOf(".") == 3) {
            result = qcRecoverRate.setScale(0, RoundingMode.HALF_EVEN).toString();
        } else {
            result = String.format("%s", qcRecoverRate.setScale(1, RoundingMode.HALF_EVEN).toString());
        }
        map.put("qcRecoverRate", StringUtil.isNotEmpty(result) ? result + "%" : result);
        return map;
    }

    @Override
    public Map<String, Object> calculateCorrectionFactorValue(String testValue, String qcValue, String formulaId) {
        DtoParamsPartFormula paramsPartFormula = findReplaceParamsPartFormula(formulaId);
        if (StringUtil.isNotNull(paramsPartFormula)) {
            return this.calculateCorrectionFactorValue(testValue, qcValue, paramsPartFormula.getMostSignificance(), paramsPartFormula.getMostDecimal());
        }
        return this.calculateCorrectionFactorValue(testValue, qcValue, -1, -1);
    }

    private Map<String, Object> calculateCorrectionFactorValue(String testValue, String qcValue, Integer mostSignificance, Integer mostDecimal) {
        Map<String, Object> map = new HashMap<>();
        BigDecimal qcRecoverRate;       //校正系数检验偏差
        try {
            BigDecimal testValueDec = new BigDecimal(testValue);
            if (!MathUtil.isNumeral(qcValue)) {
                throw new BaseException("校正点浓度不是数字类型,无法计算偏差!");
            }
            BigDecimal qcValueDec = new BigDecimal(qcValue);
            BigDecimal difference;      //出证结果和校正点浓度之差
            if (mostSignificance != -1 && mostDecimal != -1) {
                difference = new BigDecimal(proService.getDecimal(mostSignificance, mostDecimal, testValueDec.subtract(qcValueDec).toString()));
            } else {
                difference = testValueDec.subtract(qcValueDec);
            }
            //计算校正系数检验偏差
            qcRecoverRate = difference.divide(qcValueDec, 20, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        } catch (Exception ex) {
            map.put("qcRecoverRate", "");
            return map;
        }
        //保留三位有效位数
        String result = qcRecoverRate.abs().toString(); //绝对值
        //正好是三位整数
        if (result.indexOf(".") == 3) {
            result = qcRecoverRate.setScale(0, RoundingMode.HALF_EVEN).toString();
        } else {
            result = String.format("%s", qcRecoverRate.setScale(1, RoundingMode.HALF_EVEN).toString());
        }
        map.put("qcRecoverRate", StringUtil.isNotEmpty(result) ? result + "%" : result);
        return map;
    }

    @Transactional
    @Override
    public DtoAnalyseDataTemp updateStandardSampleInfo(DtoAnalyseDataTemp dtoAnalyseDataTemp) throws IllegalAccessException {
        String qcId = dtoAnalyseDataTemp.getQcId();
        String anaId = dtoAnalyseDataTemp.getId();
        Integer uncertainType = dtoAnalyseDataTemp.getUncertainType();
        String qcValue = dtoAnalyseDataTemp.getQcValue();
        String rangeLow = dtoAnalyseDataTemp.getRangeLow();
        String rangeHigh = dtoAnalyseDataTemp.getRangeHigh();
        String qcCode = dtoAnalyseDataTemp.getQcCode();
        Integer mostSignificance = dtoAnalyseDataTemp.getMostSignificance();
        Integer mostDecimal = dtoAnalyseDataTemp.getMostDecimal();
        String dimensionId = dtoAnalyseDataTemp.getDimensionId();
        String dimension = dtoAnalyseDataTemp.getDimension();
        Integer qcType = dtoAnalyseDataTemp.getQcType();
        String qcVolume = dtoAnalyseDataTemp.getQcVolume();
        String qcVolumeDimensionId = dtoAnalyseDataTemp.getQcVolumeDimensionId();
        String qcValueDimensionId = dtoAnalyseDataTemp.getQcValueDimensionId();
        DtoAnalyseData analyseData = analyseDataService.findOne(anaId);
        updateAnaDataInfo(analyseData, dimensionId, dimension, qcValueDimensionId, mostSignificance, mostDecimal, qcType, qcValue, dtoAnalyseDataTemp.getQcStandardDate());
        dtoAnalyseDataTemp.setQcInfo(analyseData.getQcInfo());
        DtoQualityControl dtoQualityControl = repository.findOne(qcId);
        updateQualityControlInfo(qcValue, qcCode, qcVolume, qcVolumeDimensionId, qcValueDimensionId, dtoAnalyseDataTemp.getQcStandardDate(),
                dtoAnalyseDataTemp.getQcValidDate(), dtoAnalyseDataTemp.getQcStandardId(), dtoQualityControl,uncertainType,rangeLow,rangeHigh);
        if (new QualityStandard().qcTypeValue().equals(qcType)) {
            dtoAnalyseDataTemp.setSampleRemark("标准编号：" + qcCode + " 标准值±不确定值：" + qcValue);
        } else if (new CurveCheck().qcTypeValue().equals(qcType)) {
            dtoAnalyseDataTemp.setSampleRemark("标准溶液加入体积：" + dtoQualityControl.getQcVolume() + " 标准物加入量：" + dtoQualityControl.getQcValue());
        }

        return dtoAnalyseDataTemp;
    }

    @Transactional
    @Override
    public Map<String, Object> updateStandardSampleInfoFromMap(Map<String, Object> map) throws IllegalAccessException {
        String qcId = map.getOrDefault("qcId", UUIDHelper.GUID_EMPTY).toString();
        String anaId = map.getOrDefault("id", UUIDHelper.GUID_EMPTY).toString();
        String qcValue = (map.containsKey("qcValue") && StringUtil.isNotNull(map.get("qcValue"))) ? map.get("qcValue").toString() : null;
        Integer uncertainType = (map.containsKey("uncertainType") && StringUtil.isNotNull(map.get("uncertainType"))) ? (Integer)map.get("uncertainType")
                :EnumBase.EnumUncertainType.浓度.getValue();
        String rangeLow = (map.containsKey("rangeLow") && StringUtil.isNotNull(map.get("rangeLow"))) ? map.get("rangeLow").toString() : null;
        String rangeHigh = (map.containsKey("rangeHigh") && StringUtil.isNotNull(map.get("rangeHigh"))) ? map.get("rangeHigh").toString() : null;
        String qcCode = (map.containsKey("qcCode") && StringUtil.isNotNull(map.get("qcCode"))) ? map.get("qcCode").toString() : null;
        Integer mostSignificance = (map.containsKey("mostSignificance") && StringUtil.isNotNull(map.get("mostSignificance")))
                ? (Integer) map.get("mostSignificance") : null;
        Integer mostDecimal = (map.containsKey("mostDecimal") && StringUtil.isNotNull(map.get("mostDecimal")))
                ? (Integer) map.get("mostDecimal") : null;
        String dimensionId = (map.containsKey("dimensionId") && StringUtil.isNotNull(map.get("dimensionId"))) ? map.get("dimensionId").toString() : null;
        String dimension = (map.containsKey("dimension") && StringUtil.isNotNull(map.get("dimension"))) ? map.get("dimension").toString() : null;
        Integer qcType = (map.containsKey("qcType") && StringUtil.isNotNull(map.get("qcType")))
                ? (Integer) map.get("qcType") : null;
        String qcVolume = (map.containsKey("qcVolume") && StringUtil.isNotNull(map.get("qcVolume"))) ? map.get("qcVolume").toString() : null;
        String qcVolumeDimensionId = (map.containsKey("qcVolumeDimensionId") && StringUtil.isNotNull(map.get("qcVolumeDimensionId")))
                ? map.get("qcVolumeDimensionId").toString() : null;
        String qcValueDimensionId = (map.containsKey("qcValueDimensionId") && StringUtil.isNotNull(map.get("qcValueDimensionId")))
                ? map.get("qcValueDimensionId").toString() : null;
        Date qcStandardDate = (map.containsKey("qcStandardDate") && StringUtil.isNotNull(map.get("qcStandardDate")))
                ? DateUtil.stringToDate(map.get("qcStandardDate").toString(), DateUtil.FULL) : null;
        Date qcValidDate = (map.containsKey("qcValidDate") && StringUtil.isNotNull(map.get("qcValidDate")))
                ? DateUtil.stringToDate(map.get("qcValidDate").toString(), DateUtil.FULL) : null;
        String qcStandardId = (map.containsKey("qcStandardId") && StringUtil.isNotNull(map.get("qcStandardId")))
                ? map.get("qcStandardId").toString() : null;
        DtoAnalyseData analyseData = analyseDataService.findOne(anaId);
        updateAnaDataInfo(analyseData, dimensionId, dimension, qcValueDimensionId, mostSignificance, mostDecimal, qcType, qcValue, qcValidDate);
        map.put("qcInfo", analyseData.getQcInfo());
        DtoQualityControl dtoQualityControl = repository.findOne(qcId);
        updateQualityControlInfo(qcValue, qcCode, qcVolume, qcVolumeDimensionId, qcValueDimensionId, qcStandardDate, qcValidDate, qcStandardId, dtoQualityControl,
                uncertainType,rangeLow,rangeHigh);
        if (new QualityStandard().qcTypeValue().equals(qcType)) {
            map.put("sampleRemark", "标准编号：" + qcCode + " 标准值±不确定值：" + qcValue);
        } else if (new CurveCheck().qcTypeValue().equals(qcType)) {
            map.put("sampleRemark", "标准溶液加入体积：" + dtoQualityControl.getQcVolume() + " 标准物加入量：" + dtoQualityControl.getQcValue());
        }
        return map;
    }

    /**
     * 更新分析数据量纲及修约规则信息
     *
     * @param analyseData        分析数据
     * @param dimensionId        量纲id
     * @param dimension          量纲
     * @param qcValueDimensionId qcValue量纲id
     * @param mostSignificance   有较位
     * @param mostDecimal        小数位
     * @param qcType             质控类型
     * @param qcValidDate        样品有效期
     */
    private void updateAnaDataInfo(DtoAnalyseData analyseData, String dimensionId, String dimension, String qcValueDimensionId,
                                   Integer mostSignificance, Integer mostDecimal, Integer qcType, String qcValue, Date qcValidDate) throws IllegalAccessException {
        if (StringUtil.isNotNull(analyseData)) {
            analyseData.setDimensionId(dimensionId);
            if (StringUtil.isNotEmpty(dimensionId) && !UUIDHelper.GUID_EMPTY.equals(dimensionId)) {
                analyseData.setDimension(dimension);
            }
            analyseData.setMostSignificance(mostSignificance);
            analyseData.setMostDecimal(mostDecimal);
            if (StringUtil.isNotNull(qcValidDate)) {
                analyseData.setRequireDeadLine(qcValidDate);
            }
            if (new CurveCheck().qcTypeValue().equals(qcType)) {
                //更新分析数据的量纲为曲线校核样的加入量量纲
                analyseData.setDimensionId(qcValueDimensionId);
                String dimId = StringUtil.isNotEmpty(qcValueDimensionId) ? qcValueDimensionId : "";
                DtoDimension dim = dimensionService.findOne(dimId);
                analyseData.setDimension(StringUtil.isNotNull(dim) ? dim.getDimensionName() : "");
            }
            //计算标样的偏差
            String testOriginValue = analyseDataService.halfLimit(analyseData.getTestOrignValue(), analyseData.getExamLimitValue());
            if (StringUtil.isNotEmpty(testOriginValue) && MathUtil.isNumeral(testOriginValue)) {
                Optional<DtoAnalyseOriginalRecord> record = analyseOriginalRecordRepository
                        .findByAnalyseDataIdIn(Collections.singletonList(analyseData.getId())).stream().findFirst();
                if (record.isPresent()) {
                    Map<String, Object> anaMap = entityToMap(analyseData);
                    String json = record.get().getJson();
                    TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
                    };
                    List<DtoAnalyseOriginalJson> originalJsonOldList = new ArrayList<>();
                    if (StringUtil.isNotNull(json)) {
                        originalJsonOldList = JsonIterator.deserialize(json, typeLiteral);
                        originalJsonOldList.forEach(p -> {
                            anaMap.put(p.getAlias(), p.getDefaultValue());
                        });
                    }
                    anaMap.put("qcValue", qcValue);
                    List<DtoQualityControlEvaluate> evaluates = qualityControlEvaluateService.evaluateAnaData(anaMap)
                            .stream().filter(e -> StringUtil.isNotNull(e.getIsPass()) && !e.getIsPass()).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(evaluates)) {
                        analyseData.setQcInfo("不合格");
                    } else {
                        analyseData.setQcInfo("合格");
                    }
                }
            }
            analyseDataService.save(analyseData);
        }
    }

    /**
     * 更新标样质控信息
     *
     * @param qcValue             加入量
     * @param qcCode              标样编号
     * @param qcVolume            加标体积
     * @param qcVolumeDimensionId 加入量量纲id
     * @param qcValueDimensionId  加入量量纲id
     * @param qcStandardDate      标准日期
     * @param qcValidDate         质控有效期
     * @param qcStandardId        质控标准id
     * @param dtoQualityControl   质控对象
     * @param uncertainType       范围类型
     * @param rangeLow            范围低点
     * @param rangeHigh           范围高点
     */
    private void updateQualityControlInfo(String qcValue, String qcCode, String qcVolume, String qcVolumeDimensionId, String qcValueDimensionId,
                                          Date qcStandardDate, Date qcValidDate, String qcStandardId, DtoQualityControl dtoQualityControl,
                                          Integer uncertainType,String rangeLow,String rangeHigh) {
        if (StringUtil.isNotNull(dtoQualityControl)) {
            dtoQualityControl.setQcValue(qcValue);
            dtoQualityControl.setQcCode(qcCode);
            dtoQualityControl.setQcVolume(qcVolume);
            dtoQualityControl.setUncertainType(uncertainType);
            dtoQualityControl.setRangeLow(rangeLow);
            dtoQualityControl.setRangeHigh(rangeHigh);
            if (StringUtil.isNotNull(dtoQualityControl.getQcValidDate())) {
                dtoQualityControl.setQcValidDate(dtoQualityControl.getQcValidDate());
            }
            if (StringUtil.isNotNull(qcStandardDate)) {
                dtoQualityControl.setQcStandardDate(qcStandardDate);
            } else {
                dtoQualityControl.setQcStandardDate(null);
            }
            if (StringUtil.isNotNull(qcValidDate)) {
                dtoQualityControl.setQcValidDate(qcValidDate);
            } else {
                dtoQualityControl.setQcValidDate(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
            }
            if (StringUtil.isNotEmpty(qcStandardId)) {
                dtoQualityControl.setQcStandardId(qcStandardId);
            }
            dtoQualityControl.setQcVolumeDimensionId(qcVolumeDimensionId == null ? UUIDHelper.GUID_EMPTY : qcVolumeDimensionId);
            dtoQualityControl.setQcValueDimensionId(qcValueDimensionId == null ? UUIDHelper.GUID_EMPTY : qcValueDimensionId);
            comRepository.merge(dtoQualityControl);
        }
    }


    /**
     * 根据项目id返回质控信息
     *
     * @param projectId 项目id
     * @return 样品数据进度
     */
    @Override
    public List<DtoQualityControlDetail> findQualityControlDetailByProjectId(String projectId) {
        return this.findQualityControlDetailByProjectIdAndSampleTypeIdList(projectId, null);
    }

    @Override
    public List<DtoQualityControlDetail> findQualityControlDetailByProjectIdAndSampleTypeIdList(String projectId, List<String> sampleTypeIds) {
        List<DtoQualityControlDetail> detailList = new ArrayList<>();
        List<DtoAnalyseDataTemp> locDataList;
        if (StringUtil.isEmpty(sampleTypeIds)) {
            locDataList = this.findLocalByProjectId(projectId);
            if (locDataList.size() == 0) {
                return detailList;
            }
        } else {
            locDataList = this.findLocalByProjectIdAndSampleTypeIds(projectId, sampleTypeIds);
            if (locDataList.size() == 0) {
                return detailList;
            }
        }
        List<DtoAnalyseDataTemp> yDataList = locDataList.stream().filter(p -> !p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue()) && !p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())).collect(Collectors.toList());
        List<DtoAnalyseDataTemp> outPxDataList = locDataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())
                && p.getQcType().equals(EnumLIM.EnumQCType.平行.getValue())).collect(Collectors.toList());
        List<DtoAnalyseDataTemp> qcAnalyseDataList = locDataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue()) || p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())).collect(Collectors.toList());
        List<String> ySampleIds = yDataList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
        List<String> outPxSampleIds = outPxDataList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
        ySampleIds.addAll(outPxSampleIds);
        if (ySampleIds.size() > 0) {
            this.setSNAssociateByProjectId(ySampleIds, qcAnalyseDataList);
        }
        this.setNonAssociateByProjectId(projectId, qcAnalyseDataList);
        qcAnalyseDataList = new ArrayList<>(qcAnalyseDataList.stream().collect(Collectors.toMap(DtoAnalyseDataTemp::getId, data -> data, (t1, t2) -> t1)).values());
        List<DtoQualityControl> qcList = new ArrayList<>();
        List<String> qcIds = qcAnalyseDataList.stream().filter(p -> p.getQcType().equals(new QualityStandard().qcTypeValue()) || p.getQcType().equals(new QualityMark().qcTypeValue())).map(DtoAnalyseDataTemp::getQcId).collect(Collectors.toList());
        if (qcIds.size() > 0) {
            qcList = repository.findAll(qcIds);
        }
        List<String> testIds = qcAnalyseDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
        //获取质控评价
        List<String> qcAnalyseIdList = qcAnalyseDataList.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = StringUtil.isNotEmpty(qcAnalyseIdList)
                ? qualityControlEvaluateRepository.findByObjectIdIn(qcAnalyseIdList) : new ArrayList<>();
        List<DtoTestQCRemindTemp> remindList = testIds.size() > 0 ? testQCRemindConfig2TestService.findByTestIds(testIds) : new ArrayList<>();
        //将list转为map
        Map<String, DtoTestQCRemindTemp> remindMap = remindList.stream().collect(Collectors.toMap(p -> String.format("%d-%d-%s",
                p.getQcGrade(), p.getQcType(), p.getTestId()), remind -> remind));
        Set<String> smpIdWithoutType = new HashSet<>(), samTypeIds = new HashSet<>();
        List<DtoAnalyseDataTemp> dataListWithoutType = new ArrayList<>();
        for (DtoAnalyseDataTemp temp : yDataList) {
            if (StringUtil.isNotEmpty(temp.getSampleTypeId()) && !UUIDHelper.GUID_EMPTY.equals(temp.getSampleTypeId())) {
                samTypeIds.add(temp.getSampleTypeId());
            } else {
                smpIdWithoutType.add(temp.getSampleId());
                dataListWithoutType.add(temp);
            }
        }
        if (StringUtil.isNotEmpty(smpIdWithoutType)) {
            List<DtoSample> sampleWithoutType = sampleRepository.findByIdInAndIsDeletedFalse(smpIdWithoutType);
            List<String> typeIdForSample = sampleWithoutType.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            samTypeIds.addAll(typeIdForSample);
            Map<String, DtoSample> sampleMapWithoutType = sampleWithoutType.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
            for (DtoAnalyseDataTemp data : dataListWithoutType) {
                if (sampleMapWithoutType.containsKey(data.getSampleId())) {
                    data.setSampleTypeId(sampleMapWithoutType.get(data.getSampleId()).getSampleTypeId());
                }
            }
        }
//        Set<String> samTypeIds = yDataList.stream().map(DtoAnalyseDataTemp::getSampleTypeId).collect(Collectors.toSet());
        List<DtoSampleType> typeList = sampleTypeService.findAll(samTypeIds);
        final List<DtoAnalyseDataTemp> finalQcDataList = qcAnalyseDataList;
        yDataList.stream().collect(Collectors.groupingBy(DtoAnalyseDataTemp::getSampleTypeId, Collectors.toList())).forEach((sampleTypeId, dataList) -> {
            DtoSampleType samType = typeList.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst().orElse(null);
            dataList.stream().collect(Collectors.groupingBy(DtoAnalyseDataTemp::getTestId, Collectors.toList())).forEach((testId, testDataList) -> {
                DtoQualityControlDetail detail = new DtoQualityControlDetail();
                detail.setSampleTypeId(sampleTypeId);
                detail.setTestId(testId);
                String typeName = "";
                if (StringUtil.isNotNull(samType)) {
                    typeName = samType.getTypeName();
                }
                detail.setRedAnalyzeItemName(String.format("%s(%s)", testDataList.get(0).getRedAnalyzeItemName(), typeName));
                detail.setAllSample(testDataList.size());
                List<String> sampleIds = testDataList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
                List<DtoAnalyseDataTemp> thisQcDataList = finalQcDataList.stream().filter(p ->
                        (p.getSampleTypeId().equals(sampleTypeId) || sampleIds.contains(p.getAssociateSampleId())) &&
                                p.getTestId().equals(testId)).collect(Collectors.toList());
                if (StringUtil.isNull(sampleTypeIds)) {
                    calculateAnalysisQuality(detail, thisQcDataList, remindMap, testId, qualityControlEvaluateList);
                } else {
                    calculateAnalysisQualityWithSampleTypeIds(detail, thisQcDataList, remindMap, testId, sampleTypeIds, qualityControlEvaluateList);
                }
                detailList.add(detail);
            });
        });
        return detailList;
    }

    @Override
    public void calculateAnalysisQuality(DtoQualityControlDetail detail, List<DtoAnalyseDataTemp> thisQcDataList, Map<String, DtoTestQCRemindTemp> remindMap,
                                         String testId, List<DtoQualityControlEvaluate> qualityControlEvaluateList) {
        this.calculateAnalysisQualityWithSampleTypeIds(detail, thisQcDataList, remindMap, testId, null, qualityControlEvaluateList);
    }

    @Override
    public void calculateAnalysisQuality(DtoQualityControlDetail detail, List<DtoAnalyseDataTemp> thisQcDataList, Map<String, DtoTestQCRemindTemp> remindMap,
                                         String testId, List<DtoQualityControlEvaluate> qualityControlEvaluateList, List<String> sampleTypeIds) {
        this.calculateAnalysisQualityWithSampleTypeIds(detail, thisQcDataList, remindMap, testId, sampleTypeIds, qualityControlEvaluateList);
    }

    @Override
    @Transactional
    public DtoQualityControl updateReplaceSampleInfo(DtoQualityControl dtoQualityControl) {
        DtoQualityControl oriDtoQualityControl = repository.findOne(dtoQualityControl.getId());
        if (StringUtil.isNull(oriDtoQualityControl)) {
            throw new BaseException("替代样质控信息不存在!");
        }
        List<DtoSample> samples = sampleRepository.findByQcIdIn(Collections.singletonList(dtoQualityControl.getId()));
        if (StringUtil.isNotEmpty(samples)) {
            DtoAnalyseData analyseData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(samples.get(0).getId(), dtoQualityControl.getTestId()).stream().findFirst().orElse(null);
            if (StringUtil.isNotNull(analyseData)) {
                analyseData.setMostSignificance(dtoQualityControl.getMostSignificance());
                analyseData.setMostDecimal(dtoQualityControl.getMostDecimal());
                analyseData.setDimensionId(dtoQualityControl.getQcValueDimensionId());
                dimensionService.findAll().stream().filter(v->v.getId().equals(analyseData.getDimensionId())).findFirst()
                        .ifPresent(v->analyseData.setDimension(v.getDimensionName()));
                analyseDataRepository.save(analyseData);
            }
        }
        List<String> valueList = new ArrayList<>();
        valueList.add(dtoQualityControl.getTestValue());
        valueList.add(dtoQualityControl.getTestValue());
        valueList.add(dtoQualityControl.getAddition());
        DtoTest test = testRepository.findOne(dtoQualityControl.getTestId());
        List<DtoQualityControlLimit> limitList = qualityControlLimitRepository.findByTestId(test.getParentId()).stream()
                .filter(p -> p.getQcType().equals(new QualityReplace().qcTypeValue())
                        && p.getSubstituteName().equals(dtoQualityControl.getCompoundName())).collect(Collectors.toList());
        DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
        qualityControlLimit.setTestId(test.getParentId());
        qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.回收率.getValue());
        qualityControlLimit.setSubstituteName(dtoQualityControl.getCompoundName());
        qualityControlLimit.setQcType(new QualityReplace().qcTypeValue());
        qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
        limitList.add(qualityControlLimit);
        //计算回收率
        String recoverRate = "";
        if (limitList.size() > 0) {
            Map<String, Object> jbMap = new QualityReplace().calculateDeviationValue(limitList, valueList);
            DtoQualityControlLimit limit = (DtoQualityControlLimit) jbMap.get("limit");
            if (StringUtil.isNotNull(limit)) {
                String range = String.format("%s不在%s范围内", EnumBase.EnumJudgingMethod.getName(limit.getJudgingMethod()), limit.getAllowLimit());
                String qcRecoverRate = (String) jbMap.get("qcRate");
                Integer sign = new QualityReplace().getQualityConfig().getMostSignificance();
                Integer md = new QualityReplace().getQualityConfig().getMostDecimal();
                String recoverRateStr = proService.getDecimal(sign, md, qcRecoverRate);
                Boolean isPass = new QualityReplace().deviationQualified(limit, recoverRateStr);
                isPass = StringUtil.isNotNull(isPass) ? isPass : true;
                if (StringUtil.isNotEmpty(recoverRateStr)) {
                    recoverRate = recoverRateStr + "%";
                }
                dtoQualityControl.setIsPass(isPass);
                dtoQualityControl.setRange(range);
            }
        }
        //修改CasCode，加入量，化合物名称
        oriDtoQualityControl.setQcCode(dtoQualityControl.getCasCode());
        oriDtoQualityControl.setQcValue(dtoQualityControl.getAddition());
        oriDtoQualityControl.setQcVolume(dtoQualityControl.getCompoundName());
        oriDtoQualityControl.setQcValueDimensionId(dtoQualityControl.getQcValueDimensionId());
        super.update(oriDtoQualityControl);
        dtoQualityControl.setQcRecoverRate(recoverRate);
        return dtoQualityControl;
    }

    @Override
    @Transactional
    public DtoQualityControl updateCorrectionFactorSampleInfo(DtoQualityControl dtoQualityControl) {
        DtoQualityControl oriDtoQualityControl = repository.findOne(dtoQualityControl.getId());
        if (StringUtil.isNull(oriDtoQualityControl)) {
            throw new BaseException("校正系数检验样质控信息不存在!");
        }
        List<String> valueList = new ArrayList<>();
        valueList.add(dtoQualityControl.getQcValue());
        valueList.add(dtoQualityControl.getTestValue());
        valueList.add(dtoQualityControl.getTestValue());
        List<DtoQualityControlLimit> limitList = qualityControlLimitRepository.findByTestId(dtoQualityControl.getTestId()).stream()
                .filter(p -> p.getQcType().equals(new QualityCorrectionFactor().qcTypeValue())).collect(Collectors.toList());
        DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
        qualityControlLimit.setTestId(dtoQualityControl.getTestId());
        qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
        qualityControlLimit.setQcType(new QualityCorrectionFactor().qcTypeValue());
        qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
        limitList.add(qualityControlLimit);
        //计算回收率
        String recoverRate = "";
        if (limitList.size() > 0) {
            Map<String, Object> jbMap = new QualityCorrectionFactor().calculateDeviationValue(limitList, valueList);
            DtoQualityControlLimit limit = (DtoQualityControlLimit) jbMap.get("limit");
            if (StringUtil.isNotNull(limit)) {
                String range = String.format("%s不在%s范围内", EnumBase.EnumJudgingMethod.getName(limit.getJudgingMethod()), limit.getAllowLimit());
                String qcRecoverRate = (String) jbMap.get("qcRate");
                Integer sign = new QualityCorrectionFactor().getQualityConfig().getMostSignificance();
                Integer md = new QualityCorrectionFactor().getQualityConfig().getMostDecimal();
                String recoverRateStr = proService.getDecimal(sign, md, qcRecoverRate);
                Boolean isPass = new QualityCorrectionFactor().deviationQualified(limit, recoverRateStr);
                isPass = StringUtil.isNotNull(isPass) ? isPass : true;
                if (StringUtil.isNotEmpty(recoverRateStr)) {
                    recoverRate = recoverRateStr + "%";
                }
                dtoQualityControl.setIsPass(isPass);
                dtoQualityControl.setRange(range);
            }
        }
        //修改校正点浓度
        oriDtoQualityControl.setQcValue(dtoQualityControl.getQcValue());
        //修改量纲
        oriDtoQualityControl.setQcValueDimensionId(dtoQualityControl.getQcValueDimensionId());
        super.update(oriDtoQualityControl);
        dtoQualityControl.setQcRecoverRate(recoverRate);
        return dtoQualityControl;
    }

    private void calculateAnalysisQualityWithSampleTypeIds(DtoQualityControlDetail detail,
                                                           List<DtoAnalyseDataTemp> thisQcDataList,
                                                           Map<String, DtoTestQCRemindTemp> remindMap,
                                                           String testId, List<String> sampleTypeIds,
                                                           List<DtoQualityControlEvaluate> qualityControlEvaluateList) {
        Integer pxNum = 0, ipxNum = 0, qkbNum = 0, jbNum = 0, byNum = 0, kbNum = 0, mmNum = 0;
        Integer allPXPassNum = 0, allIPXPassNum = 0, allKBPassNum = 0, allQKBPassNum = 0, allJBPassNum = 0, allBYPassNum = 0, allMMPassNum = 0;
        Map<String, List<DtoQualityControlEvaluate>> qualityControlEvaluateMap = qualityControlEvaluateList
                .stream().collect(Collectors.groupingBy(DtoQualityControlEvaluate::getObjectId));
        for (DtoAnalyseDataTemp ana : thisQcDataList) {
            List<DtoQualityControlEvaluate> evaluateList = qualityControlEvaluateMap.get(ana.getId());
            Boolean isPass = StringUtil.isNotEmpty(evaluateList) ? evaluateList.get(0).getIsPass() : null;
            //EnumPRO.EnumQCType qcType = EnumPRO.EnumQCType.getByValue(ana.getQcType());
            String qcType = null;
            if (!ana.getQcType().equals(-1)) {
                qcType = QualityTaskFactory.getInstance().getQcSample(ana.getQcType()).qcTypeName();
            }
            if (StringUtil.isNotNull(qcType)) {
                if (ana.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())) {
                    switch (qcType) {
                        case "平行":
                            pxNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allPXPassNum++;
                            }
                            break;
                        case "空白":
                            qkbNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allQKBPassNum++;
                            }
                            break;

                        default:
                            break;
                    }
                } else if (ana.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())) {
                    switch (qcType) {
                        case "平行":
                            ipxNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allIPXPassNum++;
                            }
                            break;
                        case "设备空白":
                        case "现场空白":
                            qkbNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allQKBPassNum++;
                            }
                            break;
                        case "空白":
                        case "试剂空白":
                            kbNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allKBPassNum++;
                            }
                            break;
                        case "加标":
                            jbNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allJBPassNum++;
                            }
                            break;
                        case "标样":
                            byNum++;
                            if (StringUtil.isNotNull(isPass) && isPass) {
                                allBYPassNum++;
                            }
                            break;
                        default:
                            break;
                    }
                }
            } else if (ana.getIsQm()) {
                List<DtoQualityControlEvaluate> qmEvaluateList = qualityControlEvaluateMap.get(ana.getProjectId());
                isPass = StringUtil.isNotEmpty(qmEvaluateList) ? qmEvaluateList.get(0).getIsPass() : null;
                mmNum++;
                if (StringUtil.isNotNull(isPass) && isPass) {
                    allMMPassNum++;
                }
            }
        }
        detail.setKbCtrlNum(kbNum);
        if (StringUtil.isNull(sampleTypeIds)) {
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityBlank().qcTypeValue(), testId), null), detail.getKbCtrlRate())) {
                detail.setKbCtrlRate(detail.getKbCtrlRate() + "-red");
            }
            detail.setKbPassNum(allKBPassNum);
            if (!this.isPass(detail.getKbPassRate(), detail.getKbCtrlRate())) {
                detail.setKbPassRate(detail.getKbPassRate() + "-red");
            }
            detail.setQkbCtrlNum(qkbNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.外部质控.getValue(), new QualityBlank().qcTypeValue(), testId), null), detail.getQkbCtrlRate())) {
                detail.setQkbCtrlRate(detail.getQkbCtrlRate() + "-red");
            }
            detail.setQkbPassNum(allQKBPassNum);
            if (!this.isPass(detail.getQkbPassRate(), detail.getQkbCtrlRate())) {
                detail.setQkbPassRate(detail.getQkbPassRate() + "-red");
            }
            detail.setPxCtrlNum(pxNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.外部质控.getValue(), new QualityParallel().qcTypeValue(), testId), null), detail.getPxCtrlRate())) {
                detail.setPxCtrlRate(detail.getPxCtrlRate() + "-red");
            }
            detail.setPxPassNum(allPXPassNum);
            if (!this.isPass(detail.getPxPassRate(), detail.getPxCtrlRate())) {
                detail.setPxPassRate(detail.getPxPassRate() + "-red");
            }
            detail.setIpxCtrlNum(ipxNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityParallel().qcTypeValue(), testId), null), detail.getIpxCtrlRate())) {
                detail.setIpxCtrlRate(detail.getIpxCtrlRate() + "-red");
            }
            detail.setIpxPassNum(allIPXPassNum);
            if (!this.isPass(detail.getIpxPassRate(), detail.getIpxCtrlRate())) {
                detail.setIpxPassRate(detail.getIpxPassRate() + "-red");
            }
            detail.setJBCtrlNum(jbNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityMark().qcTypeValue(), testId), null), detail.getJbCtrlRate())) {
                detail.setJbCtrlRate(detail.getJbCtrlRate() + "-red");
            }
            detail.setJBPassNum(allJBPassNum);
            if (!this.isPass(detail.getJbPassRate(), detail.getJbCtrlRate())) {
                detail.setJbPassRate(detail.getJbPassRate() + "-red");
            }
            detail.setBYCtrlNum(byNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityStandard().qcTypeValue(), testId), null), detail.getByCtrlRate())) {
                detail.setByCtrlRate(detail.getByCtrlRate() + "-red");
            }
            detail.setByPassNum(allBYPassNum);
            if (!this.isPass(detail.getByPassRate(), detail.getByCtrlRate())) {
                detail.setByPassRate(detail.getByPassRate() + "-red");
            }
            detail.setMmCtrlNum(mmNum);
            detail.setMmPassNum(allMMPassNum);
            if (!this.isPass(detail.getMmPassRate(), detail.getMmCtrlRate())) {
                detail.setMmPassRate(detail.getMmPassRate() + "-red");
            }
            detail.setAllCtrlNum(kbNum + qkbNum + pxNum + ipxNum + jbNum + byNum + mmNum);
            if (!this.isCtrl(null, detail.getAllCtrlRate())) {
                detail.setAllCtrlRate(detail.getAllCtrlRate() + "-red");
            }
            detail.setAllPassNum(allKBPassNum + allQKBPassNum + allPXPassNum + allIPXPassNum + allJBPassNum + allBYPassNum + allMMPassNum);
            if (!this.isPass(detail.getAllPassRate(), detail.getAllCtrlRate())) {
                detail.setAllPassRate(detail.getAllPassRate() + "-red");
            }
        } else {
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityBlank().qcTypeValue(), testId), null), detail.getKbCtrlRate())) {
                detail.setKbCtrlRate(detail.getKbCtrlRate());
            }
            detail.setQkbCtrlNum(qkbNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.外部质控.getValue(), new QualityBlank().qcTypeValue(), testId), null), detail.getQkbCtrlRate())) {
                detail.setQkbCtrlRate(detail.getQkbCtrlRate());
            }
            detail.setPxCtrlNum(pxNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.外部质控.getValue(), new QualityParallel().qcTypeValue(), testId), null), detail.getPxCtrlRate())) {
                detail.setPxCtrlRate(detail.getPxCtrlRate());
            }
            detail.setPxPassNum(allPXPassNum);
            if (!this.isPass(detail.getPxPassRate(), detail.getPxCtrlRate())) {
                detail.setPxPassRate(detail.getPxPassRate());
            }
            detail.setIpxCtrlNum(ipxNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityParallel().qcTypeValue(), testId), null), detail.getIpxCtrlRate())) {
                detail.setIpxCtrlRate(detail.getIpxCtrlRate());
            }
            detail.setIpxPassNum(allIPXPassNum);
            if (!this.isPass(detail.getIpxPassRate(), detail.getIpxCtrlRate())) {
                detail.setIpxPassRate(detail.getIpxPassRate());
            }
            detail.setJBCtrlNum(jbNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityMark().qcTypeValue(), testId), null), detail.getJbCtrlRate())) {
                detail.setJbCtrlRate(detail.getJbCtrlRate());
            }
            detail.setJBPassNum(allJBPassNum);
            if (!this.isPass(detail.getJbPassRate(), detail.getJbCtrlRate())) {
                detail.setJbPassRate(detail.getJbPassRate());
            }
            detail.setBYCtrlNum(byNum);
            if (!this.isCtrl(remindMap.getOrDefault(String.format("%d-%d-%s", EnumLIM.EnumQCGrade.内部质控.getValue(), new QualityStandard().qcTypeValue(), testId), null), detail.getByCtrlRate())) {
                detail.setByCtrlRate(detail.getByCtrlRate());
            }
            detail.setMmCtrlNum(mmNum);
            detail.setMmPassNum(allMMPassNum);
            if (!this.isPass(detail.getMmPassRate(), detail.getMmCtrlRate())) {
                detail.setMmPassRate(detail.getMmPassRate());
            }
            detail.setAllCtrlNum(kbNum + qkbNum + pxNum + ipxNum + jbNum + byNum + mmNum);
            if (!this.isCtrl(null, detail.getAllCtrlRate())) {
                detail.setAllCtrlRate(detail.getAllCtrlRate());
            }
            detail.setAllPassNum(allKBPassNum + allQKBPassNum + allPXPassNum + allIPXPassNum + allJBPassNum + allBYPassNum + allMMPassNum);
            if (!this.isPass(detail.getAllPassRate(), detail.getAllCtrlRate())) {
                detail.setAllPassRate(detail.getAllPassRate());
            }
        }
    }

    private List<DtoAnalyseDataTemp> findLocalByProjectId(String projectId) {
        return this.findLocalByProjectIdAndSampleTypeIds(projectId, null);
    }

    private List<DtoAnalyseDataTemp> findLocalByProjectIdAndSampleTypeIds(String projectId, List<String> sampleTypeIds) {
        StringBuilder sqlSb = new StringBuilder();
        sqlSb.append("select new com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp" +
                "(a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId," +
                "a.qcGrade,a.qcType,a.testValue,a.testOrignValue,a.testValueD," +
                "a.testValueDstr,a.sampleTypeId,'00000000-0000-0000-0000-000000000000', a.gatherCode, a.qcInfo)");
        sqlSb.append(" from DtoAnalyseData a,DtoSample s,DtoReceiveSampleRecord r");
        sqlSb.append(" where 1=1");
        sqlSb.append(" and s.isDeleted = 0  and a.isDeleted = 0 ");
        sqlSb.append(" and a.sampleId = s.id");
        sqlSb.append(" and s.receiveId = r.id");
        sqlSb.append(" and r.projectId = :projectId");
        sqlSb.append(" and a.dataStatus <> :dataStatus");
        Map<String, Object> params = new HashMap<>();
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            sqlSb.append(" and s.sampleTypeId in :sampleTypeIds");
            params.put("sampleTypeIds", sampleTypeIds);
        }
        params.put("projectId", projectId);
        params.put("dataStatus", EnumPRO.EnumAnalyseDataStatus.未测.getValue());

        return comRepository.find(sqlSb.toString(), params);
    }

    /**
     * 纳入室内关联样数据
     *
     * @param ySampleIds        原样id集合
     * @param qcAnalyseDataList 质控数据
     */
    private void setSNAssociateByProjectId(List<String> ySampleIds, List<DtoAnalyseDataTemp> qcAnalyseDataList) {
        Map<String, Object> params = new HashMap<>();
        PageBean<DtoAnalyseDataTemp> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoSample s");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp(a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId,a.qcGrade," +
                "a.qcType,a.testValue,a.testOrignValue,a.testValueD,a.testValueDstr,a.sampleTypeId,s.associateSampleId,a.gatherCode,a.qcInfo)");
        pb.addCondition(" and s.isDeleted = 0");
        pb.addCondition(" and s.associateSampleId in :associateSampleIds");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and a.qcGrade = :qcGrade");
        params.put("associateSampleIds", ySampleIds);
        params.put("qcGrade", EnumLIM.EnumQCGrade.内部质控.getValue());
        List<DtoAnalyseDataTemp> dataList = comRepository.find(pb.getAutoQuery(), params);
        qcAnalyseDataList.addAll(dataList);
    }

    /**
     * 纳入室内非关联样数据
     *
     * @param projectId         项目id
     * @param qcAnalyseDataList 质控数据
     */
    private void setNonAssociateByProjectId(String projectId, List<DtoAnalyseDataTemp> qcAnalyseDataList) {
        Map<String, Object> params = new HashMap<>();
        PageBean<DtoAnalyseDataTemp> pbInner = new PageBean<>();
        pbInner.setEntityName("DtoAnalyseData a,DtoProject2WorkSheetFolder p2w");
        pbInner.setSelect("select new com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp(a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId,a.qcGrade," +
                "a.qcType,a.testValue,a.testOrignValue,a.testValueD,a.testValueDstr,a.sampleTypeId,'00000000-0000-0000-0000-000000000000', a.gatherCode, a.qcInfo)");
        pbInner.addCondition(" and a.qcGrade = :qcGrade");
        pbInner.addCondition(" and a.qcType in :types");
        pbInner.addCondition(" and a.workSheetFolderId = p2w.workSheetFolderId  and a.isDeleted = 0 ");
        pbInner.addCondition(" and a.workSheetFolderId != '00000000-0000-0000-0000-000000000000' ");
        pbInner.addCondition(" and p2w.projectId = :projectId");
        params.put("qcGrade", EnumLIM.EnumQCGrade.内部质控.getValue());
        List<Integer> types = new ArrayList<>();
        types.add(new QualityBlank().qcTypeValue());
        types.add(new QualityReagentBlank().qcTypeValue());
        types.add(new QualityStandard().qcTypeValue());
        params.put("types", types);
        params.put("projectId", projectId);
        List<DtoAnalyseDataTemp> dataList = comRepository.find(pbInner.getAutoQuery(), params);
        qcAnalyseDataList.addAll(dataList);
    }

    /**
     * 纳入计算数据
     *
     * @param analyseData 对应平行样数据
     * @param anaList     关联样数据
     * @param yAnaList    原样数据
     * @param values      纳入的数据（依次为均值，最大值，最小值）
     */
    private void getPxValues(DtoAnalyseDataTemp analyseData, List<DtoAnalyseDataTemp> anaList, List<DtoAnalyseDataTemp> yAnaList, List<BigDecimal> values) {
        List<BigDecimal> testOrignValues = new ArrayList<>();
        if (yAnaList.stream().anyMatch(p -> p.getSampleId().equals(analyseData.getAssociateSampleId()) && p.getTestId().equals(analyseData.getTestId()))) {
            for (DtoAnalyseDataTemp ana : anaList.stream().filter(p -> p.getAssociateSampleId().equals(analyseData.getAssociateSampleId())
                    && p.getTestId().equals(analyseData.getTestId()) && p.getQcType().equals(new QualityParallel().qcTypeValue()) &&
                    p.getQcGrade().equals(analyseData.getQcGrade())).collect(Collectors.toList())) {
                if (MathUtil.isNumeral(ana.getTestOrignValue())) {
                    testOrignValues.add(MathUtil.getBigDecimal(ana.getTestOrignValue()));
                }
            }
            DtoAnalyseDataTemp yAna = yAnaList.stream().filter(p -> p.getSampleId().equals(analyseData.getAssociateSampleId()) &&
                    p.getTestId().equals(analyseData.getTestId())).findFirst().get();
            if (MathUtil.isNumeral(yAna.getTestOrignValue())) {
                testOrignValues.add(MathUtil.getBigDecimal(yAna.getTestOrignValue()));
            }
        }
        if (testOrignValues.size() > 0) {
            testOrignValues.sort(Comparator.comparing((BigDecimal p) -> p));
            BigDecimal max = testOrignValues.get(testOrignValues.size() - 1);
            BigDecimal min = testOrignValues.get(0);
            BigDecimal sum = testOrignValues.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
            BigDecimal avg = sum.divide(new BigDecimal(testOrignValues.size()), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
            values.add(avg);
            values.add(max);
            values.add(min);
        }
    }

    /**
     * 判断空白样数据是否合格（低于检出限为合格，否则不合格）
     *
     * @param analyseData 空白数据
     * @return 是否合格
     */
    private boolean blankIsPass(DtoAnalyseDataTemp analyseData) {
        if (MathUtil.isNumeral(analyseData.getExamLimitValue())) {
            return analyseData.getTestValueD().compareTo(MathUtil.getBigDecimal(analyseData.getExamLimitValue())) < 0;
        }
        //没有检出限默认无法判断是否合格，则永远是合格
        return true;
    }

    /**
     * 判断是否满足检查率
     *
     * @param temp 比例配置
     * @param rate 检查率
     * @return 是否满足
     */
    private boolean isCtrl(DtoTestQCRemindTemp temp, String rate) {
        Integer qcRemindPercent = StringUtil.isNotNull(temp) ? temp.getQcRemindPercent() : DEFAULT_REMIND_PERCENT;
        BigDecimal rateDec = MathUtil.getBigDecimal(rate.replace("%", ""));
        return StringUtil.isNotNull(rateDec) && rateDec.compareTo(new BigDecimal(qcRemindPercent)) >= 0;
    }

    /**
     * 判断是否满足合格率
     *
     * @param rate     合格率
     * @param ctrlRate 检查率
     * @return 是否满足
     */
    private boolean isPass(String rate, String ctrlRate) {
        BigDecimal rateDec = MathUtil.getBigDecimal(rate.replace("%", ""));
        ctrlRate = ctrlRate.replace("-red", "").replace("%", "");
        //BUG2024040800617 项目进度质控统计、分析质控统计】需要修改标红规则，只针对检查率不为0，合格率不为100%的情况，标红显示合格率
        if (MathUtil.isNumeral(ctrlRate)) {
            return (new BigDecimal(ctrlRate)).compareTo(BigDecimal.ZERO) == 0
                    || (StringUtil.isNotNull(rateDec) && rateDec.compareTo(new BigDecimal(DEFAULT_PASS_RATE)) == 0);
        } else {
            return StringUtil.isNotNull(rateDec) && rateDec.compareTo(new BigDecimal(DEFAULT_PASS_RATE)) == 0;
        }
    }

    private void getJbValue(DtoQualityControl dtoQualityControl, List<DtoQualityControlLimit> qualityControlLimitList) {
        List<String> valueList = new ArrayList<>();
        valueList.add(dtoQualityControl.getRealSampleTestValue());
        valueList.add(dtoQualityControl.getQcTestValue());
        valueList.add(dtoQualityControl.getQcValue());
        valueList.add(dtoQualityControl.getTestValue());
        List<DtoQualityControlLimit> limitList = qualityControlLimitList.stream()
                .filter(p -> p.getQcType().equals(dtoQualityControl.getQcType())).collect(Collectors.toList());
        String qcAddedValue = "";
        String recoverRateStr = "";
        Boolean isPass = true;
        String range = "";
        DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
        qualityControlLimit.setTestId(dtoQualityControl.getTestId());
        qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.回收率.getValue());
        qualityControlLimit.setQcType(dtoQualityControl.getQcType());
        qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
        limitList.add(qualityControlLimit);
        if (limitList.size() > 0) {
            Map<String, Object> jbMap = new QualityMark().calculateDeviationValue(limitList, valueList);
            DtoQualityControlLimit limit = (DtoQualityControlLimit) jbMap.get("limit");
            if (StringUtil.isNotNull(limit)) {
                range = limit.getAllowLimitData();
                Integer sign = new QualityMark().getQualityConfig().getMostSignificance();
                Integer md = new QualityMark().getQualityConfig().getMostDecimal();
                qcAddedValue = (String) jbMap.get("qcAddedValue");
                String qcRecoverRate = (String) jbMap.get("qcRate");
                recoverRateStr = proService.getDecimal(sign, md, qcRecoverRate);
                isPass = new QualityMark().deviationQualified(limit, recoverRateStr);
                isPass = StringUtil.isNotNull(isPass) ? isPass : true;
            }
        }
        dtoQualityControl.setQcAddedValue(qcAddedValue);
        dtoQualityControl.setQcRecoverRate(recoverRateStr);
        dtoQualityControl.setIsPass(isPass);
        dtoQualityControl.setRange(range);
    }

    private static <T> Map<String, Object> entityToMap(T obj) throws IllegalAccessException {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();

        Field[] declaredFields = obj.getClass().getSuperclass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }

        return map;
    }

    @Autowired
    @Lazy
    public void setDimensionService(DimensionService dimensionService) {
        this.dimensionService = dimensionService;
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    @Lazy
    public void setIConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    @Lazy
    public void setQualityControlEvaluateService(QualityControlEvaluateService qualityControlEvaluateService) {
        this.qualityControlEvaluateService = qualityControlEvaluateService;
    }

    @Autowired
    public void setAnalyseOriginalRecordRepository(AnalyseOriginalRecordRepository analyseOriginalRecordRepository) {
        this.analyseOriginalRecordRepository = analyseOriginalRecordRepository;
    }
}