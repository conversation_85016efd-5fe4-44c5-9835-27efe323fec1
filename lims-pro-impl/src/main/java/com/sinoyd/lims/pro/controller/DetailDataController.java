package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.DetailDataCriteria;
import com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoMonitorData;
import com.sinoyd.lims.pro.service.DetailDataService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 详细数据服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019-11-15
 * @since V100R001
 */
@Api(tags = "详细数据服务")
@RestController
@RequestMapping("/api/pro/detailData")
public class DetailDataController extends ExceptionHandlerController<DetailDataService> {
    /**
     * 查询样品详细数据
     *
     * @param detailDataCriteria dto
     * @return RestResponse<DtoDetailData>
     */
    @ApiOperation(value = "查询样品详细数据", notes = "查询样品详细数据")
    @PostMapping(path = "/samples")
    public RestResponse<DtoMonitorData> getBySample(@RequestBody DetailDataCriteria detailDataCriteria) {
        RestResponse<DtoMonitorData> restResp = new RestResponse<>();
        DtoMonitorData monitorData = service.getBySampleIdSort(detailDataCriteria);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(monitorData);
        restResp.setCount(monitorData.getAnalyseData().size());
        return restResp;
    }


    /**
     * 查询样品详细数据
     *
     * @param detailDataCriteria dto
     * @return RestResponse<DtoDetailData>
     */
    @ApiOperation(value = "查询数据", notes = "查询数据")
    @PostMapping
    public RestResponse<Map<String, Object>> findDetailDataByPage(@RequestBody DetailDataCriteria detailDataCriteria) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        PageBean<Object[]> pageBean = super.getPageBean();
        Map<String, Object> map = service.findDetailDataByPage(pageBean, detailDataCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(map);
        restResp.setCount(pageBean.getRowsCount());
        return restResp;
    }

    /**
     * 查询样品详细数据
     *
     * @param detailDataProjectCriteria dto
     * @return RestResponse<DtoDetailData>
     */
    @ApiOperation(value = "查询项目数据", notes = "查询项目数据")
    @PostMapping("/project")
    public RestResponse<Map<String, Object>> findProjectDetailData(@RequestBody DetailDataProjectCriteria detailDataProjectCriteria) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        Map<String, Object> map = service.findProjectDetailData(detailDataProjectCriteria);
        List<LinkedHashMap<String, Object>> analyseDataMapList = (List<LinkedHashMap<String, Object>>) map.get("analyseData");
        restResp.setRestStatus(StringUtil.isEmpty(analyseDataMapList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(map);
        restResp.setCount(analyseDataMapList.size());
        return restResp;
    }
}
