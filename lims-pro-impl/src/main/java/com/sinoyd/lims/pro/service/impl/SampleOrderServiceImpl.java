package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.XmlConfig;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.factory.quality.QualityParallel;
import com.sinoyd.base.factory.quality.QualityTransportBlank;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.SampleOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 样品排序相关业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/4
 */
@Service
@Slf4j
public class SampleOrderServiceImpl implements SampleOrderService {


    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<Map<String, String>> initSampleOrderNum(List<DtoSample> samples, List<DtoAnalyseDataTemp> tempList, List<OrderReviseVO> orderReviseVOList) {
        log.info("=================initSampleOrderNum当前子线程:" + Thread.currentThread().getName() + "=========================");
        Map<String, String> map = new HashMap<>();
        for (DtoSample sample : samples) {
            map.put(sample.getId(), initSampleOrderNum(sample, tempList, orderReviseVOList));
        }
        return new AsyncResult<>(map);
    }

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<Map<String, String>> generateSampleOrderNum(List<Map<String, Object>> mapList, List<DtoAnalyseDataTemp> tempList, List<OrderReviseVO> orderReviseVOList) {
        log.info("=================generateSampleOrderNum当前子线程:" + Thread.currentThread().getName() + "=========================");
        Map<String, String> map = new HashMap<>();
        for (Map<String, Object> data : mapList) {
            DtoAnalyseDataTemp analyseDataTemp = (DtoAnalyseDataTemp) data.get("analyseDataTemp");
            String orderNum = getSortNumber(tempList, analyseDataTemp, orderReviseVOList);
            map.put(data.get("id").toString(), orderNum);
        }
        return new AsyncResult<>(map);
    }

    /**
     * 获取样品排序值
     *
     * @param tempList           数据集合
     * @param dtoAnalyseDataTemp 当前数据
     * @return 排序值
     */
    protected String getSortNumber(List<DtoAnalyseDataTemp> tempList, DtoAnalyseDataTemp dtoAnalyseDataTemp, List<OrderReviseVO> orderReviseVOList) {
        String orderNum = "30-" + dtoAnalyseDataTemp.getSampleCode() + "-30";
        Map<String, String> codeMap = tempList.stream().collect(Collectors.groupingBy(DtoAnalyseDataTemp::getSampleId,
                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSampleCode())));
        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
            Integer qcType = dtoAnalyseDataTemp.getQcType();
            if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.串联样.getQcType();
            }
            if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
            }
            DtoQualityConfig qualityConfig = QualityTaskFactory.getInstance().getQcSample(qcType).getQualityConfig(orderReviseVOList);
            if (!(EnumLIM.EnumQCGrade.外部质控.getValue().equals(dtoAnalyseDataTemp.getQcGrade())
                    && (new QualityBlank().qcTypeValue().equals(qcType)
                    || new QualityParallel().qcTypeValue().equals(qcType) || new QualityTransportBlank().qcTypeValue().equals(qcType)))) {
                //是否跟随原样
                if (qualityConfig.getIsFollowSample()) {
                    //原样的上方还是下方(true 是下 false 是 上)
                    Integer qcNumber = qualityConfig.getOrderNumber();
                    if (!qualityConfig.getUpordown()) {
                        qcNumber = 30 - qualityConfig.getOrderNumber() / 10;
                    }
                    orderNum = String.format("%s-%s-%s", "30", codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""), qcNumber);
                    //质控信息是否关联
                    Optional<DtoAnalyseDataTemp> assDataTemp = tempList.stream().filter(p -> p.getSampleId()
                            .equals(dtoAnalyseDataTemp.getAssociateSampleId())).findFirst();
                    if (assDataTemp.isPresent()) {
                        //关联的样品不是原样同时不是全程序空白和现场平行
                        Integer assType = assDataTemp.get().getQcType();
                        if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.串联样.getQcType();
                        }
                        if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
                        }
                        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(assDataTemp.get().getSampleCategory())
                                && !(EnumLIM.EnumQCGrade.外部质控.getValue().equals(assDataTemp.get().getQcGrade())
                                && (new QualityBlank().qcTypeValue().equals(assType)
                                || new QualityParallel().qcTypeValue().equals(assType)
                                || new QualityTransportBlank().qcTypeValue().equals(assType)))) {
                            DtoQualityConfig assQc = QualityTaskFactory.getInstance().getQcSample(assType).getQualityConfig(orderReviseVOList);
                            Integer number = assQc.getOrderNumber();
                            //原样为不关联样品
                            if (!assQc.getExtension()) {
                                if (!EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(dtoAnalyseDataTemp.getSampleCategory())) {
                                    orderNum = String.format("%s-%s-%s", number, codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""),
                                            qualityConfig.getOrderNumber());
                                } else {
                                    if (assQc.getIsFollowSample()) {
                                        if (!assQc.getUpordown()) {
                                            number = 30 - assQc.getOrderNumber() / 10;
                                        }
                                        orderNum = String.format("%s-%s-%s", "30", codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""), number);
                                    } else {
                                        orderNum = String.format("%s-%s-%s", number, codeMap.getOrDefault(dtoAnalyseDataTemp.getGroupSampleId(), ""),
                                                qualityConfig.getOrderNumber());
                                    }
                                }
                            } else {
                                orderNum = String.format("%s-%s", orderNum, number);
                            }
                        }
                    }
                } else {
                    //原样的上方还是下方(true 是下 false 是 上)
                    orderNum = String.format("%s-%s-%s", qualityConfig.getOrderNumber(), dtoAnalyseDataTemp.getSampleCode(), "30");
                }
            }
        }
        return orderNum;
    }

    protected List<OrderReviseVO> getQualityList() {
        XmlConfig xmlConfig = SpringContextAware.getBean(XmlConfig.class);
        return xmlConfig.getQcRulesConfigVO().getQualityReviseVo().getOrderAndRevise();
    }

    /**
     * 初始化样品排序
     *
     * @param sample   样品
     * @param tempList 数据集合
     * @return 排序值
     */
    private String initSampleOrderNum(DtoSample sample, List<DtoAnalyseDataTemp> tempList, List<OrderReviseVO> orderReviseVOList) {
        String orderNum = "30-" + sample.getCode() + "-30";
        Map<String, String> codeMap = tempList.stream().collect(Collectors.groupingBy(DtoAnalyseDataTemp::getSampleId,
                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSampleCode())));
        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())) {
            Integer qcType = sample.getQcType();
            if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(sample.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.串联样.getQcType();
            }
            if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(sample.getSampleCategory())) {
                qcType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
            }
            DtoQualityConfig qualityConfig = QualityTaskFactory.getInstance().getQcSample(qcType).getQualityConfig(orderReviseVOList);
            if (!(EnumLIM.EnumQCGrade.外部质控.getValue().equals(sample.getQcGrade())
                    && (new QualityBlank().qcTypeValue().equals(qcType)
                    || new QualityParallel().qcTypeValue().equals(qcType) || new QualityTransportBlank().qcTypeValue().equals(qcType)))) {
                //是否跟随原样
                if (qualityConfig.getIsFollowSample()) {
                    //原样的上方还是下方(true 是下 false 是 上)
                    Integer qcNumber = qualityConfig.getOrderNumber();
                    if (!qualityConfig.getUpordown()) {
                        qcNumber = 30 - qualityConfig.getOrderNumber() / 10;
                    }
                    Optional<DtoAnalyseDataTemp> tempOptional = tempList.stream().filter(p -> p.getSampleId().equals(sample.getId())).findFirst();
                    String code = tempOptional.isPresent() ? codeMap.getOrDefault(tempOptional.get().getGroupSampleId(), "") : sample.getCode();
//                    tempOptional.ifPresent(p -> sample.setCode(codeMap.getOrDefault(p.getGroupSampleId(), "")));
                    orderNum = String.format("%s-%s-%s", "30", code, qcNumber);
                    //质控信息是否关联
                    Optional<DtoAnalyseDataTemp> assDataTemp = tempList.stream().filter(p -> p.getSampleId()
                            .equals(sample.getAssociateSampleId())).findFirst();
                    if (assDataTemp.isPresent()) {
                        //关联的样品不是原样同时不是全程序空白和现场平行
                        Integer assType = assDataTemp.get().getQcType();
                        if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.串联样.getQcType();
                        }
                        if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                            assType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
                        }
                        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(assDataTemp.get().getSampleCategory())
                                && !(EnumLIM.EnumQCGrade.外部质控.getValue().equals(assDataTemp.get().getQcGrade())
                                && (new QualityBlank().qcTypeValue().equals(assType) || new QualityParallel().qcTypeValue().equals(assType)
                                || new QualityTransportBlank().qcTypeValue().equals(assType)))) {
                            DtoQualityConfig assQc = QualityTaskFactory.getInstance().getQcSample(assType).getQualityConfig(orderReviseVOList);
                            Integer number = assQc.getOrderNumber();
                            //原样为不关联样品
                            if (!assQc.getExtension()) {
                                if (!EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(sample.getSampleCategory())) {
                                    orderNum = String.format("%s-%s-%s", number, code, qualityConfig.getOrderNumber());
                                } else {
                                    if (assQc.getIsFollowSample()) {
                                        if (!assQc.getUpordown()) {
                                            number = 30 - assQc.getOrderNumber() / 10;
                                        }
                                        orderNum = String.format("%s-%s-%s", "30", code, number);
                                    } else {
                                        orderNum = String.format("%s-%s-%s", number, code, qualityConfig.getOrderNumber());
                                    }
                                }
                            } else {
                                orderNum = String.format("%s-%s", orderNum, number);
                            }
                        }
                    }
                } else {
                    //原样的上方还是下方(true 是下 false 是 上)
                    orderNum = String.format("%s-%s-%s", qualityConfig.getOrderNumber(), sample.getCode(), "30");
                }
            }
        }
        return orderNum;
    }
}