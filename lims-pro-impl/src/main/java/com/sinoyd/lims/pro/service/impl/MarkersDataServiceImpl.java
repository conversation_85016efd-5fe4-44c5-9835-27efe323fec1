package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.listener.LIMSEvent;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoMarkersData;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ReportConfigRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.AnalyseOriginalRecordRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.MarkersDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据标记操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/1
 * @since V100R001
 */
@Service
@Slf4j
public class MarkersDataServiceImpl implements MarkersDataService {

    private TestRepository testRepository;
    private AnalyseDataRepository analyseDataRepository;
    private SampleRepository sampleRepository;
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;
    private ParamsFormulaRepository paramsFormulaRepository;
    private QualityControlLimitRepository qualityControlLimitRepository;
    private ReportConfigRepository reportConfigRepository;

    @Override
    public void updateValidate(DtoMarkersData dtoMarkersData) {
        DtoMarkersData markersData = processData(dtoMarkersData);
        // 数据审核后，发布保存关联数据事件
        SpringContextAware.getApplicationContext().publishEvent(
                new LIMSEvent<>(markersData, EnumLIM.EnumMarkersData.MARKERS_DATA.name(), EnumLIM.EnumMarkersData.VALIDATE.name()));
    }

    /**
     * 处理数据
     *
     * @param dtoMarkersData 数据标注参数实体
     * @return DtoMarkersData
     */
    private DtoMarkersData processData(DtoMarkersData dtoMarkersData) {
        List<String> testIds;
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();

        // ids不为空时，表示审核通过
        if (StringUtil.isNotEmpty(dtoMarkersData.getIds())) {
            List<DtoSample> samList = sampleRepository.findByReceiveIdIn(dtoMarkersData.getIds());
            if (StringUtil.isNotEmpty(samList)) {
                List<String> sampleIds = samList.stream().map(DtoSample::getId).collect(Collectors.toList());
                analyseDataList = analyseDataRepository.findBySampleIdInAndIsOutsourcingAndIsSamplingOutAndIsDeletedFalse(sampleIds, false, false);
                analyseDataList = analyseDataList.stream().filter(DtoAnalyseData::getIsCompleteField).collect(Collectors.toList());
            } else {
                // 工作单下分析数据
                analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(dtoMarkersData.getIds());
            }
            // 报表编码不为空实代表生成报表
        } else if (StringUtil.isNotEmpty(dtoMarkersData.getReportCode())) {
            List<DtoReportConfig> reportConfig = reportConfigRepository.findByReportCode(dtoMarkersData.getReportCode());
            // 现场测试项目
            if (StringUtil.isNotEmpty(dtoMarkersData.getReceiveSampleRecordId())) {
                //根据送验单id获取现场分析数据
                List<DtoSample> samList = sampleRepository.findByReceiveId(dtoMarkersData.getReceiveSampleRecordId());
                List<String> sampleIds = samList.stream().map(DtoSample::getId).collect(Collectors.toList());
                analyseDataList = analyseDataRepository.findBySampleIdInAndIsOutsourcingAndIsSamplingOutAndIsDeletedFalse(sampleIds, false, false);
                analyseDataList = analyseDataList.stream().filter(DtoAnalyseData::getIsCompleteField).collect(Collectors.toList());

            }
            // 实验室测试项目
            if (StringUtil.isNotEmpty(dtoMarkersData.getWorkSheetFolderId())) {
                // 工作单下分析数据
                analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(Collections.singletonList(dtoMarkersData.getWorkSheetFolderId()));
            }
            // 报表模版配置使用次数修改
            updateReportConfig(reportConfig.stream().findFirst().orElse(null));
        }

        if (dtoMarkersData.getIsUpdateTestValidate()) {
            //所有现场指标
            testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            // 获取测试项目
            if (StringUtil.isNotEmpty(testIds)) {
                List<DtoTest> dtoTests = testRepository.findAll(testIds);
                //处理存在总称的数据
                List<String> parentIds = dtoTests.stream().filter(DtoTest::getIsTotalTest).map(DtoTest::getId).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(parentIds)) {
                    dtoTests.addAll(testRepository.findByParentIdIn(parentIds));
                }
                List<String> parentIdList = dtoTests.stream().map(DtoTest::getParentId).filter(p -> !p.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
                dtoTests.addAll(StringUtil.isNotEmpty(parentIdList) ? testRepository.findAll(parentIdList) : new ArrayList<>());
                updateTest(dtoTests, dtoMarkersData);
                // 测试项目公式
                updateFormula(analyseDataList, dtoMarkersData);
                // 质控限值
                updateQualityControlLimit(testIds, dtoMarkersData);
            }
        }
        return dtoMarkersData;
    }

    /**
     * 标记测试项目
     *
     * @param dtoTests       测试项目
     * @param dtoMarkersData 标记数据
     */
    private void updateTest(List<DtoTest> dtoTests, DtoMarkersData dtoMarkersData) {
        if (StringUtil.isNotEmpty(dtoTests)) {
            dtoTests.forEach(p -> {
                if (!dtoMarkersData.getIsPass()) {
                    // 退回时减1
                    p.setUsageNum(StringUtil.isNotNull(p.getUsageNum()) && p.getUsageNum() > 1 ? p.getUsageNum() - 1 : 0);
                    if (p.getUsageNum() == 0) {
                        p.setValidate(EnumLIM.EnumCodeValidate.未验证.getValue());
                    }
                } else {
                    p.setUsageNum(StringUtil.isNotNull(p.getUsageNum()) ? p.getUsageNum() + 1 : 1);
                    p.setValidate(EnumLIM.EnumCodeValidate.已验证.getValue());
                }
            });
            List<DtoTest> tests = testRepository.save(dtoTests);
            tests = tests.stream().filter(p -> EnumLIM.EnumCodeValidate.已验证.getValue().equals(p.getValidate())).collect(Collectors.toList());
            dtoMarkersData.setTestList(tests);
        }
    }

    /**
     * 标记公式
     *
     * @param analyseDataList 分析数据集合
     * @param dtoMarkersData  标记数据
     */
    protected void updateFormula(List<DtoAnalyseData> analyseDataList, DtoMarkersData dtoMarkersData) {
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<DtoAnalyseOriginalRecord> originalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList()));
            List<String> formulaId = originalRecords.stream().map(DtoAnalyseOriginalRecord::getTestFormulaId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(formulaId)) {
                List<DtoParamsFormula> paramsFormulas = paramsFormulaRepository.findAll(formulaId);
                paramsFormulas.forEach(p -> {
                    if (!dtoMarkersData.getIsPass()) {
                        // 退回时减1
                        p.setUsageNum(StringUtil.isNotNull(p.getUsageNum()) && p.getUsageNum() > 1 ? p.getUsageNum() - 1 : 0);
                        if (p.getUsageNum() == 0) {
                            p.setValidate(EnumLIM.EnumCodeValidate.未验证.getValue());
                        }
                    } else {
                        p.setUsageNum(StringUtil.isNotNull(p.getUsageNum()) ? p.getUsageNum() + 1 : 1);
                        p.setValidate(EnumLIM.EnumCodeValidate.已验证.getValue());
                    }
                });
                List<DtoParamsFormula> save = paramsFormulaRepository.save(paramsFormulas);
                save = save.stream().filter(p -> EnumLIM.EnumCodeValidate.已验证.getValue().equals(p.getValidate())).collect(Collectors.toList());
                dtoMarkersData.setParamsFormulaList(save);
            }
        }
    }

    /**
     * 标记质控限值
     *
     * @param testIds        测试项目ids
     * @param dtoMarkersData 标记数据
     */
    protected void updateQualityControlLimit(List<String> testIds, DtoMarkersData dtoMarkersData) {
        if (StringUtil.isNotEmpty(testIds)) {
            List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIds);
            qualityControlLimitList.forEach(p -> {
                if (!dtoMarkersData.getIsPass()) {
                    // 退回时减1
                    p.setUsageNum(StringUtil.isNotNull(p.getUsageNum()) && p.getUsageNum() > 1 ? p.getUsageNum() - 1 : 0);
                    if (p.getUsageNum() == 0) {
                        p.setValidate(EnumLIM.EnumCodeValidate.未验证.getValue());
                    }
                } else {
                    p.setUsageNum(StringUtil.isNotNull(p.getUsageNum()) ? p.getUsageNum() + 1 : 1);
                    p.setValidate(EnumLIM.EnumCodeValidate.已验证.getValue());
                }
            });
            qualityControlLimitRepository.save(qualityControlLimitList);
            qualityControlLimitList = qualityControlLimitList.stream().filter(p -> EnumLIM.EnumCodeValidate.已验证.getValue().equals(p.getValidate())).collect(Collectors.toList());
            dtoMarkersData.setQualityControlLimitList(qualityControlLimitList);
        }
    }

    /**
     * 报表模版配置使用次数修改
     *
     * @param dtoReportConfig 模板配置实体
     */
    private void updateReportConfig(DtoReportConfig dtoReportConfig) {
        if (StringUtil.isNotNull(dtoReportConfig)) {
            dtoReportConfig.setUsageNum(StringUtil.isNotNull(dtoReportConfig.getUsageNum()) ? dtoReportConfig.getUsageNum() + 1 : 1);
            dtoReportConfig.setValidate(EnumLIM.EnumCodeValidate.已验证.getValue());
            reportConfigRepository.save(dtoReportConfig);
        }
    }

    @Autowired
    @Lazy
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    @Lazy
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }


    @Autowired
    @Lazy
    public void setAnalyseOriginalRecordRepository(AnalyseOriginalRecordRepository analyseOriginalRecordRepository) {
        this.analyseOriginalRecordRepository = analyseOriginalRecordRepository;
    }

    @Autowired
    @Lazy
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }


    @Autowired
    @Lazy
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    @Lazy
    public void setReportConfigRepository(ReportConfigRepository reportConfigRepository) {
        this.reportConfigRepository = reportConfigRepository;
    }
}
