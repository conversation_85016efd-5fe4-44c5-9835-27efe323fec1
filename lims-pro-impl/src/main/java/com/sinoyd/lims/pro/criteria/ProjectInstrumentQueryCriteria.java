package com.sinoyd.lims.pro.criteria;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.SignatureService;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 仪器出入库查询
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectInstrumentQueryCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目名称、项目编号
     */
    private String projectNameCode;

    /**
     * 仪器名称编号，出厂编号，规格型号
     */
    private String instrumentKey;

    /**
     * 出入库状态 0：所有  1：待入库  2：部分入库  3：已入库
     */
    private Integer storageStatus = EnumLIM.EnumProjectInstrumentStatus.所有.getValue();

    /**
     * 主键集合
     */
    private List<String> ids;

    /**
     * 仪器id
     */
    private String instrumentId;

    @Override
    public String getCondition() {
        //清除条件数据
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date startDate = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.useDate >= :startDate");
            values.put("startDate", startDate);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date endDate = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            c.add(Calendar.DAY_OF_MONTH, 1);
            endDate = c.getTime();
            condition.append(" and a.useDate <= :endDate");
            values.put("endDate", endDate);
        }

        if (StringUtil.isNotEmpty(this.projectNameCode)) {
            ProjectRepository projectRepository = SpringContextAware.getBean(ProjectRepository.class);
            DtoProject project = projectRepository.findByProjectCode(projectNameCode);
            if (StringUtil.isNotNull(project)) {
                condition.append(" and ( a.projectName like :projectNameCode or a.projectId like :projId)");
                values.put("projectNameCode", "%" + this.projectNameCode + "%");
                values.put("projId", "%" + project.getId() + "%");
            } else {
                condition.append(" and ( a.projectName like :projectNameCode)");
                values.put("projectNameCode", "%" + this.projectNameCode + "%");
            }
        }

        if (StringUtil.isNotEmpty(this.instrumentKey)) {
            condition.append(" and exists (select 1 from DtoProjectInstrumentDetails c, DtoInstrument d " +
                    " where c.instrumentId = d.id and d.isDeleted = 0 and (d.instrumentName like :instrumentNameCode " +
                    " or d.instrumentsCode like :instrumentNameCode or d.model like :instrumentNameCode " +
                    " or d.serialNo like :instrumentNameCode) and c.projectInstrumentId = a.id) ");
            values.put("instrumentNameCode", "%" + this.instrumentKey + "%");
        }

        if (!this.storageStatus.equals(EnumLIM.EnumProjectInstrumentStatus.所有.getValue())) {
            //未入库
            if (this.storageStatus.equals(EnumLIM.EnumProjectInstrumentStatus.待入库.getValue())) {
                condition.append(" and not exists (select 1 from DtoProjectInstrumentDetails c " +
                        " where c.isStorage = 1 and c.projectInstrumentId = a.id ) ");
            }
            //部分入库
            else if (this.storageStatus.equals(EnumLIM.EnumProjectInstrumentStatus.部分入库.getValue())) {
                condition.append(" and exists (select 1 from DtoProjectInstrumentDetails c " +
                        " where c.isStorage = 1 and c.projectInstrumentId = a.id ) ");
                condition.append(" and exists (select 1 from DtoProjectInstrumentDetails c " +
                        " where c.isStorage = 0 and c.projectInstrumentId = a.id ) ");
            }
            //已入库
            else if (this.storageStatus.equals(EnumLIM.EnumProjectInstrumentStatus.已入库.getValue())) {
                condition.append(" and not exists (select 1 from DtoProjectInstrumentDetails c " +
                        " where c.isStorage = 0 and c.projectInstrumentId = a.id ) ");
                condition.append(" and (select count(*) from DtoProjectInstrumentDetails c " +
                        " where c.projectInstrumentId = a.id ) > 0 ");
            }
        }

        if (StringUtil.isNotEmpty(this.ids)) {
            condition.append(" and a.id in :ids");
            values.put("ids", this.ids);
        }
        if (StringUtil.isNotEmpty(this.instrumentId)) {
            condition.append(" and exists (select 1 from DtoProjectInstrumentDetails cc, DtoInstrument dd " +
                    " where cc.instrumentId = dd.id and dd.isDeleted = 0 and dd.id = :instrumentId and cc.projectInstrumentId = a.id) ");
            values.put("instrumentId", instrumentId);
        }
        return condition.toString();
    }
}
