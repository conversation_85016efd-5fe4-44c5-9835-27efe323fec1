package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * 分析数据数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/13
 * @since V100R001
 */
public interface AnalyseDataRepository extends IBaseJpaRepository<DtoAnalyseData, String>, LimsRepository<DtoAnalyseData, String> {
    /**
     * 根据样品id集合查询分析数据
     *
     * @param sampleIds 样品id集合
     */
    List<DtoAnalyseData> findBySampleIdIn(Collection<String> sampleIds);

    List<DtoAnalyseData> findBySampleIdInAndIsDeletedFalse(Collection<String> sampleIds);

    /**
     * 根据样品id和检测单id查询分析数据
     *
     * @param sampleIds         样品id集合
     * @param workSheetFolderId 检测单id
     * @return 分析数据集合
     */
    List<DtoAnalyseData> findBySampleIdInAndWorkSheetFolderIdAndIsDeletedFalse(List<String> sampleIds, String workSheetFolderId);

    /**
     * 根据样品id和测试项目id查询分析数据
     *
     * @param sampleId 样品id
     * @param testId   测试项目id
     */
    List<DtoAnalyseData> findBySampleIdAndTestIdAndIsDeletedFalse(String sampleId, String testId);

    /**
     * 根据样品id和测试项目id查询分析数据
     *
     * @param sampleIds 样品id
     * @param testId    测试项目id
     */
    List<DtoAnalyseData> findBySampleIdInAndTestIdAndIsDeletedFalse(List<String> sampleIds, String testId);

    /**
     * 根据样品id和测试项目id查询分析数据
     *
     * @param sampleIds 样品id
     * @param testIds   测试项目id集合
     */
    List<DtoAnalyseData> findBySampleIdInAndTestIdInAndIsDeletedFalse(Collection<String> sampleIds, Collection<String> testIds);

    /**
     * 根据测试项目和质控类型获取数据
     *
     * @param testIds 测试项目
     * @param qcType  质控类型
     * @return 数据集合
     */
    List<DtoAnalyseData> findByTestIdInAndQcTypeAndIsDeletedFalse(List<String> testIds, int qcType);

    /**
     * @param sampleIds     样品ids
     * @param isOutsourcing 是否分包
     * @return
     */
    List<DtoAnalyseData> findBySampleIdInAndIsOutsourcing(List<String> sampleIds, Boolean isOutsourcing);

    List<DtoAnalyseData> findBySampleIdInAndIsOutsourcingAndIsSamplingOutAndIsDeletedFalse(List<String> sampleIds, Boolean isOutsourcing, Boolean isSamplingOut);


    /**
     * 根据样品id集合查询分析数据
     *
     * @param sampleIds 样品id集合
     */
    @Query(value = "select  new com.sinoyd.lims.pro.dto.DtoAnalyseData(a.id,a.sampleId,a.testId," +
            "a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.analyseItemId," +
            "a.analyzeMethodId,a.mostSignificance,a.mostDecimal,a.isQm,a.examLimitValue,a.dimensionId," +
            "a.dimension,a.analystId,a.analystName,a.isCompleteField,a.isOutsourcing,a.grade,a.isPostCert," +
            "a.certEffectiveTime,a.isSamplingOut) " +
            "from DtoAnalyseData as a where (a.isDeleted = 0 or a.status = '作废') and  a.sampleId in :sampleIds")
    List<DtoAnalyseData> findCopyBySampleIdIn(@Param("sampleIds") Collection<String> sampleIds);

    /**
     * 根据样品id查询分析数据
     *
     * @param sampleId 样品id
     */
    List<DtoAnalyseData> findBySampleId(String sampleId);

    List<DtoAnalyseData> findBySampleIdAndIsDeletedFalse(String sampleId);

    /**
     * 根据测试项目Ids找到对应的数据
     *
     * @param testIds   测试项目ids
     * @param testValue 数据值不能为空
     * @return 数据集合
     */
    List<DtoAnalyseData> findByTestIdInAndTestValueNotAndIsDeletedFalse(List<String> testIds, String testValue);

    /**
     * 根据样品id查询分析数据
     *
     * @param sampleId 样品id
     */
    @Query("select a from DtoAnalyseData a where a.isDeleted = 0 and a.sampleId = :sampleId and (a.workSheetFolderId = '00000000-0000-0000-0000-000000000000')")
    List<DtoAnalyseData> findBySampleIdAndWorkSheetFolderIdEmpty(@Param("sampleId") String sampleId);

    /**
     * 根据检测单id获取分析数据
     *
     * @param workSheetFolderId 检测单id
     * @return 返回相应分析数据
     */
    List<DtoAnalyseData> findByWorkSheetFolderId(String workSheetFolderId);

    List<DtoAnalyseData> findByWorkSheetFolderIdAndIsDeletedFalse(String workSheetFolderId);

    /**
     * 根据检测单id获取相关的分析数据
     *
     * @param workSheetIds 检测单ids
     * @return 返回相应分析数据
     */
    List<DtoAnalyseData> findByWorkSheetIdIn(List<String> workSheetIds);

    List<DtoAnalyseData> findByWorkSheetIdInAndIsDeletedFalse(List<String> workSheetIds);

    /**
     * 根据检测单id获取分析数据
     *
     * @param workSheetFolderIds
     * @return 返回相应分析数据
     */
    List<DtoAnalyseData> findByWorkSheetFolderIdIn(List<String> workSheetFolderIds);

    List<DtoAnalyseData> findByWorkSheetFolderIdInAndIsDeletedFalse(List<String> workSheetFolderIds);

    /**
     * 批量办结样品数据状态
     *
     * @param sampleIds  样品的ids
     * @param dataStatus 数据状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.status = :status,a.dataStatus = :dataStatus,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.sampleId in :sampleIds")
    Integer finishAnalyseData(@Param("sampleIds") List<String> sampleIds, @Param("status") String status, @Param("dataStatus") Integer dataStatus, @Param("modifier") String modifier,
                              @Param("modifyDate") Date modifyDate);

    /**
     * 批量清除样品数据
     *
     * @param sampleIds 样品的ids
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.workSheetFolderId = '00000000-0000-0000-0000-000000000000'," +
            "a.workSheetId = '00000000-0000-0000-0000-000000000000',a.isDeleted = 1, a.modifier = :modifier," +
            "a.modifyDate= :modifyDate where a.isDeleted = 0 and a.sampleId in :sampleIds")
    Integer deleteAnalyseData(@Param("sampleIds") List<String> sampleIds, @Param("modifier") String modifier,
                              @Param("modifyDate") Date modifyDate);

    /**
     * 根据样品id及分析项目ids找到相关的分析数据
     *
     * @param sampleId       样品id
     * @param analyseItemIds 分析项目ids
     * @return 返回相关的分析数据
     */
    List<DtoAnalyseData> findBySampleIdAndAnalyseItemIdIn(String sampleId, List<String> analyseItemIds);

    List<DtoAnalyseData> findBySampleIdAndAnalyseItemIdInAndIsDeletedFalse(String sampleId, List<String> analyseItemIds);

    /**
     * 只用于质控样的删除，不影响数据查询
     *
     * @param ids 数据ids
     * @return 返回相应的删除行数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoAnalyseData as  s where s.isDeleted = 0 and s.id in :ids")
    Integer deleteByIds(@Param("ids") List<String> ids);

    /**
     * 分析数据假删
     *
     * @param ids 数据ids
     * @return 返回相应的删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.isDeleted= true,a.workSheetId='00000000-0000-0000-0000-000000000000',a.workSheetFolderId='00000000-0000-0000-0000-000000000000'," +
            "a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer logicDeleteByIds(@Param("ids") List<String> ids,
                             @Param("modifier") String modifier,
                             @Param("modifyDate") Date modifyDate);

    /**
     * 分析数据分包
     *
     * @param ids 数据ids
     * @return 返回相应的删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.isOutsourcing= true,a.isDataEnabled=true,a.workSheetId='00000000-0000-0000-0000-000000000000'," +
            "a.workSheetFolderId='00000000-0000-0000-0000-000000000000',a.isSamplingOut = false," +
            "a.status= :status,a.dataStatus= :dataStatus,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer subAnalyseData(@Param("ids") List<String> ids,
                           @Param("status") String status,
                           @Param("dataStatus") Integer dataStatus,
                           @Param("modifier") String modifier,
                           @Param("modifyDate") Date modifyDate);

    /**
     * 分析数据分包
     *
     * @param ids 数据ids
     * @return 返回相应的删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.isOutsourcing = true,a.workSheetId='00000000-0000-0000-0000-000000000000'," +
            "a.workSheetFolderId='00000000-0000-0000-0000-000000000000',a.isSamplingOut = false," +
            "a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer subAnalyseData(@Param("ids") List<String> ids,
                           @Param("modifier") String modifier,
                           @Param("modifyDate") Date modifyDate);

    /**
     * 分析数据取消分包
     *
     * @param ids 数据ids
     * @return 返回相应的删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.isOutsourcing= false ,a.isDataEnabled=false,a.isSamplingOut = false," +
            "a.status = :status,a.dataStatus= :dataStatus,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer cancelSubAnalyseData(@Param("ids") List<String> ids,
                                 @Param("status") String status,
                                 @Param("dataStatus") Integer dataStatus,
                                 @Param("modifier") String modifier,
                                 @Param("modifyDate") Date modifyDate);

    /**
     * 分析数据采测分包
     *
     * @param ids 数据ids
     * @return 返回相应的删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.isOutsourcing= false ,a.isDataEnabled=false,a.isSamplingOut = true," +
            "a.status = :status,a.dataStatus= :dataStatus,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer samplingSubAnalyseData(@Param("ids") List<String> ids,
                                   @Param("status") String status,
                                   @Param("dataStatus") Integer dataStatus,
                                   @Param("modifier") String modifier,
                                   @Param("modifyDate") Date modifyDate);

    /**
     * 批量更新数据状态
     *
     * @param ids        数据ids
     * @param status     数据状态
     * @param dataStatus 状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新数据
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.status= :status,a.dataStatus= :dataStatus,a.isDataEnabled=:isDataEnabled,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer updateAnalyseDataStatus(@Param("ids") List<String> ids,
                                    @Param("status") String status,
                                    @Param("dataStatus") Integer dataStatus,
                                    @Param("isDataEnabled") Boolean isDataEnabled,
                                    @Param("modifier") String modifier,
                                    @Param("modifyDate") Date modifyDate);

    /**
     * 批量更新现场非分包数据状态
     *
     * @param sampleIds  样品ids
     * @param status     数据状态
     * @param dataStatus 状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新数据
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.status= :status,a.dataStatus= :dataStatus,a.isDataEnabled=:isDataEnabled,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.sampleId in :sampleIds and a.isOutsourcing = false and a.isCompleteField = true")
    Integer updateLocalStatusBySample(@Param("sampleIds") List<String> sampleIds,
                                      @Param("status") String status,
                                      @Param("dataStatus") Integer dataStatus,
                                      @Param("isDataEnabled") Boolean isDataEnabled,
                                      @Param("modifier") String modifier,
                                      @Param("modifyDate") Date modifyDate);

    /**
     * 批量修改分析人员
     *
     * @param ids         数据ids
     * @param analystId   分析人id
     * @param analystName 分析人
     * @param status      状态
     * @param dataStatus  状态
     * @param modifier    修改人
     * @param modifyDate  修改时间
     * @return 返回更新数据
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.analystId = :analystId,a.analystName = :analystName,a.domainId=:domainId,a.workSheetId='00000000-0000-0000-0000-000000000000'," +
            "a.workSheetFolderId='00000000-0000-0000-0000-000000000000', a.status= :status,a.dataStatus= :dataStatus,a.isDataEnabled=false," +
            "a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer updateAnalyst(@Param("ids") List<String> ids,
                          @Param("analystId") String analystId,
                          @Param("analystName") String analystName,
                          @Param("domainId") String domainId,
                          @Param("status") String status,
                          @Param("dataStatus") Integer dataStatus,
                          @Param("modifier") String modifier,
                          @Param("modifyDate") Date modifyDate);

    /**
     * 批量修改分析方法
     *
     * @param ids         数据ids
     * @param analystId   分析人id
     * @param analystName 分析人
     * @param status      状态
     * @param dataStatus  状态
     * @param modifier    修改人
     * @param modifyDate  修改时间
     * @return 返回更新数据
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.testId = :testId,a.analyzeMethodId = :analyzeMethodId,a.redAnalyzeMethodName = :redAnalyzeMethodName,a.redCountryStandard = :redCountryStandard," +
            "a.dataInputTime = '1753-01-01',a.analyzeTime = '1753-01-01',a.mostSignificance = :mostSignificance,a.mostDecimal = :mostDecimal, a.examLimitValue = :examLimitValue," +
            "a.dimensionId = :dimensionId,a.dimension = :dimension, a.analystId = :analystId,a.analystName = :analystName,a.domainId=:domainId,a.workSheetId='00000000-0000-0000-0000-000000000000'," +
            "a.workSheetFolderId='00000000-0000-0000-0000-000000000000',a.testValue = '',a.testOrignValue = '',a.testValueDstr = '',a.testValueD = 0, a.status= :status,a.dataStatus= :dataStatus,a.isDataEnabled=false," +
            "a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id in :ids")
    Integer updateMethod(@Param("ids") List<String> ids,
                         @Param("testId") String testId,
                         @Param("analyzeMethodId") String analyzeMethodId,
                         @Param("redAnalyzeMethodName") String redAnalyzeMethodName,
                         @Param("redCountryStandard") String redCountryStandard,
                         @Param("mostSignificance") Integer mostSignificance,
                         @Param("mostDecimal") Integer mostDecimal,
                         @Param("examLimitValue") String examLimitValue,
                         @Param("dimensionId") String dimensionId,
                         @Param("dimension") String dimension,
                         @Param("analystId") String analystId,
                         @Param("analystName") String analystName,
                         @Param("domainId") String domainId,
                         @Param("status") String status,
                         @Param("dataStatus") Integer dataStatus,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate);


    /**
     * 根据组织架构id 测试项目ids 检测单ids 查询所需数据
     *
     * @param testIds           测试项目ids
     * @param orgId             组织结构id
     * @param workSheetFolderId 检测单ID
     * @return 返回数据
     */
    @Query("select a from DtoAnalyseData as a where a.isDeleted = 0 and a.testId in :testIds and a.orgId = :orgId and a.workSheetFolderId = :workSheetFolderId and a.isCompleteField = 0")
    List<DtoAnalyseData> findByTestIdInAndOrgIdAAndWorkSheetFolderId(@Param("testIds") List<String> testIds, @Param("orgId") String orgId, @Param("workSheetFolderId") String workSheetFolderId);

    /**
     * 修改交接状态
     *
     * @param dataStatus    数据状态
     * @param status        状态
     * @param isDataEnabled 是否出证
     * @param modifier      修改人
     * @param modifyDate    修改时间
     * @param id            数据id
     * @return 返回修改行数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.dataStatus= :dataStatus,a.status= :status,a.isDataEnabled =:isDataEnabled," +
            "a.analystId = :analystId,a.analystName = :analystName,a.modifier = :modifier,a.modifyDate= :modifyDate where a.isDeleted = 0 and a.id = :id")
    Integer innerAnalyseStatus(@Param("dataStatus") Integer dataStatus,
                               @Param("status") String status,
                               @Param("isDataEnabled") Boolean isDataEnabled,
                               @Param("analystId") String analystId,
                               @Param("analystName") String analystName,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate,
                               @Param("id") String id);

    /**
     * 批量修改数据更改状态
     *
     * @param ids 样品的ids
     * @return 返回相应的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoAnalyseData a set a.dataChangeStatus = :dataChangeStatus,a.modifyDate=:modifyDate,a.modifier=:modifier where a.isDeleted = 0 and a.id in :ids")
    Integer updateDataChangeStatus(@Param("ids") List<String> ids,
                                   @Param("dataChangeStatus") Integer dataChangeStatus,
                                   @Param("modifier") String modifier,
                                   @Param("modifyDate") Date modifyDate);

    /**
     * 按工作单id进行数据分组
     *
     * @param workSheetFolderIds 工作单ids
     * @return 返回数据
     */
    @Query("select count(a.id),a.workSheetFolderId from DtoAnalyseData a where a.isDeleted = 0 and a.workSheetFolderId in :workSheetFolderIds " +
            "and a.dataStatus in :dataStatus group by a.workSheetFolderId")
    List groupAnalyseDataNumByWorkSheetFolderId(@Param("workSheetFolderIds") List<String> workSheetFolderIds,
                                                @Param("dataStatus") List<Integer> dataStatus);

    /**
     * 按工作单id进行数据分组（不按照数据状态过滤）
     *
     * @param workSheetFolderIds 工作单ids
     * @return 返回数据
     */
    @Query("select count(a.id),a.workSheetFolderId from DtoAnalyseData a where a.isDeleted = 0 and a.workSheetFolderId in :workSheetFolderIds " +
            "group by a.workSheetFolderId")
    List groupAnalyseDataNumByWorkSheetFolderIdIgnoreStatus(@Param("workSheetFolderIds") List<String> workSheetFolderIds);


    /**
     * 按工作单id查询相应的等级
     *
     * @param workSheetFolderIds 工作单ids
     * @return 返回数据
     */
    @Query("select max(a.grade),a.workSheetFolderId from DtoAnalyseData a where a.isDeleted = 0 and a.workSheetFolderId in :workSheetFolderIds group by a.workSheetFolderId")
    List findGradeByWorkSheetFolderId(@Param("workSheetFolderIds") List<String> workSheetFolderIds);

    /**
     * 修改假删的数据
     *
     * @param ids 数据ids
     */
    @Transactional
    @Modifying
    @Query(value = "update DtoAnalyseData a set a.isDeleted = 0 , a.status = '未测' , a.dataStatus = 1  where a.id in :ids")
    Integer updateDeleteAnalyseStatus(@Param("ids") List<String> ids);

    /**
     * 通过样品ids找到对应的数据
     *
     * @param sampleIds 样品ids
     * @return 数据集合
     */
    @Query(value = "select a.id,a.sampleId from DtoAnalyseData as a where ( a.isDeleted = 0 or a.status = '作废') and a.sampleId in :sampleIds")
    List findAnalyseDataStatusAll(@Param("sampleIds") List<String> sampleIds);

    /**
     * 通过样品id找到对应的数据
     *
     * @param sampleId 样品id
     * @return 数据集合
     */
    @Query(value = "select a.id,a.sampleId,a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard," +
            "a.examLimitValue,a.dimension,a.isCompleteField,a.isOutsourcing,a.testId,a.isSamplingOut,a.lowerLimit " +
            "from DtoAnalyseData as a where ( a.isDeleted = 0 or a.status = '作废') and a.sampleId = :sampleId")
    List<Object[]> findAnalyseDataListBySampleId(@Param("sampleId") String sampleId);

    /**
     * 根据样品id删除测试项目
     *
     * @param sampleId 样品id
     * @return 删除数量
     */
    Integer deleteAllBySampleId(String sampleId);

    /**
     * 根据样品id集合删除测试项目
     *
     * @param sampleIds 样品id集合
     * @return 删除的数量
     */
    Integer deleteAllBySampleIdIn(List<String> sampleIds);


    /**
     * 根据样品id查询现场数据
     *
     * @param sampleIds 样品id集合
     * @return 数据集合
     */
    @Query("select a from DtoAnalyseData a where a.isDeleted = false " +
            "and a.isOutsourcing = false " +
            "and a.isCompleteField = true " +
            "and a.sampleId in :sampleIds")
    List<DtoAnalyseData> findLocalData(@Param("sampleIds") Collection<String> sampleIds);

    /**
     * 根据测试项目id以及工作单id查询数据
     *
     * @param testIds           测试项目id集合
     * @param workSheetFolderId 工作单id
     * @return 分析数据
     */
    List<DtoAnalyseData> findByTestIdInAndWorkSheetFolderIdAndIsDeletedFalse(List<String> testIds, String workSheetFolderId);

    /**
     * 根据测试项目ids，质控类型，分析人员获取分析数据
     *
     * @param testIds   测试项目ids
     * @param qcType    质控类型
     * @param analystId 分析人员id
     * @return 分析数据集合
     */
    List<DtoAnalyseData> findByTestIdInAndQcTypeAndAnalystIdAndIsCompleteFieldFalseAndIsDataEnabledTrueOrderByAnalyzeTimeDesc(List<String> testIds, Integer qcType, String analystId);
}