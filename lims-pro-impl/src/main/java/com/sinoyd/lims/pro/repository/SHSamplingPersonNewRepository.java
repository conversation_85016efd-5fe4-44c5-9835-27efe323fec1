package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSHSamplingPersonNew;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *  采样人员repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/11/25
 */
public interface SHSamplingPersonNewRepository extends IBaseJpaPhysicalDeleteRepository<DtoSHSamplingPersonNew,String> {

    /**
     * 根据任务id查询采样人员配置
     * @param taskId 任务id
     * @return List<DtoSHSamplingPersonNew>
     */
    List<DtoSHSamplingPersonNew> findByTaskId(String taskId);

    /**
     * 根据任务id集合查询采样人员配置
     * @param taskIds 任务id集合
     * @return List<DtoSHSamplingPersonNew>
     */
    List<DtoSHSamplingPersonNew> findByTaskIdIn(List<String> taskIds);

    /**
     * 根据任务id删除采样人员配置
     * @param taskIds 任务id集合
     * @return 删除条数
     */
    @Transactional
    Integer deleteByTaskIdIn(List<String> taskIds);

}
