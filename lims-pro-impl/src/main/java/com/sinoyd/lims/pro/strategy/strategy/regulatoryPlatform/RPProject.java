package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.RPProjectVO;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 上海监管平台委托任务信息策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/12
 */
@Component
public class RPProject extends AbsRegulatoryPlatformRemote<RPProjectVO> {

    @Override
    @Transactional
    public void handlePushResult(RegulatoryPlatformPushVO pushVo, JSONObject result) {
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(pushVo.getId()).stream().findFirst();
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            JSONObject response = getResponse(result, insertMethodName()).getJSONObject("ResultStruct");
            if (response != null && "True".equals(response.getString("succes"))) {
                String rpProjectId = response.getString("ID");
                projectContract.setPId(rpProjectId);
                projectContract.setHasPush(1);
                projectContractRepository.save(projectContract);
            }
        }
    }

    @Override
    protected void validatePushData(RegulatoryPlatformPushVO pushVo) {
        DtoProject project = projectService.findOne(pushVo.getId());
        if (project == null) {
            throw new BaseException("LIMS:需推送的项目不存在！");
        }
        //项目合同信息校验
        validateContract(project);
        //项目状态校验
        validateStatus(project);
        //校验文档附件
        validateDoc(pushVo);
    }

    @Override
    protected RPProjectVO getPushInstance(RegulatoryPlatformPushVO pushVo) {
        DtoProject project = projectService.findOne(pushVo.getId());
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(project.getId()).stream().findFirst();
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            DtoArea area = areaService.findById(projectContract.getTaskLocation());
            return new RPProjectVO(project, projectContract, area);
        }
        return super.getPushInstance(pushVo);
    }


    @Override
    protected void handlePushDoc(RegulatoryPlatformPushVO pushVo, RPProjectVO data) {
        //设置推送数据中的附件名称与附件路径
        handlePushFileName(pushVo, data);
    }

    /**
     * 项目合同信息校验
     *
     * @param project 项目信息
     */
    private void validateContract(DtoProject project) {
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(project.getId()).stream().findFirst();
        //判断项目合同信息
        if (!projectContractOp.isPresent()) {
            throw new BaseException("LIMS：项目不存在推送与信息管理信息，请确认！");
        }
        //判断关联监管平台合同
        DtoProjectContract projectContract = projectContractOp.get();
        if (UUIDHelper.GUID_EMPTY.equals(projectContract.getContractId()) || StringUtil.isEmpty(projectContract.getContractId())) {
            throw new BaseException("LIMS：项目信息未关联监管平台合同，请确认！");
        }
        //判断被测对象
        if (StringUtil.isEmpty(projectContract.getTestTarget())) {
            throw new BaseException("LIMS：项目推送与信息管理未选择被测对象单位信息，请确认！");
        }
        //判断合同金额
        Optional<DtoOrderContract> orderContractOp = orderContractRepository.findByCId(projectContract.getContractId()).stream().findFirst();
        if (orderContractOp.isPresent()) {
            DtoOrderContract orderContract = orderContractOp.get();
            if (projectContract.getTaskPrice() != null) {
                if (projectContract.getTaskPrice().compareTo(orderContract.getTotalAmount()) > 0) {
                    throw new BaseException("LIMS：项目金额大于合同金额，请确认！");
                }
            }
        }
    }

    /**
     * 项目状态校验
     *
     * @param project LIMS项目信息
     */
    private void validateStatus(DtoProject project) {
        List<String> statusList = Stream.of(
                        EnumPRO.EnumProjectStatus.项目下达中.name(), EnumPRO.EnumProjectStatus.方案审核中.name(),
                        EnumPRO.EnumProjectStatus.方案未通过.name(), EnumPRO.EnumProjectStatus.方案确认中.name(),
                        EnumPRO.EnumProjectStatus.方案编制中.name(), EnumPRO.EnumProjectStatus.开展中.name(),
                        EnumPRO.EnumProjectStatus.数据汇总中.name(), EnumPRO.EnumProjectStatus.已办结.name())
                .collect(Collectors.toList());
        if (!statusList.contains(project.getStatus())) {
            throw new BaseException("LIMS：项目需审核完成后才可推送！");
        }
    }

    /**
     * 设置推送数据中的附件名称与附件路径
     *
     * @param pushVo 推送传参
     */
    private void validateDoc(RegulatoryPlatformPushVO pushVo) {
        Optional<DtoDocument> docOp = documentRepository.findByFolderIdAndIsDeletedFalse(pushVo.getId())
                .stream().filter(p-> "BASE_DocumentExtendType_DJWD".equals(p.getDocTypeId())).findFirst();
        //判断是否上传合同附件
        if (docOp.isPresent()) {
            //判断附件类型是否为pdf
            if (!docOp.get().getDocSuffix().contains("pdf")) {
                throw new BaseException("LIMS：需推送的项目附件类型必须为pdf类型！");
            }
        }
    }

    /**
     * 设置推送数据中的附件名称与附件路径
     *
     * @param pushVo 推送传参
     * @param data   推送数据
     */
    private void handlePushFileName(RegulatoryPlatformPushVO pushVo, RPProjectVO data) {
        Optional<DtoDocument> docOp = documentRepository.findByFolderIdAndIsDeletedFalse(pushVo.getId()).stream()
                .filter(p-> "BASE_DocumentExtendType_DJWD".equals(p.getDocTypeId())).findFirst();
        //判断是否上传合同附件
        if (docOp.isPresent()) {
            String fileName = docOp.get().getFilename();
            String rpFileName = pushRemoteFile(docOp.get(), EnumPRO.EnumRegulatoryPlatformDocType.监测任务其他附件.getValue());
            data.setFileName(fileName);
            data.setRpFileName(rpFileName);
        }
    }


    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.委托任务信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.委托任务信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.委托任务信息.getDeleteMethod();
    }
}
