package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RPPollutantDischargingUnitVO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 上海监管平台排污单位信息_社会信用代码策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/13
 */
@Component
public class RPPollutantDischargingUnitInfo extends AbsRegulatoryPlatformRemote<RPPollutantDischargingUnitVO> {

    @Override
    protected void filterCriteria(List<RPPollutantDischargingUnitVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //分析方法模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getKey())) {
            list.removeIf(e -> !e.getEnterpriseName().contains(methodCriteria.getKey()));
        }
    }

    @Override
    protected String loadFindSoapBody(String userName, String password, BaseCriteria criteria, PageBean<RPPollutantDischargingUnitVO> pageBean) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        Map<String, Object> criteriaMap = new HashMap<>();
        if (StringUtil.isNotEmpty(methodCriteria.getSocialCreditCode())){
            criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "socialCreditCode", methodCriteria.getSocialCreditCode());
        } else {
            throw new BaseException("LIMS：查询条件[社会信用代码]必填！");
        }
        return RPSoapBodyUtil.loadSoapBody(userName, password, queryMethodName(), criteriaMap);
    }

    @Override
    protected List<RPPollutantDischargingUnitVO> parseResult(JSONObject response, String methodName) {
        List<RPPollutantDischargingUnitVO> pollutionUnitVos = super.parseResult(response, methodName);
        //根据区域名称获取到本系统区域Id
        Set<String> areaNames = pollutionUnitVos.stream().map(RPPollutantDischargingUnitVO::getCountry).collect(Collectors.toSet());
        Map<String, String> areaIdMap = getAreaIdMap(areaNames);
        for (RPPollutantDischargingUnitVO pollutionUnitVo : pollutionUnitVos) {
            pollutionUnitVo.setAreaId(areaIdMap.get(pollutionUnitVo.getCountry()));
        }
        return pollutionUnitVos;
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.排污单位信息_社会信用代码条件.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.排污单位信息_社会信用代码条件.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.排污单位信息_社会信用代码条件.getDeleteMethod();
    }
}
