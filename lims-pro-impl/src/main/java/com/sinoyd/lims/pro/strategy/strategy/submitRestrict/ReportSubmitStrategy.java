package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.customer.DtoQcSampleEvaluateDetail;
import com.sinoyd.lims.pro.dto.customer.DtoReportDetailQuery;
import com.sinoyd.lims.pro.dto.customer.DtoSampleTemp;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告提交
 *
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_REPORT)
public class ReportSubmitStrategy extends AbsSubmitRestrictStrategy {

    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String reportId = objMap.toString();
        DtoReport report = reportService.findOne(reportId);
        //校验提交报告状态
        restrictVoList.add(checkStatus(status, report.getStatus(), EnumPRO.EnumRestrictItem.提交状态验证));
        DtoReportDetailQuery detailQuery = new DtoReportDetailQuery();
        detailQuery.setReportId(reportId);
        List<DtoSampleTemp> tempList = reportDetailService.queryReport(detailQuery);
        List<String> personIds = Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId());
        if (report.getStatus().equals(EnumPRO.EnumReportState.编制报告中.name())
                || report.getStatus().equals(EnumPRO.EnumReportState.报告未通过.name())) {
            //生成报告 -ok
            restrictVoList.add(checkReportDocument(reportId));
            personIds = Collections.singletonList(report.getCreatePersonId());
        }
        //报告编号 -ok
        restrictVoList.add(checkReportCode(report));
        //质控信息 -ok
        restrictVoList.add(checkQualitySample(reportId));
        //是否关联样品 -ok
        restrictVoList.add(checkReportSample(tempList));
        //是否样品检毕 -ok
        restrictVoList.add(checkSampleStatus(tempList));
        //编制人有无签名 -ok
        restrictVoList.add(super.checkSigUrl(personIds, EnumPRO.EnumRestrictItem.报告电子签名));
        // 监管平台编号校验
        checkRegulateCode(restrictVoList, report);
        return restrictVoList;
    }


    protected DtoSubmitRestrictVo checkReportCode(DtoReport report) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.报告编号.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.报告编号.getModuleName());
        if (!StringUtil.isNotEmpty(report.getCode())
                && EnumPRO.EnumReportState.报告签发中.name().equals(report.getStatus())) {
            restrictVo.setExceptionOption("报告未生成报告编号");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 验证质控信息
     *
     * @param reportId 报告id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkQualitySample(String reportId) {
        PageBean<DtoQcSampleEvaluateDetail> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DtoQcSampleEvaluateDetail evaluateDetail = new DtoQcSampleEvaluateDetail();
        evaluateDetail.setReportIdList(Collections.singletonList(reportId));
        List<DtoQcSampleEvaluateDetail> detailList = qualityControlEvaluateService.findEvaluateDetailByPage(pageBean, evaluateDetail);
        Set<String> unPassSampleCodeSet = new HashSet<>();
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.质控信息.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.质控信息.getModuleName());
        for (DtoQcSampleEvaluateDetail detail : detailList) {
            if (StringUtil.isNotEmpty(detail.getIsPass()) && "0".equals(detail.getIsPass())) {
                unPassSampleCodeSet.add(detail.getSampleCode());
            }
        }
        if (unPassSampleCodeSet.size() > 0) {
            restrictVo.setExceptionOption("质控样品：" + String.join("、", unPassSampleCodeSet) + " 不合格!");
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否出具报告
     *
     * @param reportId 报告id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkReportDocument(String reportId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.报告验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.报告验证.getModuleName());
        List<DtoDocument> documentList = super.checkDocument(reportId, BaseCodeHelper.DOCUMENT_EXTEND_REPORT);
        if (documentList.size() == 0) {
            restrictVo.setExceptionOption("未生成报告");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否关联样品
     *
     * @param tempList 样品列表
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkReportSample(List<DtoSampleTemp> tempList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.关联样品.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.关联样品.getModuleName());
        if (tempList.size() == 0) {
            restrictVo.setExceptionOption("报告未有关联样品");
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否样品检毕
     *
     * @param tempList 样品列表
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSampleStatus(List<DtoSampleTemp> tempList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.样品检毕.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.样品检毕.getModuleName());
        List<DtoSampleTemp> sampleTempList = tempList.stream()
                .filter(p -> !EnumPRO.EnumSampleStatus.样品检毕.name().equals(p.getStatus())).collect(Collectors.toList());
        if (sampleTempList.size() > 0) {
            List<String> codeList = sampleTempList.stream().map(DtoSampleTemp::getCode).collect(Collectors.toList());
            restrictVo.setExceptionOption(String.format("样品：%s未检毕", String.join("、", codeList)));
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }


    /**
     * 监管平台编号校验
     *
     * @param restrictVoList 验证结果集合
     * @param report         报告实体
     */
    private void checkRegulateCode(List<DtoSubmitRestrictVo> restrictVoList, DtoReport report) {
        if (report.getStatus().equals(EnumPRO.EnumReportState.报告签发中.name())){
            String projectId = report.getProjectId();
            Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(projectId).stream().findFirst();
            if (projectContractOp.isPresent() && projectContractOp.get().getIsPush() == 1) {
                DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
                restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.监管平台系统编号验证.getCheckItem());
                restrictVo.setModuleName(EnumPRO.EnumRestrictItem.监管平台系统编号验证.getModuleName());
                if (StringUtil.isEmpty(report.getRegulateCode())) {
                    restrictVo.setExceptionOption("监管平台系统编号未生成");
                    restrictVo.setIsUnusual(Boolean.FALSE);
                }
                restrictVoList.add(restrictVo);
            }
        }
    }
}
