package com.sinoyd.lims.pro.service.impl;

import com.aspose.cells.Cell;
import com.aspose.cells.ProtectionType;
import com.aspose.cells.Style;
import com.aspose.cells.*;
import com.aspose.words.SaveFormat;
import com.aspose.words.*;
import com.google.gson.Gson;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.BaseLicenseService;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.SystemConfigService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.entity.Person;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoSigDocument;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.ReportService;
import com.sinoyd.lims.pro.service.SignatureService;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单签名
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
@Service
@Slf4j
public class SignatureServiceImpl implements SignatureService {

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    @Lazy
    private ReportService reportService;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    @Lazy
    private BaseLicenseService licenseService;

    @Autowired
    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    @Autowired
    private SampleRepository sampleRepository;

    private SystemConfigService systemConfigService;

    private StatusForReportRepository statusForReportRepository;
    private ReportDetailRepository reportDetailRepository;

    private ProjectRepository projectRepository;
    private ProjectContractRepository projectContractRepository;


    /**
     * @param repId      报表id
     * @param personIds  签名人员id
     * @param personType 签名类型
     * @param docTypeId  文件类型
     * @param timeStr    日期
     * @throws Exception
     */
    @Override
    public void sig(String repId, List<String> personIds, Integer personType, String docTypeId, String timeStr) throws Exception {
        this.sig(repId, personIds, personType, docTypeId, timeStr, Boolean.FALSE);
    }

    @Override
    public void sig(String repId, List<String> docIds, List<String> personIds, Integer personType, String docTypeId, String timeStr) {
        List<DtoDocument> documentList = documentRepository.findAll(docIds);
        this.sing(repId, personIds, personType, docTypeId, timeStr, Boolean.FALSE, documentList);
    }


    /**
     * 报表签名
     *
     * @param repId      报表id
     * @param personIds  签名人员id
     * @param personType 签名类型
     */
    @Override
    public void sig(String repId, List<String> personIds, Integer personType, String docTypeId, String timeStr, Boolean isXC) throws Exception {
        List<DtoDocument> documentList = documentRepository.findByFolderId(repId);
        documentList = documentList.stream().filter(p -> !p.getFilename().contains("副本")
                && (p.getDocSuffix().contains("xls") || p.getDocSuffix().contains("doc"))
                && !p.getIsDeleted()).collect(Collectors.toList());
        this.sing(repId, personIds, personType, docTypeId, timeStr, isXC, documentList);
    }

    private void sing(String repId, List<String> personIds, Integer personType, String docTypeId, String timeStr, Boolean isXC, List<DtoDocument> documentList) {
        DtoReport report = reportService.findAttachPath(repId);
        if (StringUtil.isNotEmpty(docTypeId)) {
            documentList = documentList.stream().filter(p -> p.getDocTypeId().equals(docTypeId)).collect(Collectors.toList());
        }
        List<String> perIds = new ArrayList<>();
        //找到采样人员
        String personId = UUIDHelper.GUID_EMPTY;
        if (personIds.size() > 0) {
            personId = personIds.get(0);
        }
        if (UUIDHelper.GUID_EMPTY.equals(personId) && !personType.equals(EnumPRO.EnumSigType.陪同人.getValue()) && !personType.equals(EnumPRO.EnumSigType.执法人员.getValue())) {
            perIds = samplingPersonConfigRepository.findByObjectTypeAndObjectId(EnumPRO.EnumSamplingType.送样单.getValue(), repId)
                    .stream().map(DtoSamplingPersonConfig::getSamplingPersonId).distinct().collect(Collectors.toList());
        }
        for (DtoDocument document : documentList) {
            try {
                if (EnumPRO.EnumSigType.maker.getValue().equals(personType)) {
                    signature(repId, document.getPath(), document.getPhysicalName(), Collections.singletonList(personId), personType, "", false, document.getDocSize(), 1, new ArrayList<>(), timeStr, isXC);
                    if (!report.getCertifiedPersonId().equals(UUIDHelper.GUID_EMPTY)) {
                        signature(repId, document.getPath(), document.getPhysicalName(), Collections.singletonList(report.getCertifiedPersonId()), EnumPRO.EnumSigType.certificateMaker.getValue(), "", false, document.getDocSize(), 1, new ArrayList<>(), timeStr, isXC);
                    }
                } else {
                    if (UUIDHelper.GUID_EMPTY.equals(personId)) {
                        //找到采样人员（多个）
                        if (perIds.size() > 0) {
                            signature(repId, document.getPath(), document.getPhysicalName(), perIds, personType, "", false, document.getDocSize(), 0, new ArrayList<>(), timeStr, isXC);
                        }
                    } else {
                        if (EnumPRO.EnumSigType.陪同人.getValue().equals(personType) || EnumPRO.EnumSigType.执法人员.getValue().equals(personType)) {
                            personIds = personIds.stream().filter(p -> new File(filePathConfig.getFilePath() + p).exists()).collect(Collectors.toList());
                            if (personIds.size() > 0) {
                                signature(repId, document.getPath(), document.getPhysicalName(), Collections.singletonList(UUIDHelper.GUID_EMPTY), personType, "", false, document.getDocSize(), 1, personIds, timeStr, isXC);
                            }
                        } else {
                            if (EnumPRO.EnumSigType.上岗证人员.getValue().equals(personType)){
                                //找到上岗证人员
                                DtoWorkSheetFolder workSheetFolder = workSheetFolderService.findOne(repId);
                                if (StringUtil.isNotNull(workSheetFolder) && StringUtil.isNotEmpty(workSheetFolder.getCertificatorId())
                                        && !UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getCertificatorId())) {
                                    signature(repId, document.getPath(), document.getPhysicalName(), Collections.singletonList(workSheetFolder.getCertificatorId()), personType, "", false, document.getDocSize(), 2, new ArrayList<>(), timeStr, isXC);
                                }
                            }else {
                                signature(repId, document.getPath(), document.getPhysicalName(), Collections.singletonList(personId), personType, "", false, document.getDocSize(), 1, new ArrayList<>(), timeStr, isXC);
                            }

                            //如果原始记录单分析人员签名的时候，需要考虑是否存在上岗证人员
//                            if (EnumPRO.EnumSigType.分析者.getValue().equals(personType)) {
//                                    //判断存在上岗证人员
//                                    if (StringUtil.isNotEmpty(workSheetFolder.getCertificatorId()) && !UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getCertificatorId())) {
//                                        personType = EnumPRO.EnumSigType.上岗证人员.getValue();
//                                     }
//                                }
//                            }
                        }
                    }
                }
            } catch (Exception ex) {
                log.error("签名发生异常 - 1:", ex);
                throw new BaseException(ex.getMessage());
            }
        }
    }

    /**
     * 下载签名
     *
     * @param docPath    文件路径
     * @param sigDocList 匹配内容
     * @throws Exception 报错信息
     */
    @Override
    public void sigByDocument(String docPath, List<DtoSigDocument> sigDocList) throws Exception {
        if (!licenseService.isLicense()) {
            throw new Exception("aspose版权存在问题！");
        }
        WorkbookDesigner designer = new WorkbookDesigner();
        designer.setWorkbook(new Workbook(docPath));
        List<String> peronIds = sigDocList.stream().map(DtoSigDocument::getPersonId).collect(Collectors.toList());
        List<DtoPerson> personList = personService.findAll(peronIds);
        Integer signIndex = 1;

        for (DtoSigDocument sigDoc : sigDocList) {
            Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(sigDoc.getPersonId())).findFirst();
            String path = "";
            if (person.isPresent()) {
                path = person.get().getSignature();
            }
            try {
                setPicColumn(designer, path, EnumPRO.EnumSigType.getByValue(sigDoc.getSigType()).toString(),
                        EnumPRO.EnumSigType.getByValue(sigDoc.getSigType()).getName(), Boolean.FALSE, Boolean.FALSE,
                        signIndex, sigDoc.getTimeStr(), sigDoc.getSigIndex(), sigDoc.getIsSplitDate(), sigDoc.getSplitIndex(),
                        sigDoc.getTimeIndex());
            } catch (Exception exception) {
                log.info("签名发生异常 - 下载签名:" + "未维护电子签名，请到人员管理中维护！");
                throw new BaseException("未维护电子签名，请到人员管理中维护！");
            }
        }
        designer.getWorkbook().save(docPath);
    }

    /**
     * 报表签名
     *
     * @param dtoReportConfig 报表类型
     * @param params          参数
     * @param oldPath         路径
     * @throws Exception 报错信息
     */
    @Override
    public void sigDocument(DtoReportConfig dtoReportConfig, List<Object> params, String oldPath) throws Exception {
        if (dtoReportConfig.getBizType() == 3) {
            log.info("开始获取采样单执法人员，厂方人员，外协人员签名");
            //采样单签名 (厂方人员，外协人员，执法人员)
            Map<String, Object> objectMap = (Map<String, Object>) params.get(0);
            String receiveId = "";
            if (StringUtils.isNotNullAndEmpty(objectMap.get("receiveSampleRecordId"))) {
                receiveId = objectMap.get("receiveSampleRecordId").toString();
            }
            if (StringUtil.isEmpty(receiveId) || UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                List<String> sampleIdList = (List<String>) objectMap.get("sampleIds");
                List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIdList) ? sampleRepository.findAll(sampleIdList) : new ArrayList<>();
                List<String> receiveIdlist = sampleList.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(receiveIdlist)) {
                    receiveId = receiveIdlist.get(0);
                }
            }
            if (StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                this.sigByReceiveId(oldPath, receiveId, "PRO_RecordSignature_QM", Boolean.FALSE);
            }
        }
        // 其他报表签名
        if (EnumLIM.EnumRecordType.采样记录单.getValue().equals(dtoReportConfig.getBizType()) ||
                EnumLIM.EnumRecordType.原始记录单.getValue().equals(dtoReportConfig.getBizType())) {
            // 设置签名，分页签名和不分页签名
            setPageSig(dtoReportConfig, params, oldPath);
        }
    }


    @Override
    public void sigExcelByReceiveId(String docPath, String receiveId) {
        this.sigByReceiveId(docPath, receiveId, "PRO_RecordSignature_QM", Boolean.TRUE);
    }


    @Override
    public void removeSignatureByDoc(DtoDocument document, Map<String, Integer> mapType) {
        try {
            //存在需要删除的类型
            removeSignature(document.getPath(), mapType);
        } catch (Exception ex) {
            log.info("移除签名报错 - 1:" + ex.getMessage());
        }
    }


    /**
     * 报告添加点位示意图
     *
     * @param params   报表参数
     * @param filePath 报表文件路径
     */
    @Override
    public void addPointPicForReport(List<Object> params, String filePath) {
        Map<String, Object> map = (Map<String, Object>) params.get(0);
        DtoReportConfig reportConfig = (DtoReportConfig) map.get("reportConfig");
        if (StringUtil.isNotNull(reportConfig) && (reportConfig.getBizType() == 4 || reportConfig.getBizType() == 3)) {
            List<String> folderIdList = new ArrayList<>();
            String reportId = map.getOrDefault("reportId", UUIDHelper.GUID_EMPTY).toString();

            folderIdList.add(reportId);
            //获取报告样品对应的送样单的点位示意图
            List<String> sampleIdList = reportDetailRepository.findByReportId(reportId).stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
            if (reportConfig.getBizType() == 3) {
                sampleIdList = (List<String>) map.getOrDefault("sampleIds", new ArrayList<>());
            }
            if (StringUtil.isNotEmpty(sampleIdList)) {
                List<String> receiveIdList = sampleRepository.findByIds(sampleIdList).stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                        .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(receiveIdList)) {
                    folderIdList.addAll(receiveIdList);
                }
            }
            List<DtoDocument> documentList = documentRepository.findByFolderIdIn(folderIdList);
            documentList = documentList.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
            //过滤出点位示意图，按照创建时间倒序排序
            documentList = documentList.stream().filter(p -> "pointPic".equals(p.getDocTypeId())).collect(Collectors.toList());
            documentList.sort(Comparator.comparing(DtoDocument::getCreateDate).reversed());
            if (StringUtil.isNotEmpty(documentList)) {
                try {
                    if (!licenseService.isLicense()) {
                        throw new Exception("aspose版权存在问题！");
                    }
                    if (reportConfig.getBizType() == 4) {
                        setReportPic(filePath, documentList);
                    } else {
                        //采样单获取点位示意图
                        setSamplingPic(filePath, documentList);
                    }
                } catch (Exception e) {
                    log.error("报告获取点位示意图发生异常：" + e.getMessage(), e);
                    throw new BaseException("报告获取点位示意图发生异常" + e.getMessage());
                }
            }
        }
    }

    /**
     * 报告设置点位示意图（通过书签进行定位）
     *
     * @param filePath     报告文件路径
     * @param documentList 点位示意图文档对象
     */
    private void setReportPic(String filePath, List<DtoDocument> documentList) throws Exception {
        try {
            String picMark = "pointPicSeal";
            List<String> picPathList = documentList.stream().map(DtoDocument::getPath).collect(Collectors.toList());
            for (String picPath : picPathList) {
                if (new File(filePathConfig.getFilePath() + picPath).exists()) {
                    String path = filePath;
                    Document doc = new Document(path);
                    DocumentBuilder builderDoc = new DocumentBuilder(doc);
                    if (builderDoc.moveToBookmark(picMark)) {
                        if (StringUtils.isNotNullAndEmpty(picPath)) {
                            builderDoc.insertImage(filePathConfig.getFilePath() + "/" + picPath, RelativeHorizontalPosition.MARGIN,
                                    0, RelativeHorizontalPosition.MARGIN, 0, -1.0D, -1.0D, WrapType.INLINE);
                        }
                    }
                    doc.save(path);
                } else {
                    log.error("报告获取点位示意图发生异常，点位示意图不存在，或点位示意图配置路径错误!");
                }
            }
        } catch (Exception e) {
            log.error("报告获取点位示意图发生异常", e);
        }
    }

    /**
     * 采样单设置点位示意图（通过书签进行定位）
     *
     * @param filePath     采样单文件路径
     * @param documentList 点位示意图文档对象
     */
    private void setSamplingPic(String filePath, List<DtoDocument> documentList) throws Exception {
        String picKeyWord = "点位示意图";
        WorkbookDesigner designer = new WorkbookDesigner();
        designer.setWorkbook(new Workbook(filePath));
        int signIndex = 0;
        for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
            Worksheet worksheet = designer.getWorkbook().getWorksheets().get(i);
            if (worksheet.getCells() != null) {
                // 循环当前sheet最大有效行
                for (int j = 0; j < worksheet.getCells().getMaxRow(); j++) {
                    Cell cell = worksheet.getCells().find(picKeyWord, worksheet.getCells().get(j, 0), new FindOptions());
                    if (cell != null) {
                        cell.putValue("");
                        try {
                            for (DtoDocument document : documentList) {
                                InputStream stream = new FileInputStream(filePathConfig.getFilePath() + document.getPath());
//                                int leftWidth = 0;
//                                int maxColumn = cell.getColumn();
//                                int mergedColumnCount = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount();
//                                Picture pic = worksheet.getShapes().addPicture(cell.getRow(), cell.getColumn(), stream, 5, 5);
                                int lowerRightColumn = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount();
                                int lowerRightRow = cell.getMergedRange().getFirstRow() + cell.getMergedRange().getRowCount();
                                Picture pic = worksheet.getShapes().addPicture(cell.getRow(), cell.getColumn(), lowerRightRow, lowerRightColumn, stream);
                                double[] actWidthHeight = getActWidthHeight(pic);
                                pic.setName("pointPic" + signIndex);
                                pic.setWidthInch(actWidthHeight[0]);
                                pic.setHeightInch(actWidthHeight[1]);
                                j = cell.getRow();
                                signIndex++;
                            }
                        } catch (Exception ex) {
                            log.error("添加采样单点位示意图出错", ex);
                            throw new Exception("添加采样单点位示意图出错：" + ex.getMessage());
                        }
                    } else {
                        break;
                    }
                }
            }
        }
        designer.getWorkbook().save(filePath);
    }

    /**
     * 重新调整采样单点位示意图图片大小
     *
     * @param pic 图片对象
     * @return 调整后的图片宽度和高度
     */
    protected double[] getActWidthHeight(Picture pic) {
        //获取excel报表点位示意图单元格的宽度和高度
        Double widthInch = pic.getWidthInch(), heightInch = pic.getHeightInch();
        //获取图片的宽度和高度
        Double picWidth = pic.getOriginalWidthInch(), picHeight = pic.getOriginalHeightInch();
        double[] arr = new double[]{picWidth, picHeight};
        if (StringUtil.isNotNull(widthInch) && StringUtil.isNotNull(heightInch) && StringUtil.isNotNull(picHeight) && StringUtil.isNotNull(picWidth)) {
            BigDecimal picWidthDec = BigDecimal.valueOf(picWidth), picHeightDec = BigDecimal.valueOf(picHeight),
                    widthInchDec = BigDecimal.valueOf(widthInch), heightInchDec = BigDecimal.valueOf(heightInch);
            BigDecimal widthProp = picWidthDec.divide(widthInchDec, ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
            BigDecimal heightProp = picHeightDec.divide(heightInchDec, ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
            boolean isWidthBig = picWidth.compareTo(widthInch) >= 0, isHeightBig = picHeight.compareTo(heightInch) >= 0;
            boolean isWidthPropBig = widthProp.compareTo(heightProp) >= 0;
            double widthDivHeightProp = picWidthDec.divide(heightProp, ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).doubleValue();
            double heightDivWidthProp = picHeightDec.divide(widthProp, ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).doubleValue();
            if ((isWidthBig && isHeightBig) || (!isWidthBig && !isHeightBig)) {
                //图片宽高都大于等于单元格宽度高或者宽高都小于单元格宽高的情况
                arr[0] = isWidthPropBig ? widthInch : widthDivHeightProp;
                arr[1] = isWidthPropBig ? heightDivWidthProp : heightInch;
            } else if (isWidthBig) {
                //图片宽度大于等于单元格宽度，图片高度小于单元格高度的情况
                arr[0] = widthInch;
                arr[1] = heightDivWidthProp;
            } else {
                //图片高度大于等于单元格高度，图片宽度小于单元格宽度的情况
                arr[1] = heightInch;
                arr[0] = widthDivHeightProp;
            }
        }
        return arr;
    }

    /**
     * 报告添加系统信息企业logo
     *
     * @param params   报表参数
     * @param filePath 报表文件路径
     */
    @Override
    public void addSystemLogoForReport(List<Object> params, String filePath) {
        Map<String, Object> map = (Map<String, Object>) params.get(0);
        DtoReportConfig reportConfig = (DtoReportConfig) map.get("reportConfig");
        if (StringUtil.isNotNull(reportConfig) && reportConfig.getBizType() == 4) {
            //获取系统信息配置的系统logo
            List<DtoSystemConfig> systemConfigList = systemConfigService.findAll();
            DtoSystemConfig systemConfig = StringUtil.isNotEmpty(systemConfigList) ? systemConfigList.get(0) : null;
            String folderId = StringUtil.isNotNull(systemConfig) ? systemConfig.getId() : UUIDHelper.GUID_EMPTY;
            List<DtoDocument> systemConfigDocList = documentRepository.findByFolderIdAndIsDeletedFalse(folderId);
            DtoDocument logoDoc = systemConfigDocList.stream().filter(p -> "PRO_DocumentExtendType_LOGO".equals(p.getDocTypeId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(logoDoc)) {
                try {
                    if (!licenseService.isLicense()) {
                        throw new Exception("aspose版权存在问题！");
                    }
                    String logoPath = logoDoc.getPath();
                    addPicture(filePath, "logoSeal", logoPath, 35, 35);
                    boolean logoHeadFlag = checkMarkExist(filePath, "logoSealHead");
                    boolean logoHeadSmallFlag = checkMarkExist(filePath, "logoSealHeadSmall");
                    if (logoHeadFlag && logoHeadSmallFlag) {
                        addPicture(filePath, "logoSealHead", logoPath, 70, 70);
                    } else {
                        addPicture(filePath, "logoSealHead", logoPath, 70, 70);
                        addPicture(filePath, "logoSealHeadSmall", logoPath, 35, 35);
                    }
                } catch (Exception e) {
                    log.error("报告获取系统logo发生异常：" + e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 判断书签是否存在
     *
     * @param filePath 文档路径
     * @param markName 书签名称
     * @return 书签是否存在
     */
    private boolean checkMarkExist(String filePath, String markName) {
        boolean flag = false;
        try {
            Document doc = new Document(filePath);
            DocumentBuilder builderDoc = new DocumentBuilder(doc);
            flag = builderDoc.moveToBookmark(markName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return flag;
    }

    private void addPicture(String filePath, String logoMark, String logoPath, int width, int height) throws Exception {
        if (new File(filePathConfig.getFilePath() + logoPath).exists()) {
            Document doc = new Document(filePath);
            DocumentBuilder builderDoc = new DocumentBuilder(doc);
            if (builderDoc.moveToBookmark(logoMark)) {
                if (StringUtils.isNotNullAndEmpty(logoPath)) {
                    builderDoc.insertImage(filePathConfig.getFilePath() + "/" + logoPath, RelativeHorizontalPosition.MARGIN,
                            0, RelativeHorizontalPosition.MARGIN, 0, width, height, WrapType.INLINE);
                }
            }
            doc.save(filePath);
        } else {
            log.error("报告获取系统logo发生异常，系统logo不存在，或系统logo配置路径错误!");
            throw new BaseException("报告获取系统logo发生异常，系统logo不存在，或系统logo配置路径错误!");
        }
    }


    /**
     * 报表签名
     *
     * @param docPath   路径
     * @param receiveId 送样单id
     * @param docType   报表类型
     */
    protected void sigByReceiveId(String docPath, String receiveId, String docType, Boolean isChange) {
        //获取厂方人员的签名
        List<DtoDocument> docList = documentRepository.findByFolderId(receiveId).stream().filter(p ->
                docType.equals(p.getDocTypeId()) && !p.getIsDeleted()).collect(Collectors.toList());
        String timeStr = DateUtil.nowTime("yyyy.MM.dd");
        int signIndex = 1;
        List<String> docPathKeyList = Arrays.asList("厂方", "执法", "外协");
        List<String> personTypeList = Arrays.asList("企业当事人确认", "执法人员", "见证人员");
        try {
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(new Workbook(docPath));
            for (int i = 0; i < docPathKeyList.size(); i++) {
                String docPathKey = docPathKeyList.get(i);
                List<String> docPathListCf = docList.stream().map(DtoDocument::getPath)
                        .filter(path -> path.contains(docPathKey)).distinct().collect(Collectors.toList());
                if (docPathListCf.size() > 0) {
                    for (String path : docPathListCf) {
                        setPicColumn(designer, path, personTypeList.get(i), personTypeList.get(i), isChange, Boolean.FALSE, signIndex, timeStr, 0, Boolean.FALSE, 0, 0);
                        signIndex++;
                    }
                }
                signIndex = 1;
            }
            designer.getWorkbook().save(docPath);
        } catch (Exception exception) {
            log.info("采样单执法，外协，厂方人员签名出错");
        }
    }

    private void getSigDocumentList(List<DtoOATaskHandleLog> oaTaskHandleLogList, String statusName, Integer typeValue, List<DtoSigDocument> sigDocuments) {
        Optional<DtoOATaskHandleLog> oaTaskHandleLog = oaTaskHandleLogList.stream().filter(p -> p.getActTaskName().contains(statusName))
                .sorted(Comparator.comparing(DtoOATaskHandleLog::getCreateDate).reversed()).findFirst();
        if (oaTaskHandleLog.isPresent()) {
            List<String> personIds = new ArrayList<>();
            personIds.add(oaTaskHandleLog.get().getCreator());
            if (StringUtil.isNotEmpty(oaTaskHandleLog.get().getJurorId())) {
                personIds.addAll(Arrays.asList(oaTaskHandleLog.get().getJurorId().split(";")));
            }
            Integer number = 0;
            for (String personId : personIds) {
                DtoSigDocument sigDoc = new DtoSigDocument();
                sigDoc.setPersonId(personId);
                if (personId.equals(oaTaskHandleLog.get().getCreator())) {
                    sigDoc.setTimeStr(DateUtil.dateToString(oaTaskHandleLog.get().getCompleteTime(), DateUtil.YEAR_ZH_CN));
                }
                sigDoc.setTimeIndex(number);
                sigDoc.setIsSplitDate(Boolean.FALSE);
                sigDoc.setSigIndex(number * 4);
                sigDoc.setSplitIndex(0);
                sigDoc.setSigType(typeValue);
                sigDocuments.add(sigDoc);
                number++;
            }
        }
    }


    protected void setPageSig(DtoReportConfig dtoReportConfig, List<Object> params, String oldPath) {
        // 模板参数配置，isPageSig
        if (StringUtil.isNotEmpty(dtoReportConfig.getParams())) {
            Gson gson = new Gson();
            Map<String, Object> paramConfigMap = (Map<String, Object>) gson.fromJson(dtoReportConfig.getParams(), Map.class);
            // 分页签名，每页签名不同
            if (paramConfigMap.containsKey("isPageSig") && (Boolean) (paramConfigMap.get("isPageSig"))) {
                Map<String, List<Map<String, Object>>> reportDataMap = (Map<String, List<Map<String, Object>>>) params.get(3);
                if (reportDataMap.keySet().stream().anyMatch(key -> key.startsWith("Signature"))) {
                    List<String> signature = reportDataMap.keySet().stream().filter(key -> key.startsWith("Signature")).collect(Collectors.toList());
                    List<Map<String, Object>> signList = reportDataMap.entrySet().stream().filter(p -> signature.contains(p.getKey())).flatMap(p -> p.getValue().stream()).collect(Collectors.toList());
                    // 所有签名占位符，可在具体实现类中定义
                    List<String> personTypeList = new ArrayList<>(signList.stream().flatMap(p -> p.keySet().stream()).distinct().collect(Collectors.toList()));
                    String timeStr = DateUtil.nowTime("yyyy.MM.dd");
                    try {
                        // 当前工作簿
                        WorkbookDesigner designer = new WorkbookDesigner();
                        designer.setWorkbook(new Workbook(oldPath));
                        for (int i = 0; i < personTypeList.size(); i++) {
                            setPicColumn(designer, personTypeList.get(i), personTypeList.get(i), Boolean.TRUE, Boolean.FALSE, timeStr, 0, Boolean.FALSE, 0, 0, params);
                        }
                        designer.getWorkbook().save(oldPath);
                    } catch (Exception exception) {
                        log.error(exception.getMessage(), exception);
                        log.info("分页签名出错");
                    }
                }
            }
            // 不分页签名，根据报表模版配置中配置的json参数判断是否需要签名
            if (paramConfigMap.containsKey("isSignature") && (Boolean) (paramConfigMap.get("isSignature"))) {
                Map<String, Object> dataMap = (Map<String, Object>) params.get(0);
                // 占位符为key,签名path为value 由表单个性化处理
                Map<String, String> personTypeList = (Map<String, String>) dataMap.get("signature");
                String timeStr = DateUtil.nowTime("yyyy.MM.dd");
                // 是否签名在占位符位置
                boolean isSelfCell = paramConfigMap.containsKey("isSelfCell") && (Boolean) (paramConfigMap.get("isSelfCell"));

                try {
                    int signIndex = 1;
                    // 当前工作簿
                    WorkbookDesigner designer = new WorkbookDesigner();
                    designer.setWorkbook(new Workbook(oldPath));
                    for (String personType : personTypeList.keySet()) {
                        if (personType.contains("日期") || personType.contains("时间")) {
                            setPicColumn(designer, "", personType, personType, Boolean.FALSE, Boolean.FALSE, signIndex, personTypeList.get(personType), 0, Boolean.TRUE, 0, 0);
                        } else {
                            setPicColumn(designer, personTypeList.get(personType), personType, personType, Boolean.FALSE, Boolean.FALSE, signIndex, timeStr, 0, Boolean.FALSE, 0, 0, isSelfCell);
                        }
                        signIndex++;
                    }
                    designer.getWorkbook().save(oldPath);
                } catch (Exception exception) {
                    log.error(exception.getMessage(), exception);
                    log.info("签名出错");
                }
            }

        }
    }

    /**
     * 删除副本（退回时需要删除副本）
     *
     * @param folderId 关联id
     */
    @Override
    public void deleteDoc(String folderId) {
        //找到副本文件
        List<DtoDocument> documentList = documentRepository.findByFolderId(folderId).stream()
                .filter(p -> p.getIsTranscript()).collect(Collectors.toList());
        //把对应的副本删掉
        documentService.delete(documentList);
    }

    /**
     * 移除原始记录单签名
     *
     * @param repId
     */
    @Override
    public void removeWorkSheetSig(String repId, Map<String, Integer> mapType) {
        List<DtoDocument> documentList = documentRepository.findByFolderId(repId);
        documentList = documentList.stream().filter(p -> !p.getFilename().contains("副本")
                && p.getIsDeleted().equals(false)).collect(Collectors.toList());
        try {
            //存在需要删除的类型
            for (DtoDocument document : documentList) {
                removeSignature(document.getPath(), mapType);
            }
        } catch (Exception ex) {
            log.info("移除签名报错 - 1:" + ex.getMessage());
        }
    }

    /**
     * 表单签名
     *
     * @param repId      报表id
     * @param filePath   路径
     * @param fileName   报表名称
     * @param personIds  人员id
     * @param personType 人员类型
     * @param remark     备注
     * @param isCopy     是否复制
     * @param fileSize   大小
     * @throws Exception 报错
     */
    private void signature(String repId, String filePath, String fileName, List<String> personIds, Integer personType,
                           String remark, Boolean isCopy, Integer fileSize, Integer signIndex, List<String> signPathList,
                           String timeStr, Boolean isXC)
            throws Exception {
        if (!licenseService.isLicense()) {
            throw new Exception("aspose版权存在问题！");
        }
        String fullPath = filePath;
        String physicalName = "";
        String fileType = "";
        Boolean copy = false;

        String personId = personIds.get(0);
        Person person = personService.findOne(personId, true);
        String signPath = "";
        if (signPathList.size() > 0) {
            signPath = signPathList.get(0);
        }
        String personSignature = "";
        if (StringUtils.isNotNullAndEmpty(person)) {
            personSignature = StringUtil.isNotEmpty(person.getSignature()) ? person.getSignature() : personSignature;
        }
        if (((!UUIDHelper.GUID_EMPTY.equals(personId) && StringUtils.isNotNullAndEmpty(person))
                && new File(filePathConfig.getFilePath() + personSignature).exists())
                || (StringUtils.isNotNullAndEmpty(signPath)
                && new File(filePathConfig.getFilePath() + signPath).exists())) {
            //word报表
            if (fullPath.toLowerCase().contains(".doc")) {
                if (EnumPRO.EnumSigType.maker.getValue().equals(personType) || EnumPRO.EnumSigType.certificateMaker.getValue().equals(personType) || isCopy) {
                    ArrayList<String> farr = GetSplitString(fileName, "\\.");
                    //文件类型
                    String leftStr = farr.get(0);
                    fileType = farr.get(1);
                    String time = DateUtil.nowTime("yyyyMMddHHmmssSSS");
                    fileName = leftStr + "(副本)." + fileType;
                    ArrayList<String> subarr = GetSplitString(fileName, "\\.");
                    String sleftStr = subarr.get(0);
                    String newFileName = sleftStr + "_" + time;
                    physicalName = newFileName + "." + fileType;
                    fullPath = fullPath.replace(leftStr, newFileName);
                    copy = true;
                }
                sigWord(fullPath, person, signPath, repId, fileName, physicalName, filePath, fileType, fileSize, copy, personType, remark);
            }
            //excel报表
            else if (fullPath.toLowerCase().contains(".xls")) {
                List<DtoPerson> perList = personService.findAllDeleted(personIds);
                sigExcel(fullPath, signPathList, perList, repId, fileName, physicalName, filePath, fileType, fileSize, isCopy, personType, signIndex, timeStr, isXC);
            }
        } else {
            log.info("签名发生异常 - 2:" + "人员未维护电子签名，请联系系统管理员维护电子签名！");
            throw new BaseException("[" + person.getCName() + "]人员未维护电子签名，请联系系统管理员维护电子签名！");
        }
    }

    /**
     * 判断副本是否需要创建，同时返回路径
     *
     * @param repId        报表id
     * @param fileName     报表名称
     * @param fullPath     路径
     * @param physicalName 物理路径
     * @param filePath     实际路径
     * @param fileType     类型
     * @param fileSize     大小
     * @return 存储路径
     */
    private String getFullPath(String repId, String fileName, String fullPath, String physicalName, String filePath, String fileType, Integer fileSize, Boolean isCopy) {

        //找到报告的副本
        List<DtoDocument> docList = documentRepository.findByFolderId(repId);
        docList = docList.stream().filter(p -> p.getIsDeleted().equals(false)).collect(Collectors.toList());
        if (docList.size() > 0) {
            ArrayList<String> farr = GetSplitString(fileName, "\\.");
            String name = farr.get(0);
            List<DtoDocument> fileList = docList.stream().filter(p -> p.getFilename().contains(name)
                    && p.getIsTranscript().equals(true)).collect(Collectors.toList());
            if (fileList.size() == 0 && isCopy) {
                //fullPath = fileList.stream().map(DtoDocument::getPath).findFirst().orElse("");
                createDocCopy(repId, fileName, physicalName, filePath, fullPath, fileType, fileSize);
            } else if (fileList.size() > 0) {
                fullPath = fileList.stream().filter(p -> !p.getDocSuffix().contains("pdf")).map(DtoDocument::getPath).findFirst().orElse("");
            }
        }
        return fullPath;
    }

    /**
     * 获取路径中文件名称
     *
     * @param str    路径
     * @param spchar 分隔符
     * @return
     */
    private ArrayList<String> GetSplitString(String str, String spchar) {
        ArrayList<String> result = new ArrayList<>();
        String[] arr = str.split(spchar);
        String left = "";
        String right = "";
        if (arr.length > 1) {
            right = arr[arr.length - 1];
            left = str.substring(0, str.length() - right.length() - 1);
        } else {
            left = str;
        }
        result.add(left);
        result.add(right);
        return result;
    }

    /**
     * 复制报表文件
     *
     * @param folderId     关联id
     * @param fileName     文件名称
     * @param physicalName 物理文件名称
     * @param oldPath      文件路径
     * @param newPath      复制文件路径
     * @param fileType     文件类别
     * @param fileSize     文件大小
     */
    private void createDocCopy(String folderId, String fileName, String physicalName, String oldPath, String newPath, String fileType,
                               Integer fileSize) {
        String filePath = newPath;
        //region 读取文件、创建文件
        oldPath = filePathConfig.getFilePath() + oldPath;//reportConfig.getTemplatePath() + "/" +
//        if (StringUtil.isNotEmpty(oldPath)) {
//            if (oldPath.substring(0, 1).equals(File.separator) || oldPath.substring(0, 1).equals("/")) {
//                log.info("");
//                oldPath = oldPath.substring(1);
//            }
//        }
        log.info("============ init old path：" + oldPath);
        newPath = filePathConfig.getFilePath() + newPath;//reportConfig.getTemplatePath() + "/" +
//        if (StringUtil.isNotEmpty(newPath)) {
//            if (newPath.substring(0, 1).equals(File.separator) || newPath.substring(0, 1).equals("/")) {
//                newPath = newPath.substring(1);
//            }
//        }
        //创建文件
        String strPath = createFile(newPath);
        File oldFile = new File(oldPath);
        File newFile;
        if (StringUtils.isNotNullAndEmpty(strPath)) {
            newFile = new File(strPath);
        } else {
            newFile = new File(newPath);
        }
        InputStream stream = null;
        OutputStream outStream = null;
        try {
            log.info("start1");
            if (oldFile.exists()) {
                log.info("分支1开始");
                stream = new FileInputStream(oldFile);
                log.info("分支1结束");
            } else {
                log.info("分支2开始");
                stream = this.getClass().getClassLoader().getResourceAsStream(oldPath);
                log.info("分支2结束");
            }
            if (newFile.exists()) {
                log.info("分支3开始");
                outStream = new FileOutputStream(newFile);
                log.info("分支3结束");
            } else {
                log.info("分支4开始:" + strPath);
                outStream = new FileOutputStream(strPath);
                log.info("分支4结束:" + strPath);
            }
            Integer bufferLen = 4096;
            byte[] buffer = new byte[bufferLen];
            int bytesRead;
            while ((bytesRead = stream.read(buffer)) != -1) {
                outStream.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            log.info("签名发生异常 - 3:" + "复制报表文件失败");
            log.info("oldPath:" + oldPath);
            log.info("newPath:" + newPath);
            log.info(e.getMessage());
            throw new BaseException("复制报表文件失败");
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    throw new BaseException("关闭流失败");
                }
            }
            if (outStream != null) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    throw new BaseException("关闭流失败");
                }
            }
        }
        //endregion
        //region 创建doc
        log.info("新增附件开始：" + "fileName:" + fileName +
                "filePath:" + filePath + "===" + physicalName);
        DtoDocument dtoDocument = new DtoDocument();
        dtoDocument.setFolderId(folderId);
        dtoDocument.setFolderName("");
        dtoDocument.setFilename(fileName);
        dtoDocument.setPhysicalName(physicalName);
        dtoDocument.setPath(filePath);
        dtoDocument.setIsTranscript(false);
        dtoDocument.setDocTypeId(UUIDHelper.GUID_EMPTY);
        dtoDocument.setDocTypeName("");
        dtoDocument.setDocSize(fileSize);
        String dotfileType = (!fileType.startsWith(".") && !fileType.contains(".")) ? "." + fileType : fileType;
        dtoDocument.setDocSuffix(dotfileType);
        dtoDocument.setDownloadTimes(0);
        dtoDocument.setOrderNum(0);
        dtoDocument.setRemark("");
        dtoDocument.setIsTranscript(true);
        dtoDocument.setUploadPerson(PrincipalContextUser.getPrincipal().getUserName());
        dtoDocument.setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
        dtoDocument.setIsStick(false);
        dtoDocument.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        documentService.save(dtoDocument);
        log.info("新增附件结束：");
        //endregion
    }

    /**
     * 创建文件
     *
     * @param filePath 文件路径
     * @return 文件路径
     */
    private static String createFile(String filePath) { // 创建单个文件
        File file = new File(filePath);
        if (file.exists()) { // 判断文件是否存在
            //System.out.println("目标文件已存在" + filePath);
            return filePath;
        }
        if (filePath.endsWith(File.separator)) { // 判断文件是否为目录
            //System.out.println("目标文件不能为目录！");
            return "";
        }
        // 判断目标文件所在的目录是否存在
        if (!file.getParentFile().exists()) {
            // 如果目标文件所在的文件夹不存在，则创建父文件夹
            System.out.println("目标文件所在目录不存在，准备创建它！");
            if (!file.getParentFile().mkdirs()) { // 判断创建目录是否成功
                log.info("创建目标文件所在的目录失败！");
                return "";
            }
        }
        try {
            if (file.createNewFile()) { // 创建目标文件
                log.info("创建文件成功:" + filePath);
                return filePath;
            } else {
                log.info("创建文件失败！");
                return "";
            }
        } catch (IOException e) { // 捕获异常
            log.info("签名发生异常 - 4:" + e.getMessage());
            return "";
        }
    }

    /**
     * 移除签名图片
     *
     * @param filePath 路径
     * @param mapType  类型
     */
    protected void removeSignature(String filePath, Map<String, Integer> mapType) throws Exception {
        String fullPath = filePathConfig.getFilePath() + filePath;
        try {
            if (fullPath.toLowerCase().contains(".doc")) {
                String path = filePathConfig.getFilePath() + filePath;//reportConfig.getTemplatePath() + "/" +
                if (StringUtil.isNotEmpty(path)) {
                    if (path.substring(0, 1).equals(File.separator) || path.charAt(0) == '/') {
                        path = path.substring(1);
                    }
                }
                com.aspose.words.Document doc = new com.aspose.words.Document(path);
                doc = removeWord(doc, new ArrayList<>(mapType.keySet()));
                doc.save(fullPath);
            } else {
                if (fullPath.toLowerCase().contains(".xls")) {
                    WorkbookDesigner designer = new WorkbookDesigner();
                    designer.setWorkbook(new Workbook(fullPath));
                    designer = removeExcel(designer, mapType);
                    designer.getWorkbook().save(fullPath);
                }
            }
        } catch (Exception ex) {
            log.info("签名发生异常 - 5:" + ex.getMessage());
            throw new Exception(ex.getMessage());
        }
    }

    /**
     * 移除签名图片
     *
     * @param designer 文档
     * @param mapType  类型
     */
    protected void removeByDesigner(WorkbookDesigner designer, Map<String, Integer> mapType) throws Exception {
        try {
            removeExcel(designer, mapType);
        } catch (Exception ex) {
            log.info("签名发生异常 - 5:" + ex.getMessage());
            throw new Exception(ex.getMessage());
        }
    }

    /**
     * 设置图片大小
     *
     * @param image  图片
     * @param width  宽度
     * @param height 高度
     * @return 修改之后的图片
     */
    private static BufferedImage resizeImage(final Image image, int width, int height) {
        final BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        final Graphics2D graphics2D = bufferedImage.createGraphics();
        graphics2D.setComposite(AlphaComposite.Src);
        graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graphics2D.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics2D.drawImage(image, 0, 0, width, height, null);
        graphics2D.dispose();
        return bufferedImage;
    }

    /**
     * 绑定图片签名
     *
     * @param sigPath    图片路径
     * @param fullPath   文件路径
     * @param personMark 签名位置
     * @param builderDoc 文件
     * @param isHidden   是否隐藏
     */
    private void insertImage(String sigPath, String fullPath, String personMark,
                             DocumentBuilder builderDoc, Boolean isHidden) throws Exception {
        try {
            String photoPath = sigPath;
            if (StringUtils.isNotNullAndEmpty(photoPath)) {
                if (isHidden) {
                    builderDoc.getFont().setHidden(false);
                }
                builderDoc.insertImage(filePathConfig.getFilePath() + "/" + photoPath, RelativeHorizontalPosition.MARGIN,
                        0, RelativeHorizontalPosition.MARGIN, 0, 55, 20, WrapType.INLINE);
            }
        } catch (Exception ex) {
            log.info("签名发生异常 - 6:" + ex.getMessage());
            throw new BaseException("图片签名失败!" + ex.getMessage());
        }
    }

    /**
     * word 签名
     *
     * @param fullPath
     * @param person
     * @param signPath
     * @param repId
     * @param fileName
     * @param physicalName
     * @param filePath
     * @param fileType
     * @param fileSize
     * @param copy
     * @param personType
     * @param remark
     */
    private void sigWord(String fullPath, Person person, String signPath, String repId, String fileName, String physicalName,
                         String filePath, String fileType, Integer fileSize, Boolean copy, Integer personType, String remark) {
        try {

            //region word报表签名
            //人员签名的路径地址
            fullPath = getFullPath(repId, fileName, fullPath, physicalName, filePath, fileType, fileSize, copy);
            String path = filePathConfig.getFilePath() + fullPath;//reportConfig.getTemplatePath() + "/" +

            com.aspose.words.Document doc = new com.aspose.words.Document(path);
            DocumentBuilder builderDoc = new DocumentBuilder(doc);
            //获取监管平台系统编号
            setSignInfo(builderDoc, repId);

            String bookmark = "";
            String personMark = "";//第一个签名位置
            String reportDate = "";//日期域名称
            String sigRemark = "";//意见域
            setWordPicNew(personType, personMark, reportDate, bookmark, sigRemark, builderDoc, doc, person, fullPath, remark, signPath, repId);
            doc.save(path);
            //endregion
        } catch (Exception ex) {
            log.info("签名发生异常 - 8:" + "人员未维护电子签名，请联系系统管理员维护电子签名！", ex);
            throw new BaseException("[" + person.getCName() + "]人员未维护电子签名，请联系系统管理员维护电子签名！");
        }
    }

    /**
     * 报告签名
     *
     * @param personType
     * @param personMark
     * @param reportDate
     * @param bookmark
     * @param sigRemark
     * @param builderDoc
     * @param doc
     * @param signPath
     * @param fullPath
     * @param remark
     * @throws Exception
     */
    protected void setWordPic(Integer personType, String personMark, String reportDate,
                              String bookmark, String sigRemark, DocumentBuilder builderDoc,
                              com.aspose.words.Document doc, Person person, String fullPath,
                              String remark, String signPath, String repId) throws Exception {
        String sigPath = StringUtils.isNotNullAndEmpty(person.getSignature()) ? person.getSignature() : signPath;
        if (EnumPRO.EnumSigType.maker.getValue().equals(personType)) {
            personMark = EnumPRO.EnumSigType.maker.toString();
            reportDate = EnumPRO.EnumSigDate.makersmallDate.toString();
            bookmark = "报告人";
        } else if (EnumPRO.EnumSigType.certificateMaker.getValue().equals(personType)) {
            personMark = EnumPRO.EnumSigType.certificateMaker.toString();
            reportDate = EnumPRO.EnumSigDate.makersmallDate.toString();
            bookmark = "有证报告人";
        } else if (EnumPRO.EnumSigType.auditor.getValue().equals(personType)) {
            personMark = EnumPRO.EnumSigType.auditor.toString();
            reportDate = EnumPRO.EnumSigDate.auditorsmallDate.toString();
            sigRemark = EnumPRO.EnumSigRemark.审核意见.toString();
            bookmark = "审核人";
        } else if (EnumPRO.EnumSigType.signer.getValue().equals(personType)) {
            personMark = EnumPRO.EnumSigType.signer.toString();
            reportDate = EnumPRO.EnumSigDate.signersmallDate.toString();
            sigRemark = EnumPRO.EnumSigRemark.签发意见.toString();
            bookmark = "签发人";
        }
        if (builderDoc.moveToBookmark(personMark) || builderDoc.moveToBookmark(bookmark)) {
            insertImage(sigPath, fullPath, personMark, builderDoc, false);
            if (builderDoc.moveToBookmark(sigRemark)) {
                builderDoc.insertHtml(remark);
            }
            if (builderDoc.moveToBookmark(reportDate)) {
                builderDoc.insertHtml(DateUtil.nowTime(DateUtil.YEAR_ZH_CN));
            }
        }
        while (builderDoc.moveToMergeField(personMark, true, true)) {
            insertImage(sigPath, fullPath, personMark, builderDoc, true);
            if (!EnumPRO.EnumSigType.certificateMaker.toString().equals(personMark)) {
                builderDoc.moveToMergeField(reportDate, true, true);
                builderDoc.insertHtml("<font size=\"3\"><u>" + DateUtil.nowTime(DateUtil.YEAR_ZH_CN) + "</u></font>");
            }
            if (builderDoc.moveToMergeField(sigRemark, true, true)) {
                builderDoc.insertHtml(remark);
            }
        }
        if (EnumPRO.EnumSigType.signer.getValue().equals(personType)) {
            //签发之后报告锁定
            doc.protect(com.aspose.words.ProtectionType.READ_ONLY, passwordValue());
            if (builderDoc.moveToBookmark("signerSmallDate1")) {
                builderDoc.insertField(DateUtil.YEAR_ZH_CN, "OpenDate");
                doc.getMailMerge().execute(new String[]{"OpenDate"}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
            }
            if (builderDoc.moveToBookmark("signerSmallDate2")) {
                builderDoc.insertField(DateUtil.YEAR_ZH_CN, "OpenDate1");
                doc.getMailMerge().execute(new String[]{"OpenDate1"}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
            }
            doc.getMailMerge().execute(new String[]{reportDate}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
        }
        if (EnumPRO.EnumSigType.auditor.getValue().equals(personType)) {
            if (builderDoc.moveToBookmark("OpenDate2")) {
                builderDoc.insertField(DateUtil.YEAR_ZH_CN, "OpenDate2");
                doc.getMailMerge().execute(new String[]{"OpenDate2"}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
            }
        }
    }

    protected void setWordPicNew(Integer personType, String personMark, String reportDate, String bookmark, String sigRemark, DocumentBuilder builderDoc,
                                 com.aspose.words.Document doc, Person person, String fullPath, String remark, String signPath, String repId) throws Exception {
        List<DtoStatusForReport> statusForReportList = statusForReportRepository.findByReportId(repId);
        //编制人
        Optional<DtoStatusForReport> reportEdit = statusForReportList.stream()
                .filter(p -> p.getModule().equals(EnumLIM.EnumReportModule.报告编制.getCode())).findFirst();
        if (reportEdit.isPresent()) {
            String personId = reportEdit.get().getModifier();
            Person editPerson = personService.findOne(personId, Boolean.TRUE);
            setWordPicDateAndName(personType, builderDoc, doc, editPerson, signPath, reportEdit.get().getCreateDate());
        }
        //校核人
        Optional<DtoStatusForReport> reportCheck = statusForReportList.stream()
                .filter(p -> p.getModule().trim().equals(EnumLIM.EnumReportModule.报告校核.getCode().trim())).findFirst();
        Person checkPerson = null;
        if (reportCheck.isPresent()) {
            String personId = reportCheck.get().getCurrentPersonId();
            checkPerson = personService.findOne(personId, Boolean.TRUE);
            setWordPicDateAndName(EnumPRO.EnumSigType.certificateMaker.getValue(),
                    builderDoc, doc, checkPerson, signPath, reportCheck.get().getCreateDate());
        }
        //复核人
        Optional<DtoStatusForReport> reportReview = statusForReportList.stream()
                .filter(p -> p.getModule().equals(EnumLIM.EnumReportModule.报告复核.getCode())).findFirst();
        if (reportReview.isPresent()) {
            String personId = reportReview.get().getCurrentPersonId();
            Person reviewPerson = personService.findOne(personId, Boolean.TRUE);
            setWordPicDateAndName(51, builderDoc, doc, reviewPerson, signPath, reportReview.get().getCreateDate());
        }
        //审核人
        Optional<DtoStatusForReport> reportAudit = statusForReportList.stream()
                .filter(p -> p.getModule().equals(EnumLIM.EnumReportModule.报告审核.getCode())).findFirst();
        Person auditPerson = null;
        if (reportAudit.isPresent()) {
            String personId = reportAudit.get().getModifier();
            auditPerson = personService.findOne(personId, Boolean.TRUE);
            setWordPicDateAndName(EnumPRO.EnumSigType.auditor.getValue(), builderDoc, doc, auditPerson, signPath, reportAudit.get().getCreateDate());
        }
        //签发人
        Optional<DtoStatusForReport> reportSign = statusForReportList.stream()
                .filter(p -> p.getModule().equals(EnumLIM.EnumReportModule.报告签发.getCode()) && EnumPRO.EnumStatus.已处理.getValue().equals(p.getStatus())).findFirst();
        if (reportSign.isPresent()) {
            String personId = reportSign.get().getModifier();
            Person signPerson = personService.findOne(personId, Boolean.TRUE);
            setWordPicDateAndName(EnumPRO.EnumSigType.signer.getValue(), builderDoc, doc, signPerson, signPath, reportSign.get().getCreateDate());
        } else {
            setWordPicDateAndName(EnumPRO.EnumSigType.signer.getValue(), builderDoc, doc, person, signPath, new Date());
        }
        if (reportCheck.isPresent() && StringUtil.isNotNull(checkPerson)) {
            setWordPicDateAndName(80, builderDoc, doc, checkPerson, signPath, reportCheck.get().getCreateDate());
        }
        if (reportAudit.isPresent() && StringUtil.isNotNull(auditPerson)) {
            setWordPicDateAndName(80, builderDoc, doc, auditPerson, signPath, reportAudit.get().getCreateDate());
        }
    }

    /**
     * 设置签名，日期，及人名
     */
    private void setWordPicDateAndName(Integer personType, DocumentBuilder builderDoc,
                                       com.aspose.words.Document doc, Person person, String signPath, Date date) throws Exception {
        String sigPath = StringUtil.isNull(person) ? signPath : StringUtils.isNotNullAndEmpty(person.getSignature()) ? person.getSignature() : signPath;
        String personName = StringUtils.isNotNull(person) ? person.getCName() : "";
        //签名书签
        String sigMark = "";
        //日期书签
        String dateMark = "";
        //人名书签
        String nameMark = "";
        if (EnumPRO.EnumSigType.maker.getValue().equals(personType)) {
            sigMark = EnumPRO.EnumSigType.maker.toString();
            dateMark = EnumPRO.EnumSigDate.makersmallDate.toString();
            nameMark = "makerChinese";
        } else if (EnumPRO.EnumSigType.certificateMaker.getValue().equals(personType)) {
            sigMark = EnumPRO.EnumSigType.certificateMaker.toString();
            dateMark = EnumPRO.EnumSigDate.makersmallDate.toString();
            nameMark = "certificateChinese";
        } else if (personType.equals(51)) {
            sigMark = "reviewer";
            dateMark = "reviewSmallDate";
            nameMark = "reviewChinese";
        } else if (EnumPRO.EnumSigType.auditor.getValue().equals(personType)) {
            sigMark = EnumPRO.EnumSigType.auditor.toString();
            dateMark = EnumPRO.EnumSigDate.auditorsmallDate.toString();
            nameMark = "auditorChinese";
        } else if (EnumPRO.EnumSigType.signer.getValue().equals(personType)) {
            sigMark = EnumPRO.EnumSigType.signer.toString();
            dateMark = EnumPRO.EnumSigDate.signersmallDate.toString();
            nameMark = "signerChinese";
        } else if (personType.equals(80)) {
            sigMark = "certAuditor";
            dateMark = "certAuditorSmallDate";
            nameMark = "certAuditorChinese";
        }

        //图片签名
        if (builderDoc.moveToBookmark(sigMark)) {
            insertImage(sigPath, builderDoc, false);
        }
        //写入日期
        if (builderDoc.moveToBookmark(dateMark)) {
            String dateStr = DateUtil.dateToString(date, DateUtil.YEAR_ZH_CN);
            builderDoc.insertHtml("<span style='font-family:Times new Roman; font-size: 14px'>" + dateStr + "</span>");
        }
        //写入人名
        if (builderDoc.moveToBookmark(nameMark)) {
            builderDoc.insertHtml("<span style='font-family:宋体; font-size: 14px'>" + personName + "</span>");
        }

        //签发之后报告锁定
        if (EnumPRO.EnumSigType.signer.getValue().equals(personType)) {
            if (builderDoc.moveToBookmark("reportDate")) {
                String dateStr = DateUtil.dateToString(date, "yyyy.MM.dd");
                double fontSize = builderDoc.getCurrentParagraph().getParagraphBreakFont().getSize();
                log.info("签发日期字体大小：" + fontSize);
                builderDoc.insertHtml("<span style='font-family:Times new Roman; font-size: " + fontSize + "pt'>" + dateStr + "</span>");
            }
            if (builderDoc.moveToBookmark("reportDateCn")) {
                String dateStr = DateUtil.dateToString(date, DateUtil.YEAR_ZH_CN);
                double fontSize = builderDoc.getCurrentParagraph().getParagraphBreakFont().getSize();
                log.info("签发日期字体大小：" + fontSize);
                builderDoc.insertHtml("<span style='font-family:Times new Roman; font-size: " + fontSize + "pt'>" + dateStr + "</span>");
            }
            doc.protect(com.aspose.words.ProtectionType.READ_ONLY, passwordValue());
//            if (builderDoc.moveToBookmark("signerSmallDate1")) {
//                builderDoc.insertField(DateUtil.YEAR_ZH_CN, "OpenDate");
//                doc.getMailMerge().execute(new String[]{"OpenDate"}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
//            }
//            if (builderDoc.moveToBookmark("signerSmallDate2")) {
//                builderDoc.insertField(DateUtil.YEAR_ZH_CN, "OpenDate1");
//                doc.getMailMerge().execute(new String[]{"OpenDate1"}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
//            }

        }
//        if (EnumPRO.EnumSigType.auditor.getValue().equals(personType)) {
//            if (builderDoc.moveToBookmark("OpenDate2")) {
//                builderDoc.insertField(DateUtil.YEAR_ZH_CN, "OpenDate2");
//                doc.getMailMerge().execute(new String[]{"OpenDate2"}, new Object[]{DateUtil.nowTime(DateUtil.YEAR_ZH_CN)});
//            }
//        }
    }

    /**
     * excel签名
     *
     * @param document      附件
     * @param personType    签名类型
     * @param multipartFile 签名图片
     * @throws Exception 异常抛出
     */
    @Override
    public void sigExcel(DtoDocument document, String personType, Boolean isDown, MultipartFile multipartFile) throws Exception {

        //移走签名
        Map<String, Integer> mapType = new HashMap<>();
        mapType.put(personType, 1);
        WorkbookDesigner designer = new WorkbookDesigner();
        designer.setWorkbook(new Workbook(filePathConfig.getFilePath() + document.getPath()));
        removeByDesigner(designer, mapType);
        setPicColumnByFile(designer, personType, 1, 0, isDown, multipartFile);
        // 保存文件
        designer.getWorkbook().save(filePathConfig.getFilePath() + document.getPath());

    }

    /**
     * excel签名
     *
     * @param fullPath
     * @param signPathList
     * @param personList
     * @param repId
     * @param fileName
     * @param physicalName
     * @param filePath
     * @param fileType
     * @param fileSize
     * @param copy
     * @param personType
     * @param signIndex
     */
    private void sigExcel(String fullPath, List<String> signPathList, List<DtoPerson> personList, String repId, String fileName,
                          String physicalName, String filePath, String fileType, Integer fileSize, Boolean copy, Integer personType,
                          Integer signIndex, String timeStr, Boolean isXC) {
        try {
            //todo 需要把签名变成集合传过来
            //region excel报表签名
            if (signPathList.size() == 0) {
                for (DtoPerson person : personList) {
                    if (StringUtils.isNotNullAndEmpty(person)) {
                        String sigPath = StringUtils.isNotNullAndEmpty(person.getSignature()) ? person.getSignature() : "";
                        signPathList.add(sigPath);
                    }
                }
            }
            //copy = changeCopy(copy);
            fullPath = getFullPath(repId, fileName, fullPath, physicalName, filePath, fileType, fileSize, copy);
            //移走签名
            Map<String, Integer> mapType = new HashMap<>();
            mapType.put(EnumPRO.EnumSigType.getByValue(personType).toString(), signIndex);
            //removeSignature(fullPath, mapType);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(new Workbook(filePathConfig.getFilePath() + fullPath));
            removeByDesigner(designer, mapType);
            //InputStream stream = new FileInputStream(filePathConfig.getFilePath() + sigPath);
            Boolean isChenge = false;
            Boolean isEnd = false;
            String sigType = EnumPRO.EnumSigType.getByValue(personType).toString();
            String strValue = EnumPRO.EnumSigType.getByValue(personType).getName();
            if (EnumPRO.EnumSigType.上岗证人员.getValue().equals(personType)) {
//                isChenge = true;
                strValue = EnumPRO.EnumSigType.分析者.toString();
                sigType = EnumPRO.EnumSigType.分析者.toString();
            } else if (EnumPRO.EnumSigType.审核者.getValue().equals(personType)
                    || EnumPRO.EnumSigType.审核人员_日期.getValue().equals(personType)) {
                isEnd = true;
            } else if (EnumPRO.EnumSigType.测试人员.getValue().equals(personType)) {
                isChenge = true;
            } else if (EnumPRO.EnumSigType.采样人.getValue().equals(personType)) {
                // 如果有多个人的，最多获取4个
                signPathList = signPathList.stream().limit(4).collect(Collectors.toList());
                List<String> finalSignPathList = signPathList;
                personList = personList.stream().filter(p -> finalSignPathList.contains(p.getSignature())).collect(Collectors.toList());
                isChenge = true;
            }
            //如果其实位置大于1，并且是否调整签名位置为false的时候，签名位置需要调整
            //现在只有上岗证人员才会出现signIndex>1的情况，后续可能需要调整
            if (!isChenge && signIndex > 1) {
                isChenge = true;
                signIndex = 1;
            }

            for (String path : signPathList) {
                try {
                    setPic(designer, path, sigType, strValue, isChenge, isEnd, signIndex, timeStr, isXC, repId);
                    signIndex++;
                } catch (Exception exception) {
                    log.info("签名发生异常 - 10:" + "人员未维护电子签名，请联系系统管理员维护电子签名！");
                    throw new BaseException("[" + personList.stream().map(DtoPerson::getCName).collect(Collectors.joining(",")) + "]人员未维护电子签名，请联系系统管理员维护电子签名！");
                }
            }
            designer.getWorkbook().save(filePathConfig.getFilePath() + fullPath);
            //endregion
        } catch (Exception ex) {
            if (ex instanceof BaseException) {
                log.error("签名发生异常 - 8:" + ex.getMessage());
            } else {
                log.error("签名发生异常 - 8:", ex);
            }
            throw new BaseException(ex.getMessage());
        }
    }

    protected Boolean changeCopy(Boolean isCopy) {
        return isCopy;
    }

    protected void setPic(WorkbookDesigner designer, String sigPath, String personType, String formula,
                          Boolean isChenge, Boolean isEnd, Integer signIndex, String timeStr, Boolean isXC, String repId) throws Exception {
        this.setPic(designer, sigPath, personType, formula, isChenge, isEnd, signIndex, timeStr, isXC);
    }


    protected void setPic(WorkbookDesigner designer, String sigPath, String personType, String formula,
                          Boolean isChenge, Boolean isEnd, Integer signIndex, String timeStr) throws Exception {
        setPicColumn(designer, sigPath, personType, formula, isChenge, isEnd, signIndex, timeStr, 0, Boolean.FALSE, 0, 0);
    }

    /**
     * excel报表图片签名方法
     *
     * @param designer   excel表格
     * @param sigPath    图片路径
     * @param personType 签名类型
     * @param formula    签名位置
     * @param isChenge   是否多签名
     * @param isEnd      是否结束
     */
    protected void setPic(WorkbookDesigner designer, String sigPath, String personType, String formula,
                          Boolean isChenge, Boolean isEnd, Integer signIndex, String timeStr, Boolean isXC) throws Exception {
        setPicColumn(designer, sigPath, personType, formula, isChenge, isEnd, signIndex, timeStr, 0, Boolean.FALSE, 0, 0);
        if (EnumPRO.EnumSigType.分析者.getName().equals(personType)) {
            //签名前是否需要解码
//            decodeByDocment(designer);
//            setPicColumn(designer, "", EnumPRO.EnumSigType.分析日期.getName(), EnumPRO.EnumSigType.分析日期.getName(), isChenge, Boolean.TRUE, signIndex,
//                    timeStr, 0, Boolean.FALSE, 0, 0);
        }
        if (EnumPRO.EnumSigType.复核者.getName().equals(personType)) {
            decodeByDocment(designer);
            setPicColumn(designer, "", EnumPRO.EnumSigType.校核日期.getName(), EnumPRO.EnumSigType.校核日期.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
            decodeByDocment(designer);
            setPicColumn(designer, "", EnumPRO.EnumSigType.复核日期.getName(), EnumPRO.EnumSigType.复核日期.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
        }
        if (EnumPRO.EnumSigType.采样人.getName().equals(personType)) {
            decodeByDocment(designer);
            setPicColumn(designer, "", EnumPRO.EnumSigType.分析日期.getName(), EnumPRO.EnumSigType.采样日期.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
            setPicColumn(designer, sigPath, EnumPRO.EnumSigType.检测人员.getName(), EnumPRO.EnumSigType.检测人员.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
            setPicColumn(designer, sigPath, EnumPRO.EnumSigType.监测人员.getName(), EnumPRO.EnumSigType.监测人员.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
            setPicColumn(designer, "", EnumPRO.EnumSigType.监测日期.getName(), EnumPRO.EnumSigType.监测日期.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
            setPicColumn(designer, "", EnumPRO.EnumSigType.检测日期.getName(), EnumPRO.EnumSigType.检测日期.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
        }
        if (EnumPRO.EnumSigType.接样者.getName().equals(personType)) {
            decodeByDocment(designer);
            setPicColumn(designer, sigPath, EnumPRO.EnumSigType.接样人.getName(), EnumPRO.EnumSigType.接样人.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
            setPicColumn(designer, "", EnumPRO.EnumSigType.接样时间.getName(), EnumPRO.EnumSigType.接样时间.getName(), isChenge, Boolean.TRUE, signIndex,
                    timeStr, 0, Boolean.FALSE, 0, 0);
        }
    }

    /**
     * excel报表图片签名方法
     *
     * @param designer   excel表格
     * @param sigPath    图片路径
     * @param personType 签名类型
     * @param formula    签名位置
     * @param isChenge   是否多签名
     * @param isEnd      是否结束
     */
    protected void setPicColumn(WorkbookDesigner designer, String sigPath, String personType, String formula,
                                Boolean isChenge, Boolean isEnd, Integer signIndex, String timeStr,
                                Integer excursionColumn, Boolean isSplitDate, Integer splitIndex, Integer timeIndex) throws Exception {
        this.setPicColumn(designer, sigPath, personType, formula, isChenge, isEnd, signIndex, timeStr, excursionColumn, isSplitDate, splitIndex, timeIndex, false);
    }


    /**
     * excel报表图片签名方法
     *
     * @param designer   excel表格
     * @param sigPath    图片路径
     * @param personType 签名类型
     * @param formula    签名位置
     * @param isChenge   是否多签名
     * @param isEnd      是否结束
     * @param isSelfCell 是否签名在占位符位置
     */
    protected void setPicColumn(WorkbookDesigner designer, String sigPath, String personType, String formula,
                                Boolean isChenge, Boolean isEnd, Integer signIndex, String timeStr,
                                Integer excursionColumn, Boolean isSplitDate, Integer splitIndex, Integer timeIndex, Boolean isSelfCell) throws Exception {
        for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
            Worksheet u = designer.getWorkbook().getWorksheets().get(i);
            if (isEnd) {
                u.protect(ProtectionType.ALL, passwordValue(), "");
                u.getProtection().setAllowSelectingLockedCell(false);
                u.getProtection().setAllowSelectingUnlockedCell(false);
            }
            if (u.getCells() != null) {
                for (int j = 0; j < u.getCells().getMaxRow(); j++) {
                    Cell cell = u.getCells().find(formula, u.getCells().get(j, 0), new FindOptions());
                    if (cell != null) {
                        //表示有图片
                        if (StringUtil.isNotEmpty(sigPath)) {
                            try {
                                InputStream stream = new FileInputStream(filePathConfig.getFilePath() + sigPath);
                                int leftWidth = 0;
                                int maxColumn = cell.getColumn();
                                if (cell.isMerged()) {
                                    int mergedColumnCount = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + excursionColumn;
                                    try {
                                        Picture pic;
                                        if (isSelfCell) {
                                            cell.setValue("");
                                            pic = u.getShapes().addPicture(cell.getRow(), cell.getMergedRange().getFirstColumn(), stream, 5, 5);
                                        } else {
                                            pic = u.getShapes().addPicture(cell.getRow(), mergedColumnCount, stream, 5, 5);
                                        }pic.setName(personType + signIndex);
                                        pic.setWidth(75);
                                        pic.setHeight(30);
                                        pic.setTop(5);
                                        j = cell.getRow();
                                        if (isChenge) {
                                            maxColumn = mergedColumnCount;
                                            for (int k = 0; k < maxColumn; k++) {
                                                leftWidth += u.getCells().getColumnWidthPixel(k);
                                            }
                                            pic.setLeftToCorner(leftWidth + pic.getWidth() * signIndex);
                                        }
                                        if (formula.contains("日期") || formula.contains("时间") || personType.contains("日期") || personType.contains("时间")) {
                                            cell = u.getCells().get(cell.getRow(), mergedColumnCount);
                                            if (cell.isMerged()) {
                                                int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + timeIndex;
                                                cell = u.getCells().get(cell.getRow(), column);
                                            } else {
                                                cell = u.getCells().get(cell.getRow(), mergedColumnCount + 1);
                                            }
                                            if (isSplitDate) {
                                                //绑定年
                                                Date dateValue = DateUtil.stringToDate(timeStr, DateUtil.YEAR_ZH_CN);
                                                Calendar calendar = Calendar.getInstance();
                                                calendar.setTime(dateValue);
                                                Integer year = calendar.get(Calendar.YEAR);
                                                cell.putValue(year);
                                                Style style = cell.getStyle();
                                                style.setHorizontalAlignment(TextAlignmentType.CENTER);
                                                cell.setStyle(style);
                                                //绑定月
                                                if (cell.isMerged()) {
                                                    int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + splitIndex;
                                                    cell = u.getCells().get(cell.getRow(), column);
                                                }
                                                Integer month = calendar.get(Calendar.MONTH);
                                                cell.putValue(month + 1);
                                                style = cell.getStyle();
                                                style.setHorizontalAlignment(TextAlignmentType.CENTER);
                                                cell.setStyle(style);
                                                //绑定日
                                                if (cell.isMerged()) {
                                                    int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + splitIndex;
                                                    cell = u.getCells().get(cell.getRow(), column);
                                                }
                                                Integer day = calendar.get(Calendar.DATE);
                                                cell.putValue(day);
                                                style = cell.getStyle();
                                                style.setHorizontalAlignment(TextAlignmentType.CENTER);
                                                cell.setStyle(style);
                                            } else {
                                                cell.putValue(timeStr);
                                            }
                                        }
                                    } catch (Exception ex) {
                                        throw new Exception("添加签名出错：" + ex.getMessage());
                                    }
                                } else {
                                    Picture pic;
                                    if (isSelfCell){
                                        pic = u.getShapes().addPicture(cell.getRow(), cell.getColumn(), stream, 5, 5);
                                    }else {
                                        pic = u.getShapes().addPicture(cell.getRow(), cell.getColumn() + 1, stream, 5, 5);
                                    }
                                    pic.setName(personType + signIndex);
                                    pic.setWidth(75);
                                    pic.setHeight(30);
                                    pic.setTop(5);
                                    j = cell.getRow();
                                    if (isChenge) {
                                        maxColumn = 1;
                                        for (int k = 0; k < maxColumn; k++) {
                                            leftWidth += u.getCells().getColumnWidthPixel(k);
                                        }
                                        pic.setLeftToCorner(leftWidth + pic.getWidth() * signIndex);
                                    }
                                }
                            } catch (Exception ex) {
                                log.info("签名发生异常 - 7:" + "人员未维护电子签名，请联系系统管理员维护电子签名！");
                                throw new BaseException("人员未维护电子签名，请联系系统管理员维护电子签名！");
                            }
                        } else {
                            if (cell.isMerged()) {
                                int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + timeIndex;
                                cell = u.getCells().get(cell.getRow(), column);
                            } else {
                                cell = u.getCells().get(cell.getRow(), cell.getColumn() + 1);
                            }
                            if (formula.contains("日期") || formula.contains("时间") || personType.contains("日期") || personType.contains("时间")) {
                                cell.putValue(timeStr);
                            }
                        }
                    } else {
                        //如果是null，则表示当前sheet页中不包含任何满足条件的单元格
                        break;
                    }
                    if (isEnd) {
                        cell = u.getCells().find("报告日期：", u.getCells().get(j, 0), new FindOptions());
                        if (cell != null) {
                            if (cell.isMerged()) {
                                int mergedColumnCount = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount();
                                cell = u.getCells().get(cell.getRow(), mergedColumnCount);
                                cell.putValue(timeStr);
                            } else {
                                cell = u.getCells().get(cell.getRow(), cell.getColumn() + 1);
                                cell.putValue(timeStr);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据附件和占位符和图片直接设置excel报表图片签名
     *
     * @param designer        excel表格
     * @param personType      签名类型
     * @param excursionColumn 签名间隔
     * @param isDown          是否向下签名
     * @param multipartFile   签名图片附件
     */
    protected void setPicColumnByFile(WorkbookDesigner designer,
                                      String personType,
                                      Integer signIndex,
                                      Integer excursionColumn,
                                      Boolean isDown,
                                      MultipartFile multipartFile) throws Exception {
        for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
            Worksheet u = designer.getWorkbook().getWorksheets().get(i);
            u.protect(ProtectionType.ALL, passwordValue(), "");
            u.getProtection().setAllowSelectingLockedCell(false);
            u.getProtection().setAllowSelectingUnlockedCell(false);

            if (u.getCells() != null) {
                for (int j = 0; j < u.getCells().getMaxRow(); j++) {
                    Cell cell = u.getCells().find(personType, u.getCells().get(j, 0), new FindOptions());
                    if (cell != null) {
                        //表示有图片,图片直接转输入流
                        if (StringUtil.isNotNull(multipartFile)) {
                            try {
                                InputStream stream = multipartFile.getInputStream();
                                // 签名位置行
                                int rowIndex = isDown ? cell.getRow() + 1 : cell.getRow();
                                if (cell.isMerged()) {
                                    // 签名位置列 向下签名时，行数加一
                                    int mergedColumnCount = isDown ?
                                            cell.getMergedRange().getFirstColumn() + 1
                                            : cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + excursionColumn;
                                    try {
                                        // 签名到占位符的下一行
                                        Picture pic = u.getShapes().addPicture(rowIndex, mergedColumnCount, stream, 5, 5);
                                        pic.setName(personType + signIndex);
                                        pic.setWidth(75);
                                        pic.setHeight(30);
                                        pic.setTop(5);
                                        pic.setLeft(5);
                                        j = cell.getRow();
                                    } catch (Exception ex) {
                                        throw new Exception("添加签名出错：" + ex.getMessage());
                                    }
                                } else {
                                    Picture pic = u.getShapes().addPicture(rowIndex, cell.getColumn() + 1, stream, 5, 5);
                                    pic.setName(personType + signIndex);
                                    pic.setWidth(75);
                                    pic.setHeight(30);
                                    pic.setTop(5);
                                    j = cell.getRow();
                                }
                            } catch (Exception ex) {
                                log.info("签名发生异常 - 7:" + ex.getMessage());
                                throw new BaseException("签名发生异常 - 7:" + ex.getMessage());
                            }
                        }
                    } else {
                        //如果是null，则表示当前sheet页中不包含任何满足条件的单元格
                        break;
                    }
                }
            }
        }
    }

    /**
     * 分页设置excel报表图片签名方法
     *
     * @param designer        excel表格
     * @param personType      签名类型
     * @param formula         签名位置
     * @param isChenge        是否多签名
     * @param isEnd           是否结束
     * @param timeStr         时间格式
     * @param excursionColumn 时间格式
     * @param isSplitDate     是否拆分时间
     * @param splitIndex      拆分时间间距
     * @param timeIndex       签时间间隔
     * @param params          数据源
     */
    protected void setPicColumn(WorkbookDesigner designer, String personType, String formula,
                                Boolean isChenge, Boolean isEnd, String timeStr,
                                Integer excursionColumn, Boolean isSplitDate, Integer splitIndex,
                                Integer timeIndex, List<Object> params) throws Exception {
        Map<String, List<Map<String, Object>>> reportDataMap = (Map<String, List<Map<String, Object>>>) params.get(3);
        Map<String, Object> positionMap = new HashMap<>();
        boolean isSelfCell = false;
        if (params.size() == 5) {
            positionMap = (Map<String, Object>) params.get(4);
            if (positionMap.containsKey("selfCell")) {
                isSelfCell = (Boolean) positionMap.get("selfCell");
            }
        }
        // 循环所有的sheet页
        for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
            Worksheet u = designer.getWorkbook().getWorksheets().get(i);
            if (isEnd) {
                u.protect(ProtectionType.ALL, passwordValue(), "");
                u.getProtection().setAllowSelectingLockedCell(false);
                u.getProtection().setAllowSelectingUnlockedCell(false);
            }
            if (u.getCells() != null) {
                // 记录
                int page = 1;
                // 循环当前sheet最大有效行
                for (int j = 0; j < u.getCells().getMaxRow(); j++) {
                    Cell cell = u.getCells().find(formula, u.getCells().get(j, 0), new FindOptions());
                    if (cell != null) {
                        // 根据分页后签名位置的顺序,获取对应分页下的签名数据,暂存到Signature
                        if (reportDataMap.containsKey("Signature" + page)){
                            List<DtoDocument> pageSignatureList = (List<DtoDocument>) reportDataMap.get("Signature" + page).get(0).get(personType);
                            //表示有图片
                            if (StringUtil.isNotEmpty(pageSignatureList)) {
                                int signIndex = 0;
                                InputStream stream = null;
                                try {
                                    for (DtoDocument dtoDocument : pageSignatureList) {
                                        stream = new FileInputStream(filePathConfig.getFilePath() + dtoDocument.getPath());
                                        int leftWidth = 0;
                                        int maxColumn = cell.getColumn();
                                        if (cell.isMerged()) {
                                            int mergedColumnCount = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + excursionColumn;
                                            try {
                                                Picture pic;
                                                if (isSelfCell) {
                                                    cell.setValue("");
                                                    pic = u.getShapes().addPicture(cell.getRow(), cell.getMergedRange().getFirstColumn(), stream, 5, 5);
                                                    pic.setWidth(75);
                                                    pic.setHeight(30);
                                                    if (isChenge) {
                                                        maxColumn = cell.getMergedRange().getFirstColumn();
                                                        for (int k = 0; k < maxColumn; k++) {
                                                            leftWidth += u.getCells().getColumnWidthPixel(k);
                                                        }
                                                        pic.setLeftToCorner(leftWidth + pic.getWidth() * signIndex);
                                                    }
                                                } else {
                                                    pic = u.getShapes().addPicture(cell.getRow(), mergedColumnCount, stream, 5, 5);
                                                    pic.setWidth(75);
                                                    pic.setHeight(30);
                                                    if (isChenge) {
                                                        maxColumn = mergedColumnCount;
                                                        for (int k = 0; k < maxColumn; k++) {
                                                            leftWidth += u.getCells().getColumnWidthPixel(k);
                                                        }
                                                        pic.setLeftToCorner(leftWidth + pic.getWidth() * signIndex);
                                                    }
                                                }
                                                pic.setTop(5);
                                                pic.setLeft(5);
                                                pic.setName(personType + signIndex);
                                                j = cell.getRow();
                                                if (formula.contains("日期") || personType.contains("日期")) {
                                                    cell = u.getCells().get(cell.getRow(), mergedColumnCount);
                                                    if (cell.isMerged()) {
                                                        int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + timeIndex;
                                                        cell = u.getCells().get(cell.getRow(), column);
                                                    } else {
                                                        cell = u.getCells().get(cell.getRow(), mergedColumnCount + 1);
                                                    }
                                                    if (isSplitDate) {
                                                        //绑定年
                                                        Date dateValue = DateUtil.stringToDate(timeStr, DateUtil.YEAR_ZH_CN);
                                                        Calendar calendar = Calendar.getInstance();
                                                        calendar.setTime(dateValue);
                                                        Integer year = calendar.get(Calendar.YEAR);
                                                        cell.putValue(year);
                                                        Style style = cell.getStyle();
                                                        style.setHorizontalAlignment(TextAlignmentType.CENTER);
                                                        cell.setStyle(style);
                                                        //绑定月
                                                        if (cell.isMerged()) {
                                                            int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + splitIndex;
                                                            cell = u.getCells().get(cell.getRow(), column);
                                                        }
                                                        Integer month = calendar.get(Calendar.MONTH);
                                                        cell.putValue(month + 1);
                                                        style = cell.getStyle();
                                                        style.setHorizontalAlignment(TextAlignmentType.CENTER);
                                                        cell.setStyle(style);
                                                        //绑定日
                                                        if (cell.isMerged()) {
                                                            int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount() + splitIndex;
                                                            cell = u.getCells().get(cell.getRow(), column);
                                                        }
                                                        Integer day = calendar.get(Calendar.DATE);
                                                        cell.putValue(day);
                                                        style = cell.getStyle();
                                                        style.setHorizontalAlignment(TextAlignmentType.CENTER);
                                                        cell.setStyle(style);
                                                    } else {
                                                        cell.putValue(timeStr);
                                                    }
                                                }
                                            } catch (Exception ex) {
                                                throw new Exception("添加签名出错：" + ex.getMessage());
                                            }
                                        } else {
                                            Picture pic = u.getShapes().addPicture(cell.getRow(), cell.getColumn() + 1, stream, 5, 5);
                                            pic.setName(personType + signIndex);
                                            pic.setWidth(75);
                                            pic.setHeight(30);
                                            j = cell.getRow();
                                            if (isChenge) {
                                                for (int k = 0; k <= maxColumn; k++) {
                                                    leftWidth += u.getCells().getColumnWidthPixel(k);
                                                }
                                                pic.setLeftToCorner(leftWidth + pic.getWidth() * signIndex);
                                            }
                                        }
                                        signIndex++;
                                        stream.close();
                                    }
                                } catch (Exception ex) {
                                    if (stream != null) {
                                        stream.close();
                                    }
                                    log.error(ex.getMessage(), ex);
                                    log.info("签名发生异常 - 7:" + "人员未维护电子签名，请联系系统管理员维护电子签名！");
                                    throw new BaseException("人员未维护电子签名，请联系系统管理员维护电子签名！");
                                }
                            } else {
                                int mergedColumnCount = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount();
                                int column = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount();
                                cell = u.getCells().get(cell.getRow(), column);
                                cell = u.getCells().get(cell.getRow(), mergedColumnCount);
                                cell.putValue(timeStr);
                            }
                            // 页数加一
                            page++;
                        }
                    } else {
                        //如果是null，则表示当前sheet页中不包含任何满足条件的单元格
                        break;
                    }
                    if (isEnd) {
                        cell = u.getCells().find("报告日期：", u.getCells().get(j, 0), new FindOptions());
                        if (cell != null) {
                            if (cell.isMerged()) {
                                int mergedColumnCount = cell.getMergedRange().getFirstColumn() + cell.getMergedRange().getColumnCount();
                                cell = u.getCells().get(cell.getRow(), mergedColumnCount);
                                cell.putValue(timeStr);
                            } else {
                                cell = u.getCells().get(cell.getRow(), cell.getColumn() + 1);
                                cell.putValue(timeStr);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 移除签名图片
     *
     * @param designer 文件
     * @param mapType  类型组
     */
    private WorkbookDesigner removeExcel(WorkbookDesigner designer, Map<String, Integer> mapType) {
        //移除签名需要解锁
        decodeByDocment(designer);
        for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
            int num = i;
            mapType.forEach((mapStr, personType) -> {
                Worksheet sheet = designer.getWorkbook().getWorksheets().get(num);
                Cell cell = designer.getWorkbook().getWorksheets().get(num).getCells()
                        .find(mapStr, designer.getWorkbook().getWorksheets().get(num).getCells().get(0, 0), new FindOptions());
                if (cell != null) {
                    ShapeCollection picList = designer.getWorkbook().getWorksheets().get(num).getShapes();
                    for (int k = (picList.getCount() - 1); k >= 0; k--) {
                        // 当personType等于0 时，当做类型存在多个签名，因为拿不到当前类型签名的数量，所以按照总数遍历清除。
                        personType = personType == 0 ? picList.getCount() : personType;
                        //一般n只是1，只有当签名标签是分析者的时候才会是2
                        for (int n = 0; n <= personType; n++) {
                            if (picList.get(k).getName().equals(mapStr + n)) {
                                picList.removeAt(k);
                                break;
                            }
                        }
                    }
                    if (mapStr.contains("日期") || mapStr.contains("时间")) {
                        //移除日期/时间数据
                        for (int j = 0; j < sheet.getCells().getMaxRow(); j++) {
                            Cell dateCell = sheet.getCells().find(mapStr, sheet.getCells().get(j, 0), new FindOptions());
                            if (dateCell != null) {
                                int mergedColumnCount = dateCell.getMergedRange().getFirstColumn() + dateCell.getMergedRange().getColumnCount();
                                dateCell = sheet.getCells().get(dateCell.getRow(), mergedColumnCount);
                                dateCell.putValue("");
                            } else {
                                break;
                            }
                        }
                    }
                }
            });
        }
        return designer;
    }

    /**
     * 移除图片
     *
     * @param doc      文档
     * @param typeList 类型
     * @return 文档
     */
    private com.aspose.words.Document removeWord(com.aspose.words.Document doc, List<String> typeList) {
        for (String personType : typeList) {
            Bookmark bk = doc.getRange().getBookmarks().get(personType);
            if (bk != null) {
                NodeCollection shapes = doc.getChildNodes(NodeType.SHAPE, true);
                for (int i = 0; i < shapes.getCount(); i++) {
                    if (shapes.get(i).getPreviousSibling() == bk.getBookmarkStart()) {
                        shapes.get(i).remove();
                        break;
                    }
                }
            }
        }
        return doc;
    }

    /**
     * 不同项目密码不同
     *
     * @return 返回密码
     */
    protected String passwordValue() {
        return "sinoyd**..";
    }

    /**
     * excel锁定解除
     *
     * @param designer 文档
     */
    protected void decodeByDocment(WorkbookDesigner designer) {
        for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
            Worksheet u = designer.getWorkbook().getWorksheets().get(i);
            //当前excel是否锁定
            if (u.isProtected()) {
                u.unprotect(passwordValue());
            }
        }
    }

    /**
     * 报告签章并转换为pdf格式
     *
     * @param reportId 报告id
     */
    @Override
    public void sealChangePdf(String reportId) {
        DtoReport report = reportService.findAttachPath(reportId);
        List<DtoDocument> documentList = documentRepository.findByFolderId(reportId);
        documentList = documentList.stream().filter(p -> p.getFilename().contains("副本") && !p.getDocSuffix().contains("pdf")
                && p.getIsDeleted().equals(false)).collect(Collectors.toList());
        //报告签章
        seal(documentList, report, true);
        //转换为pdf格式
        changePdf(documentList, reportId);
    }

    /**
     * 设置签发时需要展示的相关信息（监管平台系统编号，电子报告副本备注）
     *
     * @param builder        报告文档对象
     * @param reportId       报告id
     */
    @Override
    public void setSignInfo(DocumentBuilder builder, String reportId) {
        DtoReport report = reportService.findAttachPath(reportId);
        if (StringUtil.isNotNull(report)) {
            log.info("开始判断是否需要设置监管平台系统编号，报告编号： " + report.getCode());
            DtoProject project = projectRepository.findOne(report.getProjectId());
            if (StringUtil.isNotNull(project)) {
                List<DtoProjectContract> projectContractList = projectContractRepository.findByProjectId(project.getId());
                DtoProjectContract projectContract = StringUtil.isNotEmpty(projectContractList) ? projectContractList.get(0) : null;
                if (StringUtil.isNotNull(projectContract) && StringUtil.isNotNull(projectContract.getIsPush()) && projectContract.getIsPush().equals(1)) {
                    String regulateCode = StringUtil.isNotEmpty(report.getRegulateCode()) ? report.getRegulateCode() : "";
                    log.info("开始设置监管平台系统编号，报告编号： " + report.getCode() + " 监管平台系统编号：" + regulateCode);
                    if (StringUtil.isNotEmpty(regulateCode)) {
                        log.info("监管平台系统编号不为空，系统编号：" + regulateCode);
                        try {
                            if (builder.moveToBookmark("regulateCodeMark")) {
                                log.info("成功移动至监管系统编号书签位置");
                                //插入监管系统编号
                                double fontSize = builder.getCurrentParagraph().getParagraphBreakFont().getSize();
                                String fontName = builder.getCurrentParagraph().getParagraphBreakFont().getName();
                                String html = "<span style='font-family:楷体; font-size: " + fontSize + "pt'>" + "系统编号：" + "</span>";
                                html += "<span style='font-family:" + fontName + "; font-size: " + fontSize + "pt'>" + regulateCode + "</span>";
                                builder.insertHtml(html);
                            }
                        } catch (Exception e) {
                            log.error("设置监管平台系统编号失败", e);
                        }
                    }
                }
            }
        }

    }

    /**
     * 报告文档转pdf格式
     *
     * @param documentList 报告文档列表
     */
    public void changePdf(List<DtoDocument> documentList, String reportId) {
        try {
            for (DtoDocument document : documentList) {
                String docPath = filePathConfig.getFilePath() + document.getPath();
                Document doc = new Document(docPath);
                String pdfPhysicalFileName = document.getPhysicalName().split("\\.")[0] + ".pdf";
                String pdfFileName = document.getFilename().split("\\.")[0] + ".pdf";
                String pfdDocPath = document.getPath().replace(document.getPhysicalName(), pdfPhysicalFileName);
                //复制已完成签名和盖章的副本doc文件，并修改后缀名为".pdf"
                createDocCopy(reportId, pdfFileName, pdfPhysicalFileName, document.getPath(), pfdDocPath, "pdf", document.getDocSize());
                //将复制后的副本文件内容转为pdf格式
                doc.save(filePathConfig.getFilePath() + pfdDocPath, SaveFormat.PDF);
            }
        } catch (Exception e) {
            log.error("报告转pdf失败：", e);
            throw new BaseException("报告转pdf失败！");
        }
    }

    /**
     * 报告签章
     *
     * @param pathList  需要签章的文档路径数据
     * @param report    报告数据
     * @param isSicCma  是否签CMA章
     * @param isSicCnas 是否签CNAS章
     * @param isSicTest 是否签监测专用章
     * @param isOutput  是否为生成时签章
     */
    @Override
    public void seal(List<String> pathList, DtoReport report, boolean isSicCma, boolean isSicCnas, boolean isSicTest, boolean isOutput) {
        List<DtoSystemConfig> systemConfigList = systemConfigService.findAll();
        DtoSystemConfig systemConfig = StringUtil.isNotEmpty(systemConfigList) ? systemConfigList.get(0) : new DtoSystemConfig();
        log.info("系统配置对象id: " + systemConfig.getId());
        String folderId = StringUtil.isNotNull(systemConfig.getId()) ? systemConfig.getId() : UUIDHelper.GUID_EMPTY;
        List<DtoDocument> systemConfigDocList = documentRepository.findByFolderIdAndIsDeletedFalse(folderId);
        DtoDocument csDoc = systemConfigDocList.stream().filter(p -> "PRO_DocumentExtendType_CHECK".equals(p.getDocTypeId())).findFirst().orElse(null);
        //检测专用章图片路径
        String testSealPath = StringUtil.isNotNull(csDoc) ? csDoc.getPath() : "";
        log.info("检测专用章图片路径: " + testSealPath);
        DtoDocument cmaDoc = systemConfigDocList.stream().filter(p -> "PRO_DocumentExtendType_CMA".equals(p.getDocTypeId())).findFirst().orElse(null);
        //cma章图片路径
        String cmaSealPath = StringUtil.isNotNull(cmaDoc) ? cmaDoc.getPath() : "";
        log.info("cma章图片路径: " + cmaSealPath);
        DtoDocument cnasDoc = systemConfigDocList.stream().filter(p -> "PRO_DocumentExtendType_CNAS".equals(p.getDocTypeId())).findFirst().orElse(null);
        //CNAS章图片路径
        String cnasSealPath = StringUtil.isNotNull(cnasDoc) ? cnasDoc.getPath() : "";
        log.info("cnam章图片路径: " + cnasSealPath);
        for (String path : pathList) {
            try {
                if (isSicCma) {
                    double cmaWidth = getSealSize(systemConfig.getCmaWidth(), 135);
                    double cmaHeight = getSealSize(systemConfig.getCmaHeight(), 95);
                    setSeal(path, "cmaSeal", cmaSealPath, cmaWidth, cmaHeight, isOutput);
                    setSeal(path, "cmaSealTwo", cmaSealPath, cmaWidth, cmaHeight, isOutput);
                }
                if (isSicCnas) {
                    double cmasWidth = getSealSize(systemConfig.getCnasWidth(), 150);
                    double cmasHeight = getSealSize(systemConfig.getCnasHeight(), 56.7);
                    setSeal(path, "cnasSeal", cnasSealPath, cmasWidth, cmasHeight, isOutput);
                }
                if (isSicTest) {
                    double checkWidth = getSealSize(systemConfig.getCheckWidth(), 135);
                    double checkHeight = getSealSize(systemConfig.getCheckHeight(), 135);
                    setSeal(path, "testSeal", testSealPath, checkWidth, checkHeight, isOutput);
                    setSeal(path, "testSealTwo", testSealPath, checkWidth, checkHeight, isOutput);
                }
            } catch (Exception ex) {
                log.error("报告盖章发生异常：" + ex.getMessage(), ex);
                throw new BaseException("报告盖章发生异常" + ex.getMessage());
            }
        }
    }

    /**
     * 获取签章大小
     *
     * @param sealSize    指定大小
     * @param defaultSize 默认大小
     * @return 签章大小
     */
    private double getSealSize(String sealSize, double defaultSize) {
        if (StringUtil.isNotEmpty(sealSize) && MathUtil.isNumeral(sealSize)) {
            return Double.parseDouble(sealSize);
        }
        return defaultSize;
    }

    /**
     * 报表盖章
     *
     * @param documentList 报告文档列表
     * @param report       报告对象
     */
    @Override
    public void seal(List<DtoDocument> documentList, DtoReport report, boolean cma) {
        List<String> pathList = documentList.stream().map(DtoDocument::getPath).collect(Collectors.toList());
        String reportStamp = report.getReportStamp();
        // 统一空值检查
        boolean hasValidStamp = StringUtil.isNotEmpty(reportStamp);

        cma = hasValidStamp && reportStamp.contains(EnumPRO.EnumProjectReportStamp.CMA章.getValue().toString());
        boolean sicCnas = hasValidStamp && reportStamp.contains(EnumPRO.EnumProjectReportStamp.CNAS章.getValue().toString());
        boolean sicTest = hasValidStamp && reportStamp.contains(EnumPRO.EnumProjectReportStamp.监测专用章.getValue().toString());
        seal(pathList, report, cma, sicCnas, sicTest, false);
    }

    /**
     * 报告盖章
     *
     * @param filePath 路径
     * @param sealMark 签章位置书签名称
     * @param sealPath 签章图片路径
     * @param isOutput 是否为生成时签章
     * @throws Exception 报错
     */
    private void setSeal(String filePath, String sealMark, String sealPath, double width, double height, Boolean isOutput)
            throws Exception {
        if (!licenseService.isLicense()) {
            throw new Exception("aspose版权存在问题！");
        }
        if (new File(filePathConfig.getFilePath() + sealPath).exists()) {
//            fullPath = getFullPath(repId, fileName, fullPath);
            String path = isOutput ? filePath : filePathConfig.getFilePath() + filePath;
            log.info("=======================签章时的文件路径：" + path + "=======================================");
            Document doc = new Document(path);
            DocumentBuilder builderDoc = new DocumentBuilder(doc);
            if (builderDoc.moveToBookmark(sealMark)) {
                log.info("=============================文档操作对象创建成功，光标移动至书签[" + sealMark + "]位置==================================");
                log.info("=============================签章路径：" + filePathConfig.getFilePath() + "/" + sealPath + "==================================");
                if (StringUtils.isNotNullAndEmpty(sealPath)) {
                    builderDoc.insertImage(filePathConfig.getFilePath() + "/" + sealPath, RelativeHorizontalPosition.MARGIN,
                            0, RelativeHorizontalPosition.MARGIN, 0, width, height, WrapType.INLINE);
                }
                //如果是在生成时签章的，则签完章后移除对应书签，防止在报告签发时二次签章
                if (isOutput) {
                    Node currentNode = builderDoc.getCurrentNode();
                    if (StringUtil.isNotNull(currentNode)) {
                        if (currentNode.getNodeType() == NodeType.BOOKMARK_END) {
                            currentNode.remove();
                        }
                    }
                }
            }
            doc.save(path);
        } else {
            log.error("报告盖章发生异常，签章不存在，或签章配置路径错误!");
            throw new BaseException("报告盖章发生异常，签章不存在，或签章配置路径错误!");
        }
    }

    /**
     * 绑定图片签名
     *
     * @param sigPath    图片路径
     * @param builderDoc 文件
     * @param isHidden   是否隐藏
     */
    private void insertImage(String sigPath, DocumentBuilder builderDoc, Boolean isHidden) {
        try {
            if (StringUtils.isNotNullAndEmpty(sigPath)) {
                if (isHidden) {
                    builderDoc.getFont().setHidden(false);
                }
                builderDoc.insertImage(filePathConfig.getFilePath() + "/" + sigPath, RelativeHorizontalPosition.MARGIN,
                        0, RelativeHorizontalPosition.MARGIN, 0, 55, 20, WrapType.INLINE);
            }
        } catch (Exception ex) {
            log.info("签名发生异常", ex);
            throw new BaseException("图片签名失败!" + ex.getMessage());
        }
    }

    @Autowired
    @Lazy
    public void setSystemConfigService(SystemConfigService systemConfigService) {
        this.systemConfigService = systemConfigService;
    }

    @Autowired
    public void setStatusForReportRepository(StatusForReportRepository statusForReportRepository) {
        this.statusForReportRepository = statusForReportRepository;
    }

    @Autowired
    public void setReportDetailRepository(ReportDetailRepository reportDetailRepository) {
        this.reportDetailRepository = reportDetailRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setProjectContractRepository(ProjectContractRepository projectContractRepository) {
        this.projectContractRepository = projectContractRepository;
    }
}
