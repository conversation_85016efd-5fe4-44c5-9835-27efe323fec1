package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RPSamplingPlanFolderVO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上海监管平台采样计划：方案点位策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/15
 */
@Component
public class RPSamplingPlanFolder extends AbsRegulatoryPlatformRemote<RPSamplingPlanFolderVO> {

    @Override
    public List<RPSamplingPlanFolderVO> findByPage(BaseCriteria criteria, PageBean<RPSamplingPlanFolderVO> pageBean) {
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        return super.findByPage(criteria, pageBean);
    }

    @Override
    protected String loadFindSoapBody(String userName, String password, BaseCriteria criteria, PageBean<RPSamplingPlanFolderVO> pageBean) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        Map<String, Object> criteriaMap = new HashMap<>();
        criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "MTID", methodCriteria.getPId());
        return RPSoapBodyUtil.loadSoapBody(userName, password, queryMethodName(),  criteriaMap);
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划_点位.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划_点位.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划_点位.getDeleteMethod();
    }
}
