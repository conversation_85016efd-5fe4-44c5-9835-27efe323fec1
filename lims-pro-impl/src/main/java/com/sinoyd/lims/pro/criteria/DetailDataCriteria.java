package com.sinoyd.lims.pro.criteria;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 详细数据的查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2020年01月16日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DetailDataCriteria extends BaseCriteria implements Serializable {

    /**
     * 采样开始时间
     */
    private String startTime;

    /***
     * 采样结束时间
     */
    private String endTime;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 委托方
     */
    private String customerName;

    /**
     * 受检方
     */
    private String inspectedEnt;

    /**
     * 采样地点
     */
    private String samplePlace;

    /**
     * 排序方式
     */
    private String sortId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 参数配置ids
     */
    private List<String> paramsConfigIds;

    /**
     * 分析项目ids
     */
    private List<String> analyzeItemIds;

    /**
     * 项目类型ids
     */
    private List<String> projectTypeIds;


    /**
     * 检测类型id集合
     */
    private List<String> sampleTypeIds;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 要查询的select 语句字段
     */
    private String select = "";

    /**
     * 是否显示出证数据
     */
    private Boolean isDataEnabled;

    /**
     * 是否标红显示
     */
    private Boolean isOverRed = false;

    /**
     * 是否检毕
     */
    private Boolean isComplete = false;

    /**
     * 是否只显示重点污染源数据
     */
    private Boolean isShowPollution;

    /**
     * 当样品中没有此数据时的默认显示
     */
    private String noneDataShow = "";

    /**
     * 所属区域Id（空Guid代表所有）
     */
    private List<String> areaId;
    /**
     * 所属区域Id（空Guid代表所有）
     */
    private List<String> areaIds;

    /**
     * 项目id集合
     */
    private List<String> projectIds;

    /**
     * 是否项目进度
     */
    private Boolean isInquiry = false;

    /**
     * 样品ids
     */
    private List<String> sampleIds;

    /**
     * 分析项目排序id
     */
    private String analyseItemSortId;

    /**
     * 对选择的select 重新赋值
     *
     * @return 返回查询条件
     */
    public String getSelect() {
        if (StringUtils.isNotNullAndEmpty(this.select)) {
            return select;
        } else {
            List<String> selectList = new ArrayList<>();
            if (StringUtil.isNotNull(this.paramsConfigIds)) {
                for (String paramsConfigId : this.paramsConfigIds) {
                    selectList.add("json_value(paramsData,'$." + "P_" + paramsConfigId.replace("-", "") + "')");
                }
                return String.join(",", selectList);
            } else {
                return select;
            }
        }
    }

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and samplingTimeBegin >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and samplingTimeBegin < :endTime");
            values.put("endTime", to);
        }
        if (StringUtils.isNotNullAndEmpty(this.projectCode)) {
            condition.append(" and projectCode like :projectCode ");
            values.put("projectCode", "%" + projectCode + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.projectName)) {
            condition.append(" and projectName like :projectName ");
            values.put("projectName", "%" + projectName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.customerName)) {
            condition.append(" and customerName like :customerName ");
            values.put("customerName", "%" + customerName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.inspectedEnt)) {
            condition.append(" and inspectedEnt like :inspectedEnt ");
            values.put("inspectedEnt", "%" + inspectedEnt + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.samplePlace)) {
            condition.append(" and redFolderName like :samplePlace ");
            values.put("samplePlace", "%" + samplePlace + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            condition.append(" and sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            condition.append(" and projectId = :projectId");
            values.put("projectId", this.projectId);
        }
        if (StringUtil.isNotNull(this.projectTypeIds) && projectTypeIds.size() > 0) {
            condition.append(" and projectTypeId in :projectTypeIds");
            values.put("projectTypeIds", this.projectTypeIds);
        }
        if (StringUtil.isNotNull(this.sampleTypeIds) && sampleTypeIds.size() > 0) {
            condition.append(" and sampleTypeId in :sampleTypeIds");
            values.put("sampleTypeIds", this.sampleTypeIds);
        }
        if (StringUtil.isNotNull(isShowPollution) && isShowPollution) {
            condition.append(" and exists(select 1 from DtoEnterprise p where p.id = a.inspectedEntId and bitand(p.type,:type)<>0)");
            values.put("type", EnumBase.EnumEnterpriseType.污染源.getValue());
        }
        if (StringUtil.isNotNull(analyzeItemIds) && analyzeItemIds.size() > 0) {
            condition.append(" and exists(select 1 from DtoDetailAnalyseData da where da.detailDataId = a.id " +
                    "and da.isDeleted = 0 and da.analyseItemId in :analyseItemIds ");
            if (StringUtil.isNotNull(this.isComplete) && this.isComplete) {
                condition.append(" and da.isDataEnabled = :isDataEnabled)");
                values.put("isDataEnabled", this.isComplete);
            }
            values.put("analyseItemIds", this.analyzeItemIds);
        } else {
            if (StringUtil.isNotNull(this.isComplete) && this.isComplete) {
                condition.append(" and exists(select 1 from DtoDetailAnalyseData da where da.detailDataId = a.id " +
                        "and da.isDeleted = 0 and da.isDataEnabled = :isDataEnabled)");
                values.put("isDataEnabled", this.isComplete);
            }
        }
        if (StringUtil.isNotNull(this.areaIds) && this.areaIds.size() > 0) {
            condition.append(" and exists(select 1 from DtoEnterprise ent where ent.id = a.inspectedEntId and ent.areaId in :areaIds)");
            values.put("areaIds", this.areaIds);
        }
        if (StringUtil.isNotNull(this.projectIds) && projectIds.size() > 0) {
            condition.append(" and projectId in :projectIds");
            values.put("projectIds", this.projectIds);
        }
        return condition.toString();
    }
}
