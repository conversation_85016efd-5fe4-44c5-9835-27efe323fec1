package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.AnalysisQualityStatisticsCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlDetail;
import com.sinoyd.lims.pro.service.AnalysisQualityStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分析质控统计服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: AnalyseOriginalRecord服务")
@RestController
@RequestMapping("api/pro/analysisQualityStatistics")
public class AnalysisQualityStatisticsController extends ExceptionHandlerController<AnalysisQualityStatisticsService> {

    @ApiOperation(value = "分析质控统计查询", notes = "分析质控统计查询")
    @PostMapping
    public RestResponse<List<DtoQualityControlDetail>> findQualityControlDetailGroupByAnalyseDate(@RequestBody AnalysisQualityStatisticsCriteria baseCriteria) {
        PageBean<DtoQualityControlDetail> pageBean = super.getPageBean();
        pageBean.setPageNo(baseCriteria.getPage());
        pageBean.setRowsPerPage(baseCriteria.getRows());
        RestResponse<List<DtoQualityControlDetail>> restResp = new RestResponse<>();
        service.findQualityControlDetailGroupByAnalyseDate(pageBean,baseCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(pageBean.getData());
        restResp.setCount(pageBean.getRowsCount());
        return restResp;
    }
}
