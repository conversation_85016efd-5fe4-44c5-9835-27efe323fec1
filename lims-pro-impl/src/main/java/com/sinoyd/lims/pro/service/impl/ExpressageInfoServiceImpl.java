package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.criteria.ExpressageInfo2ReportCriteria;
import com.sinoyd.lims.pro.dto.DtoExpressageInfo;
import com.sinoyd.lims.pro.dto.DtoExpressageInfo2Report;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.entity.ExpressageInfo2Report;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.ExpressageInfo2ReportRepository;
import com.sinoyd.lims.pro.repository.ExpressageInfoRepository;
import com.sinoyd.lims.pro.repository.ReportRepository;
import com.sinoyd.lims.pro.service.ExpressageInfo2ReportService;
import com.sinoyd.lims.pro.service.ExpressageInfoService;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.ReportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 快递操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
@Service
public class ExpressageInfoServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoExpressageInfo, String, ExpressageInfoRepository> implements ExpressageInfoService {

    /**
     * 快递报告数据访问操作接口
     */
    @Autowired
    private ExpressageInfo2ReportRepository expressageInfo2ReportRepository;

    @Autowired
    @Lazy
    private ExpressageInfo2ReportService expressageInfo2ReportService;

    @Autowired
    private ReportService reportService;

    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    private NewLogService newLogService;


    @Override
    public void findByPage(PageBean<DtoExpressageInfo> pb, BaseCriteria expressageInfoCriteria) {
        pb.setEntityName("DtoExpressageInfo a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, expressageInfoCriteria);//分页查询出主表信息
        List<DtoExpressageInfo> datas = pb.getData();
        //筛选出分页后的主表id集合
        List<String> ids = datas.stream().map(DtoExpressageInfo::getId).collect(Collectors.toList());

        //查询对应快递下的关联表的信息
        PageBean<DtoExpressageInfo2Report> pbe2r = new PageBean<>();
        pbe2r.setRowsPerPage(Integer.MAX_VALUE);
        ExpressageInfo2ReportCriteria criteria = new ExpressageInfo2ReportCriteria();
        criteria.setExpressageInfoIds(ids);
        expressageInfo2ReportService.findByPage(pbe2r, criteria);

        //遍历绑定关联表信息
        for (DtoExpressageInfo data : datas) {
            List<DtoExpressageInfo2Report> dataList = pbe2r.getData().stream().filter(p -> p.getExpressageInfoId().equals(data.getId())).collect(Collectors.toList());
            data.setExpressageInfo2Report(dataList);
        }
    }

    @Transactional
    @Override
    public List<DtoExpressageInfo> save(Collection<DtoExpressageInfo> entities) {
        List<DtoExpressageInfo> list = new ArrayList<>();

        //获取去重的快递单号Set
        List<String> expressNumbers = entities.stream().map(DtoExpressageInfo::getExpressNumber).collect(Collectors.toList());
        if (entities.size() != expressNumbers.size()) {
            throw new BaseException("存在相同的快递单号，请核查");
        }
        List<String> ids = entities.stream().map(DtoExpressageInfo::getId).collect(Collectors.toList());

        List<String> nullNumbers = expressNumbers.stream().filter(p -> "".equals(p.trim())).collect(Collectors.toList());
        //读取重复的快递个数
        if (entities.size() != nullNumbers.size()) {
            List<String> numbers = expressNumbers.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            if (numbers.size() > 0) {
                Integer repeat = repository.countByExpressNumberInAndIdNotIn(numbers, ids);
                if (repeat > 0) {
                    throw new BaseException("存在相同的快递单号，请核查");
                }
            }
        }

        List<DtoExpressageInfo2Report> expressageInfo2Reports = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        //用于更新的快递id
        List<String> hasExpressReport = new ArrayList<>();
        List<String> hasRecoverReports = new ArrayList<>();
        //直接对所传数据进行遍历，保证返回的数据顺序与前端一致
        for (DtoExpressageInfo entity : entities) {
            //主键id存在，表明用于更新，若为更新则直接将该快递下对应的关联先清除
            if (StringUtils.isNotNullAndEmpty(entity.getId())) {
                //判断报告是否已发放
                List<String> reportIds = expressageInfo2ReportRepository.findByExpressageInfoIdIn(Collections.singletonList(entity.getId())).stream().map(DtoExpressageInfo2Report::getReportId).collect(Collectors.toList());
                List<String> newReportIds = entity.getExpressageInfo2Report().stream().map(DtoExpressageInfo2Report::getReportId).collect(Collectors.toList());
                //判断修改的关联报告是否更改
                if (!compareList(reportIds, newReportIds)) {
                    //相同的报告
                    List<String> sameReportIds = newReportIds.stream().filter(reportIds::contains).collect(Collectors.toList());
                    //判断修改的报告是否含有以前的报告
                    if (StringUtil.isEmpty(sameReportIds)) {
                        List<String> newReports = newReportIds.stream().filter(p -> !reportIds.contains(p)).collect(Collectors.toList());
                        //判断新增的报告中是否已经发放或回收
                        List<String> hasExpressReportCode = reportService.findAll(newReports).stream().filter(p -> p.getGrantStatus() == 3 || p.getGrantStatus() == 2).map(DtoReport::getCode).collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(hasExpressReportCode)) {
                            hasExpressReport.addAll(hasExpressReportCode);
                            continue;
                        }

                    }
                    //判断那些报告已回收（提示不可修改）
                    List<String> deleteReportIds = reportIds.stream().filter(p -> !newReportIds.contains(p)).collect(Collectors.toList());
                    //判断去除的报告中是否已被回收
                    List<String> hasRecoverReportCode = reportService.findAll(deleteReportIds).stream().filter(p -> p.getGrantStatus() == 3).map(DtoReport::getCode).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(hasRecoverReportCode)) {
                        hasRecoverReports.addAll(hasRecoverReportCode);
                        continue;
                    }
                }
                super.update(entity);
                String expressageInfoId = entity.getId();
                idList.add(expressageInfoId);
                for (DtoExpressageInfo2Report expressageInfo2Report : entity.getExpressageInfo2Report()) {
                    expressageInfo2Report.setExpressageInfoId(expressageInfoId);
                    expressageInfo2Reports.add(expressageInfo2Report);
                }

                list.add(entity);
            } else {//主键id为空或null，表明用于新增，前端需将id置为空传参
                String expressageInfoId = UUIDHelper.NewID();
                //判断报告是否已发放
                List<String> reportIds = entity.getExpressageInfo2Report().stream().map(DtoExpressageInfo2Report::getReportId).collect(Collectors.toList());
                List<String> reportCodes = reportService.findAll(reportIds).stream().filter(p -> p.getGrantStatus() == 2 || p.getGrantStatus() == 3).map(DtoReport::getCode).collect(Collectors.toList());
                hasExpressReport.addAll(reportCodes);
                if (StringUtil.isNotEmpty(reportCodes)) {
                    continue;
                }
                entity.setId(expressageInfoId);
                super.save(entity);
                for (DtoExpressageInfo2Report expressageInfo2Report : entity.getExpressageInfo2Report()) {
                    expressageInfo2Report.setExpressageInfoId(expressageInfoId);
                    expressageInfo2Reports.add(expressageInfo2Report);
                }
                list.add(entity);
            }
        }
        if (StringUtil.isNotEmpty(hasExpressReport.stream().distinct().collect(Collectors.toList()))) {
            throw new BaseException("报告" + hasExpressReport + "已发放/回收，不可重复发放");
        }
        if (StringUtil.isNotEmpty(hasRecoverReports.stream().distinct().collect(Collectors.toList()))) {
            throw new BaseException("报告" + hasRecoverReports + "已发放，不可修改或删除");
        }

        //用于更新的快递，需先删除快递报告关联信息
        List<DtoExpressageInfo2Report> oldList = new ArrayList<>();
        if (idList.size() > 0) {
            oldList = expressageInfo2ReportRepository.findByExpressageInfoIdIn(idList);
        }
        List<String> oldReportIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(oldList)) {
            oldReportIds = oldList.stream().map(DtoExpressageInfo2Report::getReportId).collect(Collectors.toList());
        }
        List<String> newReportIds = expressageInfo2Reports.stream().map(DtoExpressageInfo2Report::getReportId).collect(Collectors.toList());

        if (expressageInfo2Reports.size() > 0) {
            //region 刘庄卓添加（2022/4/14） 添加快递信息时修改报告关联状态
            List<String> updateReportIds = oldReportIds.stream().filter(p -> !newReportIds.contains(p)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(updateReportIds)) {
                newLogService.createReportGrantInfoLog(updateReportIds, "删除发放");
                reportRepository.updateGrantStatus(updateReportIds, 1, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }
            //判断是否有回收的报告，如果有则不更改回收的报告状态
            List<String> reportIds = expressageInfo2Reports.stream().map(ExpressageInfo2Report::getReportId).collect(Collectors.toList());
            List<DtoReport> updateReports = reportService.findAll(reportIds);
            reportIds = updateReports.stream().filter(p -> p.getGrantStatus() != 3).map(DtoReport::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(reportIds)) {
                newLogService.createReportGrantInfoLog(reportIds, "新增发放");
                reportRepository.updateGrantStatus(reportIds, 2, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }

            //endregion
            if (StringUtil.isEmpty(oldList)) {
                expressageInfo2ReportService.save(expressageInfo2Reports);
            } else {
                if (!compareList(oldReportIds, newReportIds)) {
                    expressageInfo2ReportRepository.deleteByExpressageInfoIdIn(idList);
                    expressageInfo2ReportService.save(expressageInfo2Reports);
                }
            }

        }
        return list;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        List<String> idList = new ArrayList<>();
        idList.add(idStr);
        //region 刘庄卓添加（2022/4/14） 删除时修改报告状态
        List<DtoExpressageInfo2Report> list = expressageInfo2ReportRepository.findByExpressageInfoIdIn(idList);
        List<String> reportIds = list.stream().map(DtoExpressageInfo2Report::getReportId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(reportIds)) {
            List<DtoReport> updateReports = reportService.findAll(reportIds);
            List<String> canNotDelete = updateReports.stream().filter(p -> p.getGrantStatus() == 3).map(DtoReport::getCode).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(canNotDelete)) {
                throw new BaseException("报告：" + canNotDelete + "已经回收，请先删除回收信息");
            }
            reportRepository.updateGrantStatus(reportIds, 1, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        newLogService.createReportGrantInfoLog(reportIds, "删除发放");
        //endregion
        //删除快递报告关联信息

        expressageInfo2ReportRepository.deleteByExpressageInfoIdIn(idList);
        //删除快递信息
        return super.logicDeleteById(idStr);
    }

    @Override
    @Transactional
    public DtoExpressageInfo save(DtoExpressageInfo entity) {
//        validate(entity);
        if (StringUtil.isNotEmpty(entity.getReportId())) {
            DtoExpressageInfo2Report e2r = new DtoExpressageInfo2Report();
            e2r.setReportId(entity.getReportId());
            e2r.setExpressageInfoId(entity.getId());
            expressageInfo2ReportRepository.save(e2r);
            List<String> reportIds = Collections.singletonList(entity.getReportId());
            newLogService.createReportGrantInfoLog(reportIds, "新增发放");
            reportRepository.updateGrantStatus(reportIds, 2, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoExpressageInfo update(DtoExpressageInfo entity) {
//        validate(entity);
        if(Boolean.TRUE.equals(entity.getIsClear())){
            DtoReport report = reportRepository.findOne(entity.getReportId());
            if(report!=null){
                report.setGrantStatus(EnumPRO.EnumReportGrantStatus.未发放.getValue());
                reportRepository.save(report);
            }
        }
        return super.update(entity);
    }


    /**
     * 批量新增报告发放
     *
     * @param entity 项目快递信息实体
     * @return 项目快递信息实体
     */
    @Override
    @Transactional
    public DtoExpressageInfo batch(DtoExpressageInfo entity) {
        if (StringUtil.isNotEmpty(entity.getReportIds())) {
            List<DtoExpressageInfo2Report> expressageInfo2Reports = new ArrayList<>();
            List<DtoExpressageInfo> expressageInfos = new ArrayList<>();
            for (String reportId : entity.getReportIds()) {
                // 发放记录
                DtoExpressageInfo expressageInfo = new DtoExpressageInfo();
                BeanUtils.copyProperties(entity, expressageInfo, "id");
                expressageInfo.setReportId(reportId);
                expressageInfos.add(expressageInfo);
                // 报告快递关联
                DtoExpressageInfo2Report e2r = new DtoExpressageInfo2Report();
                e2r.setReportId(reportId);
                e2r.setExpressageInfoId(expressageInfo.getId());
                expressageInfo2Reports.add(e2r);
            }
            repository.save(expressageInfos);
            expressageInfo2ReportRepository.save(expressageInfo2Reports);
            reportRepository.updateGrantStatus(entity.getReportIds(), 2, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            newLogService.createReportGrantInfoLog(entity.getReportIds(), "新增发放");
        }
        return entity;
    }


    /**
     * 通用校验
     */
    private void validate(DtoExpressageInfo entity) {
        if (StringUtil.isNotEmpty(entity.getExpressNumber())) {
            Integer repeat = repository.countByExpressNumberAndIdIsNot(entity.getExpressNumber(), entity.getId());
            if (repeat > 0) {
                throw new BaseException("存在相同的快递单号，请核查");
            }
        }
    }

    /**
     * 比较两个Sting集合是否相同
     *
     * @param list1 集合1
     * @param list2 集合2
     * @return 是否相同
     */
    private Boolean compareList(List<String> list1, List<String> list2) {
        if (list1.size() != list2.size()) {
            return false;
        }
        Collections.sort(list1);
        Collections.sort(list2);
        for (int i = 0; i < list1.size(); i++) {
            if (!list1.get(i).equals(list2.get(i))) {
                return false;
            }
        }
        return true;
    }
}
