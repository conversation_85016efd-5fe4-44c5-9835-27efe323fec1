package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.QualityStandard;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.InstrumentUseRecordRepository;
import com.sinoyd.lims.lim.repository.lims.PersonAbilityRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.criteria.QualityControlEvaluateCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoQualityControlEvaluate;
import com.sinoyd.lims.pro.dto.customer.DtoQualityRemind;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbsSubmitRestrictStrategy {

    protected SampleService sampleService;

    protected ParamsConfigService paramsConfigService;

    protected AnalyseDataService analyseDataService;

    protected ReceiveSampleRecordService receiveSampleRecordService;

    protected DocumentService documentService;

    protected PersonRepository personRepository;

    protected QualityControlEvaluateService qualityControlEvaluateService;

    protected QualityRemindService qualityRemindService;

    protected InstrumentUseRecordService instrumentUseRecordService;

    protected ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    protected ParamsDataService paramsDataService;

    protected ReceiveSubSampleRecordService receiveSubSampleRecordService;

    protected WorkSheetFolderService workSheetFolderService;

    protected ItemRelationParamsService itemRelationParamsService;

    protected ItemRelationService itemRelationService;

    protected ProjectService projectService;

    protected ProjectTypeService projectTypeService;

    protected PersonAbilityService personAbilityService;

    protected ReportDetailService reportDetailService;

    protected ReportService reportService;

    protected TestService testService;

    protected IConfigService configService;

    protected AnalyseOriginalRecordService analyseOriginalRecordService;

    protected CodeService codeService;

    protected TestQCRemindConfig2TestService testQCRemindConfig2TestService;

    protected InstrumentUseRecordRepository instrumentUseRecordRepository;

    protected ProjectPlanService projectPlanService;

    protected SamplingPersonConfigRepository samplingPersonConfigRepository;

    protected PersonAbilityRepository personAbilityRepository;

    protected SampleTypeRepository sampleTypeRepository;

    protected SampleGroupRepository sampleGroupRepository;

    protected SampleGroup2TestRepository sampleGroup2TestRepository;

    protected ProjectContractRepository projectContractRepository;


    @Autowired
    protected FilePathConfig filePathConfig;

    /**
     * 提交判断
     *
     * @param objMap 参数
     * @param status 状态
     * @return 提交判断
     */
    public abstract List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status);

    /**
     * 根据项目获取数据集合
     *
     * @param projectId 项目id
     * @return 返回验证结果
     */
    protected List<DtoAnalyseData> findByProjectId(String projectId) {
        return sampleService.findByProjectId(projectId);
    }

    /**
     * 根据送样单获取数据集合
     *
     * @param receiveId 送样单id
     * @return 返回验证结果
     */
    protected List<DtoAnalyseData> findByReceiveId(String receiveId) {
        return sampleService.findDataByReceiveId(receiveId);
    }

    /**
     * 是否存在表单
     *
     * @param folderId  附件关联Id
     * @param docTypeId 类型id
     * @return 表单集合
     */
    protected List<DtoDocument> checkDocument(String folderId, String docTypeId) {
        return documentService.findByObjectId(folderId).stream()
                .filter(p -> docTypeId.equals(p.getDocTypeId()) && !p.getIsDeleted()).collect(Collectors.toList());
    }

    /**
     * 提交状态验证
     *
     * @param status       提交状态
     * @param realStatus   验证状态
     * @param restrictItem 验证枚举
     * @return 返回验证结果
     */
    protected DtoSubmitRestrictVo checkStatus(String status, String realStatus, EnumPRO.EnumRestrictItem restrictItem) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(restrictItem.getCheckItem());
        restrictVo.setModuleName(restrictItem.getModuleName());
        if (!status.contains(realStatus)) {
            restrictVo.setExceptionOption("当前任务已经提交，请确认需要提交的任务");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否上传签名
     *
     * @param personIds    人员ids
     * @param restrictItem 验证枚举
     * @return 返回验证结果
     */
    protected DtoSubmitRestrictVo checkSigUrl(List<String> personIds, EnumPRO.EnumRestrictItem restrictItem) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(restrictItem.getCheckItem());
        restrictVo.setModuleName(restrictItem.getModuleName());
        if (personIds.size() > 0) {
            List<DtoPerson> personList = personRepository.findAll(personIds);
            List<DtoPerson> signatureList = personList.stream().filter(p -> !StringUtil.isNotEmpty(p.getSignature())).collect(Collectors.toList());
            if (signatureList.size() > 0) {
                restrictVo.setExceptionOption("未上传电子签名");
                restrictVo.setIsUnusual(Boolean.FALSE);
            } else {
                for (DtoPerson person : personList) {
                    if (!restrictVo.getIsUnusual()) {
                        break;
                    }
                    try {
                        InputStream stream = new FileInputStream(filePathConfig.getFilePath() + person.getSignature());
                    } catch (FileNotFoundException e) {
                        restrictVo.setExceptionOption(String.format("未找到%s上传的电子签名", person.getCName()));
                        restrictVo.setIsUnusual(Boolean.FALSE);
                    }
                }
            }
        }
        return restrictVo;
    }

    /**
     * 质控评价信息
     *
     * @param objectId        质控id
     * @param restrictItem    验证枚举
     * @param testList        测试项目集合
     * @param submitItem      验证枚举
     * @param analyseDataList 数据集合
     * @return 返回验证结果
     */
    protected List<DtoSubmitRestrictVo> checkControlEvaluate(String objectId, EnumPRO.EnumRestrictItem restrictItem,
                                                             List<DtoTest> testList, EnumPRO.EnumRestrictItem submitItem,
                                                             List<DtoAnalyseData> analyseDataList) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        DtoSubmitRestrictVo submitRestrictVo = new DtoSubmitRestrictVo();
        PageBean<DtoQualityControlEvaluate> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        QualityControlEvaluateCriteria qualityControlEvaluateCriteria = new QualityControlEvaluateCriteria();
        qualityControlEvaluateCriteria.setWorkSheetFolderId(objectId);
        qualityControlEvaluateService.findByPage(pageBean, qualityControlEvaluateCriteria);
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = pageBean.getData();
        Set<String> testIds = testList.stream().map(DtoTest::getParentId)
                .filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toSet());
        //总称
        if (testIds.size() > 0) {
            List<DtoTest> parentList = testService.findAll(testIds);
            analyseDataList.forEach(p -> {
                Optional<DtoTest> testOptional = testList.stream().filter(t -> t.getId().equals(p.getTestId())).findFirst();
                if (testOptional.isPresent()) {
                    if (!UUIDHelper.GUID_EMPTY.equals(testOptional.get().getParentId())) {
                        Optional<DtoTest> parentOptional = parentList.stream()
                                .filter(t -> t.getId().equals(testOptional.get().getParentId())).findFirst();
                        parentOptional.ifPresent(test -> p.setRedAnalyzeItemName(test.getRedAnalyzeItemName()));
                    }
                }
            });
        }
        List<String> msgList = new ArrayList<>();
        List<String> itemList = new ArrayList<>();
        submitRestrictVo.setCheckItem(submitItem.getCheckItem());
        submitRestrictVo.setModuleName(submitItem.getModuleName());
        if (StringUtil.isNotNull(qualityControlEvaluateList) && qualityControlEvaluateList.size() > 0) {
            qualityControlEvaluateList.forEach(p -> {
                if (StringUtil.isNotEmpty(p.getQcMessage()) && "不合格".equals(p.getQcMessage())) {
                    String msg = String.format("%s：%s%s(%s)不满足%s的要求", p.getSampleCode(), p.getRedAnalyzeItemName(),
                            p.getJudgingMethodName(), p.getCheckItemValue(), p.getAllowLimit());
                    msgList.add(msg);
                }
                //排除标样
                if (UUIDHelper.GUID_EMPTY.equals(p.getLimitId())
                        && !new QualityStandard().qcTypeValue().equals(p.getQcType())) {
                    Optional<DtoAnalyseData> analyseDataOptional = analyseDataList.stream()
                            .filter(a -> p.getObjectId().equals(a.getId())).findFirst();
                    String itemName = p.getRedAnalyzeItemName();
                    if (analyseDataOptional.isPresent()) {
                        itemName = analyseDataOptional.get().getRedAnalyzeItemName();
                    }
                    String itemMsg = String.format("%s,%s", itemName, p.getQcTypeName());
                    itemList.add(itemMsg);
                }
                // 统计无法计算的质控
                if (p.getCheckItemValue().contains("无法计算")){
                    String msg = String.format("%s：%s%s无法计算", p.getSampleCode(), p.getRedAnalyzeItemName(),
                            p.getJudgingMethodName());
                    msgList.add(msg);
                }
            });
        }
        //存在评定的质控信息
        if (StringUtil.isNotNull(qualityControlEvaluateList)) {
            if (itemList.size() != qualityControlEvaluateList.size()) {
                restrictVo.setCheckItem(restrictItem.getCheckItem());
                restrictVo.setModuleName(restrictItem.getModuleName());
                //质控评价配置
                restrictVoList.add(restrictVo);
            }
        }
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        List<String> subMsgList = new ArrayList<>();
        if (itemList.size() > 0) {
            Map<String, List<String>> mapList = new HashMap<>();
            itemList.forEach(p -> {
                String keyName = p.split(",")[0];
                String valueName = p.split(",")[1];
                if (mapList.containsKey(keyName)) {
                    if (!mapList.get(keyName).contains(valueName)) {
                        mapList.get(keyName).add(valueName);
                    }
                } else {
                    List<String> valueList = new ArrayList<>();
                    valueList.add(valueName);
                    mapList.put(keyName, valueList);
                }
            });
            for (String keyName : mapList.keySet()) {
                String mstr = String.format("%s（%s）未配置质控限值或无相关合格要求", keyName,
                        String.join("、", mapList.get(keyName)));
                subMsgList.add(mstr);
            }
            submitRestrictVo.setExceptionOption(String.join(";", subMsgList));
            submitRestrictVo.setIsUnusual(Boolean.FALSE);
        } else {
            submitRestrictVo.setExceptionOption("未予以判定");
        }
        if (StringUtil.isNotNull(qualityControlEvaluateList) && qualityControlEvaluateList.size() > 0) {
            restrictVoList.add(submitRestrictVo);
        }
        return restrictVoList;
    }

    /**
     * 质控比例配置
     *
     * @param config2TestList 质控比例配置
     * @param testList        测试项目集合
     * @param restrictItem    item类型
     * @return 返回验证结果
     */
    protected DtoSubmitRestrictVo checkRemindConfig(List<DtoTestQCRemindTemp> config2TestList,
                                                    List<DtoTest> testList, EnumPRO.EnumRestrictItem restrictItem) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        List<String> configTestIds = config2TestList.stream().map(DtoTestQCRemindTemp::getTestId).collect(Collectors.toList());
        Set<String> parentIds = testList.stream().map(DtoTest::getParentId)
                .filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toSet());
        if (parentIds.size() > 0) {
            List<DtoTest> parentTestList = testService.findAll(parentIds);
            testList.addAll(parentTestList);
        }
        //没有总称的测试项目
        testList = testList.stream().filter(p -> !configTestIds.contains(p.getId())
                && UUIDHelper.GUID_EMPTY.equals(p.getParentId())
                && configTestIds.contains(p.getParentId())).collect(Collectors.toList());

        List<String> itemNameList = testList.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList());
        restrictVo.setCheckItem(restrictItem.getCheckItem());
        restrictVo.setModuleName(restrictItem.getModuleName());
        restrictVo.setExceptionOption("未予以判定");
        if (itemNameList.size() > 0) {
            restrictVo.setExceptionOption(String.format("%s质控比例要求未配置或无相关比例要求，未予判定。",
                    String.join("、", itemNameList)));
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 质控比例提醒
     *
     * @param recordId 送样单id
     * @return 返回验证结果
     */
    protected DtoSubmitRestrictVo checkRemind(String recordId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        List<DtoQualityRemind> remindList = qualityRemindService.findByReceiveIds(Collections.singletonList(recordId));
        Map<String, List<DtoQualityRemind>> remindMap = remindList.stream().collect(Collectors.groupingBy(DtoQualityRemind::getRedAnalyzeItemName));
        List<String> msgList = new ArrayList<>();
        remindMap.forEach((k, v) -> {
            List<String> itemMsg = v.stream().map(p -> String.format("%s质控比例未满足%d%s", QualityTaskFactory.getInstance().getQcSample(p.getQcType())
                    .getSampleProperty(EnumLIM.EnumQCGrade.外部质控.getValue()), p.getQcRemindPercent(), "%")).collect(Collectors.toList());
            String msg = String.join(";", itemMsg);
            msgList.add(String.format("%s：%s", k, msg));
        });
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.现场质控比例判定.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.现场质控比例判定.getModuleName());
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    protected List<DtoTestQCRemindTemp> config2TestByDataList(List<DtoAnalyseData> analyseDataList){
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findAll(testIds);
        //总称测试项目
        Set<String> parentIds = testList.stream().map(DtoTest::getParentId)
                .filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toSet());
        if (parentIds.size() > 0) {
            testIds.addAll(parentIds);
        }
        List<DtoTestQCRemindTemp> config2TestList = testQCRemindConfig2TestService.findByTestIds(testIds);
        //质控比例 -ok
        config2TestList = config2TestList.stream().filter(p ->
                EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
         return config2TestList;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    @Lazy
    public void setParamsConfigService(ParamsConfigService paramsConfigService) {
        this.paramsConfigService = paramsConfigService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setReceiveSampleRecordService(ReceiveSampleRecordService receiveSampleRecordService) {
        this.receiveSampleRecordService = receiveSampleRecordService;
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    @Lazy
    public void setQualityRemindService(QualityRemindService qualityRemindService) {
        this.qualityRemindService = qualityRemindService;
    }

    @Autowired
    @Lazy
    public void setQualityControlEvaluateService(QualityControlEvaluateService qualityControlEvaluateService) {
        this.qualityControlEvaluateService = qualityControlEvaluateService;
    }

    @Autowired
    @Lazy
    public void setInstrumentUseRecordService(InstrumentUseRecordService instrumentUseRecordService) {
        this.instrumentUseRecordService = instrumentUseRecordService;
    }

    @Autowired
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }

    @Autowired
    @Lazy
    public void setParamsDataService(ParamsDataService paramsDataService) {
        this.paramsDataService = paramsDataService;
    }

    @Autowired
    @Lazy
    public void setReceiveSubSampleRecordService(ReceiveSubSampleRecordService receiveSubSampleRecordService) {
        this.receiveSubSampleRecordService = receiveSubSampleRecordService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }

    @Autowired
    @Lazy
    public void setItemRelationParamsService(ItemRelationParamsService itemRelationParamsService) {
        this.itemRelationParamsService = itemRelationParamsService;
    }

    @Autowired
    @Lazy
    public void setItemRelationService(ItemRelationService itemRelationService) {
        this.itemRelationService = itemRelationService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    @Lazy
    public void setProjectTypeService(ProjectTypeService projectTypeService) {
        this.projectTypeService = projectTypeService;
    }

    @Autowired
    @Lazy
    public void setPersonAbilityService(PersonAbilityService personAbilityService) {
        this.personAbilityService = personAbilityService;
    }

    @Autowired
    @Lazy
    public void setReportDetailService(ReportDetailService reportDetailService) {
        this.reportDetailService = reportDetailService;
    }

    @Autowired
    @Lazy
    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    @Lazy
    public void setAnalyseOriginalRecordService(AnalyseOriginalRecordService analyseOriginalRecordService) {
        this.analyseOriginalRecordService = analyseOriginalRecordService;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setTestQCRemindConfig2TestService(TestQCRemindConfig2TestService testQCRemindConfig2TestService) {
        this.testQCRemindConfig2TestService = testQCRemindConfig2TestService;
    }

    @Autowired
    @Lazy
    public void setInstrumentUseRecordRepository(InstrumentUseRecordRepository instrumentUseRecordRepository) {
        this.instrumentUseRecordRepository = instrumentUseRecordRepository;
    }

    @Autowired
    @Lazy
    public void setProjectPlanService(ProjectPlanService projectPlanService) {
        this.projectPlanService = projectPlanService;
    }

    @Autowired
    @Lazy
    public void setSamplingPersonConfigRepository(SamplingPersonConfigRepository samplingPersonConfigRepository) {
        this.samplingPersonConfigRepository = samplingPersonConfigRepository;
    }

    @Autowired
    @Lazy
    public void setPersonAbilityRepository(PersonAbilityRepository personAbilityRepository) {
        this.personAbilityRepository = personAbilityRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setSampleGroupRepository(SampleGroupRepository sampleGroupRepository) {
        this.sampleGroupRepository = sampleGroupRepository;
    }

    @Autowired
    @Lazy
    public void setSampleGroup2TestRepository(SampleGroup2TestRepository sampleGroup2TestRepository) {
        this.sampleGroup2TestRepository = sampleGroup2TestRepository;
    }

    @Autowired
    @Lazy
    public void setProjectContractRepository(ProjectContractRepository projectContractRepository) {
        this.projectContractRepository = projectContractRepository;
    }
}
