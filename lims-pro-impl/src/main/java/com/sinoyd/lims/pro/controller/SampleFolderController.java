package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.criteria.SampleFolderCriteria;
import com.sinoyd.lims.pro.dto.DtoChangeMethodSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.dto.customer.DtoMethodStandardSample;
import com.sinoyd.lims.pro.service.FolderExtendService;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import com.sinoyd.lims.pro.vo.TestShMethodUpdateBatchVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;


/**
 * SampleFolder服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: SampleFolder服务")
 @RestController
 @RequestMapping("api/pro/sampleFolder")
 public class SampleFolderController extends BaseJpaController<DtoSampleFolder, String,SampleFolderService> {

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    @Lazy
    private FolderExtendService folderExtendService;

    /**
     * 分页动态条件查询样品
     *
     * @param sampleFolderCriteria 条件参数
     * @return RestResponse<List < DtoSampleFolder>>
     */
    @ApiOperation(value = "分页动态条件查询点位", notes = "分页动态条件查询点位")
    @GetMapping
    public RestResponse<List<DtoSampleFolder>> findByPage(SampleFolderCriteria sampleFolderCriteria) {
        PageBean<DtoSampleFolder> pageBean = super.getPageBean();
        RestResponse<List<DtoSampleFolder>> restResponse = new RestResponse<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        service.findByPage(pageBean, sampleFolderCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 返回点位树
     *
     * @return 点位树
     */
    @ApiOperation(value = "获取点位树结构", notes = "获取点位树结构")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> tree(@RequestParam(name = "projectId") String projectId, @RequestParam(name = "sampleTypeId") String sampleTypeId) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setData(service.getSampleFolderTree(projectId, sampleTypeId));

        return restResponse;
    }

    /**
     * 按主键查询SampleFolder
     *
     * @param id 主键id
     * @return RestResponse<DtoSampleFolder>
     */
    @ApiOperation(value = "按主键查询SampleFolder", notes = "按主键查询SampleFolder")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoSampleFolder> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleFolder> restResponse = new RestResponse<>();
        DtoSampleFolder sampleFolder = service.findOne(id);
        restResponse.setData(sampleFolder);
        restResponse.setRestStatus(StringUtil.isNull(sampleFolder) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "按主键查询点位详情", notes = "按主键查询点位详情")
    @GetMapping(path = "/detail/{id}")
    public RestResponse<DtoSampleFolderTemp> findDetail(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleFolderTemp> restResponse = new RestResponse<>();
        DtoSampleFolderTemp sampleFolder = service.findDetail(id);
        restResponse.setData(sampleFolder);
        restResponse.setRestStatus(StringUtil.isNull(sampleFolder) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增点位
     *
     * @param sampleFolder 实体列表
     * @return RestResponse<DtoSampleFolder>
     */
    @ApiOperation(value = "新增点位", notes = "新增点位")
    @PostMapping("/folderTemp")
    public RestResponse<DtoSampleFolderTemp> create(@RequestBody @Validated DtoSampleFolder sampleFolder) {
        RestResponse<DtoSampleFolderTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.addFolder(sampleFolder));
        return restResponse;
    }

    /**
     * 新增点位
     *
     * @param sampleFolder 实体列表
     * @return RestResponse<DtoSampleFolder>
     */
    @ApiOperation(value = "新增点位", notes = "新增点位")
    @PostMapping
    public RestResponse<DtoSampleFolderTemp> createFolder(@RequestBody @Validated DtoSampleFolder sampleFolder) {
        RestResponse<DtoSampleFolderTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.addFolderTemp(sampleFolder));
        return restResponse;
    }

    /**
     * 修改点位
     *
     * @param sampleFolder 实体列表
     * @return RestResponse<DtoSampleFolder>
     */
    @ApiOperation(value = "修改点位", notes = "修改点位")
    @PutMapping
    public RestResponse<DtoSampleFolder> update(@RequestBody @Validated DtoSampleFolder sampleFolder) {
        RestResponse<DtoSampleFolder> restResponse = new RestResponse<>();
        restResponse.setData(service.update(sampleFolder));
        return restResponse;
    }

    /**
     * 根据id删除点位
     *
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除点位", notes = "根据id删除点位")
    @DeleteMapping(path = "/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 返回项目方案列表
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param isLoad       是否加载
     * @return 方案
     */
    @ApiOperation(value = "获取项目方案列表", notes = "获取项目方案列表")
    @GetMapping("/scheme")
    public RestResponse<DtoProjectScheme> scheme(@RequestParam(name = "projectId") String projectId,
                                                 @RequestParam(name = "sampleTypeId") String sampleTypeId,
                                                 @RequestParam(name = "isLoad") Boolean isLoad) {
        RestResponse<DtoProjectScheme> restResponse = new RestResponse<>();
        restResponse.setData(service.findScheme(projectId, sampleTypeId, isLoad, null, null, false));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 复制点位
     *
     * @param dto 实体
     * @return 点位方案
     */
    @ApiOperation(value = "复制点位", notes = "复制点位")
    @PostMapping("/copy")
    public RestResponse<List<DtoSampleFolderTemp>> copyFolders(@RequestBody DtoComplexQuery dto) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.copyFolders(dto.getIds(), dto.getTimes(), false);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 新增点位周期频次
     *
     * @param dto 实体
     * @return 点位周期频次
     */
    @ApiOperation(value = "新增点位周期频次", notes = "新增点位周期频次")
    @PostMapping("/periodTime")
    public RestResponse<List<DtoSampleFolderTemp>> addPeriodTimes(@RequestBody DtoSamplingFrequency dto) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.addPeriodTimes(dto.getSampleFolderId(), dto.getPeriodCount(),
                dto.getTimePerPeriod(), dto.getSamplePerTime());
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 新增次数
     *
     * @param dto 实体
     * @return 点位次数
     */
    @ApiOperation(value = "新增次数", notes = "新增次数")
    @PostMapping("/time")
    public RestResponse<List<DtoSampleFolderTemp>> addTimes(@RequestBody DtoSamplingFrequency dto) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.addTimes(dto.getSampleFolderId(), dto.getPeriodCount(),
                dto.getTimePerPeriod(), dto.getSamplePerTime());
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 新增样品数
     *
     * @param dto 实体
     * @return 点位样品数
     */
    @ApiOperation(value = "新增样品数", notes = "新增样品数")
    @PostMapping("/samplePeriod")
    public RestResponse<List<DtoSampleFolderTemp>> addSamplePeriods(@RequestBody DtoSamplingFrequency dto) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.addSamplePeriods(dto.getSampleFolderId(), dto.getPeriodCount(),
                dto.getTimePerPeriod(), dto.getSamplePerTime());
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 复制周期
     *
     * @param dto 实体
     * @return 周期
     */
    @ApiOperation(value = "复制周期", notes = "复制周期")
    @PostMapping("/copyPeriod/{copyTimes}")
    public RestResponse<List<DtoSampleFolderTemp>> copyPeriod(@RequestBody DtoSamplingFrequency dto, @PathVariable(name = "copyTimes") Integer copyTimes) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.copyPeriod(dto.getSampleFolderId(), dto.getPeriodCount(), copyTimes);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 复制批次
     *
     * @param dto 实体
     * @return 周期
     */
    @ApiOperation(value = "复制批次", notes = "复制批次")
    @PostMapping("/copyTimePeriod/{copyTimes}")
    public RestResponse<List<DtoSampleFolderTemp>> copyTimePeriod(@RequestBody DtoSamplingFrequency dto, @PathVariable(name = "copyTimes") Integer copyTimes) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.copyTimePeriod(dto.getSampleFolderId(), dto.getPeriodCount(), dto.getTimePerPeriod(), copyTimes);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 删除周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "删除周期", notes = "删除周期")
    @DeleteMapping(path = "/period/{sampleFolderId}/{periodCount}")
    public RestResponse<Boolean> deletePeriod(@PathVariable(name = "sampleFolderId") String sampleFolderId,
                                              @PathVariable(name = "periodCount") Integer periodCount) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.deletePeriod(sampleFolderId, periodCount);
        restResponse.setData(true);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 删除批次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "删除批次", notes = "删除批次")
    @DeleteMapping(path = "/timePeriod/{sampleFolderId}/{periodCount}/{timePeriod}")
    public RestResponse<Boolean> deleteTimePeriod(@PathVariable(name = "sampleFolderId") String sampleFolderId,
                                                  @PathVariable(name = "periodCount") Integer periodCount,
                                                  @PathVariable(name = "timePeriod") Integer timePeriod) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.deleteTimePeriod(sampleFolderId, periodCount, timePeriod);
        restResponse.setData(true);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 复制次数
     *
     * @param dto 实体
     * @return 周期
     */
    @ApiOperation(value = "复制次数", notes = "复制次数")
    @PostMapping("/copyTime/{copyTimes}")
    public RestResponse<List<DtoSampleFolderTemp>> copyTimes(@RequestBody DtoSamplingFrequencyTest dto,
                                                             @PathVariable(name = "copyTimes") Integer copyTimes) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.copyTimes(dto.getSampleFolderId(), dto.getSamplingFrequencyId(), copyTimes);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 删除样品数
     *
     * @param samplingFrequencyId 频次id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "删除样品数", notes = "删除样品数")
    @DeleteMapping(path = "/time/{samplingFrequencyId}")
    public RestResponse<Boolean> deleteTimes(@PathVariable(name = "samplingFrequencyId") String samplingFrequencyId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        List<String> worksheetfolderIds = service.getWorkSheetIdsBySamplingFrequencyId(Collections.singletonList(samplingFrequencyId));
        service.deleteTimes(samplingFrequencyId);
        workSheetFolderService.checkWorkSheetFolder(worksheetfolderIds);
        restResponse.setData(true);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 批量删除周期次数
     *
     * @param ids 频次id集合
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "批量删除周期次数样品数", notes = "批量删除周期次数样品数")
    @DeleteMapping(path = "/time")
    public RestResponse<Boolean> deleteTimes(@RequestBody List<String> ids) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        List<String> worksheetfolderIds = service.getWorkSheetIdsBySamplingFrequencyId(ids);
        service.deleteTimes(ids);
        workSheetFolderService.checkWorkSheetFolder(worksheetfolderIds);
        restResponse.setData(true);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 修改方法
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "修改方法", notes = "修改方法")
    @PutMapping(path = "/analyzeMethod")
    public RestResponse<List<DtoProjectTest>> changeAnalyzeMethod(@RequestBody DtoSchemeMethodChange dto) {
        RestResponse<List<DtoProjectTest>> restResponse = new RestResponse<>();
        List<DtoProjectTest> datas = service.changeAnalyzeMethod(dto);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 根据样品修改方法
     *
     * @param dto 实体s
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "根据样品修改方法", notes = "根据样品修改方法")
    @PutMapping(path = "/analyzeMethodSample")
    public RestResponse<List<DtoProjectTest>> changeSampleAnalyzeMethod(@RequestBody DtoChangeMethodSample dto) {
        RestResponse<List<DtoProjectTest>> restResponse = new RestResponse<>();
        List<DtoProjectTest> data = service.changeAnalyzeMethodBySample(dto.getSampleIds(), dto.getTestId(), dto.getNewAnalyzeMethodId(), dto.getIsChangeAll());
        restResponse.setData(data);
        restResponse.setRestStatus(StringUtil.isNull(data) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(data) ? 0 : data.size());
        return restResponse;
    }

    /**
     * 设置分包
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "设置分包", notes = "设置分包")
    @PutMapping(path = "/sub")
    public RestResponse<Boolean> sub(@RequestBody DtoProjectScheme dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.sub(dto.getSamplingFrequencyIds(), dto.getTest());
        restResponse.setData(true);
        return restResponse;
    }

    //region 刘庄卓新增

    /**
     * 获取关联样品
     *
     * @param testId       测试项目编号
     * @param projectId    项目编号
     * @param sampleTypeId 样品类型编号
     * @param isLoad       是否缓存
     * @return 样品列表
     */
    @ApiOperation(value = "获取关联样品", notes = "获取关联样品")
    @GetMapping(path = "/getSamples")
    public RestResponse<List<DtoMethodStandardSample>> getSamples(@RequestParam(name = "testId") String testId,
                                                                  @RequestParam(name = "projectId") String projectId,
                                                                  @RequestParam(name = "sampleTypeId") String sampleTypeId,
                                                                  @RequestParam(name = "isLoad") Boolean isLoad) {
        RestResponse<List<DtoMethodStandardSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSample(testId, projectId, sampleTypeId, isLoad));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 返回方法标准列表
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param isLoad       是否加载
     * @return 方法标准
     */
    @ApiOperation(value = "获取方法标准列表", notes = "获取方法标准列表")
    @GetMapping("/methodStandard")
    public RestResponse<List<DtoProjectTest>> getMethodStandard(@RequestParam(name = "projectId") String projectId,
                                                                @RequestParam(name = "sampleTypeId") String sampleTypeId,
                                                                @RequestParam(name = "redAnalyzeItemName") String redAnalyzeItemName,
                                                                @RequestParam(name = "redAnalyzeMethodName") String redAnalyzeMethodName,
                                                                @RequestParam(name = "isLoad") Boolean isLoad) {
        RestResponse<List<DtoProjectTest>> restResponse = new RestResponse<>();
        restResponse.setData(service.findMethodStandard(projectId, sampleTypeId, redAnalyzeItemName, redAnalyzeMethodName, isLoad));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @GetMapping("/getByReceiveId")
    public RestResponse<List<DtoSampleFolder>> getByReceiveId(@RequestParam(name = "receiveId") String receiveId) {
        RestResponse<List<DtoSampleFolder>> response = new RestResponse<>();
        response.setData(service.getByReceiveId(receiveId));
        return response;
    }

    @GetMapping(path = "/checkFolderName")
    public RestResponse<Boolean> checkSampleCode() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.checkSampleCode();
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }
    //endregion

    /**
     * 按点位录入(新）
     * BUG2024030199945  【重要】【2024-03-02】【潘长城】【现场任务】现场任务-样品信息-按点位录入改造，原本左侧排列点位，现在将点位完全平铺，将一个点位的样品放在一个卡片区域（注意质控样），公共参数还是在最上面，点位参数跟随每个选项卡，下面样品参数、分析项目参数及其他固定列参考‘按样品录入’表格形式，参数直接放在列上，注：排序等机制都参考‘按样品录入’表格。注意每个区块增加收缩功能，并且支持全部收缩和全部展开，参考方案编制。
     *
     * @param sampleFolderCriteria 条件参数
     * @return RestResponse<List < DtoSampleFolder>>
     */
    @ApiOperation(value = "按点位录入(新）", notes = "按点位录入(新）")
    @GetMapping("/loadDataBySampleFolder")
    public RestResponse<DtoSampleFolderLoadDataContainer> loadDataBySampleFolder(SampleFolderCriteria sampleFolderCriteria) {
        RestResponse<DtoSampleFolderLoadDataContainer> restResponse = new RestResponse<>();
        PageBean<DtoSampleFolder> pageBean = super.getPageBean();
        restResponse.setData(service.loadDataBySampleFolder(pageBean, sampleFolderCriteria));
        return restResponse;
    }

    @ApiOperation(value = "获取点位列表", notes = "获取点位列表")
    @GetMapping("/getSampleFolders")
    public RestResponse<List<DtoSampleFolder>> findByProjectId(String projectId) {
        RestResponse<List<DtoSampleFolder>> response = new RestResponse<>();
        response.setData(folderExtendService.findFoldersByProjectId(projectId, ""));
        return response;
    }

    @ApiOperation(value = "方案拓展列表", notes = "方案拓展列表")
    @GetMapping("/findBySampleFolderId")
    public RestResponse<List<DtoTest>> find(TestCriteria criteria) {
        RestResponse<List<DtoTest>> response = new RestResponse<>();
        response.setData(folderExtendService.findBySampleFolderId(criteria));
        return response;
    }

    @ApiOperation(value = "批量修改分析方法", notes = "批量修改分析方法")
    @PostMapping("/batchUpdateAnalyzeMethod")
    public RestResponse<Void> batchUpdateAnalyzeMethod(@RequestBody TestShMethodUpdateBatchVO batchVO) {
        RestResponse<Void> response = new RestResponse<>();
        folderExtendService.batchUpdateAnalyzeMethod(batchVO);
        return response;
    }

    @ApiOperation(value = "批量修改采样方法", notes = "批量修改采样方法")
    @PostMapping("/batchUpdateSamplingMethod")
    public RestResponse<Void> batchUpdateSamplingMethod(@RequestBody TestShMethodUpdateBatchVO batchVO) {
        RestResponse<Void> response = new RestResponse<>();
        folderExtendService.batchUpdateSamplingMethod(batchVO);
        return response;
    }
}