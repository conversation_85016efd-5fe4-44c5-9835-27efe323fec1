package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.rcc.EvaluationCriteriaRepository;
import com.sinoyd.base.repository.rcc.EvaluationLevelRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortDetilRepository;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoReportInfo;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.ElectronicReportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.base.core.BaseCodeHelper.DOCUMENT_EXTEND_REPORT;
import static com.sinoyd.base.core.BaseCodeHelper.DOCUMENT_EXTEND_REPORT_UP;


/**
 * 电子报告相关操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
@Service
public class ElectronicReportServiceImpl extends BaseJpaServiceImpl<DtoReportBaseInfo, String, ReportBaseInfoRepository> implements ElectronicReportService {

    private ReportRepository reportRepository;
    private ReportDetailRepository reportDetailRepository;
    private ReportSampleInfoRepository reportSampleInfoRepository;
    private ReportFolderInfoRepository reportFolderInfoRepository;
    private ProjectRepository projectRepository;
    private SampleRepository sampleRepository;
    private AnalyseDataRepository analyseDataRepository;
    private SampleFolderRepository sampleFolderRepository;
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;
    private TestRepository testRepository;
    private EvaluationRecordRepository evaluationRecordRepository;
    private EvaluationCriteriaRepository evaluationCriteriaRepository;
    private EvaluationLevelRepository evaluationLevelRepository;
    private ParamsDataRepository paramsDataRepository;
    private ParamsConfigRepository paramsConfigRepository;
    private OutSourceDataRepository outSourceDataRepository;
    private FixedPointSortDetilRepository fixedPointSortDetilRepository;
    protected DocumentService documentService;
    private DocumentRepository documentRepository;
    protected FilePathConfig filePathConfig;
    private ReportFolderSortInfoRepository reportFolderSortInfoRepository;
    private IConfigService configService;
    private ProjectContractRepository projectContractRepository;


    /**
     * 保存电子报告信息
     *
     * @param reportInfo 对象id
     */
    @Override
    @Transactional
    public void saveReportInfo(DtoReportInfo reportInfo) {
        String reportId = reportInfo.getReportId();
        checkReportExist(reportId);
        //保存报告基础信息
        saveBaseInfo(reportInfo.getReportBaseInfo(), reportId);
        //保存报告点位信息
        saveFolderInfo(reportInfo.getReportFolderInfoList(), reportId);
        //保存报告样品信息
        saveSampleInfo(reportInfo.getReportFolderInfoList(), reportId);
    }

    /**
     * 自动获取电子报告信息
     *
     * @param reportId 报告id
     * @return 电子报告信息
     */
    @Override
    @Transactional
    public DtoReportInfo autoGenerate(String reportId) {
        DtoReportBaseInfo baseInfo = getBaseInfo(reportId);
        List<DtoReportFolderInfo> reportFolderInfoList = getFolderInfoForReport(reportId);
        DtoReportInfo reportInfo = new DtoReportInfo(reportId, baseInfo, reportFolderInfoList, false);
        this.saveReportInfo(reportInfo);
        return reportInfo;
    }

    @Override
    public List<Map<String, String>> getSortFolderForReport(String reportId, String sampleFolderSortId) {
        List<DtoReportFolderSortInfo> folderSortInfoList = reportFolderSortInfoRepository.findByReportId(reportId);
        if (StringUtil.isNotEmpty(folderSortInfoList)) {
            List<String> folderIdList = folderSortInfoList.stream().map(DtoReportFolderSortInfo::getFolderId).filter(StringUtil::isNotEmpty)
                    .distinct().collect(Collectors.toList());
            List<DtoSampleFolder> folderList = StringUtil.isNotEmpty(folderIdList) ? sampleFolderRepository.findAll(folderIdList) : new ArrayList<>();
            Map<String, DtoSampleFolder> folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
            //已存在保存的点位排序数据，则直接获取
            List<Map<String, String>> mapList = new ArrayList<>();
            folderSortInfoList.sort(Comparator.comparing(DtoReportFolderSortInfo::getOrderNum));
            for (DtoReportFolderSortInfo folderSortInfo : folderSortInfoList) {
                Map<String, String> map = new HashMap<>();
                map.put("folderId", folderSortInfo.getFolderId());
                map.put("folderName", folderMap.containsKey(folderSortInfo.getFolderId()) ? folderMap.get(folderSortInfo.getFolderId()).getWatchSpot() : "");
                mapList.add(map);
            }
            return mapList;
        } else {
            //不存在则重新获取
            List<DtoSample> sampleList = getSampleForReport(reportId);
            Map<String, List<DtoSample>> folderId2SampleListMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleFolderId));
            List<String> sampleFolderIdList = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
            List<Map<String, String>> mapList = new ArrayList<>();
            if (StringUtil.isNotEmpty(sampleFolderIdList)) {
//            List<DtoSampleFolder> folderList = sampleFolderRepository.findAll(sampleFolderIdList);
                List<DtoFixedPointSortDetil> sortDetailList = fixedPointSortDetilRepository.findBySortId(sampleFolderSortId);
                Map<String, DtoSampleFolder> fldId2SmpFldMap = sortFldByFixPntSort(sortDetailList, sampleFolderIdList, folderId2SampleListMap);
                for (String folderId : sampleFolderIdList) {
                    Map<String, String> map = new HashMap<>();
                    String watchSpot = fldId2SmpFldMap.containsKey(folderId) ? fldId2SmpFldMap.get(folderId).getWatchSpot() : "";
                    map.put("folderId", folderId);
                    map.put("folderName", watchSpot);
                    mapList.add(map);
                }
            }
            return mapList;
        }
    }

    @Override
    @Transactional
    public void clearReportInfo(String reportId) {
        DtoReportInfo reportInfo = new DtoReportInfo();
        DtoReportBaseInfo baseInfo = repository.findByReportId(reportId);
        if (StringUtil.isNotNull(baseInfo)) {
            baseInfo.setIsDeleted(true);
            repository.save(baseInfo);
        }
        List<DtoReportFolderInfo> folderInfoList = reportFolderInfoRepository.findByReportId(reportId);
        if (StringUtil.isNotEmpty(folderInfoList)) {
            folderInfoList.forEach(p -> p.setIsDeleted(true));
            reportFolderInfoRepository.save(folderInfoList);
        }
        List<DtoReportSampleInfo> sampleInfoList = reportSampleInfoRepository.findByReportId(reportId);
        if (StringUtil.isNotEmpty(sampleInfoList)) {
            sampleInfoList.forEach(p -> p.setIsDeleted(true));
            reportSampleInfoRepository.save(sampleInfoList);
        }
    }

    @Override
    @Transactional
    public List<DtoDocument> uploadFolderSketch(HttpServletRequest request, List<String> allowSuffixList) {
        //删除原有的文档
        String docTypeId = request.getParameter("docTypeId");
        String folderId = request.getParameter("folderId");
        List<DtoDocument> oldDocList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(Collections.singletonList(folderId), docTypeId);
        if (StringUtil.isNotEmpty(oldDocList)) {
            documentRepository.delete(oldDocList);
        }
        List<DtoDocument> documentList = documentService.upload(request, null);
        return documentList;
    }

    @Override
    @Transactional
    public DtoDocument uploadReport(HttpServletRequest request) {
        //删除原有的文档
        List<DtoDocument> oldDocList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(Collections.singletonList(request.getParameter("folderId")),
                request.getParameter("docTypeId"));
        if (StringUtil.isNotEmpty(oldDocList)) {
            documentRepository.delete(oldDocList);
        }
        List<DtoDocument> documentList = documentService.upload(request, null);
        return StringUtil.isNotEmpty(documentList) ? documentList.get(0) : null;
    }

    @Override
    public String downloadReport(String reportId, Boolean isCopy, HttpServletResponse response) throws IOException {
        DtoDocument document = getDownloadReport(reportId, isCopy);
        if (StringUtil.isNotNull(document)) {
            String path = filePathConfig.getFilePath() + document.getPath();
            String filename = document.getFilename();
            return documentService.fileDownload(path, filename, response);
        } else {
            return "文件不存在！";
        }
    }

    /**
     * 获取下载的报告
     *
     * @param reportId 报告id
     * @param isCopy   是否副本
     * @return
     */
    private DtoDocument getDownloadReport(String reportId, boolean isCopy) {
        List<DtoDocument> documentList = documentService.findByObjectId(reportId);
        DtoDocument document;
        // 是否下载副本文件
        if (isCopy) {
            document = documentList.stream().filter(p -> p.getFilename().contains("副本") && p.getDocSuffix().contains("pdf")).sorted(Comparator.comparing(DtoDocument::getCreateDate, Comparator.reverseOrder())).findFirst().orElse(null);
        } else {
            documentList = documentList.stream().filter(p -> !p.getIsDeleted() && (DOCUMENT_EXTEND_REPORT.equals(p.getDocTypeId())
                    || DOCUMENT_EXTEND_REPORT_UP.equals(p.getDocTypeId()))).sorted(Comparator.comparing(DtoDocument::getModifyDate).reversed())
                    .collect(Collectors.toList());
            document = StringUtil.isNotEmpty(documentList) ? documentList.get(0) : null;
        }
        return document;
    }

    @Override
    public DtoReportInfo queryReportInfo(String reportId) {
        DtoReportInfo reportInfo = new DtoReportInfo();
        DtoReportBaseInfo baseInfo = repository.findByReportId(reportId);
        if (StringUtil.isNull(baseInfo)) {
            baseInfo = new DtoReportBaseInfo();
            baseInfo.setTestPurpose("/");
            baseInfo.setTotalTest(false);
            reportInfo.setIsCreate(true);
        }
        reportInfo.setReportId(reportId);
        reportInfo.setReportBaseInfo(baseInfo);
        List<DtoReportFolderInfo> folderInfoList = reportFolderInfoRepository.findByReportId(reportId);
        List<DtoReportSampleInfo> sampleInfoList = reportSampleInfoRepository.findByReportId(reportId);
        Map<String, List<DtoReportSampleInfo>> sampleInfoMap = sampleInfoList.stream().collect(Collectors.groupingBy(DtoReportSampleInfo::getReportFolderInfoId));
        for (DtoReportFolderInfo folderInfo : folderInfoList) {
            List<DtoReportSampleInfo> sampleInfoForFolder = sampleInfoMap.getOrDefault(folderInfo.getId(), new ArrayList<>());
            sampleInfoForFolder.sort(Comparator.comparing(DtoReportSampleInfo::getSampleCode));
            folderInfo.setReportSampleInfoList(sampleInfoForFolder);
        }
        reportInfo.setReportFolderInfoList(folderInfoList);
        return reportInfo;
    }

    @Override
    public ConfigModel findTechnicalRemark() {
        return configService.findConfig("sys_pro_report_technicalRemark");
    }

    /**
     * 查询报告总称测试项目
     *
     * @param reportId 报告id
     * @return 总称测试项目列表
     */
    @Override
    public List<DtoTest> findReportMergeTest(String reportId) {
        List<DtoTest> mergeTestList = new ArrayList<>();
        DtoReport report = reportRepository.findOne(reportId);
        if (StringUtil.isNotNull(report)) {
            List<String> sampleIdList = reportDetailRepository.findByReportId(report.getId()).stream()
                    .map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleIdList)) {
                List<String> testIdList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList).stream().map(DtoAnalyseData::getTestId)
                        .distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(testIdList)) {
                    List<String> parentIdList = testRepository.findAll(testIdList).stream().map(DtoTest::getParentId)
                            .filter(p -> StringUtil.isNotEmpty(p) && !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(parentIdList)) {
                        mergeTestList = testRepository.findAll(parentIdList).stream().filter(p -> StringUtil.isNotNull(p.getIsTotalTest()) && p.getIsTotalTest()).collect(Collectors.toList());
                    }
                }
            }
        }
        return mergeTestList;
    }

    @Override
    public String getDocName(String reportId, Boolean isCopy) {
        DtoDocument document = getDownloadReport(reportId, isCopy);
        if (StringUtil.isNotNull(document)) {
            return document.getFilename();
        } else {
            throw new BaseException("文件不存在");
        }
    }

    /**
     * 根据例行任务点位排序明细对folderIdList进行排序
     *
     * @param fixedPointSortDtlList  点位排序明细
     * @param folderIdList           要排序的点位id列表
     * @param folderId2SampleListMap 点位id和样品的映射
     * @return 点位id和点位对象的映射
     */
    private Map<String, DtoSampleFolder> sortFldByFixPntSort(List<DtoFixedPointSortDetil> fixedPointSortDtlList, List<String> folderIdList,
                                                             Map<String, List<DtoSample>> folderId2SampleListMap) {
        fixedPointSortDtlList.sort(Comparator.comparing(DtoFixedPointSortDetil::getOrderNum));
        List<String> sortPointIdList = fixedPointSortDtlList.stream().map(DtoFixedPointSortDetil::getFixedPointId).collect(Collectors.toList());
        List<DtoSampleFolder> allSampleFolderList = sampleFolderRepository.findAll(folderIdList);
        Map<String, DtoSampleFolder> fldId2SmpFldMap = allSampleFolderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        List<String> fixedPointFldIdList = new ArrayList<>();
        List<String> unFixedPointFldIdList = new ArrayList<>();
        Map<String, List<String>> fixPntId2FldIdListMap = new HashMap<>();
        for (String fldId : folderIdList) {
            if (fldId2SmpFldMap.containsKey(fldId) && !UUIDHelper.GUID_EMPTY.equals(fldId2SmpFldMap.get(fldId).getFixedPointId())) {
                fixedPointFldIdList.add(fldId);
                if (!fixPntId2FldIdListMap.containsKey(fldId2SmpFldMap.get(fldId).getFixedPointId())) {
                    fixPntId2FldIdListMap.put(fldId2SmpFldMap.get(fldId).getFixedPointId(), new ArrayList<>());
                }
                fixPntId2FldIdListMap.get(fldId2SmpFldMap.get(fldId).getFixedPointId()).add(fldId);
            } else {
                unFixedPointFldIdList.add(fldId);
            }
        }
        folderIdList.clear();
        for (String sortPntId : sortPointIdList) {
            if (fixPntId2FldIdListMap.containsKey(sortPntId)) {
                folderIdList.addAll(fixPntId2FldIdListMap.get(sortPntId));
                fixedPointFldIdList.removeAll(fixPntId2FldIdListMap.get(sortPntId));
            }
        }
        folderIdList.addAll(fixedPointFldIdList);
        //不是例行任务的点位按照其下样品的样品编号排序
        List<String> sortUnFixedPointFldIdList = sortFolderIdBySampleCode(unFixedPointFldIdList, folderId2SampleListMap);
        folderIdList.addAll(sortUnFixedPointFldIdList);
        return fldId2SmpFldMap;
    }

    /**
     * 不是例行任务的点位按照其下样品的样品编号排序
     *
     * @param folderIdList           点位id列表
     * @param folderId2SampleListMap 点位id和样品的映射
     * @return 排好序的点位id
     */
    private List<String> sortFolderIdBySampleCode(List<String> folderIdList, Map<String, List<DtoSample>> folderId2SampleListMap) {
        List<String> resList = new ArrayList<>();
        List<String> remainFolderIdList = new ArrayList<>();
        List<DtoSample> sampleList = new ArrayList<>();
        for (String folderId : folderIdList) {
            if (folderId2SampleListMap.containsKey(folderId)) {
                sampleList.addAll(folderId2SampleListMap.get(folderId));
            } else {
                remainFolderIdList.add(folderId);
            }
        }
        sampleList.sort(Comparator.comparing(DtoSample::getCode));
        for (DtoSample sample : sampleList) {
            if (resList.size() + remainFolderIdList.size() == folderIdList.size()) {
                break;
            }
            if (!resList.contains(sample.getSampleFolderId())) {
                resList.add(sample.getSampleFolderId());
            }
        }
        resList.addAll(remainFolderIdList);
        return resList;
    }

    /**
     * 获取电子报告基础信息
     *
     * @param reportId 报告id
     * @return 电子报告基础信息
     */
    private DtoReportBaseInfo getBaseInfo(String reportId) {
        DtoReport report = checkReportExist(reportId);
        DtoReportBaseInfo baseInfo = repository.findByReportId(reportId);
        if (StringUtil.isNull(baseInfo)) {
            baseInfo = new DtoReportBaseInfo();
        }
        DtoProject project = projectRepository.findOne(report.getProjectId());
        baseInfo.setProjectName(StringUtil.isNotNull(project) ? project.getProjectName() : "");
        baseInfo.setInspectedEnt(StringUtil.isNotNull(project) ? project.getInspectedEnt() : "");
        baseInfo.setInspectedAddress(StringUtil.isNotNull(project) ? project.getInspectedAddress() : "");
        baseInfo.setCustomerName(StringUtil.isNotNull(project) ? project.getCustomerName() : "");
        baseInfo.setCustomerAddress(StringUtil.isNotNull(project) ? project.getCustomerAddress() : "");
        baseInfo.setProjectRemark(StringUtil.isNotNull(project) ? project.getRemark() : "");
        String inputTime = (StringUtil.isNotNull(project) && StringUtil.isNotNull(project.getInputTime()))
                ? DateUtil.dateToString(project.getInputTime(), DateUtil.YEAR) : "";
        inputTime = !inputTime.contains("1753") ? inputTime : "";
        baseInfo.setReportDate(inputTime);
//        ConfigModel configModel = configService.findConfig("sys_pro_report_technicalRemark");
//        String technicalRemark = StringUtil.isNotNull(configModel) ? configModel.getConfigValue() : "";
        baseInfo.setTechnicalRemark("");
        // 检测目的”默认获取“项目基本信息-推送与信息管理”中的“任务类型”字段。如果任务未标记推送，依旧默认“/”。
        String testPurpose = "/";
        List<DtoProjectContract> projectContracts = projectContractRepository.findByProjectId(report.getProjectId());
        if (StringUtil.isNotEmpty(projectContracts) && (projectContracts.get(0).getIsPush() == 1)){
            testPurpose = projectContracts.get(0).getTaskType();
        }
        baseInfo.setTestPurpose(testPurpose);
        // 是否总成为是时，默认全选
        if (baseInfo.getTotalTest()) {
            List<DtoTest> dtoTests = findReportMergeTest(reportId);
            baseInfo.setTotalTestIds(dtoTests.stream().map(DtoTest::getId).collect(Collectors.joining(",")));
        }
        return baseInfo;
    }

    /**
     * 按照报告生成的逻辑获取报告点位及样品信息
     *
     * @param reportId 报告id
     * @return 报告点位及样品信息
     */
    private List<DtoReportFolderInfo> getFolderInfoForReport(String reportId) {
        List<DtoSample> sampleList = getSampleForReport(reportId);
        List<DtoAnalyseData> analyseDataList = getAnalyseDataList(sampleList);
        List<DtoTest> testList = getTestList(analyseDataList);
        List<DtoSample> qcSampleList = getQcSampleForReport(sampleList);
        List<DtoAnalyseData> qcAnaDataList = getQcAnalyseDataList(qcSampleList);
        List<DtoSampleFolder> folderList = getFolderList(sampleList);
        List<DtoOutSourceData> outSourceDataList = getOutSourceData(analyseDataList);
        outSourceDataList.addAll(getOutSourceData(qcAnaDataList));
        List<DtoReportFolderInfo> allFolderInfoList = new ArrayList<>();
//        String fixedPj = getConfigValue("sys_pro_report_folderRemarkStr_pj"), fixedLl = getConfigValue("sys_pro_report_folderRemarkStr_ll"),
//                fixedGk = getConfigValue("sys_pro_report_folderRemarkStr_gk");
        //原样报告点位信息
        allFolderInfoList.addAll(getFolderInfoForSample(sampleList, folderList, reportId));
        //全程序空白样报告点位信息
        allFolderInfoList.addAll(getFolderInfoForOutBlankSample(qcSampleList, qcAnaDataList, testList, reportId));
        //平行样样报告点位信息
        allFolderInfoList.addAll(getFolderInfoForParallelSample(qcSampleList, folderList, qcAnaDataList, testList, reportId));
        return allFolderInfoList;
    }

    /**
     * 获取参数值
     *
     * @param configName 参数名称
     * @return 参数值
     */
    private String getConfigValue(String configName) {
        ConfigModel configModel = configService.findConfig(configName);
        return StringUtil.isNotNull(configModel) ? configModel.getConfigValue() : "";
    }

    /**
     * 获取点位列表
     *
     * @param sampleList 样品列表
     * @return 点位列表
     */
    private List<DtoSampleFolder> getFolderList(List<DtoSample> sampleList) {
        List<String> folderIdList = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        return StringUtil.isNotEmpty(folderIdList) ? sampleFolderRepository.findAll(folderIdList) : new ArrayList<>();
    }

    /**
     * 获取原样的点位样品信息
     *
     * @param sampleList 样品列表
     * @param reportId   报告id
     * @return 原样的点位样品信息
     */
    private List<DtoReportFolderInfo> getFolderInfoForSample(List<DtoSample> sampleList, List<DtoSampleFolder> folderList, String reportId) {
        List<DtoReportFolderInfo> infoList = new ArrayList<>();
        Map<String, List<DtoSample>> sampleMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleFolderId));
        List<List<DtoSample>> sortSampleList = sortSampleMap(sampleMap);
        Map<String, DtoSampleFolder> folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        for (List<DtoSample> folderSampleList : sortSampleList) {
            String folderId = folderSampleList.get(0).getSampleFolderId();
            DtoSampleFolder folder = folderMap.getOrDefault(folderId, null);
            String evaRemark = "";
            String outSrcRemark = "";
            String folderName = StringUtil.isNotNull(folder) ? folder.getWatchSpot() : "";
            String folderCode = StringUtil.isNotNull(folder) ? folder.getFolderCode() : "";
            folderId = StringUtil.isNotEmpty(folderId) ? folderId : UUIDHelper.GUID_EMPTY;
            DtoReportFolderInfo info = initReportFolderInfo(reportId, folderSampleList, folderName, folderCode, folderId, evaRemark + ";" + outSrcRemark);
            infoList.add(info);
        }
        return infoList;
    }

    /**
     * 获取室外空白的点位样品信息
     *
     * @param qcSampleList  样品列表
     * @param qcAnaDataList 分析数据列表
     * @param testList      测试项目列表
     * @param reportId      报告id
     * @return 原样的点位样品信息
     */
    private List<DtoReportFolderInfo> getFolderInfoForOutBlankSample(List<DtoSample> qcSampleList, List<DtoAnalyseData> qcAnaDataList, List<DtoTest> testList, String reportId) {
        //过滤出现场空白样分析数据
        List<DtoAnalyseData> outBlankAnaDataList = qcAnaDataList.stream().filter(p -> EnumLIM.EnumQCType.空白.getValue().equals(p.getQcType())
                && EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
        List<String> outBlankSampleIdList = outBlankAnaDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<String> outBlankTestIdList = outBlankAnaDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> outBlankTestList = testList.stream().filter(p -> outBlankTestIdList.contains(p.getId())).collect(Collectors.toList());
        List<DtoSample> outBlankSampleList = qcSampleList.stream().filter(p -> outBlankSampleIdList.contains(p.getId())).collect(Collectors.toList());
        List<DtoReportFolderInfo> infoList = new ArrayList<>();
        if (StringUtil.isNotEmpty(outBlankSampleList)) {
            DtoSampleFolder folder = new DtoSampleFolder();
            folder.setWatchSpot("全程序空白样");
            String sampleFolderId = outBlankSampleList.get(0).getSampleFolderId();
            String folderId = StringUtil.isNotEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId) ? sampleFolderId : "全程序空白样";
            folder.setId(folderId);
            String evaRemark = "";
            String outSrcRemark = "";
            DtoReportFolderInfo info = initReportFolderInfo(reportId, outBlankSampleList, folder.getWatchSpot(), "", folderId, evaRemark + ";" + outSrcRemark);
            infoList.add(info);
        }
        return infoList;
    }

    /**
     * 获取室外空白的点位样品信息
     *
     * @param qcSampleList  样品列表
     * @param qcAnaDataList 分析数据列表
     * @param testList      测试项目列表
     * @param reportId      报告id
     * @return 原样的点位样品信息
     */
    private List<DtoReportFolderInfo> getFolderInfoForParallelSample(List<DtoSample> qcSampleList, List<DtoSampleFolder> folderList, List<DtoAnalyseData> qcAnaDataList,
                                                                     List<DtoTest> testList, String reportId) {
        //过滤出现场平行样分析数据
        List<DtoAnalyseData> outParallelAnaDataList = qcAnaDataList.stream().filter(p -> EnumLIM.EnumQCType.平行.getValue().equals(p.getQcType())
                && EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
        List<String> outParallelSampleIdList = outParallelAnaDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> outParallelSampleList = qcSampleList.stream().filter(p -> outParallelSampleIdList.contains(p.getId())).collect(Collectors.toList());
        List<String> outParallelTestIdList = outParallelAnaDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> outParallelTestList = testList.stream().filter(p -> outParallelTestIdList.contains(p.getId())).collect(Collectors.toList());
        List<DtoReportFolderInfo> infoList = new ArrayList<>();
        Map<String, List<DtoSample>> sampleMap = outParallelSampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleFolderId));
        List<List<DtoSample>> sortSampleList = sortSampleMap(sampleMap);
        Map<String, DtoSampleFolder> folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        for (List<DtoSample> folderSampleList : sortSampleList) {
            String folderId = folderSampleList.get(0).getSampleFolderId();
            DtoSampleFolder folder = folderMap.getOrDefault(folderId, null);
            String evaRemark = "";
            String outSrcRemark = "";
            String folderName = StringUtil.isNotNull(folder) ? folder.getWatchSpot() : folderSampleList.get(0).getRedFolderName();
            if (folderName.contains("(现场平行样)")) {
                folderName = formatWatchSpotForPx(folderName);
            }
            folderId = StringUtil.isNotEmpty(folderId) ? folderId : "";
            folderId = folderId + "现场平行样";
            String folderCode = StringUtil.isNotNull(folder) ? folder.getFolderCode() : "";
            DtoReportFolderInfo info = initReportFolderInfo(reportId, folderSampleList, folderName, folderCode, folderId, evaRemark + ";" + outSrcRemark);
            infoList.add(info);
        }
        return infoList;
    }

    /**
     * 处理室外平行样的点位名称
     *
     * @param watchSpot 原来的点位名称
     * @return 处理后的名称
     */
    private String formatWatchSpotForPx(String watchSpot) {
        int pxIdx = watchSpot.lastIndexOf("(现场平行样)");
        watchSpot = watchSpot.substring(0, pxIdx);
        int timeIdx = watchSpot.lastIndexOf("(");
        if (timeIdx != -1) {
            watchSpot = watchSpot.substring(0, timeIdx);
        }
        watchSpot = watchSpot + "(现场平行样)";
        return watchSpot;
    }

    /**
     * 初始化报告点位信息
     *
     * @param reportId           报告id
     * @param outBlankSampleList 空白样品列表
     * @param folderName         点位名称
     * @param folderCode         点位编码
     * @param folderId           点位id
     * @return 报告点位信息
     */
    private DtoReportFolderInfo initReportFolderInfo(String reportId, List<DtoSample> outBlankSampleList, String folderName, String folderCode,
                                                     String folderId, String remark) {
        DtoReportFolderInfo info = new DtoReportFolderInfo();
        info.setReportId(reportId);
        info.setFolderId(folderId);
        info.setFolderName(folderName);
        info.setFolderCode(folderCode);
        info.setFolderRemark(remark);
        List<DtoReportSampleInfo> reportSampleInfoList = new ArrayList<>();
        for (DtoSample folderSample : outBlankSampleList) {
            DtoReportSampleInfo sampleInfo = new DtoReportSampleInfo();
            sampleInfo.setReportId(reportId);
            sampleInfo.setReportFolderInfoId(info.getId());
            sampleInfo.setSampleCode(folderSample.getCode());
            sampleInfo.setSampleId(folderSample.getId());
            sampleInfo.setSampleRemark("");
            reportSampleInfoList.add(sampleInfo);
        }
        info.setReportSampleInfoList(reportSampleInfoList);
        return info;
    }

    /**
     * 获取点位备注
     *
     * @param folder           点位
     * @param folderSampleList 样品列表
     * @return 点位备注
     */
    private String getFolderRemark(DtoSampleFolder folder, List<DtoSample> folderSampleList, List<DtoAnalyseData> analyseDataList,
                                   List<DtoTest> testList, List<DtoTest> parentTestList, Map<String, List<DtoEvaluationRecord>> folderEvaluationMap, String fixedPj) {
        if (StringUtil.isNotNull(folder)) {
            StringBuilder pjStr = new StringBuilder();
            List<String> sampleIdList = folderSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<String> pageTestIdList = analyseDataList.stream().filter(p -> sampleIdList.contains(p.getSampleId())).map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> pageTestList = testList.stream().filter(p -> pageTestIdList.contains(p.getId())).collect(Collectors.toList());
            Map<String, DtoTest> testMap = pageTestList.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
            Map<String, DtoTest> parentTestMap = parentTestList.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
            String watchSpot = folder.getWatchSpot();
            if (folderEvaluationMap.containsKey(folder.getId())) {
                pjStr.append(watchSpot).append("：");
                Map<String, List<DtoEvaluationRecord>> recordMap = folderEvaluationMap.get(folder.getId()).stream()
                        .collect(Collectors.groupingBy(p -> p.getEvaluationName() + "_" + p.getEvaluationLevelName()));
                for (Map.Entry<String, List<DtoEvaluationRecord>> entry : recordMap.entrySet()) {
                    List<String> testIdList = entry.getValue().stream().map(DtoEvaluationRecord::getTestId).distinct().collect(Collectors.toList());
                    Set<String> anaItemNameSet = new HashSet<>();
                    for (String testId : testIdList) {
                        if (testMap.containsKey(testId)) {
                            DtoTest test = testMap.get(testId);
                            String itemName = parentTestMap.containsKey(test.getParentId())
                                    ? parentTestMap.get(test.getParentId()).getRedAnalyzeItemName() : test.getRedAnalyzeItemName();
                            anaItemNameSet.add(itemName);
                        }
                    }
                    String[] arr = entry.getKey().split("_");
                    String evaName = arr.length > 0 ? arr[0] : "";
                    String lv = arr.length > 1 ? arr[1] : "";
                    pjStr.append(String.join("、", anaItemNameSet)).append(fixedPj).append(evaName).append(" ")
                            .append(lv).append("。");
                }
                pjStr.append("\n");
            }

            String pj = pjStr.toString();
            if (pj.endsWith("\n")) {
                pj = pj.substring(0, pj.length() - 1);
            }
            pj = StringUtil.isNotEmpty(pj) ? pj : "";
            return pj;
        }
        return "";
    }

    /**
     * 获取点位分包备注
     *
     * @param analyseDataList   分析数据列表
     * @param folderSampleList  点位样品列表
     * @param testList          测试项目列表
     * @param paramsDataList    参数列表
     * @param outSourceDataList 分包数据列表
     * @return 点位分包备注
     */
    private String getFolderOutSrcRemark(List<DtoAnalyseData> analyseDataList, List<DtoSample> folderSampleList, List<DtoTest> testList, List<DtoParamsData> paramsDataList,
                                         List<DtoOutSourceData> outSourceDataList, String fixedLl, String fixedGk) {
        List<String> sampleIdList = folderSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<String> pageTestIdList = analyseDataList.stream().filter(p -> sampleIdList.contains(p.getSampleId())).map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> pageTestList = testList.stream().filter(p -> pageTestIdList.contains(p.getId())).collect(Collectors.toList());
        List<String> outSrcTestIdList = analyseDataList.stream().filter(p -> p.getIsOutsourcing() || p.getIsSamplingOut()).map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> ourSrcTestList = pageTestList.stream().filter(p -> outSrcTestIdList.contains(p.getId())).collect(Collectors.toList());
        int numIdx = StringUtil.isNotEmpty(ourSrcTestList) ? 2 : 1;
        Map<String, List<DtoOutSourceData>> outSourceDataMap = outSourceDataList.stream().collect(Collectors.groupingBy(DtoOutSourceData::getAnalyseDataId));
        String itemNameStr = getAnaItemNameStr(ourSrcTestList, analyseDataList, outSourceDataMap);
        String remark = "";
        if (StringUtil.isNotEmpty(fixedLl)) {
            remark = itemNameStr + (StringUtil.isNotEmpty(itemNameStr) ? "\n" : "") + numIdx + fixedLl;
        }
        if (StringUtil.isNotEmpty(fixedGk)) {
            String paramValue = getParamValByName(paramsDataList, "工况信息", "");
            remark = remark + "\n" + (numIdx + 1) + fixedGk + paramValue;
        }
        return remark;
    }

    /**
     * 获取分析项目名称字符串
     *
     * @param testList 分析数据列表
     * @return 析项目名称字符串
     */
    private static String getAnaItemNameStr(List<DtoTest> testList, List<DtoAnalyseData> analyseDataList, Map<String, List<DtoOutSourceData>> ourSourceDataMap) {
        String resultStr = "";
        if (StringUtil.isNotEmpty(testList)) {
            List<DtoAnalyseData> outSrcAnalyseDataList = analyseDataList.stream().filter(AnalyseData::getIsOutsourcing).collect(Collectors.toList());
            List<DtoOutSourceData> outSourceDataList = new ArrayList<>();
            for (DtoAnalyseData analyseData : outSrcAnalyseDataList) {
                if (ourSourceDataMap.containsKey(analyseData.getId())) {
                    outSourceDataList.add(ourSourceDataMap.get(analyseData.getId()).get(0));
                }
            }
            Set<String> subcontractorSet = new HashSet<>(), cmaCodeSet = new HashSet<>(), outReportCodeSet = new HashSet<>();
            for (DtoOutSourceData outSourceData : outSourceDataList) {
                if (StringUtil.isNotEmpty(outSourceData.getSubcontractor())) {
                    subcontractorSet.add(outSourceData.getSubcontractor());
                }
                if (StringUtil.isNotEmpty(outSourceData.getCmaCode())) {
                    cmaCodeSet.add(outSourceData.getCmaCode());
                }
                if (StringUtil.isNotEmpty(outSourceData.getOutSourceReportCode())) {
                    outReportCodeSet.add(outSourceData.getOutSourceReportCode());
                }
            }

            //过滤出非认可认证的测试项目
            List<String> notCertItemNameList = new ArrayList<>();
            List<String> certItemNameList = new ArrayList<>();
            for (DtoTest test : testList) {
                if (EnumLIM.EnumTestCert.非认证认可.getValue().equals(test.getCert())) {
                    notCertItemNameList.add(test.getRedAnalyzeItemName());
                } else {
                    certItemNameList.add(test.getRedAnalyzeItemName());
                }
            }
            String notCertItemNameStr = StringUtil.isNotEmpty(notCertItemNameList) ? String.join("、", notCertItemNameList) : "";
            String certItemNameStr = StringUtil.isNotEmpty(certItemNameList) ? String.join("、", certItemNameList) : "";
            if (StringUtil.isNotEmpty(certItemNameStr)) {
                resultStr = resultStr + certItemNameStr + "为本实验室资质认定项目";
                resultStr += StringUtil.isNotEmpty(notCertItemNameStr) ? "，" : "。";
            }
            if (StringUtil.isNotEmpty(notCertItemNameStr)) {
                resultStr += (notCertItemNameStr + "为非本实验室资质认定项目。由具有能力之外部实验室-" + String.join("、", subcontractorSet)
                        + "，报告编号为" + String.join("、", outReportCodeSet) + "CMA证书编号：" + String.join("、", cmaCodeSet) + "。");
            }
            if (StringUtil.isNotEmpty(resultStr)) {
                resultStr = "注：1、" + resultStr;
            }
        }
        return resultStr;
    }

    /**
     * 获取样品相关参数数据
     *
     * @param sampleList 样品列表
     * @return 参数数据
     */
    private List<DtoParamsData> getParamsDataList(List<DtoSample> sampleList) {
        List<DtoParamsData> list = new ArrayList<>();
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleIds)) {
            list = paramsDataRepository.findByObjectIdIn(sampleIds);
            List<String> configIdList = list.stream().map(DtoParamsData::getParamsConfigId).distinct().collect(Collectors.toList());
            List<DtoParamsConfig> paramsConfigList = StringUtil.isNotEmpty(configIdList) ? paramsConfigRepository.findAll(configIdList) : new ArrayList<>();
            Map<String, DtoParamsConfig> configMap = paramsConfigList.stream().collect(Collectors.toMap(DtoParamsConfig::getId, dto -> dto));
            for (DtoParamsData paramsData : list) {
                DtoParamsConfig config = configMap.getOrDefault(paramsData.getParamsConfigId(), null);
                if (StringUtil.isNotNull(config)) {
                    paramsData.setDataSource(config.getDataSource());
                    paramsData.setAnalyzeItemId(config.getAnalyzeItemId());
                    paramsData.setParamsType(config.getParamsType());
                }
            }
        }
        return list;
    }

    /**
     * 获取分包数据信息
     *
     * @param analyseDataList 分析数据列表
     * @return 获取分包数据信息
     */
    private List<DtoOutSourceData> getOutSourceData(List<DtoAnalyseData> analyseDataList) {
        List<String> anaDataIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        return StringUtil.isNotEmpty(anaDataIdList) ? outSourceDataRepository.findByAnalyseDataIdIn(anaDataIdList) : new ArrayList<>();
    }

    /**
     * 点位样品组间排序
     *
     * @param smpMap 分组的样品
     * @return 排好序的样品
     */
    private List<List<DtoSample>> sortSampleMap(Map<String, List<DtoSample>> smpMap) {
        List<List<DtoSample>> sortSampleList = new ArrayList<>();
        List<String> keyList = new ArrayList<>(smpMap.keySet());
        Collections.sort(keyList);
        for (String key : keyList) {
            List<DtoSample> sampleListForKey = smpMap.get(key);
            sampleListForKey.sort(Comparator.comparing(DtoSample::getCode));
            sortSampleList.add(sampleListForKey);
        }
        sortSampleList.sort(this::sort);
        return sortSampleList;
    }

    /**
     * 样品列表排序
     *
     * @param o1 样品1
     * @param o2 样品2
     */
    private int sort(List<DtoSample> o1, List<DtoSample> o2) {
        String folderName1 = o1.get(0).getRedFolderName();
        int idx = folderName1.lastIndexOf("(");
        if (idx != -1) {
            folderName1 = folderName1.substring(0, idx);
        }
        String folderName2 = o2.get(0).getRedFolderName();
        int idx2 = folderName2.lastIndexOf("(");
        if (idx2 != -1) {
            folderName2 = folderName2.substring(0, idx2);
        }
        return folderName1.compareTo(folderName2);
    }

    /**
     * 获取报告质控样
     *
     * @param sampleList 样品列表
     * @return 质控样
     */
    private List<DtoSample> getQcSampleForReport(List<DtoSample> sampleList) {
        List<DtoSample> qcSampleList = getQcSample(sampleList);
        //从检测单中获取质控样
        List<DtoSample> innerQcSampleList = getWorkSheetQcSample(sampleList, qcSampleList);
        if (StringUtil.isNotEmpty(innerQcSampleList)) {
            qcSampleList.addAll(innerQcSampleList);
        }
        //从现场送样单中获取现场质控样
        List<DtoSample> recordQcSampleList = getReceiveSampleRecordQcSampleList(sampleList, qcSampleList);
        if (StringUtil.isNotEmpty(recordQcSampleList)) {
            qcSampleList.addAll(recordQcSampleList);
        }
        return qcSampleList;
    }

    /**
     * 获取报告下的样品
     *
     * @param reportId 报告id
     * @return 报告下的样品
     */
    private List<DtoSample> getSampleForReport(String reportId) {
        List<DtoReportDetail> reportDetailList = reportDetailRepository.findByReportId(reportId);
        List<String> sampleIds = reportDetailList.stream().map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
        return StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
    }

    /**
     * 获取报告下的分析数据
     *
     * @param sampleList 样品列表
     * @return 报告下的分析数据
     */
    private List<DtoAnalyseData> getAnalyseDataList(List<DtoSample> sampleList) {
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        return StringUtil.isNotEmpty(sampleIdList) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList) : new ArrayList<>();
    }

    /**
     * 获取报告下的质控分析数据
     *
     * @param qcSampleList 样品列表
     * @return 报告下的质控分析数据
     */
    private List<DtoAnalyseData> getQcAnalyseDataList(List<DtoSample> qcSampleList) {
        List<String> qcSampleIdList = qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        return StringUtil.isNotEmpty(qcSampleIdList) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(qcSampleIdList) : new ArrayList<>();
    }

    /**
     * 获取报告下的测试项目数据
     *
     * @param analyseDataList 报告id
     * @return 报告下的测试项目数据
     */
    private List<DtoTest> getTestList(List<DtoAnalyseData> analyseDataList) {
        List<String> testIdList = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        return StringUtil.isNotEmpty(testIdList) ? testRepository.findAll(testIdList) : new ArrayList<>();
    }

    /**
     * 获取报告下的父测试项目数据
     *
     * @param testList 测试项目列表
     * @return 报告下的测试项目数据
     */
    private List<DtoTest> getParentTestList(List<DtoTest> testList) {
        List<String> parentTestIdList = testList.stream().map(DtoTest::getParentId).distinct().collect(Collectors.toList());
        return StringUtil.isNotEmpty(parentTestIdList) ? testRepository.findAll(parentTestIdList) : new ArrayList<>();
    }

    /**
     * 获取点位评价记录
     *
     * @param folderList 点位列表
     * @return 点位评价记录
     */
    private Map<String, List<DtoEvaluationRecord>> getFolderEvaluationMap(List<DtoSampleFolder> folderList) {
        List<String> folderIdList = folderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoEvaluationRecord> evaluationRecordList = StringUtil.isNotEmpty(folderIdList) ? evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan
                (folderIdList, EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue()) : new ArrayList<>();
        setEvaluationLevelName(evaluationRecordList);
        return evaluationRecordList.stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId));
    }

    /**
     * 设置评价记录的标准名称和等级名称
     *
     * @param evaluationRecordList 评价记录列表
     */
    private void setEvaluationLevelName(List<DtoEvaluationRecord> evaluationRecordList) {
        if (StringUtil.isNotEmpty(evaluationRecordList)) {
            List<String> evaluationIdList = evaluationRecordList.stream().map(DtoEvaluationRecord::getEvaluationId).distinct().collect(Collectors.toList());
            List<String> levelIdList = evaluationRecordList.stream().map(DtoEvaluationRecord::getEvaluationLevelId).distinct().collect(Collectors.toList());
            List<DtoEvaluationCriteria> evaluationCriteriaList = evaluationCriteriaRepository.findAll(evaluationIdList);
            Map<String, DtoEvaluationCriteria> evaId2EvaCriMap = evaluationCriteriaList.stream().collect(Collectors.toMap(DtoEvaluationCriteria::getId, dto -> dto));
            List<DtoEvaluationLevel> evaluationLevelList = evaluationLevelRepository.findAll(levelIdList);
            Map<String, DtoEvaluationLevel> lvId2LvMap = evaluationLevelList.stream().collect(Collectors.toMap(DtoEvaluationLevel::getId, dto -> dto));
            for (DtoEvaluationRecord evaRcd : evaluationRecordList) {
                DtoEvaluationCriteria evaluationCriteria = evaId2EvaCriMap.get(evaRcd.getEvaluationId());
                if (StringUtil.isNotNull(evaluationCriteria)) {
                    evaRcd.setEvaluationName(evaluationCriteria.getName());
                    evaRcd.setRedCountryStandard(evaluationCriteria.getCode());
                }
                DtoEvaluationLevel evaluationLevel = lvId2LvMap.get(evaRcd.getEvaluationLevelId());
                if (StringUtil.isNotNull(evaluationLevel)) {
                    evaRcd.setEvaluationLevelName(evaluationLevel.getName());
                }
            }
        }
    }

    /**
     * 从现场送样单中获取现场质控样
     *
     * @param sampleList   样品列表
     * @param qcSampleList 质控样列表
     * @return 现场质控样
     */
    private List<DtoSample> getReceiveSampleRecordQcSampleList(List<DtoSample> sampleList, List<DtoSample> qcSampleList) {
        List<DtoSample> resList = new ArrayList<>();
        List<String> receiveIdList = sampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        List<String> qcReceiveIdList = qcSampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(qcReceiveIdList)) {
            receiveIdList.addAll(qcReceiveIdList);
        }
        List<DtoReceiveSubSampleRecord> subRecordList = StringUtil.isNotEmpty(receiveIdList)
                ? receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIdList) : new ArrayList<>();
        subRecordList = subRecordList.stream().filter(p -> p.getCode().contains("-XC")).collect(Collectors.toList());
        List<String> subRecordIdList = subRecordList.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
        List<DtoReceiveSubSampleRecord2Sample> record2SampleList = StringUtil.isNotEmpty(subRecordIdList)
                ? receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordIdIn(subRecordIdList) : new ArrayList<>();
        List<String> sampleIdForSubRecord = record2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleIdForSubRecord)) {
            List<String> existSampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(qcSampleList)) {
                existSampleIdList.addAll(qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList()));
            }
            sampleIdForSubRecord.removeAll(existSampleIdList);
            if (StringUtil.isNotEmpty(sampleIdForSubRecord)) {
                resList = sampleRepository.findByIdInAndIsDeletedFalse(sampleIdForSubRecord);
            }
        }
        return resList;
    }

    /**
     * 获取报告样品关联检测单的所有质控样品
     *
     * @param sampleList   样品列表
     * @param qcSampleList 质控样列表
     */
    protected List<DtoSample> getWorkSheetQcSample(List<DtoSample> sampleList, List<DtoSample> qcSampleList) {
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        sampleIdList.addAll(qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList()));
        return getWorkSheetQcSampleList(sampleIdList);
    }

    /**
     * 获取质控样品
     *
     * @param sampleList 样品列表
     * @return 质控样列表
     */
    private List<DtoSample> getQcSample(List<DtoSample> sampleList) {
        List<DtoSample> resList = new ArrayList<>();
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSample> qcSampleList = StringUtil.isNotEmpty(sampleIdList) ? sampleRepository.findByAssociateSampleIdIn(sampleIdList) : new ArrayList<>();
        if (StringUtil.isNotEmpty(qcSampleList)) {
            resList.addAll(qcSampleList);
            resList.addAll(getQcSample(qcSampleList));
        }
        return resList;
    }

    /**
     * 从检测单中获取质控样
     *
     * @param sampleIds 样品id列表
     * @return 样品列表
     */
    private List<DtoSample> getWorkSheetQcSampleList(List<String> sampleIds) {
        List<DtoSample> resList = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        List<String> worksheetFolderIdList = analyseDataList.stream().filter(p -> StringUtil.isNotEmpty(p.getWorkSheetFolderId()) && !UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId()))
                .map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(worksheetFolderIdList)) {
            List<DtoAnalyseData> analyseDataForFolderList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(worksheetFolderIdList);
            analyseDataForFolderList = analyseDataForFolderList.stream().filter(p -> !sampleIds.contains(p.getSampleId()) && StringUtil.isNotEmpty(p.getQcId())
                    && !UUIDHelper.GUID_EMPTY.equals(p.getQcId())).collect(Collectors.toList());
            List<String> workSheetQcSampleIdList = analyseDataForFolderList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(workSheetQcSampleIdList)) {
                resList = sampleRepository.findByIdInAndIsDeletedFalse(workSheetQcSampleIdList);
            }
        }
        return resList;
    }


    /**
     * 保存电子报告基础信息
     *
     * @param reportBaseInfo 电子报告基础信息
     * @param reportId       报告id
     */
    private void saveBaseInfo(DtoReportBaseInfo reportBaseInfo, String reportId) {
        DtoReportBaseInfo oldBaseInfo = repository.findByReportId(reportId);
        if (StringUtil.isNotNull(oldBaseInfo)) {
            //修改
            BeanUtils.copyProperties(reportBaseInfo, oldBaseInfo, "id", "reportId", "createDate", "creator", "modifier", "modifyDate");
            repository.save(oldBaseInfo);
        } else {
            //新增
            reportBaseInfo.setReportId(reportId);
            repository.save(reportBaseInfo);
        }
    }

    /**
     * 保存电子报告点位信息
     *
     * @param reportFolderInfoList 电子报告基础信息
     * @param reportId             报告id
     */
    private void saveFolderInfo(List<DtoReportFolderInfo> reportFolderInfoList, String reportId) {
        reportFolderInfoList.forEach(p -> p.setReportId(reportId));
        List<DtoReportFolderInfo> oldFolderInfoList = reportFolderInfoRepository.findByReportId(reportId);
        if (StringUtil.isNotEmpty(oldFolderInfoList)) {
            //原来有数据删除原来的，再插入新的
            reportFolderInfoRepository.logicDeleteById(oldFolderInfoList.stream().map(DtoReportFolderInfo::getId).collect(Collectors.toList()), new Date());
            if (StringUtil.isNotEmpty(reportFolderInfoList)) {
                reportFolderInfoList.forEach(p -> p.setId(UUIDHelper.NewID()));
                reportFolderInfoRepository.save(reportFolderInfoList);
            }
        } else {
            //直接插入新的
            if (StringUtil.isNotEmpty(reportFolderInfoList)) {
                reportFolderInfoRepository.save(reportFolderInfoList);
            }
        }

    }

    /**
     * 保存电子报告样品信息
     *
     * @param reportFolderInfoList 电子报告点位信息列表
     * @param reportId             报告id
     */
    private void saveSampleInfo(List<DtoReportFolderInfo> reportFolderInfoList, String reportId) {
        List<DtoReportSampleInfo> sampleInfoList = new ArrayList<>();
        for (DtoReportFolderInfo folderInfo : reportFolderInfoList) {
            List<DtoReportSampleInfo> loopSampleInfoList = folderInfo.getReportSampleInfoList();
            loopSampleInfoList.forEach(p -> {
                p.setReportId(reportId);
                p.setReportFolderInfoId(folderInfo.getId());
            });
            sampleInfoList.addAll(loopSampleInfoList);
        }
        List<DtoReportSampleInfo> oldSampleInfoList = reportSampleInfoRepository.findByReportId(reportId);
        if (StringUtil.isNotEmpty(oldSampleInfoList)) {
            //原来有数据，先删除原有的，再插入新的
            reportSampleInfoRepository.logicDeleteById(oldSampleInfoList.stream().map(DtoReportSampleInfo::getId).collect(Collectors.toList()), new Date());
            if (StringUtil.isNotEmpty(sampleInfoList)) {
                sampleInfoList.forEach(p -> p.setId(UUIDHelper.NewID()));
                reportSampleInfoRepository.save(sampleInfoList);
            }
        } else {
            //直接插入新的
            if (StringUtil.isNotEmpty(sampleInfoList)) {
                reportSampleInfoRepository.save(sampleInfoList);
            }
        }
    }

    /**
     * 判断报告是否存在
     */
    private DtoReport checkReportExist(String reportId) {
        DtoReport report = reportRepository.findOne(reportId);
        if (StringUtil.isNull(report)) {
            throw new BaseException("报告不存在！");
        }
        return report;
    }

    /**
     * 根据参数名称获取参数值（任意取一个）
     *
     * @param paramsDataList 参数对象列表
     * @param name           参数名称
     * @param defVal         默认值
     * @return 参数值
     */
    private static String getParamValByName(List<DtoParamsData> paramsDataList, String name, String defVal) {
        DtoParamsData paramsData = paramsDataList.stream().filter(p -> name.equals(p.getParamsName())
                && StringUtil.isNotEmpty(p.getParamsValue())).findFirst().orElse(null);
        if (StringUtil.isNotNull(paramsData)) {
            return StringUtil.isNotEmpty(paramsData.getParamsValue()) ? paramsData.getParamsValue() : "";
        }
        return defVal;
    }


    @Autowired
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Autowired
    public void setReportSampleInfoRepository(ReportSampleInfoRepository reportSampleInfoRepository) {
        this.reportSampleInfoRepository = reportSampleInfoRepository;
    }

    @Autowired
    public void setReportFolderInfoRepository(ReportFolderInfoRepository reportFolderInfoRepository) {
        this.reportFolderInfoRepository = reportFolderInfoRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setReportDetailRepository(ReportDetailRepository reportDetailRepository) {
        this.reportDetailRepository = reportDetailRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }

    @Autowired
    public void setReceiveSubSampleRecord2SampleRepository(ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository) {
        this.receiveSubSampleRecord2SampleRepository = receiveSubSampleRecord2SampleRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setEvaluationRecordRepository(EvaluationRecordRepository evaluationRecordRepository) {
        this.evaluationRecordRepository = evaluationRecordRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setEvaluationCriteriaRepository(EvaluationCriteriaRepository evaluationCriteriaRepository) {
        this.evaluationCriteriaRepository = evaluationCriteriaRepository;
    }

    @Autowired
    public void setEvaluationLevelRepository(EvaluationLevelRepository evaluationLevelRepository) {
        this.evaluationLevelRepository = evaluationLevelRepository;
    }

    @Autowired
    public void setParamsDataRepository(ParamsDataRepository paramsDataRepository) {
        this.paramsDataRepository = paramsDataRepository;
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }

    @Autowired
    public void setOutSourceDataRepository(OutSourceDataRepository outSourceDataRepository) {
        this.outSourceDataRepository = outSourceDataRepository;
    }

    @Autowired
    public void setFixedPointSortDetilRepository(FixedPointSortDetilRepository fixedPointSortDetilRepository) {
        this.fixedPointSortDetilRepository = fixedPointSortDetilRepository;
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setReportFolderSortInfoRepository(ReportFolderSortInfoRepository reportFolderSortInfoRepository) {
        this.reportFolderSortInfoRepository = reportFolderSortInfoRepository;
    }

    @Autowired
    public void setConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    public void setProjectContractRepository(ProjectContractRepository projectContractRepository) {
        this.projectContractRepository = projectContractRepository;
    }
}