package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.lims.pro.enums.EnumPRO;
import org.springframework.stereotype.Component;

/**
 * 上海监管平台通用类型策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/15
 */
@Component
public class CommonRegulatoryPlatformClass extends AbsRegulatoryPlatformRemote<Object> {
    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.通用类型.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.通用类型.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.通用类型.getDeleteMethod();
    }
}
