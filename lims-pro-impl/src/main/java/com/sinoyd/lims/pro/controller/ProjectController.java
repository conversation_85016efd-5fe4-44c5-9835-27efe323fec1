package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.pro.criteria.ProjectCriteria;
import com.sinoyd.lims.pro.criteria.ProjectQueryCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.pro.service.SignatureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * Project服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: Project服务")
@RestController
@RequestMapping("api/pro/project")
public class ProjectController extends BaseJpaController<DtoProject, String, ProjectService> {

    @Autowired
    private ProService proService;

    /**
     * 分页动态条件查询项目
     *
     * @param projectCriteria 条件参数
     * @return RestResponse<List < P r oject>>
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping
    public RestResponse<List<DtoProject>> findByPage(ProjectCriteria projectCriteria) {
        PageBean<DtoProject> pageBean = super.getPageBean();
        RestResponse<List<DtoProject>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, projectCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询项目
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoProject>>
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping("/queryProject")
    public RestResponse<List<DtoProject>> queryProject(ProjectQueryCriteria criteria) {
        PageBean<DtoProject> pageBean = super.getPageBean();
        RestResponse<List<DtoProject>> restResponse = new RestResponse<>();
        service.queryProject(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 获取合同下的项目列表
     *
     * @param contractId 合同id
     * @return RestResponse<List < P r o ject>>
     */
    @ApiOperation(value = "获取合同下的项目列表", notes = "获取合同下的项目列表")
    @GetMapping(path = "/contract")
    public RestResponse<List<DtoProject>> findByContractId(@RequestParam(name = "contractId") String contractId) {
        RestResponse<List<DtoProject>> restResponse = new RestResponse<>();
        List<DtoProject> projectList = service.findByContractId(contractId);
        restResponse.setRestStatus(projectList.size() == 0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectList);
        restResponse.setCount(projectList.size());
        return restResponse;
    }

    /**
     * 按主键查询项目
     *
     * @param id 主键id
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "按主键查询项目", notes = "按主键查询项目")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoProject> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        DtoProject project = service.findOne(id);
        restResponse.setData(project);
        restResponse.setRestStatus(StringUtil.isNull(project) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按主键查询送样类项目
     *
     * @param id 主键id
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "按主键查询送样类项目", notes = "按主键查询送样类项目")
    @GetMapping(path = "/outsideSendSample/{id}")
    public RestResponse<DtoReceiveSampleRecordTemp> findOutsideSendSample(@PathVariable(name = "id") String id) {
        RestResponse<DtoReceiveSampleRecordTemp> restResponse = new RestResponse<>();
        DtoReceiveSampleRecordTemp temp = service.findOutsideSendSample(id);
        restResponse.setData(temp);
        restResponse.setRestStatus(StringUtil.isNull(temp) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增项目
     *
     * @param project 实体
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "新增项目", notes = "新增项目")
    @PostMapping
    public RestResponse<DtoProject> create(@RequestBody @Validated DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setData(service.save(project));
        return restResponse;
    }

    /**
     * 新增送样项目
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "新增送样项目", notes = "新增送样项目")
    @PostMapping(path = "/outsideSendSample")
    public RestResponse<DtoReceiveSampleRecordTemp> createOutsideSendSample(@RequestBody @Validated DtoReceiveSampleRecordTemp dto) {
        RestResponse<DtoReceiveSampleRecordTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.saveOutsideSendSample(dto));
        return restResponse;
    }

    /**
     * 修改项目
     *
     * @param project 实体列表
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "修改项目", notes = "修改项目")
    @PutMapping
    public RestResponse<DtoProject> update(@RequestBody @Validated DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setData(service.update(project));
        return restResponse;
    }

    /**
     * 修改送样项目
     *
     * @param dto 实体
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "修改送样项目", notes = "修改送样项目")
    @PutMapping(path = "/outsideSendSample")
    public RestResponse<DtoReceiveSampleRecordTemp> updateOutsideSendSample(@RequestBody @Validated DtoReceiveSampleRecordTemp dto) {
        RestResponse<DtoReceiveSampleRecordTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.updateOutsideSendSample(dto));
        return restResponse;
    }

    /**
     * "根据id删除项目
     *
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除项目", notes = "根据id删除项目")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(id));

        return restResp;
    }

    /**
     * "根据id批量删除项目
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除项目", notes = "根据id批量删除项目")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * 复制项目
     *
     * @param dto 接收字段
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复制项目", notes = "复制项目")
    @PostMapping(path = "/copy")
    public RestResponse<Boolean> copy(@RequestBody DtoReceiveSampleRecord dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.copyProject(dto);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 复制方案
     *
     * @param dto 接收字段（oldProjectId，projectId）
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复制方案", notes = "复制方案")
    @PostMapping(path = "/copyScheme")
    public RestResponse<Boolean> copyScheme(@RequestBody DtoSchemeCopy dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        //复制项目方案
        service.copyScheme(dto.getOldProjectId(), dto.getProjectId());
        restResponse.setData(true);
        return restResponse;
    }

    //projectSchemeModify

    /**
     * 记录修改方案日志
     *
     * @param dto 接收字段（project，opinion）
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "记录修改方案日志", notes = "记录修改方案日志")
    @PostMapping(path = "/projectSchemeModify")
    public RestResponse<Boolean> projectSchemeModify(@RequestBody DtoProjectSchemeModify dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.projectSchemeModify(dto.getProjectId(), dto.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 项目采样操作
     *
     * @param queryDto       dto
     * @param samplingStatus 采样状态
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "项目采样操作", notes = "项目采样操作")
    @PutMapping(path = "/samplingStatus/{samplingStatus}")
    public RestResponse<Boolean> changeSamplingStatus(@RequestBody DtoComplexQuery queryDto, @PathVariable(name = "samplingStatus") Integer samplingStatus) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.changeSamplingStatus(queryDto.getIds(), samplingStatus);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 项目报告操作
     *
     * @param queryDto     dto
     * @param reportStatus 报告状态
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "项目报告操作", notes = "项目报告操作")
    @PutMapping(path = "/reportStatus/{reportStatus}")
    public RestResponse<Boolean> changeReportStatus(@RequestBody DtoComplexQuery queryDto, @PathVariable(name = "reportStatus") Integer reportStatus) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.changeReportStatus(queryDto.getIds(), reportStatus, queryDto.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * "信号操作
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "信号操作", notes = "信号操作")
    @PostMapping("/signal")
    public RestResponse<Boolean> signal(@RequestBody DtoWorkflowSign dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.projectSignal(dto);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 更换编制报告人
     *
     * @param plan dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "更换编制报告人", notes = "更换编制报告人")
    @PutMapping(path = "/reportMaker")
    public RestResponse<Boolean> changeReportMaker(@RequestBody DtoProjectPlan plan) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.changeReportMaker(plan.getProjectId(), plan.getReportMakerId(), plan.getRemark());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 判断项目对应场景下的条件是否满足
     *
     * @param queryDto dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "判断项目对应场景下的条件是否满足", notes = "判断项目对应场景下的条件是否满足")
    @PostMapping(path = "/checkCondition")
    public RestResponse<Boolean> checkCondition(@RequestBody DtoComplexQuery queryDto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.checkCondition(queryDto.getIds(), queryDto.getType());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 办结项目
     *
     * @param queryDto dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "办结项目", notes = "办结项目")
    @PostMapping(path = "/finish")
    public RestResponse<Boolean> finish(@RequestBody DtoComplexQuery queryDto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        proService.finishProject(queryDto.getIds(), queryDto.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 获取项目下检测类型
     *
     * @param projectId 项目id
     * @return RestResponse<List < D t o SampleType>>
     */
    @ApiOperation(value = "获取项目下检测类型", notes = "获取项目下检测类型")
    @GetMapping(path = "/sampleTypes")
    public RestResponse<List<DtoSampleType>> findSampleTypes(@RequestParam(name = "projectId") String projectId) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        restResponse.setData(service.findProjectSampleTypes(projectId));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取项目下检测大类
     *
     * @param projectId 项目id
     * @return RestResponse<List < D t o SampleType>>
     */
    @ApiOperation(value = "获取项目下检测大类", notes = "获取项目下检测大类")
    @GetMapping(path = "/bigSampleTypes")
    public RestResponse<List<DtoSampleType>> findProjectBigSampleTypes(@RequestParam(name = "projectId") String projectId) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        restResponse.setData(service.findProjectBigSampleTypes(projectId));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * "获取项目下未采样品个数
     *
     * @param projectId 项目id
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "获取项目下未采样品个数", notes = "获取项目下未采样品个数")
    @GetMapping("/notSample")
    public RestResponse<Integer> notSample(@RequestParam(name = "projectId") String projectId) {
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setData(service.countNotSampleByProjectId(projectId));
        return restResp;
    }

    /**
     * 获取项目文档
     *
     * @param projectId 项目id
     * @return RestResponse<Map < St r ing, O b j ect>>
     */
    @ApiOperation(value = "获取项目文档", notes = "获取项目文档")
    @GetMapping(path = "/documents")
    public RestResponse<Map<String, Object>> findDocByProjectId(@RequestParam(name = "projectId") String projectId) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(proService.findDocByProjectId(projectId, 1, LimConstants.DOCUMENT_TYPE_ALL));
        return restResponse;
    }

    /**
     * 获取项目文档
     *
     * @param reportId 项目id
     * @return RestResponse<Map < St r ing, O b j ect>>
     */
    @ApiOperation(value = "获取项目文档", notes = "获取项目文档")
    @GetMapping(path = "/documentByReportIds")
    public RestResponse<Map<String, Object>> findDocByReportId(@RequestParam(name = "reportId") String reportId) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(proService.findDocByReportId(reportId, 2, LimConstants.DOCUMENT_TYPE_ALL));
        return restResponse;
    }

    /**
     * 获取测点示意图
     *
     * @param criteria 查询条件
     * @return RestResponse<List < DtoDocument>>
     */
    @GetMapping(path = "/getPointPicDocument")
    public RestResponse<List<DtoDocument>> getPointPicDocument(DocumentCriteria criteria) {
        RestResponse<List<DtoDocument>> response = new RestResponse<>();
        PageBean<DtoDocument> pageBean = super.getPageBean();
        proService.getPointPicDocument(pageBean, criteria);
        response.setData(pageBean.getData());
        response.setCount(pageBean.getRowsCount());
        return response;
    }

    /**
     * 获取送样单文档
     *
     * @param receiveId 项目id
     * @param type      文件类型
     * @return RestResponse<Map < St r ing, O b j ect>>
     */
    @ApiOperation(value = "获取项目文档", notes = "获取项目文档")
    @GetMapping(path = "/documentsByRecId")
    public RestResponse<Map<String, Object>> findDocByReceiveId(@RequestParam(name = "receiveId") String receiveId, @RequestParam(name = "type") Integer type, @RequestParam(name = "docTypeId") String docTypeId) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(proService.findDocByProjectId(receiveId, type, docTypeId));
        return restResponse;
    }

    @ApiOperation(value = "批量下载", notes = "批量下载")
    @GetMapping("/batch-download")
    public RestResponse<String> fileDownload(@RequestParam(name = "projectId") String projectId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(proService.batchDownload(projectId, response));
        return restResp;
    }

    /**
     * 获取项目进度
     *
     * @param projectId 项目id
     * @return RestResponse<DtoProjectInquiry>
     */
    @ApiOperation(value = "获取项目进度", notes = "获取项目进度")
    @GetMapping(path = "/inquiry")
    public RestResponse<DtoProjectInquiry> findProjectInquiry(@RequestParam(name = "projectId") String projectId) {
        RestResponse<DtoProjectInquiry> restResponse = new RestResponse<>();
        restResponse.setData(proService.findProjectInquiry(projectId));
        return restResponse;
    }

    //region 例行任务登记

    /**
     * 监测计划树
     *
     * @return
     */
    @ApiOperation(value = "监测计划树", notes = "监测计划树")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> tree(@RequestParam(name = "key") String key, @RequestParam(name = "year") Integer year) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.getPointProperty(key, year, -1));
        return restResponse;
    }

    /**
     * 已选监测计划列表
     *
     * @return
     */
    @ApiOperation(value = "监测计划列表", notes = "监测计划列表")
    @GetMapping("/getProjectProperty")
    public RestResponse<List<DtoFixedPointProperty>> getProjectProperty(@RequestParam(name = "projectId") String projectId) {
        RestResponse<List<DtoFixedPointProperty>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.getProjectProperty(projectId));
        return restResponse;
    }

    /**
     * 新增项目
     *
     * @param project 实体
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "新增项目", notes = "新增项目")
    @PostMapping("/createScheme")
    public RestResponse<DtoProject> createScheme(@RequestBody DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setData(service.saveProject(project, project.getPropertyIds(), project.getPollution(), project.getScheme()));
        return restResponse;
    }

    /**
     * 获取污染源配置点位信息
     *
     * @return
     */
    @ApiOperation(value = "污染源配置点位信息", notes = "污染源配置点位信息")
    @GetMapping("/getPollutionFolder")
    public RestResponse<List<DtoFixedpoint>> getPollutionFolder(@RequestParam(name = "projectId") String projectId,
                                                                @RequestParam(name = "entId") String entId,
                                                                @RequestParam(name = "enableStatus") Integer enableStatus,
                                                                @RequestParam(name = "showConfig") Boolean showConfig) {
        RestResponse<List<DtoFixedpoint>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.getPollutionFolder(projectId, entId, enableStatus, showConfig));
        return restResponse;
    }

    /**
     * 更新例行方案
     *
     * @return
     */
    @ApiOperation(value = "更新例行方案", notes = "更新例行方案")
    @PostMapping("/routineMonitorScheme")
    public RestResponse routineMonitorScheme(@RequestBody DtoProjectProperty projectProperty) {
        RestResponse restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.routineMonitorScheme(projectProperty.getProjectId(), projectProperty.getPropertyIds());
        return restResponse;
    }

    /**
     * 更新污染源方案
     *
     * @return
     */
    @ApiOperation(value = "更新污染源方案", notes = "更新污染源方案")
    @PostMapping("/pollutionMonitorScheme")
    public RestResponse pollutionMonitorScheme(@RequestBody DtoProjectProperty projectProperty) {
        RestResponse restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.pollutionMonitorScheme(projectProperty.getProjectId(), projectProperty.getPropertyIds());
        return restResponse;
    }

    @ApiOperation(value = "项目进度获取点位地图", notes = "项目进度获取点位地图")
    @GetMapping("/sampleFolderMap")
    public RestResponse<List<DtoSampleFolderMap>> sampleFolderMap(@RequestParam(name = "projectId") String projectId,
                                                                  @RequestParam(name = "keyWord") String keyWord) {
        RestResponse<List<DtoSampleFolderMap>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        List<DtoSampleFolderMap> result = service.getSampleFolderMapOfProject(projectId, keyWord);
        restResponse.setData(result);
        restResponse.setCount(result.size());
        return restResponse;
    }

    /**
     * 编制报告处新增项目接口，新增的项目项目类型为分包项目
     *
     * @param project 修改的项目数据
     * @return 保存的项目
     */
    @ApiOperation(value = "编制报告处新增项目", notes = "编制报告处新增项目")
    @PostMapping("/subcontractedProject")
    public RestResponse<DtoProject> createSubcontractedProject(@RequestBody DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.createSubcontractedProject(project));
        return restResponse;
    }

    /**
     * 更新编制报告处的项目
     *
     * @param project 编辑的项目
     * @return 完成编辑的项目
     */
    @ApiOperation(value = "编制报告处修改项目", notes = "编制报告处修改项目")
    @PutMapping("/subcontractedProject")
    public RestResponse<DtoProject> updateSubcontractedProject(@RequestBody DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.updateSubcontractedProject(project));
        return restResponse;
    }

    /**
     * 复制项目(带次数，实际项目不使用，只是为了方便测试组造数据使用)
     *
     * @param dto 接收字段（projectId，samplingTime）
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复制项目", notes = "复制项目")
    @PostMapping(path = "/test/copy")
    public RestResponse<Boolean> copyWithTimes(@RequestBody DtoReceiveSampleRecord dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.copyProject(dto.getProjectCode(), dto.getSamplingTime(), dto.getCopyTimes());
        restResponse.setData(true);
        return restResponse;
    }

    //endregion

    /**
     * 导入订单方案
     *
     * @param dto 接收字段（DetailIds，ProjectId）
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "导入订单方案", notes = "导入订单方案")
    @PostMapping(path = "/matchOrderScheme")
    public RestResponse<Boolean> matchOrderScheme(@RequestBody DtoQuotationDetail dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.matchOrderScheme(dto.getDetailIds(), dto.getProjectId());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 导入订单方案-按点位导入
     *
     * @param dto 接收字段（DetailIds，ProjectId）
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "导入订单方案-按点位导入", notes = "导入订单方案-按点位导入")
    @PostMapping(path = "/matchOrderSchemeFolder")
    public RestResponse<Boolean> matchOrderSchemeFolder(@RequestBody DtoQuotationDetail dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.matchOrderSchemeFolder(dto.getDetailIds(), dto.getProjectId(),dto.getFilterList());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 下载合并PDf文档
     *
     * @param criteria 参数容器
     * @return RestResponse<Map < St r ing, O b j ect>>
     */
    @ApiOperation(value = "下载合并PDf文档", notes = "下载合并PDf文档")
    @GetMapping(path = "/mergePDF")
    public RestResponse<String> findDocByProjectId(DocumentCriteria criteria, HttpServletResponse response) throws IOException {
        RestResponse<String> restResponse = new RestResponse<>();
        criteria.setIsRemoting(false);
        restResponse.setData(proService.downLoadPDF(criteria, response));
        return restResponse;
    }

    /**
     * 下载合并PDf文档【返回文档路径】
     *
     * @param criteria 参数容器
     * @return RestResponse<Map < St r ing, O b j ect>>
     */
    @ApiOperation(value = "下载合并PDf文档【返回文档路径】", notes = "下载合并PDf文档【返回文档路径】")
    @GetMapping(path = "/mergePDF/remoting")
    public RestResponse<String> downLoadWithEntity(DocumentCriteria criteria, HttpServletResponse response) throws IOException {
        RestResponse<String> restResponse = new RestResponse<>();
        criteria.setIsRemoting(true);
        restResponse.setData(proService.downLoadPDF(criteria, response));
        return restResponse;
    }

    /**
     * 获取所有的历史项目监测目的
     *
     * @return RestResponse<List < String>>
     */
    @ApiOperation(value = "获取所有的历史项目监测目的", notes = "获取所有的历史项目监测目的")
    @GetMapping("/history/monitorPurp")
    public RestResponse<List<String>> findHistory() {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.findHistoryMsg());
        return restResponse;
    }

//    /**
//     * 获取所有的历史项目监测目的
//     *
//     * @return RestResponse<List<String>>
//     */
//    @ApiOperation(value = "获取所有的历史项目监测目的", notes = "获取所有的历史项目监测目的")
//    @PostMapping("/sig")
//    public RestResponse<Boolean> sig() throws Exception {
//        RestResponse<Boolean> restResponse = new RestResponse<>();
//        signatureService.siqqfz();
//        return restResponse;
//    }


    /**
     * 导出解析图谱文件zip
     *
     * @param projectId 项目id
     * @return RestResponse<Map < St r ing, O b j ect>>
     */
    @ApiOperation(value = "导出解析图谱文件zip", notes = "导出解析图谱文件zip")
    @GetMapping(path = "/exportAtlasZip")
    public RestResponse<String> exportAtlasZip(@RequestParam(name = "projectId") String projectId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(proService.exportAtlasZip(projectId, response));
        return restResp;
    }

    /**
     * 获取提交信息
     *
     * @param objectId     objId
     * @param restrictType 类型
     * @param status       状态
     * @return RestResponse<List < DtoSubmitRestrictVo>>
     */
    @ApiOperation(value = "获取项目文档", notes = "获取项目文档")
    @GetMapping(path = "/restrictMsgById")
    public RestResponse<List<DtoSubmitRestrictVo>> submitMsgById(@RequestParam(name = "objectId") String objectId,
                                                                 @RequestParam(name = "restrictType") String restrictType,
                                                                 @RequestParam(name = "status") String status) {
        RestResponse<List<DtoSubmitRestrictVo>> restResponse = new RestResponse<>();
        restResponse.setData(service.submitMsgList(objectId, restrictType, status));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 获取提交信息
     *
     * @param objectIds    objId
     * @param restrictType 类型
     * @param status       状态
     * @return RestResponse<List < DtoSubmitRestrictVo>>
     */
    @ApiOperation(value = "获取项目文档", notes = "获取项目文档")
    @GetMapping(path = "/restrictMsgByIds")
    public RestResponse<List<DtoSubmitRestrictVo>> submitMsgByIds(@RequestParam(name = "objectIds") String[] objectIds,
                                                                  @RequestParam(name = "restrictType") String restrictType,
                                                                  @RequestParam(name = "status") String status) {
        RestResponse<List<DtoSubmitRestrictVo>> restResponse = new RestResponse<>();
        restResponse.setData(service.submitMsgList(Arrays.asList(objectIds), restrictType, status));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 复制方案或者更新方案时检查方案中测试项目
     *
     * @param map 传输实体
     * @return 提示信息
     */
    @ApiOperation(value = "复制方案或者更新方案时检查方案中测试项目", notes = "复制方案或者更新方案时检查方案中测试项目")
    @PostMapping("/checkTest")
    public RestResponse<Void> checkTest(@RequestBody Map<String, Object> map) {
        RestResponse<Void> restResp = new RestResponse<>();
        service.checkTest(map);
        return restResp;
    }

    /**
     * 电子表单推送至移动端签名模块
     *
     * @param documentId 附件id
     * @return 提示信息
     */
    @ApiOperation(value = "电子表单推送至移动端签名模块", notes = "电子表单推送至移动端签名模块")
    @PostMapping("/pushSign/{documentId}")
    public RestResponse<Void> pushSign(@PathVariable("documentId") String documentId) {
        RestResponse<Void> restResp = new RestResponse<>();
        service.pushSign(documentId);
        return restResp;
    }

    /**
     * 获取项目中样品的最早采样日期
     *
     * @param projectId 项目id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "获取项目中样品的最早采样日期", notes = "获取项目中样品的最早采样日期")
    @GetMapping(path = "/getEarliestSamplingTime")
    public RestResponse<String> getSamplingTime(String projectId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.getEarliestSamplingTime(projectId));
        return restResponse;
    }

    /**
     * 获取监管平台合同列表
     * @param req 参数
     * @return 结果
     */
    @ApiOperation(value = "获取数据", notes = "获取数据")
    @GetMapping("/platform/contract")
    public RestResponse<List<Map<String, Object>>> query(@RequestParam Map<String,String> req) {
        RestResponse<List<Map<String, Object>>> response = new RestResponse<>();
        List<Map<String, Object>> data = service.queryPlatformContract(req,response);
        response.setData(data);
        return response;
    }

    /**
     * 更新外部送样单
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "更新外部送样单", notes = "更新外部送样单")
    @PostMapping(path = "/updateOutReceiveRecord")
    public RestResponse<Void> updateOutReceiveRecord(@RequestBody @Validated DtoReceiveSampleRecordTemp dto) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.updateOutReceiveRecord(dto);
        return restResponse;
    }


    /**
     * 获取项目下样品是否全部采测分包
     *
     * @param projectId 项目id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "获取项目下样品是否全部采测分包", notes = "获取项目下样品是否全部采测分包")
    @GetMapping(path = "/isAllOutsourcing")
    public RestResponse<Boolean> getIsAllOutsourcing(String projectId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.getIsAllOutsourcing(projectId));
        return restResponse;
    }
}