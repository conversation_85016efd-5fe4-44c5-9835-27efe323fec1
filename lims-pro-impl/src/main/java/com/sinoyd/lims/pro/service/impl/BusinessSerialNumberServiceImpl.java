package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.service.SerialNumberConfigService;
import com.sinoyd.lims.pro.dto.DtoBusinessSerialNumber;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.BusinessSerialNumberRepository;
import com.sinoyd.lims.pro.service.BusinessSerialNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务流水号业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/2/29
 */
@Service
@SuppressWarnings({"unchecked"})
public class BusinessSerialNumberServiceImpl extends BaseJpaServiceImpl<DtoBusinessSerialNumber, String, BusinessSerialNumberRepository>
        implements BusinessSerialNumberService {

    private RedisTemplate redisTemplate;

    private SerialNumberConfigService serialNumberConfigService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void clearBusinessSerialNumber(EnumPRO.EnumLogObjectType em, Collection<String> businessIds, String redisKey) {
        List<DtoBusinessSerialNumber> serialNumberList = repository.findByBusinessTypeAndBusinessIdIn(em.name(), businessIds);

        if (StringUtils.isNotNullAndEmpty(serialNumberList)) {
            //删除
            super.delete(serialNumberList);
            //清除编号后需要对 serialNumberType 进行更新处理
            updateSerialNumberType(serialNumberList, redisKey);
        }

    }

    /**
     * 更新流水号表流水号
     *
     * @param serialNumberList 业务编号实体
     * @param redisKey         待清除的redis key
     */
    protected void updateSerialNumberType(List<DtoBusinessSerialNumber> serialNumberList, String redisKey) {
        Map<String, List<DtoBusinessSerialNumber>> groupMap = serialNumberList.stream().collect(Collectors.groupingBy(DtoBusinessSerialNumber::getSerialNumberType));
        List<DtoSerialNumberConfig> existedSerialNumberConfigList = serialNumberConfigService.findBySerialNumberType(groupMap.keySet());
        List<DtoBusinessSerialNumber> existedBusinessSerialNumberList = repository.findBySerialNumberTypeIn(groupMap.keySet());

        List<DtoSerialNumberConfig> updateSerialNumberConfigList = new ArrayList<>();
        List<String> deletedSerialNumberConfigIds = new ArrayList<>();
        for (Map.Entry<String, List<DtoBusinessSerialNumber>> entry : groupMap.entrySet()) {
            String serialNumberType = entry.getKey();
            List<DtoBusinessSerialNumber> value = entry.getValue();
            Optional<DtoSerialNumberConfig> optionalDtoSerialNumberConfig = existedSerialNumberConfigList.stream()
                    .filter(s -> serialNumberType.equals(s.getSerialNumberType())).findFirst();
            if (optionalDtoSerialNumberConfig.isPresent()) {
                List<DtoBusinessSerialNumber> list = existedBusinessSerialNumberList.stream().filter(p -> p.getSerialNumberType().equals(serialNumberType)).collect(Collectors.toList());
                if (StringUtil.isEmpty(list)) {
                    deletedSerialNumberConfigIds.add(optionalDtoSerialNumberConfig.get().getId());
                } else {
                    list.sort(Comparator.comparing(DtoBusinessSerialNumber::getSerialNumber, Comparator.reverseOrder()));
                    int maxSn = list.get(0).getSerialNumber();
                    optionalDtoSerialNumberConfig.get().setPara1(String.valueOf(maxSn));
                    updateSerialNumberConfigList.add(optionalDtoSerialNumberConfig.get());
                }
                if (redisTemplate.opsForValue().get(redisKey) != null) {
                    redisTemplate.delete(redisKey);
                }
                //样品编号目前有还有按 serialNumberType+机构id的形式进行redis换成，需要清除
                String sampleCodeKey = serialNumberType + PrincipalContextUser.getPrincipal().getOrgId();
                if (redisTemplate.opsForValue().get(sampleCodeKey) != null) {
                    redisTemplate.delete(sampleCodeKey);
                }
            }
        }

        if (StringUtil.isNotEmpty(updateSerialNumberConfigList)) {
            serialNumberConfigService.update(updateSerialNumberConfigList);
        }
        if (StringUtil.isNotEmpty(deletedSerialNumberConfigIds)) {
            serialNumberConfigService.logicDeleteById(deletedSerialNumberConfigIds);
        }
    }

    @Autowired
    @Lazy
    public void setSerialNumberConfigService(SerialNumberConfigService serialNumberConfigService) {
        this.serialNumberConfigService = serialNumberConfigService;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

}