package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ModuleActionModel;
import com.sinoyd.boot.frame.sys.service.IRolePermissionService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.service.AnalyzeItemSortDetialService;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.DetailDataCriteria;
import com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoDetailDataColumn;
import com.sinoyd.lims.pro.dto.customer.DtoMonitorData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.DataEvaluateService;
import com.sinoyd.lims.pro.service.DetailDataService;
import com.sinoyd.lims.pro.service.LogForDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;


/**
 * 详细数据操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
@Service
@Slf4j
public class DetailDataServiceImpl extends BaseJpaServiceImpl<DtoDetailData, String, DetailDataRepository> implements DetailDataService {
    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private AnalyzeItemSortDetialService analyzeItemSortDetialService;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Autowired
    private EvaluationRecordRepository evaluationRecordRepository;

    @Autowired
    private EvaluationValueRepository evaluationValueRepository;

    @Autowired
    private DataEvaluateService dataEvaluateService;

    @Autowired
    private DetailAnalyseDataRepository detailAnalyseDataRepository;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private IRolePermissionService rolePermissionService;

    @Autowired
    private AreaService areaService;

    private CodeService codeService;


    /**
     * 根据样品id集合返回详细数据
     *
     * @param sampleIds 样品id集合
     */
    @Override
    public DtoMonitorData getBySampleIds(List<String> sampleIds) {
        DetailDataCriteria detailDataCriteria = new DetailDataCriteria();
        detailDataCriteria.setSampleIds(sampleIds);
        return getBySampleIdSort(detailDataCriteria);
    }

    @Override
    public DtoMonitorData getBySampleIdSort(BaseCriteria baseCriteria) {
        DetailDataCriteria detailDataCriteria = (DetailDataCriteria) baseCriteria;
        List<String> sampleIds = detailDataCriteria.getSampleIds();
        String analyseItemSortId = detailDataCriteria.getAnalyseItemSortId();
        DtoMonitorData monitorData = new DtoMonitorData();
        if (sampleIds.size() > 0) {
            List<DtoAnalyseData> anaDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            //将list转为样品id的数据map
            Map<String, List<DtoAnalyseData>> anaDataMap = anaDatas.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));

            Map<String, Integer> analyzeItemSortMap = new HashMap<>();
            // 分析项目排序
            if (StringUtil.isNotEmpty(analyseItemSortId) && !UUIDHelper.GUID_EMPTY.equals(analyseItemSortId)) {
                List<DtoAnalyzeItemSortDetail> sortDetailList = analyzeItemSortDetialService.getSortDetailList(analyseItemSortId);
                analyzeItemSortMap = sortDetailList.stream().collect(Collectors.toMap(DtoAnalyzeItemSortDetail::getAnalyzeItemId, DtoAnalyzeItemSortDetail::getOrderNum));

            }

            //将list转为测试项目id的数据map
            Map<String, List<DtoAnalyseData>> testMap = anaDatas.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId));

            List<Map<String, Object>> testList = new ArrayList<>();
            //直接取数据上的测试项目信息，以免测试项目那边删除了以前的项目看不到当时的测试项目数据
            for (String testId : testMap.keySet()) {
                DtoAnalyseData test = testMap.get(testId).get(0);
                Map<String, Object> mapObject = new HashMap<>();
                mapObject.put("testId", test.getTestId());
                mapObject.put("redAnalyzeItemName", test.getRedAnalyzeItemName());
                mapObject.put("dimension", test.getDimension());
                mapObject.put("orderNum", analyzeItemSortMap.getOrDefault(test.getAnalyseItemId(), 0));
                testList.add(mapObject);
            }
            // 先根据分析项目排序，再根据分析项目名称排序
            testList.sort(Comparator.comparing((Map<String, Object> map) -> (Integer) map.get("orderNum"), Comparator.nullsFirst(Comparator.reverseOrder()))
                    .thenComparing(map -> (String) map.get("redAnalyzeItemName"), Comparator.nullsLast(Comparator.naturalOrder())));
            monitorData.setTest(testList);

            List<Map<String, Object>> analyseDatas = new ArrayList<>();
            List<DtoSample> samples = sampleRepository.findAll(sampleIds);
            for (DtoSample sample : samples) {
                Map<String, Object> mapObject = new HashMap<>();
                if (anaDataMap.containsKey(sample.getId())) {
                    this.setMapAnalyseDatas(mapObject, anaDataMap.get(sample.getId()));
                }
                DtoSampleType samType = sampleTypeService.findOne(sample.getSampleTypeId());
                if (StringUtil.isNotNull(samType)) {
                    sample.setSampleTypeName(samType.getTypeName());
                }
                mapObject.put("id", sample.getId());
                mapObject.put("code", sample.getCode());
                mapObject.put("inspectedEnt", sample.getInspectedEnt());
                mapObject.put("redFolderName", sample.getRedFolderName());
                mapObject.put("samplingTimeBegin", sample.getSamplingTimeBegin());
                mapObject.put("sampleTypeId", sample.getSampleTypeId());
                mapObject.put("sampleTypeName", sample.getSampleTypeName());
                analyseDatas.add(mapObject);
            }
            analyseDatas.sort(Comparator.comparing(DetailDataServiceImpl::comparingBySampleTypeName)
                    .thenComparing(DetailDataServiceImpl::comparingBySamplingTimeBegin, Comparator.reverseOrder())
                    .thenComparing(DetailDataServiceImpl::comparingByCode));
            monitorData.setAnalyseData(analyseDatas);
        }
        return monitorData;
    }

    @Override
    public Map<String, Object> findDetailDataByPage(PageBean<Object[]> pageBean, BaseCriteria baseCriteria) {
        DetailDataCriteria detailDataCriteria = (DetailDataCriteria) baseCriteria;
        Boolean isOverRed = detailDataCriteria.getIsOverRed();
        Boolean isCompleted = detailDataCriteria.getIsComplete();
        StringBuilder stringBuilder = new StringBuilder("select a.id,a.projectCode,a.projectTypeId,a.projectName,a.inceptTime,a.customerName,");
        stringBuilder.append("a.sampleCode,a.redFolderName,a.samplingTimeBegin,a.samplingTimeEnd,");
        stringBuilder.append("a.inspectedEnt,a.sampleTypeId,a.watchSpot,a.fixedPointId,a.folderCode,a.sampleFolderId,a.inspectedEntId");
        if (StringUtils.isNotNullAndEmpty(detailDataCriteria.getSelect())) {
            stringBuilder.append("," + detailDataCriteria.getSelect());
        }
        if (StringUtils.isNotNullAndEmpty(detailDataCriteria.getAreaId()) && !UUIDHelper.GUID_EMPTY.equals(detailDataCriteria.getAreaId())) {
            List<String> areaIds = new ArrayList<>();
            for (String areaId : detailDataCriteria.getAreaId()) {
                List<String> areaIdList = new ArrayList<>();
                areaIds.addAll(getAreaIds(areaIdList, areaId));
            }
            detailDataCriteria.setAreaIds(areaIds);
        }
        Map<String, Object> resultMap = new HashMap<>();
        pageBean.setEntityName("DtoDetailData a");
        pageBean.setSelect(stringBuilder.toString());
        comRepository.findByPage(pageBean, baseCriteria);
        //得到分页结束的样品id
        List<Object[]> detailDataList = pageBean.getData();

        List<String> sampleIds = detailDataList.stream().map(p -> (String) p[0]).distinct().collect(Collectors.toList());

        List<DtoAnalyseData> dtoAnalyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();

        List<String> sampleTypeIds = detailDataList.stream().map(p -> (String) p[11]).distinct().collect(Collectors.toList());
        //获取所有的点位id
        List<String> sampleFolderIds = detailDataList.stream().map(p -> (String) p[15]).distinct().collect(Collectors.toList());
        //根据点位id获取评价标准
        List<DtoEvaluationRecord> evaRecords = evaluationRecordRepository.findByObjectIdIn(sampleFolderIds);
        Map<String, List<DtoEvaluationRecord>> evaRecordsGroup = evaRecords.stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId));
        //获取所有的评价限值
        List<String> evaluationLevelIds = evaRecords.stream().map(DtoEvaluationRecord::getEvaluationLevelId).distinct().collect(Collectors.toList());
        evaluationLevelIds.removeIf(p -> UUIDHelper.GUID_EMPTY.equals(p));
        List<DtoEvaluationValue> evaValues = evaluationValueRepository.findByLevelIdIn(evaluationLevelIds);
        List<LinkedHashMap<String, Object>> analyseDataMapList = new ArrayList<>();
        List<DtoDetailDataColumn> dtoDetailDataTests = new ArrayList<>();
        if (sampleIds.size() > 0) { //寻找测试项目的列及参数的列
            List<DtoDetailAnalyseData> detailAnalyseDataList = getDetailAnalyseDataBySampleIds(sampleIds,
                    detailDataCriteria.getAnalyzeItemIds(), Boolean.FALSE);

            List<String> testIds = detailAnalyseDataList.stream().map(DtoDetailAnalyseData::getTestId).distinct().collect(Collectors.toList());
            //检测类型数据
            List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
            //参数列
            List<DtoDetailDataColumn> paramsColumns = new ArrayList<>();
            //测试项目列
            List<DtoDetailDataColumn> testColumns = new ArrayList<>();
            //参数数据
            List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
            if (StringUtil.isNotNull(detailDataCriteria.getParamsConfigIds()) && detailDataCriteria.getParamsConfigIds().size() > 0) {
                paramsConfigs = paramsConfigService.findAll(detailDataCriteria.getParamsConfigIds());
            }

            dtoDetailDataTests = getDetailDataColumns(testIds, detailDataCriteria.getSortId(), paramsConfigs, paramsColumns, testColumns, new ArrayList<>(), dtoAnalyseDataList);
            // 循环迭代获取JPA中查询返回的属性
            Iterator<Object[]> iterator = detailDataList.iterator();
            while (iterator.hasNext()) {
                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                Object obj = iterator.next();
                Object[] objects = (Object[]) obj;
                String sampleId = (String) objects[0];
                String projectName = (String) objects[3];
                String sampleCode = (String) objects[6];
                String redFolderName = (String) objects[7];
                //时间
                String samplingTimeBegin = DateUtil.dateToString((Date) objects[8], DateUtil.YEAR);
                String inspectedEnt = (String) objects[10];
                String sampleTypeId = (String) objects[11];
                String sampleFolderId = (String) objects[15];
                String inspectedEntId = (String) objects[16];
                DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst().orElse(null);
                String sampleTypeName = "";
                map.put("id", sampleId);
                map.put("projectName", projectName);
                map.put("code", sampleCode);
                map.put("inspectedEnt", inspectedEnt);
                map.put("redFolderName", redFolderName);
                map.put("samplingTimeBegin", samplingTimeBegin);
                map.put("sampleTypeId", sampleTypeId);
                map.put("inspectedEntId", inspectedEntId);
                if (StringUtil.isNotNull(sampleType)) {
                    sampleTypeName = sampleType.getTypeName();
                }
                map.put("sampleTypeName", sampleTypeName);
                Integer i = 17;
                for (DtoDetailDataColumn paramsColumn : paramsColumns) {
                    map.put(paramsColumn.getTestId(), StringUtils.isNotNullAndEmpty(objects[i]) ? objects[i] : "");
                    i++;
                }
                //详细数据
                List<DtoDetailAnalyseData> analyseDataList = detailAnalyseDataList.stream().filter(p -> p.getDetailDataId().equals(sampleId)).collect(Collectors.toList());
                List<DtoAnalyseData> analyseData2Sample = dtoAnalyseDataList.stream().filter(a -> a.getSampleId().equals(sampleId)).collect(Collectors.toList());
                for (DtoDetailDataColumn testColumn : testColumns) {
                    String testValue = detailDataCriteria.getNoneDataShow();
                    DtoDetailAnalyseData analyseData = analyseDataList.stream().filter(p -> p.getTestId().equals(testColumn.getTestId())).findFirst().orElse(null);
                    DtoAnalyseData anaData = analyseData2Sample.stream().filter(a -> a.getTestId().equals(testColumn.getTestId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(analyseData)) {
                        testValue = analyseData.getTestValue();
                        if (isOverRed) {
                            List<DtoEvaluationRecord> evaRecordsOfSampleFolder = evaRecordsGroup.get(sampleFolderId);
                            Optional<DtoEvaluationRecord> evaRecordOp = StringUtil.isNotEmpty(evaRecordsOfSampleFolder)
                                    ? evaRecordsOfSampleFolder.stream().filter(p -> testColumn.getTestId().equals(p.getTestId())).findFirst()
                                    : Optional.empty();
                            if (evaRecordOp.isPresent()) {
                                String evaluationLevelId = evaRecordOp.get().getEvaluationLevelId();
                                Optional<DtoEvaluationValue> evaValueOp = evaValues.stream().filter(p -> evaluationLevelId.equals(p.getLevelId()) && analyseData.getAnalyseItemId().equals(p.getAnalyzeItemId())).findFirst();
                                if (evaValueOp.isPresent()) {
                                    Boolean isPass = dataEvaluateService.getIsPass(evaValueOp.get(), testValue);
                                    if (!isPass) {
                                        testValue = "Red:" + testValue;
                                    }
                                }
                            }

                        }
                        testValue = dealTestValue(testValue, testColumn, anaData);
                        //是否过滤已检毕数据
                        if (isCompleted) {
                            if (!EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(analyseData.getDataStatus())
                                    && !EnumPRO.EnumAnalyseDataStatus.复核通过.getValue().equals(analyseData.getDataStatus())) {
                                testValue = "";
                            }
                        }
                    }
                    map.put(testColumn.getTestId(), testValue);
                }
                analyseDataMapList.add(map);
            }
        }
        resultMap.put("test", dtoDetailDataTests);
        resultMap.put("analyseData", analyseDataMapList);
        //获取样品总数及数据总数
        getSampleAnalyseDataCount(resultMap, detailDataCriteria);
        //获取企业相关数量
        getEnterpriseDataCount(resultMap, detailDataCriteria);
        return resultMap;
    }

    /**
     * 处理出征结果，如果和testColumn中量纲不一致，显示加上(量纲)
     *
     * @param testValue  数据
     * @param testColumn 测试项目数据
     * @param anaData    因子数据
     */
    private String dealTestValue(String testValue, DtoDetailDataColumn testColumn, DtoAnalyseData anaData) {
        if (anaData != null && !testColumn.getDimensionName().equals(anaData.getDimension()) && StringUtil.isNotEmpty(anaData.getDimension())) {
            return testValue + "(" + anaData.getDimension() + ")";
        }
        return testValue;
    }

    /**
     * 获取企业相关数量
     *
     * @param resultMap          返回数据对象映射
     * @param detailDataCriteria 查询条件
     */
    private void getEnterpriseDataCount(Map<String, Object> resultMap, DetailDataCriteria detailDataCriteria) {
        int inspectedEntCount = 0, entNumCount = 0;
        PageBean<Object[]> pageBean = new PageBean<>();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setEntityName("DtoDetailData a");
        pageBean.setSelect("select a.id,a.samplingTimeBegin,a.inspectedEntId");
        comRepository.findByPage(pageBean, detailDataCriteria);
        //得到分页结束的样品id
        List<Object[]> detailDataList = pageBean.getData();
        if (detailDataList.size() > 0) { //寻找测试项目的列及参数的列
            List<LinkedHashMap<String, Object>> analyseDataMapList = new ArrayList<>();
            for (Object[] objects : detailDataList) {
                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                map.put("id", objects[0]);
                map.put("samplingTimeBegin", objects[1]);
                map.put("inspectedEntId", objects[2]);
                analyseDataMapList.add(map);
            }
            Map<String, List<LinkedHashMap<String, Object>>> inspectedEntGroup = analyseDataMapList.stream().collect(Collectors.groupingBy(p -> p.get("inspectedEntId").toString()));
            //获取所有的企业数量
            inspectedEntCount = StringUtil.isNotEmpty(inspectedEntGroup) ? inspectedEntGroup.keySet().size() : 0;
            //获取企业次数
            for (String key : inspectedEntGroup.keySet()) {
                Map<String, List<LinkedHashMap<String, Object>>> samplingTimeBeginGroup = inspectedEntGroup.get(key).stream().collect(Collectors.groupingBy(p -> p.get("samplingTimeBegin").toString()));
                entNumCount += StringUtil.isNotEmpty(samplingTimeBeginGroup) ? samplingTimeBeginGroup.keySet().size() : 0;
            }

        }
        resultMap.put("inspectedEntCount", inspectedEntCount);
        resultMap.put("entNumCount", entNumCount);
    }

    /**
     * 获取样品总数及数据总数
     *
     * @param resultMap          返回数据对象映射
     * @param detailDataCriteria 查询条件对象
     */
    private void getSampleAnalyseDataCount(Map<String, Object> resultMap, DetailDataCriteria detailDataCriteria) {
        //样品数,分析数据数
        int sampleCount = 0, analyseDataCount = 0;
        PageBean<String> pageBean = new PageBean<>();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setEntityName("DtoDetailData a");
        pageBean.setSelect("select a.id");
        comRepository.findByPage(pageBean, detailDataCriteria);
        //得到分页结束的样品id
        List<String> allSampleIdList = pageBean.getData();
        if (allSampleIdList.size() > 0) { //寻找测试项目的列及参数的列
            List<DtoDetailAnalyseData> detailAnalyseDataList = getDetailAnalyseDataBySampleIds(allSampleIdList, detailDataCriteria.getAnalyzeItemIds(),
                    detailDataCriteria.getIsComplete());
            sampleCount = (int) detailAnalyseDataList.stream().map(DtoDetailAnalyseData::getDetailDataId).distinct().count();
            if (detailDataCriteria.getIsComplete()) {
                detailAnalyseDataList.removeIf(p -> (!EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(p.getDataStatus())
                        && !EnumPRO.EnumAnalyseDataStatus.复核通过.getValue().equals(p.getDataStatus())));
            }
            analyseDataCount = detailAnalyseDataList.size();
        }
        resultMap.put("sampleCount", sampleCount);
        resultMap.put("analyseDataCount", analyseDataCount);
    }

    /**
     * 获取分析数据
     *
     * @param analyseData 详细数据
     * @param testValue   赋值前字符串
     * @return 复制后的字符串
     */
    protected String getTestValue(DtoAnalyseData analyseData, String testValue) {
        if (StringUtil.isNotNull(analyseData)) {
            testValue = getTestValue(analyseData, testValue, new ArrayList<>(), false);
        }
        return testValue;
    }

    /**
     * 获取分析数据（带权限控制）
     *
     * @param analyseData 分析数据
     * @param testVal     数据结果
     * @param isAction    是否根据权限控制
     * @return 分析数据结果
     */
    protected String getTestValue(DtoAnalyseData analyseData, String testVal, List<ModuleActionModel> actionPermission, Boolean isAction) {
        if (isAction) {
            if (actionPermission.size() == 0) {
                //当前登录人员
                String userId = PrincipalContextUser.getPrincipal().getUserId();
                //当前登录人员的所有权限
                actionPermission = rolePermissionService.findActionPermission(userId);
            }
            //判断当前登陆人员是否有数据查看的权限
            Optional<ModuleActionModel> anaDetailAction = actionPermission.stream().filter(p -> LimConstants.DetailDataConstants.ANALYSE_DATA_SHOW.equals(p.getActionCode())).findFirst();
            testVal = analyseData.getTestValue();
            if (anaDetailAction.isPresent()) {
                //判断当前登陆人员是否有权限查看未审核的数据
                Optional<ModuleActionModel> anaAuditDetailShow = actionPermission.stream().filter(p -> LimConstants.DetailDataConstants.ANALYSE_AUDIT_SHOW.equals(p.getActionCode())).findFirst();
                //没有权限的情况
                if (!anaAuditDetailShow.isPresent()) {
                    //判断当前数据是否审核完成
                    if (StringUtil.isNotNull(analyseData)) {
                        if (!analyseData.getIsDataEnabled()) {
                            testVal = LimConstants.DetailDataConstants.ENCRYPT_STRING;
                        }
                    }
                }
            } else {
                testVal = LimConstants.DetailDataConstants.ENCRYPT_STRING;
            }
            return testVal;
        } else {
            return analyseData.getTestValue();
        }
    }

    /**
     * 过滤数据
     *
     * @param analyseData 分析数据
     * @param testValue   分析值
     */
    protected String filterData(DtoAnalyseData analyseData, String testValue) {
        return testValue;
    }

    @Override
    public Map<String, Object> findProjectDetailData(BaseCriteria baseCriteria) {
        DetailDataProjectCriteria detailDataProjectCriteria = (DetailDataProjectCriteria) baseCriteria;
        if (detailDataProjectCriteria.getIsShowQc()) {
            if (StringUtil.isNotEmpty(detailDataProjectCriteria.getProjectId()) && !UUIDHelper.GUID_EMPTY.equals(detailDataProjectCriteria.getProjectId())) {
                List<String> recIds = receiveSampleRecordRepository.findByProjectId(detailDataProjectCriteria.getProjectId()).stream()
                        .map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
                detailDataProjectCriteria.setReceiveIds(recIds);
            }
        }
        //当前登录人员
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        //当前登录人员的所有权限
        List<ModuleActionModel> actionPermission = rolePermissionService.findActionPermission(userId);
        //是否只显示已检毕的数据
        Boolean isCompleted = detailDataProjectCriteria.getIsCompleted();
        Boolean isAction = detailDataProjectCriteria.getIsAction();
        //StringBuilder stringBuilder = new StringBuilder("select a.id,b.projectCode,b.projectTypeId,b.projectName,b.inceptTime,b.customerName,");
        StringBuilder stringBuilder = new StringBuilder("select a.id,");
        stringBuilder.append("a.code,a.redFolderName,a.samplingTimeBegin,a.samplingTimeEnd,");
        stringBuilder.append("a.inspectedEnt,a.sampleTypeId,a.redAnalyzeItems,a.receiveId,a.inspectedEntId");
        stringBuilder.append(" from DtoSample a  where 1=1 ");
        Map<String, Object> resultMap = new HashMap<>();
        List<Object[]> dataList = comRepository.find(stringBuilder.toString(), baseCriteria);
        List<String> sampleIds = dataList.stream().map(p -> (String) p[0]).distinct().collect(Collectors.toList());
        List<String> sampleTypeIds = dataList.stream().map(p -> (String) p[6]).distinct().collect(Collectors.toList());
        List<String> receiveIds = dataList.stream().map(p -> (String) p[8]).distinct().collect(Collectors.toList());
        List<LinkedHashMap<String, Object>> analyseDataMapList = new ArrayList<>();
        List<DtoDetailDataColumn> dtoDetailDataTests = new ArrayList<>();
        if (sampleIds.size() > 0) { //寻找测试项目的列及参数的列
            DetailDataProjectCriteria criteria = (DetailDataProjectCriteria) baseCriteria;
            //如果需要显示分析数据
            List<DtoAnalyseData> detailAnalyseDataList = new ArrayList<>();
            List<String> testIds = new ArrayList<>();
            if (StringUtil.isNotNull(criteria.getIsShowAnaData()) && criteria.getIsShowAnaData()) {
                detailAnalyseDataList = getAnalyseDataBySampleIds(sampleIds);
                testIds = detailAnalyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            }
            //获取所有的测试项目数据id
//            List<DtoAnalyseData> anaDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdIn(sampleIds) : new ArrayList<>();
            List<DtoAnalyseData> anaDataList = detailAnalyseDataList;
            //测试项目数据Map
            Map<String, DtoAnalyseData> anaDataMap = anaDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, dto -> dto));
            //获取所有的工作单数据
            List<String> workSheetFolderIds = detailAnalyseDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
            List<DtoWorkSheetFolder> workSheetFolders = StringUtil.isNotEmpty(workSheetFolderIds) ? workSheetFolderRepository.findByIdIn(workSheetFolderIds) : new ArrayList<>();
            //工作单编号Map
            Map<String, String> workSheetCodeMap = workSheetFolders.stream().collect(Collectors.toMap(DtoWorkSheetFolder::getId, DtoWorkSheetFolder::getWorkSheetCode));
            //获取所有送样单数据
            List<DtoReceiveSampleRecord> receiveSampleRecords = StringUtil.isNotEmpty(receiveIds) ? receiveSampleRecordRepository.findAll(receiveIds) : new ArrayList<>();
            //送样单编号Map
            Map<String, String> recordCodeMap = receiveSampleRecords.stream().collect(Collectors.toMap(DtoReceiveSampleRecord::getId, DtoReceiveSampleRecord::getRecordCode));
            //获取所有测试项目id集合
            List<String> anaDataIds = detailAnalyseDataList.stream().map(DtoAnalyseData::getId).distinct().collect(Collectors.toList());
            //获取数据的所有日志
//            List<DtoLogForData> dataLogs = logForDataRepository.findByObjectIdIn(anaDataIds);
            LogForDataService logForDataService = SpringContextAware.getBean(LogForDataService.class);
            //多线程获取数据日志
            List<DtoLogForData> dataLogs = new ArrayList<>();
            // 是否展示修改日志
            boolean isShowChangeLog = getIsShowChangeLog();
            if(isShowChangeLog) {
                List<Future<List<DtoLogForData>>> logForDataResultList = new ArrayList<>();
                List<String> list = null;
                final int batchSize = 150;
                for (String anaDataId : anaDataIds) {
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    if (list.size() < batchSize) {
                        list.add(anaDataId);
                    } else if (list.size() == batchSize) {
                        //多线程处理排序
                        logForDataResultList.add(logForDataService.getLogForData(list));
                        list = new ArrayList<>();
                        list.add(anaDataId);
                    }
                }
                //如果存在最后一批样，需要单独去排序处理
                if (StringUtil.isNotEmpty(list)) {
                    logForDataResultList.add(logForDataService.getLogForData(list));
                }
                //处理多线程处理的结果
                try {
                    for (Future<List<DtoLogForData>> logForDataResult : logForDataResultList) {
                        while (true) {
                            if (logForDataResult.isDone() && !logForDataResult.isCancelled()) {
                                dataLogs.addAll(logForDataResult.get());
                                break;
                            } else {
                                //防止CPU高速轮询被耗空
                                Thread.sleep(1);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new BaseException("......多线程处理分析数据日志出错......");
                }
            }
            //将日志按照测试项目数据id分组
            Map<String, List<DtoLogForData>> logGroupMap = dataLogs.stream().collect(Collectors.groupingBy(DtoLogForData::getObjectId));

            //检测类型id
            String sId = criteria.getSampleTypeId();
            List<DtoParamsConfig> paramsConfigs = new ArrayList<>();

            //参数数据
            List<DtoParamsData> paramsDataList = new ArrayList<>();
            if (StringUtil.isNotNull(criteria.getIsShowParamData())
                    && criteria.getIsShowParamData() &&
                    StringUtils.isNotNullAndEmpty(sId)) {
                paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sId, testIds);//paramsConfigService.findBySampleTypeId(sId);

                paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(sampleIds, EnumPRO.EnumParamsDataType.样品.getValue());

            }
            //检测类型数据
            List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
            //测试项目列
            List<DtoDetailDataColumn> testColumns = new ArrayList<>();
            dtoDetailDataTests = getDetailDataColumns(testIds, detailDataProjectCriteria.getSort(), paramsConfigs, new ArrayList<>(), testColumns, paramsDataList, anaDataList);
            // 循环迭代获取JPA中查询返回的属性
            Iterator<Object[]> iterator = dataList.iterator();
            while (iterator.hasNext()) {
                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                Object obj = iterator.next();
                Object[] objects = (Object[]) obj;
                String sampleId = (String) objects[0];
                String sampleCode = (String) objects[1];
                String redFolderName = (String) objects[2];
                String samplingTimeBegin = DateUtil.dateToString((Date) objects[3], DateUtil.YEAR);//时间
                String inspectedEnt = (String) objects[5];
                String sampleTypeId = (String) objects[6];
                String redAnalyzeItems = (String) objects[7];
                String receiveId = (String) objects[8];
                String inspectedEntId = (String) objects[9];
                DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst().orElse(null);
                String sampleTypeName = "";
                map.put("id", sampleId);
                map.put("code", sampleCode);
                map.put("inspectedEnt", inspectedEnt);
                map.put("inspectedEntId", inspectedEntId);
                map.put("redFolderName", redFolderName);
                map.put("samplingTimeBegin", samplingTimeBegin);
                map.put("sampleTypeId", sampleTypeId);
                if (StringUtil.isNotNull(sampleType)) {
                    sampleTypeName = sampleType.getTypeName();
                }
                map.put("sampleTypeName", sampleTypeName);
                map.put("redAnalyzeItems", redAnalyzeItems);
                //详细数据
                List<DtoAnalyseData> analyseDataList = detailAnalyseDataList.stream().filter(p -> p.getSampleId().equals(sampleId)).collect(Collectors.toList());
                List<DtoAnalyseData> anaData2Sample = anaDataList.stream().filter(a -> a.getSampleId().equals(sampleId)).collect(Collectors.toList());
                for (DtoDetailDataColumn testColumn : dtoDetailDataTests) {
                    String testValue = "";
                    //分析数据
                    DtoAnalyseData analyseData = analyseDataList.stream().filter(p -> p.getTestId().equals(testColumn.getTestId())).findFirst().orElse(null);
                    if (analyseData != null) {
                        testValue = getTestValue(analyseData, testValue, actionPermission, true);
                    }
                    DtoAnalyseData anaData = anaData2Sample.stream().filter(a -> a.getTestId().equals(testColumn.getTestId())).findFirst().orElse(null);
                    testValue = dealTestValue(testValue, testColumn, anaData);
                    //参数数据
                    DtoParamsData paramsData = paramsDataList.stream().filter(p -> (p.getParamsConfigId() + "_" + p.getGroupId()).equals(testColumn.getTestId())
                            && p.getObjectId().equals(sampleId)).findFirst().orElse(null);
                    if (StringUtil.isNotNull(paramsData)) {
                        testValue = paramsData.getParamsValue();
                    }
                    if (isCompleted && StringUtil.isNotNull(analyseData)) {
                        if (!EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(analyseData.getDataStatus())
                                && !EnumPRO.EnumAnalyseDataStatus.复核通过.getValue().equals(analyseData.getDataStatus())) {
                            testValue = "";
                        }
                    }
                    if (analyseData != null) {
                        testValue = filterData(analyseData, testValue);
                    }
                    map.put(testColumn.getTestId(), testValue);
                    if (criteria.getIsShowAnaData() && isShowChangeLog) {
                        //处理日志显示
                        Map<String, Object> logMsgMap = new HashMap<>();
                        //是否修改数据
                        Boolean isChange = false;
                        //需要显示的日志数据
                        List<String> comments = new ArrayList<>();
                        //单号
                        String recordCode = "";
                        if (StringUtil.isNotNull(analyseData)) {
                            String anaDataId = StringUtil.isNotNull(analyseData) ? analyseData.getId() : "";
                            DtoAnalyseData anaDataTemp = anaDataMap.getOrDefault(anaDataId, analyseData);
                            //工作单编号
                            String workSheetCode = StringUtil.isEmpty(workSheetCodeMap) ? "" : workSheetCodeMap.getOrDefault(analyseData.getWorkSheetFolderId(), "");
                            //送样单编号
                            String receiveRecordCode = StringUtil.isEmpty(recordCodeMap) ? "" : recordCodeMap.getOrDefault(receiveId, "");
                            //获取修改数据的日志
                            List<DtoLogForData> saveDataLogs = getDataLogsByType(logGroupMap, analyseData.getId(), EnumPRO.EnumLogOperateType.保存数据.name());
                            //获取数据审核的日志
                            List<DtoLogForData> auditDataLogs = getDataLogsByType(logGroupMap, analyseData.getId(), EnumPRO.EnumLogOperateType.审核数据.name());
                            //获取最新的一条数据修改日志
                            DtoLogForData firstSaveLog = saveDataLogs.stream().max(Comparator.comparing(DtoLogForData::getOperateTime)).orElse(null);
                            //获取最新的数据审核数据日志
                            DtoLogForData firstAuditLog = auditDataLogs.stream().max(Comparator.comparing(DtoLogForData::getOperateTime)).orElse(null);
                            //判断数据是否修改
                            if (StringUtil.isNotNull(firstSaveLog) && StringUtil.isNotNull(firstAuditLog)) {
                                if (!firstSaveLog.getRemark().equals(firstAuditLog.getRemark())) {
                                    isChange = true;
                                    //获取数据审核的时间
                                    Date auditTime = firstAuditLog.getOperateTime();
                                    //获取审核时间后的修改数据的日志
                                    comments = saveDataLogs.stream()
                                            .filter(p -> auditTime.compareTo(p.getOperateTime()) < 0)
                                            .sorted(Comparator.comparing(DtoLogForData::getOperateTime).reversed())
                                            .map(p -> p.getOperateTime() + " | " + p.getComment())
                                            .collect(Collectors.toList());
                                }
                            }
                            recordCode = anaDataTemp.getIsCompleteField() ? receiveRecordCode : workSheetCode;
                        }
                        logMsgMap.put("isChange", isChange);
                        logMsgMap.put("workSheetCode", recordCode);
                        logMsgMap.put("dataLogs", comments);
                        map.put(testColumn.getTestId() + "|logs", logMsgMap);
                    }
                }
                analyseDataMapList.add(map);
            }
        }
        if (StringUtil.isNotEmpty(analyseDataMapList)) {
            analyseDataMapList.sort(Comparator.comparing(p -> ((String) (((Map<String, Object>) p).get("samplingTimeBegin")))).reversed().thenComparing(p -> ((String) (((Map<String, Object>) p).get("code")))));
        }
        resultMap.put("test", dtoDetailDataTests);
        resultMap.put("analyseData", analyseDataMapList);
        return resultMap;
    }

    /**
     * 根据日志类型获取日志
     *
     * @param logForDataMap 日志字典数据
     * @param objectId      测试项目数据id
     * @param operateInfo   日志类型
     * @return 日志数据
     */
    private List<DtoLogForData> getDataLogsByType(Map<String, List<DtoLogForData>> logForDataMap, String objectId, String operateInfo) {
        List<DtoLogForData> logsOfObject = StringUtil.isNotEmpty(objectId) ? logForDataMap.getOrDefault(objectId, new ArrayList<>()) : new ArrayList<>();
        return StringUtil.isNotEmpty(logsOfObject) ? logsOfObject.stream().filter(p -> operateInfo.equals(p.getOperateInfo())).collect(Collectors.toList()) : new ArrayList<>();
    }

    /**
     * 写入map数据
     *
     * @param mapObject map对象
     * @param datas     写入的数据列表
     */
    private void setMapAnalyseDatas(Map<String, Object> mapObject, List<DtoAnalyseData> datas) {
        for (DtoAnalyseData data : datas) {
            mapObject.put(data.getTestId(), data.getTestValue());
        }
    }

    /**
     * 获取开关配置，是否展示数据修改日志
     *
     * @return 是否展示
     */
    private boolean getIsShowChangeLog() {
        boolean isShowChange = true;
        DtoCode code = codeService.findByCode(ProCodeHelper.PRO_IsShowChangeLog);
        if (StringUtil.isNotNull(code)) {
            if ("0".equals(code.getDictValue())) {
                isShowChange = false;
            }
        }
        return isShowChange;
    }


    /**
     * 获取相应的数据
     *
     * @param sampleIds     样品ids
     * @param isDataEnabled 出证数据
     * @return 返回相应的分数据
     */
    private List<DtoDetailAnalyseData> getDetailAnalyseDataBySampleIds(List<String> sampleIds, List<String> analyzeItemIds, Boolean isDataEnabled) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select a");
        stringBuilder.append(" from DtoDetailAnalyseData a  where 1=1 and a.isDeleted=0 ");
        if (StringUtil.isNotNull(isDataEnabled) && isDataEnabled) {
            stringBuilder.append(" and isDataEnabled = :isDataEnabled");
            values.put("isDataEnabled", isDataEnabled);
        }
        if (StringUtil.isNotNull(analyzeItemIds) && analyzeItemIds.size() > 0) {
            //按分析项目过滤想要的测试项目
            stringBuilder.append(" and analyseItemId in :analyzeItemIds");
            values.put("analyzeItemIds", analyzeItemIds);
        }
        stringBuilder.append(" and detailDataId in :sampleIds");
        values.put("sampleIds", sampleIds);
        List<DtoDetailAnalyseData> dataList = comRepository.find(stringBuilder.toString(), values);
        return dataList;
    }


    /**
     * 获取相应的数据
     *
     * @param sampleIds 样品ids
     * @return 返回相应的分数据
     */
    private List<DtoAnalyseData> getAnalyseDataBySampleIds(List<String> sampleIds) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.DtoAnalyseData(");
        stringBuilder.append("a.id,a.sampleId,a.testId,a.analyseItemId,a.testValue,");
        stringBuilder.append("a.testOrignValue,a.testValueD,a.testValueDstr,a.status,a.dataStatus,a.isDataEnabled,a.workSheetFolderId, a.dimension, a.isCompleteField)");
        stringBuilder.append(" from DtoAnalyseData a  where 1=1 and a.isDeleted=0 ");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            String orgId = PrincipalContextUser.getPrincipal().getOrgId();
            stringBuilder.append(" and orgId = :orgId");
            values.put("orgId", orgId);
        }
        stringBuilder.append(" and sampleId in :sampleIds");
        values.put("sampleIds", sampleIds);
        List<DtoAnalyseData> dataList = comRepository.find(stringBuilder.toString(), values);
        return dataList;
    }


    /**
     * 获取详细数据的列
     *
     * @param testIds       测试项目ids
     * @param sort          排序方式
     * @param paramsConfigs 参数对象
     * @param paramsColumns 参数列
     * @param testColumns   测试项目列
     * @return 返回详细数据的列
     */
    @Override
    public List<DtoDetailDataColumn> getDetailDataColumns(List<String> testIds, String sort, List<DtoParamsConfig> paramsConfigs,
                                                          List<DtoDetailDataColumn> paramsColumns, List<DtoDetailDataColumn> testColumns,
                                                          List<DtoParamsData> paramsDataList, List<DtoAnalyseData> analyseDataList) {
        List<DtoDetailDataColumn> dtoDetailDataTests = new ArrayList<>();
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        //获取监测类型数据
        List<String> sampleTypeIds = paramsConfigs.stream().map(DtoParamsConfig::getObjId).distinct().collect(Collectors.toList());
        //获取监测类型数据Map
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
        Map<String, String> sampleTypeNameMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
        List<DtoAnalyzeItemSortDetail> analyzeItemSortDetails = new ArrayList<>();

        //处理排序
        if (StringUtils.isNotNullAndEmpty(sort) && !sort.equals(UUIDHelper.GUID_EMPTY)) {
            analyzeItemSortDetails = analyzeItemSortDetialService.getSortDetailList(sort);
        }
//        List<DtoDimension> dtoDimensions = new ArrayList<>();
//        List<String> dimensionIds = testList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getDimensionId())
//                && !p.getDimensionId().equals(UUIDHelper.GUID_EMPTY)).map(DtoTest::getDimensionId).distinct().collect(Collectors.toList());
//        if (dimensionIds.size() > 0) {
//            dtoDimensions = dimensionService.findRedisByIds(dimensionIds);
//        }
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            String sampleTypeName = sampleTypeNameMap.getOrDefault(paramsConfig.getObjId(), "");
            if (StringUtil.isNotEmpty(paramsDataList)) {
                List<DtoParamsData> paramsData2Config = paramsDataList.stream().filter(d -> d.getParamsConfigId().equals(paramsConfig.getId())).collect(Collectors.toList());
                List<String> paramsConfigForGroup = paramsData2Config.stream().map(p -> p.getParamsConfigId() + "_" + p.getGroupId()).distinct().collect(Collectors.toList());
                for (String paramsGroup : paramsConfigForGroup) {
                    Optional<DtoParamsData> paramsData = paramsData2Config.stream().filter(d -> (d.getParamsConfigId() + "_" + d.getGroupId()).equals(paramsGroup)).findFirst();
                    if (paramsData.isPresent()) {
                        DtoDetailDataColumn dtoDetailDataColumn = new DtoDetailDataColumn();
                        dtoDetailDataColumn.setTestId(paramsGroup);
                        dtoDetailDataColumn.setRedAnalyzeItemName(paramsData.get().getParamsName() + (StringUtil.isNotEmpty(sampleTypeName) ? "【" + sampleTypeName + "】" : ""));
                        dtoDetailDataColumn.setDimensionName(paramsConfig.getDimension());
                        dtoDetailDataColumn.setOrderNum(paramsConfig.getOrderNum());
                        dtoDetailDataColumn.setRedAnalyzeMethodName("");
                        dtoDetailDataColumn.setRedCountryStandard("");
                        dtoDetailDataColumn.setRedYearSn("");
                        paramsColumns.add(dtoDetailDataColumn);
                    }
                }
            } else {
                DtoDetailDataColumn dtoDetailDataColumn = new DtoDetailDataColumn();
                dtoDetailDataColumn.setTestId(paramsConfig.getId());
                dtoDetailDataColumn.setRedAnalyzeItemName(paramsConfig.getAlias() + (StringUtil.isNotEmpty(sampleTypeName) ? "【" + sampleTypeName + "】" : ""));
                dtoDetailDataColumn.setDimensionName(paramsConfig.getDimension());
                dtoDetailDataColumn.setOrderNum(paramsConfig.getOrderNum());
                dtoDetailDataColumn.setRedAnalyzeMethodName("");
                dtoDetailDataColumn.setRedCountryStandard("");
                dtoDetailDataColumn.setRedYearSn("");
                paramsColumns.add(dtoDetailDataColumn);
            }

        }
        paramsColumns = paramsColumns.stream().sorted(Comparator.comparing(DtoDetailDataColumn::getOrderNum).reversed()).collect(Collectors.toList());
        dtoDetailDataTests.addAll(paramsColumns);
        for (DtoTest dtoTest : testList) {
            DtoDetailDataColumn dtoDetailDataColumn = new DtoDetailDataColumn();
            String analyzeItemId = dtoTest.getAnalyzeItemId();
            DtoAnalyzeItemSortDetail dtoAnalyzeItemSortDetail = analyzeItemSortDetails.stream().filter(p -> p.getAnalyzeItemId().equals(analyzeItemId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoAnalyzeItemSortDetail)) {
                dtoDetailDataColumn.setOrderNum(dtoAnalyzeItemSortDetail.getOrderNum());
            }
            String dimensionName = analyseDataList.stream().filter(a -> a.getTestId().equals(dtoTest.getId()) && StringUtils.isNotNullAndEmpty(a.getDimension())).collect(Collectors.groupingBy(DtoAnalyseData::getDimension, Collectors.counting())).entrySet()
                    .stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey).orElse("");
            dtoDetailDataColumn.setDimensionName(dimensionName);

//            String dimensionId = dtoTest.getDimensionId();
//            DtoDimension dtoDimension = dtoDimensions.stream().filter(p -> p.getId().equals(dimensionId)).findFirst().orElse(null);
//            if (StringUtil.isNotNull(dtoDimension)) {
//                dtoDetailDataColumn.setDimensionName(dtoDimension.getDimensionName());
//            }
            dtoDetailDataColumn.setTestId(dtoTest.getId());
            dtoDetailDataColumn.setRedAnalyzeItemName(dtoTest.getRedAnalyzeItemName());
            dtoDetailDataColumn.setRedAnalyzeMethodName(dtoTest.getRedAnalyzeMethodName());
            dtoDetailDataColumn.setRedCountryStandard(StringUtil.isNotEmpty(dtoTest.getRedCountryStandard()) ? dtoTest.getRedCountryStandard() : "");
            dtoDetailDataColumn.setRedYearSn(StringUtil.isNotEmpty(dtoTest.getRedYearSn()) ? dtoTest.getRedYearSn() : "");
            testColumns.add(dtoDetailDataColumn);
        }
        testColumns = testColumns.stream().sorted(Comparator.comparing(DtoDetailDataColumn::getOrderNum).reversed()
                .thenComparing(DtoDetailDataColumn::getRedAnalyzeItemName)
                .thenComparing(DtoDetailDataColumn::getRedAnalyzeMethodName)
                .thenComparing(DtoDetailDataColumn::getRedCountryStandard)
                .thenComparing(DtoDetailDataColumn::getRedYearSn)).collect(Collectors.toList());
        //最后的列名（测试项目、参数）
        dtoDetailDataTests.addAll(testColumns);
        return dtoDetailDataTests;
    }

    /**
     * 根据detailDataId集合查询所有的测试项目数据
     *
     * @param detailDataIds 样品id集合
     * @return 测试项目数据
     */
    @Override
    public List<DtoDetailAnalyseData> getDetailAnalyseDataByIds(List<String> detailDataIds) {
        return detailAnalyseDataRepository.findByDetailDataIdIn(detailDataIds);
    }

    private List<String> getAreaIds(List<String> areaIds, String areaId) {
        areaIds.add(areaId);
        List<DtoArea> list = areaService.findByParentId(areaId);
        if (StringUtil.isNotEmpty(list)) {
            for (DtoArea area : list) {
                getAreaIds(areaIds, area.getId());
            }
        }
        return areaIds;
    }

    /**
     * 按检测类型排序
     *
     * @param map 集合对象
     * @return 返回检测类型
     */
    private static String comparingBySampleTypeName(Map<String, Object> map) {
        return (String) map.get("sampleTypeName");
    }

    /**
     * 按样品编号排序
     *
     * @param map 集合对象
     * @return 返回样品编号
     */
    private static String comparingByCode(Map<String, Object> map) {
        return (String) map.get("code");
    }

    /**
     * 按采样日期排序
     *
     * @param map 集合对象
     * @return 返回采样日期
     */
    private static Date comparingBySamplingTimeBegin(Map<String, Object> map) {
        return (Date) map.get("samplingTimeBegin");
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}