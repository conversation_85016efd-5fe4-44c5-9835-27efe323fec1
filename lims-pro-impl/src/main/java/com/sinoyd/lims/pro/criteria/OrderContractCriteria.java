package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * OrderContract查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderContractCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 签订人员id
     */
    private String signPersonId;

    /**
     * 是否分包
     */
    private Boolean isHavingSub;

    /**
     * 签订日期开始时间
     */
    private String signStartTime;

    /**
     * 签订日期结束时间
     */
    private String signEndTime;

    /**
     * 合同编号，合同名称，甲方名称，分包机构名称
     */
    private String key;

    /**
     * 合同编号
     */
    private String code;

    /**
     * 合同编号，合同名称，甲方名称
     */
    private String newKey;

    /**
     * 上海监管平台甲方合同名称
     */
    private String shEntKey;

    /**
     * 是否已经推送
     */
    private Boolean isHavingPut;

    /**
     * 签订状态
     */
    private Integer contractStatus;

    @Override
    public String getCondition() {
        values.clear();
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.isDeleted = false");
        if (StringUtil.isNotEmpty(this.orderId)) {
            condition.append(" and a.orderId = :orderId");
            values.put("orderId", this.orderId);
        }
        if (StringUtil.isNotEmpty(this.signPersonId)) {
            condition.append(" and a.signPersonId like :signPersonId");
            values.put("signPersonId", "%" + this.signPersonId + "%");
        }
        if (this.isHavingSub != null) {
            condition.append(" and a.isHavingSub = :isHavingSub");
            values.put("isHavingSub", this.isHavingSub);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.contractCode like :key or a.contractName like :key or a.firstEntName like :key or a.subOrgs like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.code)){
            condition.append(" and a.contractCode like :code");
            values.put("code", "%" + this.code + "%");
        }
        if (StringUtil.isNotEmpty(this.newKey)) {
            condition.append(" and (a.contractCode like :newKey or a.contractName like :newKey or a.firstEntName like :newKey)");
            values.put("newKey", "%" + this.newKey + "%");
        }
        if (StringUtil.isNotEmpty(this.shEntKey)) {
            condition.append(" and a.shanghaiEntName like :shEntKey");
            values.put("shEntKey", "%" + this.shEntKey + "%");
        }
        //签订时间开始时间查询
        if (StringUtil.isNotEmpty(this.signStartTime)) {
            Date date = DateUtil.stringToDate(this.signStartTime, DateUtil.YEAR);
            condition.append(" and a.signDate >= :signStartTime");
            values.put("signStartTime", date);
        }
        //签订时间结束时间查询
        if (StringUtil.isNotEmpty(this.signEndTime)) {
            Date date = DateUtil.stringToDate(this.signEndTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.signDate < :signEndTime");
            values.put("signEndTime", date);
        }
        if (this.isHavingPut != null) {
            condition.append(" and a.isHavingPut = :isHavingPut");
            values.put("isHavingPut", this.isHavingPut);
        }
        //签订状态
        if (StringUtil.isNotNull(this.contractStatus)) {
            condition.append(" and a.contractStatus = :contractStatus");
            values.put("contractStatus", this.contractStatus);
        }
        return condition.toString();
    }
}
