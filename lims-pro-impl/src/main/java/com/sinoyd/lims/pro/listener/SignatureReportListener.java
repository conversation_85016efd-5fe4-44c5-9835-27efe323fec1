package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.SignatureService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.context.annotation.Primary;

import java.util.Collections;

/**
 * 报告签发工作流监听器
 *
 * <AUTHOR>
 * @version V1.0.0 2023/01/11
 * @since V100R001
 */
@Primary
@Slf4j
public class SignatureReportListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        String businessKey = delegateExecution.getProcessBusinessKey();
        //签名
        SignatureService signatureService = SpringContextAware.getBean(SignatureService.class);
        try {
            Integer type = EnumPRO.EnumSigType.maker.getValue();
            signatureService.sig(businessKey, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), type, "", DateUtil.nowTime("yyyy.MM.dd"));
            signatureService.sealChangePdf(businessKey);
        } catch (Exception ex) {
            log.error("报告签发流程监听执行失败！", ex);
            throw new BaseException(ex.getMessage());
        }
    }
}
