package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * ReceiveSampleRecord查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReceiveSampleRecordCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 当前人id
     */
    private String currentPersonId;

    /**
     * 关键字（受检方、送样单号、项目编号、项目名称、委托方）
     */
    private String key;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 模块编码
     */
    private String module = EnumLIM.EnumReceiveRecordModule.样品交接.getValue();

    /**
     * 处理状态
     */
    private Integer status = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 送样单状态
     */
    private Integer receiveStatus = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 送样单id列表
     */
    private List<String> receiveIdList;

    /**
     * 项目名称、项目编号、委托方
     */
    private String projectKey;

    /**
     * 送样单号、送样人
     */
    private String receiveKey;

    /**
     * 检测类型id集合
     */
    private List<String> sampleTypeIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and r.projectId = p.id");
//        condition.append(" and p.id = pl.projectId");
        condition.append(" and r.id = s.receiveId");

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and r.sendTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and r.sendTime < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        if (StringUtil.isNotEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            condition.append(" and r.projectId = :projectId");
            values.put("projectId", this.projectId);
        }
        if (StringUtil.isNotEmpty(this.currentPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.currentPersonId)) {
            //送样人和采样人均可见
            if (module.equals(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue())) {
                condition.append(" and (r.senderId = :currentPersonId or exists (select 1 from DtoSamplingPersonConfig sfc" +
                        " where r.id = sfc.objectId and sfc.objectType = :objectType and sfc.samplingPersonId = :currentPersonId))");
                values.put("objectType", EnumPRO.EnumSamplingType.送样单.getValue());
            } else {
                condition.append(" and s.currentPersonId = :currentPersonId");
            }

            values.put("currentPersonId", this.currentPersonId);
        }

        condition.append(" and s.module = :module");
        values.put("module", this.module);

        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.status)) {
            condition.append(" and s.status = :status");
            values.put("status", this.status);
        }
        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.receiveStatus)) {
            condition.append(" and r.receiveStatus = :receiveStatus");
            values.put("receiveStatus", this.receiveStatus);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (r.recordCode like :key or p.projectCode like :key or p.projectName like :key or p.customerName like :key or p.inspectedEnt like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and exists (select 1 from DtoSample sam where r.id = sam.receiveId and sam.isDeleted = 0 and sam.code like :sampleCode)");
            values.put("sampleCode", "%" + this.sampleCode + "%");
        }
        if (StringUtil.isNotEmpty(this.receiveIdList)) {
            condition.append(" and r.id in :receiveIdList ");
            values.put("receiveIdList", this.receiveIdList);
        }
        if (StringUtil.isNotEmpty(this.projectKey)) {
            condition.append(" and (p.projectCode like :projectKey or p.projectName like :projectKey or p.customerName like :projectKey)");
            values.put("projectKey", "%" + this.projectKey + "%");
        }
        if (StringUtil.isNotEmpty(this.receiveKey)) {
            condition.append(" and (r.recordCode like :receiveKey or r.senderName like :receiveKey)");
            values.put("receiveKey", "%" + this.receiveKey + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleTypeIds)) {
            condition.append(" and (");
            int i = 0;
            for (String sampleTypeId : sampleTypeIds) {
                if (sampleTypeIds.indexOf(sampleTypeId) != 0) {
                    condition.append(" or");
                }
                condition.append(" json_value(r.json,'$.sampleTypeIds') like :").append("sampleTypeId").append(i);
                values.put("sampleTypeId"+ i, "%" + sampleTypeId + "%");
                i++;
            }
            condition.append(")");
        }
        return condition.toString();
    }
}