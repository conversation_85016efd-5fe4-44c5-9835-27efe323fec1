package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoEvaluationRecordCopyVO;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.customer.DtoEvaluationBatchProcess;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.EvaluationAnalyzeResultCriteria;
import com.sinoyd.lims.pro.dto.DtoEvaluationRecord;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluationBatch;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluationResult;
import com.sinoyd.lims.pro.service.EvaluationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 评价记录服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
 @Api(tags = "示例: EvaluationRecord服务")
 @RestController
 @RequestMapping("api/pro/evaluationRecord")
 public class EvaluationRecordController extends BaseJpaController<DtoEvaluationRecord, String,EvaluationRecordService> {

    /**
     * 获取项目下的初始化配置列表
     * @param eva
     * @return RestResponse<List<DtoEvaluationValue>>
     */
    @ApiOperation(value = "获取项目下的初始化配置列表", notes = "获取项目下的初始化配置列表")
    @PostMapping(path = "/init")
    public RestResponse<List<DtoEvaluationValue>> findInitList(@RequestBody DtoEvaluationBatch eva) {
        RestResponse<List<DtoEvaluationValue>> restResponse = new RestResponse<>();
        List<DtoEvaluationValue> datas = service.getInitEvaluation(eva);
        restResponse.setRestStatus(datas.size() == 0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(datas);
        restResponse.setCount(datas.size());
        return restResponse;
    }

    /**
     * 批量保存评价记录
     * @param eva
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "批量保存评价记录", notes = "批量保存评价记录")
    @PostMapping(path = "/batch")
    public RestResponse<Boolean> createBatch(@RequestBody DtoEvaluationBatch eva) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.createBatch(eva);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 清空评价记录
     * @param folderIdList 点位id列表
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "清空评价记录", notes = "清空评价记录")
    @DeleteMapping(path = "/clear")
    public RestResponse<Boolean> clear(@RequestBody List<String> folderIdList) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.clearRecord(folderIdList);
        restResponse.setData(true);
        return restResponse;
    }

     /**
     * 获取点位评价记录
     * @param eva 实体
     * @return RestResponse<List<DtoEvaluationRecord>>
     */
    @ApiOperation(value = "获取点位评价记录", notes = "获取点位评价记录")
    @PostMapping(path = "/query")
    public RestResponse<List<DtoEvaluationRecord>> query(@RequestBody DtoEvaluationResult eva) {
        RestResponse<List<DtoEvaluationRecord>> restResponse = new RestResponse<>();
        List<DtoEvaluationRecord> records = service.query(eva);
        restResponse.setRestStatus(records.size() == 0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(records);
        restResponse.setCount(records.size());
        return restResponse;
     }

    /**
     * 保存评价记录
     * @param eva 评价记录列表
     * @return RestResponse<Boolean>
     */
     @ApiOperation(value = "保存评价记录", notes = "保存评价记录")
     @PostMapping
     public RestResponse<Boolean> create(@RequestBody DtoEvaluationResult eva) {
         RestResponse<Boolean> restResponse = new RestResponse<>();
         service.saveRecord(eva);
         restResponse.setData(true);
         return restResponse;
      }

    /**
     * 批处理评价记录
     * @param eva 批处理评价记录
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "批处理评价记录", notes = "批处理评价记录")
    @PostMapping(path = "/batchProcess")
    public RestResponse<Boolean> batchProcess(@RequestBody DtoEvaluationBatchProcess eva) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.batchProcess(eva);
        restResponse.setData(true);
        return restResponse;
    }

     /**
     * 获取评价详情及结论
     * @param evaluationRecord 实体
     * @return RestResponse<List<DtoEvaluationResult>>
     */
    @ApiOperation(value = "获取评价详情及结论", notes = "获取评价详情及结论")
    @PostMapping(path = "/result")
    public RestResponse<List<DtoEvaluationResult>> result(@RequestBody DtoEvaluationRecord evaluationRecord) {
        RestResponse<List<DtoEvaluationResult>> restResponse = new RestResponse<>();
        List<DtoEvaluationResult> results = service.getResult(evaluationRecord);
        restResponse.setRestStatus(results.size()==0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(results);
        restResponse.setCount(results.size());
        return restResponse;
     }

    /**
     * 获取评价分析结果
     * @param evaluationAnalyzeResultCriteria 查询参数实体
     * @return RestResponse<List<DtoEvaluationResult>>
     */
    @ApiOperation(value = "获取评价分析结果", notes = "获取评价分析结果")
    @PostMapping(path = "/analyzeResult")
    public RestResponse<Map<String, Object>> analyzeResult(@RequestBody EvaluationAnalyzeResultCriteria evaluationAnalyzeResultCriteria) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        Map<String, Object> results = service.getAnalyzeResult(evaluationAnalyzeResultCriteria);
        restResponse.setRestStatus(results.size()==0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(results);
        restResponse.setCount(results.size());
        return restResponse;
    }

    /**
     * 复制评价信息
     * @param evaluationAnalyzeResultCriteria 查询参数实体
     * @return RestResponse<List<DtoEvaluationResult>>
     */
    /**
     * 复制评价信息
     * @param eva 复制评价信息
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复制评价信息", notes = "复制评价信息")
    @PostMapping(path = "/copy")
    public RestResponse<Boolean> copy(@RequestBody DtoEvaluationRecordCopyVO eva) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.copy(eva);
        restResponse.setData(true);
        return restResponse;
    }
 }