package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.common.utils.WebServiceUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.annotate.RegulatoryPlatformField;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.FolderExtendService;
import com.sinoyd.lims.pro.service.OrderContractService;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.pro.util.RPJsonObjectUtil;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import javax.transaction.Transactional;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 上海监管平台远程接口策略基类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/29
 */
@Component
@Slf4j
public abstract class AbsRegulatoryPlatformRemote<T> {

    /**
     * 上海监管平台推送地址
     */
    @Value("${shanghaiAPI.url: unknown}")
    protected String url;

    /**
     * 上海监管平台账号
     */
    @Value("${shanghaiAPI.username: unknown}")
    protected String username;

    /**
     * 上海监管平台密码
     */
    @Value("${shanghaiAPI.password: unknown}")
    protected String password;

    protected DocumentRepository documentRepository;
    protected FilePathConfig filePathConfig;
    protected com.sinoyd.lims.pro.util.WebServiceUtil webServiceUtil;
    protected AreaService areaService;
    protected ProjectService projectService;
    protected ProjectContractRepository projectContractRepository;
    protected OrderContractRepository orderContractRepository;
    protected OrderContractService orderContractService;
    protected SampleFolderRepository sampleFolderRepository;
    protected FolderExtendService folderExtendService;
    protected SamplingFrequencyRepository samplingFrequencyRepository;
    protected SHSamplingPersonNewRepository shSamplingPersonNewRepository;
    protected SHSamplingInstrumentNewRepository shSamplingInstrumentNewRepository;
    protected PersonService personService;
    protected InstrumentService instrumentService;
    protected ReportRepository reportRepository;

    /**
     * 通用请求接口
     *
     * @param methodName    请求方法名
     * @param requestParams 请求参数
     * @return 请求结果
     */
    public JSONObject doRequest(String methodName, Map<String, Object> requestParams) {
        //加载请求体
        String soapBody = RPSoapBodyUtil.loadSoapBody(this.username, this.password, methodName, requestParams);
        //发送接口请求
        JSONObject result = doRequest(soapBody, loadSoapAction(methodName), methodName);
        //返回请求结果
        return getResponse(result, methodName);
    }


    /**
     * 查询分页数据
     *
     * @param criteria 查询条件
     * @param pageBean 分页数据
     * @return 分页数据
     */
    public List<T> findByPage(BaseCriteria criteria, PageBean<T> pageBean) {
        //获取请求体
        String soapBody = loadFindSoapBody(this.username, this.password, criteria, pageBean);
        //发送接口请求
        JSONObject response = doRequest(soapBody, loadSoapAction(queryMethodName()), queryMethodName());
        //处理请求结果
        List<T> list = parseResult(response, queryMethodName());
        //处理查询条件
        filterCriteria(list, criteria);
        //处理分页数据
        return loadPageData(list, pageBean);
    }

    /**
     * 监管平台推送
     *
     * @param pushVo 推送参数VO
     */
    @Transactional
    public void push(RegulatoryPlatformPushVO pushVo) {
        //推送数据校验
        validatePushData(pushVo);
        //初始化推送数据
        T pushData = initPushData(pushVo);
        //处理推送附件
        handlePushDoc(pushVo, pushData);
        //获取请求体
        String soapBody = loadPushSoapBody(this.username, this.password, pushData);
        //远程推送请求
        JSONObject response = doRequest(soapBody, loadSoapAction(insertMethodName()), insertMethodName());
        // (处理结果)
        handlePushResult(pushVo, response);
    }

    /**
     * 对请求结果进行数据处理
     *
     * @param pushVo 推送参数
     * @param result 推送结果
     */
    @Transactional
    public void handlePushResult(RegulatoryPlatformPushVO pushVo, JSONObject result) {

    }


    /**
     * 根据查询条件过滤数据
     *
     * @param list     数据集合
     * @param criteria 查询条件
     */
    protected void filterCriteria(List<T> list, BaseCriteria criteria) {

    }

    /**
     * 处理分页数据
     *
     * @param list 数据集合
     * @param pb   分页参数
     * @return 分页数据
     */
    protected List<T> loadPageData(List<T> list, PageBean<T> pb) {
        pb.setRowsCount(list.size());
        //分页数据
        List<T> pageData = list.stream()
                .skip((long) (pb.getPageNo() - 1) * pb.getRowsPerPage())
                .limit(pb.getRowsPerPage()).collect(Collectors.toList());
        pb.setData(pageData);
        return pageData;
    }

    /**
     * 请求Soap接口
     *
     * @param soapBody   请求体
     * @param soapAction Soap请求动作Action
     * @param methodName 请求方法
     * @return 请求结果
     */
    public JSONObject doRequest(String soapBody, String soapAction, String methodName) {
        //格式化Xml请求体
        boolean isHttps = this.url.startsWith("https");
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Future<JSONObject> future = executor.submit(() ->
                WebServiceUtil.callWebService(this.url, soapAction, soapBody, isHttps)
        );
        //获取请求结果
        JSONObject result;
        try {
            // 设置超时时间为60秒
            result = future.get(60000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            future.cancel(true);
            String message = "监管平台请求接口失败";
            if (e instanceof TimeoutException) {
                message = "监管平台接口请求超时";
            }
            log.error(message + ":" + e.getMessage(), e);
            throw new BaseException(message + "！");
        } finally {
            executor.shutdown();
        }
        // 统一校验远程请求结果
        validateMsg(result, methodName);
        return result;
    }


    /**
     * 统一校验远程请求结果
     *
     * @param response   请求响应
     * @param methodName 请求方法
     */
    public void validateMsg(JSONObject response, String methodName) {
        //如果不为空数据返回则判断
        JSONObject resultResponse = getResponse(response, methodName);
        if (!isEmptyReturn(response, methodName) && resultResponse.containsKey("ResultStruct")) {
            JSONObject resultStruct = resultResponse.getJSONObject("ResultStruct");
            //判断返回结果是否成功
            String msg = resultStruct.getString("message");
            if ("False".equals(resultStruct.getString("succes"))) {
                String errorMsg = msg.contains("错误信息：") ? msg.substring(msg.indexOf("错误信息：") + 5) : msg;
                log.error("远程请求监管平台失败，原因：{}", msg);
                throw new RuntimeException(String.format("监管平台：请求失败，原因[%s]", errorMsg));
            }
        }
    }

    /**
     * 判断监管平台返回数据是否为未查询到数据
     *
     * @param response   请求响应
     * @param methodName 请求方法
     * @return 是否为未查询到数据
     */
    public Boolean isEmptyReturn(JSONObject response, String methodName) {
        Boolean isEmptyReturn = false;
        JSONObject resultResponse = getResponse(response, methodName);
        if (resultResponse.containsKey("ResultStruct")) {
            JSONObject resultStruct = resultResponse.getJSONObject("ResultStruct");
            //判断返回结果是否成功
            String msg = resultStruct.getString("message");
            if ("False".equals(resultStruct.getString("succes")) && "错误代码：-3;错误信息：未查询到数据！".equals(msg)) {
                isEmptyReturn = true;
            }
        }
        return isEmptyReturn;
    }

    /**
     * 加载获取请求动作
     *
     * @param methodName 执行方法
     * @return 请求动作
     */
    public String loadSoapAction(String methodName) {
        return String.format("http://tempuri.org/%s", methodName);
    }

    /**
     * 获取监管平台的查询方法
     *
     * @return 查询方法名
     */
    public abstract String queryMethodName();

    /**
     * 获取监管平台的新增方法
     *
     * @return 新增方法名
     */
    public abstract String insertMethodName();

    /**
     * 获取监管平台的删除方法
     *
     * @return 删除方法名
     */
    public abstract String deleteMethodName();

    /**
     * 创建监管平台附件
     *
     * @param document 本系统附件数据
     * @param fileType 监管平台附件类型 {@link com.sinoyd.lims.pro.enums.EnumPRO.EnumRegulatoryPlatformDocType}
     * @return 监管平台已创建的附件名称
     */
    private String createRegulatoryPlatFormDoc(DtoDocument document, Integer fileType) {
        String createFileMethod = EnumPRO.EnumRegulatoryPlatformMethod.附件创建.getInsertMethod();
        Map<String, Object> params = new HashMap<>();
        params.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "fileName", document.getFilename());
        params.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "fileType", fileType.toString());
        String soapBody = RPSoapBodyUtil.loadSoapBody(username, password, createFileMethod, params);
        //TODO: 错误处理，附件名称获取
        JSONObject response = getResponse(doRequest(soapBody, loadSoapAction(createFileMethod), createFileMethod), createFileMethod)
                .getJSONObject("ResultStruct");
        String success = response.getString("succes");
        String message = response.getString("message");
        if ("False".equals(success)) {
            throw new BaseException(String.format("监管平台：创建附件失败[%s]", message));
        }
        return message;
    }

    /**
     * 上传上海监管平台附件
     *
     * @param document   本系统附件数据
     * @param rpFileName 监管平台创建附件的名称
     * @param fileType   监管平台附件类型 {@link com.sinoyd.lims.pro.enums.EnumPRO.EnumRegulatoryPlatformDocType}
     */
    private void uploadRegulatoryPlatFormDoc(DtoDocument document, String rpFileName, Integer fileType) {
        String uploadFileMethod = EnumPRO.EnumRegulatoryPlatformMethod.附件断点上传.getInsertMethod();
        Map<String, Object> params = new HashMap<>();
        params.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "fileNameNew", rpFileName);
        params.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "fileType", fileType);
        String str = "";
        try {
            InputStream inputStream = new FileInputStream(filePathConfig.getFilePath() + document.getPath());
            byte[] data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
            // 加密
            BASE64Encoder encoder = new BASE64Encoder();
            str = encoder.encode(data);
        } catch (IOException e) {
            log.error("文件获取异常：{}", e.getMessage(), e);
            throw new BaseException(String.format("LIMS：文件获取异常, 路径：[%s]", filePathConfig.getFilePath() + document.getPath()));
        }
        params.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "buffer", str);
        String soapBody = RPSoapBodyUtil.loadSoapBody(username, password, uploadFileMethod, params);
        JSONObject response = getResponse(doRequest(soapBody, loadSoapAction(uploadFileMethod), uploadFileMethod), uploadFileMethod)
                .getJSONObject("ResultStruct");
        String success = response.getString("succes");
        String message = response.getString("message");
        if ("False".equals(success)) {
            log.error("监管平台附件断点上传失败，msg:{}，路径:{}", message, filePathConfig.getFilePath() + document.getPath());
            throw new BaseException(String.format("监管平台：文件断点上传异常[%s]", message));
        }
    }

    /**
     * 远程推送文件
     *
     * @param document 附件数据
     * @param fileType 监管平台附件类型 {@link com.sinoyd.lims.pro.enums.EnumPRO.EnumRegulatoryPlatformDocType}
     * @return 远程文件名称
     */
    protected String pushRemoteFile(DtoDocument document, Integer fileType) {
        //创建附件
        String rpFileName = createRegulatoryPlatFormDoc(document, fileType);
        //上传附件
        uploadRegulatoryPlatFormDoc(document, rpFileName, fileType);
        //返回附件名称
        return rpFileName;
    }

    /**
     * 加载推送请求体
     *
     * @param username 上海监管平台用户名
     * @param password 上海监管平台密码
     * @param pushData 推送数据实体
     * @return 请求体
     */
    protected String loadPushSoapBody(String username, String password, T pushData) {
        return RPSoapBodyUtil.loadSoapBody(username, password, insertMethodName(), pushData);
    }

    /**
     * 处理推送附件（设置相关推送附件字段数据）
     *
     * @param pushVo 推送传参
     * @param data   推送数据
     */
    protected void handlePushDoc(RegulatoryPlatformPushVO pushVo, T data) {

    }

    /**
     * 校验推送实体数据
     *
     * @param pushVo 推送传参
     */
    protected void validatePushData(RegulatoryPlatformPushVO pushVo) {

    }

    /**
     * 泳衣校验推送字段数据
     *
     * @param data 推送数据
     */
    private void validatePushField(T data) {
        StringBuilder validMsg = new StringBuilder("LIMS:");
        boolean validResult = true;
        //统一推送字段校验
        for (Field field : data.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            Object val;
            //获取字段值
            try {
                val = field.get(data);
            } catch (IllegalAccessException e) {
                log.error("字段校验获取字段值失败: 字段权限不足" + e.getMessage(), e);
                throw new BaseException("推送数据校验失败！");
            }
            //标签名称获取RegulatoryPlatformField注解中的fieldName值
            RegulatoryPlatformField rpField = field.getAnnotation(RegulatoryPlatformField.class);
            //判断当前字段是否需要进行字段校验
            if (rpField != null) {
                //校验提示字符串
                String msg = rpField.fieldName();
                //非空判断
                if (!rpField.nullable()) {
                    if (val == null) {
                        validResult = false;
                        validMsg.append(String.format("字段：[%s]值不能为空！", msg)).append(";");
                    }
                }
            }
        }
        if (!validResult) {
            throw new BaseException(validMsg.toString());
        }
    }

    /**
     * 初始化推送数据
     *
     * @param pushVo 推送参数VO
     * @return 推送数据
     */
    protected T initPushData(RegulatoryPlatformPushVO pushVo) {
        //实例化推送数据
        T data = getPushInstance(pushVo);
        //统一校验推送字段
        validatePushField(data);
        return data;
    }

    /**
     * 初始化推送数据
     *
     * @param pushVo 推送参数VO
     * @return 推送数据
     */
    protected T getPushInstance(RegulatoryPlatformPushVO pushVo) {
        T data;
        try {
            data = getTClass().newInstance();
        } catch (Exception e) {
            log.error("推送数据初始化失败:" + e.getMessage(), e);
            throw new BaseException("推送数据初始化失败！");
        }
        return data;
    }

    /**
     * 解析请求结果
     *
     * @param response   请求结果
     * @param methodName 监管平台执行方法
     * @return 数据集合
     */
    protected List<T> parseResult(JSONObject response, String methodName) {
        if (isEmptyReturn(response, methodName)) {
            return new ArrayList<>();
        }
        Object obj = getResponse(response, methodName)
                .getJSONObject(resultItemMainLabel()).get(resultItemLabel());
        if (obj instanceof JSONObject) {
            return new ArrayList<T>() {{
                add(RPJsonObjectUtil.jsonObjToObj((JSONObject) obj, getTClass()));
            }};
        } else if (obj instanceof JSONArray) {
            return RPJsonObjectUtil.jsonArrayToList((JSONArray) obj, getTClass());
        }
        return new ArrayList<>();
    }

    /**
     * 获取请求结果
     *
     * @param response   原始请求结果
     * @param methodName 监管平台执行方法
     * @return 请求结果
     */
    protected JSONObject getResponse(JSONObject response, String methodName) {
        return response.getJSONObject("Body")
                .getJSONObject(methodName + "Response")
                .getJSONObject(methodName + "Result");
    }

    /**
     * 获取返回结果中数据项父级标签
     *
     * @return 数据项父级标签
     */
    protected String resultItemMainLabel() {
        return "Items";
    }

    /**
     * 获取返回结果中数据项标签
     *
     * @return 数据项标签
     */
    protected String resultItemLabel() {
        return "Item";
    }

    /**
     * 获取泛型T的Class类型
     *
     * @return T的Class类型
     */
    @SuppressWarnings("unchecked")
    protected Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 获取分页查询的Soap请求体
     *
     * @param userName 用户账号
     * @param password 用户密码
     * @param criteria 查询条件
     * @param pageBean 分页参数
     * @return 请求体
     */
    protected String loadFindSoapBody(String userName, String password, BaseCriteria criteria, PageBean<T> pageBean) {
        return RPSoapBodyUtil.loadSoapBody(userName, password, queryMethodName());
    }

    /**
     * 根据区域名称获取匹配的区域id字典
     *
     * @param areaNames 区域名称集合
     * @return 区域id字典
     */
    protected Map<String, String> getAreaIdMap(Set<String> areaNames) {
        List<DtoArea> areaList = areaService.findAll();
        // 过滤上海市
        areaList = filterSh(areaList);
        Map<String, String> areaIdMap = new HashMap<>();
        for (String areaName : areaNames) {
            //获取匹配到区域名称的第一条id,如果匹配不到则返回空字符串
            String areaId = areaList.stream().filter(area ->
                    area.getAreaName().contains(areaName))
                    .findFirst().map(DtoArea::getId).orElse("");
            areaIdMap.put(areaName, areaId);
        }
        return areaIdMap;
    }

    /**
     * 过滤上海市
     *
     * @param areaList
     * @return
     */
    private List<DtoArea> filterSh(List<DtoArea> areaList) {
        // 1. 构建parentId到子节点列表的映射
        Map<String, List<DtoArea>> childrenMap = areaList.stream().collect(Collectors.groupingBy(DtoArea::getParentId));
        // 2 递归收集所有子节点
        List<DtoArea> result = new ArrayList<>();
        areaList.stream().filter(p -> p.getAreaName().contains("上海")).findFirst().ifPresent(shanghai ->
                collectChildren(shanghai, childrenMap, result));
        return result;
    }

    /**
     * 递归收集子节点
     *
     * @param current     当前城市
     * @param childrenMap 子城市Map
     * @param result      结果容器
     */
    private void collectChildren(DtoArea current, Map<String, List<DtoArea>> childrenMap, List<DtoArea> result) {
        result.add(current); // 添加当前节点
        List<DtoArea> children = childrenMap.get(current.getId());
        if (children != null) {
            for (DtoArea child : children) {
                collectChildren(child, childrenMap, result); // 递归处理子节点
            }
        }
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setWebServiceUtil(com.sinoyd.lims.pro.util.WebServiceUtil webServiceUtil) {
        this.webServiceUtil = webServiceUtil;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    public void setProjectContractRepository(ProjectContractRepository projectContractRepository) {
        this.projectContractRepository = projectContractRepository;
    }

    @Autowired
    public void setOrderContractRepository(OrderContractRepository orderContractRepository) {
        this.orderContractRepository = orderContractRepository;
    }

    @Autowired
    @Lazy
    public void setOrderContractService(OrderContractService orderContractService) {
        this.orderContractService = orderContractService;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    @Lazy
    public void setFolderExtendService(FolderExtendService folderExtendService) {
        this.folderExtendService = folderExtendService;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    public void setShSamplingPersonNewRepository(SHSamplingPersonNewRepository shSamplingPersonNewRepository) {
        this.shSamplingPersonNewRepository = shSamplingPersonNewRepository;
    }

    @Autowired
    public void setShSamplingInstrumentNewRepository(SHSamplingInstrumentNewRepository shSamplingInstrumentNewRepository) {
        this.shSamplingInstrumentNewRepository = shSamplingInstrumentNewRepository;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }

    @Autowired
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }
}
