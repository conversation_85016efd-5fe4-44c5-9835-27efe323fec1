package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoOATaskHandleLog;
import com.sinoyd.lims.pro.repository.OATaskHandleLogRepository;
import com.sinoyd.lims.pro.service.OATaskHandleLogService;

import org.springframework.stereotype.Service;

/**
 * 审批任务流程日志业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Service
public class OATaskHandleLogServiceImpl extends BaseJpaServiceImpl<DtoOATaskHandleLog, String, OATaskHandleLogRepository>
        implements OATaskHandleLogService {
    @Override
    public void findByPage(PageBean<DtoOATaskHandleLog> page, BaseCriteria criteria) {
        String sort = page.getSort();
        if (StringUtil.isEmpty(sort)) {
            page.setSort("completeTime+");
        }

        page.setRowsPerPage(1000);

        page.setEntityName("DtoOATaskHandleLog x");
        page.setSelect("select x");

        super.findByPage(page, criteria);
    }
}
