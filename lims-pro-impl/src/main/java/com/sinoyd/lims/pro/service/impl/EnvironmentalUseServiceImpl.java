package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.EnvironmentalRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.InstrumentUseRecordRepository;
import com.sinoyd.lims.lim.service.EnvironmentalLogService;
import com.sinoyd.lims.lim.service.EnvironmentalRecordService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoEnvironmentalUse;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.*;
import org.aspectj.lang.annotation.DeclareAnnotation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境信息操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
@Service
public class EnvironmentalUseServiceImpl implements EnvironmentalUseService {

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private EnvironmentalRecordService environmentalRecordService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private EnvironmentalLogService environmentalLogService;

    @Autowired
    private InstrumentUseRecordRepository instrumentUseRecordRepository;

    /**
     * 保存环境信息记录
     *
     * @param dto 环境信息记录
     * @return 环境信息记录
     */
    @Transactional
    @Override
    public DtoEnvironmentalRecord save(DtoEnvironmentalRecord dto) {
//        if (dto.getStartTime().after(dto.getEndTime())) {
//            throw new BaseException("仪器使用开始时间不能大于结束时间！");
//        }
        if (dto.getInstrumentUseRecord().size() > dto.getInstrumentUseRecord().stream().map(DtoInstrumentUseRecord::getInstrumentId).collect(Collectors.toSet()).size()) {
            throw new BaseException("所选仪器存在重复选择！");
        }
        List<String> instrumentIds = dto.getInstrumentUseRecord().stream().map(DtoInstrumentUseRecord::getInstrumentId).collect(Collectors.toList());
        if (StringUtil.isEmpty(instrumentIds)) {
            throw new BaseException("未选择仪器");
        }

        if (dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(dto.getObjectId());
//            dto.setTestIds(analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList()));
            dto.setSampleIds(analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList()));
        }

        // 如果根据id查询存在记录,则为修改实验室仪器使用记录, 否则为新增实验室仪器使用记录
        DtoEnvironmentalRecord record = environmentalRecordService.findRecord(dto.getId());
        if (StringUtil.isNull(record)) {
            this.createInsertLog(dto);
        } else {
            this.createUpdateLog(record, dto);
        }

        // 如果存在实验室id,则进行实验室环境使用记录新增
        if (StringUtil.isNotEmpty(dto.getEnvironmentalId())) {
            environmentalLogService.saveOrUpdateEnvironmentalLog(dto, dto.getId());
        }
        return environmentalRecordService.save(dto);
    }

    @Override
    @Transactional
    public List<DtoEnvironmentalRecord> saveShareInstrument(List<String> useRecordIds, String objectId, Integer objectType) {
        List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordRepository.findAll(useRecordIds);
        List<String> instrumentIds = useRecords.stream().map(DtoInstrumentUseRecord::getInstrumentId).distinct().collect(Collectors.toList());
        if (useRecords.size() > instrumentIds.size()) {
            throw new BaseException("所选仪器存在重复选择！");
        }
        List<String> testIds = new ArrayList<>();
        List<String> sampleIds = new ArrayList<>();
        if (EnumLIM.EnumEnvRecObjType.实验室分析.getValue().equals(objectType)) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(objectId);
            testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        }
        List<String> manageIds = useRecords.stream().map(DtoInstrumentUseRecord::getEnvironmentalManageId).distinct().collect(Collectors.toList());
        List<DtoEnvironmentalRecord> environmentalRecords = StringUtil.isNotEmpty(manageIds) ? environmentalRecordService.findAll(manageIds) : new ArrayList<>();
        List<DtoEnvironmentalRecord> result = new ArrayList<>();
        for (DtoInstrumentUseRecord useRecord : useRecords) {
            DtoEnvironmentalRecord existsRecord = environmentalRecords.stream().filter(e -> e.getId().equals(useRecord.getEnvironmentalManageId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(existsRecord)) {
                DtoEnvironmentalRecord environmentalRecord = new DtoEnvironmentalRecord();
                BeanUtils.copyProperties(existsRecord, environmentalRecord, "id", "objectId", "creator", "createDate", "modifier", "modifyDate");
                environmentalRecord.setObjectId(objectId);
                environmentalRecord.setObjectType(objectType);
                environmentalRecord.setUsePersonId(PrincipalContextUser.getPrincipal().getUserId());
                environmentalRecord.setTestIds(testIds);
                environmentalRecord.setSampleIds(sampleIds);
                DtoInstrumentUseRecord instrumentUseRecord = new DtoInstrumentUseRecord();
                BeanUtils.copyProperties(useRecord, instrumentUseRecord, "id", "objectId", "environmentalManageId", "objectType", "creator", "createDate", "modifier", "modifyDate");
                instrumentUseRecord.setTestIds("");
                environmentalRecord.setInstrumentUseRecord(Collections.singletonList(instrumentUseRecord));
                result.add(environmentalRecordService.save(environmentalRecord));
                this.createInsertLog(environmentalRecord);
            }
        }
        return result;
    }

    @Override
    public String check(DtoEnvironmentalRecord dto) {
        List<DtoInstrumentUseRecord> useRecordList = environmentalRecordService.getConflictRecords(dto);
        Map<String, DtoInstrumentUseRecord> recordMap = dto.getInstrumentUseRecord().stream().collect(Collectors.toMap(DtoInstrumentUseRecord::getInstrumentId, record -> record));

        //因为涉及限制，故认为存在冲突的使用记录条数不多，所以直接遍历了
        if (useRecordList.size() > 0) {
            List<String> msgs = new ArrayList<>();
            for (DtoInstrumentUseRecord useRecord : useRecordList) {
                DtoInstrumentUseRecord record = recordMap.get(useRecord.getInstrumentId());
                Map<String, String> objectMap = this.getObjectMap(useRecord.getObjectId(), useRecord.getObjectType());
                msgs.add(String.format("仪器：%s %s %s;%s使用时间：%s~%s",
                        record.getInstrumentName(),
                        record.getModel(),
                        record.getInstrumentsCode(),
                        StringUtil.isNotNull(objectMap) ? String.format("%s：%s;操作人员：%s;",
                                objectMap.get("objectName"), objectMap.get("code"), objectMap.get("usePerson")) : "",
                        DateUtil.dateToString(useRecord.getStartTime(), DateUtil.FULL_NO_SECOND),
                        DateUtil.dateToString(useRecord.getEndTime(), DateUtil.FULL_NO_SECOND)
                ));
            }
            throw new BaseException(String.join("</br>", msgs));
        }
        return null;
    }

    /**
     * 删除环境信息记录
     *
     * @param ids 环境信息id集合
     * @return 删除条数
     */
    @Transactional
    @Override
    public Integer delete(List<String>ids) {
        Integer objectType;
        DtoEnvironmentalRecord rec = environmentalRecordService.findOne(ids.get(0));
        objectType = rec.getObjectType();
        PageBean<DtoEnvironmentalRecord> pb = new PageBean<>();
        EnvironmentalRecordCriteria criteria = new EnvironmentalRecordCriteria();
        criteria.setObjectType(rec.getObjectType());
        criteria.setIds(ids);
        environmentalRecordService.findByPage(pb, criteria);
        List<DtoEnvironmentalRecord> recordList = pb.getData();
        List<DtoLog> logList = new ArrayList<>();
        for (DtoEnvironmentalRecord record : recordList) {
            StringBuilder comment = new StringBuilder();
            comment.append("</br>开始时间:").append(DateUtil.dateToString(record.getStartTime(), DateUtil.FULL_NO_SECOND));
            comment.append("</br>结束时间:").append(DateUtil.dateToString(record.getEndTime(), DateUtil.FULL_NO_SECOND));
            comment.append("</br>温度:").append(record.getTemperature());
            comment.append("</br>湿度:").append(record.getHumidity());
            comment.append("</br>使用仪器:");
            for (DtoInstrumentUseRecord ins : record.getInstrumentUseRecord()) {
                comment.append(String.format("</br>%s-%s %s", ins.getInstrumentName(), ins.getModel(), ins.getInstrumentsCode()));
            }
            if (objectType.equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.删除使用记录.toString());
                log.setLogType(EnumPRO.EnumLogType.检测单使用记录.getValue());
                log.setObjectId(record.getObjectId());
                log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
                log.setComment("删除环境与仪器使用日志:" + comment.toString());
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            } else {
                List<DtoTest> testList = record.getTestIds().size() > 0 ? testService.findRedisByIds(record.getTestIds()) : new ArrayList<>();
                for (DtoTest test : testList) {
                    DtoLog log = new DtoLog();
                    log.setId(UUIDHelper.NewID());
                    log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                    log.setOperateTime(new Date());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.删除使用记录.toString());
                    log.setLogType(objectType.equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue()) ? EnumPRO.EnumLogType.现场领样单使用记录.getValue() : EnumPRO.EnumLogType.送样单采样使用记录.getValue());
                    log.setObjectId(record.getObjectId());
                    log.setObjectType(objectType.equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue()) ? EnumPRO.EnumLogObjectType.现场领样单.getValue() : EnumPRO.EnumLogObjectType.送样单.getValue());
                    log.setComment("删除检测项目" + test.getRedAnalyzeItemName() + "环境与仪器使用日志:" + comment.toString());
                    log.setOpinion("");
                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    log.setRemark("");
                    logList.add(log);
                }
            }
        }
        newLogService.createLog(logList, objectType.equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue()) ? EnumPRO.EnumLogType.检测单使用记录.getValue() :
                (objectType.equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue()) ? EnumPRO.EnumLogType.现场领样单使用记录.getValue() : EnumPRO.EnumLogType.送样单采样使用记录.getValue()));
        return environmentalRecordService.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public void createLog(DtoEnvironmentalRecord dto) {
        createInsertLog(dto);
    }

    /**
     * 创建添加的日志
     * @param dto 环境信息记录
     */
    private void createInsertLog(DtoEnvironmentalRecord dto) {
        DtoEnvironmentalUse useInfo = new DtoEnvironmentalUse(dto);
        StringBuilder comment = new StringBuilder();
        comment.append("</br>开始时间:").append(useInfo.getStartTime());
        comment.append("</br>结束时间:").append(useInfo.getEndTime());
        comment.append("</br>温度:").append(StringUtil.isNotEmpty(useInfo.getTemperature()) ? useInfo.getTemperature() : "");
        comment.append("</br>湿度:").append(StringUtil.isNotEmpty(useInfo.getHumidity()) ? useInfo.getHumidity() : "");
        comment.append("</br>使用仪器:");
        for (DtoInstrumentUseRecord record : dto.getInstrumentUseRecord()) {
            comment.append(String.format("</br>%s-%s %s", record.getInstrumentName(), record.getModel(), record.getInstrumentsCode()));
        }

        if (!dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
            List<DtoSample> sampleList = dto.getSampleIds().size() > 0 ? sampleRepository.findAll(dto.getSampleIds()) : new ArrayList<>();
            sampleList.sort(Comparator.comparing(DtoSample::getCode));
            useInfo.setSampleCodes(String.join("、", sampleList.stream().map(DtoSample::getCode).collect(Collectors.toList())));
            List<DtoTest> testList = dto.getSampleIds().size() > 0 ? testService.findRedisByIds(dto.getTestIds()) : new ArrayList<>();

            List<DtoLog> logList = new ArrayList<>();
            for (DtoTest test : testList) {
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.新增使用记录.toString());
                log.setLogType(dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue()) ? EnumPRO.EnumLogType.送样单采样使用记录.getValue() : EnumPRO.EnumLogType.现场领样单使用记录.getValue());
                log.setObjectId(dto.getObjectId());
                log.setObjectType(dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue()) ? EnumPRO.EnumLogObjectType.送样单.getValue() : EnumPRO.EnumLogObjectType.现场领样单.getValue());
                log.setComment(String.format("保存检测项目%s的环境与仪器使用日志:%s", test.getRedAnalyzeItemName(), comment.toString()));
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
            newLogService.createLog(logList, dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue()) ? EnumPRO.EnumLogType.送样单采样使用记录.getValue() : EnumPRO.EnumLogType.现场领样单使用记录.getValue());
        } else {
            newLogService.createLog(dto.getObjectId(), String.format("新增环境与仪器使用日志:%s", comment.toString()), "",
                    EnumPRO.EnumLogType.检测单使用记录.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.新增使用记录.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
    }

    /**
     * 创建修改的日志
     * @param oldRecord 旧环境信息记录
     * @param newRecord 新环境信息记录
     */
    private void createUpdateLog(DtoEnvironmentalRecord oldRecord,DtoEnvironmentalRecord newRecord) {
        DtoEnvironmentalUse oldInfo = new DtoEnvironmentalUse(oldRecord);
        DtoEnvironmentalUse newInfo = new DtoEnvironmentalUse(newRecord);
        if (!newRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
            if (!oldRecord.getSampleIds().equals(newRecord.getSampleIds())) {
                Set<String> sampleIds = new HashSet<>();
                sampleIds.addAll(oldRecord.getSampleIds());
                sampleIds.addAll(newRecord.getSampleIds());
                List<DtoSample> samples = sampleRepository.findAll(sampleIds);
                samples.sort(Comparator.comparing(DtoSample::getCode));
                oldInfo.setSampleCodes(String.join("、", samples.stream().filter(p -> oldRecord.getSampleIds().contains(p.getId())).map(DtoSample::getCode).collect(Collectors.toList())));
                newInfo.setSampleCodes(String.join("、", samples.stream().filter(p -> newRecord.getSampleIds().contains(p.getId())).map(DtoSample::getCode).collect(Collectors.toList())));
            }
            if (!oldRecord.getTestIds().equals(newRecord.getTestIds())) {
                Set<String> testIds = new HashSet<>();
                testIds.addAll(oldRecord.getTestIds());
                testIds.addAll(newRecord.getTestIds());
                List<DtoTest> tests = testService.findRedisByIds(new ArrayList<>(testIds));
                tests.sort(Comparator.comparing(DtoTest::getRedAnalyzeItemName));
                oldInfo.setTestNames(String.join("、", tests.stream().filter(p -> oldRecord.getTestIds().contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList())));
                newInfo.setTestNames(String.join("、", tests.stream().filter(p -> newRecord.getTestIds().contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList())));
            }
        }
        StringBuilder comment = new StringBuilder();
        try {
            Map<String, Map<String, Object>> map = new HashMap<>();
            Field[] fields = DtoEnvironmentalUse.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object oldValue = field.get(oldInfo);
                Object newValue = field.get(newInfo);
                //满足改前、改后均不为null，且改前、改后存在一个为null或改前和改后的值不一样，即判定为修改过
                if (!(StringUtil.isNull(oldValue) && StringUtil.isNull(newValue)) &&
                        (StringUtil.isNull(oldValue) || StringUtil.isNull(newValue) || !oldValue.equals(newValue))) {
                    String name = "";
                    for (Annotation anno : field.getAnnotations()) {
                        if (anno instanceof DeclareAnnotation) {
                            name = ((DeclareAnnotation) anno).value();
                            break;
                        }
                    }
                    if (!StringUtil.isNotNull(oldValue)) {
                        oldValue = "";
                    }
                    if (!StringUtil.isNotNull(newValue)) {
                        newValue = "";
                    }
                    comment.append(String.format("</br>%s由'%s'改为'%s':", name, oldValue, newValue));
                }
            }
        } catch (SecurityException | IllegalAccessException | IllegalArgumentException ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
        Set<String> instrumentIds = new HashSet<>();
        instrumentIds.addAll(oldRecord.getInstrumentUseRecord().stream().map(DtoInstrumentUseRecord::getInstrumentId).distinct().collect(Collectors.toList()));
        instrumentIds.addAll(newRecord.getInstrumentUseRecord().stream().map(DtoInstrumentUseRecord::getInstrumentId).distinct().collect(Collectors.toList()));
        for (String instrumentId : instrumentIds) {
            DtoInstrumentUseRecord oldInsRecord = oldRecord.getInstrumentUseRecord().stream().filter(p -> p.getInstrumentId().equals(instrumentId)).findFirst().orElse(null);
            DtoInstrumentUseRecord newInsRecord = newRecord.getInstrumentUseRecord().stream().filter(p -> p.getInstrumentId().equals(instrumentId)).findFirst().orElse(null);
            if (StringUtil.isNull(oldInsRecord) && StringUtil.isNotNull(newInsRecord)) {
                comment.append(String.format("</br>添加关联仪器:%s-%s %s", newInsRecord.getInstrumentName(), newInsRecord.getModel(), newInsRecord.getInstrumentsCode()));
            } else if (StringUtil.isNotNull(oldInsRecord) && StringUtil.isNull(newInsRecord)) {
                comment.append(String.format("</br>删除关联仪器:%s-%s %s", oldInsRecord.getInstrumentName(), oldInsRecord.getModel(), oldInsRecord.getInstrumentsCode()));
            } else if (newRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
                List<String> contents = new ArrayList<>();
                if (!oldInsRecord.getBeforeUseSituation().equals(newInsRecord.getBeforeUseSituation())) {
                    contents.add(String.format("使用前情况由'%s'修改为'%s'", oldInsRecord.getBeforeUseSituation(), newInsRecord.getBeforeUseSituation()));
                }
                if (!oldInsRecord.getBeforeAfterSituation().equals(newInsRecord.getBeforeAfterSituation())) {
                    contents.add(String.format("使用后情况由'%s'修改为'%s'", oldInsRecord.getBeforeAfterSituation(), newInsRecord.getBeforeAfterSituation()));
                }
                if (!oldInsRecord.getIsAssistInstrument().equals(newInsRecord.getIsAssistInstrument())) {
                    contents.add(String.format("是否辅助仪器由'%s'修改为'%s'", oldInsRecord.getIsAssistInstrument() ? "是" : "否", newInsRecord.getIsAssistInstrument() ? "是" : "否"));
                }
                if (contents.size() > 0) {
                    comment.append(String.format("</br>修改关联仪器:%s-%s %s,%s", oldInsRecord.getInstrumentName(), oldInsRecord.getModel(), oldInsRecord.getInstrumentsCode(), String.join("、", contents)));
                }
            }
        }
        newLogService.createLog(newRecord.getObjectId(), String.format("修改环境与仪器使用日志:%s", comment.toString()), "",
                newRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue()) ?
                        EnumPRO.EnumLogType.检测单使用记录.getValue() : (newRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue()) ?
                        EnumPRO.EnumLogType.现场领样单使用记录.getValue() : EnumPRO.EnumLogType.送样单采样使用记录.getValue()),
                newRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue()) ?
                        EnumPRO.EnumLogObjectType.检测单.getValue() : (newRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue()) ?
                        EnumPRO.EnumLogObjectType.现场领样单.getValue() : EnumPRO.EnumLogObjectType.送样单.getValue()),
                EnumPRO.EnumLogOperateType.修改使用记录.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 获取对应的关联信息
     *
     * @param objectId 关联id
     * @param objectType 关联类型
     * @return 关联信息
     */
    private Map<String,String> getObjectMap(String objectId, Integer objectType) {
        Map<String, String> map = new HashMap<>();
        if (objectType.equals(EnumLIM.EnumEnvRecObjType.采样.getValue())) {
            DtoReceiveSampleRecord record = receiveSampleRecordService.findOne(objectId);
            if (StringUtil.isNotNull(record)) {
                map.put("objectName", "送样单编号");
                map.put("code", record.getRecordCode());
                map.put("usePerson", record.getSenderName());
            }
        } else if (objectType.equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue())) {
            DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findOne(objectId);
            DtoReceiveSampleRecord receiveRecord = receiveSampleRecordService.findOne(StringUtil.isNull(subRecord) ? objectId : subRecord.getReceiveId());
            if (StringUtil.isNotNull(subRecord)) {
                map.put("objectName", "领样单编号");
                map.put("code", subRecord.getCode());
                map.put("usePerson", receiveRecord.getSenderName());
            } else if (StringUtil.isNotNull(receiveRecord)) {
                map.put("objectName", "送样单编号");
                map.put("code", receiveRecord.getRecordCode());
                map.put("usePerson", receiveRecord.getSenderName());
            }
        } else if (objectType.equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
            DtoWorkSheetFolder folder = workSheetFolderService.findOne(objectId);
            if (StringUtil.isNotNull(folder)) {
                map.put("objectName", "检测单编号");
                map.put("code", folder.getWorkSheetCode());
                map.put("usePerson", folder.getAnalystName());
            }
        }
        return map.size() > 0 ? map : null;
    }
}
