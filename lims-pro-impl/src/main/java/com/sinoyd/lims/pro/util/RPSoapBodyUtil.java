package com.sinoyd.lims.pro.util;

import com.sinoyd.base.utils.XmlUtil;
import com.sinoyd.boot.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 上海监管平台远程请求请求体构造工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/29
 */
@Slf4j
public class RPSoapBodyUtil {

    /**
     * 加载获取请求体（无参请求）
     * <br/>
     * <p>
     * 该方法用于构建 SOAP 请求的主体部分它从 XML 文件中读取预定义的 SOAP 请求模板，
     * 并根据提供的用户名、密码和方法名称替换模板中的占位符
     * <p/>
     *
     * @param userName   用户名，用于替换模板中的 ${head_userName} 占位符
     * @param password   密码，用于替换模板中的 ${head_passWord} 占位符
     * @param methodName 方法名称，用于确定请求体中的标签请求方法 _replace_label_methodName 占位符
     * @return 替换占位符后的 SOAP 请求体字符串
     */
    public static String loadSoapBody(String userName, String password,
                                      String methodName) {
        // 从指定的 XML 文件中读取 SOAP 请求模板，并根据参数替换模板中的占位符
        return XmlUtil.readXml("classpath:RegulatoryPlatform/RPSoapBody.xml")
                .replace("${head_userName}", userName)
                .replace("${head_passWord}", password)
                .replace("${body_labels}", loadNoneParamsBodyFields(methodName));
    }


    /**
     * 加载获取请求体（数据实体传参, JAXB方式）
     * <br/>
     * 考虑数据实体支持集合类型，暂时未发行有参数集合类型的请求 【2025-04-29 刘庄卓】
     * <p>
     * 该方法用于构建 SOAP 请求的主体部分它从 XML 文件中读取预定义的 SOAP 请求模板，
     * 并根据提供的用户名、密码和方法名称替换模板中的占位符
     * <p/>
     *
     * @param userName   用户名，用于替换模板中的 ${head_userName} 占位符
     * @param password   密码，用于替换模板中的 ${head_passWord} 占位符
     * @param methodName 方法名称，用于确定请求体中的标签请求方法 _replace_label_methodName 占位符
     * @param data       参数实体数据
     * @return 替换占位符后的 SOAP 请求体字符串
     */
    public static String loadSoapBody(String userName, String password,
                                      String methodName, Object data) {
        return XmlUtil.readXml("classpath:RegulatoryPlatform/RPSoapBody.xml")
                .replace("${head_userName}", userName)
                .replace("${head_passWord}", password)
                .replace("${body_labels}", loadBodyFields(data).replace("${label_methodName}", methodName));
    }

    public static String loadSoapBody(String userName, String password) {
        return XmlUtil.readXml("classpath:RegulatoryPlatform/RPSoapBody.xml")
                .replace("${head_userName}", userName)
                .replace("${head_passWord}", password);
    }

    /**
     * 加载获取请求体（Map传参）
     * <br/>
     * 单实体传参，不支持集合传参
     * <p>
     * 该方法用于构建 SOAP 请求的主体部分它从 XML 文件中读取预定义的 SOAP 请求模板，
     * 并根据提供的用户名、密码和方法名称替换模板中的占位符
     * <p/>
     *
     * @param userName   用户名，用于替换模板中的 ${head_userName} 占位符
     * @param password   密码，用于替换模板中的 ${head_passWord} 占位符
     * @param methodName 方法名称，用于确定请求体中的标签请求方法 _replace_label_methodName 占位符
     * @param params     请求参数
     * @return 替换占位符后的 SOAP 请求体字符串
     */
    public static String loadSoapBody(String userName, String password,
                                      String methodName, Map<String, Object> params) {
        return XmlUtil.readXml("classpath:RegulatoryPlatform/RPSoapBody.xml")
                .replace("${head_userName}", userName)
                .replace("${head_passWord}", password)
                .replace("${body_labels}", loadBodyFields(methodName, params));
    }

    /**
     * 加载请求体字段标签
     *
     * @param methodName 请求方法
     * @param params     参数
     * @return 字段标签
     */
    private static String loadBodyFields(String methodName, Map<String, Object> params) {
        List<String> bodyLabels = new ArrayList<>();
        //请求体标签模板
        String labelTemplate = XmlUtil.readXml("classpath:RegulatoryPlatform/RPSoapBodyLabel.xml");
        params.forEach((k, v) -> bodyLabels.add(String.format("<%s>%s</%s>\n", k, v, k)));
        return labelTemplate.replace("${label_methodName}", methodName)
                .replace("${body_fields}", String.join("\n", bodyLabels));
    }

    /**
     * 加载请求体字段标签（JAXB方式）
     *
     * @param data 数据
     * @return 字段标签
     */
    public static String loadBodyFields(Object data) {
        return loadBodyFields(data, null, false);
    }

    /**
     * 加载请求体字段标签（无参请求、JAXB方式）
     *
     * @param methodName 方法名称
     * @return 字段标签
     */
    public static String loadNoneParamsBodyFields(String methodName) {
        return loadBodyFields(null, methodName, true);
    }

    /**
     * 加载请求体字段标签（JAXB方式）
     *
     * @param data         请求数据
     * @param methodName   请求方法
     * @param isNoneParams 是否无参请求
     * @return 字段标签
     */
    private static String loadBodyFields(Object data, String methodName, boolean isNoneParams) {
        //请求体标签模板
        String labelTemplate = XmlUtil.readXml("classpath:RegulatoryPlatform/RPSoapBodyLabel.xml");
        if (isNoneParams) {
            return labelTemplate.replace("${label_methodName}", methodName)
                    .replace("${body_fields}", "");
        }
        try {
            JAXBContext ctx = JAXBContext.newInstance(data.getClass());
            Marshaller marshaller = ctx.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
            StringWriter stringWriter = new StringWriter();
            marshaller.marshal(data, stringWriter);
            stringWriter.write("\n");
            return stringWriter.toString();
        } catch (JAXBException e) {
            e.printStackTrace();
            log.error("加载请求体失败：对象转换请求体失败, msg:{}", e.getMessage(), e);
            throw new BaseException("加载请求体失败：对象转换请求体失败！");
        }
    }

}
