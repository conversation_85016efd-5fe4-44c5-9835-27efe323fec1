package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import com.sinoyd.lims.pro.dto.DtoSHSamplingInstrumentNew;
import com.sinoyd.lims.pro.dto.DtoSHSamplingPersonNew;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RPSamplingPlanDeviceVO;
import com.sinoyd.lims.pro.vo.RPSamplingPlanPersonVO;
import com.sinoyd.lims.pro.vo.RPSamplingPlanVO;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 上海监管平台采样计划策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Component
public class RPSamplingPlan extends AbsRegulatoryPlatformRemote<RPSamplingPlanVO> {

    @Override
    protected void validatePushData(RegulatoryPlatformPushVO pushVo) {
        DtoProject project = projectService.findOne(pushVo.getId());
        if (project == null) {
            throw new BaseException("LIMS:需推送方案的项目不存在！");
        }
        //校验项目推送信息
        DtoProjectContract projectContract = validateContract(project);
        //校验采样计划
        validatePlan(projectContract.getPId());
    }

    @Override
    @Transactional
    public void push(RegulatoryPlatformPushVO pushVo) {
        //推送数据校验
        validatePushData(pushVo);
        //初始化推送数据
        RPSamplingPlanVO pushData = initPushData(pushVo);
        //推送采样计划人员
        pushSamplingDevices(pushData);
        //推送采样计划仪器
        pushSamplingPersons(pushData);
        // (处理结果)
        handlePushResult(pushVo, null);
    }

    @Override
    protected RPSamplingPlanVO getPushInstance(RegulatoryPlatformPushVO pushVo) {
        RPSamplingPlanVO result = super.getPushInstance(pushVo);
        DtoProject project = projectService.findOne(pushVo.getId());
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(project.getId()).stream().findFirst();
        //判断项目合同信息
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            String rpProjectId = projectContract.getPId();
            //添加采样计划人员信息
            List<DtoSHSamplingPersonNew> rpPersonsRelations = shSamplingPersonNewRepository.findByTaskId(rpProjectId);
            //监管平台采样计划人员信息（推送用）
            RPSamplingPlanPersonVO planPersons = new RPSamplingPlanPersonVO();
            if (StringUtil.isNotEmpty(rpPersonsRelations)) {
                //查询LIMS人员数据
                List<DtoPerson> personList = personService.findAll();
                //获取监管平台人员匹配id
                for (DtoSHSamplingPersonNew person : rpPersonsRelations) {
                    Optional<DtoPerson> personExpand = personList.stream()
                            .filter(p -> p.getId().equals(person.getPersonId())).findFirst();
                    personExpand.ifPresent(per -> planPersons.getSamplingPersonData()
                            .add(new RPSamplingPlanPersonVO.SamplingPersonData(rpProjectId, per.getRegulateId())));
                }
            }
            result.setPlanPersons(planPersons);
            //添加采样计划仪器信息
            List<DtoSHSamplingInstrumentNew> rpDeviceRelations = shSamplingInstrumentNewRepository.findByTaskId(rpProjectId);
            //监管平台采样计划设备信息（推送用）
            RPSamplingPlanDeviceVO planDevices = new RPSamplingPlanDeviceVO();
            if (StringUtil.isNotEmpty(rpDeviceRelations)) {
                //查询LIMS仪器数据
                List<DtoInstrument> instrumentList = instrumentService.findAll();
                //获取监管平台设备匹配id
                for (DtoSHSamplingInstrumentNew instrument : rpDeviceRelations) {
                    Optional<DtoInstrument> instrumentExpand = instrumentList.stream()
                            .filter(i -> i.getId().equals(instrument.getInstrumentId())).findFirst();
                    instrumentExpand.ifPresent(ins -> planDevices.getSamplingDeviceData()
                            .add(new RPSamplingPlanDeviceVO.SamplingDeviceData(rpProjectId, ins.getRegulateId())));
                }
            }
            result.setPlanDevices(planDevices);
            return result;
        }
        return super.getPushInstance(pushVo);
    }

    @Override
    @Transactional
    public void handlePushResult(RegulatoryPlatformPushVO pushVo, JSONObject result) {
        DtoProject project = projectService.findOne(pushVo.getId());
        Optional<DtoProjectContract> projectContractOp = projectContractRepository
                .findByProjectId(project.getId()).stream().findFirst();
        //更新推送结果
        if (projectContractOp.isPresent()) {
            DtoProjectContract projectContract = projectContractOp.get();
            projectContract.setPlanHasPush(1);
            projectContractRepository.save(projectContract);
            updateNewSamplingPerson(projectContract.getPId());
        }
    }


    /**
     * 验证采样仪器和采样人员是否与监管平台绑定
     *
     * @param pid 监管平台项目id
     */
    private void validatePlan(String pid) {
        //采样计划人员校验
        List<DtoSHSamplingPersonNew> rpPersonRelationList = shSamplingPersonNewRepository.findByTaskId(pid);
        if (StringUtil.isNotEmpty(rpPersonRelationList)) {
            Set<String> personIds = rpPersonRelationList.stream().map(DtoSHSamplingPersonNew::getPersonId).collect(Collectors.toSet());
            List<DtoPerson> personList = personService.findAll(personIds);
            if (personIds.size() != personList.size() || personList.stream().anyMatch(p -> StringUtil.isEmpty(p.getRegulateId()))) {
                throw new BaseException("LIMS：当前计划采样人员未与监管平台绑定，请确认后重试！");
            }
        }
        //采样计划仪器校验
        List<DtoSHSamplingInstrumentNew> rpInsRelationList = shSamplingInstrumentNewRepository.findByTaskId(pid);
        if (StringUtil.isNotEmpty(rpInsRelationList)) {
            Set<String> instrumentIds = rpInsRelationList.stream().map(DtoSHSamplingInstrumentNew::getInstrumentId).collect(Collectors.toSet());
            List<DtoInstrument> instrumentList = instrumentService.findAll(instrumentIds);
            if (instrumentIds.size() != instrumentList.size() || instrumentList.stream().anyMatch(p -> StringUtil.isEmpty(p.getRegulateId()))) {
                throw new BaseException("LIMS：当前计划采样仪器未与监管平台绑定，请确认后重试！");
            }
        }
    }

    /**
     * 校验项目推送信息
     *
     * @param project LIMS项目信息
     */
    private DtoProjectContract validateContract(DtoProject project) {
        Optional<DtoProjectContract> projectContractOp = projectContractRepository.findByProjectId(project.getId()).stream().findFirst();
        //判断项目合同信息
        if (!projectContractOp.isPresent()) {
            throw new BaseException("LIMS：项目不存在推送与信息管理信息，请确认！");
        }
        return projectContractOp.get();
    }

    /**
     * 推送采样计划人员数据
     *
     * @param pushData 采样计划推送数据
     */
    private void pushSamplingPersons(RPSamplingPlanVO pushData) {
        //监管平台采样计划人员数据添加方法
        String samplingPersonMethod = EnumPRO.EnumRegulatoryPlatformMethod.采样计划_人员.getInsertMethod();
        if (StringUtil.isNotEmpty(samplingPersonMethod) && pushData.getPlanPersons() != null
                && StringUtil.isNotEmpty(pushData.getPlanPersons().getSamplingPersonData())) {
            //获取请求体
            String soapBody = RPSoapBodyUtil.loadSoapBody(username, password, samplingPersonMethod, pushData.getPlanPersons());
            //请求推送
            doRequest(soapBody, loadSoapAction(samplingPersonMethod), samplingPersonMethod);
        }
    }

    /**
     * 推送采样计划设备数据
     *
     * @param pushData 采样计划推送数据
     */
    private void pushSamplingDevices(RPSamplingPlanVO pushData) {
        //监管平台采样计划设备数据添加方法
        String samplingDeviceMethod = EnumPRO.EnumRegulatoryPlatformMethod.采样计划_仪器.getInsertMethod();
        if (StringUtil.isNotEmpty(samplingDeviceMethod) && pushData.getPlanDevices() != null
                && StringUtil.isNotEmpty(pushData.getPlanDevices().getSamplingDeviceData())) {
            //获取请求体
            String soapBody = RPSoapBodyUtil.loadSoapBody(username, password, samplingDeviceMethod, pushData.getPlanDevices());
            //请求推送
            doRequest(soapBody, loadSoapAction(samplingDeviceMethod), samplingDeviceMethod);
        }
    }

    /**
     * 更新采样计划的采样人员为最新推送的采样人员
     *
     * @param taskId 任务id
     */
    private void updateNewSamplingPerson(String taskId) {
        List<DtoSHSamplingPersonNew> personList = shSamplingPersonNewRepository.findByTaskId(taskId);
        List<DtoProjectContract> projectContracts = projectContractRepository.findByPIdNotNullAndPlanHasPush(0);
        List<String> exsitsTaskIds = projectContracts.stream().map(DtoProjectContract::getPId).collect(Collectors.toList());
        shSamplingPersonNewRepository.deleteByTaskIdIn(exsitsTaskIds);
        List<DtoSHSamplingPersonNew> newPersonList = new ArrayList<>();
        exsitsTaskIds.forEach(t -> {
            personList.forEach(p -> {
                DtoSHSamplingPersonNew samplingPerson = new DtoSHSamplingPersonNew();
                samplingPerson.setTaskId(t);
                samplingPerson.setPersonId(p.getPersonId());
                newPersonList.add(samplingPerson);
            });
        });
        if (StringUtil.isNotEmpty(newPersonList)) {
            shSamplingPersonNewRepository.save(newPersonList);
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.采样计划信息.getDeleteMethod();
    }
}
