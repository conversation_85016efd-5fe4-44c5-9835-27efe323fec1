package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.RPPersonVO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 上海监管平台人员信息策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Component
public class RPPerson extends AbsRegulatoryPlatformRemote<RPPersonVO> {
    @Override
    protected void filterCriteria(List<RPPersonVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //分析方法模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getKey())) {
            list.removeIf(p -> !p.getName().contains(methodCriteria.getKey()));
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.人员信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.人员信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.人员信息.getDeleteMethod();
    }
}
