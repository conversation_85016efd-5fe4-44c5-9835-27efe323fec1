package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.Collection;
import java.util.List;


/**
 * QuotationDetail数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
public interface QuotationDetailRepository extends IBaseJpaRepository<DtoQuotationDetail, String> {

    /**
     * 根据订单Id获取费用明细
     * @param orderId 订单id
     * @return 费用明细
     */
    List<DtoQuotationDetail> findByOrderId(String orderId);

    /**
     * 根据订单Ids获取费用明细
     * @param orderIds 订单ids
     * @return 费用明细
     */
    List<DtoQuotationDetail> findByOrderIdIn(Collection<String> orderIds);
}