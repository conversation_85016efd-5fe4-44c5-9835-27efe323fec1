package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.constants.SoapBodyConstants;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.RPSoapBodyUtil;
import com.sinoyd.lims.pro.vo.RPContractVO;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 上海监管平台合同信息条件查询服务策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/08
 */
@Component
public class RPContract extends AbsRegulatoryPlatformRemote<RPContractVO> {

    @Override
    protected void filterCriteria(List<RPContractVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //过滤掉未通过的合同
        list.removeIf(m -> !m.getIsCheck().equals("已通过"));
        //合同标题模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getContractTitle())) {
            list.removeIf(i -> !i.getContractName().contains(methodCriteria.getContractTitle()));
        }
        //合同甲方名称模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getContractPartyAName())) {
            list.removeIf(i -> !i.getEntrustingUnitName().contains(methodCriteria.getContractPartyAName()));
        }
    }

    @Override
    protected String loadFindSoapBody(String userName, String password, BaseCriteria criteria, PageBean<RPContractVO> pageBean) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        Map<String, Object> criteriaMap = new HashMap<>();
        if (StringUtil.isNotEmpty(methodCriteria.getStartTime())) {
            criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "startTime", methodCriteria.getStartTime());
        }
        if (StringUtil.isNotEmpty(methodCriteria.getEndTime())) {
            criteriaMap.put(SoapBodyConstants.LABEL_ITEM_PREFIX + "endTime", methodCriteria.getEndTime());
        }
        return RPSoapBodyUtil.loadSoapBody(userName, password, this.queryMethodName(), criteriaMap);
    }


    @Override
    @Transactional
    public void handlePushResult(RegulatoryPlatformPushVO pushVo, JSONObject result) {
        //查询到当前合同数据
        DtoOrderContract orderContract = orderContractService.findOne(pushVo.getId());
        //若推送成功，测更新合同推送状态
        JSONObject response = getResponse(result, insertMethodName()).getJSONObject("ResultStruct");
        if (response != null && "True".equals(response.getString("succes"))) {
            String rpContractId = response.getString("ID");
            orderContract.setCId(rpContractId);
            orderContract.setIsHavingPut(true);
            orderContractService.save(orderContract);
        }
    }

    @Override
    protected RPContractVO getPushInstance(RegulatoryPlatformPushVO pushVo) {
        //查询到当前合同数据
        DtoOrderContract orderContract = orderContractService.findOne(pushVo.getId());
        if (orderContract != null) {
            return new RPContractVO(orderContract);
        }
        return super.getPushInstance(pushVo);
    }

    @Override
    protected void validatePushData(RegulatoryPlatformPushVO pushVo) {
        DtoOrderContract orderContract = orderContractService.findOne(pushVo.getId());
        if (orderContract == null) {
            throw new RuntimeException("LIMS：推送的合同不存在！");
        }
        if (orderContract.getIsHavingPut()) {
            throw new BaseException("LIMS：合同已推送，请勿重复推送！");
        }
        //校验文档附件
        validateDoc(pushVo);
    }

    @Override
    protected void handlePushDoc(RegulatoryPlatformPushVO pushVo, RPContractVO data) {
        //设置推送数据中的附件名称与附件路径
        handlePushFileName(pushVo, data);
    }


    /**
     * 校验合同文档附件
     *
     * @param pushVo 推送传参
     */
    private void validateDoc(RegulatoryPlatformPushVO pushVo) {
        Optional<DtoDocument> docOp = documentRepository.findByFolderIdAndIsDeletedFalse(pushVo.getId()).stream().findFirst();
        //判断是否上传合同附件
        if (!docOp.isPresent()) {
            throw new BaseException("LIMS：需推送的合同未上传附件！");
        } else {
            //判断附件类型是否为pdf
            if (!docOp.get().getDocSuffix().contains("pdf")) {
                throw new BaseException("LIMS：需推送的合同附件类型必须为pdf类型！");
            }
        }
    }

    /**
     * 设置推送数据中的附件名称与附件路径
     *
     * @param pushVo 推送传参
     * @param data   推送数据
     */
    private void handlePushFileName(RegulatoryPlatformPushVO pushVo, RPContractVO data) {
        Optional<DtoDocument> docOp = documentRepository.findByFolderIdAndIsDeletedFalse(pushVo.getId()).stream().findFirst();
        //判断是否上传合同附件
        if (docOp.isPresent()) {
            String fileName = docOp.get().getFilename();
            String rpFileName = pushRemoteFile(docOp.get(), EnumPRO.EnumRegulatoryPlatformDocType.合同附件.getValue());
            data.setFileName(fileName);
            data.setFilePath(rpFileName);
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.合同信息.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.合同信息.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.合同信息.getDeleteMethod();
    }

    @Override
    protected String resultItemMainLabel() {
        return "CONTRACTS";
    }

    @Override
    protected String resultItemLabel() {
        return "CONTRACT";
    }
}
