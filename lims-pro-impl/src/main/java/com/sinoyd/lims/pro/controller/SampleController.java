package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.criteria.ParentSampleTestCriteria;
import com.sinoyd.lims.pro.criteria.SampleCriteria;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.SchemeService;
import com.sinoyd.lims.pro.service.SampleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * Sample服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: Sample服务")
@RestController
@RequestMapping("api/pro/sample")
public class SampleController extends BaseJpaController<DtoSample, String, SampleService> {
    @Autowired
    private SchemeService schemeService;

    @Autowired
    private ProService proService;

    /**
     * 分页动态条件查询样品
     *
     * @param sampleCriteria 条件参数
     * @return RestResponse<List < DtoSample>>
     */
    @ApiOperation(value = "分页动态条件查询样品", notes = "分页动态条件查询样品")
    @GetMapping
    public RestResponse<List<DtoSample>> findByPage(SampleCriteria sampleCriteria) {
        PageBean<DtoSample> pageBean = super.getPageBean();
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, sampleCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    @ApiOperation(value = "分页查询样品和项目", notes = "分页查询样品和项目")
    @GetMapping("/page")
    public RestResponse<List<DtoSample>> findSampleAndProjectByPage(SampleCriteria sampleCriteria) {
        PageBean<DtoSample> pageBean = super.getPageBean();
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        service.findSampleAndProjectByPage(pageBean, sampleCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


    /**
     * 分页动态条件查询样品测试项目
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoSample>>
     */
    @ApiOperation(value = "分页动态条件查询样品测试项目", notes = "分页动态条件查询样品测试项目")
    @GetMapping(path = "/tests")
    public RestResponse<List<DtoTest>> findByPage(ParentSampleTestCriteria criteria) {
        PageBean<DtoTest> pageBean = new PageBean<>();
        PageBean<DtoSample> page = super.getPageBean();
        pageBean.setRowsPerPage(page.getRowsPerPage());
        pageBean.setPageNo(page.getPageNo());
        pageBean.setSort(page.getSort());
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        service.findSampleTestByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询样品
     *
     * @param id 主键id
     * @return RestResponse<DtoSample>
     */
    @ApiOperation(value = "按主键查询样品", notes = "按主键查询样品")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoSample> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSample> restResponse = new RestResponse<>();
        DtoSample sample = service.findOne(id);
        restResponse.setData(sample);
        restResponse.setRestStatus(StringUtil.isNull(sample) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增外部样品
     *
     * @param sample 实体列表
     * @return RestResponse<DtoSample>
     */
    @ApiOperation(value = "新增外部样品", notes = "新增外部样品")
    @PostMapping(path = "/outside")
    public RestResponse<DtoOutSample> saveOutsideSample(@RequestBody DtoOutSample sample) {
        RestResponse<DtoOutSample> restResponse = new RestResponse<>();
        restResponse.setData(service.saveOutsideSample(sample));
        return restResponse;
    }

    /**
     * 修改样品信息
     *
     * @param sample 实体列表
     * @return RestResponse<DtoSample>
     */
    @ApiOperation(value = "修改样品信息", notes = "修改样品信息")
    @PutMapping
    public RestResponse<DtoSample> update(@RequestBody @Validated DtoSample sample) {
        RestResponse<DtoSample> restResponse = new RestResponse<>();
        restResponse.setData(service.updateSample(sample));
        return restResponse;
    }

    /**
     * 修改外部样品
     *
     * @param dto 实体列表
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "修改外部样品", notes = "修改外部样品")
    @PutMapping(path = "/outside")
    public RestResponse<Boolean> updateOutsideSample(@RequestBody @Validated DtoOutSampleSave dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.updateOutsideSample(dto);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 根据id批量删除外部送样样品
     *
     * @param dto 删除结构
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "根据id批量删除外部送样样品", notes = "根据id批量删除外部送样样品")
    @DeleteMapping(path = "/outside")
    public RestResponse<Boolean> delete(@RequestBody DtoOutSampleDelete dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        schemeService.deleteOutsideSampleClearSampleCode(dto.getSampleIds(), dto.getReceiveId());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 根据id批量删除质控样
     *
     * @param dto 删除结构
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "根据id批量删除质控样", notes = "根据id批量删除质控样")
    @DeleteMapping(path = "/qc")
    public RestResponse<Boolean> delete(@RequestBody DtoQCSampleDelete dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        schemeService.deleteQCSample(dto.getProjectId(), dto.getSampleIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * "根据id纠正样品状态
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id纠正样品状态", notes = "根据id纠正样品状态")
    @PostMapping(path = "/check")
    public RestResponse<Boolean> check(@RequestBody List<String> ids) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.check(ids);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 初始化外部样品
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "初始化外部样品", notes = "初始化外部样品")
    @PostMapping(path = "/init")
    public RestResponse<DtoOutSample> initOutSample(@RequestBody DtoOutSample dto) {
        RestResponse<DtoOutSample> restResponse = new RestResponse<>();
        DtoOutSample sample = service.initOutSample(dto);
        restResponse.setData(sample);
        restResponse.setRestStatus(StringUtil.isNull(sample) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 加载手动修改的样品编号
     *
     * @param sample 实体
     * @return 返回信息
     */
    @ApiOperation(value = "加载手动修改的样品编号", notes = "加载手动修改的样品编号")
    @PostMapping(path = "/code/manual")
    public RestResponse<String> loadManualSampleCode(@RequestBody DtoSample sample) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.loadManualSampleCode(sample));
        return restResponse;
    }

    /**
     * 按主键查询样品详情
     *
     * @param id 主键id
     * @return RestResponse<DtoSample>
     */
    @ApiOperation(value = "按主键查询样品详情", notes = "按主键查询样品详情")
    @GetMapping(path = "/detail/{id}")
    public RestResponse<DtoOutSample> findDetail(@PathVariable(name = "id") String id) {
        RestResponse<DtoOutSample> restResponse = new RestResponse<>();
        DtoOutSample sample = service.findDetail(id);
        restResponse.setData(sample);
        restResponse.setRestStatus(StringUtil.isNull(sample) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 查询样品信息列表
     *
     * @param sampleCriteria 动态参数
     * @return RestResponse<DtoSampleInfo>
     */
    @ApiOperation(value = "查询样品信息列表", notes = "查询样品信息列表")
    @GetMapping(path = "/folderDetails")
    public RestResponse<DtoSampleInfo> findFolderDetails(SampleCriteria sampleCriteria) {
        RestResponse<DtoSampleInfo> restResponse = new RestResponse<>();
        DtoSampleInfo sampleInfo = service.findFolderDetails(sampleCriteria.getReceiveId(), sampleCriteria.getSampleTypeId(),
                sampleCriteria.getSampleFolderId(), sampleCriteria.getSampleKey(), sampleCriteria.getKey(),
                sampleCriteria.getSortType());
        restResponse.setData(sampleInfo);
        restResponse.setRestStatus(StringUtil.isNotNull(sampleInfo) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    @ApiOperation(value = "查询样品信息列表", notes = "查询样品信息列表")
    @GetMapping(path = "/details")
    public RestResponse<DtoSampleInfo> findDetails(SampleCriteria sampleCriteria) {
        RestResponse<DtoSampleInfo> restResponse = new RestResponse<>();
        DtoSampleInfo sampleInfo = service.findDetails(sampleCriteria.getReceiveId(), sampleCriteria.getSampleTypeId(),
                sampleCriteria.getSampleFolderId(), sampleCriteria.getIsProject());
        restResponse.setData(sampleInfo);
        restResponse.setRestStatus(StringUtil.isNotNull(sampleInfo) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 复制样品
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "复制样品", notes = "复制样品")
    @PostMapping(path = "/copy")
    public RestResponse<Boolean> copySample(@RequestBody DtoSampleCopyParam dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        schemeService.copyOutsideSample(dto.getIds(), dto.getReceiveId(), dto.getTimes(), dto.getSamplingTimeBegin());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 新增现场关联样
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "新增现场关联样", notes = "新增现场关联样")
    @PostMapping(path = "/qcSample")
    public RestResponse<List<DtoSample>> addQcSample(@RequestBody DtoQCSampleSave dto) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.addAssociateSample(dto.getIds(), dto.getSampleCategory(), dto.getQcGrade(), dto.getQcType()));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 新增现场关联样
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "新增现场关联样", notes = "新增现场关联样")
    @PostMapping(path = "/qcSampleApi")
    public RestResponse<List<DtoSample>> addApiQcSample(@RequestBody DtoQCSampleSave dto) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.addAssociateSample(dto.getIds(), dto.getSampleCategory(), dto.getQcGrade(), dto.getQcType(), true));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取采样准备点位列表
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param key          关键字
     * @param status       采样状态
     * @return RestResponse<DtoPrepareFolder>
     */
    @ApiOperation(value = "获取采样准备点位列表", notes = "获取采样准备点位列表")
    @GetMapping(path = "/sampleFolder")
    public RestResponse<DtoPrepareFolder> findPrepareFolder(@RequestParam(name = "projectId") String projectId,
                                                            @RequestParam(name = "sampleTypeId") String sampleTypeId,
                                                            @RequestParam(name = "key") String key,
                                                            @RequestParam(name = "status") Integer status) {
        RestResponse<DtoPrepareFolder> restResponse = new RestResponse<>();
        restResponse.setData(service.findPrepareFolder(projectId, sampleTypeId, key, status));
        return restResponse;
    }

    /**
     * 建立样品编号
     *
     * @param dto 传输实体
     * @return RestResponse<DtoPrepareFolder>
     */
    @ApiOperation(value = "建立样品编号", notes = "建立样品编号")
    @PostMapping(path = "/code")
    public RestResponse<List<DtoSample>> createSampleCode(@RequestBody DtoPrepareSample dto) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        List<DtoSample> samples = service.createSampleCode(dto.getProjectId(), dto.getIds(), dto.getSamplingTime(), dto.getSamplingPersonId(), dto.getSamplingPerson(), dto.getSamplingPersonConfig());
        restResponse.setData(samples);
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        restResponse.setMsg(StringUtil.isEmpty(samples) ? "所选样品已存在编号！" : "操作成功");
        return restResponse;
    }

    /**
     * 清除样品编号
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "清除样品编号", notes = "清除样品编号")
    @PostMapping(path = "/clear")
    public RestResponse<Boolean> clearSampleCode(@RequestBody DtoComplexQuery dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        List<String> receiveIds = service.clearCodeBackRecordIds(dto.getIds());
        if (receiveIds.size() > 0) {
            proService.checkReceiveSampleRecord(receiveIds);
        }
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 作废样品
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "作废样品", notes = "作废样品")
    @PostMapping(path = "/invalidSamples")
    public RestResponse<Boolean> invalidSamples(@RequestBody DtoComplexQuery dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.invalidSamples(dto.getIds(), dto.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 取消作废样品
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "取消作废样品", notes = "取消作废样品")
    @PostMapping(path = "/invalidCancelSamples")
    public RestResponse<Boolean> invalidCancelSamples(@RequestBody DtoComplexQuery dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.invalidCancelSamples(dto.getIds(), dto.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 获取所选样品id的原样及现场质控样
     *
     * @param dto 传输实体
     * @return RestResponse<List < DtoSample>>>
     */
    @ApiOperation(value = "获取所选样品id的原样及现场质控样", notes = "获取所选样品id的原样及现场质控样")
    @PostMapping(path = "/localSample/query")
    public RestResponse<List<DtoSample>> findAllLocalSample(@RequestBody DtoComplexQuery dto) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAllLocalSample(dto.getIds()));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取所选原样的现场关联样
     *
     * @param dto 传输实体
     * @return RestResponse<List < DtoSample>>>
     */
    @ApiOperation(value = "获取所选原样的现场关联样", notes = "获取所选原样的现场关联样")
    @PostMapping(path = "/qcSample/query")
    public RestResponse<List<DtoSample>> getLocalAssociateSample(@RequestBody DtoComplexQuery dto) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.getLocalAssociateSample(dto.getIds()));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取内部送样待选样品
     *
     * @param sampleCriteria 动态参数
     * @return RestResponse<List < DtoSample>>>
     */
    @ApiOperation(value = "获取内部送样待选样品", notes = "获取内部送样待选样品")
    @GetMapping(path = "/removed")
    public RestResponse<List<DtoSample>> findRemovedSamples(SampleCriteria sampleCriteria) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.findRemovedSamples(sampleCriteria.getProjectId(), sampleCriteria.getSampleTypeId(), sampleCriteria.getSampleFolderId(), sampleCriteria.getKey()));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 按仪器使用记录id查询样品
     *
     * @param instrumentUseRecordId 仪器使用记录id
     * @return RestResponse<List < DtoSample>>>
     */
    @ApiOperation(value = "按仪器使用记录id查询样品", notes = "按仪器使用记录id查询样品")
    @GetMapping(path = "/instrumentUseRecord/{instrumentUseRecordId}")
    public RestResponse<List<DtoSample>> findSamplesByUseRecord(@PathVariable(name = "instrumentUseRecordId") String instrumentUseRecordId) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSamplesByUseRecord(instrumentUseRecordId));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取样品模板
     *
     * @param dto 实体
     * @return RestResponse<List < DtoSample>>>
     */
    @ApiOperation(value = "获取样品模板", notes = "获取样品模板")
    @PostMapping(path = "/template")
    public RestResponse<List<Map<String, Object>>> findSampleTemplateDetail(@RequestBody DtoSampleItemParams dto) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleTemplateDetail(dto));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取对应项目检测类型指标下的原样及质控样品数据
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param testId       测试项目id
     * @return RestResponse<List < DtoQCSample>>
     */
    @ApiOperation(value = "获取对应项目检测类型指标下的原样及质控样品数据", notes = "获取对应项目检测类型指标下的原样及质控样品数据")
    @GetMapping(path = "/qcInfo")
    public RestResponse<List<DtoQCSample>> findSampleTemplateDetail(@RequestParam(name = "projectId") String projectId,
                                                                    @RequestParam(name = "sampleTypeId") String sampleTypeId,
                                                                    @RequestParam(name = "testId") String testId) {
        RestResponse<List<DtoQCSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.findQCSample(projectId, sampleTypeId, testId));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }


    @ApiOperation(value = "获取对应项目检测类型指标下的原样及质控样品数据", notes = "获取对应项目检测类型指标下的原样及质控样品数据")
    @GetMapping(path = "/log/{id}")
    public RestResponse<List<DtoLog>> findSampleLog(@PathVariable("id") String sampleId) {
        RestResponse<List<DtoLog>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleLog(sampleId));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 批量添加质控样时获取当前测试项目上一次配置的量纲
     *
     * @param qcType     质控类型
     * @param testIdList 测试项目列表
     * @return RestResponse<List < DtoQCSample>>
     */
    @ApiOperation(value = "批量添加质控样时获取当前测试项目上一次配置的量纲", notes = "批量添加质控样时获取当前测试项目上一次配置的量纲")
    @PostMapping(path = "/qcDimension")
    public RestResponse<Map<String, Map<String, String>>> findLastDimension(Integer qcType, @RequestBody List<String> testIdList) {
        RestResponse<Map<String, Map<String, String>>> restResponse = new RestResponse<>();
        restResponse.setData(service.getQcDimensionInfoForTests(qcType, testIdList));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 选定日期样品登记明细
     *
     * @param date 选定日期
     * @return 样品列表
     */
    @ApiOperation(value = "选定日期样品登记明细", notes = "选定日期样品登记明细")
    @GetMapping(path = "/findDateRegisteredSampleList/{date}")
    public RestResponse<List<DtoSample>> findDateRegisteredSampleList(@PathVariable(name = "date") String date) {
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        restResponse.setData(service.findDateRegisteredSampleList(date));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 样品参数同步ocr识别结果
     *
     * @param ocrDataSyncParams 同步参数
     * @return RestResponse<List < DtoQCSample>>
     */
    @ApiOperation(value = "样品参数同步ocr识别结果", notes = "样品参数同步ocr识别结果")
    @PostMapping(path = "/syncOcrParamsData")
    public RestResponse<Void> syncOcrParamsData(@RequestBody DtoOcrDataSyncParams ocrDataSyncParams) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.syncOcrParamsData(ocrDataSyncParams);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 样品参数同步例行点位信息
     *
     * @param receiveId 送样单Id
     * @return RestResponse<List < DtoQCSample>>
     */
    @ApiOperation(value = "样品参数同步例行点位信息", notes = "样品参数同步例行点位信息")
    @PostMapping(path = "/syncFixedPoint/{receiveId}")
    public RestResponse<Void> syncFixedPoint(@PathVariable String receiveId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.syncFixedPoint(receiveId);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 样品同步移动端签到点位的经纬度数据
     *
     * @param receiveId 送样单Id
     * @return RestResponse<List < DtoQCSample>>
     */
    @ApiOperation(value = "样品同步移动端签到点位的经纬度数据", notes = "样品同步移动端签到点位的经纬度数据")
    @PostMapping(path = "/syncFolderLocation/{receiveId}")
    public RestResponse<Void> syncFolderLocation(@PathVariable String receiveId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.syncFolderLocation(receiveId);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 送样类批量添加测试项目
     *
     * @param samplingFrequencyTest 实体列l表
     * @return RestResponse<DtoSamplingFrequencyTest>
     */
    @ApiOperation(value = "送样类批量添加测试项目", notes = "送样类批量添加测试项目")
    @PostMapping("/batchAddTest")
    public RestResponse<Void> batchAddTest(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.batchAddTest(samplingFrequencyTest);
        return restResponse;
    }


    /**
     * 送样类批量填写参数
     *
     * @param sampleParamsVO 样品参数传输实体
     * @return RestResponse<DtoSamplingFrequencyTest>
     */
    @ApiOperation(value = "送样类批量填写参数", notes = "送样类批量填写参数")
    @PostMapping("/batchSampleParams")
    public RestResponse<Void> batchSampleParams(@RequestBody DtoSampleParamsVO sampleParamsVO) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.batchSampleParams(sampleParamsVO);
        return restResponse;
    }

}