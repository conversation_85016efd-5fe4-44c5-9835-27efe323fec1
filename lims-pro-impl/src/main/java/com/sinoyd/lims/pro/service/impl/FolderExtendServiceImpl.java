package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTest2ShSamplingMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.Test2ShSamplingMethodRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTestCount;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.SampleFolderRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTestRepository;
import com.sinoyd.lims.pro.service.FolderExtendService;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.vo.RPAnalyzeMethodVO;
import com.sinoyd.lims.pro.vo.TestShMethodUpdateBatchVO;
import com.sinoyd.lims.strategy.context.IRegulatoryPlatformContextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * FolderExtend操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Slf4j
@Service
public class FolderExtendServiceImpl implements FolderExtendService {

    private SampleFolderRepository sampleFolderRepository;

    private SampleFolderService sampleFolderService;

    private SamplingFrequencyRepository samplingFrequencyRepository;

    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    private TestService testService;

    private TestRepository testRepository;

    private SampleTypeService sampleTypeService;

    private IRegulatoryPlatformContextService regulatoryPlatformContextService;

    private Test2ShSamplingMethodRepository test2ShSamplingMethodRepository;

    @Override
    public void batchUpdateAnalyzeMethod(TestShMethodUpdateBatchVO batchVO) {
        List<DtoTest> testList = testRepository.findAll(batchVO.getTestIds());
        testList.forEach(f -> {
            f.setShMethodId(batchVO.getShMethodId());
            f.setShMethodName(batchVO.getShMethodName());
        });
        //匹配RP测试项目，设置匹配状态
        matchRpTestStatus(testList);
        testRepository.save(testList);
    }

    @Override
    public void batchUpdateSamplingMethod(TestShMethodUpdateBatchVO batchVO) {
        if (StringUtil.isNotEmpty(batchVO.getTestIds())){
            //查询到当前点位数据
            DtoSampleFolder sampleFolder = sampleFolderRepository.findOne(batchVO.getSampleFolderId());
            if (sampleFolder != null){
                //根据检测类型与测试项目id集合查询到对应的测试项目检测类型关联监管平台采样方法数据
                List<DtoTest2ShSamplingMethod> shSamplingMethods = test2ShSamplingMethodRepository.findByTestIdInAndSampleTypeId(batchVO.getTestIds(), sampleFolder.getSampleTypeId());
                //需要更新的测试项目关联监管平台采样方法数据
                List<DtoTest2ShSamplingMethod> updateShSamplingMethods = new ArrayList<>();
                //使用关联的测试项目进行数据的更新，防止当前测试项目还没创建关联数据
                List<DtoTest> testList = testRepository.findAll(batchVO.getTestIds());
                for (DtoTest test : testList) {
                    DtoTest2ShSamplingMethod shSamplingMethod = shSamplingMethods.stream()
                            .filter(p -> test.getId().equals(p.getTestId())).findFirst()
                            .orElse(new DtoTest2ShSamplingMethod(test.getId(), sampleFolder.getSampleTypeId()));
                    shSamplingMethod.setShSamplingMethodId(batchVO.getShSamplingMethodId());
                    shSamplingMethod.setShSamplingMethodName(batchVO.getShSamplingMethodName());
                    updateShSamplingMethods.add(shSamplingMethod);
                }
                if (StringUtil.isNotEmpty(updateShSamplingMethods)){
                    test2ShSamplingMethodRepository.save(updateShSamplingMethods);
                }
            }
        }
    }

    @Override
    public List<DtoSampleFolder> findFoldersByProjectId(String projectId, String sampleTypeId) {
        List<DtoSampleFolder> sampleFolders = StringUtil.isNotEmpty(sampleTypeId)
                ? sampleFolderRepository.findByProjectIdAndSampleTypeId(projectId, sampleTypeId)
                : sampleFolderRepository.findByProjectId(projectId);
        //查询检测类型id
        Set<String> sampleTypeIds = sampleFolders.stream().map(DtoSampleFolder::getSampleTypeId).collect(Collectors.toSet());
        Map<String, String> sampleTypeMap = sampleTypeService.findAll(sampleTypeIds).stream()
                .collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
        List<String> sampleFolderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoSamplingFrequency> frequencies = samplingFrequencyRepository.findBySampleFolderIdIn(sampleFolderIds);
        //查询所有点位测试项目
        TestCriteria testCriteria = new TestCriteria();
        testCriteria.setSampleFolderIds(sampleFolderIds);
        Map<String, List<DtoTest>> sampleFolderGroupTest = findSampleFolderGroupTest(testCriteria);
        sampleFolders.forEach(s -> {
            List<DtoSamplingFrequency> frequencies2Folder = frequencies.stream().filter(f -> f.getSampleFolderId().equals(s.getId())).collect(Collectors.toList());
            Optional<DtoSamplingFrequency> frequency = frequencies2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getPeriodCount));
            frequency.ifPresent(f -> s.setPeriodCount(f.getPeriodCount()));
            frequency = frequencies2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod));
            frequency.ifPresent(f -> s.setTimePerPeriod(f.getTimePerPeriod()));
            s.setSampleTypeName(sampleTypeMap.getOrDefault(s.getSampleTypeId(), ""));
            //处理测试项目匹配状态
            Integer rpTestMatchStatus = EnumLIM.EnumShTestMatchStatus.未匹配.getValue();
            List<DtoTest> folderTests = sampleFolderGroupTest.getOrDefault(s.getId(), new ArrayList<>());
            if (StringUtil.isNotEmpty(folderTests)) {
                if (folderTests.stream().allMatch(p -> EnumLIM.EnumShTestMatchStatus.完美匹配.getValue().equals(p.getShMatchStatus()))) {
                    rpTestMatchStatus = EnumLIM.EnumShTestMatchStatus.完美匹配.getValue();
                } else if (folderTests.stream().allMatch(p -> EnumLIM.EnumShTestMatchStatus.未匹配.getValue().equals(p.getShMatchStatus()))) {
                    rpTestMatchStatus = EnumLIM.EnumShTestMatchStatus.未匹配.getValue();
                } else {
                    rpTestMatchStatus = EnumLIM.EnumShTestMatchStatus.异常匹配.getValue();
                }
            }
            s.setShTestMatchStatus(rpTestMatchStatus);
        });
        return sampleFolders.stream().sorted(Comparator.comparing(DtoSampleFolder::getWatchSpot)).collect(Collectors.toList());
    }

    @Override
    public List<DtoTest> findBySampleFolderId(BaseCriteria criteria) {
        return findSampleFolderGroupTest(criteria).values()
                .stream().filter(StringUtil::isNotEmpty)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 查询监管平台推送点位测试项目数据
     *
     * @param criteria 查询条件
     * @return 监管平台推送点位测试项目数据
     */
    private List<DtoTest> findPushTestListByCriteria(BaseCriteria criteria) {
        TestCriteria testCriteria = (TestCriteria) criteria;
        Set<String> sampleFolderIds = new HashSet<>();
        if (StringUtil.isNotEmpty(testCriteria.getSampleFolderId())) {
            sampleFolderIds.add(testCriteria.getSampleFolderId());
        }
        if (StringUtil.isNotEmpty(testCriteria.getSampleFolderIds())) {
            sampleFolderIds.addAll(testCriteria.getSampleFolderIds());
        }

        //查询点位关联测试项目数据
        List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<DtoSampleFolderTestCount> countList = sampleFolderService.querySampleFolderTestCount(sampleFolderIds);
        //查询所有测试项目数据
        List<String> testIds = frequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findAll(testIds);
        //设置测试项目的样品数量
        for (DtoTest test : testList) {
            countList.stream().filter(v -> test.getId().equals(v.getTestId())).findFirst().ifPresent(v -> test.setSampleCount(v.getSampleCount()));
        }
        //根据查询条件过滤测试项目数据
        if (StringUtil.isNotEmpty(testCriteria.getAnalyzeItemKey())) {
            testList = testList.stream().filter(r -> r.getRedAnalyzeItemName().contains(testCriteria.getAnalyzeItemKey()))
                    .collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(testCriteria.getAnalyzeMethodKey())) {
            testList = testList.stream()
                    .filter(r -> r.getRedCountryStandard().contains(testCriteria.getAnalyzeMethodKey())
                            || r.getRedAnalyzeMethodName().contains(testCriteria.getAnalyzeMethodKey()))
                    .collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(testCriteria.getKey())) {
            testList = testList.stream()
                    .filter(r -> r.getRedAnalyzeItemName().contains(testCriteria.getKey())
                            || r.getRedAnalyzeMethodName().contains(testCriteria.getKey())
                            || r.getRedCountryStandard().contains(testCriteria.getKey()))
                    .collect(Collectors.toList());
        }
        testList.sort(Comparator.comparing(DtoTest::getRedAnalyzeItemName));
        return testList;
    }

    @Override
    public Map<String, List<DtoTest>> findSampleFolderGroupTest(BaseCriteria criteria) {
        TestCriteria testCriteria = (TestCriteria) criteria;
        //查询测试项目数据
        List<DtoTest> testList = findPushTestListByCriteria(testCriteria);
        Set<String> sampleFolderIds = new HashSet<>();
        if (StringUtil.isNotEmpty(testCriteria.getSampleFolderId())) {
            sampleFolderIds.add(testCriteria.getSampleFolderId());
        }
        if (StringUtil.isNotEmpty(testCriteria.getSampleFolderIds())) {
            sampleFolderIds.addAll(testCriteria.getSampleFolderIds());
        }
        //查询所有点位数据
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(sampleFolderIds);
        //查询测试项目关联的监管平台采样方法数据
        List<String> testIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
        Map<String, List<DtoTest2ShSamplingMethod>> shSamplingMethodTestGroup = test2ShSamplingMethodRepository.findByTestIdIn(testIds)
                .stream().collect(Collectors.groupingBy(DtoTest2ShSamplingMethod::getTestId));
        //查询点位关联测试项目数据
        List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<DtoSampleFolderTestCount> countList = sampleFolderService.querySampleFolderTestCount(sampleFolderIds);
        //对点位测试项目进行分组
        Map<String, List<DtoSamplingFrequencyTest>> frequencyTestGroup = frequencyTests.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId));

        Map<String, List<DtoTest>> folderTestGroup = new HashMap<>();
        for (DtoSampleFolder sampleFolder : sampleFolders) {
            //点位下测试项目id集合
            List<String> folderTestIds = frequencyTestGroup.get(sampleFolder.getId()).stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> folderTests = new ArrayList<>();
            for (DtoTest test : testList) {
                if (folderTestIds.contains(test.getId())) {
                    DtoTest testTemp = new DtoTest();
                    BeanUtils.copyProperties(test, testTemp);
                    folderTests.add(testTemp);
                }
            }

            for (DtoTest test : folderTests) {
                //处理样品数量
                countList.stream().filter(v -> test.getId().equals(v.getTestId()) && sampleFolder.getId().equals(v.getSampleFolderId())).findFirst()
                        .ifPresent(v -> test.setSampleCount(v.getSampleCount()));
                //根据点位的检测类型获取到测试项目下检测类型的监管平台采样方法
                Optional<DtoTest2ShSamplingMethod> shSamplingMethodOp = shSamplingMethodTestGroup
                        .getOrDefault(test.getId(), new ArrayList<>()).stream()
                        .filter(p -> sampleFolder.getSampleTypeId().equals(p.getSampleTypeId())).findFirst();
                shSamplingMethodOp.ifPresent(shSamplingMethod -> {
                    test.setShSamplingMethodId(shSamplingMethod.getShSamplingMethodId());
                    test.setShSamplingMethodName(shSamplingMethod.getShSamplingMethodName());
                });
            }
            folderTestGroup.put(sampleFolder.getId(), folderTests);
        }
//        for (Map.Entry<String, List<DtoSamplingFrequencyTest>> entry : frequencyTestGroup.entrySet()) {
//            //点位下测试项目id集合
//            List<String> folderTestIds = entry.getValue().stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
//            List<DtoTest> folderTests = new ArrayList<>();
//            for (DtoTest test : testList) {
//                if (folderTestIds.contains(test.getId())) {
//                    DtoTest testTemp = new DtoTest();
//                    BeanUtils.copyProperties(test, testTemp);
//                    folderTests.add(testTemp);
//                }
//            }
//            //处理样品数量
//            for (DtoTest test : folderTests) {
//                countList.stream().filter(v -> test.getId().equals(v.getTestId()) && entry.getKey().equals(v.getSampleFolderId())).findFirst()
//                        .ifPresent(v -> test.setSampleCount(v.getSampleCount()));
//            }
//            folderTestGroup.put(entry.getKey(), folderTests);
//        }
        return folderTestGroup;
    }

    @Override
    public List<DtoTest> findByProjectId(String projectId) {
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByProjectId(projectId);
        if (StringUtil.isEmpty(sampleFolders)) {
            throw new BaseException("该任务下没有点位");
        }
        List<String> sampleFolderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        return testService.findAll(frequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList()));
    }

    /**
     * 填充冗余字段
     *
     * @param testList 测试项目数据
     */
    @Override
    public void matchRpTestStatus(List<DtoTest> testList) {
        PageBean<RPAnalyzeMethodVO> pb = new PageBean<>();
        pb.setRowsPerPage(Integer.MAX_VALUE);
        regulatoryPlatformContextService.findByPage(new BaseRegulatoryPlatformCriteria(),
                pb, EnumPRO.EnumRegulatoryPlatformMethod.分析方法.getQueryMethod());
        List<RPAnalyzeMethodVO> rpMethodList = pb.getData();
        for (DtoTest test : testList) {
            Integer matchStatus = StringUtil.isNotEmpty(test.getShMethodId())
                    ? EnumLIM.EnumShTestMatchStatus.异常匹配.getValue() : EnumLIM.EnumShTestMatchStatus.未匹配.getValue();
            String matchMsg = "";
            Optional<RPAnalyzeMethodVO> rpMethod = rpMethodList.stream()
                    .filter(m -> StringUtil.isNotNull(m.getMethodId()) && (m.getMethodId()).equals(test.getShMethodId())).findFirst();
            if (rpMethod.isPresent()) {
                if (StringUtil.isNotNull(rpMethod.get().getItemName())
                        && rpMethod.get().getItemName().equals(test.getRedAnalyzeItemName())) {
                    matchStatus = EnumLIM.EnumShTestMatchStatus.完美匹配.getValue();
                } else {
                    matchMsg = "匹配异常：选择监管平台分析方法对应的分析项目名称与平台不匹配";
                }
            } else {
                matchMsg = "匹配异常：未在监管平台中查询到匹配的分析方法";
            }
            test.setShMatchStatus(matchStatus);
            test.setShMatchMessage(matchMsg);
        }
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestRepository(SamplingFrequencyTestRepository samplingFrequencyTestRepository) {
        this.samplingFrequencyTestRepository = samplingFrequencyTestRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    public void setRegulatoryPlatformContextService(IRegulatoryPlatformContextService regulatoryPlatformContextService) {
        this.regulatoryPlatformContextService = regulatoryPlatformContextService;
    }

    @Autowired
    public void setTest2ShSamplingMethodRepository(Test2ShSamplingMethodRepository test2ShSamplingMethodRepository) {
        this.test2ShSamplingMethodRepository = test2ShSamplingMethodRepository;
    }
}
