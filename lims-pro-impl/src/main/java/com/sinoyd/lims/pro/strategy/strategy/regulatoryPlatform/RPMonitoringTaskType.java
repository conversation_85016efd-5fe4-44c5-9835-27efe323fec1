package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.enums.EnumPRO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 上海监管平台委托任务类型策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Component
public class RPMonitoringTaskType extends AbsRegulatoryPlatformRemote<String> {

    @Override
    public List<String> findByPage(BaseCriteria criteria, PageBean<String> pageBean) {
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        return super.findByPage(criteria, pageBean);
    }

    @Override
    protected List<String> parseResult(JSONObject response, String methodName) {
        List<String> taskType = new ArrayList<>();
        //TODO: 错误处理，附件名称获取
        JSONArray typeArray = getResponse(response, methodName)
                .getJSONObject(resultItemMainLabel()).getJSONArray(resultItemLabel());
        for (Object obj : typeArray) {
            taskType.add(obj.toString());
        }
        return taskType;
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.监测任务类别.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.监测任务类别.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.监测任务类别.getDeleteMethod();
    }
}
