package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * QualityManage查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityManageCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 样品id
    */
    private String sampleId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and q.anaId = a.id and a.isDeleted = 0");

        if (StringUtil.isNotEmpty(this.sampleId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleId)) {
            condition.append(" and a.sampleId = :sampleId");
            values.put("sampleId", this.sampleId);
        }

        return condition.toString();
    }
}