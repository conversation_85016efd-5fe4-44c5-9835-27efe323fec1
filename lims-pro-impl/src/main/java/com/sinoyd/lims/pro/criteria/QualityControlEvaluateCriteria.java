package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


/**
 * QualityControlEvaluate查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 202年11月10日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityControlEvaluateCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 对象id列表
     */
    private List<String> objectIdList;

    /**
     * 是否合格
     */
    private Boolean isPass;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 分析项目
     */
    private String analyseItem;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        //是否合格
        if (StringUtil.isNotNull(this.isPass)) {
            condition.append(" and isPass = :isPass ");
            values.put("isPass", this.isPass);
        }
        if (StringUtil.isNotEmpty(this.objectIdList)) {
            condition.append(" and objectId in :objectIdList ");
            values.put("objectIdList", this.objectIdList);
        }
        return condition.toString();
    }
}