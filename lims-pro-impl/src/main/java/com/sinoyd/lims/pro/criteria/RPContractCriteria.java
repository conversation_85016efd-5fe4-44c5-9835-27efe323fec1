package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 监管平台合同查询条件
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RPContractCriteria extends BaseRegulatoryPlatformCriteria{

    /**
     * 合同来源 枚举 {@link com.sinoyd.lims.pro.enums.EnumPRO.EnumContractSource}
     */
    private Integer contractSource;
}
