package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.service.SamplingFrequencyService;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 点位频次操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/23
 * @since V100R001
 */
@Service
public class SamplingFrequencyServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingFrequency, String, SamplingFrequencyRepository> implements SamplingFrequencyService {

    private SampleFolderService sampleFolderService;

    private SampleTypeService sampleTypeService;

    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    private SampleRepository sampleRepository;

    private SampleFolderRepository sampleFolderRepository;

    private FixedpointRepository fixedpointRepository;

    private EnterpriseRepository enterpriseRepository;

    private SamplingArrangeRepository samplingArrangeRepository;

    @Override
    public void findByPage(PageBean<DtoSamplingFrequency> pb, BaseCriteria samplingFrequencyCriteria) {
        pb.setEntityName("DtoSamplingFrequency a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, samplingFrequencyCriteria);
    }

    /**
     * 获取采样准备点位列表
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param key          关键字
     */
    @Override
    public List<Object> findPrepareSamplingFrequency(String projectId, String sampleTypeId, String key, Integer status) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoSamplingFrequency> pb = new PageBean<>();
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoSamplingFrequency sf, DtoSampleFolder s");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.DtoSamplingFrequency(sf.id, sf.sampleFolderId, sf.periodCount," +
                "sf.timePerPeriod, sf.folderType, sf.samplePerTime, sf.orgId, s.watchSpot, s.sampleTypeId)");
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.addCondition(" and sf.sampleFolderId = s.id");
        pb.addCondition(" and s.projectId = :projectId");
        values.put("projectId", projectId);
        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(sampleTypeId)) {
            pb.addCondition(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", sampleTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(key)) {
            pb.addCondition(" and s.watchSpot like :key");
            values.put("key", "%" + key + "%");
        }
        comRepository.findByPage(pb, values);
        List<DtoSamplingFrequency> samplingFrequencyList = pb.getData();
        if (StringUtil.isNotEmpty(samplingFrequencyList)) {
            List<String> sampleTypeIds = samplingFrequencyList.stream().map(DtoSamplingFrequency::getSampleTypeId)
                    .distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeService.findAllDeleted(sampleTypeIds);
            for (DtoSamplingFrequency sf : samplingFrequencyList) {
                sf.setPeriodName(String.format("第%s周期", MathUtil.toChinese(sf.getPeriodCount())));
                sf.setTimeName(String.format("第%s批次", MathUtil.toChinese(sf.getTimePerPeriod())));
                Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.stream().filter(s -> sf.getSampleTypeId().equals(s.getId()))
                        .findFirst();
                sampleTypeOptional.ifPresent(s -> sf.setSampleTypeName(s.getTypeName()));
            }
        }

        List<String> folderIds = samplingFrequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList());
        Map<String, DtoSampleFolder> folderMap = new HashMap<>();
        Map<String, DtoFixedpoint> fixedPointMap = new HashMap<>();
        Map<String, DtoEnterprise> enterpriseMap = new HashMap<>();
        if (StringUtil.isNotEmpty(folderIds)) {
            List<DtoSampleFolder> folderList = sampleFolderRepository.findAll(folderIds);
            folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
            List<String> fixPointIdList = folderList.stream().map(DtoSampleFolder::getFixedPointId).distinct().collect(Collectors.toList());
            List<DtoFixedpoint> fixedPointList = StringUtil.isNotEmpty(fixPointIdList) ? fixedpointRepository.findAll(fixPointIdList) : new ArrayList<>();
            fixedPointMap = fixedPointList.stream().collect(Collectors.toMap(DtoFixedpoint::getId, dto -> dto));
            List<String> enterpriseIdList = fixedPointList.stream().map(DtoFixedpoint::getEnterpriseId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            List<DtoEnterprise> enterpriseList = StringUtil.isNotEmpty(enterpriseIdList) ? enterpriseRepository.findAll(enterpriseIdList) : new ArrayList<>();
            enterpriseMap = enterpriseList.stream().collect(Collectors.toMap(DtoEnterprise::getId, dto -> dto));
        }

        List<DtoSample> sampleList = sampleRepository.findBySampleFolderIdInOrderById(folderIds).stream()
                .filter(p -> (!p.getIsDeleted() || EnumPRO.EnumSampleStatus.样品作废.name().equals(p.getStatus())))
                .collect(Collectors.toList());
        for (DtoSamplingFrequency samplingFrequency : samplingFrequencyList) {
            DtoSample sample = sampleList.stream().filter(p -> samplingFrequency.getSampleFolderId().equals(p.getSampleFolderId())
                    && samplingFrequency.getSamplePerTime().equals(p.getSampleOrder())
                    && samplingFrequency.getTimePerPeriod().equals(p.getTimesOrder())
                    && samplingFrequency.getPeriodCount().equals(p.getCycleOrder())).findFirst().orElse(null);
            if (StringUtil.isNotNull(sample)) {
                samplingFrequency.setSampleStatus(sample.getStatus());
            }
            DtoEnterprise enterprise = null;
            DtoSampleFolder folder = folderMap.get(samplingFrequency.getSampleFolderId());
            if (StringUtil.isNotNull(folder)) {
                DtoFixedpoint fixedPoint = fixedPointMap.get(folder.getFixedPointId());
                if (StringUtil.isNotNull(fixedPoint) && enterpriseMap.containsKey(fixedPoint.getEnterpriseId())) {
                    enterprise = enterpriseMap.get(fixedPoint.getEnterpriseId());
                }
            }
            samplingFrequency.setCustomerId(StringUtil.isNotNull(enterprise) ? enterprise.getId() : UUIDHelper.GUID_EMPTY);
            samplingFrequency.setCustomerName(StringUtil.isNotNull(enterprise) ? enterprise.getName() : "");
        }

        // 根据采样状态过滤
        if (StringUtil.isNotNull(status)) {
            if (EnumPRO.EnumSampledStatus.已采毕.getValue().equals(status)) {
                sampleList = sampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getCode())).collect(Collectors.toList());
            } else {
                sampleList = sampleList.stream().filter(p -> StringUtil.isEmpty(p.getCode())).collect(Collectors.toList());
            }
            // 筛选点位频次id过滤
            List<String> samplingFrequencyIds = sampleList.stream().map(DtoSample::getSamplingFrequencyId).collect(Collectors.toList());
            samplingFrequencyList = samplingFrequencyList.stream().filter(p -> samplingFrequencyIds.contains(p.getId())).collect(Collectors.toList());
        }

        samplingFrequencyList.sort(Comparator.comparing(DtoSamplingFrequency::getSampleTypeName).thenComparing(DtoSamplingFrequency::getWatchSpot).
                thenComparing(DtoSamplingFrequency::getPeriodCount).thenComparing(DtoSamplingFrequency::getTimePerPeriod));
        List<Object> resList = new ArrayList<>();
        resList.add(samplingFrequencyList);
        resList.add(sampleList);
        return resList;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        DtoSamplingFrequency frequency = repository.findOne(idStr);
        if (StringUtil.isNotNull(frequency)) {
            Integer count = repository.countBySampleFolderIdAndIdNot(frequency.getSampleFolderId(), idStr);
            if (count == 0) {//该点位下已经不存在频次
                sampleFolderService.logicDeleteById(frequency.getSampleFolderId());
            }
            List<String> frequencyIds = new ArrayList<>();
            frequencyIds.add(idStr);
            samplingFrequencyTestRepository.deleteBySamplingFrequencyIdIn(frequencyIds);
            syncFolderPeriod(Collections.singletonList(frequency), true);
            return super.logicDeleteById(id);
        }
        return 0;
    }


    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> frequencyIds = new ArrayList<>();
        for (Object id : ids) {
            frequencyIds.add(String.valueOf(id));
        }
        //查询需要删除的频次
        List<DtoSamplingFrequency> frequencys = repository.findAll(frequencyIds);
        //读取对应频次下的点位id并读取所有对应点位下的频次
        List<String> sampleFolderIds = frequencys.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSamplingFrequency> folderFrequencys = repository.findBySampleFolderIdIn(sampleFolderIds);
        //将需被移除的频次过滤掉，并筛选出保留下的点位id集合
        folderFrequencys = folderFrequencys.stream().filter(p -> !frequencyIds.contains(p.getId())).collect(Collectors.toList());
        List<String> existSampleFolderIds = folderFrequencys.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList());
        //在原有频次点位id集合内移除所有保留下的点位id集合，故移除后的点位id是需要删除的
        sampleFolderIds.removeAll(existSampleFolderIds);
        if (sampleFolderIds.size() > 0) {
            sampleFolderService.logicDeleteById(sampleFolderIds);
        }
        samplingFrequencyTestRepository.deleteBySamplingFrequencyIdIn(frequencyIds);
        syncFolderPeriod(frequencys, true);
        return super.logicDeleteById(ids);
    }


    @Override
    @Transactional
    public DtoSamplingFrequency save(DtoSamplingFrequency entity) {
        syncFolderPeriod(Collections.singletonList(entity), false);
        return super.save(entity);
    }

    @Override
    @Transactional
    public List<DtoSamplingFrequency> save(Collection<DtoSamplingFrequency> entities) {
        syncFolderPeriod((List<DtoSamplingFrequency>) entities, false);
        return super.save(entities);
    }

    @Override
    @Transactional
    public void delete(String key) {
        DtoSamplingFrequency dtoSamplingFrequency = repository.findOne(key);
        syncFolderPeriod(Collections.singletonList(dtoSamplingFrequency), true);
        super.delete(key);
    }

    @Override
    @Transactional
    public void delete(Collection<DtoSamplingFrequency> entities) {
        syncFolderPeriod((List<DtoSamplingFrequency>) entities, true);
        super.delete(entities);
    }

    @Override
    @Transactional
    public void delete(DtoSamplingFrequency entity) {
        syncFolderPeriod(Collections.singletonList(entity), true);
        super.delete(entity);
    }

    @Override
    @Transactional
    public Integer deleteBySampleFolderId(String sampleFolderId) {
        List<DtoSamplingFrequency> list = repository.findBySampleFolderId(sampleFolderId);
        syncFolderPeriod(list, true);
        return repository.deleteBySampleFolderId(sampleFolderId);
    }

    @Override
    @Transactional
    public Integer deleteBySampleFolderIdIn(List<String> sampleFolderIds) {
        List<DtoSamplingFrequency> list = repository.findBySampleFolderIdIn(sampleFolderIds);
        syncFolderPeriod(list, true);
        return repository.deleteBySampleFolderIdIn(sampleFolderIds);
    }

    @Override
    @Transactional
    public Integer updateSampleFolderId(String id, String sampleFolderId) {
        return repository.updateSampleFolderId(id, sampleFolderId);
    }


    @Override
    @Transactional
    public void syncFolderPeriod(List<DtoSamplingFrequency> entityList, boolean isDelete) {
        //更新容器
        List<String> waitSavePeriodIdList;
        List<String> waitRemovePeriodIdList;
        List<DtoSamplingArrange> waitSavePeriodList = new ArrayList<>();
        List<DtoSamplingArrange> waitRemovePeriodList;
        //所有关联数据容器
        List<DtoSamplingFrequency> dtoSamplingFrequencyList;
        List<DtoSamplingArrange> dtoSamplingArrangeList;
        if (StringUtil.isNotEmpty(entityList)) {
            //部分更新情况
            List<String> folderIdList = entityList.stream().map(DtoSamplingFrequency::getSampleFolderId).collect(Collectors.toList());
            dtoSamplingArrangeList = samplingArrangeRepository.findAllBySampleFolderIdIn(folderIdList);
            //移除点位下原周期数据
            waitRemovePeriodList = dtoSamplingArrangeList.stream().filter(p -> folderIdList.contains(p.getSampleFolderId()))
                    .collect(Collectors.toList());

            //分组数据初始化
            dtoSamplingFrequencyList = repository.findBySampleFolderIdIn(folderIdList);
            if (isDelete) {
                dtoSamplingFrequencyList.removeAll(entityList);
            } else {
                dtoSamplingFrequencyList.addAll(entityList);
            }
            waitSavePeriodIdList = dtoSamplingFrequencyList.stream().map(p -> p.getSampleFolderId() + "_" + p.getPeriodCount()).distinct().collect(Collectors.toList());
        } else {
            //全局刷新情况
            //待移除部分
            dtoSamplingFrequencyList = repository.findAll();
            List<String> periodList = dtoSamplingFrequencyList.stream().map(p -> p.getSampleFolderId() + "_" + p.getPeriodCount()).distinct().collect(Collectors.toList());
            dtoSamplingArrangeList = samplingArrangeRepository.findAll();
            List<String> arrangeList = dtoSamplingArrangeList.stream().map(p -> p.getSampleFolderId() + "_" + p.getPeriodCount()).collect(Collectors.toList());
            waitRemovePeriodIdList = new ArrayList<>(arrangeList);
            waitRemovePeriodIdList.removeAll(periodList);
            waitRemovePeriodList = dtoSamplingArrangeList.stream().filter(p -> waitRemovePeriodIdList.contains(p.getSampleFolderId() + "_" + p.getPeriodCount()))
                    .collect(Collectors.toList());
            //待新增部分
            waitSavePeriodIdList = new ArrayList<>(periodList);
            waitSavePeriodIdList.removeAll(arrangeList);
        }
        //待插入周期填充额外数据
        //便于模糊查询及查询额外数据 冗沉 projectId sampleTypeId
        List<String> queryFolderIdList = waitSavePeriodIdList.stream().map(p -> p.substring(0, p.indexOf("_"))).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> dtoFolderList = StringUtil.isNotEmpty(queryFolderIdList) ? sampleFolderRepository.findAll(queryFolderIdList) : new ArrayList<>();
        for (String id : waitSavePeriodIdList) {
            DtoSamplingArrange samplingArrange = new DtoSamplingArrange();
            String[] idArr = id.split("_");
            samplingArrange.setSampleFolderId(idArr[0]);
            samplingArrange.setPeriodCount(Integer.parseInt(idArr[1]));
            samplingArrange.setIsArrange(false);
            //计算周期样品数
            int sampleCount = (int) dtoSamplingFrequencyList.stream().filter(p -> id.equals(p.getSampleFolderId() + "_" + p.getPeriodCount())).count();
            samplingArrange.setSampleCount(sampleCount);
            //冗沉标记位
            DtoSampleFolder dtoSampleFolder = dtoFolderList.stream().filter(f -> f.getId().equals(samplingArrange.getSampleFolderId())).findFirst().orElse(null);
            if (dtoSampleFolder != null) {
                samplingArrange.setProjectId(dtoSampleFolder.getProjectId());
                samplingArrange.setSampleTypeId(dtoSampleFolder.getSampleTypeId());
            }
            waitSavePeriodList.add(samplingArrange);
        }
        samplingArrangeRepository.save(waitSavePeriodList);
        samplingArrangeRepository.delete(waitRemovePeriodList);
    }


    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    @Lazy
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    @Lazy
    public void setFixedpointRepository(FixedpointRepository fixedpointRepository) {
        this.fixedpointRepository = fixedpointRepository;
    }

    @Autowired
    @Lazy
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }

    @Autowired
    @Lazy
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    @Lazy
    public void setSamplingFrequencyTestRepository(SamplingFrequencyTestRepository samplingFrequencyTestRepository) {
        this.samplingFrequencyTestRepository = samplingFrequencyTestRepository;
    }

    @Autowired
    public void setSamplingArrangeRepository(SamplingArrangeRepository samplingArrangeRepository) {
        this.samplingArrangeRepository = samplingArrangeRepository;
    }
}

