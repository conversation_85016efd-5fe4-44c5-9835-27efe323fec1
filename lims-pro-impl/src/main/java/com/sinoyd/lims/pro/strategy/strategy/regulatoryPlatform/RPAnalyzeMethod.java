package com.sinoyd.lims.pro.strategy.strategy.regulatoryPlatform;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.criteria.BaseRegulatoryPlatformCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.RPAnalyzeMethodVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 上海监管平台分析方法服务策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Service
public class RPAnalyzeMethod extends AbsRegulatoryPlatformRemote<RPAnalyzeMethodVO> {

    @Override
    protected void filterCriteria(List<RPAnalyzeMethodVO> list, BaseCriteria criteria) {
        BaseRegulatoryPlatformCriteria methodCriteria = (BaseRegulatoryPlatformCriteria) criteria;
        //分析方法模糊查询
        if (StringUtil.isNotEmpty(methodCriteria.getKey())) {
            list.removeIf(m -> !m.getMethodName().contains(methodCriteria.getKey()));
        }
    }

    @Override
    public String queryMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.分析方法.getQueryMethod();
    }

    @Override
    public String insertMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.分析方法.getInsertMethod();
    }

    @Override
    public String deleteMethodName() {
        return EnumPRO.EnumRegulatoryPlatformMethod.分析方法.getDeleteMethod();
    }
}
