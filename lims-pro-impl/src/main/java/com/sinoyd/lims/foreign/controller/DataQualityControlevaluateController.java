package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataOutSorceDataVO;
import com.sinoyd.lims.foreign.DataQualityControlevaluateVO;
import com.sinoyd.lims.foreign.service.DataOutSorceDataService;
import com.sinoyd.lims.foreign.service.DataQualityControlevaluateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 质控评价信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataQualityControlevaluate服务")
@RestController
@RequestMapping("api/pro/foreign/dataQualityControlevaluate")
public class DataQualityControlevaluateController extends ExceptionHandlerController<DataQualityControlevaluateService> {


    /**
     * 数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataQualityControlevaluateVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataQualityControlevaluateVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataQualityControlevaluateVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }

}
