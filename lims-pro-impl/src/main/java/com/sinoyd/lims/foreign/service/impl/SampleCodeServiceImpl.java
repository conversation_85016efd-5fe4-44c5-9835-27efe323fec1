package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.service.ISampleCodeService;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Person;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestPost2PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPostRepository;
import com.sinoyd.lims.lim.repository.rcc.SerialNumberConfigRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.BusinessSerialNumberRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.repository.ReceiveSampleRecordRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.service.SerialNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 样品编号服务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/27
 **/
@Service
@Slf4j
public class SampleCodeServiceImpl implements ISampleCodeService {

    private final SampleTypeService sampleTypeService;
    private final SampleFolderService sampleFolderService;
    private final TestPost2PersonRepository testPost2PersonRepository;
    private final TestPostRepository testPostRepository;
    private final SampleRepository sampleRepository;
    private final ReceiveSampleRecordRepository receiveSampleRecordRepository;
    private final ProjectRepository projectRepository;
    private final ProjectTypeService projectTypeService;
    private final SerialNumberService serialNumberService;
    private final SerialNumberConfigRepository serialNumberConfigRepository;
    private final BusinessSerialNumberRepository businessSerialNumberRepository;

    public SampleCodeServiceImpl(SampleTypeService sampleTypeService,
                                 SampleFolderService sampleFolderService,
                                 TestPost2PersonRepository testPost2PersonRepository,
                                 TestPostRepository testPostRepository,
                                 SampleRepository sampleRepository,
                                 ReceiveSampleRecordRepository receiveSampleRecordRepository,
                                 ProjectRepository projectRepository,
                                 ProjectTypeService projectTypeService,
                                 @Qualifier("sample") SerialNumberService serialNumberService,
                                 SerialNumberConfigRepository serialNumberConfigRepository,
                                 BusinessSerialNumberRepository businessSerialNumberRepository) {
        this.sampleTypeService = sampleTypeService;
        this.sampleFolderService = sampleFolderService;
        this.testPost2PersonRepository = testPost2PersonRepository;
        this.testPostRepository = testPostRepository;
        this.sampleRepository = sampleRepository;
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
        this.projectRepository = projectRepository;
        this.projectTypeService = projectTypeService;
        this.serialNumberService = serialNumberService;
        this.serialNumberConfigRepository = serialNumberConfigRepository;
        this.businessSerialNumberRepository = businessSerialNumberRepository;
    }

    @Override
    @Transactional
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams) {

        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);

        DtoSampleFolder dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);

        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());

        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, testPost, record, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, associateSampleCode, extendParams);
    }

    @Override
    @Transactional
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          String samplingPersonId,
                                          String sampleId,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          Boolean isAutoCommitSN,
                                          String associateSampleCode,
                                          Map<String, Object> extendParams) {

        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);

        DtoSampleFolder dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);

        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());

        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, null, null, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, -1, -1, isAutoCommitSN, associateSampleCode, extendParams);
    }

    @Override
    @Transactional
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          String samplingPersonId,
                                          String sampleId,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          Integer qcType, Integer qcGrade,
                                          Boolean isAutoCommitSN,
                                          String associateSampleCode,
                                          Map<String, Object> extendParams) {
        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);
        DtoSampleFolder dtoSampleFolder = null;
        if (StringUtils.isNotNullAndEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
            dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);
        }
        //根据采样人，获取岗位
        DtoTestPost testPost = null;
        if (StringUtils.isNotNullAndEmpty(samplingPersonId) && !UUIDHelper.GUID_EMPTY.equals(samplingPersonId)) {
            List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByPersonId(samplingPersonId);
            List<String> testPostIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getTestPostId).distinct().collect(Collectors.toList());
            List<DtoTestPost> testPostList = StringUtil.isNotEmpty(testPostIdList) ? testPostRepository.findAll(testPostIdList) : new ArrayList<>();
            testPostList = testPostList.stream().filter(p -> EnumLIM.EnumPostType.现场.getValue().equals(p.getPostType())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(testPostList)) {
                testPost = testPostList.get(0);
            }
        }

        //根据原样获取送样单
        DtoReceiveSampleRecord record = null;
        if (StringUtils.isNotNullAndEmpty(sampleId) && !UUIDHelper.GUID_EMPTY.equals(sampleId)) {
            DtoSample sample = sampleRepository.findOne(sampleId);
            if (StringUtil.isNotNull(sample) && StringUtils.isNotNullAndEmpty(sample.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getReceiveId())) {
                record = receiveSampleRecordRepository.findOne(sample.getReceiveId());
            }
        }

        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());

        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, testPost, record, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, qcType, qcGrade, isAutoCommitSN, associateSampleCode, extendParams);
    }

    @Override
    @Transactional
    public String createSampleCode(String projectId,
                                   String projectTypeId,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Map<String, Object> extendParams) {

        DtoProject dtoProject = projectRepository.findOne(projectId);

        DtoProjectType dtoProjectType = projectTypeService.findOne(projectTypeId);

        return createSampleCode(dtoProject, dtoProjectType, testPost, record, sampleTypeId,
                sampleFolderId, samplingTimeBegin,
                isCreate, currentUserId,
                isQC, associateSampleId, qcId, sampleCategory, associateSampleCode, extendParams);
    }

    @Override
    @Transactional
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                testPost,
                record,
                samplingTimeBegin,
                isCreate,
                currentUserId,
                isQC,
                associateSampleId,
                qcId,
                sampleCategory, -1, -1, true, associateSampleCode, extendParams).getCode();
    }

    @Override
    @Transactional
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          DtoSampleType dtoSampleType,
                                          DtoSampleType dtoSampleTypeParent,
                                          DtoSampleFolder dtoSampleFolder,
                                          DtoTestPost testPost,
                                          DtoReceiveSampleRecord record,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          Integer qcType, Integer qcGrade,
                                          Boolean isAutoCommitSN,
                                          String associateSampleCode,
                                          Map<String, Object> extendParams) {
        Integer manualSn = extendParams == null ? -1 : (Integer) extendParams.getOrDefault("manualSn", -1);
        return serialNumberService.createNewNumber(isAutoCommitSN, dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                samplingTimeBegin,
                isCreate,
                currentUserId,
                isQC,
                associateSampleId,
                qcId,
                sampleCategory, qcType, qcGrade, associateSampleCode, testPost, record, manualSn);
    }

    @Override
    @Transactional
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Map<String, Object> extendParams) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                testPost,
                record,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.原样.getValue(), "", extendParams);
    }

    @Override
    @Transactional
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          String samplingPersonId,
                                          String sampleId,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isAutoCommitSN,
                                          Map<String, Object> extendParams) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                samplingPersonId,
                sampleId,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.原样.getValue(), -1, -1, isAutoCommitSN, "", extendParams);
    }

    @Override
    @Transactional
    public String createSampleCode(String projectId,
                                   String projectTypeId,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Map<String, Object> extendParams) {
        if (extendParams != null) {
            Integer newSn = (Integer) extendParams.get("sn");
            if (newSn != null && newSn > 0) {
                String code = createSampleCode(projectId,
                        projectTypeId,
                        sampleTypeId,
                        sampleFolderId,
                        samplingTimeBegin,
                        false,
                        currentUserId, false,
                        UUIDHelper.GUID_EMPTY,
                        UUIDHelper.GUID_EMPTY,
                        EnumPRO.EnumSampleCategory.原样.getValue(), "", testPost, record, extendParams);
                //校验编号是否存在
                Integer codeCount = sampleRepository.countByCode(code);
                if (codeCount != null && codeCount > 0) {
                    throw new BaseException("样品编号已存在，请重新输入");
                }
            }
        }
        return createSampleCode(projectId,
                projectTypeId,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.原样.getValue(), "", testPost, record, extendParams);
    }

    @Override
    @Transactional
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Map<String, Object> extendParams) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                null,
                null,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.原样.getValue(), "", extendParams);
    }


    @Override
    @Transactional
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          DtoSampleType dtoSampleType,
                                          DtoSampleType dtoSampleTypeParent,
                                          DtoSampleFolder dtoSampleFolder,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isAutoCommitSN,
                                          Map<String, Object> extendParams) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                null,
                null,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.原样.getValue(), -1, -1, isAutoCommitSN, "", extendParams);
    }

    @Override
    @Transactional
    public void manualUpdateSerialNumber(DtoSample sample) {
        //根据样品id在业务流水号里查询记录, 更新对应流水号
        List<DtoBusinessSerialNumber> serialNumberList = businessSerialNumberRepository.findByBusinessTypeAndBusinessIdIn(EnumPRO.EnumLogObjectType.样品.name(), Collections.singletonList(sample.getId()));
        if (StringUtil.isNotEmpty(serialNumberList)) {
            DtoBusinessSerialNumber serialNumber = serialNumberList.get(0);
            serialNumber.setPara1(sample.getSn().toString());
            businessSerialNumberRepository.save(serialNumber);

            //需要更新流水号表对应流水号
            DtoSerialNumberConfig serialNumberConfig = serialNumberConfigRepository.findBySerialNumberType(serialNumber.getSerialNumberType());
            if (StringUtil.isNotNull(serialNumberConfig)) {
                serialNumberConfig.setPara1(sample.getSn().toString());
                serialNumberConfigRepository.save(serialNumberConfig);
            }
            //
        }
    }
}
