package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.service.ISampleCodeService;
import com.sinoyd.lims.foreign.service.ISySampleCodeService;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.BusinessSerialNumberService;
import com.sinoyd.lims.pro.service.SampleFolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 送样类样品编号服务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/28
 **/
@Service
@Slf4j
public class SySampleCodeServiceImpl implements ISySampleCodeService {

    private final ISampleCodeService sampleCodeService;
    private final BusinessSerialNumberService businessSerialNumberService;
    private final ProjectRepository projectRepository;
    private final ProjectTypeService projectTypeService;
    private final SampleTypeService sampleTypeService;
    private final SampleFolderService sampleFolderService;
    private final SampleRepository sampleRepository;

    public SySampleCodeServiceImpl(ISampleCodeService sampleCodeService,
                                   BusinessSerialNumberService businessSerialNumberService,
                                   ProjectRepository projectRepository,
                                   ProjectTypeService projectTypeService,
                                   SampleTypeService sampleTypeService,
                                   SampleFolderService sampleFolderService,
                                   SampleRepository sampleRepository) {
        this.sampleCodeService = sampleCodeService;
        this.businessSerialNumberService = businessSerialNumberService;
        this.projectRepository = projectRepository;
        this.projectTypeService = projectTypeService;
        this.sampleTypeService = sampleTypeService;
        this.sampleFolderService = sampleFolderService;
        this.sampleRepository = sampleRepository;
    }

    @Override
    public String createSampleCode(String projectId,
                                   String projectTypeId,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   DtoSample sample,
                                   Map<String, Object> extendParams) {
        DtoGenerateSN generateSN = createSampleCode(projectId,
                projectTypeId,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.原样.getValue(),
                "",
                testPost,
                record, extendParams);

        DtoBusinessSerialNumber dtoBusinessSerialNumber = new DtoBusinessSerialNumber();
        dtoBusinessSerialNumber.setBusinessType(EnumPRO.EnumLogObjectType.样品.name());
        dtoBusinessSerialNumber.setBusinessId(sample.getId());
        dtoBusinessSerialNumber.setBusinessNumber(sample.getCode());
        dtoBusinessSerialNumber.setSerialNumberType(generateSN.getCurrentSerialNumberType());
        dtoBusinessSerialNumber.setPara0(generateSN.getCurrentPara0());
        dtoBusinessSerialNumber.setPara1(generateSN.getCurrentPara1());
        dtoBusinessSerialNumber.setPara2(generateSN.getCurrentPara2());
        dtoBusinessSerialNumber.setPara3(generateSN.getCurrentPara3());
        businessSerialNumberService.save(dtoBusinessSerialNumber);
        return generateSN.getCode();
    }

    @Override
    public DtoGenerateSN createSampleCode(String projectId,
                                          String projectTypeId,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          String associateSampleCode,
                                          DtoTestPost testPost,
                                          DtoReceiveSampleRecord record,
                                          Map<String, Object> extendParams) {
        DtoProject dtoProject = projectRepository.findOne(projectId);

        DtoProjectType dtoProjectType = projectTypeService.findOne(projectTypeId);

        return createSampleCode(dtoProject, dtoProjectType, testPost, record, sampleTypeId,
                sampleFolderId, samplingTimeBegin,
                isCreate, currentUserId,
                isQC, associateSampleId, qcId, sampleCategory, associateSampleCode, extendParams);
    }

    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          DtoTestPost testPost,
                                          DtoReceiveSampleRecord record,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          String associateSampleCode,
                                          Map<String, Object> extendParams) {
        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);
        DtoSampleFolder dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);
        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());
        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, testPost, record, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, associateSampleCode, extendParams);
    }

    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          DtoSampleType dtoSampleType,
                                          DtoSampleType dtoSampleTypeParent,
                                          DtoSampleFolder dtoSampleFolder,
                                          DtoTestPost testPost,
                                          DtoReceiveSampleRecord record,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          String associateSampleCode,
                                          Map<String, Object> extendParams) {
        return sampleCodeService.createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                testPost,
                record,
                samplingTimeBegin,
                isCreate,
                currentUserId,
                isQC,
                associateSampleId,
                qcId,
                sampleCategory, -1, -1, true, associateSampleCode, extendParams);
    }


}
