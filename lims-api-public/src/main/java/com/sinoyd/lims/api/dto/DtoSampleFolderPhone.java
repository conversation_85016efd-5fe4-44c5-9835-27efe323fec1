package com.sinoyd.lims.api.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;


@Data
public class DtoSampleFolderPhone {

    /**
     * 点位id
     */
    private String id;

    /**
     * 点位名称
     */
    @Length(message = "点位名称{validation.message.length}", max = 100)
    private String sampleFolderName;

    /**
     * 周期
     */
    private String cycleOrder;

    /**
     * 周期排序使用
     */
    private Integer cycValue;

    /**
     * 分析项目名称
     */
    private String analyzeItems;

    /**
     * 送样单信息
     */
    private List<String> recordCode;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 样品类型名称
     */
    private String sampleTypeName;

    /**
     * 样品个数（总数）
     */
    private Integer sampleCount;

    /**
     * 样品个数（总数）
     */
    private Integer collectCount;

    /**
     * 分析项目个数
     */
    private Integer analyzeCount;

    /**
     * 采样人ids（去掉）
     */
    private String samplingPersonIds;

    /**
     * 采样人名称（去掉）
     */
    private String samplingPersonNames;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 计划经度
     */
    private String planLon;

    /**
     * 计划纬度
     */
    private String planLat;

    /**
     * 签到时间
     */
    private String signTime;

    /**
     * 采样时间（去掉）
     */
    private String samplingTime;

    /**
     * 是否样品都已加入送样单
     */
    private Integer isOver;

    /**
     * 状态（是否完成）
     */
    private String status;

    /**
     * 签到人员名称
     */
    private String signPerson;

    /**
     * 语音说明
     */
    private String voiceTip;

    /**
     * 点位签到信息id
     */
    private String folderSignId;

    /**
     * 比对类型
     */
    private Integer checkType;

    /**
     * 检测大类id
     */
    private String bigSampleTypeId;

    /**
     * 检测小类排序值
     */
    private Integer sampleTypeOrderNum;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 是否录入
     */
    private Boolean isInput;
}
