package com.sinoyd.lims.api.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class DtoReceiveSampleRecordPhone {

    /**
     * 送样单号
     */
    private String receiveSampleRecordCode;

    /**
     * 送样单id
     */
    private String id;

    /**
     * 采样人员名称
     */
    private String samplingPersonNames;

    /**
     * 采样人员ids
     */
    private List<String> samplingPersonIds;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 负责人id
     */
    private String leaderId;

    /**
     * 采样日期
     */
    private String samplingTime;

    /**
     * 采样时间
     */
    private Date samplingDate;

    /**
     * 点位名称
     */
    private String folderName;

    /**
     * 送样单状态
     */
    private String status;

    /**
     * 点位个数
     */
    private Integer folderCount;

    /**
     * 样品类型名称
     */
    private String typeNames;

    /**
     * 现场领样单id
     */
    private String subXCId;

    /**
     * 分析领样单id
     */
    private String subFXId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 报告人id
     */
    private String reportorId;

    /**
     * 报告人名称
     */
    private String reportorName;

    /**
     * 要求完成日期
     */
    private Date requireTime;

    /**
     * 项目等级
     */
    private Integer grade;

    /**
     * 受检单位id
     */
    private String inspectedEntId;

    /**
     * 受检单位名称
     */
    private String inspectedEnt;

    /**
     * 委托单位id
     */
    private String customerId;

    /**
     * 委托单位名称
     */
    @Length(message = "{validation.message.length}", max = 1000)
    private String customerName;

    /**
     * 受检方联系人
     */
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    private String inspectedAddress;

    /**
     * 送样人id
     */
    private String senderId;

    /**
     * 送样人名称
     */
    private String senderName;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 监测目的
     */
    private String monitorPurp;

    /**
     * 监测方式
     */
    private String monitorMethods;

    /**
     * 监测要求及说明
     */
    @Length(message = "客户委托内容{validation.message.length}", max = 1000)
    private String customerRequired;

    /**
     * 其他说明
     */
    private String remark;

    /**
     * 送样单备注
     */
    private String recordRemark;

    /**
     * 状态排序
     */
    private Integer statusNumber;

    /**
     * 送样单ids
     */
    private List<String> receiveIds = new ArrayList<>();

    /**
     * 送样单备注
     */
    private String receiveRemark;
}
