package com.sinoyd.lims.instrument.parse.dto;

import com.sinoyd.lims.instrument.parse.entity.ParseLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.File;
import java.util.Date;

/**
 * 仪器解析日志传输实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/8/7
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_Log")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoParseLog extends ParseLog {

    /**
     * 解析文件相对路径
     */
    @Transient
    private String relativePath;


    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Transient
    private String instrumentCode;

    /**
     * 操作人名称
     */
    @Transient
    private String creatorName;

    public DtoParseLog() {

    }

    public DtoParseLog(String id, String fileName, String instrumentName, String instrumentCode, Date parseDate) {
        setId(id);
        setFileOrgName(fileName);
        this.instrumentName = instrumentName;
        this.instrumentCode = instrumentCode;
        setBeginTime(parseDate);
    }

    public DtoParseLog(String id, String fileName, String instrumentName, String instrumentCode, Date parseDate, String parseStatus) {
        this(id, fileName, instrumentName, instrumentCode, parseDate);
        setParseStatus(parseStatus);
    }
    public DtoParseLog(String id, String fileName, String instrumentName, String instrumentCode, Date parseDate, String parseStatus, String creator) {
        this(id, fileName, instrumentName, instrumentCode, parseDate);
        setParseStatus(parseStatus);
        setCreator(creator);
    }

    public String getRelativePath() {
        return getDestFolderName() + File.separator + getFileName();
    }
}