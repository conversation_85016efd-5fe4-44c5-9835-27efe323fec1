package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;


/**
 * 监管平台设备信息数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPDevicesVO {

    /**
     * 设备id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    private String id;

    /**
     * 设备编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SBBH")
    private String instrumentCode;

    /**
     * 设备名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SBMC")
    private String instrumentName;

    /**
     * 出厂编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "CCBH")
    private String serialNo;

    /**
     * 设备产权
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SBCQ")
    private String propertyRights;

    /**
     * 型号/规格
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XG_GE")
    private String model;

    /**
     * 设备描述
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SBMS")
    private String description;

}
