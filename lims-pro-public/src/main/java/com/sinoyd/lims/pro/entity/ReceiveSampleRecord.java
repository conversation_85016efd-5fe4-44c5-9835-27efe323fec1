package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ReceiveSampleRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @MappedSuperclass
 @ApiModel(description="ReceiveSampleRecord")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ReceiveSampleRecord extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public ReceiveSampleRecord() {
        this.recorderId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getUserId() : "";
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目ID
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("项目ID")
    private String projectId;

    /**
     * 送样单号
     */
    @Column(length = 20)
    @ApiModelProperty("送样单号")
    @Length(message = "送样单号{validation.message.length}", max = 20)
    private String recordCode;

    /**
     * 采样时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("采样时间")
    private Date samplingTime;

    /**
     * 送样时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("送样时间")
    private Date sendTime;

    /**
     * 排序值Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("排序值Id")
    private String sortId;

    /**
     * 送样人（内部人员）ID
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样人（内部人员）ID")
    private String senderId;

    /**
     * 送样人（采样负责人）
     */
    @Column(length = 100)
    @ApiModelProperty("送样人（采样负责人）")
    @Length(message = "送样人（采样负责人）{validation.message.length}", max = 100)
    private String senderName;

    /**
     * 登记时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("登记时间")
    private Date receiveTime;

    /**
     *  接样人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("接样人Id")
    private String recipientId;

    /**
     * 登记人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("登记人id")
    private String recorderId;

    /**
     * 接样类型（枚举EnumReceiveType：1.内部 2.外部 3.现场）
     */
    @Column(nullable = false)
    @ApiModelProperty("接样类型（枚举EnumReceiveType：1.内部 2.外部 3.现场）")
    private Integer receiveType;

    /**
     * 送样单状态（字符串，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）
     */
    @Column(length = 50)
    @ApiModelProperty("送样单状态（字符串，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）")
    @Length(message = "送样单状态{validation.message.length}", max = 50)
    private String status;

    /**
     * 送样单状态（ 用于判断，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("送样单状态（ 用于判断，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）")
    private Integer receiveStatus;

    /**
     * 信息状态（枚举EnumReceiveInfoStatus：1.信息登记中，2.信息复核中 3.信息审核中 4.已确认）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("信息状态（枚举EnumReceiveInfoStatus：1.信息登记中，2.信息复核中 3.信息审核中 4.已确认）")
    private Integer infoStatus;

    /**
     * 分析数据报告编号
     */
    @Column(length = 20)
    @ApiModelProperty("分析数据报告编号")
    @Length(message = "分析数据报告编号{validation.message.length}", max = 20)
    private String reportCode;

    /**
     * 移动端状态(0：未提交，1：数据录入中，2：已数据同步)
     */
    @Column(nullable = false)
    @ColumnDefault("'0'")
    @ApiModelProperty("移动端状态(0：未提交，1：数据录入中，2：已数据同步)")
    private Integer uploadStatus;

    /**
     * 上传时间（移动端）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("上传时间（移动端）")
    private Date uploadTime;

    /**
     * 有证采样人员（用于图片签名）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("有证采样人员（用于图片签名）")
    private String validAnalyzeId;

    /**
     * 复核人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("复核人Id")
    private String checkerId;

    /**
     * 复核时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("复核时间")
    private Date checkTime;

    /**
     * 有证复核人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("有证复核人员id")
    private String validCheckerId;

    /**
     * 审核人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("审核人Id")
    private String auditorId;

    /**
     * 审核时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
     * 有证采样审核人员
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("有证采样审核人员")
    private String validAuditerId;

    /**
     * 退回意见（最新一个）
     */
    @Column(length = 1000)
    @ApiModelProperty("退回意见（最新一个）")
    @Length(message = "退回意见（最新一个）{validation.message.length}", max = 1000)
    private String backOpinion;

   /**
    * 交接时间
    */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("交接时间")
    private Date receiveSampleDate;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * * 冗余json信息
     */
    @Column(length = 4000)
    @ApiModelProperty("冗余json信息")
    private String json;

    /**
     * 是否分批接样
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否分批接样")
    private Boolean isBatchReceive = false;

    /**
     * 内部送样人ID
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("内部送样人ID")
    private String innerReceiveId;

    /**
     * 内部接收人名称
     */
    @Column(length = 100)
    @ApiModelProperty("内部接收人")
    @Length(message = "内部接收人{validation.message.length}", max = 100)
    private String innerReceiveName;



    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
}