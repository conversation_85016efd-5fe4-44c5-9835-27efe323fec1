package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;

/**
 * 监管平台采样计划点位数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPSamplingPlanFolderVO {

    /**
     * 采样计划 ID
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    private String id;

    /**
     * 采样任务 ID
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "MTSTID")
    private String samplingTaskId;

    /**
     * 计划采样日期
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "JHSJ")
    private String samplingCompleteTime;

    /**
     * 周期
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ZQ")
    private String periodCount;

    /**
     * 频次
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "PC")
    private String timePerPeriod;

    /**
     * 采样项目
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "PT")
    private String watchSpot;

    /**
     * 测试项目名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ITEMS")
    private String testNames;
}
