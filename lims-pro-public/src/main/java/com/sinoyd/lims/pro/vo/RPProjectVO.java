package com.sinoyd.lims.pro.vo;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.lims.pro.annotate.RegulatoryPlatformField;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;

/**
 * 监管平台委托任务信息数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPProjectVO {

    /**
     * 任务id (0 新增/ID 编辑)
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    @RegulatoryPlatformField(fieldName = "任务id", nullable = false)
    private String id;

    /**
     * 合同ID
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "CID")
    @RegulatoryPlatformField(fieldName = "合同ID", nullable = false)
    private String contractId;

    /**
     * 任务编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWBH")
    @RegulatoryPlatformField(fieldName = "任务编号", nullable = false)
    private String projectCode;

    /**
     * 任务名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWMC")
    @RegulatoryPlatformField(fieldName = "任务名称", nullable = false)
    private String projectName;

    /**
     * 任务类别
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWLB")
    @RegulatoryPlatformField(fieldName = "任务类别", nullable = false)
    private String projectType;

    /**
     * 是否采样
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "CYLX")
    @RegulatoryPlatformField(fieldName = "是否采样", nullable = false)
    private String isSampling;

    /**
     * 任务补传编号（当是否采样为"补传"时，此字段必填）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "REPORTAPPLYID")
    @RegulatoryPlatformField(fieldName = "任务补传编号")
    private String reportApplyId;

    /**
     * 任务来源
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWLY")
    @RegulatoryPlatformField(fieldName = "任务来源", nullable = false)
    private String taskSource;

    /**
     * 省
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "PROVINCE")
    @RegulatoryPlatformField(fieldName = "省区域", nullable = false)
    private String province;

    /**
     * 市
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "CITY")
    @RegulatoryPlatformField(fieldName = "市区域", nullable = false)
    private String city;

    /**
     * 县
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "DISTRICT")
    @RegulatoryPlatformField(fieldName = "县区域", nullable = false)
    private String district;


    /**
     * 任务地址
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWDZ")
    @RegulatoryPlatformField(fieldName = "任务地址", nullable = false)
    private String taskAddress;

    /**
     * 被测对象
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "BCDX")
    @RegulatoryPlatformField(fieldName = "被测对象", nullable = false)
    private String testTarget;


    /**
     * 被测对象污染源编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "BCDX_WRYBH")
    @RegulatoryPlatformField(fieldName = "被测对象污染源编号")
    private String targetPollutionCode;


    /**
     * 现场采样联系人
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XCLXR")
    @RegulatoryPlatformField(fieldName = "现场采样联系人", nullable = false)
    private String siteSamplingPerson;

    /**
     * 现场联系电话
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XCLXDH")
    @RegulatoryPlatformField(fieldName = "现场联系电话", nullable = false)
    private String siteSamplingPhone;

    /**
     * 任务金额
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "JE")
    @RegulatoryPlatformField(fieldName = "任务金额", nullable = false)
    private String taskAmount;

    /**
     * 附件原名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FILENAME")
    @RegulatoryPlatformField(fieldName = "附件原名称")
    private String fileName;

    /**
     * 监管平台附件返回名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FILENAMEOLD")
    @RegulatoryPlatformField(fieldName = "监管平台附件返回名称")
    private String rpFileName;

    /**
     * 附件说明
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FJMS")
    @RegulatoryPlatformField(fieldName = "附件说明")
    private String fileDesc;

    /**
     * 操作类别 1保存，3提交
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "IS_CHECK")
    @RegulatoryPlatformField(fieldName = "操作类别", nullable = false)
    private String isCheck;

    /**
     * 任务概述
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWGS")
    @RegulatoryPlatformField(fieldName = "任务概述")
    private String taskDesc;

    /**
     * 接样时间
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "JYSJ")
    @RegulatoryPlatformField(fieldName = "接样时间")
    private String receiveTime;

    /**
     * 构造函数（根据项目、项目合同信息、区域信息构造）
     *
     * @param project         项目信息
     * @param projectContract 项目合同信息
     * @param area            区域信息
     */
    public RPProjectVO(DtoProject project, DtoProjectContract projectContract, DtoArea area) {
        String pollutionCode = "";
        if (StringUtil.isNotNull(project) && StringUtils.isNotNullAndEmpty(project.getPollutionCode())) {
            pollutionCode = project.getPollutionCode();
        }
        this.id = "0";
        this.contractId = projectContract.getContractId();
        this.projectCode = project.getProjectCode();
        this.projectName = project.getProjectName();
        this.projectType = projectContract.getTaskType();
        this.isSampling = projectContract.getIsSample();
        this.taskSource = projectContract.getTaskSource();
        this.province = "上海市";
        this.city = area.getAreaName();
        this.district = area.getAreaName();
        this.taskAddress = projectContract.getTaskAddress();
        this.testTarget = projectContract.getTestTarget();
        this.targetPollutionCode = pollutionCode;
        this.siteSamplingPerson = projectContract.getSampleContact();
        this.siteSamplingPhone = projectContract.getContactPhone();
        this.taskAmount = projectContract.getTaskPrice().toString();
        this.isCheck = "1";
        this.taskDesc = projectContract.getTaskContent();
        this.fileDesc = projectContract.getFileExplain();
    }


}
