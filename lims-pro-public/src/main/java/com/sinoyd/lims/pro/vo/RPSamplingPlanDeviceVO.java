package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;

/**
 * 监管平台采样仪器更新实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPSamplingPlanDeviceVO {


    /**
     * 采样仪器绑定数据集合
     */
    @XmlElementWrapper(name = LABEL_ITEM_PREFIX + "DeivceDataList")
    @XmlElement(name = LABEL_ITEM_PREFIX + "DeivceData")
    private List<SamplingDeviceData> samplingDeviceData = new ArrayList<>();


    /**
     * 采样仪器绑定数据
     */
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = LABEL_ITEM_PREFIX + "DeivceData")
    public static class SamplingDeviceData {

        /**
         * 监管平台任务id
         */
        @XmlElement(name = LABEL_ITEM_PREFIX + "MTID")
        private String rpProjectId;

        /**
         * 监管平台仪器id
         */
        @XmlElement(name = LABEL_ITEM_PREFIX + "ODID")
        private String rpDeviceId;

        /**
         * 构造函数
         *
         * @param rpProjectId 监管平台任务id
         * @param rpDeviceId  监管平台仪器id
         */
        public SamplingDeviceData(String rpProjectId, String rpDeviceId) {
            this.rpProjectId = rpProjectId;
            this.rpDeviceId = rpDeviceId;
        }
    }
}
