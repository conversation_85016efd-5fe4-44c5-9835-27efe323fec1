package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoLocalTaskPeopleCompare;
import lombok.Data;
import org.bouncycastle.util.Strings;

import java.util.*;

/**
 * 质控任务数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/2/4
 * @since V100R001
 */
@Data
public class DtoQCProject {
    public List<String> getReportMethodArr() {
        if (this.reportMethodArr.size() > 0) {
            return this.reportMethodArr;
        } else if (StringUtils.isNotNullAndEmpty(this.reportMethod)) {
            return Arrays.asList(Strings.split(this.reportMethod, ','));
        }
        return new ArrayList<>();
    }

    public void setReportMethodArr(List<String> reportMethodArr) {
        this.reportMethodArr = reportMethodArr;
        if (StringUtil.isNotNull(reportMethodArr)) {
            this.setReportMethod(String.join(",", reportMethodArr));
        } else {
            this.setReportMethod("");
        }
    }

    /**
     * 项目id
     */
    private String id;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 评价信息id
     */
    private String evaluationId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目状态
     */
    private String status;

    /**
     * 输入时间
     */
    private Date inputTime;

    /**
     * 登记日期
     */
    private Date inceptTime;

    /**
     * 样品描述
     */
    private String sampleDescription;

    /**
     * 样品数量
     */
    private String sampleQuantity;

    /**
     * 委托单位id
     */
    private String customerId;

    /**
     * 委托单位名称
     */
    private String customerName;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkPhone;

    /**
     * 地址
     */
    private String customerAddress;

    /**
     * 质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）
     */
    private Integer qcGrade;

    /**
     * 质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核)
     */
    private Integer qcType;

    /**
     * 质量来源
     */
    private String qcSource;

    /**
     * 判断依据
     */
    private String judgment;

    /**
     * 责任人id
     */
    private String responsePerson;

    /**
     * 报告出具方式
     */
    private String reportMethod;

    /**
     * 出具报告日期
     */
    private Date reportDate;

    /**
     * 要求完成时间
     */
    private Date deadLine;

    /**
     * 意见
     */
    private String lastNewOpinion;

    /**
     * 人员id
     */
    private String inceptPersonId;

    /**
     * 人员名称
     */
    private String inceptPersonName;

    /**
     * 存放项目拓展字段
     */
    private Map<String, Object> dbExtendMap;

    /**
     * 考核负责人
     */
    private String leaderId;

    /**
     * 考核人员
     */
    private List<String> assessPersonIds;

    /**
     * 考核日期
     */
    private Date requireAnalyzeDate;

    /**
     * 报告出具方式数组
     */
    private List<String> reportMethodArr = new ArrayList<>();

    /**
     * 是否协同任务
     */
    private Boolean isAssist;

    /**
     * 考核内容
     */
    private String testContent;

    /**
     * 考核要求
     */
    private String testRequirement;

    /**
     * 备注
     */
    private String remark;

    /**
     * 送样单id集合
     */
    private List<String> receiveIds;

    /**
     * 现场质控-人员比对信息
     */
    private List<DtoLocalTaskPeopleCompare> localTaskPeopleCompareList;

    public DtoQCProject() {

    }

    /**
     * 该构造函数主要用到 QCProjectServiceImpl 下面 findQCProjectByPage
     *
     * @param id                项目id
     * @param receiveId         送样单id
     * @param projectCode       项目编号
     * @param projectTypeId     项目类型
     * @param projectName       项目名称
     * @param status            项目状态
     * @param inputTime         录入时间
     * @param inceptTime        登记时间
     * @param sampleDescription 样品描述
     * @param sampleQuantity    样品数量
     * @param customerId        委托单位id
     * @param customerName      委托单位名称
     * @param linkMan           联系人
     * @param linkPhone         联系人电话
     * @param customerAddress   地址
     * @param qcGrade           控制类型
     * @param qcType            控制类型分类
     * @param qcSource          质量来源
     * @param judgment          判断依据
     * @param responsePerson    责任人
     * @param reportMethod      报告出具方式
     * @param reportDate        出具报告日期
     * @param deadLine          要求完成时间
     */
    public DtoQCProject(String id, String receiveId, String projectCode, String projectTypeId, String projectName, String status, Date inputTime,
                        Date inceptTime, String sampleDescription, String sampleQuantity, String customerId, String customerName,
                        String linkMan, String linkPhone, String customerAddress, Integer qcGrade, Integer qcType, String qcSource,
                        String judgment, String responsePerson, String reportMethod, Date reportDate, Date deadLine) {
        this.id = id;
        this.receiveId = receiveId;
        this.projectCode = projectCode;
        this.projectTypeId = projectTypeId;
        this.projectName = projectName;
        this.status = status;
        this.inputTime = inputTime;
        this.inceptTime = inceptTime;
        this.sampleDescription = sampleDescription;
        this.sampleQuantity = sampleQuantity;
        this.customerId = customerId;
        this.customerName = customerName;
        this.linkMan = linkMan;
        this.linkPhone = linkPhone;
        this.customerAddress = customerAddress;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.qcSource = qcSource;
        this.judgment = judgment;
        this.responsePerson = responsePerson;
        this.reportMethod = reportMethod;
        this.reportDate = reportDate;
        this.deadLine = deadLine;
    }

    public DtoQCProject(String id, String receiveId, String projectCode, String projectTypeId, String projectName, String status, Date inputTime,
                        Date inceptTime, String sampleDescription, String sampleQuantity, String customerId, String customerName,
                        String linkMan, String linkPhone, String customerAddress, Integer qcGrade, Integer qcType, String qcSource,
                        String judgment, String responsePerson, String reportMethod, Date reportDate, Date deadLine, String inceptPersonId) {
        this.id = id;
        this.receiveId = receiveId;
        this.projectCode = projectCode;
        this.projectTypeId = projectTypeId;
        this.projectName = projectName;
        this.status = status;
        this.inputTime = inputTime;
        this.inceptTime = inceptTime;
        this.sampleDescription = sampleDescription;
        this.sampleQuantity = sampleQuantity;
        this.customerId = customerId;
        this.customerName = customerName;
        this.linkMan = linkMan;
        this.linkPhone = linkPhone;
        this.customerAddress = customerAddress;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.qcSource = qcSource;
        this.judgment = judgment;
        this.responsePerson = responsePerson;
        this.reportMethod = reportMethod;
        this.reportDate = reportDate;
        this.deadLine = deadLine;
        this.inceptPersonId = inceptPersonId;
    }

    public DtoQCProject(String id, String receiveId, String projectCode, String projectTypeId, String projectName,
                        String status, Date inputTime, Date inceptTime, String sampleDescription, String sampleQuantity,
                        String customerId, String customerName, String linkMan, String linkPhone, String customerAddress,
                        Integer qcGrade, Integer qcType, String qcSource, String judgment, String responsePerson,
                        String reportMethod, Date reportDate, Date deadLine, String inceptPersonId, Boolean isAssist) {
        this.id = id;
        this.receiveId = receiveId;
        this.projectCode = projectCode;
        this.projectTypeId = projectTypeId;
        this.projectName = projectName;
        this.status = status;
        this.inputTime = inputTime;
        this.inceptTime = inceptTime;
        this.sampleDescription = sampleDescription;
        this.sampleQuantity = sampleQuantity;
        this.customerId = customerId;
        this.customerName = customerName;
        this.linkMan = linkMan;
        this.linkPhone = linkPhone;
        this.customerAddress = customerAddress;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.qcSource = qcSource;
        this.judgment = judgment;
        this.responsePerson = responsePerson;
        this.reportMethod = reportMethod;
        this.reportDate = reportDate;
        this.deadLine = deadLine;
        this.inceptPersonId = inceptPersonId;
        this.isAssist = isAssist;
    }

    public DtoQCProject(String id,String projectCode,String projectTypeId,String projectName, String status, Date inputTime,
                        Date inceptTime, String sampleDescription,String sampleQuantity, String customerId, String customerName,
                        String linkMan,String linkPhone,String customerAddress,Integer qcGrade,Integer qcType,String qcSource,
                        String judgment,String responsePerson, String reportMethod,Date reportDate,Date deadLine, String inceptPersonId,Boolean isAssist) {
        this.id = id;
        this.projectCode = projectCode;
        this.projectTypeId = projectTypeId;
        this.projectName = projectName;
        this.status = status;
        this.inputTime = inputTime;
        this.inceptTime = inceptTime;
        this.sampleDescription = sampleDescription;
        this.sampleQuantity = sampleQuantity;
        this.customerId = customerId;
        this.customerName = customerName;
        this.linkMan = linkMan;
        this.linkPhone = linkPhone;
        this.customerAddress = customerAddress;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.qcSource = qcSource;
        this.judgment = judgment;
        this.responsePerson = responsePerson;
        this.reportMethod = reportMethod;
        this.reportDate = reportDate;
        this.deadLine = deadLine;
        this.inceptPersonId = inceptPersonId;
        this.isAssist = isAssist;
    }
}
