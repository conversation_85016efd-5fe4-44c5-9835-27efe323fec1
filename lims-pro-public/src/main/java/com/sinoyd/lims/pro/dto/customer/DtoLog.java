package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.lims.pro.dto.*;
import lombok.Data;

import java.util.Date;


/**
 * DtoLog实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
@Data
public class DtoLog implements BaseEntity {
    /**
     * 主键id
     */
    private String id;

    /**
     * 操作者Id
     */
    private String operatorId;

    /**
     * 操作者名字
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作类型（新建、保存、修改等）
     */
    private String operateInfo;

    /**
     * 下一步操作人Id
     */
    private String nextOperatorId;

    /**
     * 下一步操作人名字
     */
    private String nextOperatorName;

    /**
     * 日志类型（如项目的方案、合同，样品的信息、检测项目）
     */
    private Integer logType;

    /**
     * 对象id
     */
    private String objectId;

    /**
     * 对象类型（检测单、项目、数据等）
     */
    private Integer objectType;

    /**
     * 说明
     */
    private String comment;

    /**
     * 意见（评审意见等）
     */
    private String opinion;

    /**
     * 备注
     */
    private String remark;

    /**
     * 组装说明
     */
    private String allComment;

    /**
     * 日志类型名称
     */
    private String logTypeName;

    /**
     * 对象类型名称
     */
    private String objectTypeName;

    /**
     * 默认的构造函数
     */
    public DtoLog() {

    }

    /**
     * 费用管理的相关日志
     * @param cost 费用管理
     */
    public DtoLog(DtoLogForCost cost) {
        this.setId(cost.getId());
        this.setOperatorId(cost.getOperatorId());
        this.setOperatorName(cost.getOperatorName());
        this.setOperateTime(cost.getOperateTime());
        this.setOperateInfo(cost.getOperateInfo());
        this.setNextOperatorId(cost.getNextOperatorId());
        this.setNextOperatorName(cost.getNextOperatorName());
        this.setLogType(cost.getLogType());
        this.setObjectId(cost.getObjectId());
        this.setObjectType(cost.getObjectType());
        this.setComment(cost.getComment());
        this.setOpinion(cost.getOpinion());
        this.setRemark(cost.getRemark());
    }


    /**
     * 送样单的相关日志
     * @param record 送样单相关日志
     */
    public DtoLog(DtoLogForRecord record) {
        this.setId(record.getId());
        this.setOperatorId(record.getOperatorId());
        this.setOperatorName(record.getOperatorName());
        this.setOperateTime(record.getOperateTime());
        this.setOperateInfo(record.getOperateInfo());
        this.setNextOperatorId(record.getNextOperatorId());
        this.setNextOperatorName(record.getNextOperatorName());
        this.setLogType(record.getLogType());
        this.setObjectId(record.getObjectId());
        this.setObjectType(record.getObjectType());
        this.setComment(record.getComment());
        this.setOpinion(record.getOpinion());
        this.setRemark(record.getRemark());
    }

    /**
     * 样品的相关日志
     * @param sample 样品的相关日志
     */
    public DtoLog(DtoLogForSample sample) {
        this.setId(sample.getId());
        this.setOperatorId(sample.getOperatorId());
        this.setOperatorName(sample.getOperatorName());
        this.setOperateTime(sample.getOperateTime());
        this.setOperateInfo(sample.getOperateInfo());
        this.setNextOperatorId(sample.getNextOperatorId());
        this.setNextOperatorName(sample.getNextOperatorName());
        this.setLogType(sample.getLogType());
        this.setObjectId(sample.getObjectId());
        this.setObjectType(sample.getObjectType());
        this.setComment(sample.getComment());
        this.setOpinion(sample.getOpinion());
        this.setRemark(sample.getRemark());
    }

    /**
     * 数据的相关日志
     * @param ana 数据的相关日志
     */
    public DtoLog(DtoLogForData ana) {
        this.setId(ana.getId());
        this.setOperatorId(ana.getOperatorId());
        this.setOperatorName(ana.getOperatorName());
        this.setOperateTime(ana.getOperateTime());
        this.setOperateInfo(ana.getOperateInfo());
        this.setNextOperatorId(ana.getNextOperatorId());
        this.setNextOperatorName(ana.getNextOperatorName());
        this.setLogType(ana.getLogType());
        this.setObjectId(ana.getObjectId());
        this.setObjectType(ana.getObjectType());
        this.setComment(ana.getComment());
        this.setOpinion(ana.getOpinion());
        this.setRemark(ana.getRemark());
    }


    /**
     * 工作单的相关日志
     * @param workSheet 工作单的相关日志
     */
    public DtoLog(DtoLogForWorkSheet workSheet) {
        this.setId(workSheet.getId());
        this.setOperatorId(workSheet.getOperatorId());
        this.setOperatorName(workSheet.getOperatorName());
        this.setOperateTime(workSheet.getOperateTime());
        this.setOperateInfo(workSheet.getOperateInfo());
        this.setNextOperatorId(workSheet.getNextOperatorId());
        this.setNextOperatorName(workSheet.getNextOperatorName());
        this.setLogType(workSheet.getLogType());
        this.setObjectId(workSheet.getObjectId());
        this.setObjectType(workSheet.getObjectType());
        this.setComment(workSheet.getComment());
        this.setOpinion(workSheet.getOpinion());
        this.setRemark(workSheet.getRemark());
    }

    /**
     * 项目的相关日志
     * @param project 项目的相关日志
     */
    public DtoLog(DtoLogForProject project) {
        this.setId(project.getId());
        this.setOperatorId(project.getOperatorId());
        this.setOperatorName(project.getOperatorName());
        this.setOperateTime(project.getOperateTime());
        this.setOperateInfo(project.getOperateInfo());
        this.setNextOperatorId(project.getNextOperatorId());
        this.setNextOperatorName(project.getNextOperatorName());
        this.setLogType(project.getLogType());
        this.setObjectId(project.getObjectId());
        this.setObjectType(project.getObjectType());
        this.setComment(project.getComment());
        this.setOpinion(project.getOpinion());
        this.setRemark(project.getRemark());
    }

    /**
     * 方案的相关日志
     * @param plan 方案的相关日志
     */
    public DtoLog(DtoLogForPlan plan) {
        this.setId(plan.getId());
        this.setOperatorId(plan.getOperatorId());
        this.setOperatorName(plan.getOperatorName());
        this.setOperateTime(plan.getOperateTime());
        this.setOperateInfo(plan.getOperateInfo());
        this.setNextOperatorId(plan.getNextOperatorId());
        this.setNextOperatorName(plan.getNextOperatorName());
        this.setLogType(plan.getLogType());
        this.setObjectId(plan.getObjectId());
        this.setObjectType(plan.getObjectType());
        this.setComment(plan.getComment());
        this.setOpinion(plan.getOpinion());
        this.setRemark(plan.getRemark());
    }


    /**
     * 报告的相关日志
     * @param report 报告的相关日志
     */
    public DtoLog(DtoLogForReport report) {
        this.setId(report.getId());
        this.setOperatorId(report.getOperatorId());
        this.setOperatorName(report.getOperatorName());
        this.setOperateTime(report.getOperateTime());
        this.setOperateInfo(report.getOperateInfo());
        this.setNextOperatorId(report.getNextOperatorId());
        this.setNextOperatorName(report.getNextOperatorName());
        this.setLogType(report.getLogType());
        this.setObjectId(report.getObjectId());
        this.setObjectType(report.getObjectType());
        this.setComment(report.getComment());
        this.setOpinion(report.getOpinion());
        this.setRemark(report.getRemark());
    }
}