package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;


/**
 * QualityManage实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "QualityManage")
@Data
public class QualityManage implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public QualityManage() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 关联样品数据id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("关联样品数据id")
    private String anaId;

    /**
     * 质控类型（枚举EnumQMType：1.标样，2.加标样，3.其他）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控类型（枚举EnumQMType：1.标样，2.加标样，3.其他）")
    private Integer qmType;

    /**
     * 质控值（标准样的值/加标的值/其他为空）
     */
    @ApiModelProperty("质控值（标准样的值/加标的值/其他为空）")
    @Length(message = "质控值（标准样的值/加标的值/其他为空）{validation.message.length}", max = 255)
    private String qmValue;

    /**
     * 范围
     */
    @ApiModelProperty("范围")
    @Length(message = "范围{validation.message.length}", max = 255)
    private String qmRange;

    /**
     * 加标体积
     */
    @Column(length = 50)
    @ApiModelProperty("加标体积")
    @Length(message = "加标体积{validation.message.length}", max = 50)
    private String qmVolume;

    /**
     * 添加质控人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("添加质控人员id")
    private String qmPersonId;

    /**
     * 添加质控时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("添加质控时间")
    private Date qmTime;

    /**
     * 测定值
     */
    @Column(length = 50)
    @ApiModelProperty("测定值")
    @Length(message = "测定值{validation.message.length}", max = 50)
    private String qmTestValue;

    /**
     * 样值（可以是质量的也可以是浓度的）
     */
    @Column(length = 50)
    @ApiModelProperty("样值（可以是质量的也可以是浓度的）")
    @Length(message = "样值（可以是质量的也可以是浓度的）{validation.message.length}", max = 50)
    private String stTestValue;

    /**
     * 标准编号
     */
    @ApiModelProperty("标准编号")
    @Length(message = "标准编号{validation.message.length}", max = 255)
    private String qmCode;

    /**
     * 原样的检测结果（找限值范围）
     */
    @Column(length = 50)
    @ApiModelProperty("原样的检测结果（找限值范围）")
    @Length(message = "原样的检测结果（找限值范围）{validation.message.length}", max = 50)
    private String qmOriginValue;

    /**
     * 仪器id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器id")
    private String instrumentId;

    /**
     * 是否混标
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否混标")
    private Boolean isMixedStandard;

    /**
     * 分析项目名称
     */
    @Column(length = 100)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 100)
    private String redAnalyzeItemName;

    /**
     * 计量单位id
     */
    @Column(length = 50)
    @ApiModelProperty("计量单位id")
    private String unitId;

    /**
     * 计量单位
     */
    @Column(length = 50)
    @ApiModelProperty("计量单位")
    @Length(message = "计量单位{validation.message.length}", max = 50)
    private String unit;

    /**
     * 不确定度类型
     */
    @ApiModelProperty("不确定度类型")
    private Integer uncertainType;

    /**
     * 范围低点
     */
    @Column(length = 50)
    @ApiModelProperty("范围低点")
    private String rangeLow;

    /**
     * 范围高点
     */
    @Column(length = 50)
    @ApiModelProperty("范围高点")
    private String rangeHigh;
    
    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}