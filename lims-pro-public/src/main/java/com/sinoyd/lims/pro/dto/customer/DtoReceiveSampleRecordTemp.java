package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bouncycastle.util.Strings;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 送样项目实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/19
 * @since V100R001
 */
@Data
public class DtoReceiveSampleRecordTemp extends LimsBaseEntity {

    /**
     * 加载送样项目
     *
     * @param project 送样项目实体
     */
    public void loadFromProject(DtoProject project) {
        this.leaderId = project.getLeaderId();
        this.reportMakerId = project.getReportMakerId();
        this.schemeMakerId = project.getSchemeMakerId();
        this.spotPersonId = project.getSpotPersonId();
        this.supervisorId = project.getSupervisorId();
        this.deadLine = project.getDeadLine();
        this.reportDate = project.getReportDate();
        this.requireAnalyzeDate = project.getRequireAnalyzeDate();
        this.requireSamplingDate = project.getRequireSamplingDate();
        this.responsePerson = project.getResponsePerson();
        this.requires = project.getRequires();
        this.testMethodRequires = project.getTestMethodRequires();
        this.remark = project.getRemark();
        this.receiveRemark = project.getReceiveRemark();
        this.isUseMethod = project.getIsUseMethod();
        this.isEvaluate = project.getIsEvaluate();
        this.isWarning = project.getIsWarning();
        this.warningDay = project.getWarningDay();
        this.isFeedback = project.getIsFeedback();
        this.isContract = project.getIsContract();
        this.subName = project.getSubName();
        this.subItems = project.getSubItems();
        this.subMethod = project.getSubMethod();
        this.isOutsourcing = project.getIsOutsourcing();
        this.isMakePlan = project.getIsMakePlan();
        this.reportMakerIIId = project.getReportMakerIIId();
        this.qcGrade = project.getQcGrade();
        this.qcType = project.getQcType();
        this.qcSource = project.getQcSource();
        this.judgment = project.getJudgment();
        this.projectId = project.getId();
        this.projectCode = project.getProjectCode();
        this.projectTypeId = project.getProjectTypeId();
        this.projectTypeCode = project.getProjectTypeCode();
        this.projectName = project.getProjectName();
        this.inceptPersonId = project.getInceptPersonId();
        this.inceptTime = project.getInceptTime();
        this.isStress = project.getIsStress();
        this.grade = project.getGrade();
        this.customerId = project.getCustomerId();
        this.customerName = project.getCustomerName();
        this.inspectedEntId = project.getInspectedEntId();
        this.inspectedEnt = project.getInspectedEnt();
        this.inspectedLinkMan = project.getInspectedLinkMan();
        this.inspectedLinkPhone = project.getInspectedLinkPhone();
        this.inspectedAddress = project.getInspectedAddress();
        this.customerOwner = project.getCustomerOwner();
        this.customerAddress = project.getCustomerAddress();
        this.linkMan = project.getLinkMan();
        this.linkPhone = project.getLinkPhone();
        this.linkEmail = project.getLinkEmail();
        this.linkFax = project.getLinkFax();
        this.zipCode = project.getZipCode();
        this.monitorPurp = project.getMonitorPurp();
        this.monitorMethods = project.getMonitorMethods();
        this.customerRequired = project.getCustomerRequired();
        this.sampleType = project.getSampleType();
        this.postMethod = project.getPostMethod();
        this.saveCondition = project.getSaveCondition();
        this.reportNum = project.getReportNum();
        this.qrCodeUrl = project.getQrCodeUrl();
        this.contractId = project.getContractId();
        this.contractName = project.getContractName();
        this.contractCode = project.getContractCode();
        this.sendSamplePerson = project.getSendSamplePerson();
        this.totalAmount = project.getTotalAmount();
        this.contractStatus = project.getContractStatus();
        this.postMethodKeys = project.getPostMethodKeys();
        this.base64Content = project.getBase64Content();
        this.timeBegin = project.getTimeBegin();
        this.timeEnd = project.getTimeEnd();
        this.setSampleTypeArr(project.getSampleTypeArr());
        this.orderId = project.getOrderId();
        this.extendStr1 = project.getExtendStr1();
        this.reportStamp = project.getReportStamp();
        this.monitorMethod = project.getMonitorMethod();
        this.monitorMethodRemark = project.getMonitorMethodRemark();
        this.isAccredited = project.getIsAccredited();
        this.isAccreditedRemark = project.getIsAccreditedRemark();
        this.environmentCode = project.getEnvironmentCode();
        this.isCMA = project.getIsCMA();
        this.regulateReportType = project.getRegulateReportType();
        this.pollutionCode = project.getPollutionCode();
        this.addressName = project.getAddressName();
        this.setSampleSaveMethod(project.getSampleSaveMethod());
        this.setIsSaveContainer(project.getIsSaveContainer());
        this.setIsSampleCountRight(project.getIsSampleCountRight());
        this.setSampleValidityPeriod(project.getSampleValidityPeriod());
        this.setSampleConfirmRemark(project.getSampleConfirmRemark());
    }

    public String[] getSampleTypeArr() {
        if (StringUtil.isNotNull(this.sampleTypeArr)) {
            return this.sampleTypeArr;
        } else if (StringUtils.isNotNullAndEmpty(this.getSampleType())) {
            return Strings.split(this.getSampleType(), ',');
        }
        return null;
    }

    public void setSampleTypeArr(String[] sampleTypeArr) {
        this.sampleTypeArr = sampleTypeArr;
        if (StringUtil.isNotNull(sampleTypeArr)) {
            this.setSampleType(String.join(",", sampleTypeArr));
        } else {
            this.setSampleType("");
        }
    }

    /**
     * 送样单id
     */
    private String id = UUIDHelper.NewID();

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 送样单号
     */
    private String recordCode;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 送样人（内部人员）ID
     */
    private String senderId;

    /**
     * 送样人（采样负责人）
     */
    private String senderName;

    /**
     * 登记时间
     */
    private Date receiveTime;

    /**
     * 登记人id
     */
    private String recorderId;

    /**
     * 接样类型（枚举EnumReceiveType：1.内部 2.外部 3.现场 4.）
     */
    private Integer receiveType;

    /**
     * 项目备注
     */
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 送样单备注
     */
    private String receiveRemark;

    /**
     * 流水编号
     */
    @Length(message = "流水编号{validation.message.length}", max = 50)
    private String projectCode;

    /**
     * 项目类型id（外键）
     */
    private String projectTypeId;

    /**
     * 项目类型编码
     */
    private String projectTypeCode;

    /**
     * 项目名称
     */
    @Length(message = "项目名称{validation.message.length}", max = 100)
    private String projectName;

    /**
     * 登记人id
     */
    private String inceptPersonId;

    /**
     * 委托时间
     */
    private Date inceptTime;

    /**
     * 是否着重关注
     */
    private Boolean isStress;

    /**
     * 项目等级(EnumProjectGrade：0.一般 1.紧急 2.特急)
     */
    private Integer grade;

    /**
     * 委托单位id
     */
    private String customerId;

    /**
     * 委托单位
     */
    @Length(message = "委托单位{validation.message.length}", max = 1000)
    private String customerName;

    /**
     * 受检单位Id
     */
    private String inspectedEntId;

    /**
     * 受检方联系人
     */
    @Length(message = "受检方联系人{validation.message.length}", max = 50)
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    @Length(message = "受检方联系电话{validation.message.length}", max = 50)
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    @Length(message = "受检方地址{validation.message.length}", max = 100)
    private String inspectedAddress;

    /**
     * 受检单位
     */
    @Length(message = "受检单位{validation.message.length}", max = 100)
    private String inspectedEnt;

    /**
     * 法人代表
     */
    @Length(message = "法人代表{validation.message.length}", max = 50)
    private String customerOwner;

    /**
     * 地址
     */
    @Length(message = "地址{validation.message.length}", max = 100)
    private String customerAddress;

    /**
     * 联系人
     */
    @Length(message = "联系人{validation.message.length}", max = 50)
    private String linkMan;

    /**
     * 电话
     */
    @Length(message = "电话{validation.message.length}", max = 50)
    private String linkPhone;

    /**
     * 送样人（TB_PRO_Project表中字段）
     */
    @Length(message = "送样人{validation.message.length}", max = 50)
    private String sendSamplePerson;

    /**
     * 电子邮件
     */
    @Length(message = "电子邮件{validation.message.length}", max = 50)
    private String linkEmail;

    /**
     * 传真
     */
    @Length(message = "传真{validation.message.length}", max = 50)
    private String linkFax;

    /**
     * 邮编
     */
    @Length(message = "邮编{validation.message.length}", max = 10)
    private String zipCode;

    /**
     * 监测目的
     */
    @Length(message = "监测目的{validation.message.length}", max = 255)
    private String monitorPurp;

    /**
     * 监测方式
     */
    @Length(message = "监测方式{validation.message.length}", max = 255)
    private String monitorMethods;

    /**
     * 客户委托内容
     */
    @Length(message = "客户委托内容{validation.message.length}", max = 1000)
    private String customerRequired;

    /**
     * 样品类型
     */
    @Length(message = "样品类型{validation.message.length}", max = 1000)
    private String sampleType;

    /**
     * 发送方式(枚举EnumPostMethod：0.未设置 1.自取 2.挂号邮寄 3.特快专递)
     */
    private Integer postMethod;

    /**
     * 保存条件
     */
    @Length(message = "保存条件{validation.message.length}", max = 255)
    private String saveCondition;

    /**
     * 检测报告数
     */
    @Length(message = "检测报告数{validation.message.length}", max = 255)
    private String reportNum;

    /**
     * 二维码图片存储地址
     */
    @Length(message = "二维码图片存储地址{validation.message.length}", max = 500)
    private String qrCodeUrl;

    /**
     * 报告交付方式值数组
     */
    private List<Integer> postMethodKeys;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同开始时间
     */
    private String timeBegin;

    /**
     * 合同结束时间
     */
    private String timeEnd;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 合同状态 0未签  1已签 EnumContractStatus
     */
    private Integer contractStatus;

    /**
     * 监测类型数组
     */
    private String[] sampleTypeArr;

    /**
     * 项目负责人id
     */
    private String leaderId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 编制报告人id
     */
    private String reportMakerId;

    /**
     * 编制方案人id
     */
    private String schemeMakerId;

    /**
     * 现场负责人id
     */
    private String spotPersonId;

    /**
     * 监督人id
     */
    private String supervisorId;

    /**
     * 要求完成时间
     */
    private Date deadLine;

    /**
     * 预计出具报告时间
     */
    private Date reportDate;

    /**
     * 预计完成分析日期
     */
    private Date requireAnalyzeDate;

    /**
     * 预计完成采样日期
     */
    private Date requireSamplingDate;

    /**
     * 责任人（质量控制计划）
     */
    private String responsePerson;

    /**
     * 测试项目或具体要求的文字说明
     */
    private String requires;

    /**
     * 测试标准文字说明
     */
    private String testMethodRequires;

    /**
     * 是否同意使用非标准方法
     */
    private Boolean isUseMethod;

    /**
     * 是否评价
     */
    private Boolean isEvaluate;

    /**
     * 是否警告
     */
    private Boolean isWarning;

    /**
     * 提前警告天数
     */
    private Integer warningDay;

    /**
     * 是否反馈
     */
    private Boolean isFeedback;

    /**
     * 是否拟订合同
     */
    private Boolean isContract;

    /**
     * 分包单位
     */
    private String subName;

    /**
     * 分包项目
     */
    private String subItems;

    /**
     * 分包方式(枚举EnumSubMethod:0.无1.客户指定2.本站联系)
     */
    private Integer subMethod;

    /**
     * 分包情况(枚举EnumOutSourcing: 0.不分包1.全部分包2.部分分包)
     */
    private Integer isOutsourcing;

    /**
     * 是否编制方案
     */
    private Boolean isMakePlan;

    /**
     * 辅助编制报告人id
     */
    private String reportMakerIIId;

    /**
     * 质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）
     */
    private Integer qcGrade;

    /**
     * 质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核)
     */
    private Integer qcType;

    /**
     * 质量来源（质量控制——外部质控）
     */
    private String qcSource;

    /**
     * 判断依据（质量控制计划）
     */
    private String judgment;

    /**
     * 项目类型名称
     */
    private String extendStr1;

    /**
     * 样品类型ids
     */
    private List<String> sampleTypeIds;

    /**
     * 样品类型名称s
     */
    private List<String> sampleTypeNames;

    /**
     * 采样人id集合
     */
    private List<String> samplingPersonIds = new ArrayList<>();

    /**
     * 采样车辆集合
     */
    private List<String> carIds = new ArrayList<>();

    /**
     * 图片转化后的base64的字符串
     */
    private String base64Content;

    /**
     * 报告盖章  监测专用章、CMA章、CNAS章（多选）
     */
    @Length(message = "报告盖章{validation.message.length}", max = 50)
    private String reportStamp;

    /**
     * 监测方法  由服务方决定、由委托方决定
     */
    private Integer monitorMethod;

    /**
     * 监测方法说明
     */
    @Length(message = "监测方法说明{validation.message.length}", max = 500)
    private String monitorMethodRemark;

    /**
     * 是否资质认定：是，否'
     */
    private Boolean isAccredited;

    /**
     * 资质认定说明
     */
    @Length(message = "资质认定说明{validation.message.length}", max = 500)
    private String isAccreditedRemark;

    @Transient
    private DtoProjectContract projectContract;

    /**
     * 环境院编号
     */
    @Transient
    private String environmentCode;

    /**
     * 任务所在区名称
     */
    @Transient
    private String addressName;

    /**
     * 是否加盖CMA章
     */
    @Transient
    private Integer isCMA;

    /**
     * 报告类型
     */
    @Transient
    private String regulateReportType;

    /**
     * 监管平台客户污染源编号
     */
    @Transient
    private String pollutionCode;


    /**
     * 保存方式：符合规范、不符合规范、不确定（单选）
     */
    @Transient
    private Integer sampleSaveMethod;

    /**
     * 保存容器：正确、不正确、不确定（单选）
     */
    @Transient
    private Integer isSaveContainer;

    /**
     * 样品数量：正确、不正确、不确定（单选）
     */
    @Transient
    private Integer isSampleCountRight;

    /**
     * 样品有效期：在有效期内、不在有效期内、不确定（单选）
     */
    @Transient
    private Integer sampleValidityPeriod;

    /**
     * 样品确认其他意见
     */
    @Length(message = "样品确认其他意见{validation.message.length}", max = 500)
    private String sampleConfirmRemark;

    private Integer infoStatus;

    /**
     * 内部接收人id
     */
    private String innerReceiveId;

    /**
     * 内部接收人
     */
    private String innerReceiveName;


    /**
     * 默认构造函数
     */
    public DtoReceiveSampleRecordTemp() {

    }

    /**
     * 该构造函数主要用到 ReceiveSampleRecordServiceImpl 下面 findReceiveSampleRecordList 打开检测单方法，如果修改，对应的应用地方也要调整
     *
     * @param projectId    项目id
     * @param id           送样单id
     * @param qcGrade      质量任务等级
     * @param judgment     判断依据
     * @param customerName 委托单位
     */
    public DtoReceiveSampleRecordTemp(String projectId, String id, Integer qcGrade, String judgment, String customerName) {
        this.projectId = projectId;
        this.id = id;
        this.qcGrade = qcGrade;
        this.judgment = judgment;
        this.customerName = customerName;
    }

    /**
     * 该构造函数主要用到 ReceiveSampleRecordServiceImpl 下面 findReceiveSampleRecordList 打开检测单方法，如果修改，对应的应用地方也要调整
     *
     * @param projectId    项目id
     * @param id           送样单id
     * @param qcGrade      质量任务等级
     * @param judgment     判断依据
     * @param customerName 委托单位
     * @param infoStatus   送样单状态
     */
    public DtoReceiveSampleRecordTemp(String projectId, String id, Integer qcGrade, String judgment, String customerName, Integer infoStatus) {
        this.projectId = projectId;
        this.id = id;
        this.qcGrade = qcGrade;
        this.judgment = judgment;
        this.customerName = customerName;
        this.infoStatus = infoStatus;
    }
}
