package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ExpressageInfo;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoExpressageInfo实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ExpressageInfo")
 @Data
 @DynamicInsert
 public  class DtoExpressageInfo extends ExpressageInfo {

    /** 
     * 快递报告关联
    */
    @Transient
    private List<DtoExpressageInfo2Report>expressageInfo2Report = new ArrayList<>();

    /**
     * 报告标识
     */
    @Transient
    private String reportId;

    /**
     * 报告集合标识
     */
    @Transient
    private List<String> reportIds;

    @Transient
    private Boolean isClear;
 }