package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.*;

/**
 * 监管平台排污单位实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/13
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPPollutantDischargingUnitVO {

    /**
     * 污染源名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "DEVCOMPANY")
    private String enterpriseName;

    /**
     * 污染源编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "WRYBH")
    private String pollutionCode;

    /**
     * 经营地址
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "OPEADDRESS")
    private String businessAddress;

    /**
     * 行政区
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "COUNTY")
    private String country;

    private String areaId;
}
