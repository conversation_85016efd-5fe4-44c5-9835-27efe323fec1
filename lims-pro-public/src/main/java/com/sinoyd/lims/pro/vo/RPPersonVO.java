package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;

/**
 * 上海监管平台人员VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPPersonVO {

    /**
     * 人员id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    private String id;

    /**
         * 姓名
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XM")
    private String name;

    /**
     * 性别
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XB")
    private String sex;

    /**
     * 年龄
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "NL")
    private String age;

    /**
     * 岗位
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ZW")
    private String post;

    /**
     * 学历
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XL")
    private String degree;

    /**
     * 是否专业技术人员
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XZ")
    private String isProfessional;

    /**
     * 专业技术人员职称（同等能力）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ZYJSRYZC")
    private String professionalTitles;

    /**
     * 所学专业
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SXZY")
    private String major;

    /**
     * 岗位：其他名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ZW_OTHER")
    private String positionOther;
}
