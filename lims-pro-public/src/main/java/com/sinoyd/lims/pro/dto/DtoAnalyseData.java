package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.AnalyseData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * DtoAnalyseData实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_AnalyseData")
//@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoAnalyseData extends AnalyseData {
    private static final long serialVersionUID = 1L;

    public DtoAnalyseData() {

    }

    public DtoAnalyseData(Boolean isInit) {
        if (isInit) {
            //初始化一些信息（业务逻辑比较相关的不要在这里初始化，如一些状态的枚举数字）
            this.setAnalystName("");
            this.setTestValue("");
            this.setTestOrignValue("");
            this.setTestValueDstr("");
            this.setDimension("");
        }
    }

    /**
     * 该构造函数用到DetailDataServiceImpl getAnalyseDataBySampleIds的方法，如果要修改，统一修正
     *
     * @param id             数据id
     * @param sampleId       样品id
     * @param testId         测试项目id
     * @param analyseItemId  分析项目id
     * @param testValue      出证结果
     * @param testOrignValue 检测结果（未修约）
     * @param testValueD     参与运算的值（检测结果的数值）（已修约）
     * @param testValueDstr  检测结果（已修约）
     * @param status         数据状态
     * @param dataStatus     数据状态
     * @param isDataEnabled  是否出证
     */
    public DtoAnalyseData(String id, String sampleId,
                          String testId,
                          String analyseItemId,
                          String testValue,
                          String testOrignValue,
                          BigDecimal testValueD,
                          String testValueDstr,
                          String status,
                          Integer dataStatus,
                          Boolean isDataEnabled
    ) {
        this.setId(id);
        this.setSampleId(sampleId);
        this.setTestId(testId);
        this.setAnalyseItemId(analyseItemId);
        this.setTestValue(testValue);
        this.setTestOrignValue(testOrignValue);
        this.setTestValueD(testValueD);
        this.setTestValueDstr(testValueDstr);
        this.setStatus(status);
        this.setDataStatus(dataStatus);
        this.setIsDataEnabled(isDataEnabled);
    }

    /**
     * 该构造函数用到DetailDataServiceImpl getAnalyseDataBySampleIds的方法，如果要修改，统一修正(多添加一个工作单id)
     *
     * @param id                数据id
     * @param sampleId          样品id
     * @param testId            测试项目id
     * @param analyseItemId     分析项目id
     * @param testValue         出证结果
     * @param testOrignValue    检测结果（未修约）
     * @param testValueD        参与运算的值（检测结果的数值）（已修约）
     * @param testValueDstr     检测结果（已修约）
     * @param status            数据状态
     * @param dataStatus        数据状态
     * @param isDataEnabled     是否出证
     * @param workSheetFolderId 工作单id
     */
    public DtoAnalyseData(String id, String sampleId,
                          String testId,
                          String analyseItemId,
                          String testValue,
                          String testOrignValue,
                          BigDecimal testValueD,
                          String testValueDstr,
                          String status,
                          Integer dataStatus,
                          Boolean isDataEnabled,
                          String workSheetFolderId
    ) {
        this(id, sampleId, testId, analyseItemId, testValue, testOrignValue, testValueD, testValueDstr, status, dataStatus, isDataEnabled);
        this.setWorkSheetFolderId(workSheetFolderId);
    }

    /**
     * 该构造函数主要用到 领样单获取领样单下数据，分析数据获取分析数据（AnalyseDataServiceImpl下的findAnalyseDataWithDelete，findAnalyseDataBySample）
     *
     * @param id                   数据id
     * @param workSheetFolderId    检测单id
     * @param sampleId             样品id
     * @param testId               测试项目id
     * @param analyseItemId        分析项目id
     * @param redAnalyzeItemName   分析项目名称
     * @param analyzeMethodId      分析项目方法id
     * @param redAnalyzeMethodName 分析项目方法
     * @param redCountryStandard   标准
     * @param analystId            分析人id
     * @param analystName          分析人
     * @param status               状态
     * @param qcGrade              质控等级
     * @param isCompleteField      是否现场
     * @param isOutsourcing        是否分包
     * @param dataStatus           数据状态
     * @param isDataEnabled        是否确认
     */
    public DtoAnalyseData(String id, String workSheetFolderId, String sampleId, String sampleTypeId, String testId, String analyseItemId, String redAnalyzeItemName, String analyzeMethodId,
                          String redAnalyzeMethodName, String redCountryStandard, String analystId, String analystName, String status, Integer qcGrade, Boolean isCompleteField,
                          Boolean isOutsourcing, Integer dataStatus, Boolean isDataEnabled) {
        this.setId(id);
        this.setWorkSheetFolderId(workSheetFolderId);
        this.setSampleId(sampleId);
        this.setSampleTypeId(sampleTypeId);
        this.setTestId(testId);
        this.setAnalyseItemId(analyseItemId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setAnalyzeMethodId(analyzeMethodId);
        this.setRedAnalyzeMethodName(redAnalyzeMethodName);
        this.setRedCountryStandard(redCountryStandard);
        this.setAnalystId(analystId);
        this.setAnalystName(analystName);
        this.setStatus(status);
        this.setQcGrade(qcGrade);
        this.setIsCompleteField(isCompleteField);
        this.setIsOutsourcing(isOutsourcing);
        this.setDataStatus(dataStatus);
        this.setIsDataEnabled(isDataEnabled);
    }

    /**
     * 点位id
     */
    @Transient
    private String sampleFolderId;

    /**
     * 点位名称
     */
    @Transient
    private String watchSpot;

    /**
     * 检测类型
     */
    @Transient
    private String sampleTypeName;

    /**
     * 关联样品id
     */
    @Transient
    private String associateSampleId;

    /**
     * 样品类别
     */
    @Transient
    private Integer sampleCategory;

    /**
     * 测试项目id集合（传输用）
     */
    @Transient
    private List<String> testIds = new ArrayList<>();

    /**
     * 送样单id
     */
    @Transient
    private String receiveId;

    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 领样状态
     */
    @Transient
    public Integer innerReceiveStatus;

    /**
     * 检测单分析日期
     */
    @Transient
    public Date folderAnalyzeTime;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;
    /**
     * 接样日期
     */
    @Transient
    private Date receiveTime;
    /**
     * 数据状态(用于检测数据统计)
     */
    @Transient
    private String analyseDataStatus;

    /**
     * 是否已经质控评价（用于环境质量项目登记、更新方案）
     */
    @Transient
    private Boolean isEvaluationRecord = false;


    /**
     * 这个构造函数主要用到AnalyseDataBaseRepository下的findBySampleIdInAndIsOutsourcing方法，主要是保证纠正状态查询的性能
     *
     * @param id              主键id
     * @param sampleId        样品id
     * @param isOutsourcing   是否分包
     * @param isCompleteField 是现场
     * @param dataStatus      数据状态
     * @param status          状态
     * @param isDataEnabled   是否出证
     */
    public DtoAnalyseData(String id,
                          String testId,
                          String sampleId,
                          Boolean isOutsourcing,
                          Boolean isSamplingOut,
                          Boolean isCompleteField,
                          Integer dataStatus,
                          String status,
                          Boolean isDataEnabled) {
        this.setId(id);
        this.setTestId(testId);
        this.setIsOutsourcing(isOutsourcing);
        this.setIsSamplingOut(isSamplingOut);
        this.setSampleId(sampleId);
        this.setIsCompleteField(isCompleteField);
        this.setDataStatus(dataStatus);
        this.setStatus(status);
        this.setIsDataEnabled(isDataEnabled);
    }


    public DtoAnalyseData(String id, String sampleId,
                          String testId,
                          String redAnalyzeItemName,
                          String redAnalyzeMethodName,
                          String redCountryStandard,
                          String analyseItemId,
                          String analyzeMethodId,
                          Integer mostSignificance,
                          Integer mostDecimal,
                          Boolean isQM,
                          String examLimitValue,
                          String dimensionId,
                          String dimension,
                          String analystId,
                          String analystName,
                          Boolean isCompleteField,
                          Boolean isOutsourcing,
                          Integer grade,
                          Boolean isPostCert,
                          Date certEffectiveTime) {
        this.setId(id);
        this.setSampleId(sampleId);
        this.setTestId(testId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setRedAnalyzeMethodName(redAnalyzeMethodName);
        this.setRedCountryStandard(redCountryStandard);
        this.setAnalyseItemId(analyseItemId);
        this.setAnalyzeMethodId(analyzeMethodId);
        this.setMostSignificance(mostSignificance);
        this.setMostDecimal(mostDecimal);
        this.setIsQm(isQM);
        this.setExamLimitValue(examLimitValue);
        this.setDimensionId(dimensionId);
        this.setDimension(dimension);
        this.setAnalystId(analystId);
        this.setAnalystName(analystName);
        this.setIsCompleteField(isCompleteField);
        this.setIsOutsourcing(isOutsourcing);
        this.setGrade(grade);
        this.setIsPostCert(isPostCert);
        this.setCertEffectiveTime(certEffectiveTime);
    }

    /**
     * 构造方法用于工作单评价结果选项卡
     *
     * @param id              分析数据id
     * @param sampleId        样品id
     * @param sampleCode      样品编号
     * @param testValue       分析数据
     * @param analyseItemName 分析数据名称
     */
    public DtoAnalyseData(String id, String sampleId, String sampleCode, String testValue, String analyseItemName) {
        this.setId(id);
        this.setSampleId(sampleId);
        this.setSampleCode(sampleCode);
        this.setTestValue(testValue);
        this.setRedAnalyzeItemName(analyseItemName);
    }

    public DtoAnalyseData(String id, String sampleId,
                          String testId,
                          String redAnalyzeItemName,
                          String redAnalyzeMethodName,
                          String redCountryStandard,
                          String analyseItemId,
                          String analyzeMethodId,
                          Integer mostSignificance,
                          Integer mostDecimal,
                          Boolean isQM,
                          String examLimitValue,
                          String dimensionId,
                          String dimension,
                          String analystId,
                          String analystName,
                          Boolean isCompleteField,
                          Boolean isOutsourcing,
                          Integer grade,
                          Boolean isPostCert,
                          Date certEffectiveTime,
                          Boolean isSamplingOut) {
        this.setId(id);
        this.setSampleId(sampleId);
        this.setTestId(testId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setRedAnalyzeMethodName(redAnalyzeMethodName);
        this.setRedCountryStandard(redCountryStandard);
        this.setAnalyseItemId(analyseItemId);
        this.setAnalyzeMethodId(analyzeMethodId);
        this.setMostSignificance(mostSignificance);
        this.setMostDecimal(mostDecimal);
        this.setIsQm(isQM);
        this.setExamLimitValue(examLimitValue);
        this.setDimensionId(dimensionId);
        this.setDimension(dimension);
        this.setAnalystId(analystId);
        this.setAnalystName(analystName);
        this.setIsCompleteField(isCompleteField);
        this.setIsOutsourcing(isOutsourcing);
        this.setGrade(grade);
        this.setIsPostCert(isPostCert);
        this.setCertEffectiveTime(certEffectiveTime);
        this.setIsSamplingOut(isSamplingOut);
    }

    /**
     * 该构造函数用到getAnalyseDataBySampleIds的方法，如果要修改，统一修正(多加一个量纲和是否现场)
     *
     * @param id                数据id
     * @param sampleId          样品id
     * @param testId            测试项目id
     * @param analyseItemId     分析项目id
     * @param testValue         出证结果
     * @param testOrignValue    检测结果（未修约）
     * @param testValueD        参与运算的值（检测结果的数值）（已修约）
     * @param testValueDstr     检测结果（已修约）
     * @param status            数据状态
     * @param dataStatus        数据状态
     * @param isDataEnabled     是否出证
     * @param workSheetFolderId 工作单id
     * @param dimension         量纲
     * @param isCompleteField   是否现场
     */
    public DtoAnalyseData(String id, String sampleId,
                          String testId,
                          String analyseItemId,
                          String testValue,
                          String testOrignValue,
                          BigDecimal testValueD,
                          String testValueDstr,
                          String status,
                          Integer dataStatus,
                          Boolean isDataEnabled,
                          String workSheetFolderId,
                          String dimension,
                          Boolean isCompleteField
    ) {
        this(id, sampleId, testId, analyseItemId, testValue, testOrignValue, testValueD, testValueDstr,
                status, dataStatus, isDataEnabled, workSheetFolderId);
        this.setDimension(dimension);
        this.setIsCompleteField(isCompleteField);
    }

    /**
     * 构造方法用于工作单评价结果选项卡(扩展量纲和检出限)
     *
     * @param id              分析数据id
     * @param sampleId        样品id
     * @param sampleCode      样品编号
     * @param testValue       分析数据
     * @param analyseItemName 分析数据名称
     * @param dimension       量纲
     * @param examLimitValue  检出限
     */
    public DtoAnalyseData(String id, String sampleId, String sampleCode, String testValue, String analyseItemName, String dimension, String examLimitValue) {
        this(id, sampleId, sampleCode, testValue, analyseItemName);
        this.setDimension(dimension);
        this.setExamLimitValue(examLimitValue);
    }
}