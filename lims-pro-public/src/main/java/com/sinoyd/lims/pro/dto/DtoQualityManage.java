package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.QualityManage;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoQualityManage实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_QualityManage")
 @Data
 @DynamicInsert
 public  class DtoQualityManage extends QualityManage {
    private static final long serialVersionUID = 1L;

    /**
     * 样品id
     */
    @Transient
    private String sampleId;

    /**
     * 测试项目id
     */
    @Transient
    private String testId;

    /**
     * 指标名称
     */
    @Transient
    private String testName;

    /**
     * 方法名称
     */
    @Transient
    private String redAnalyzeMethodName;

    /**
     * 标准
     */
    @Transient
    private String redCountryStandard;

   /**
    * 分析人
    */
   @Transient
   private String analystId;

   /**
    * 分析人姓名
    */
   @Transient
   private String analystName;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName = "";

    /**
     * 仪器编号
     */
    @Transient
    private String instrumentsCode = "";

    /**
     * 规格型号
     */
    @Transient
    private String model = "";

    /**
     * 是否现场
     */
    @Transient
    private Boolean isCompleteField;

    /**
     * 是否分包
     */
    @Transient
    private Boolean isOutsourcing;

    /**
     * 分析项目id
     */
    @Transient
    private String analyzeItemId;

    /**
     * 分析方法id
     */
    @Transient
    private String analyzeMethodId;
}