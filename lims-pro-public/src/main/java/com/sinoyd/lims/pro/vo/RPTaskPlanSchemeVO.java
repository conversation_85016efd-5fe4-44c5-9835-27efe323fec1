package com.sinoyd.lims.pro.vo;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 * 上海监管平台方案编制实体：方案详情
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/12
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Item")
@XmlType(name = "PlanSchemeItem")
public class RPTaskPlanSchemeVO {

    /**
     * 方案id
     */
    @XmlElement(name = "ID")
    private String id;

    /**
     * 点位名称
     */
    @XmlElement(name = "PT")
    private String watchSpot;

    /**
     * 点位经度
     */
    @XmlElement(name = "X")
    private String lon;

    /**
     * 点位纬度
     */
    @XmlElement(name = "Y")
    private String lat;

    public RPTaskPlanSchemeVO() {
    }

    public RPTaskPlanSchemeVO(DtoSampleFolder folder, String pushId) {
        this.id = pushId;
        this.watchSpot = folder.getWatchSpot();
        this.lon = StringUtils.isNotNullAndEmpty(folder.getLon()) ? folder.getLon() : "0";
        this.lat = StringUtils.isNotNullAndEmpty(folder.getLat()) ? folder.getLat() : "0";
    }
}
