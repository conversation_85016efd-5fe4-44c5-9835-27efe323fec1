package com.sinoyd.lims.pro.vo;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.dto.DtoReport;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.*;

/**
 * 监管平台报告数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/15
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPReportVO {

    /**
     * 监管平台项目id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "MTID")
    private String rpProjectId;

    /**
     * id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    private String id;

    /**
     * 报告编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "CODE")
    private String reportCode;

    /**
     * 报告时间
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "REPORT_DATE")
    private String reportDate;

    /**
     * 本系统上传的报告附件
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FILENAME")
    private String fileName;

    /**
     * 上传到监管平台返回的附件名
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FILENAMEOLD")
    private String rpFileName;

    /**
     * 根据报告数据构造
     *
     * @param rpProjectId 监管平台项目id
     * @param report      报告数据
     */
    public RPReportVO(String rpProjectId, DtoReport report) {
        this.rpProjectId = rpProjectId;
        this.id = "0";
        this.reportCode = report.getCode();
        this.reportDate = DateUtil.dateToString(report.getCreateTime(), DateUtil.YEAR);
    }

}
