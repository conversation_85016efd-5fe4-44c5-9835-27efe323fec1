package com.sinoyd.lims.pro.dto;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.entity.Sample;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * DtoSample实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Sample")
// @Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSample extends Sample {
    private static final long serialVersionUID = 1L;

    public DtoSample() {

    }

    /**
     * 构造 初始化一些字段
     */
    public DtoSample(Boolean isInit) {
        if (isInit) {
            this.setSubProjectId(UUIDHelper.GUID_EMPTY);
            this.setProjectId(UUIDHelper.GUID_EMPTY);
            this.setReceiveId(UUIDHelper.GUID_EMPTY);
            this.setSampleFolderId(UUIDHelper.GUID_EMPTY);
            this.setSamplingFrequencyId(UUIDHelper.GUID_EMPTY);
            this.setCode("");
            this.setCycleOrder(1);
            this.setTimesOrder(1);
            this.setSampleOrder(1);
            this.setSamplingPersonId(UUIDHelper.GUID_EMPTY);
            this.setSampleTypeId(UUIDHelper.GUID_EMPTY);
            this.setRedAnalyzeItems("");
            this.setSampleCategory(EnumPRO.EnumSampleCategory.原样.getValue());
            this.setInspectedEntId(UUIDHelper.GUID_EMPTY);
            this.setSamplingConfig(0);
            this.setStoreStatus(0);
            this.setMakeStatus(0);
            this.setDataChangeStatus(EnumPRO.EnumSampleChangeStatus.未变更.getValue());
            this.setCustomerCode("");
            this.setQcId(UUIDHelper.GUID_EMPTY);
            this.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
            this.setIsDeleted(false);
            this.setLon("");
            this.setLat("");
            this.setIsPrint(0);
            this.setIsKeep(false);
            this.setKeepLongTime(0);
            this.setSamKind(0);
            this.setIsQualified(true);
            this.setIsReturned(false);
            this.setConsistencyValidStatus(0);
            this.setSignerId(UUIDHelper.GUID_EMPTY);
            this.setIsOutsourcing(0);
            this.setSamplingRecordId(UUIDHelper.GUID_EMPTY);
            this.setBlindType(EnumPRO.EnumSampleBlindType.非盲样.getValue());
        }
    }

    /**
     * 检测大类id
     */
    @Transient
    private String bigSampleTypeId;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 关联样品编号
     */
    @Transient
    private String associateSampleCode;

    /**
     * 质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）
     */
    @Transient
    private Integer qcGrade = -1;

    /**
     * 质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）
     */
    @Transient
    private Integer qcType = -1;

    /**
     * 送样单号
     */
    @Transient
    private String recordCode;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 委托单位名称
     */
    @Transient
    private String customerName;

    /**
     * 送样人
     */
    @Transient
    private String senderName;

    /**
     * 送样日期
     */
    @Transient
    private Date sendTime;

    /**
     * 次数名称
     */
    @Transient
    private String timesName;

    /**
     * 样品批次名称
     */
    @Transient
    private String sampleTimesName;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 采样准备排序值
     */
    @Transient
    private Integer prepareOrderNum;

    @Transient
    private String sortOrder;

    @Transient
    private List<DtoAnalyseData> analyseData;

    @Transient
    private List<String> testIds;

    /**
     * 检测类型大类图标
     */
    @Transient
    private String icon;

    /**
     * 留样处置所有样品下的分析项目拼接
     */
    @Transient
    private String analyseItems;

    /**
     * 样品处置状态
     */
    @Transient
    private String reserveStatus;

    /**
     * 制备人名称
     */
    @Transient
    private String preparedPersonName;

    /**
     * 制备日期
     */
    @Transient
    private Date preparedDate;

    /**
     * 样品留样状态
     */
    @Transient
    private String keepStatus;

    /**
     * 样品测试项目领取数量
     */
    @Transient
    private Integer receiveTestNum;

    /**
     * 样品关联所有测试项目数量
     */
    @Transient
    private Integer receiveTotalTestNum;

    /**
     * 关联测试项目领取数据
     */
    @Transient
    private List<DtoAnalyzeItem> receiveTest;

    /**
     * 样品处置信息
     */
    @Transient
    private DtoSampleReserve sampleReserve;

    /**
     * 样品下所有数据是否都保存出征结果
     */
    @Transient
    private Boolean dataFlag;

    /**
     * 样品编号流水号
     */
    @Transient
    private Integer sn;

    /**
     * 这个构造函数主要用到SampleBaseRepository下的findByReceiveId方法，主要是保证纠正状态查询的性能
     *
     * @param id                 主键id
     * @param status             样品状态
     * @param ananlyzeStatus     分析状态
     * @param samplingStatus     采样状态
     * @param innerReceiveStatus 领取状态
     */
    public DtoSample(String id, String status, Integer ananlyzeStatus, Integer samplingStatus, Integer innerReceiveStatus) {
        this.setId(id);
        this.setStatus(status);
        this.setAnanlyzeStatus(ananlyzeStatus);
        this.setSamplingStatus(samplingStatus);
        this.setInnerReceiveStatus(innerReceiveStatus);
    }


    /**
     * 这个构造函数主要用到SampleRepository下的findCopySampleByProjectId方法，主要是保证复制样品得到必要的字段
     *
     * @param id                  主键id
     * @param sampleFolderId      点位id
     * @param samplingFrequencyId 采样频次id
     * @param redAnalyzeItems     分析项目
     */
    public DtoSample(String id, String sampleFolderId, String sampleTypeId,
                     String samplingFrequencyId, String redAnalyzeItems,
                     String code,
                     String redFolderName,
                     Integer cycleOrder,
                     Integer timesOrder,
                     Integer sampleOrder,
                     Integer sampleCategory,
                     String inspectedEnt,
                     String inspectedEntId,
                     String lat,
                     String lon) {
        this.setId(id);
        this.setSampleTypeId(sampleTypeId);
        this.setSampleFolderId(sampleFolderId);
        this.setSamplingFrequencyId(samplingFrequencyId);
        this.setRedAnalyzeItems(redAnalyzeItems);
        this.setCode(code);
        this.setRedFolderName(redFolderName);
        this.setCycleOrder(cycleOrder);
        this.setTimesOrder(timesOrder);
        this.setSampleOrder(sampleOrder);
        this.setSampleCategory(sampleCategory);
        this.setInspectedEnt(inspectedEnt);
        this.setInspectedEntId(inspectedEntId);
        this.setLat(lat);
        this.setLon(lon);
    }

    /**
     * 用于留样处置样品查询
     *
     * @param id           样品id
     * @param sampleCode   样品编号
     * @param sampleType   样品类型
     * @param folderName   点位名称
     * @param samplingTime 采样时间
     * @param projectName  项目名称
     * @param projectCode  项目编号
     * @param inspectedEnt 受检单位
     * @param sampleStatus 样品状态
     */
    public DtoSample(String id, String sampleCode, String sampleType,
                     String folderName, Date samplingTime, String projectName,
                     String projectCode, String inspectedEnt, String sampleStatus) {
        this.setId(id);
        this.setCode(sampleCode);
        this.setRedFolderName(folderName);
        this.setSamplingTimeBegin(samplingTime);
        this.setProjectName(projectName);
        this.setProjectCode(projectCode);
        this.setInspectedEnt(inspectedEnt);
        this.setStatus(sampleStatus);
        this.setSampleTypeId(sampleType);
    }

    /**
     * 用于样品管理的查询
     * @param id 编号
     * @param sampleCode 样品编号
     * @param redFolderName 点位名称
     * @param sampleTypeId 检测类型
     * @param projectId 项目id
     * @param associateSampleId 原样id
     * @param status 样品状态
     * @param receiveId 送样单id
     * @param samplingTimeBegin 采样日期
     */
    public DtoSample(String id, String sampleCode, String redFolderName,
                     String sampleTypeId, String projectId,String associateSampleId,
                     String status,String receiveId,
                     Date samplingTimeBegin) {
        this.setId(id);
        this.setCode(sampleCode);
        this.setRedFolderName(redFolderName);
        this.setSampleTypeId(sampleTypeId);
        this.setProjectId(projectId);
        this.setAssociateSampleId(associateSampleId);
        this.setStatus(status);
        this.setReceiveId(receiveId);
        this.setSamplingTimeBegin(samplingTimeBegin);
    }

    /**
     * 用于SamplePreparationServiceImpl样品制备查询样品列表
     *
     * @param id               样品id
     * @param sampleCode       样品编号
     * @param sampleTypeId     样品类型id
     * @param sampleFolderId   点位id
     * @param status           样品状态
     * @param receiveId        送样单id
     * @param sampleTimeBegin  采样日期
     * @param preparedStatus   制备状态
     * @param redFolderName   点位名称
     */
    public DtoSample(String id, String sampleCode, String sampleTypeId,
                     String sampleFolderId, String status, String receiveId,
                     Date sampleTimeBegin, Integer preparedStatus,String redFolderName,Date receiveSampleDate) {
        this.setId(id);
        this.setCode(sampleCode);
        this.setSampleTypeId(sampleTypeId);
        this.setSampleFolderId(sampleFolderId);
        this.setStatus(status);
        this.setReceiveId(receiveId);
        this.setSamplingTimeBegin(sampleTimeBegin);
        this.setPreparedStatus(preparedStatus);
        this.setRedFolderName(redFolderName);
        this.setSendTime(receiveSampleDate);
    }

    /**
     * 分组后分析项目名称汇总
     */
    @Transient
    private String analyseItemNames;

    /**
     * 样品分组类型id
     */
    @Transient
    private String sampleTypeGroupId;

    /**
     * 分组标识
     */
    @Transient
    private Integer isGroup;

    /**
     * 分组表主键id
     */
    @Transient
    private String sampleGroupId;

    /**
     *  冗余分析项目（包含分析分包）
     */
    @Transient
    private String analyzeItemContainOut;




    /**
     * 用于样品管理分组后的查询
     *
     * @param id                编号
     * @param sampleCode        样品编号
     * @param redFolderName     点位名称
     * @param sampleTypeId      检测类型
     * @param projectId         项目id
     * @param associateSampleId 原样id
     * @param status            样品状态
     * @param receiveId         送样单id
     * @param samplingTimeBegin 采样日期
     * @param analyseItemNames  分组后的分析项目结果
     * @param isGroup           分组标识
     */
    public DtoSample(String id, String sampleCode, String redFolderName,
                     String sampleTypeId, String projectId, String associateSampleId,
                     String status, String receiveId, Date samplingTimeBegin,
                     String analyseItemNames, String sampleTypeGroupId, Integer isGroup, String sampleGroupId,String qcId) {
        this.setId(id);
        this.setCode(sampleCode);
        this.setRedFolderName(redFolderName);
        this.setSampleTypeId(sampleTypeId);
        this.setProjectId(projectId);
        this.setAssociateSampleId(associateSampleId);
        this.setStatus(status);
        this.setReceiveId(receiveId);
        this.setSamplingTimeBegin(samplingTimeBegin);
        this.setAnalyseItemNames(analyseItemNames);
        this.setSampleTypeGroupId(sampleTypeGroupId);
        this.setIsGroup(isGroup);
        this.setSampleGroupId(sampleGroupId);
        this.setQcId(qcId);
    }
}