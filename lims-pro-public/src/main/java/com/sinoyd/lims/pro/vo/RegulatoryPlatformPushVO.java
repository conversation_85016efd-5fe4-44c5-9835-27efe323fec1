package com.sinoyd.lims.pro.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 上海监管平台推送参数VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/29
 */
@Data
public class RegulatoryPlatformPushVO {

    /**
     * 业务数据id
     */
    private String id;

    /**
     * 监管平台执行类型{@link com.sinoyd.lims.pro.enums.EnumPRO.EnumRegulatoryPlatformMethod}
     */
    private Integer rpType;

    /**
     * 报告附件id（报告推送用）
     */
    private String documentId;

    /**
     * 采样计划点位id以及采样时间id（采样计划采样时间推送用）
     */
    private List<Map<String, String>> samplingPlan;

    /**
     * 点位id（方案推送）
     */
    private List<String> sampleFolderIds;
}
