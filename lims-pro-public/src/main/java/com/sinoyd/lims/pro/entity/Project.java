package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Project实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Project")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Project extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  Project() {
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 父级项目id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父级项目id")
	private String parentId;

    /**
    * 流水编号
    */
    @Column(length=50)
    @ApiModelProperty("流水编号")
    @Length(message = "流水编号{validation.message.length}", max = 50)
	private String projectCode;

    /**
    * 项目类型id（外键）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目类型id（外键）")
	private String projectTypeId;

    /**
    * 项目名称
    */
    @Column(length=100)
    @ApiModelProperty("项目名称")
    @Length(message = "项目名称{validation.message.length}", max = 100)
	private String projectName;

    /**
    * 项目状态(存枚举字符串EnumProjectStatus)
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目状态(存枚举字符串EnumProjectStatus)")
    //@NotBlank(message = "项目状态(存枚举字符串EnumProjectStatus){validation.message.blank}")
    @Length(message = "项目状态(存枚举字符串EnumProjectStatus){validation.message.length}", max = 50)
	private String status;

    /**
    * 委托现场送样状态（EnumSamplingStatus 1.未采毕 2.已采毕）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("委托现场送样状态（EnumSamplingStatus 1.未采毕 2.已采毕）")
    private Integer samplingStatus;

    /**
    * 报告流程状态（EnumReportStatus： 0.未完成，1.已完成）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("报告流程状态（EnumReportStatus： 0.未完成，1.已完成）")
	private Integer reportStatus;

    /**
     * 是否协同任务
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否协同任务")
    private Boolean isAssist=false;

    /**
     * 考核内容
     */
    @Column(length=2000)
    @ApiModelProperty("考核内容")
    private String testContent;

    /**
     * 考核要求
     */
    @Column(length=2000)
    @ApiModelProperty("考核要求")
    private String testRequirement;

    /**
    * 项目登记时间(登记时间不能改)
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("项目登记时间(登记时间不能改)")
	private Date inputTime;

    /**
    * 登记人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("登记人id")
	private String inceptPersonId;

    /**
    * 委托时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("委托时间")
	private Date inceptTime;

    /**
    * 项目最后修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("项目最后修改时间")
	private Date lastModifyTime;

    /**
    * 预计收费
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("预计收费")
	private BigDecimal expectedCharge;

    /**
    * 实际收费
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("实际收费")
	private BigDecimal actualCharges;

    /**
    * 是否着重关注
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否着重关注")
	private Boolean isStress;

    /**
    * 项目等级(EnumProjectGrade：0.一般 1.紧急 2.特急)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("项目等级(EnumProjectGrade：0.一般 1.紧急 2.特急)")
	private Integer grade;

    /**
    * 委托单位id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("委托单位id")
	private String customerId;

    /**
    * 委托单位
    */
    @Column(length=100)
    @ApiModelProperty("委托单位")
    @Length(message = "委托单位{validation.message.length}", max = 1000)
	private String customerName;

    /**
    * 受检单位Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("受检单位Id")
	private String inspectedEntId;

    /**
    * 受检单位
    */
    @Column(length=100)
    @ApiModelProperty("受检单位")
    @Length(message = "受检单位{validation.message.length}", max = 100)
	private String inspectedEnt;

    /**
     * 受检方联系人
     */
    @Column(length=50)
    @ApiModelProperty("受检方联系人")
    @Length(message = "受检方联系人{validation.message.length}", max = 50)
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    @Column(length=50)
    @ApiModelProperty("受检方联系电话")
    @Length(message = "受检方联系电话{validation.message.length}", max = 50)
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    @Column(length=100)
    @ApiModelProperty("受检方地址")
    @Length(message = "受检方地址{validation.message.length}", max = 100)
    private String inspectedAddress;

    /**
    * 法人代表
    */
    @Column(length=50)
    @ApiModelProperty("法人代表")
    @Length(message = "法人代表{validation.message.length}", max = 50)
	private String customerOwner;

    /**
    * 地址
    */
    @Column(length=100)
    @ApiModelProperty("地址")
    @Length(message = "地址{validation.message.length}", max = 100)
	private String customerAddress;

    /**
    * 联系人
    */
    @Column(length=50)
    @ApiModelProperty("联系人")
    @Length(message = "联系人{validation.message.length}", max = 50)
	private String linkMan;

    /**
    * 电话
    */
    @Column(length=50)
    @ApiModelProperty("电话")
    @Length(message = "电话{validation.message.length}", max = 50)
	private String linkPhone;

    /**
    * 电子邮件
    */
    @Column(length=50)
    @ApiModelProperty("电子邮件")
    @Length(message = "电子邮件{validation.message.length}", max = 50)
	private String linkEmail;

    /**
    * 传真
    */
    @Column(length=50)
    @ApiModelProperty("传真")
    @Length(message = "传真{validation.message.length}", max = 50)
	private String linkFax;

    /**
    * 邮编
    */
    @Column(length=10)
    @ApiModelProperty("邮编")
    @Length(message = "邮编{validation.message.length}", max = 10)
	private String zipCode;

    /**
    * 假删字段
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
	private Boolean isDeleted=false;

    /**
    * 监测目的
    */
    @ApiModelProperty("监测目的")
    @Length(message = "监测目的{validation.message.length}", max = 255)
	private String monitorPurp;

    /**
    * 监测方式
    */
    @ApiModelProperty("监测方式")
    @Length(message = "监测方式{validation.message.length}", max = 255)
    private String monitorMethods;

    /**
     * 项目金额
     */
    @ApiModelProperty("项目金额")
    @Length(message = "项目金额{validation.message.length}", max = 50)
    private String amount;

    /**
    * 客户委托内容
    */
    @Column(length=1000)
    @ApiModelProperty("客户委托内容")
    @Length(message = "客户委托内容{validation.message.length}", max = 1000)
	private String customerRequired;

    /**
    * 样品类型
    */
    @Column(length=1000)
    @ApiModelProperty("样品类型")
    @Length(message = "样品类型{validation.message.length}", max = 1000)
	private String sampleType;

    /**
    * 发送方式(枚举EnumPostMethod：0.未设置 1.自取 2.挂号邮寄 3.特快专递)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("发送方式(枚举EnumPostMethod：0.未设置 1.自取 2.挂号邮寄 3.特快专递)")
	private Integer postMethod;

    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;

    /**
    * 质控信息
    */
    @ApiModelProperty("质控信息")
    @Length(message = "质控信息{validation.message.length}", max = 255)
	private String qcInfo;

    /**
    * 监督信息
    */
    @ApiModelProperty("监督信息")
    @Length(message = "监督信息{validation.message.length}", max = 255)
	private String controlInfo;

    /**
    * 项目地址
    */
    @Column(length=100)
    @ApiModelProperty("项目地址")
    @Length(message = "项目地址{validation.message.length}", max = 100)
	private String projectAddress;

    /**
    * 项目批准机关及文号
    */
    @Column(length=50)
    @ApiModelProperty("项目批准机关及文号")
    @Length(message = "项目批准机关及文号{validation.message.length}", max = 50)
	private String docNumber;

    /**
    * 建设竣工日期
    */
    @Column(length=10)
    @ApiModelProperty("建设竣工日期")
    @Length(message = "建设竣工日期{validation.message.length}", max = 10)
	private String compDate;

    /**
    * 投资总额
    */
    @Column(length=20)
    @ApiModelProperty("投资总额")
    @Length(message = "投资总额{validation.message.length}", max = 20)
	private String invAmount;

    /**
    * 监测方法(0:无,1.按现行国标行标执行2.客户指定方法)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("监测方法(0:无,1.按现行国标行标执行2.客户指定方法)")
	private Integer analyzeMethod;

    /**
    * 检验结果告知方式(1.出具正规报告2.电话告知)
    */
    @ApiModelProperty("检验结果告知方式(1.出具正规报告2.电话告知)")
    @Length(message = "检验结果告知方式(1.出具正规报告2.电话告知){validation.message.length}", max = 255)
	private String reportMethod;

    /**
    * 保存条件
    */
    @ApiModelProperty("保存条件")
    @Length(message = "保存条件{validation.message.length}", max = 255)
	private String saveCondition;

    /**
    * 保存期限
    */
    @ApiModelProperty("保存期限")
    @Length(message = "保存期限{validation.message.length}", max = 255)
	private String saveDate;

    /**
     * 环境院编号
     */
    @Column(length=50)
    @ApiModelProperty("环境院编号")
    @Length(message = "环境院编号{validation.message.length}", max = 50)
    private String environmentCode;

    /**
     * 任务所在区名称
     */
    @Column(length=300)
    @ApiModelProperty("任务所在区名称")
    @Length(message = "任务所在区名称{validation.message.length}", max = 300)
    private String addressName;

    /**
     * 是否加盖CMA章
     */
    @Column
    @ApiModelProperty("是否加盖CMA章")
    private Integer isCMA;

    /**
     * 报告类型
     */
    @Column(length=50)
    @ApiModelProperty("报告类型")
    @Length(message = "报告类型{validation.message.length}", max = 50)
    private String regulateReportType;

    /**
     * 监管平台客户污染源编号
     */
    @Column(length=50)
    @ApiModelProperty("监管平台客户污染源编号")
    @Length(message = "污染源编号{validation.message.length}", max = 50)
    private String pollutionCode;

    /**
    * 检测报告数
    */
    @ApiModelProperty("检测报告数")
    @Length(message = "检测报告数{validation.message.length}", max = 255)
	private String reportNum;

    /**
    * 是否网上登记(0.否 1. 是)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否网上登记(0.否 1. 是)")
	private Integer isOnline;

    /**
    * 推送状态(0：不发布，1：已发布，2：已办结）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("推送状态(0：不发布，1：已发布，2：已办结）")
	private Integer pushStatus;

     /**
      * 报告盖章  监测专用章、CMA章、CNAS章（多选）
      */
     @Column(length = 50)
     @Length(message = "报告盖章{validation.message.length}", max = 50)
     private String reportStamp;

     /**
      * 监测方法  由服务方决定、由委托方决定
      */
     private Integer monitorMethod;

     /**
      * 监测方法说明
      */
     @Column(length = 500)
     @Length(message = "监测方法说明{validation.message.length}", max = 500)
     private String monitorMethodRemark;

     /**
      * 是否资质认定：是，否'
      */
     private Boolean isAccredited;

     /**
      * 资质认定说明
      */
     @Column(length = 500)
     @Length(message = "资质认定说明{validation.message.length}", max = 500)
     private String isAccreditedRemark;

    /**
     * 订单id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("订单id")
    private String orderId;

    /**
    * 二维码图片存储地址
    */
    @Column(length=500)
    @ApiModelProperty("二维码图片存储地址")
    @Length(message = "二维码图片存储地址{validation.message.length}", max = 500)
	private String qrCodeUrl;

    /**
    * 国检_样品数量（出具在报告中）
    */
    @Column(length=10)
    @ApiModelProperty("国检_样品数量（出具在报告中）")
    @Length(message = "国检_样品数量（出具在报告中）{validation.message.length}", max = 10)
	private String sampleQuantity;

    /**
    * 国检_是否合格(样品是否含有不合格项)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("国检_是否合格(样品是否含有不合格项)")
	private Boolean isQualified;

    /**
    * 国检_样品描述
    */
    @ApiModelProperty("国检_样品描述")
    @Length(message = "国检_样品描述{validation.message.length}", max = 255)
	private String sampleDescription;

    /**
    * 国检_委托品名
    */
    @Column(length=100)
    @ApiModelProperty("国检_委托品名")
    @Length(message = "国检_委托品名{validation.message.length}", max = 100)
	private String sampleNameCustomer;

    /**
    * 国检_样品贸易类型（出口/进口/内贸）
    */
    @Column(length=50)
    @ApiModelProperty("国检_样品贸易类型（出口/进口/内贸）")
    @Length(message = "国检_样品贸易类型（出口/进口/内贸）{validation.message.length}", max = 50)
	private String samKind;

    /**
    * 国检_贸易区代码
    */
    @Column(length=50)
    @ApiModelProperty("国检_贸易区代码")
    @Length(message = "国检_贸易区代码{validation.message.length}", max = 50)
	private String tradeAreaCode;

    /**
    * 国检_批号
    */
    @Column(length=20)
    @ApiModelProperty("国检_批号")
    @Length(message = "国检_批号{validation.message.length}", max = 20)
	private String batchCode;

    /**
    * 国检_客户编号（报验号）
    */
    @Column(length=20)
    @ApiModelProperty("国检_客户编号（报验号）")
    @Length(message = "国检_客户编号（报验号）{validation.message.length}", max = 20)
	private String testCode;

    /**
    * 国检_生产厂家
    */
    @Column(length=100)
    @ApiModelProperty("国检_生产厂家")
    @Length(message = "国检_生产厂家{validation.message.length}", max = 100)
	private String prodCompany;

    /**
    * 国检_产地
    */
    @Column(length=100)
    @ApiModelProperty("国检_产地")
    @Length(message = "国检_产地{validation.message.length}", max = 100)
	private String compAddress;

    /**
     * 送样人
     */
    @Column(length=50)
    @ApiModelProperty("送样人")
    @Length(message = "送样人{validation.message.length}", max = 50)
    private String sendSamplePerson;

    /**
    * 预留int类型1
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("预留int类型1")
	private Integer extendInt1;

    /**
    * 预留int类型2
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("预留int类型2")
	private Integer extendInt2;

    /**
    * 预留int类型3
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("预留int类型3")
	private Integer extendInt3;

    /**
    * 预留string类型1
    */
    @ApiModelProperty("预留string类型1")
    @Length(message = "预留string类型1{validation.message.length}", max = 255)
	private String extendStr1;

    /**
    * 预留string类型2
    */
    @ApiModelProperty("预留string类型2")
    @Length(message = "预留string类型2{validation.message.length}", max = 255)
	private String extendStr2;

    /**
    * 预留string类型3
    */
    @ApiModelProperty("预留string类型3")
    @Length(message = "预留string类型3{validation.message.length}", max = 255)
	private String extendStr3;

    /**
    * 预留Guid类型1
    */
    @Column(length=50)
    @ApiModelProperty("预留Guid类型1")
    @Length(message = "预留Guid类型1{validation.message.length}", max = 50)
	private String extendGuid1;

    /**
    * 预留Guid类型2
    */
    @Column(length=50)
    @ApiModelProperty("预留Guid类型2")
    @Length(message = "预留Guid类型2{validation.message.length}", max = 50)
	private String extendGuid2;

    /**
    * 预留Guid类型3
    */
    @Column(length=50)
    @ApiModelProperty("预留Guid类型3")
    @Length(message = "预留Guid类型3{validation.message.length}", max = 50)
	private String extendGuid3;

    /**
    * 预留Date类型1
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("预留Date类型1")
	private Date extendDate1;

    /**
    * 预留Date类型2
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("预留Date类型2")
	private Date extendDate2;

    /**
    * 预留Date类型3
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("预留Date类型3")
	private Date extendDate3;

    /**
     * * 冗余json信息
     */
    @Column(length = 4000)
    @ApiModelProperty("冗余json信息")
    private String json;

    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否多企业")
    private Boolean isMultiEnterprise;


    /**
     * 其他说明
     */
    @Column(length = 255)
    @ApiModelProperty("其他说明")
    @Length(message = "其他说明{validation.message.length}", max = 255)
    private String otherRemark;


    /**
     * 协调内容
     */
    @Column(length = 255)
    @ApiModelProperty("协调内容")
    @Length(message = "协调内容{validation.message.length}", max = 255)
    private String coordinate;


    /**
     * 保存方式：符合规范、不符合规范、不确定（单选）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("保存方式：符合规范、不符合规范、不确定（单选）")
    private Integer sampleSaveMethod;

    /**
     * 保存容器：正确、不正确、不确定（单选）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("保存容器：正确、不正确、不确定（单选）")
    private Integer isSaveContainer;

    /**
     * 样品数量：正确、不正确、不确定（单选）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("样品数量：正确、不正确、不确定（单选）")
    private Integer isSampleCountRight;

    /**
     * 样品有效期：在有效期内、不在有效期内、不确定（单选）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("样品有效期：在有效期内、不在有效期内、不确定（单选）")
    private Integer sampleValidityPeriod;

    /**
     * 样品确认其他意见
     */
    @Column(length = 500)
    @ApiModelProperty("样品确认其他意见")
    @Length(message = "样品确认其他意见{validation.message.length}", max = 500)
    private String sampleConfirmRemark;


    /**
     * 采样地址
     */
    @Column(length = 300)
    @ApiModelProperty("采样地址")
    @Length(message = "采样地址{validation.message.length}", max = 300)
    private String samplingAddress;


    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;

    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;

    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;

    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;

    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;

 }