package com.sinoyd.lims.pro.vo;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoReport;
import lombok.Data;

/**
 * 报告推送列表VO
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2025/06/27
 */
@Data
public class RPPushReportListVO {

    /**
     * 主键id（报告id）
     */
    private String id;

    /**
     * 归档报告文档id
     */
    private String archiveReportDocId;

    /**
     * 文件后缀
     */
    private String docSuffix;

    /**
     * 文件大小
     */
    private Integer docSize;

    /**
     * 文件类型编码
     */
    private String docTypeId;

    /**
     * 文件类型名称
     */
    private String docTypeName;

    /**
     * 文件名称
     */
    private String filename;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 推送状态
     */
    private Boolean isPush = false;

    /**
     * 文件类型名称
     */
    private String reportCode;

    /**
     * 报告类型名称
     */
    private String reportType;

    /**
     * 报告年份
     */
    private String reportYear;

    /**
     * 报告编制人
     */
    private String senderName;

    /**
     * 报告文档备注，用于判断推送状态
     */
    private String remark;

    public RPPushReportListVO(DtoReport report) {
        this.id = report.getId();
        this.reportCode = report.getCode();
        this.reportYear = report.getReportYear();
    }


    /**
     * 填充归档报告文档数据
     *
     * @param archiveReportDoc 归档报告文档
     */
    public void fillArchiveReportDoc(DtoDocument archiveReportDoc) {
        this.archiveReportDocId = archiveReportDoc.getId();
        this.docSize = archiveReportDoc.getDocSize();
        this.docSuffix = archiveReportDoc.getDocSuffix();
        this.docTypeId = archiveReportDoc.getDocTypeId();
        this.docTypeName = archiveReportDoc.getDocTypeName();
        this.filename = archiveReportDoc.getFilename();
        this.path = archiveReportDoc.getPath();
        this.remark = archiveReportDoc.getRemark();
        this.isPush = StringUtil.isNotEmpty(archiveReportDoc.getRemark()) && "已经推送".equals(archiveReportDoc.getRemark());
    }
}
