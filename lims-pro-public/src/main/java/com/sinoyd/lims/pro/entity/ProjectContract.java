package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;

/**
 * 项目和推送与信息管理实体
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/11/21
 **/

@MappedSuperclass
@ApiModel(description="ProjectContract")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ProjectContract {

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 合同id
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("合同id")
    private String contractId;

    /**
     * 监管平台甲方企业名称
     */
    @Column(nullable = false)
    @ApiModelProperty("监管平台甲方企业名称")
    private String shanghaiEntName;

    /**
     * 项目id
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 合同名称
     */
    @Column(length=50)
    @ApiModelProperty("合同名称")
    private String contractName;

    /**
     * 任务金额
     */
    @Column(length=18)
    @ApiModelProperty("任务金额")
    private BigDecimal taskPrice;

    /**
     * 任务类型id
     */
    @Column
    @ApiModelProperty("任务类型id")
    private String taskTypeId;

    /**
     * 任务类型
     */
    @Column(length=50)
    @ApiModelProperty("任务类型")
    private String taskType;

    /**
     * 是否采样id
     */
    @Column
    @ApiModelProperty("是否采样id")
    private String isSampleId;

    /**
     * 是否采样
     */
    @Column(length=20)
    @ApiModelProperty("是否采样")
    private String isSample;

    /**
     * 任务来源id
     */
    @Column
    @ApiModelProperty("任务来源id")
    private String taskSourceId;

    /**
     * 任务来源
     */
    @Column(length=20)
    @ApiModelProperty("任务来源")
    private String taskSource;

    /**
     * 任务所在地
     */
    @Column(length=20)
    @ApiModelProperty("任务所在地")
    private String taskLocation;

    /**
     * 采样联系人
     */
    @Column(length=50)
    @ApiModelProperty("采样联系人")
    private String sampleContact;

    /**
     * 联系电话
     */
    @Column(length=20)
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 任务地址
     */
    @Column(length=50)
    @ApiModelProperty("任务地址")
    private String taskAddress;

    /**
     * 附件说明
     */
    @Column(length=50)
    @ApiModelProperty("附件说明")
    private String fileExplain;

    /**
     * 任务概述
     */
    @ApiModelProperty("任务概述")
    private String taskContent;

    /**
     * 被测对象
     */
    @ApiModelProperty("被测对象")
    private String testTarget;

    /**
     * 是否推送（0：否，1：是）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("是否推送")
    private Integer isPush = 0;

    /**
     * 是否已经推送（0：否，1：是）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("是否已经推送")
    private Integer hasPush = 0;

    /**
     * 项目推送后获得的上海监测站项目id
     */
    @Column(length=50)
    @ApiModelProperty("项目推送后获得的上海监测站项目id")
    private String pId;

    /**
     * 是否处理（0：否，1：是）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("是否处理")
    private Integer isHandle = 0;

    /**
     * 方案是否已经推送（0：否，1：是）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("方案是否已经推送")
    private Integer schemeHasPush = 0;

    /**
     * 计划是否已经推送（0：否，1：是）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("计划是否已经推送")
    private Integer planHasPush = 0;

    /**
     * 报告是否已经推送（0：否，1：是）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("报告是否已经推送")
    private Integer reportHasPush = 0;

    /**
     * 推送采样类型与任务类型是否匹配
     */
    @Column
    @ColumnDefault("b'1'")
    @ApiModelProperty("推送采样类型与任务类型是否匹配")
    private Boolean isShProjectTypeMatch = true;


}
