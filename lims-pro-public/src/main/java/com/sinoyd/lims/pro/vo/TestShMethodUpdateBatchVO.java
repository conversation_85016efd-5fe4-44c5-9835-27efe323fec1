package com.sinoyd.lims.pro.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量更新测试项目关联的监管平台分析方法vo
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/17
 **/
@Data
public class TestShMethodUpdateBatchVO {

    /**
     * 当前更新的点位id
     */
    private String sampleFolderId;

    /**
     * 上海监管平台分析方法id
     */
    private String shMethodId;

    /**
     * 上海监管平台分析方法名称
     */
    private String shMethodName;

    /**
     * 上海监管平台采样方法id
     */
    private String shSamplingMethodId;

    /**
     * 上海监管平台采样方法名称
     */
    private String shSamplingMethodName;

    /**
     * 需要更新的测试项目id集合
     */
    private List<String> testIds = new ArrayList<>();

}
