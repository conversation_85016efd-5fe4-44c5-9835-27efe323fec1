package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;


/**
 * 监管平台委托方信息数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPEntrustingUnitInfoVO {

    /**
     * 企业id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    private String id;

    /**
     * 企业名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "QYMC")
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XYDM")
    private String socialCreditCode;


    /**
     * 营业执照编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "YYZZBH")
    private String businessLicenseCode;

    /**
     * 企业法人
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FRDB")
    private String legalPerson;

    /**
     * 污染源编号
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "WRYBH")
    private String pollutionSourceCode;
}
