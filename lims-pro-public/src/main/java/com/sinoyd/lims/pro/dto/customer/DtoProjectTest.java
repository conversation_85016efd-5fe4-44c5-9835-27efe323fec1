package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 项目指标
 * <AUTHOR>
 * @version V1.0.0 2019/11/23
 * @since V100R001
 */
@Data
public class DtoProjectTest {

    public DtoProjectTest() {

    }

    public DtoProjectTest(DtoTest test) {
        this.testId = test.getId();
        this.analyseItemId = test.getAnalyzeItemId();
        this.analyzeMethodId = test.getAnalyzeMethodId();
        this.redAnalyzeItemName = test.getRedAnalyzeItemName();
        this.redAnalyzeMethodName = test.getRedAnalyzeMethodName();
        this.redCountryStandard = test.getRedCountryStandard();
        this.orderNum = test.getOrderNum();
        this.isCompleteField = test.getIsCompleteField();
        this.bigSampleTypeId = test.getSampleTypeId();
        this.testCert = test.getCert();
    }

    public DtoProjectTest(DtoSamplingFrequencyTest sft) {
        this.testId = sft.getTestId();
        this.analyseItemId = sft.getAnalyseItemId();
        this.analyzeMethodId = sft.getAnalyzeMethodId();
        this.redAnalyzeItemName = sft.getRedAnalyzeItemName();
        this.redAnalyzeMethodName = sft.getRedAnalyzeMethodName();
        this.redCountryStandard = sft.getRedCountryStandard();
        this.isOutsourcing = sft.getIsOutsourcing();
        this.isSamplingOut = sft.getIsSamplingOut();
        this.isCompleteField = sft.getIsCompleteField();
    }

    @JsonIgnore
    public Map<String,Object>getMapInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("redAnalyzeItemName", this.redAnalyzeItemName);
        map.put("testId", this.testId);
        return map;
    }

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 检测大类id
     */
    private String bigSampleTypeId;

    /**
     * 检测小类id
     */
    private String sampleTypeId;

    /**
     * 检测类型小类排序值
     */
    private Integer sampleTypeOrderNum;

    /**
     * 检测小类名称
     */
    private String sampleTypeName;

    /**
     * 测试id
     */
    private String testId;

    /**
     * 分析项目Id
     */
    private String analyseItemId;

    /**
     * 分析方法Id
     */
    private String analyzeMethodId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    private String redCountryStandard;

    /**
     * 是否分包
     */
    private Boolean isOutsourcing;

    /**
     * 是否分包
     */
    private Boolean isSamplingOut;
    /**
     * 是否现场数据
     */
    private Boolean isCompleteField;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 样品个数
     */
    private Integer sampleCount;

    /**
     * 测试项目检测资质
     */
    private Integer testCert;

    /**
     * 采样方法
     */
    private String samplingMethodName;
}
