package com.sinoyd.lims.pro.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 分析数据详情VO
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
@Data
@Accessors(chain = true)
public class AnalyzeDetailDataVO {

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 数据状态
     */
    private String dataStatus;

    /**
     * 采样日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date samplingDate;

    /**
     * 接样日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date receiveDate;

    /**
     * 分析日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date analyzeDate;

    /**
     * 要求完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date requiredCompleteDate;

    /**
     * 分析人员
     */
    private String analystName;

    /**
     * 分析时长
     */
    private Integer analyseDayLen;

    /**
     * 送样单id
     */
    private String receiveId;

    public AnalyzeDetailDataVO(String sampleTypeId, String sampleTypeName, String sampleCode, String analyzeItemName, String dataStatus, Date samplingDate, Date analyzeDate, String analystName, Integer analyseDayLen, String receiveId) {
        this.sampleTypeId = sampleTypeId;
        this.sampleTypeName = sampleTypeName;
        this.sampleCode = sampleCode;
        this.analyzeItemName = analyzeItemName;
        this.dataStatus = dataStatus;
        this.samplingDate = samplingDate;
        this.analyzeDate = analyzeDate;
        this.analystName = analystName;
        this.analyseDayLen = analyseDayLen;
        this.receiveId = receiveId;
    }


    public AnalyzeDetailDataVO(String sampleTypeId, String sampleTypeName, String sampleCode, String analyzeItemName, String dataStatus, Date samplingDate,
                               Date analyzeDate, String analystName, Integer analyseDayLen, String receiveId, Date receiveDate) {
        this(sampleTypeId, sampleTypeName, sampleCode, analyzeItemName, dataStatus, samplingDate, analyzeDate, analystName, analyseDayLen, receiveId);
        this.receiveDate = receiveDate;
    }
}