package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderItemVo;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.entity.SampleFolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DtoSampleFolder实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleFolder")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSampleFolder extends SampleFolder {
    private static final long serialVersionUID = 1L;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 分析项目id集合
     */
    @Transient
    private List<String> analyseItemIds = new ArrayList<>();

    /**
     * 分析项目集合
     */
    @Transient
    private List<DtoSampleFolderItemVo> itemVos = new ArrayList<>();

    /**
     * 测试项目ids
     */
    @Transient
    private List<String> testIds = new ArrayList<>();

    /**
     * 周期
     */
    @Transient
    private Integer periodCount;

    /**
     * 次数
     */
    @Transient
    private Integer timePerPeriod;

    /**
     * 样品数
     */
    @Transient
    private Integer sampleOrder;

    /**
     * 送样单id
     */
    @Transient
    private String receiveId;

    /**
     * 采样日期
     */
    @Transient
    private Date samplingTimeBegin;

    /**
     * 点位参数/样品参数/分析项目参数
     */
    @Transient
    private List<DtoParamsConfig> folderParamList;

    /**
     * 样品关联数据
     */
    @Transient
    private List<Map<String, Object>> rowMapList;

    /**
     * 监管平台测试项目匹配状态 枚举{@link com.sinoyd.lims.lim.enums.EnumLIM.EnumShTestMatchStatus}
     */
    @Transient
    private Integer shTestMatchStatus;

    /**
     * 提供默认的构造函数
     */
    public DtoSampleFolder() {

    }
}