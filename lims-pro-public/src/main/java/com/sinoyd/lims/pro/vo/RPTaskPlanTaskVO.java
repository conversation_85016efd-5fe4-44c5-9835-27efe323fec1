package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 * 上海监管平台任务方案编制：方案任务项实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Item")
@XmlType(name = "PlanTaskItem")
public class RPTaskPlanTaskVO {


    /**
     * 任务id
     */
    @XmlElement(name = "ID")
    private String id;

    /**
     * 方案id
     */
    @XmlElement(name = "MTSID")
    private String schemeId;

    /**
     * 周期
     */
    @XmlElement(name = "ZQ")
    private String periodCount;

    /**
     * 批次
     */
    @XmlElement(name = "PC")
    private String timePerPeriod;

    public RPTaskPlanTaskVO() {
    }

    public RPTaskPlanTaskVO(String pushId, String schemeId, String periodCount, String timePerPeriod) {
        this.id = pushId;
        this.schemeId = schemeId;
        this.periodCount = periodCount;
        this.timePerPeriod = timePerPeriod;
    }
}
