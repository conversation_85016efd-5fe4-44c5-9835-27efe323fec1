package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleGroup;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * DtoSampleGroup实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleGroup")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSampleGroup extends SampleGroup {
    private static final long serialVersionUID = 1L;

    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 点位名称
     */
    @Transient
    private String redFolderName;

    /**
     * 采样开始时间
     */
    @Transient
    private Date samplingBeginTime;

    /**
     * 样品类型
     */
    @Transient
    private String sampleTypeName;

    /**
     * 样品类型id
     */
    @Transient
    private String sampleTypeId;

    /**
     * 样品是否删除
     */
    @Transient
    private Boolean isSamDeleted;

    /**
     * key 分析数据id，value 测试项目id
     */
    @Transient
    private Map<String, String> analDataMap;

    /**
     * 样品分组主键id
     */
    @Transient
    private String sampleGroupId;

    /**
     * 项目id
     */
    @Transient
    private String projectId;

    /**
     * 样品状态
     */
    @Transient
    private String status;

    /**
     * 质控数据id
     */
    @Transient
    private String qcId;

    /**
     * 质控类型
     */
    @Transient
    private Integer qcGrade;

    /**
     * 质控等级
     */
    @Transient
    private Integer qcType;

    /**
     * 接样日期
     */
    @Transient
    private Date sendTime;

    /**
     * 样品处置状态
     */
    @Transient
    private String reserveStatus;

    /**
     * 样品留样状态
     */
    @Transient
    private String keepStatus;

    /**
     * 样品编号标记
     */
    @Transient
    private String sampleCodeTag;
    /**
     * 带分组标记的样品编号
     */
    @Transient
    private String sampleCodeWithTag;

    /**
     * 样品类型
     */
    @Transient
    private Integer sampleCategory;

    /**
     * 关联样id
     */
    @Transient
    private String associateSampleId;

    /**
     * 主键ids
     */
    @Transient
    private List<String> ids;

    /**
     * 主键ids
     */
    @Transient
    private List<String> testIds;


    public DtoSampleGroup() {

    }

    public DtoSampleGroup(String code, String redFolderName, Date samplingTimeBegin, String sampleTypeId,
                          String id, String receiveId, String sampleId, String sampleTypeGroupId,
                          String sampleTypeGroupName, String analyseItemNames, Boolean hasScanned,
                          Date scannedTime, String scanner, String orgId, String creator, Date createDate,
                          String domainId, String modifier, Date modifyDate) {
        this.setSampleCode(code);
        this.setRedFolderName(redFolderName);
        this.setSamplingBeginTime(samplingTimeBegin);
        this.setSampleTypeId(sampleTypeId);
        this.setId(id);
        this.setReceiveId(receiveId);
        this.setSampleId(sampleId);
        this.setSampleTypeGroupId(sampleTypeGroupId);
        this.setSampleTypeGroupName(sampleTypeGroupName);
        this.setAnalyseItemNames(analyseItemNames);
        this.setHasScanned(hasScanned);
        this.setScannedTime(scannedTime);
        this.setScanner(scanner);
        this.setOrgId(orgId);
        this.setCreator(creator);
        this.setCreateDate(createDate);
        this.setDomainId(domainId);
        this.setModifier(modifier);
        this.setModifyDate(modifyDate);
    }

    public DtoSampleGroup(String sampleGroupId, String sampleCode, String sampleId, String redFolderName,
                          String sampleTypeId, Date samplingTimeBegin, String projectId, String status,
                          String receiveId, String analyseItemNames, Integer reserveNums, Integer analyseNums,
                          String sampleTypeGroupId, Integer isGroup, String qcId) {
        setSampleGroupId(sampleGroupId);
        setSampleCode(sampleCode);
        setSampleId(sampleId);
        setRedFolderName(redFolderName);
        setSampleTypeId(sampleTypeId);
        setSamplingBeginTime(samplingTimeBegin);
        setProjectId(projectId);
        setStatus(status);
        setReceiveId(receiveId);
        setAnalyseItemNames(analyseItemNames);
        setReserveNums(reserveNums);
        setAnalyseNums(analyseNums);
        setSampleTypeGroupId(sampleTypeGroupId);
        setIsGroup(isGroup);
        setQcId(qcId);
    }

    public DtoSampleGroup(String code, String redFolderName, Date samplingTimeBegin,
                          String sampleTypeId, String id, String receiveId, String sampleId,
                          String sampleTypeGroupId, String sampleTypeGroupName, String analyseItemNames,
                          Boolean hasScanned, Date scannedTime, String scanner,
                          String orgId, String creator, Date createDate, String domainId,
                          String modifier, Date modifyDate, Integer sampleCategory, String associateSampleId) {
        this.setSampleCode(code);
        this.setRedFolderName(redFolderName);
        this.setSamplingBeginTime(samplingTimeBegin);
        this.setSampleTypeId(sampleTypeId);
        this.setId(id);
        this.setReceiveId(receiveId);
        this.setSampleId(sampleId);
        this.setSampleTypeGroupId(sampleTypeGroupId);
        this.setSampleTypeGroupName(sampleTypeGroupName);
        this.setAnalyseItemNames(analyseItemNames);
        this.setHasScanned(hasScanned);
        this.setScannedTime(scannedTime);
        this.setScanner(scanner);
        this.setOrgId(orgId);
        this.setCreator(creator);
        this.setCreateDate(createDate);
        this.setDomainId(domainId);
        this.setModifier(modifier);
        this.setModifyDate(modifyDate);
        this.setSampleCategory(sampleCategory);
        this.setAssociateSampleId(associateSampleId);
    }

    public DtoSampleGroup(String sampleGroupId, String sampleCode, String sampleId, String redFolderName,
                          String sampleTypeId, Date samplingTimeBegin, String projectId, String status,
                          String receiveId, String analyseItemNames, Integer reserveNums, Integer analyseNums,
                          String sampleTypeGroupId, Integer isGroup, String qcId, Date receiveSampleDate) {
        this(sampleGroupId, sampleCode, sampleId, redFolderName, sampleTypeId, samplingTimeBegin, projectId, status, receiveId, analyseItemNames, reserveNums,
                analyseNums, sampleTypeGroupId, isGroup, qcId);
        this.setSendTime(receiveSampleDate);
    }
}