package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分析数据公共实体对象
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseDataTemp {

    /**
     * 数据主键id
     */
    private String id;

    /**
     * 分析时间
     */
    private Date analyzeTime;

    /**
     * 数据录入时间
     */
    private Date dataInputTime;

    /**
     * 量纲名称
     */
    private String dimension;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 受检单位
     */
    private String inspectedEnt;

    /**
     * 受检单位id
     */
    private String inspectedEntId;

    /**
     * 是否质控
     */
    private Boolean isQC;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 小数位数
     */
    private Integer mostDecimal;

    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 质控值（标准样的值/加标的值/平行样为空）
     */
    private String qcValue;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析项目id
     */
    private String analyseItemId;

    /**
     * 方法id
     */
    private String analyzeMethodId;

    /**
     * 方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    private String redCountryStandard;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 采样时间
     */
    private Date sampleTime;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     * 次数
     */
    private Integer timesOrder;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 检测结果（未修约）
     */
    private String testOrignValue;

    /**
     * 检测结果（已修约）
     */
    private String testValueDstr;

    /***
     * 参与运算的值
     */
    private BigDecimal testValueD;

    /**
     * 小的检测单id
     */
    private String workSheetId;

    /**
     * 检出下限
     */
    private String lowerLimit;

    /**
     * 项目等级
     */
    private Integer grade;

    /**
     * 送样单id
     */
    private String receiveId;

    /***
     * 样品id
     */
    private String sampleId;

    /**
     * 大类的样品类型id
     */
    private String bigSampleTypeId;

    /**
     * 要求完成时间
     */
    private Date requireDeadLine;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 领样状态
     */
    private Integer innerReceiveStatus;

    /**
     * 原样id
     */
    private String associateSampleId;

    /**
     * 质控任务等级名称
     */
    private String qcRegisterGrade;

    /**
     * 判断依据
     */
    private String qcRegisterJudement;

    /**
     * 是否质控任务
     */
    private Boolean isQm;

    /**
     * 质控数据
     */
    private String qcTestValue;

    /**
     * 加标体积
     */
    private String qcVolume;

    /**
     * 质控任务类型
     */
    private Integer qmQcType;

    /**
     * 标准范围
     */
    private String qmRange;

    /**
     * 样品真实值
     */
    private String realSampleTestValue;

    /**
     * 检测单创建时间
     */
    private Date workSheetCreateTime;

    /**
     * 公式
     */
    private String formula;

    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 标准编号
     */
    private String qcCode;

    /**
     * 样品备注
     */
    private String sampleRemark;

    /**
     * 分析数据状态
     */
    private Integer dataStatus;

    /**
     * 质控种类 0.原样 1.质控样 2.串联样 3.原样加原样 4.比对样
     */
    private Integer sampleCategory;

    /**
     * 标样有效期
     */
    private Date qcValidDate;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 是否数据确认
     */
    private Boolean isDataEnabled;

    /**
     * 对应组的样品id
     */
    private String groupSampleId;

    /**
     * 采集编号
     */
    private String gatherCode;

    /**
     * 质控信息
     */
    private String qcInfo;

    /**
     * 原样的样品类型
     */
    private Integer AssociateSampleType;

    /**
     * 串联中间计算结果
     */
    private String seriesValue;

    /**
     * 标样的配置日期
     */
    private Date qcStandardDate;

    /**
     * 标样id
     */
    private String qcStandardId;

    /**
     * 加标体积/标准溶液加入体积量纲id
     */
    private String qcVolumeDimensionId;

    /**
     * 加入标准量/标准物质加入量/替代物加入量量纲id
     */
    private String qcValueDimensionId;

    /**
     * 测定值量纲id
     */
    private String qcTestValueDimensionId;

    /**
     * 样值量纲id
     */
    private String realSampleTestValueDimensionId;

    /**
     * 加标液浓度量纲id
     */
    private String qcConcentrationDimensionId;

    /**
     * 加标液浓度
     */
    private String qcConcentration;

    /**
     * 是否科学计数法
     */
    private Boolean isSci;

    /**
     * 样品盲样类型
     */
    private Integer blindType;

    /**
     * 平行样和原样的均值
     */
    private String pxAverageValue;

    /**
     * 数据集合
     */
    private List<DtoAnalyseData> analyseDataList;

    /**
     * 检测类型ids,用作质控统计按照分析项目合并
     */
    private List<String> sampleTypeIds;

    /**
     * 样品ids,用作质控统计按照分析项目合并
     */
    private List<String> testIds;

    /**
     * 带分组标记的样品编号
     */
    private String sampleCodeWithTag;

    /**
     * 不确定类型
     */
    private Integer uncertainType = EnumBase.EnumUncertainType.浓度.getValue();

    /**
     * 标样区间范围低点
     */
    private String rangeLow;

    /**
     * 标样区间范围高点
     */
    private String rangeHigh;

    /**
     * 空的构造函数
     */
    public DtoAnalyseDataTemp() {

    }

    /**
     * 该构造函数主要用到 WorkSheetFolderServiceImpl 下面 findAnalyseDataByPage 打开检测单方法及ReceiveSubSampleRecordServiceImpl 下面findLocalData方法，如果修改，对应的应用地方也要调整
     *
     * @param id                   数据id
     * @param analyzeTime          分析数据
     * @param dataInputTime        数据时间
     * @param requireDeadLine      要求完成时间
     * @param dimension            量纲名称
     * @param dimensionId          量纲id
     * @param examLimitValue       检出限
     * @param qcId                 质控id
     * @param qcType               质控类型
     * @param qcGrade              质控等级
     * @param mostDecimal          小数位数
     * @param mostSignificance     有效位数
     * @param testId               测试项目id
     * @param redAnalyzeItemName   分析项目
     * @param analyseItemId        分析项目id
     * @param analyzeMethodId      方法id
     * @param redAnalyzeMethodName 分析方法
     * @param testValue            出证结果
     * @param testOrignValue       原始结果
     * @param testValueDstr        检测结果
     * @param testValueD           检测结果修约值
     * @param workSheetId          检测单小类id
     * @param grade                等级
     * @param inspectedEnt         受检单位
     * @param inspectedEntId       受检单位id
     * @param redFolderName        点位名称
     * @param sampleTime           采样时间
     * @param sampleCode           样品编号
     * @param cycleOrder           周期
     * @param timesOrder           频次
     * @param sampleTypeId         样品类型id
     * @param receiveId            送样单id
     * @param sampleId             样品类型id
     * @param sampleTypeName       样品类型名称
     * @param sampleCategory       样品类别
     * @param associateSampleId    关联样品id
     * @param isDataEnabled        是否数据确认
     * @param gatherCode           采集编号
     * @param qcInfo               质控信息
     */
    public DtoAnalyseDataTemp(String id, Date analyzeTime, Date dataInputTime, Date requireDeadLine,
                              String dimension, String dimensionId, String examLimitValue, Integer dataStatus,
                              String qcId, Integer qcType, Integer qcGrade, Boolean isQm, Integer mostDecimal, Integer mostSignificance,
                              String testId, String redAnalyzeItemName, String analyseItemId, String analyzeMethodId, String redAnalyzeMethodName, String redCountryStandard,
                              String testValue, String testOrignValue,
                              String testValueDstr, BigDecimal testValueD, String workSheetId, String workSheetFolderId, Integer grade, String inspectedEnt, String inspectedEntId,
                              String redFolderName, Date sampleTime, String sampleCode, Integer cycleOrder, Integer timesOrder, String sampleTypeId, String bigSampleTypeId,
                              String receiveId, String sampleId, String sampleTypeName, Integer sampleCategory, String associateSampleId, Boolean isDataEnabled, String gatherCode, String qcInfo) {
        this.id = id;
        this.analyzeTime = analyzeTime;
        this.dataInputTime = dataInputTime;
        this.requireDeadLine = requireDeadLine;
        this.dimension = dimension;
        this.dimensionId = dimensionId;
        this.examLimitValue = examLimitValue;
        this.dataStatus = dataStatus;
        this.isQC = StringUtils.isNotNullAndEmpty(qcId) && !qcId.equals(UUIDHelper.GUID_EMPTY);
        this.isQm = isQm;
        this.qcId = qcId;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.mostDecimal = mostDecimal;
        this.mostSignificance = mostSignificance;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.analyseItemId = analyseItemId;
        this.testValue = testValue;
        this.testOrignValue = testOrignValue;
        this.testValueDstr = testValueDstr;
        this.testValueD = testValueD;
        this.workSheetId = workSheetId;
        this.workSheetFolderId = workSheetFolderId;
        this.grade = grade;
        this.inspectedEnt = inspectedEnt;
        this.inspectedEntId = inspectedEntId;
        this.redFolderName = redFolderName;
        this.sampleTime = sampleTime;
        this.sampleCode = sampleCode;
        this.cycleOrder = cycleOrder;
        this.timesOrder = timesOrder;
        this.sampleTypeId = sampleTypeId;
        this.bigSampleTypeId = bigSampleTypeId;
        this.receiveId = receiveId;
        this.sampleId = sampleId;
        this.analyzeMethodId = analyzeMethodId;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.sampleTypeName = sampleTypeName;
        this.sampleCategory = sampleCategory;
        this.associateSampleId = associateSampleId;
        this.isDataEnabled = isDataEnabled;
        this.gatherCode = gatherCode;
        this.qcInfo = qcInfo;
    }

    /**
     * 该构造函数主要用到 WorkSheetFolderServiceImpl 下面 createWorkSheetFolderBatch创建检测单方法中，如果修改，对应的应用地方也要调整
     *
     * @param id                   数据id
     * @param sampleId             样品id
     * @param sampleCode           样品编号
     * @param redFolderName        点位名称
     * @param inspectedEnt         受检单位
     * @param samplingTimeBegin    采用时间
     * @param testId               测试项目id
     * @param receiveId            送样单id
     * @param sampleTypeId         样品类型id
     * @param redAnalyzeItemName   分析项目名称
     * @param redCountryStandard   标准
     * @param redAnalyzeMethodName 方法名称
     * @param analyzeMethodId      方法id
     * @param isDataEnabled        是否数据确认
     * @param gatherCode           采集编号
     * @param qcInfo               质控信息
     */
    public DtoAnalyseDataTemp(String id, String sampleId, String sampleCode,
                              String redFolderName, String inspectedEnt,
                              Date samplingTimeBegin, String testId, Boolean isQm,
                              String receiveId, String projectId,
                              String sampleTypeId,
                              String analyseItemId,
                              String redAnalyzeItemName, String redCountryStandard,
                              String redAnalyzeMethodName, String analyzeMethodId, Integer innerReceiveStatus, Boolean isDataEnabled, String gatherCode, String qcInfo) {
        this.id = id;
        this.sampleId = sampleId;
        this.sampleCode = sampleCode;
        this.redFolderName = redFolderName;
        this.inspectedEnt = inspectedEnt;
        this.sampleTime = samplingTimeBegin;
        this.testId = testId;
        this.isQm = isQm;
        this.receiveId = receiveId;
        this.projectId = projectId;
        this.sampleTypeId = sampleTypeId;
        this.analyseItemId = analyseItemId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redCountryStandard = redCountryStandard;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.analyzeMethodId = analyzeMethodId;
        this.innerReceiveStatus = innerReceiveStatus;
        this.isDataEnabled = isDataEnabled;
        this.gatherCode = gatherCode;
        this.qcInfo = qcInfo;
    }


    /**
     * 该构造函数主要用到 WorkSheetFolderServiceImpl 下面 createWorkSheetFolderBatch创建检测单方法中，主要是质控任务的数据，如果修改，对应的应用地方也要调整
     *
     * @param id            数据ID
     * @param instrumentId  仪器id
     * @param sampleId      样品id
     * @param testId        测试项目id
     * @param sampleTypeId  样品类型id
     * @param isDataEnabled 是否数据确认
     * @param gatherCode    采集编号
     * @param qcInfo        质控信息
     */
    public DtoAnalyseDataTemp(String id, String instrumentId, String sampleId, String testId,
                              String sampleTypeId,
                              String analyseItemId,
                              String analyzeMethodId,
                              String redAnalyzeItemName,
                              String redAnalyzeMethodName,
                              String redCountryStandard, Integer innerReceiveStatus, Boolean isDataEnabled, String gatherCode, String qcInfo) {
        this.id = id;
        this.instrumentId = instrumentId;
        this.sampleId = sampleId;
        this.testId = testId;
        this.sampleTypeId = sampleTypeId;
        this.analyseItemId = analyseItemId;
        this.analyzeMethodId = analyzeMethodId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.innerReceiveStatus = innerReceiveStatus;
        this.isDataEnabled = isDataEnabled;
        this.gatherCode = gatherCode;
        this.qcInfo = qcInfo;
    }

    /**
     * 该构造函数主要用到 分析质控统计、质控信息
     *
     * @param id                 数据id
     * @param analyzeTime        数据时间
     * @param sampleId           样品id
     * @param testId             测试项目id
     * @param redAnalyzeItemName 分析项目名称
     * @param qcId               质控id
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param testValue          出证值
     * @param testOrignValue     原始值
     * @param testValueD         数值
     * @param testValueDstr      修约值
     * @param sampleTypeId       样品检测类型id
     * @param associateSampleId  关联样品id
     * @param gatherCode         采集编号
     * @param qcInfo             质控信息
     */
    public DtoAnalyseDataTemp(String id, Date analyzeTime, String sampleId, String testId, String redAnalyzeItemName, String qcId, Integer qcGrade, Integer qcType, String testValue, String testOrignValue, BigDecimal testValueD,
                              String testValueDstr, String sampleTypeId, String associateSampleId, String gatherCode, String qcInfo) {
        this.id = id;
        this.analyzeTime = analyzeTime;
        this.sampleId = sampleId;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.qcId = qcId;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.testValue = testValue;
        this.testOrignValue = testOrignValue;
        this.testValueD = testValueD;
        this.testValueDstr = testValueDstr;
        this.sampleTypeId = sampleTypeId;
        this.associateSampleId = associateSampleId;
        this.gatherCode = gatherCode;
        this.qcInfo = qcInfo;
    }

    /**
     * 该构造函数主要用到 分析质控统计、质控信息
     *
     * @param id                 数据id
     * @param analyzeTime        数据时间
     * @param sampleId           样品id
     * @param testId             测试项目id
     * @param redAnalyzeItemName 分析项目名称
     * @param qcId               质控id
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param testValue          出证值
     * @param testOrignValue     原始值
     * @param testValueD         数值
     * @param testValueDstr      修约值
     * @param sampleTypeId       样品检测类型id
     * @param associateSampleId  关联样品id
     * @param gatherCode         采集编号
     * @param qcInfo             质控信息
     * @param blindType          盲样类型
     * @param isQm               是否质控任务
     * @param projectId          项目Id
     * @param workSheetFolderId  工作单Id
     */
    public DtoAnalyseDataTemp(String id, Date analyzeTime, String sampleId, String testId, String redAnalyzeItemName,
                              String qcId, Integer qcGrade, Integer qcType, String testValue, String testOrignValue,
                              BigDecimal testValueD, String testValueDstr, String sampleTypeId, String associateSampleId,
                              String gatherCode, String qcInfo, Integer blindType, Boolean isQm, String projectId,
                              String workSheetFolderId) {
        this(id, analyzeTime, sampleId, testId, redAnalyzeItemName, qcId, qcGrade, qcType, testValue, testOrignValue, testValueD, testValueDstr, sampleTypeId, associateSampleId, gatherCode, qcInfo);
        this.blindType = blindType;
        this.isQm = isQm;
        this.projectId = projectId;
        this.workSheetFolderId = workSheetFolderId;
        this.receiveId = workSheetFolderId;
    }

    /**
     * 该构造函数主要用到 WorkSheetFolderServiceImpl 下面 findAnalyseDataByPage 打开检测单方法 -- 新增串联中间结果
     *
     * @param id                   数据id
     * @param analyzeTime          分析数据
     * @param dataInputTime        数据时间
     * @param requireDeadLine      要求完成时间
     * @param dimension            量纲名称
     * @param dimensionId          量纲id
     * @param examLimitValue       检出限
     * @param qcId                 质控id
     * @param qcType               质控类型
     * @param qcGrade              质控等级
     * @param mostDecimal          小数位数
     * @param mostSignificance     有效位数
     * @param testId               测试项目id
     * @param redAnalyzeItemName   分析项目
     * @param analyseItemId        分析项目id
     * @param analyzeMethodId      方法id
     * @param redAnalyzeMethodName 分析方法
     * @param testValue            出证结果
     * @param testOrignValue       原始结果
     * @param testValueDstr        检测结果
     * @param testValueD           检测结果修约值
     * @param workSheetId          检测单小类id
     * @param grade                等级
     * @param inspectedEnt         受检单位
     * @param inspectedEntId       受检单位id
     * @param redFolderName        点位名称
     * @param sampleTime           采样时间
     * @param sampleCode           样品编号
     * @param cycleOrder           周期
     * @param timesOrder           频次
     * @param sampleTypeId         样品类型id
     * @param receiveId            送样单id
     * @param sampleId             样品类型id
     * @param sampleTypeName       样品类型名称
     * @param sampleCategory       样品类别
     * @param associateSampleId    关联样品id
     * @param isDataEnabled        是否数据确认
     * @param gatherCode           采集编号
     * @param qcInfo               质控信息
     * @param seriesValue          串联中间计算结果
     */
    public DtoAnalyseDataTemp(String id, Date analyzeTime, Date dataInputTime, Date requireDeadLine,
                              String dimension, String dimensionId, String examLimitValue, Integer dataStatus,
                              String qcId, Integer qcType, Integer qcGrade, Boolean isQm, Integer mostDecimal, Integer mostSignificance,
                              String testId, String redAnalyzeItemName, String analyseItemId, String analyzeMethodId, String redAnalyzeMethodName, String redCountryStandard,
                              String testValue, String testOrignValue, String testValueDstr, BigDecimal testValueD,
                              String workSheetId, String workSheetFolderId, Integer grade, String inspectedEnt, String inspectedEntId,
                              String redFolderName, Date sampleTime, String sampleCode, Integer cycleOrder, Integer timesOrder, String sampleTypeId, String bigSampleTypeId,
                              String receiveId, String sampleId, String sampleTypeName, Integer sampleCategory, String associateSampleId, Boolean isDataEnabled,
                              String gatherCode, String qcInfo, String seriesValue, Boolean isSci, String pxAverageValue) {
        this.id = id;
        this.analyzeTime = analyzeTime;
        this.dataInputTime = dataInputTime;
        this.requireDeadLine = requireDeadLine;
        this.dimension = dimension;
        this.dimensionId = dimensionId;
        this.examLimitValue = examLimitValue;
        this.dataStatus = dataStatus;
        this.isQC = StringUtils.isNotNullAndEmpty(qcId) && !qcId.equals(UUIDHelper.GUID_EMPTY);
        this.isQm = isQm;
        this.qcId = qcId;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.mostDecimal = mostDecimal;
        this.mostSignificance = mostSignificance;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.analyseItemId = analyseItemId;
        this.testValue = testValue;
        this.testOrignValue = testOrignValue;
        this.testValueDstr = testValueDstr;
        this.testValueD = testValueD;
        this.workSheetId = workSheetId;
        this.workSheetFolderId = workSheetFolderId;
        this.grade = grade;
        this.inspectedEnt = inspectedEnt;
        this.inspectedEntId = inspectedEntId;
        this.redFolderName = redFolderName;
        this.sampleTime = sampleTime;
        this.sampleCode = sampleCode;
        this.cycleOrder = cycleOrder;
        this.timesOrder = timesOrder;
        this.sampleTypeId = sampleTypeId;
        this.bigSampleTypeId = bigSampleTypeId;
        this.receiveId = receiveId;
        this.sampleId = sampleId;
        this.analyzeMethodId = analyzeMethodId;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.sampleTypeName = sampleTypeName;
        this.sampleCategory = sampleCategory;
        this.associateSampleId = associateSampleId;
        this.isDataEnabled = isDataEnabled;
        this.gatherCode = gatherCode;
        this.qcInfo = qcInfo;
        this.seriesValue = seriesValue;
        this.isSci = isSci;
        this.pxAverageValue = pxAverageValue;
    }

    /**
     * @param id                   数据id
     * @param analyzeTime          分析数据
     * @param dataInputTime        数据时间
     * @param requireDeadLine      要求完成时间
     * @param dimension            量纲名称
     * @param dimensionId          量纲id
     * @param examLimitValue       检出限
     * @param qcId                 质控id
     * @param qcType               质控类型
     * @param qcGrade              质控等级
     * @param mostDecimal          小数位数
     * @param mostSignificance     有效位数
     * @param testId               测试项目id
     * @param redAnalyzeItemName   分析项目
     * @param analyseItemId        分析项目id
     * @param analyzeMethodId      方法id
     * @param redAnalyzeMethodName 分析方法
     * @param testValue            出证结果
     * @param testOrignValue       原始结果
     * @param testValueDstr        检测结果
     * @param testValueD           检测结果修约值
     * @param workSheetId          检测单小类id
     * @param grade                等级
     * @param inspectedEnt         受检单位
     * @param inspectedEntId       受检单位id
     * @param redFolderName        点位名称
     * @param sampleTime           采样时间
     * @param sampleCode           样品编号
     * @param cycleOrder           周期
     * @param timesOrder           频次
     * @param sampleTypeId         样品类型id
     * @param receiveId            送样单id
     * @param sampleId             样品类型id
     * @param sampleTypeName       样品类型名称
     * @param sampleCategory       样品类别
     * @param associateSampleId    关联样品id
     * @param isDataEnabled        是否数据确认
     * @param gatherCode           采集编号
     * @param qcInfo               质控信息
     * @param seriesValue          串联中间计算结果
     * @param lowerLimit           测定下限
     */
    public DtoAnalyseDataTemp(String id, Date analyzeTime, Date dataInputTime, Date requireDeadLine, String dimension,
                              String dimensionId, String examLimitValue, Integer dataStatus, String qcId, Integer qcType,
                              Integer qcGrade, Boolean isQm, Integer mostDecimal, Integer mostSignificance, String testId,
                              String redAnalyzeItemName, String analyseItemId, String analyzeMethodId, String redAnalyzeMethodName, String redCountryStandard,
                              String testValue, String testOrignValue, String testValueDstr, BigDecimal testValueD,
                              String workSheetId, String workSheetFolderId, Integer grade, String inspectedEnt, String inspectedEntId,
                              String redFolderName, Date sampleTime, String sampleCode, Integer cycleOrder, Integer timesOrder, String sampleTypeId, String bigSampleTypeId,
                              String receiveId, String sampleId, String sampleTypeName, Integer sampleCategory, String associateSampleId, Boolean isDataEnabled,
                              String gatherCode, String qcInfo, String seriesValue, Boolean isSci, String pxAverageValue, String lowerLimit) {
        this.id = id;
        this.analyzeTime = analyzeTime;
        this.dataInputTime = dataInputTime;
        this.requireDeadLine = requireDeadLine;
        this.dimension = dimension;
        this.dimensionId = dimensionId;
        this.examLimitValue = examLimitValue;
        this.dataStatus = dataStatus;
        this.isQC = StringUtils.isNotNullAndEmpty(qcId) && !qcId.equals(UUIDHelper.GUID_EMPTY);
        this.isQm = isQm;
        this.qcId = qcId;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.mostDecimal = mostDecimal;
        this.mostSignificance = mostSignificance;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.analyseItemId = analyseItemId;
        this.testValue = testValue;
        this.testOrignValue = testOrignValue;
        this.testValueDstr = testValueDstr;
        this.testValueD = testValueD;
        this.workSheetId = workSheetId;
        this.workSheetFolderId = workSheetFolderId;
        this.grade = grade;
        this.inspectedEnt = inspectedEnt;
        this.inspectedEntId = inspectedEntId;
        this.redFolderName = redFolderName;
        this.sampleTime = sampleTime;
        this.sampleCode = sampleCode;
        this.cycleOrder = cycleOrder;
        this.timesOrder = timesOrder;
        this.sampleTypeId = sampleTypeId;
        this.bigSampleTypeId = bigSampleTypeId;
        this.receiveId = receiveId;
        this.sampleId = sampleId;
        this.analyzeMethodId = analyzeMethodId;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.sampleTypeName = sampleTypeName;
        this.sampleCategory = sampleCategory;
        this.associateSampleId = associateSampleId;
        this.isDataEnabled = isDataEnabled;
        this.gatherCode = gatherCode;
        this.qcInfo = qcInfo;
        this.seriesValue = seriesValue;
        this.isSci = isSci;
        this.pxAverageValue = pxAverageValue;
        this.lowerLimit = lowerLimit;
    }
}
