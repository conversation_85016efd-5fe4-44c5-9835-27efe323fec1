package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.*;


/**
 * 上海监管平台方案编制实体：日期
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Item")
@XmlType(name = "PlanDateItem")
public class RPTaskPlanDateVO {

    /**
     * 需要修改日期的ID
     */
    @XmlElement(name = "ID")
    private String id;

    /**
     * 修改的日期
     */
    @XmlElement(name = "DATE")
    private String date;

    /**
     * 默认构造方法
     */
    public RPTaskPlanDateVO() {
    }

    /**
     * 构造方法
     *
     * @param id   需要修改日期的ID
     * @param date 修改的日期
     */
    public RPTaskPlanDateVO(String id, String date) {
        this.id = id;
        this.date = date;
    }
}
