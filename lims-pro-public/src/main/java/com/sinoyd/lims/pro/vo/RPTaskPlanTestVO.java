package com.sinoyd.lims.pro.vo;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 * 上海监管平台方案测试项目VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Item")
@XmlType(name = "PlanTestItem")
@Data
public class RPTaskPlanTestVO {

    /**
     * 点位id
     */
    @XmlElement(name = "MTSTID")
    private String folderId;

    /**
     * 上海监管平台分析方法id（上海监管平台测试项目id）
     */
    @XmlElement(name = "MMID")
    private String rpMethodId;

    /**
     * 上海监管平台采样方法id
     */
    @XmlElement(name = "SAMPLETYPE")
    private String rpSamplingMethodId;

    /**
     * 样次
     */
    @XmlElement(name = "SAMPLENUM")
    private String sampleCount;

    /**
     * 样例
     */
    @XmlElement(name = "DEMO")
    private String demo;


    public RPTaskPlanTestVO() {
    }

    /**
     * 根据测试项目构建推送数据
     *
     * @param test            测试项目数据
     * @param pushFrequencyId 推送的监管平台的频次数据id
     * @param isAnalyze       任务采样类型是否为分析类型
     */
    public RPTaskPlanTestVO(DtoTest test, String pushFrequencyId, boolean isAnalyze) {
        this.folderId = pushFrequencyId;
        this.rpMethodId = test.getShMethodId();
        if (!isAnalyze) {
            this.rpSamplingMethodId = test.getShSamplingMethodId();
        }else{
            this.rpSamplingMethodId = "";
        }
        Integer sampleCount = 0;
        if (test.getSampleCount() != null){
            sampleCount = test.getSampleCount();
        }
        this.sampleCount = sampleCount.toString();
        this.demo = "";
    }

    public RPTaskPlanTestVO(String folderId, String rpMethodId, String rpSamplingMethodId, String sampleCount, String demo) {
        this.folderId = folderId;
        this.rpMethodId = rpMethodId;
        this.rpSamplingMethodId = rpSamplingMethodId;
        this.sampleCount = sampleCount;
        this.demo = demo;
    }
}
