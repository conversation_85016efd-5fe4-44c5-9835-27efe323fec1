package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;

/**
 * 上海监管平台采样方法VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/09
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPSamplingMethodVO {

    /**
     * 方法id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    private String id;

    /**
     * 采样方法名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "NAME")
    private String methodName;
}
