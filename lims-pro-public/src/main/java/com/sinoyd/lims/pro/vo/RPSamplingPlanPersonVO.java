package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.*;

/**
 * 监管平台采样人员更新实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/14
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPSamplingPlanPersonVO {

    /**
     * 采样人员绑定数据集合
     */
    @XmlElementWrapper(name = LABEL_ITEM_PREFIX + "WorkerDataList")
    @XmlElement(name = LABEL_ITEM_PREFIX + "WorkerData")
    private List<SamplingPersonData> samplingPersonData = new ArrayList<>();


    /**
     * 采样人员绑定数据
     */
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = LABEL_ITEM_PREFIX + "WorkerData")
    public static class SamplingPersonData {

        /**
         * 监管平台任务id
         */
        @XmlElement(name = LABEL_ITEM_PREFIX + "MTID")
        private String rpProjectId;

        /**
         * 监管平台人员id
         */
        @XmlElement(name = LABEL_ITEM_PREFIX + "OWID")
        private String rpPersonId;

        /**
         * 构造函数
         *
         * @param rpProjectId 监管平台任务id
         * @param rpPersonId  监管平台人员id
         */
        public SamplingPersonData(String rpProjectId, String rpPersonId) {
            this.rpProjectId = rpProjectId;
            this.rpPersonId = rpPersonId;
        }
    }
}
