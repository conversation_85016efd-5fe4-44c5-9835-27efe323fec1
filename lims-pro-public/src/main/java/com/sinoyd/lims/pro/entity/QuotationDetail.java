package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;


/**
 * QuotationDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "QuotationDetail")
@Data
@EntityListeners(AuditingEntityListener.class)
public class QuotationDetail implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public QuotationDetail() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 订单id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("订单id")
    private String orderId;

    /**
     * 总计id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("总计id")
    private String quotationId;

    /**
     * 样品类型id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品类型id")
    private String sampleTypeId;

    /**
     * 分析项目名称
     */
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 255)
    private String redAnalyseItemName;

    /**
     * 分析项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目id")
    private String analyseItemId;

    /**
     * 分析方法名称
     */
    @ApiModelProperty("分析方法名称")
    @Length(message = "分析方法名称{validation.message.length}", max = 255)
    private String redAnalyseMethod;

    /**
     * 分析方法id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析方法id")
    private String analyseMethodId;

    /**
     * 测试项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 点位名称
     */
    @Column(length = 2000)
    @ApiModelProperty("点位名称")
    @Length(message = "点位名称{validation.message.length}", max = 2000)
    private String folderName;

    /**
     * 任务数
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("任务数")
    private Integer projectCount;

    /**
     * 监测间隔
     */
    @Column(length = 50)
    @ApiModelProperty("监测间隔")
    private String projectInterval;

    /**
     * 周期数
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("周期数")
    private Integer cycleOrder;

    /**
     * 批次数
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("批次数")
    private Integer timesOrder;

    /**
     * 样品数量
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("样品数量")
    private Integer sampleCount;

    /**
     * 总检数
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("总检数")
    private Integer sampleOrder;

    /**
     * 已检数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("已检数")
    private Integer inspectedCount;

    /**
     * 剩检数
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("剩检数")
    private Integer residueCount;

    /**
     * 采样费
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("采样费")
    private BigDecimal samplingPrice;

    /**
     * 分析费
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("分析费")
    private BigDecimal analysePrice;

    /**
     * 小计
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("小计")
    private BigDecimal charge;

    /**
     * 总价
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("总价")
    private BigDecimal quotationPrice;

    /**
     * 是否总称
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否总称")
    private Boolean isTotal = false;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}