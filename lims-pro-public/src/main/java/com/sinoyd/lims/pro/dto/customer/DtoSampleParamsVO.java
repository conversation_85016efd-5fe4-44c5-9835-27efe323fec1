package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 样品参数传输实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/4/27
 * @since V100R001
 */
@Data
public class DtoSampleParamsVO {

    /**
     * 样品id
     */
    private List<String> sampleIds;

    /**
     * 样品性状
     */
    private String sampleCharacter;

    /**
     * 样品数量
     */
    private Integer sampleNum;

    /**
     * 保存方式
     */
    private String saveMethod;

    /**
     * 采样日期
     */
    private Date samplingTimeBegin;

}
