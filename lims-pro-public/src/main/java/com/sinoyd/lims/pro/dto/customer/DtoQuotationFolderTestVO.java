package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import lombok.Data;

import java.util.List;

/**
 * 订单详情
 * <AUTHOR>
 * @version V1.0.0 2020/04/10
 * @since V100R001
 */
@Data
public class DtoQuotationFolderTestVO {

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 样品类型ids
     */
    private List<String> sampleTypeIds;

    /**
     * 样品类型名称
     */
    private String sampleTypeName;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * 方案明细
     */
    private List<DtoQuotationDetail> quotationDetail;

    /**
     * 测试项目名称
     */
    private String itemNames;

    /**
     * 测试项目集合
     */
    private List<DtoTest> testList;
}
