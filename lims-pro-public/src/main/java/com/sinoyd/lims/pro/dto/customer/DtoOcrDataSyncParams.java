package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.dto.DtoSample;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ocr 应用同步数据传参对象
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/24
 */
@Data
public class DtoOcrDataSyncParams {
    /**
     * 样品数据
     */
    private List<Map<String, Object>> sampleList;

    /**
     * 现场数据
     */
    private List<Map<String, Object>>  analyseDataList;

    /**
     * 数据类型   样品数据/现场数据
     */
    private Integer type;

    /**
     * 参数配置
     */
    private List<DtoParamsConfig> paramsConfig;

    /**
     * 手动同步，选择历史记录ids
     */
    private List<String> ocrConfigRecordIds = new ArrayList<>();
}
