package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.util.Date;

/**
 * 分析数据进度
 * <AUTHOR>
 * @version V1.0.0 2020/1/13
 * @since V100R001
 */
@Data
public class DtoAnalyseDataInquiry {

    public void setUndetectDealPerson(String undetectDealPerson) {
        this.undetect.setDealPerson(undetectDealPerson);
    }

    public void setUndetectDealTime(Date undetectDealTime) {
        this.undetect.setDealTime(undetectDealTime);
    }

    public void setUndetectStatus(Integer undetectStatus) {
        this.undetect.setStatus(undetectStatus);
    }

    public void setWaitDealPerson(String waitDealPerson) {
        this.wait.setDealPerson(waitDealPerson);
    }

    public void setWaitDealTime(Date waitDealTime) {
        this.wait.setDealTime(waitDealTime);
    }

    public void setWaitStatus(Integer waitStatus) {
        this.wait.setStatus(waitStatus);
    }

    public void setTestDealPerson(String testDealPerson) {
        this.test.setDealPerson(testDealPerson);
    }

    public void setTestDealTime(Date testDealTime) {
        this.test.setDealTime(testDealTime);
    }

    public void setTestStatus(Integer testStatus) {
        this.test.setStatus(testStatus);
    }

    public void setCheckDealPerson(String checkDealPerson) {
        this.check.setDealPerson(checkDealPerson);
    }

    public void setCheckDealTime(Date checkDealTime) {
        this.check.setDealTime(checkDealTime);
    }

    public void setCheckStatus(Integer checkStatus) {
        this.check.setStatus(checkStatus);
    }

    public void setAuditDealPerson(String auditDealPerson) {
        this.audit.setDealPerson(auditDealPerson);
    }

    public void setAuditDealTime(Date auditDealTime) {
        this.audit.setDealTime(auditDealTime);
    }

    public void setAuditStatus(Integer auditStatus) {
        this.audit.setStatus(auditStatus);
    }

    public void setAffirmDealPerson(String affirmDealPerson) {
        this.affirm.setDealPerson(affirmDealPerson);
    }

    public void setAffirmDealTime(Date affirmDealTime) {
        this.affirm.setDealTime(affirmDealTime);
    }

    public void setAffirmStatus(Integer affirmStatus) {
        this.affirm.setStatus(affirmStatus);
    }

    public void setSubAffirm(){
        this.setUndetectStatus(EnumPRO.EnumStatus.已处理.getValue());
        this.setWaitStatus(EnumPRO.EnumStatus.已处理.getValue());
        this.setTestStatus(EnumPRO.EnumStatus.已处理.getValue());
        this.setCheckStatus(EnumPRO.EnumStatus.已处理.getValue());
        this.setAuditStatus(EnumPRO.EnumStatus.已处理.getValue());
        this.setAffirmStatus(EnumPRO.EnumStatus.已处理.getValue());
    }


    /**
     * 分析数据id
     */
    private String id;

    /**
     * 检测单编号
     */
    private String workSheetCode;

    /**
     * 分析项目
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法
     */
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    private String redCountryStandard;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 量纲
     */
    private String dimensionName;

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 分析人员
     */
    private String analystName;

    /**
     * 未测
     */
    private DtoDataInquiry undetect = new DtoDataInquiry();

    /**
     * 待检
     */
    private DtoDataInquiry wait = new DtoDataInquiry();

    /**
     * 检测中
     */
    private DtoDataInquiry test = new DtoDataInquiry();

    /**
     * 复核
     */
    private DtoDataInquiry check = new DtoDataInquiry();

    /**
     * 审核
     */
    private DtoDataInquiry audit = new DtoDataInquiry();

    /**
     * 已确认
     */
    private DtoDataInquiry affirm = new DtoDataInquiry();
}
