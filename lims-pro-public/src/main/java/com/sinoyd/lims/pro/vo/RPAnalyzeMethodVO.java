package com.sinoyd.lims.pro.vo;


import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;
/**
 * 监管平台方法数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/29
 */
@Data
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
public class RPAnalyzeMethodVO {

    /**
     * 所属分类 ID
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "TYPEID")
    private String typeId;

    /**
     * 所属分类名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "TYPENAME")
    private String typeName;


    /**
     * 项目名称 ID（分析项目id）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ITEMID")
    private String itemId;

    /**
     * 项目名称 （分析项目名称）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ITEMNAME")
    private String itemName;

    /**
     * 方法 ID（分析方法id）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "METHODID")
    private String methodId;

    /**
     * 方法名称（分析方法名称）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "METHODNAME")
    private String methodName;

}
