package com.sinoyd.lims.pro.annotate;

import java.lang.annotation.*;

/**
 * 监管平台字段注解
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/29
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Inherited
@Documented
public @interface RegulatoryPlatformField {

    /**
     * 监管平台字段名称
     *
     * @return 字段名称
     */
    String fieldName() default "";

    /**
     * 是否为空
     *
     * @return 是否为空
     */
    boolean nullable() default true;
}
