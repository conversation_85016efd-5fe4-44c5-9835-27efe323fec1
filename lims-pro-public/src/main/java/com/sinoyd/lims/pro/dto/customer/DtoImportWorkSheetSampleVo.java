package com.sinoyd.lims.pro.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0.0 2025/7/2
 * @since V100R001
 */
@Data
public class DtoImportWorkSheetSampleVo {

    @Excel(name = "样品编号", orderNum = "100")
    private String sampleCode;

    @Excel(name = "称样量（g）", orderNum = "200")
    private String weight;

    @Excel(name = "含水率（%）", orderNum = "300")
    private String moistureContent;

    @Excel(name = "干物质含量（%）", orderNum = "400")
    private String dryMatterContent;

    private Map<String, Object> dataMap = new HashMap<>();

    public void fillMap() {
        // 称样量有可能叫做  取样量 样品取样量  样品称样量
        dataMap.put("称样量", this.weight);
        dataMap.put("取样量", this.weight);
        dataMap.put("样品取样量", this.weight);
        dataMap.put("样品称样量", this.weight);
        dataMap.put("含水率", this.moistureContent);
        dataMap.put("干物质含量", this.dryMatterContent);
    }

}
