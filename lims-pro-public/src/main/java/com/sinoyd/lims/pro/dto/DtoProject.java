package com.sinoyd.lims.pro.dto;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.EnumHelper;
import com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordTemp;
import com.sinoyd.lims.pro.entity.Project;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.bouncycastle.util.Strings;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.util.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoProject实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Project")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoProject extends Project {
    private static final long serialVersionUID = 1L;

    /**
     * 最新的采样日期
     */
    @Transient
    private String samplingDate;

    /**
     * 已采样子项目数
     */
    @Transient
    private Integer subStartProjectCount;

    /**
     * 采样安排
     */
    @Transient
    private List<DtoSamplingArrange> arrangeList;

    /**
     * 现场质控-考核人员
     */
    @Transient
    private List<String> assessPersonIds;

    /**
     * 采样安排相关提示
     */
    @Transient
    private String arrangeMsg;

    /**
     * 报告各状态对应数量
     */
    @Transient
    private Map<String, Long> reportStateMap;

    /**
     * 报告交付方式值数组
     */
    @Transient
    private List<Integer> postMethodKeys;

    /**
     * 任务相关模块状态
     */
    @Transient
    private Map<String, Object> stepMap;

    /**
     * 采样状态 ：a/b。a代表已经生成样品编号的样品数量，b代表样品总数
     */
    @Transient
    private String samplingRate;

    /**
     * 现场质控-人员比对信息
     */
    @Transient
    private List<DtoLocalTaskPeopleCompare> localTaskPeopleCompareList;

    /**
     * 送样单id
     */
    @Transient
    private List<String> receiveIds;


    @Transient
    private DtoProjectContract projectContract;

    /**
     * 项目类型集合
     */
    @Transient
    private List<DtoSampleType> sampleTypes;

    /**
     * 加载项目计划
     *
     * @param plan 项目计划
     */
    public void loadFromPlan(DtoProjectPlan plan) {
        if (StringUtil.isNotNull(plan.getLeaderId())) {
            this.setLeaderId(plan.getLeaderId());
        }
        if (StringUtil.isNotNull(plan.getReportMakerId())) {
            this.setReportMakerId(plan.getReportMakerId());
        }
        if (StringUtil.isNotNull(plan.getSchemeMakerId())) {
            this.setSchemeMakerId(plan.getSchemeMakerId());
        }
        if (StringUtil.isNotNull(plan.getSpotPersonId())) {
            this.setSpotPersonId(plan.getSpotPersonId());
        }
        if (StringUtil.isNotNull(plan.getSupervisorId())) {
            this.setSupervisorId(plan.getSupervisorId());
        }
        if (StringUtil.isNotNull(plan.getDeadLine())) {
            this.setDeadLine(plan.getDeadLine());
        }
        if (StringUtil.isNotNull(plan.getReportDate())) {
            this.setReportDate(plan.getReportDate());
        }
        if (StringUtil.isNotNull(plan.getRequireAnalyzeDate())) {
            this.setRequireAnalyzeDate(plan.getRequireAnalyzeDate());
        }
        if (StringUtil.isNotNull(plan.getRequireSamplingDate())) {
            this.setRequireSamplingDate(plan.getRequireSamplingDate());
        }
        if (StringUtil.isNotNull(plan.getResponsePerson())) {
            this.setResponsePerson(plan.getResponsePerson());
        }
        if (StringUtil.isNotNull(plan.getRequires())) {
            this.setRequires(plan.getRequires());
        }
        if (StringUtil.isNotNull(plan.getTestMethodRequires())) {
            this.setTestMethodRequires(plan.getTestMethodRequires());
        }
        if (StringUtil.isNotNull(plan.getRemark())) {
            this.setRemark(plan.getRemark());
        }
        if (StringUtil.isNotNull(plan.getIsUseMethod())) {
            this.setIsUseMethod(plan.getIsUseMethod());
        }
        if (StringUtil.isNotNull(plan.getIsEvaluate())) {
            this.setIsEvaluate(plan.getIsEvaluate());
        }
        if (StringUtil.isNotNull(plan.getIsWarning())) {
            this.setIsWarning(plan.getIsWarning());
        }
        if (StringUtil.isNotNull(plan.getWarningDay())) {
            this.setWarningDay(plan.getWarningDay());
        }
        if (StringUtil.isNotNull(plan.getIsFeedback())) {
            this.setIsFeedback(plan.getIsFeedback());
        }
        if (StringUtil.isNotNull(plan.getIsContract())) {
            this.setIsContract(plan.getIsContract());
        }
        if (StringUtil.isNotNull(plan.getSubName())) {
            this.setSubName(plan.getSubName());
        }
        if (StringUtil.isNotNull(plan.getSubItems())) {
            this.setSubItems(plan.getSubItems());
        }
        if (StringUtil.isNotNull(plan.getSubMethod())) {
            this.setSubMethod(plan.getSubMethod());
        }
        if (StringUtil.isNotNull(plan.getIsOutsourcing())) {
            this.setIsOutsourcing(plan.getIsOutsourcing());
        }
        if (StringUtil.isNotNull(plan.getIsMakePlan())) {
            this.setIsMakePlan(plan.getIsMakePlan());
        }
        if (StringUtil.isNotNull(plan.getReportMakerIIId())) {
            this.setReportMakerIIId(plan.getReportMakerIIId());
        }
        if (StringUtil.isNotNull(plan.getQcGrade())) {
            this.setQcGrade(plan.getQcGrade());
        }
        if (StringUtil.isNotNull(plan.getQcType())) {
            this.setQcType(plan.getQcType());
        }
        if (StringUtil.isNotNull(plan.getQcSource())) {
            this.setQcSource(plan.getQcSource());
        }
        if (StringUtil.isNotNull(plan.getJudgment())) {
            this.setJudgment(plan.getJudgment());
        }
        if (StringUtil.isNotEmpty(plan.getAssessPersonIds())) {
            this.setAssessPersonIds(plan.getAssessPersonIds());
        }
    }

    /**
     * 加载送样项目
     *
     * @param temp 送样项目实体
     */
    public void loadFromReceiveTemp(DtoReceiveSampleRecordTemp temp) {
        if (StringUtil.isNotNull(temp.getLeaderId())) {
            this.leaderId = temp.getLeaderId();
        }
        if (StringUtil.isNotNull(temp.getReportMakerId())) {
            this.reportMakerId = temp.getReportMakerId();
        }
        if (StringUtil.isNotNull(temp.getSchemeMakerId())) {
            this.schemeMakerId = temp.getSchemeMakerId();
        }
        if (StringUtil.isNotNull(temp.getSpotPersonId())) {
            this.spotPersonId = temp.getSpotPersonId();
        }
        if (StringUtil.isNotNull(temp.getSupervisorId())) {
            this.supervisorId = temp.getSupervisorId();
        }
        if (StringUtil.isNotNull(temp.getDeadLine())) {
            this.deadLine = temp.getDeadLine();
        }
        if (StringUtil.isNotNull(temp.getReportDate())) {
            this.reportDate = temp.getReportDate();
        }
        if (StringUtil.isNotNull(temp.getRequireAnalyzeDate())) {
            this.requireAnalyzeDate = temp.getRequireAnalyzeDate();
        }
        if (StringUtil.isNotNull(temp.getRequireSamplingDate())) {
            this.requireSamplingDate = temp.getRequireSamplingDate();
        }
        if (StringUtil.isNotNull(temp.getResponsePerson())) {
            this.responsePerson = temp.getResponsePerson();
        }
        if (StringUtil.isNotNull(temp.getRequires())) {
            this.requires = temp.getRequires();
        }
        if (StringUtil.isNotNull(temp.getTestMethodRequires())) {
            this.testMethodRequires = temp.getTestMethodRequires();
        }
        if (StringUtil.isNotNull(temp.getRemark())) {
            this.setRemark(temp.getRemark());
        }
        if (StringUtil.isNotNull(temp.getReceiveRemark())) {
            this.receiveRemark = temp.getReceiveRemark();
        }
        if (StringUtil.isNotNull(temp.getIsUseMethod())) {
            this.isUseMethod = temp.getIsUseMethod();
        }
        if (StringUtil.isNotNull(temp.getIsEvaluate())) {
            this.isEvaluate = temp.getIsEvaluate();
        }
        if (StringUtil.isNotNull(temp.getIsWarning())) {
            this.isWarning = temp.getIsWarning();
        }
        if (StringUtil.isNotNull(temp.getWarningDay())) {
            this.warningDay = temp.getWarningDay();
        }
        if (StringUtil.isNotNull(temp.getIsFeedback())) {
            this.isFeedback = temp.getIsFeedback();
        }
        if (StringUtil.isNotNull(temp.getIsContract())) {
            this.isContract = temp.getIsContract();
        }
        if (StringUtil.isNotNull(temp.getSubName())) {
            this.subName = temp.getSubName();
        }
        if (StringUtil.isNotNull(temp.getSubItems())) {
            this.subItems = temp.getSubItems();
        }
        if (StringUtil.isNotNull(temp.getSubMethod())) {
            this.subMethod = temp.getSubMethod();
        }
        if (StringUtil.isNotNull(temp.getIsOutsourcing())) {
            this.isOutsourcing = temp.getIsOutsourcing();
        }
        if (StringUtil.isNotNull(temp.getIsMakePlan())) {
            this.isMakePlan = temp.getIsMakePlan();
        }
        if (StringUtil.isNotNull(temp.getReportMakerIIId())) {
            this.reportMakerIIId = temp.getReportMakerIIId();
        }
        if (StringUtil.isNotNull(temp.getQcGrade())) {
            this.qcGrade = temp.getQcGrade();
        }
        if (StringUtil.isNotNull(temp.getQcType())) {
            this.qcType = temp.getQcType();
        }
        if (StringUtil.isNotNull(temp.getQcSource())) {
            this.qcSource = temp.getQcSource();
        }
        if (StringUtil.isNotNull(temp.getJudgment())) {
            this.judgment = temp.getJudgment();
        }

        if (StringUtil.isNotNull(temp.getProjectId())) {
            this.setId(temp.getProjectId());
        }

        if (StringUtil.isNotNull(temp.getProjectCode())) {
            this.setProjectCode(temp.getProjectCode());
        }

        if (StringUtil.isNotNull(temp.getProjectTypeId())) {
            this.setProjectTypeId(temp.getProjectTypeId());
        }

        if (StringUtil.isNotNull(temp.getProjectName())) {
            this.setProjectName(temp.getProjectName());
        }

        if (StringUtil.isNotNull(temp.getInceptPersonId())) {
            this.setInceptPersonId(temp.getInceptPersonId());
        }

        if (StringUtil.isNotNull(temp.getInceptTime())) {
            this.setInceptTime(temp.getInceptTime());
        }

        if (StringUtil.isNotNull(temp.getIsStress())) {
            this.setIsStress(temp.getIsStress());
        }

        if (StringUtil.isNotNull(temp.getGrade())) {
            this.setGrade(temp.getGrade());
        }

        if (StringUtil.isNotNull(temp.getCustomerId())) {
            this.setCustomerId(temp.getCustomerId());
        }

        if (StringUtil.isNotNull(temp.getCustomerName())) {
            this.setCustomerName(temp.getCustomerName());
        }

        if (StringUtil.isNotNull(temp.getInspectedEntId())) {
            this.setInspectedEntId(temp.getInspectedEntId());
        }

        if (StringUtil.isNotNull(temp.getInspectedEnt())) {
            this.setInspectedEnt(temp.getInspectedEnt());
        }

        if (StringUtil.isNotNull(temp.getInspectedLinkMan())) {
            this.setInspectedLinkMan(temp.getInspectedLinkMan());
        }

        if (StringUtil.isNotNull(temp.getInspectedLinkPhone())) {
            this.setInspectedLinkPhone(temp.getInspectedLinkPhone());
        }

        if (StringUtil.isNotNull(temp.getInspectedAddress())) {
            this.setInspectedAddress(temp.getInspectedAddress());
        }

        if (StringUtil.isNotNull(temp.getCustomerOwner())) {
            this.setCustomerOwner(temp.getCustomerOwner());
        }

        if (StringUtil.isNotNull(temp.getCustomerAddress())) {
            this.setCustomerAddress(temp.getCustomerAddress());
        }

        if (StringUtil.isNotNull(temp.getLinkMan())) {
            this.setLinkMan(temp.getLinkMan());
        }

        if (StringUtil.isNotNull(temp.getLinkPhone())) {
            this.setLinkPhone(temp.getLinkPhone());
        }

        if (StringUtil.isNotNull(temp.getLinkEmail())) {
            this.setLinkEmail(temp.getLinkEmail());
        }

        if (StringUtil.isNotNull(temp.getLinkFax())) {
            this.setLinkFax(temp.getLinkFax());
        }

        if (StringUtil.isNotNull(temp.getZipCode())) {
            this.setZipCode(temp.getZipCode());
        }

        if (StringUtil.isNotNull(temp.getMonitorPurp())) {
            this.setMonitorPurp(temp.getMonitorPurp());
        }

        if (StringUtil.isNotNull(temp.getMonitorMethods())) {
            this.setMonitorMethods(temp.getMonitorMethods());
        }

        if (StringUtil.isNotNull(temp.getCustomerRequired())) {
            this.setCustomerRequired(temp.getCustomerRequired());
        }

        if (StringUtil.isNotNull(temp.getSampleType())) {
            this.setSampleType(temp.getSampleType());
        }

        if (StringUtil.isNotNull(temp.getPostMethod())) {
            this.setPostMethod(temp.getPostMethod());
        }

        if (StringUtil.isNotNull(temp.getSaveCondition())) {
            this.setSaveCondition(temp.getSaveCondition());
        }

        if (StringUtil.isNotNull(temp.getReportNum())) {
            this.setReportNum(temp.getReportNum());
        }

        if (StringUtil.isNotNull(temp.getQrCodeUrl())) {
            this.setQrCodeUrl(temp.getQrCodeUrl());
        }

        if (StringUtil.isNotNull(temp.getContractId())) {
            this.setContractId(temp.getContractId());
        }

        if (StringUtil.isNotNull(temp.getContractName())) {
            this.setContractName(temp.getContractName());
        }

        if (StringUtil.isNotNull(temp.getContractCode())) {
            this.setContractCode(temp.getContractCode());
        }

        if (StringUtil.isNotNull(temp.getTotalAmount())) {
            this.setTotalAmount(temp.getTotalAmount());
        }

        if (StringUtil.isNotNull(temp.getContractStatus())) {
            this.setContractStatus(temp.getContractStatus());
        }

        if (StringUtil.isNotNull(temp.getOrderId())) {
            this.setOrderId(temp.getOrderId());
        }

        if (StringUtil.isNotNull(temp.getSendSamplePerson())) {
            this.setSendSamplePerson(temp.getSendSamplePerson());
        }

        if (StringUtil.isNotNull(temp.getReportStamp())) {
            this.setReportStamp(temp.getReportStamp());
        }
        if (StringUtil.isNotNull(temp.getMonitorMethod())) {
            this.setMonitorMethod(temp.getMonitorMethod());
        }
        if (StringUtil.isNotNull(temp.getMonitorMethodRemark())) {
            this.setMonitorMethodRemark(temp.getMonitorMethodRemark());
        }
        if (StringUtil.isNotNull(temp.getIsAccredited())) {
            this.setIsAccredited(temp.getIsAccredited());
        }
        if (StringUtil.isNotNull(temp.getIsAccreditedRemark())) {
            this.setIsAccreditedRemark(temp.getIsAccreditedRemark());
        }
        if (StringUtil.isNotNull(temp.getProjectContract())) {
            this.setProjectContract(temp.getProjectContract());
        }
        if (StringUtil.isNotEmpty(temp.getEnvironmentCode())) {
            this.setEnvironmentCode(temp.getEnvironmentCode());
        }
        if (StringUtil.isNotEmpty(temp.getAddressName())) {
            this.setAddressName(temp.getAddressName());
        }
        if (StringUtil.isNotNull(temp.getIsCMA())) {
            this.setIsCMA(temp.getIsCMA());
        }
        if (StringUtil.isNotEmpty(temp.getRegulateReportType())) {
            this.setRegulateReportType(temp.getRegulateReportType());
        }
        if (StringUtil.isNotEmpty(temp.getPollutionCode())) {
            this.setPollutionCode(temp.getPollutionCode());
        }
        if (StringUtil.isNotNull(temp.getSampleSaveMethod())) {
            this.setSampleSaveMethod(temp.getSampleSaveMethod());
        }
        if (StringUtil.isNotNull(temp.getIsSaveContainer())) {
            this.setIsSaveContainer(temp.getIsSaveContainer());
        }
        if (StringUtil.isNotNull(temp.getIsSampleCountRight())) {
            this.setIsSampleCountRight(temp.getIsSampleCountRight());
        }
        if (StringUtil.isNotNull(temp.getSampleValidityPeriod())) {
            this.setSampleValidityPeriod(temp.getSampleValidityPeriod());
        }
        if (StringUtil.isNotEmpty(temp.getSampleConfirmRemark())) {
            this.setSampleConfirmRemark(temp.getSampleConfirmRemark());
        }
        if (StringUtil.isNotNull(temp.getSamplingTime())) {
            this.setExtendDate1(temp.getSamplingTime());
        }
        if (StringUtil.isNotNull(temp.getSendTime())) {
            this.setExtendDate2(temp.getSendTime());
        }
        if (StringUtil.isNotEmpty(temp.getSenderName())) {
            this.setExtendStr1(temp.getSenderName());
        }

        this.setSampleTypeArr(temp.getSampleTypeArr());
    }

    public String[] getSampleTypeArr() {
        if (StringUtil.isNotNull(this.sampleTypeArr)) {
            return this.sampleTypeArr;
        } else if (StringUtils.isNotNullAndEmpty(this.getSampleType())) {
            return Strings.split(this.getSampleType(), ',');
        }
        return null;
    }

    public List<String> getReportMethodArr() {
        if (this.reportMethodArr.size() > 0) {
            return this.reportMethodArr;
        } else if (StringUtils.isNotNullAndEmpty(this.getReportMethod())) {
            return Arrays.asList(Strings.split(this.getReportMethod(), ','));
        }
        return new ArrayList<>();
    }

    public void setSampleTypeArr(String[] sampleTypeArr) {
        this.sampleTypeArr = sampleTypeArr;
        if (StringUtil.isNotNull(sampleTypeArr)) {
            this.setSampleType(String.join(",", sampleTypeArr));
        } else {
            this.setSampleType("");
        }
    }

    public void setReportMethodArr(List<String> reportMethodArr) {
        this.reportMethodArr = reportMethodArr;
        if (StringUtil.isNotNull(reportMethodArr)) {
            this.setReportMethod(String.join(",", reportMethodArr));
        } else {
            this.setReportMethod("");
        }
    }

    /**
     * 合同id
     */
    @Transient
    private String contractId;

    /**
     * 合同名称
     */
    @Transient
    private String contractName;

    /**
     * 合同编号
     */
    @Transient
    private String contractCode;

    /**
     * 合同开始时间
     */
    @Transient
    private String timeBegin;

    /**
     * 合同结束时间
     */
    @Transient
    private String timeEnd;

    /**
     * 总金额
     */
    @Transient
    private BigDecimal totalAmount;

    /**
     * 合同状态 0未签  1已签 EnumContractStatus
     */
    @Transient
    private Integer contractStatus;

    /**
     * 未上传报告数量
     */
    @Transient
    private Integer notUploadCount;

    /**
     * 监测类型数组
     */
    @Transient
    private String[] sampleTypeArr;

    /**
     * 报告出具方式数组
     */
    @Transient
    private List<String> reportMethodArr = new ArrayList<>();

    /**
     * 项目负责人id
     */
    @Transient
    private String leaderId;

    /**
     * 编制报告人id
     */
    @Transient
    private String reportMakerId;

    /**
     * 编制方案人id
     */
    @Transient
    private String schemeMakerId;

    /**
     * 现场负责人id
     */
    @Transient
    private String spotPersonId;

    /**
     * 监督人id
     */
    @Transient
    private String supervisorId;

    /**
     * 要求完成时间
     */
    @Transient
    private Date deadLine;

    /**
     * 预计出具报告时间
     */
    @Transient
    private Date reportDate;

    /**
     * 预计完成分析日期
     */
    @Transient
    private Date requireAnalyzeDate;

    /**
     * 预计完成采样日期
     */
    @Transient
    private Date requireSamplingDate;

    /**
     * 责任人（质量控制计划）
     */
    @Transient
    private String responsePerson;

    /**
     * 测试项目或具体要求的文字说明
     */
    @Transient
    private String requires;

    /**
     * 测试标准文字说明
     */
    @Transient
    private String testMethodRequires;

    /**
     * 送样单备注
     */
    @Transient
    private String receiveRemark;

    /**
     * 是否同意使用非标准方法
     */
    @Transient
    private Boolean isUseMethod;

    /**
     * 是否评价
     */
    @Transient
    private Boolean isEvaluate;

    /**
     * 是否警告
     */
    @Transient
    private Boolean isWarning;

    /**
     * 提前警告天数
     */
    @Transient
    private Integer warningDay;

    /**
     * 是否反馈
     */
    @Transient
    private Boolean isFeedback;

    /**
     * 是否拟订合同
     */
    @Transient
    private Boolean isContract;

    /**
     * 分包单位
     */
    @Transient
    private String subName;

    /**
     * 分包项目
     */
    @Transient
    private String subItems;

    /**
     * 分包方式(枚举EnumSubMethod:0.无1.客户指定2.本站联系)
     */
    @Transient
    private Integer subMethod;

    /**
     * 分包情况(枚举EnumOutSourcing: 0.不分包1.全部分包2.部分分包)
     */
    @Transient
    private Integer isOutsourcing;

    /**
     * 是否编制方案
     */
    @Transient
    private Boolean isMakePlan;

    /**
     * 辅助编制报告人id
     */
    @Transient
    private String reportMakerIIId;

    /**
     * 质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）
     */
    @Transient
    private Integer qcGrade;

    /**
     * 质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核)
     */
    @Transient
    private Integer qcType;

    /**
     * 质量来源（质量控制——外部质控）
     */
    @Transient
    private String qcSource;

    /**
     * 判断依据（质量控制计划）
     */
    @Transient
    private String judgment;

    /**
     * 收款状态
     */
    @Transient
    private Integer collectionStatus;

    /**
     * 收款进度（已收款，坏账，总金额）
     */
    @Transient
    private List<BigDecimal> collectionDetail = new ArrayList<>();

    /**
     * 未采样品数
     */
    @Transient
    private Integer notSampled;

    /**
     * 检测情况 检毕样品数/总样品数
     */
    @Transient
    private String analyzeSummary;

    /**
     * 检测进度（已出证样品数，已检毕样品数，未检毕样品数）
     */
    @Transient
    private List<Integer> analyzeDetail = new ArrayList<>();

    /**
     * 报告进度（已签发报告数、实际报告数）
     */
    @Transient
    private List<Integer> reportDetail = new ArrayList<>();

    /**
     * 变动状态
     */
    @Transient
    private Integer dataChangeStatus = EnumPRO.EnumDataChangeStatus.未变更.getValue();

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 项目类型编码
     */
    @Transient
    private String projectTypeCode;

    /**
     * 登记人名称
     */
    @Transient
    private String inceptPersonName;

    /**
     * 负责人名称
     */
    @Transient
    private String leaderName;

    /**
     * 编制报告人名称
     */
    @Transient
    private String reportMakerName;

    /**
     * 最新意见
     */
    @Transient
    private String lastNewOpinion;

    /**
     * 图片转化后的base64的字符串
     */
    @Transient
    private String base64Content;

    /**
     * 送样单id
     */
    @Transient
    private String receiveId;

    /**
     * 监测计划ids
     */
    @Transient
    private List<String> propertyIds;

    /**
     * 是否污染源
     */
    @Transient
    private Boolean pollution;

    @Transient
    private List<String> sampleTypeIds;

    @Transient
    private List<String> sampleTypeNames;

    /**
     * 是否更新方案
     */
    @Transient
    private Boolean scheme;

    /**
     * 是否踏勘标记，true已踏勘，false未踏勘
     */
    @Transient
    private Boolean exploreIndicator = Boolean.FALSE;

    /**
     * 工作流标记
     */
    @Transient
    private String workflowId;

    /**
     * 传输用的冗余字段
     */
    @Transient
    private String extendField;

    @Transient
    private List<Map<String, Object>> enterpriseList;

    @Transient
    private List<String> enterpriseIdWithScheme;

    /**
     * 子项目个数
     */
    @Transient
    private Integer subProjectCount;

    /**
     * 办结子项目个数
     */
    @Transient
    private Integer subEndProjectCount;

    /**
     * 是否自动生成编号
     */
    @Transient
    private Boolean isGeneratedProCode = Boolean.TRUE;
}