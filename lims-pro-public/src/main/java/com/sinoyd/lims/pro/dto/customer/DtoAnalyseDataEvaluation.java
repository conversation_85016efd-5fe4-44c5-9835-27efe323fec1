package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 分析项目评价返回数据
 *
 * <AUTHOR>
 * @version ：v1.0.0
 * @date ：2021/10/29
 */
@Data
public class DtoAnalyseDataEvaluation{

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 分析项目名称
     */
    private String analyseItemName;

    /**
     * 评价标准
     */
    private String evaluation;

    /**
     * 评价等级
     */
    private String evaluationLevel;

    /**
     * 上限
     */
    private String upperLimit;

    /**
     * 上限符号
     */
    private String upperLimitSymble;

    /**
     * 下限
     */
    private String lowerLimit;

    /**
     * 下限符号
     */
    private String lowerLimitSymble;

    /**
     * 出征结果
     */
    private String testValue;

    /**
     * 是否合格
     */
    private String isQualified;

    /**
     * 修约后的检测结果
     */
    private BigDecimal testValueDecimal;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 是否排放速率
     */
    private Boolean isEmissionRate;

    /**
     * 允许排放速率
     */
    private String allowEmissionRate;

    /**
     * 排放速率
     */
    private String emissionRate;

    /**
     * 排放速率判定
     */
    private String emissionRateJudge;

}
