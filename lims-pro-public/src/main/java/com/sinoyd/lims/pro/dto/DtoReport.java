package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Report;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 报告实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Report")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoReport extends Report {

    /**
     * 报告类型名称
     */
    @Transient
    private String reportTypeName;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 项目等级
     */
    @Transient
    private Integer grade;

    /**
     * 委托方id
     */
    @Transient
    private String customerId;

    /**
     * 委托方名称
     */
    @Transient
    private String customerName;

    /**
     * 委托方名称
     */
    @Transient
    private String projectStatus;

    /**
     * 编制报告人id
     */
    @Transient
    private String reportMakerId;

    /**
     * 编制报告人名称
     */
    @Transient
    private String reportMakerName;

    /**
     * 处理状态
     */
    @Transient
    private Integer dealStatus;

    /**
     * 提交时间
     */
    @Transient
    private Date submitTime;

    /**
     * 意见
     */
    @Transient
    private String comment;

    /**
     * 关联类型
     */
    @Transient
    private Integer objectType;

    /**
     * 关联id集合
     */
    @Transient
    private List<String> objectIds = new ArrayList<>();

    /**
     * 报表生成id
     */
    @Transient
    private Map<String, Object> map;

    /**
     * 模板配置id
     */
    @Transient
    private String configId;

    /**
     * 排序后的点位id列表
     */
    @Transient
    private List<String> sortFolderIdList;

    /**
     * 排序后的点位信息列表
     */
    @Transient
    private List<DtoReportFolderSortInfo> folderSortInfoList;

    /**
     * 分析项目排序名称
     */
    @Transient
    private String analyseItemSortName;

    /**
     * 点位排序名称
     */
    @Transient
    private String folderSortName;

    /**
     * 受检方联系人
     */
    @Transient
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    @Transient
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    @Transient
    private String inspectedAddress;

    /**
     * 受检单位
     */
    @Transient
    private String inspectedEnt;

    /**
     * 受检单位id
     */
    @Transient
    private String inspectedEntId;

    /**
     * 签到日期
     */
    @Transient
    private String signDate;

    /**
     * 是否推送
     */
    @Transient
    private Integer isPush = 0;

    /**
     * 发放状态文本
     */
    @Transient
    private String grantStatusText;

    /**
     * 发放信息
     */
    @Transient
    private DtoExpressageInfo expressageInfo;
}