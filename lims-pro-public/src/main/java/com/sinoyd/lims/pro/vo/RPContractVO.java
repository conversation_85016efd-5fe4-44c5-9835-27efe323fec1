package com.sinoyd.lims.pro.vo;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.annotate.RegulatoryPlatformField;
import com.sinoyd.lims.pro.annotate.SoapResponseIgnore;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import lombok.Data;

import javax.xml.bind.annotation.*;

import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_ITEM_PREFIX;
import static com.sinoyd.lims.pro.constants.SoapBodyConstants.LABEL_METHOD_NAME_REPLACE;

/**
 * 监管平台合同信息数据实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@Data
@XmlRootElement(name = LABEL_ITEM_PREFIX + LABEL_METHOD_NAME_REPLACE)
@XmlAccessorType(XmlAccessType.FIELD)
public class RPContractVO {

    /**
     * 合同id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ID")
    @RegulatoryPlatformField(fieldName = "合同id", nullable = false)
    private String id;

    /**
     * LIMS系统合同id
     */
    @XmlTransient
    private String orderContractId;

    /**
     * LIMS系统合同名称
     */
    @XmlTransient
    private String orderContractName;

    /**
     * LIMS系统合同编号
     */
    @XmlTransient
    private String orderContractCode;

    /**
     * 是否关联合同
     * </p>
     * 用于合同列表查询 <br/>
     * 如果是LIMS来源合同，则意义为是否关联监管平台合同 <br/>
     * 如果是监管平台来源合同，则表示为是否关联LIMS合同 <br/>
     */
    @XmlTransient
    private Boolean isRelationContract;

    /**
     * 合同名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "BT")
    @RegulatoryPlatformField(fieldName = "合同名称", nullable = false)
    private String contractName;

    /**
     * 合同性质
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "XZ")
    @RegulatoryPlatformField(fieldName = "合同性质", nullable = false)
    private String contractNature;

    /**
     * 委托方id（甲方id）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "JFBZ")
    @RegulatoryPlatformField(fieldName = "甲方id", nullable = false)
    private String entrustingUnitId;

    /**
     * 委托方名称（甲方名称）
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "JFNAME")
    @RegulatoryPlatformField(fieldName = "甲方名称", nullable = false)
    private String entrustingUnitName;

    /**
     * 乙方id
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "YFBZ")
    @RegulatoryPlatformField(fieldName = "乙方id")
    private String partyBId;

    /**
     * 乙方名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "YFNAME")
    @RegulatoryPlatformField(fieldName = "乙方名称")
    private String partyBName;

    /**
     * 合同金额
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "JE")
    @RegulatoryPlatformField(fieldName = "合同金额", nullable = false)
    private String contractAmount;

    /**
     * 履行周期开始
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ZQS")
    @RegulatoryPlatformField(fieldName = "履行周期开始", nullable = false)
    private String startTime;

    /**
     * 履行周期结束
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "ZQZ")
    @RegulatoryPlatformField(fieldName = "履行周期结束", nullable = false)
    private String endTime;

    /**
     * 合同概述
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "RWGS")
    @RegulatoryPlatformField(fieldName = "合同概述", nullable = false)
    private String contractSummary;

    /**
     * 监管平台附件返回名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FilePath")
    @RegulatoryPlatformField(fieldName = "监管平台附件返回名称")
    private String filePath;

    /**
     * 合同附件名称
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "FileName")
    @SoapResponseIgnore
    @RegulatoryPlatformField(fieldName = "合同附件名称")
    private String fileName;

    /**
     * 合同状态
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "IS_CHECK")
    @RegulatoryPlatformField(fieldName = "合同状态", nullable = false)
    private String isCheck;

    /**
     * 是否分包
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "IS_SUBPACKAGE")
    @RegulatoryPlatformField(fieldName = "是否分包", nullable = false)
    private String isSubpackage;

    /**
     * 分包金额
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SUBPACKAGE_MONEY")
    @RegulatoryPlatformField(fieldName = "分包金额", nullable = false)
    private String subpackageMoney;

    /**
     * 分包机构
     */
    @XmlElement(name = LABEL_ITEM_PREFIX + "SUBPACKAGE_ORG")
    @RegulatoryPlatformField(fieldName = "分包机构")
    private String subpackageOrg;


    /**
     * 无参构造函数
     */
    public RPContractVO() {
    }

    /**
     * 构造函数（根据本系统订单合同创建监管平台合同信息）
     *
     * @param contract 订单合同
     */
    public RPContractVO(DtoOrderContract contract) {
        this();
        this.id = "0";
        this.orderContractId = contract.getId();
        this.orderContractName = contract.getContractName();
        this.orderContractCode = contract.getContractCode();
        this.contractName = contract.getContractName();
        this.contractNature = contract.getContractNature();
        this.entrustingUnitId = contract.getShanghaiEntId();
        this.entrustingUnitName = contract.getShanghaiEntName();
        this.contractAmount = contract.getTotalAmount().toString();
        this.startTime = DateUtil.dateToString(contract.getExcuteStartTime(), DateUtil.YEAR);
        this.endTime = DateUtil.dateToString(contract.getExcuteEndTime(), DateUtil.YEAR);
        this.contractSummary = contract.getSummary();
        this.isCheck = "1";
        this.isSubpackage = contract.getIsHavingSub() ? "1" : "0";
        this.subpackageMoney = StringUtil.isNotNull(contract.getSubAmount()) ? contract.getSubAmount().toString() : "0";
        this.subpackageOrg = StringUtil.isNotNull(contract.getSubOrgs()) ? contract.getSubOrgs() : "";
        this.isRelationContract = contract.getIsHavingPut();
    }

    /**
     * 构造函数（根据本系统订单合同创建监管平台合同信息）
     *
     * @param contract      订单合同
     * @param isLoadRpField 是否加载监管平台字段
     */
    public RPContractVO(DtoOrderContract contract, boolean isLoadRpField) {
        this();
        this.id = contract.getCId();
        this.orderContractId = contract.getId();
        this.orderContractName = contract.getContractName();
        this.orderContractCode = contract.getContractCode();
        if (isLoadRpField){
            this.contractName = contract.getContractName();
            this.contractNature = contract.getContractNature();
        }
        this.entrustingUnitId = contract.getShanghaiEntId();
        this.entrustingUnitName = contract.getShanghaiEntName();
        this.contractAmount = contract.getTotalAmount().toString();
        this.startTime = DateUtil.dateToString(contract.getExcuteStartTime(), DateUtil.YEAR);
        this.endTime = DateUtil.dateToString(contract.getExcuteEndTime(), DateUtil.YEAR);
        this.contractSummary = contract.getSummary();
        this.isCheck = "1";
        this.isSubpackage = contract.getIsHavingSub() ? "1" : "0";
        this.subpackageMoney = StringUtil.isNotNull(contract.getSubAmount()) ? contract.getSubAmount().toString() : "0";
        this.subpackageOrg = StringUtil.isNotNull(contract.getSubOrgs()) ? contract.getSubOrgs() : "";
        this.isRelationContract = contract.getIsHavingPut();
    }
}
