package com.sinoyd.lims.pro.vo;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * 上海监管平台方案监测数据VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/12
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "MonitorTaskPlan")
public class RPTaskPlanMonitorVO {

    /**
     * 方案列表
     */
    @XmlElementWrapper(name = "Scheme")
    @XmlElement(name = "Item")
    private List<RPTaskPlanSchemeVO> schemeList;

    /**
     * 方案任务项列表
     */
    @XmlElementWrapper(name = "Task")
    @XmlElement(name = "Item")
    private List<RPTaskPlanTaskVO> taskList;

    /**
     * 方案项目列表
     */
    @XmlElementWrapper(name = "Project")
    @XmlElement(name = "Item")
    private List<RPTaskPlanTestVO> projectList;

    /**
     * 日期数据列表
     */
    @XmlElementWrapper(name = "Plan")
    @XmlElement(name = "Item")
    private List<RPTaskPlanDateVO> dateList;

}
