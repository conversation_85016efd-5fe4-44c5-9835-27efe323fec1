package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 分析数据存储实体的JSON对象
 * <AUTHOR>
 * @version V1.0.0 2020/08/31
 * @since V100R001
 */
@Data
public class DtoAnalyseOriginalJson {

    /**
     * 别名
     */
    private String alias;

    /**
     * 默认值
     */
    private String defaultValue;


    /**
     * 是否必填
     */
    private Boolean isMust;


    /**
     * 是否可编辑
     */
    private Boolean isEditable;


    /**
     * 排序号
     */
    private Integer orderNum;

    /**
     * 公式参数检出限
     */
    private String detectionLimit;

    /**
     * 计算方式：枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零
     */
    private Integer calculationMode;

    /**
     * 斜杠计算值
     */
    private Integer slashValue;
}
