package com.sinoyd.lims.pro.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.lims.pro.entity.SamplingAchievementDetails;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * DtoSamplingAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SamplingAchievementDetails")
@Data
@DynamicInsert
public class DtoSamplingAchievementDetails extends SamplingAchievementDetails {

    private static final long serialVersionUID = 1L;

    @Transient
    private String projectId;

    @Transient
    private String orderId;

    @Transient
    private String sampleFolderName;

    @Transient
    @Excel(name = "采样人员",needMerge = true,orderNum = "45",width = 11)
    private String samplingPersonNames;

    @Transient
    @Excel(name = "检测类型",needMerge = true,orderNum = "35",width = 11)
    private String sampleTypeName;

    public DtoSamplingAchievementDetails(){}

}
