package com.sinoyd.lims.monitor.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;

import java.util.Collection;
import java.util.List;


/**
 * FixedPoint2Test操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPoint2TestService extends IBaseJpaService<DtoFixedPoint2Test, String> {
    /**
     * 根据例行点位id集合查询关联测试项目数据
     *
     * @param fixedPointIds 例行点位id集合
     * @return 关联测试项目数据
     */
    List<DtoFixedPoint2Test> findByFixedPointIdIn(Collection<String> fixedPointIds);
}