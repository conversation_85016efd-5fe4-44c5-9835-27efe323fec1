package com.sinoyd.lims.monitor.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoWater;

import java.util.List;


/**
 * Fixedpoint操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedpointService extends IBaseJpaService<DtoFixedpoint, String> {

    /**
     * 点位重复判断
     *
     * @param dtoFixedpoint 点位对象
     * @return 是否重复，true: 重复； false: 不重复
     */
    Boolean duplicatePointCheck(DtoFixedpoint dtoFixedpoint);

    /**
     * 保存测试项目
     *
     * @param entity 实体
     */
    void saveTest(DtoFixedpoint entity);

    /**
     * 选择关联点位
     *
     * @param dtoFixedpoint 点位信息
     */
    void selectRelationPoints(DtoFixedpoint dtoFixedpoint);


    /**
     * 获取点位的评价标准对象列表
     *
     * @param dataList 点位数据
     * @return 点位的评价标准对象列表
     */
    List<DtoEvaluationCriteria> loadEvaluationCriteria(List<DtoFixedpoint> dataList);

    /**
     * 获取点位的评价等级对象列表
     *
     * @param dataList 点位数据
     * @return 点位的评价等级对象列表
     */
    List<DtoEvaluationLevel> loadEvaluationLevel(List<DtoFixedpoint> dataList);

    /**
     * 获取点位的所属水体列表
     *
     * @param fixedPointExpendList 点位扩展信息
     * @return 点位的所属水体列表
     */
    List<DtoWater> loadWater(List<DtoFixedPointExpend> fixedPointExpendList);

    /**
     * 获取点位的所属水体列表(根据点位数据获取)
     *
     * @param fixedPointList 点位信息
     * @return 点位的所属水体列表
     */
    List<DtoWater> loadWaterByFixedPoint(List<DtoFixedpoint> fixedPointList);

    /**
     * 获取点位的扩展信息
     *
     * @param dataList 点位数据
     * @return 点位的扩展信息
     */
    List<DtoFixedPointExpend> loadFixedPointExpendList(List<DtoFixedpoint> dataList);

    /**
     * 删除关联测试项目信息
     *
     * @param dtoFixedpoint 点位实体
     */
    void deleteTest(DtoFixedpoint dtoFixedpoint);

    /**
     * 删除关联监测计划信息
     *
     * @param dtoFixedpoint 点位实体
     */
    void deleteRelationProperty(DtoFixedpoint dtoFixedpoint);

    /**
     * 选择关联监测计划
     *
     * @param dtoFixedpoint 点位信息
     */
    void selectRelationProperty(DtoFixedpoint dtoFixedpoint);

    /**
     * 迁移点位拓展数据到新表中
     *
     * @param pointType 点位类型
     */
    void moveFixedPointExtendData(Integer pointType);

    /**
     * 处理点位类型名称
     *
     * @param dtoFixedpoint      点位数据
     * @param folderTypeCodeList 点位类型常量集合
     */
    void loadFolderTypeNames(DtoFixedpoint dtoFixedpoint, List<DtoCode> folderTypeCodeList);

    /**
     * 批量修改频次污染源点位
     *
     * @param fixedPoint2Test 前端传参
     */
    void updateTimesOrder(DtoFixedPoint2Test fixedPoint2Test);

    /**
     * 批量修改频次环境质量点位
     *
     * @param propertyPoint2Test 前端传参
     */
    void updatePointTimesOrder(DtoPropertyPoint2Test propertyPoint2Test);

    /**
     * 复制污染源点位
     *
     * @param fixedpoint 点位实体
     */
    DtoFixedpoint copyPoint(DtoFixedpoint fixedpoint);


    /**
     * 复制污染源点位测试项目
     *
     * @param fixedPoint 点位数据
     */
    void copyPointTest(DtoFixedpoint fixedPoint);

    /**
     * 根据企业id查询企业下点位数据
     *
     * @param enterpriseId 企业id
     * @return 点位数据集合
     */
    List<DtoFixedpoint> findByEnterpriseId(String enterpriseId);
}

