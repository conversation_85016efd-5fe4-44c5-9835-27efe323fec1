package com.sinoyd.lims.query.repository;

import com.sinoyd.lims.query.dto.DtoViewField;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.Collection;
import java.util.List;


/**
 * ViewField数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
public interface ViewFieldRepository extends IBaseJpaRepository<DtoViewField, String> {

    /**
     *
     * @param viewIds 视图ids
     * @return 配置视图内容
     */
    List<DtoViewField> findByViewIdIn(List<String> viewIds);
}