package com.sinoyd.lims.query.controller;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.query.dto.customer.DtoSelectBaseItem;
import com.sinoyd.lims.query.dto.customer.DtoTableColumnName;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.query.service.ItemService;
import com.sinoyd.lims.query.criteria.ItemCriteria;
import com.sinoyd.lims.query.dto.DtoItem;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;
import java.util.Map;


/**
 * Item服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Api(tags = "示例: Item服务")
 @RestController
 @RequestMapping("api/query/item")
 public class ItemController extends BaseJpaController<DtoItem, String,ItemService> {


    /**
     * 分页动态条件查询Item
     *
     * @param itemCriteria 条件参数
     * @return RestResponse<List < Item>>
     */
    @ApiOperation(value = "分页动态条件查询Item", notes = "分页动态条件查询Item")
    @GetMapping
    public RestResponse<List<DtoItem>> findByPage(ItemCriteria itemCriteria) {
        PageBean<DtoItem> pageBean = super.getPageBean();
        RestResponse<List<DtoItem>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, itemCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Item
     *
     * @param id 主键id
     * @return RestResponse<DtoItem>
     */
    @ApiOperation(value = "按主键查询Item", notes = "按主键查询Item")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoItem> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoItem> restResponse = new RestResponse<>();
        DtoItem item = service.findOne(id);
        restResponse.setData(item);
        restResponse.setRestStatus(StringUtil.isNull(item) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Item
     *
     * @param item 实体列表
     * @return RestResponse<DtoItem>
     */
    @ApiOperation(value = "新增Item", notes = "新增Item")
    @PostMapping
    public RestResponse<DtoItem> create(@RequestBody DtoItem item) {
        RestResponse<DtoItem> restResponse = new RestResponse<>();
        restResponse.setData(service.save(item));
        return restResponse;
    }

    /**
     * 新增Item
     *
     * @param item 实体列表
     * @return RestResponse<DtoItem>
     */
    @ApiOperation(value = "修改Item", notes = "修改Item")
    @PutMapping
    public RestResponse<DtoItem> update(@RequestBody DtoItem item) {
        RestResponse<DtoItem> restResponse = new RestResponse<>();
        restResponse.setData(service.update(item));
        return restResponse;
    }

    /**
     * "根据id批量删除Item
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Item", notes = "根据id批量删除Item")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "复制自定义查询", notes = "复制自定义查询")
    @PostMapping("/copy")
    public RestResponse<String> copyItem(@RequestBody DtoSelectBaseItem dtoSelectBaseItem) {
        RestResponse<String> restResp = new RestResponse<>();
        service.copyItem(dtoSelectBaseItem.getItemId());
        return restResp;
    }

    @ApiOperation(value = "实现查询功能", notes = "实现查询功能")
    @PostMapping("/selectBaseByItem")
    public RestResponse<Map<String, Object>> selectBaseByItem(@RequestBody DtoSelectBaseItem dtoSelectBaseItem) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        try {
            restResp.setData(service.selectBaseByItem(dtoSelectBaseItem.getItemId(), dtoSelectBaseItem.getDbConditionColumns(), dtoSelectBaseItem.getBasePageBean()));
        } catch (Exception ex) {
            throw new BaseException("查询内容存在问题，请确认！");
        }
        return restResp;
    }

    @ApiOperation(value = "column", notes = "column")
    @GetMapping(path = "/itemCount")
    public RestResponse<Integer> getItemCount(@RequestParam(name = "viewId") String viewId) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.getItemCount(viewId));
        return restResponse;
    }

    @ApiOperation(value = "查询校验", notes = "查询校验")
    @PostMapping("/checkoutSql")
    public RestResponse<Boolean> checkoutSql(@RequestBody DtoSelectBaseItem dtoSelectBaseItem) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setData(service.checkoutSql(dtoSelectBaseItem.getItemId(), dtoSelectBaseItem.getDbConditionColumns(), dtoSelectBaseItem.getBasePageBean()));
        return restResp;
    }
}