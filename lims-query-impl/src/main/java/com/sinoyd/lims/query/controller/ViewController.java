package com.sinoyd.lims.query.controller;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.query.dto.customer.DtoTableColumnName;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.query.service.ViewService;
import com.sinoyd.lims.query.criteria.ViewCriteria;
import com.sinoyd.lims.query.dto.DtoView;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * View服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Api(tags = "示例: View服务")
 @RestController
 @RequestMapping("api/query/view")
 public class ViewController extends BaseJpaController<DtoView, String,ViewService> {


    /**
     * 分页动态条件查询View
     *
     * @param viewCriteria 条件参数
     * @return RestResponse<List < View>>
     */
    @ApiOperation(value = "分页动态条件查询View", notes = "分页动态条件查询View")
    @GetMapping
    public RestResponse<List<DtoView>> findByPage(ViewCriteria viewCriteria) {
        PageBean<DtoView> pageBean = super.getPageBean();
        RestResponse<List<DtoView>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, viewCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询View
     *
     * @param id 主键id
     * @return RestResponse<DtoView>
     */
    @ApiOperation(value = "按主键查询View", notes = "按主键查询View")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoView> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoView> restResponse = new RestResponse<>();
        DtoView view = service.findOne(id);
        restResponse.setData(view);
        restResponse.setRestStatus(StringUtil.isNull(view) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增View
     *
     * @param view 实体列表
     * @return RestResponse<DtoView>
     */
    @ApiOperation(value = "新增View", notes = "新增View")
    @PostMapping
    public RestResponse<DtoView> create(@RequestBody DtoView view) {
        RestResponse<DtoView> restResponse = new RestResponse<>();
        restResponse.setData(service.save(view));
        return restResponse;
    }

    /**
     * 单个修改已选字段显示名称
     *
     * @param view 实体列表
     * @return RestResponse<DtoView>
     */
    @ApiOperation(value = "单个修改已选字段显示名称", notes = "单个修改已选字段显示名称")
    @PostMapping("/viewField")
    public RestResponse<DtoView> updateViewField(@RequestBody DtoView view) {
        RestResponse<DtoView> restResponse = new RestResponse<>();
        restResponse.setData(service.updateViewField(view));
        return restResponse;
    }

    /**
     * 新增View
     *
     * @param view 实体列表
     * @return RestResponse<DtoView>
     */
    @ApiOperation(value = "修改View", notes = "修改View")
    @PutMapping
    public RestResponse<DtoView> update(@RequestBody DtoView view) {
        RestResponse<DtoView> restResponse = new RestResponse<>();
        restResponse.setData(service.update(view));
        return restResponse;
    }

    /**
     * "根据id批量删除View
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除View", notes = "根据id批量删除View")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "table", notes = "table")
    @GetMapping(path = "/tableNames")
    public RestResponse<List<String>> getTableNames() {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        try {
            restResponse.setData(service.getDBTables());
        } catch (Exception ex) {
            throw new BaseException(ex.getMessage());
        }
        return restResponse;
    }

    @ApiOperation(value = "column", notes = "column")
    @GetMapping(path = "/tableColumn")
    public RestResponse<List<DtoTableColumnName>> getTableColumns(@RequestParam(name = "tableName") String tableName) {
        RestResponse<List<DtoTableColumnName>> restResponse = new RestResponse<>();
        try {
            restResponse.setData(service.getTableColumns(tableName));
        } catch (Exception ex) {
            throw new BaseException(ex.getMessage());
        }
        return restResponse;
    }

    @ApiOperation(value = "typeName", notes = "typeName")
    @GetMapping(path = "/typeName")
    public RestResponse<Map<String,String>> getTypeNames() {
        RestResponse<Map<String,String>> restResponse = new RestResponse<>();
        restResponse.setData(service.getTypeNameList());
        return restResponse;
    }
}