package com.sinoyd.lims.query.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.query.service.ViewFieldService;
import com.sinoyd.lims.query.criteria.ViewFieldCriteria;
import com.sinoyd.lims.query.dto.DtoViewField;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.ArrayList;
import java.util.List;


/**
 * ViewField服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Api(tags = "示例: ViewField服务")
 @RestController
 @RequestMapping("api/query/viewField")
 public class ViewFieldController extends BaseJpaController<DtoViewField, String,ViewFieldService> {


    /**
     * 分页动态条件查询ViewField
     * @param viewFieldCriteria 条件参数
     * @return RestResponse<List<ViewField>>
     */
     @ApiOperation(value = "分页动态条件查询ViewField", notes = "分页动态条件查询ViewField")
     @GetMapping
     public RestResponse<List<DtoViewField>> findByPage(ViewFieldCriteria viewFieldCriteria) {
         PageBean<DtoViewField> pageBean = super.getPageBean();
         RestResponse<List<DtoViewField>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, viewFieldCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ViewField
     * @param id 主键id
     * @return RestResponse<DtoViewField>
     */
     @ApiOperation(value = "按主键查询ViewField", notes = "按主键查询ViewField")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoViewField> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoViewField> restResponse = new RestResponse<>();
         DtoViewField viewField = service.findOne(id);
         restResponse.setData(viewField);
         restResponse.setRestStatus(StringUtil.isNull(viewField) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ViewField
     * @param viewField 实体列表
     * @return RestResponse<DtoViewField>
     */
     @ApiOperation(value = "新增ViewField", notes = "新增ViewField")
     @PostMapping
     public RestResponse<DtoViewField> create(@RequestBody DtoViewField viewField) {
         RestResponse<DtoViewField> restResponse = new RestResponse<>();
         restResponse.setData(service.save(viewField));
         return restResponse;
      }

     /**
     * 新增ViewField
     * @param viewField 实体列表
     * @return RestResponse<DtoViewField>
     */
     @ApiOperation(value = "修改ViewField", notes = "修改ViewField")
     @PutMapping
     public RestResponse<DtoViewField> update(@RequestBody DtoViewField viewField) {
         RestResponse<DtoViewField> restResponse = new RestResponse<>();
         restResponse.setData(service.update(viewField));
         return restResponse;
      }

    /**
     * "根据id批量删除ViewField
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ViewField", notes = "根据id批量删除ViewField")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }