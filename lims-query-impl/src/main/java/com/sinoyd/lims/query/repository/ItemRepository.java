package com.sinoyd.lims.query.repository;

import com.sinoyd.lims.query.dto.DtoItem;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * Item数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
public interface ItemRepository extends IBaseJpaRepository<DtoItem, String> {

    /**
     * 通过类型获取自定义查询集合
     * @param viewId 查询类型
     * @return 自定义集合
     */
    List<DtoItem> findByViewId(String viewId);
}