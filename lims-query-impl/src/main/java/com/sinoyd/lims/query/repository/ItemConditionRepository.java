package com.sinoyd.lims.query.repository;

import com.sinoyd.lims.query.dto.DtoItemColumn;
import com.sinoyd.lims.query.dto.DtoItemCondition;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * ItemCondition数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
public interface ItemConditionRepository extends IBaseJpaRepository<DtoItemCondition, String> {

    /**
     *
     * @param itemIds 自定义检索ids
     * @return 显示列
     */
    List<DtoItemCondition> findByItemIdIn(List<String> itemIds);
}