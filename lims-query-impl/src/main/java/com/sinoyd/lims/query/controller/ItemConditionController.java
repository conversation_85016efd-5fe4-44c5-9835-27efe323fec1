package com.sinoyd.lims.query.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.query.service.ItemConditionService;
import com.sinoyd.lims.query.criteria.ItemConditionCriteria;
import com.sinoyd.lims.query.dto.DtoItemCondition;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ItemCondition服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Api(tags = "示例: ItemCondition服务")
 @RestController
 @RequestMapping("api/query/itemCondition")
 public class ItemConditionController extends BaseJpaController<DtoItemCondition, String,ItemConditionService> {


    /**
     * 分页动态条件查询ItemCondition
     * @param itemConditionCriteria 条件参数
     * @return RestResponse<List<ItemCondition>>
     */
     @ApiOperation(value = "分页动态条件查询ItemCondition", notes = "分页动态条件查询ItemCondition")
     @GetMapping
     public RestResponse<List<DtoItemCondition>> findByPage(ItemConditionCriteria itemConditionCriteria) {
         PageBean<DtoItemCondition> pageBean = super.getPageBean();
         RestResponse<List<DtoItemCondition>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, itemConditionCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ItemCondition
     * @param id 主键id
     * @return RestResponse<DtoItemCondition>
     */
     @ApiOperation(value = "按主键查询ItemCondition", notes = "按主键查询ItemCondition")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoItemCondition> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoItemCondition> restResponse = new RestResponse<>();
         DtoItemCondition itemCondition = service.findOne(id);
         restResponse.setData(itemCondition);
         restResponse.setRestStatus(StringUtil.isNull(itemCondition) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ItemCondition
     * @param itemCondition 实体列表
     * @return RestResponse<DtoItemCondition>
     */
     @ApiOperation(value = "新增ItemCondition", notes = "新增ItemCondition")
     @PostMapping
     public RestResponse<DtoItemCondition> create(@RequestBody DtoItemCondition itemCondition) {
         RestResponse<DtoItemCondition> restResponse = new RestResponse<>();
         restResponse.setData(service.save(itemCondition));
         return restResponse;
      }

     /**
     * 新增ItemCondition
     * @param itemCondition 实体列表
     * @return RestResponse<DtoItemCondition>
     */
     @ApiOperation(value = "修改ItemCondition", notes = "修改ItemCondition")
     @PutMapping
     public RestResponse<DtoItemCondition> update(@RequestBody DtoItemCondition itemCondition) {
         RestResponse<DtoItemCondition> restResponse = new RestResponse<>();
         restResponse.setData(service.update(itemCondition));
         return restResponse;
      }

    /**
     * "根据id批量删除ItemCondition
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ItemCondition", notes = "根据id批量删除ItemCondition")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }