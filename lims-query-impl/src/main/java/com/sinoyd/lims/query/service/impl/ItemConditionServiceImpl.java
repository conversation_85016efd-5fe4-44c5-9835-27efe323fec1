package com.sinoyd.lims.query.service.impl;

import com.sinoyd.lims.query.dto.DtoItemCondition;
import com.sinoyd.lims.query.repository.ItemConditionRepository;
import com.sinoyd.lims.query.service.ItemConditionService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * ItemCondition操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Service
public class ItemConditionServiceImpl extends BaseJpaServiceImpl<DtoItemCondition,String,ItemConditionRepository> implements ItemConditionService {

    @Override
    public void findByPage(PageBean<DtoItemCondition> pb, BaseCriteria itemConditionCriteria) {
        pb.setEntityName("DtoItemCondition a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, itemConditionCriteria);
    }
}