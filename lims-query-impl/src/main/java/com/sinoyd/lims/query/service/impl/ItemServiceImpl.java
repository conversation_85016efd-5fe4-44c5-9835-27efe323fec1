package com.sinoyd.lims.query.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.query.constant.pageSymbolConstant;
import com.sinoyd.lims.query.constant.symbolConstant;
import com.sinoyd.lims.query.dto.*;
import com.sinoyd.lims.query.dto.customer.DtoBasePageBean;
import com.sinoyd.lims.query.dto.customer.DtoDbConditionColumn;
import com.sinoyd.lims.query.enums.EnumQuery;
import com.sinoyd.lims.query.repository.ItemColumnRepository;
import com.sinoyd.lims.query.repository.ItemConditionRepository;
import com.sinoyd.lims.query.repository.ItemRepository;
import com.sinoyd.lims.query.repository.ViewFieldRepository;
import com.sinoyd.lims.query.service.ItemColumnService;
import com.sinoyd.lims.query.service.ItemConditionService;
import com.sinoyd.lims.query.service.ItemService;
import com.sinoyd.lims.query.service.ViewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Item操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
@Service
public class ItemServiceImpl extends BaseJpaServiceImpl<DtoItem, String, ItemRepository> implements ItemService {

    @Value("${spring.datasource.primary.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource.primary.username}")
    private String username;

    @Value("${spring.datasource.primary.url}")
    private String url;

    @Value("${spring.datasource.primary.password}")
    private String password;

    @Autowired
    private ItemColumnService itemColumnService;

    @Autowired
    private ItemConditionService itemConditionService;

    @Autowired
    private ItemColumnRepository itemColumnRepository;

    @Autowired
    private ItemConditionRepository itemConditionRepository;

    @Autowired
    private ViewFieldRepository viewFieldRepository;

    @Autowired
    private ViewService viewService;

    @Override
    public void findByPage(PageBean<DtoItem> pb, BaseCriteria itemCriteria) {
        pb.setEntityName("DtoItem a , DtoView b");
        pb.setSelect("select a,b.typeName");
        String sort = pb.getSort();
        List<String> sortList = new ArrayList<>();
        for (String st : sort.split(",")) {
            //如果是创建时间那么需要确认是哪张表的创建时间
            if (st.contains("createDate")) {
                sortList.add("a." + st);
            }
        }
        pb.setSort(String.join(",", sortList));
        comRepository.findByPage(pb, itemCriteria);
    }

    @Transactional
    @Override
    public DtoItem save(DtoItem entity) {
        super.save(entity);
        DtoItem newItem = setConditionToValue(entity, false);
        itemColumnService.save(newItem.getItemColumnList());
        itemConditionService.save(newItem.getItemCondition());
        return entity;
    }

    /**
     * 复制保存
     *
     * @param entity 保存实体
     * @param isCopy 判断是否复制
     * @return 保存内容
     */
    private DtoItem copySave(DtoItem entity, Boolean isCopy) {
        super.save(entity);
        DtoItem newItem = setConditionToValue(entity, isCopy);
        itemColumnService.save(newItem.getItemColumnList());
        itemConditionService.save(newItem.getItemCondition());
        return entity;
    }

    @Transactional
    @Override
    public DtoItem update(DtoItem entity) {
        if (StringUtil.isNotNull(entity.getId())) {
            DtoItem oriItem = repository.findOne(entity.getId());
            if (StringUtil.isNotNull(oriItem) && !oriItem.getIsDeleted()) {
                //设置验证结果
                entity.setCheckOut(oriItem.getCheckOut());
            }
        }
        super.update(entity);
        DtoItem newItem = setConditionToValue(entity, false);
        //修改显示列
        itemColumnService.update(newItem.getItemColumnList());
        //修改检索内容
        itemConditionService.update(newItem.getItemCondition());
        return newItem;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> itemIds = (List<String>) ids;
        //删除显示列
        List<DtoItemColumn> itemColumnList = itemColumnRepository.findByItemIdIn(itemIds);
        if (StringUtil.isNotEmpty(itemColumnList)) {
            itemColumnService.logicDeleteById(itemColumnList.stream().map(DtoItemColumn::getId).collect(Collectors.toList()));
        }
        //删除检索条件
        List<DtoItemCondition> itemConditionList = itemConditionRepository.findByItemIdIn(itemIds);
        if (StringUtil.isNotEmpty(itemConditionList)) {
            itemConditionService.logicDeleteById(itemConditionList.stream().map(DtoItemCondition::getId).collect(Collectors.toList()));
        }
        //删除自定义检索
        return super.logicDeleteById(ids);
    }

    /**
     * 自定义查询 todo 修改显示查询的时候需要做下处理
     *
     * @param id 自定义查询id
     * @return 自定义查询内容
     */
    @Override
    public DtoItem findOne(String id) {
        DtoItem dtoItem = super.findOne(id);
        //显示字段
        List<DtoItemColumn> itemColumnList = itemColumnRepository.findByItemIdIn(Collections.singletonList(id));

        if (StringUtil.isNotEmpty(itemColumnList)) {
            List<DtoViewField> viewFieldList = viewFieldRepository.findAll(itemColumnList.stream()
                    .map(DtoItemColumn::getViewFieldId).collect(Collectors.toList()));

            for (DtoItemColumn itemColumn : itemColumnList) {
                DtoViewField viewField = viewFieldList.stream().filter(p -> p.getId()
                        .equals(itemColumn.getViewFieldId())).findFirst().orElse(null);
                if (viewField != null) {
                    itemColumn.setDbFieldName(viewField.getDbField());
                    itemColumn.setViewFieldName(viewField.getPageField());
                }
            }
            dtoItem.setItemColumnList(itemColumnList);
        }
        //检索内容
        DtoItemCondition itemCondition = itemConditionRepository.findByItemIdIn(Collections.singletonList(id)).stream().findFirst().orElse(null);
        dtoItem.setItemCondition(itemCondition);
        return dtoItem;
    }

    /**
     * 复制自定义查询
     *
     * @param oldItemId 自定义查询的id
     */
    @Override
    public void copyItem(String oldItemId) {
        DtoItem oldItem = this.findOne(oldItemId);
        DtoItem newItem = new DtoItem();
        newItem.setItemName(oldItem.getItemName() + "复制");
        newItem.setItemColumnList(oldItem.getItemColumnList());
        newItem.setItemCondition(oldItem.getItemCondition());
        newItem.setItemDesc(oldItem.getItemDesc());
        newItem.setUsedTimes(oldItem.getUsedTimes());
        newItem.setViewId(oldItem.getViewId());
        this.copySave(newItem, true);
    }

    /**
     * 自定义个数
     *
     * @param viewId 查询类型
     * @return 个数
     */
    @Override
    public Integer getItemCount(String viewId) {
        return repository.findByViewId(viewId).size();
    }

    /**
     * 校验
     */
    @Transactional
    @Override
    public Boolean checkoutSql(String itemId, List<DtoDbConditionColumn> dbConditionColumnList, DtoBasePageBean basePageBean) {
        Boolean checkOut;
        try {
            this.selectBaseByItem(itemId, dbConditionColumnList, basePageBean);
            checkOut = true;
        } catch (Exception ex) {
            String msg = (StringUtil.isNotNull(ex.getMessage()) && ex.getMessage().contains("验证失败")) ? ex.getMessage() : "验证失败！";
            throw new BaseException(msg);
        }
        if (StringUtil.isNotNull(itemId)) {
            //保存校验情况
            DtoItem item = repository.findOne(itemId);
            item.setCheckOut(checkOut);
            repository.save(item);
        }
        return checkOut;
    }

    /**
     * 实现查询功能 todo 考虑一下sql注入的问题
     *
     * @param itemId                自定义查询内容
     * @param dbConditionColumnList 查询列
     */
    @Override
    public Map<String, Object> selectBaseByItem(String itemId, List<DtoDbConditionColumn> dbConditionColumnList, DtoBasePageBean basePageBean) throws Exception {
        DtoItem item = this.findOne(itemId);
        //获取视图名称 vi_xxx
        DtoView view = viewService.findOne(item.getViewId());
        String selTableName = view.getViewName();
        //获取查询字段 select xxx
        List<DtoItemColumn> itemColumnList = itemColumnRepository.findByItemIdIn(Collections.singletonList(item.getId()));
        String selColumns = "";
        String selOrderBy = "";
        String orderStr = "";
        itemColumnList = itemColumnList.stream().filter(DtoItemColumn::getIsShow).collect(Collectors.toList());
        for (DtoItemColumn itemColumn : itemColumnList) {
            if (itemColumn.getIsShow()) {
                String fieldName = itemColumn.getViewFieldName().replace("（", "").replace("）", "").replace("Guid", "");
                selColumns += itemColumn.getDbFieldName() + " as " + fieldName + ",";
                if (!StringUtil.isNotEmpty(orderStr)) {
                    orderStr = itemColumn.getDbFieldName();
                }
            }
        }
        if ("".equals(selColumns)) {
            throw new BaseException("验证失败，查询字段为空！");
        }
        if (selColumns.length() > 1) {
            selColumns = selColumns.substring(0, selColumns.length() - 1);
        }

        //获取排序情况 order by xxx desc , xxx asc
        List<DtoItemColumn> descColumnList = itemColumnList.stream().filter(p -> p.getSortType().equals(EnumQuery.EnumSortType.倒序.toString()))
                .sorted(Comparator.comparing(DtoItemColumn::getSortSeq)).collect(Collectors.toList());
        List<DtoItemColumn> ascColumnList = itemColumnList.stream().filter(p -> p.getSortType().equals(EnumQuery.EnumSortType.正序.toString()))
                .sorted(Comparator.comparing(DtoItemColumn::getSortSeq)).collect(Collectors.toList());
        selOrderBy = getSelOrderByStr(descColumnList, " desc, ", selOrderBy);
        selOrderBy = getSelOrderByStr(ascColumnList, " asc, ", selOrderBy);

        //获取查询条件 where xxx
        String selWhere = " where 1 = 1";
        DtoItemCondition itemCondition = itemConditionRepository.findByItemIdIn(Collections.singletonList(item.getId())).stream().findFirst().orElse(null);
        if (itemCondition != null) {
            String pgCondition = itemCondition.getPageCondition();
            for (DtoDbConditionColumn conditionColumn : dbConditionColumnList) {
                String repStr = conditionColumn.getDbName();
                Boolean isRep = false;
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.不包含)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.不包含)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.不包含);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            str = str.replace(",", "','");
                            if (str.contains(";")) {
                                //前端在某些情况下的传参中多了一个";",需要去除
                                if (str.startsWith(";")) {
                                    str = str.substring(1,str.length());
                                }
                                str = str.replace(";", "','");
                            }
                            repStr += symbolConstant.不包含 + "('" + str + "')";
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.不等于)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.不等于)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.不等于);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.不等于 + "'" + str + "'";
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.等于)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.等于)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.等于);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.等于 + "'" + str + "'";
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.包含)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.包含)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.包含);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            str = str.replace(",", "','");
                            if (str.contains(";")) {
                                //前端在某些情况下的传参中多了一个";",需要去除
                                if (str.startsWith(";")) {
                                    str = str.substring(1,str.length());
                                }
                                str = str.replace(";", "','");
                            }
                            repStr += symbolConstant.包含 + "('" + str + "')";
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.大于)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.大于)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.大于);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.大于 + str;
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.小于)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.小于)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.小于);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.小于 + str;
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.大于等于)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.大于等于)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.大于等于);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.大于等于 + str;
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.小于等于)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.小于等于)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.小于等于);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.小于等于 + str;
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.模糊匹配)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.模糊匹配)) {
                        String str = conditionColumn.getSymbol().get(pageSymbolConstant.模糊匹配);
                        if (StringUtil.isNotEmpty(str)) {
                            isRep = true;
                            repStr += symbolConstant.模糊匹配 + "'%" + str + "%'";
                        }
                    }
                }
                if (conditionColumn.getPageSymbol().equals(pageSymbolConstant.范围)) {
                    if (conditionColumn.getSymbol().containsKey(pageSymbolConstant.范围bet)
                            && conditionColumn.getSymbol().containsKey(pageSymbolConstant.范围and)) {
                        String strBet = conditionColumn.getSymbol().get(pageSymbolConstant.范围bet);
                        String strAnd = conditionColumn.getSymbol().get(pageSymbolConstant.范围and);
                        if (StringUtil.isNotEmpty(strBet) && StringUtil.isNotEmpty(strAnd)) {
                            isRep = true;
                            repStr += symbolConstant.范围Bet + " '" + strBet + "' " + symbolConstant.范围And + " '" + strAnd + "'";
                        }
                    }
                }
                if (!isRep) {
                    repStr = " 1=1 ";
                }
                pgCondition = pgCondition.replace((conditionColumn.getItemName() + "|" + conditionColumn.getOrderNum().toString()), repStr);

            }
            if (StringUtil.isNotEmpty(pgCondition)) {
                selWhere += " and " + pgCondition;
            }
        }
        if (!StringUtil.isNotEmpty(selOrderBy)) {
            selOrderBy = " order by " + orderStr;
        }

        Boolean isTotal = true;
        String sqlStr = selColumns + " from " + selTableName + selWhere;
        //todo mysql 没有测试过 要整改
        if (driverClassName.contains("mysql") && StringUtil.isNotNull(basePageBean)) {
            sqlStr = "select " + sqlStr + selOrderBy + " limit " + (basePageBean.getPage() - 1) + "," + basePageBean.getRows();
        } else if (driverClassName.contains("sqlserver") && StringUtil.isNotNull(basePageBean)) {
            sqlStr = "select top " + basePageBean.getRows() +
                    " * from ( select row_number() over (" + selOrderBy + ") as rownumber," + sqlStr + ") x where rownumber > "
                    + (basePageBean.getRows() * (basePageBean.getPage() - 1));
        } else {
            //todo 只考虑了 mysql和sqlserver的分页，其他数据库分页未处理
            sqlStr = "select " + sqlStr + selOrderBy;
            isTotal = false;
        }
        if (isTotal && StringUtil.isNotNull(basePageBean)) {
            String sqlTotalStr = "select count(*) as totalCount from " + selTableName + selWhere;
            DtoItemColumn totalColumn = new DtoItemColumn();
            totalColumn.setViewFieldName("totalCount");
            basePageBean.setCount(selectBase(sqlTotalStr, Collections.singletonList(totalColumn)).get(0).get("totalCount"));
        }
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("Data", selectBase(sqlStr, itemColumnList));
        retMap.put("PageCount", StringUtil.isNotNull(basePageBean) ? basePageBean.getCount() : 0);
        return retMap;
    }

    /**
     * 检索条件及显示列赋值
     *
     * @param entity 自定义查询内容
     * @return 新的自定义查询
     */
    private DtoItem setConditionToValue(DtoItem entity, Boolean isCopy) {
        DtoItemCondition itemCondition = entity.getItemCondition();
        DtoItemCondition newItemCondition = new DtoItemCondition();
        if (StringUtil.isNotNull(itemCondition)) {
            if (isCopy) {
                newItemCondition.setItemId(entity.getId());
                newItemCondition.setDbCondition(itemCondition.getDbCondition());
                newItemCondition.setPageCondition(itemCondition.getPageCondition());
                newItemCondition.setIsDeleted(false);
            } else {
                itemCondition.setItemId(entity.getId());
            }
        }

        List<DtoItemColumn> itemColumnList = entity.getItemColumnList();
        List<DtoItemColumn> newItemColumnList = new ArrayList<>();
        if (StringUtil.isNotEmpty(itemColumnList)) {
            for (DtoItemColumn itemColumn : itemColumnList) {
                if (isCopy) {
                    DtoItemColumn newItemColumn = new DtoItemColumn();
                    newItemColumn.setItemId(entity.getId());
                    newItemColumn.setIsScreen(itemColumn.getIsScreen());
                    newItemColumn.setColumnLength(itemColumn.getColumnLength());
                    newItemColumn.setIsShow(itemColumn.getIsShow());
                    newItemColumn.setSortType(itemColumn.getSortType());
                    newItemColumn.setViewFieldId(itemColumn.getViewFieldId());
                    newItemColumn.setSortSeq(itemColumn.getSortSeq());
                    newItemColumn.setIsDeleted(itemColumn.getIsDeleted());
                    newItemColumnList.add(newItemColumn);
                } else {
                    itemColumn.setItemId(entity.getId());
                }
            }
        }

        if (StringUtil.isNotEmpty(entity.getDbConditionColumnList())) {
            String dbCondition = JsonStream.serialize(entity.getDbConditionColumnList());
            if (isCopy) {
                newItemCondition.setDbCondition(dbCondition);
            } else {
                itemCondition.setDbCondition(dbCondition);
            }
        }
        if (isCopy) {
            entity.setItemColumnList(newItemColumnList);
            entity.setItemCondition(newItemCondition);
        } else {
            entity.setItemColumnList(itemColumnList);
            entity.setItemCondition(itemCondition);
        }
        return entity;
    }

    /**
     * 通过sql查询数据
     *
     * @param sqlStr         sql
     * @param itemColumnList 需要返回的列
     * @return 返回的数据
     * @throws Exception 报错
     */
    private List<Map<String, String>> selectBase(String sqlStr, List<DtoItemColumn> itemColumnList) throws Exception {
        Connection conn = dataBaseConnect();
        PreparedStatement pstmt = conn.prepareStatement(sqlStr);
        ResultSet rs = pstmt.executeQuery();
        List<Map<String, String>> retList = new ArrayList<>();
        while (rs.next()) {
            Map<String, String> mapStr = new HashMap<>();
            for (DtoItemColumn itemColumn : itemColumnList) {
                String fieldName = itemColumn.getViewFieldName().replace("（", "").replace("）", "").replace("Guid", "");
                String strValue = rs.getString(fieldName);
                mapStr.put(fieldName, StringUtil.isNotNull(strValue) ? strValue : "");
            }
            retList.add(mapStr);
        }
        return retList;
    }

    /**
     * 获得数据库链接
     *
     * @return 获得数据库链接
     * @throws Exception 报错
     */
    private Connection dataBaseConnect() throws Exception {
        Connection conn = null;
        //1. JDBC连接MYSQL的代码很标准。
        String DRIVER = driverClassName;
        String URL = url;
        String USER = username;
        String PASSWORD = password;
        Properties props = new Properties();
        props.setProperty("user", USER);
        props.setProperty("password", PASSWORD);
        //1.加载驱动程序
        Class.forName(DRIVER);
        //2.获得数据库链接
        conn = DriverManager.getConnection(URL, props);
        return conn;
    }

    /**
     * 拼接sql排序语句
     *
     * @param columnList 排序的列
     * @param sortType   排序类型（正序、倒序）
     * @param selOrderBy 排序sql
     * @return 排序sql
     */
    private String getSelOrderByStr(List<DtoItemColumn> columnList, String sortType, String selOrderBy) {
        Boolean first = true;
        if (StringUtil.isNotEmpty(columnList)) {
            for (DtoItemColumn itemColumn : columnList) {
                if (!StringUtil.isNotEmpty(selOrderBy)) {
                    selOrderBy += " order by " + itemColumn.getDbFieldName() + sortType;
                    first = false;
                } else {
                    if (" asc, ".equals(sortType)) {
                        if (selOrderBy.contains("desc") && first) {
                            selOrderBy += " , ";
                            first = false;
                        }
                    } else {
                        if (selOrderBy.contains("asc") && first) {
                            selOrderBy += " , ";
                            first = false;
                        }
                    }
                    selOrderBy += itemColumn.getDbFieldName() + sortType;
                }
            }
            selOrderBy = selOrderBy.substring(0, selOrderBy.length() - 2);
        }
        return selOrderBy;
    }

}