package com.sinoyd.lims.query.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.query.dto.customer.DtoDbConditionColumn;
import com.sinoyd.lims.query.dto.customer.DtoExportBasePageBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义查询导出查询条件对象
 * <AUTHOR>
 * @version V1.0.0 2021/8/11
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SelectBaseItemCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询内容
     */
    private List<DtoDbConditionColumn> dbConditionColumns = new ArrayList<>();

    /**
     * 自定义查询id
     */
    private String itemId;

    /**
     * 分页信息
     */
    private DtoExportBasePageBean basePageBean;

    @Override
    public String getCondition() {
        if (StringUtil.isNotEmpty(this.dbConditionColumns)) {
            values.put("dbConditionColumns", this.dbConditionColumns);
        }
        if (StringUtil.isNotEmpty(itemId)) {
            values.put("itemId",this.itemId);
        }
        if (StringUtil.isNotNull(basePageBean)) {
            values.put("basePageBean", this.basePageBean);
        }
        return null;
    }
}
