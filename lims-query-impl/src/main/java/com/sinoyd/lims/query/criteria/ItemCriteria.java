package com.sinoyd.lims.query.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * Item查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * viewId
    */
    private String viewId;

    /**
     * 名字
     */
    private String itemName;

    /**
     * 查询类型
     */
    private String typeName;

    /**
     * 创建人
     */
    private String creatorId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.viewId = b.id");
        if (StringUtil.isNotEmpty(this.viewId)) {
            condition.append(" and viewId = :viewId");
            values.put("viewId", this.viewId);
        }
        if (StringUtil.isNotEmpty(this.itemName)) {
            condition.append(" and itemName like :itemName");
            values.put("itemName", "%" + this.itemName + "%");
        }
        if (StringUtil.isNotEmpty(this.typeName)) {
            condition.append(" and b.typeName like :typeName");
            values.put("typeName", "%" + this.typeName + "%");
        }
        if (StringUtil.isNotEmpty(this.creatorId)) {
            condition.append(" and a.creator = :creatorId");
            values.put("creatorId", this.creatorId);
        }
        return condition.toString();
    }
}