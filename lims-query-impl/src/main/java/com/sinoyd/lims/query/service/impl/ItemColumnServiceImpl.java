package com.sinoyd.lims.query.service.impl;

import com.sinoyd.lims.query.dto.DtoItemColumn;
import com.sinoyd.lims.query.repository.ItemColumnRepository;
import com.sinoyd.lims.query.service.ItemColumnService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * ItemColumn操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Service
public class ItemColumnServiceImpl extends BaseJpaServiceImpl<DtoItemColumn,String,ItemColumnRepository> implements ItemColumnService {

    @Override
    public void findByPage(PageBean<DtoItemColumn> pb, BaseCriteria itemColumnCriteria) {
        pb.setEntityName("DtoItemColumn a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, itemColumnCriteria);
    }
}