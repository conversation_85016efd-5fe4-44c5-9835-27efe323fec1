package com.sinoyd.lims.query.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.query.service.ItemColumnService;
import com.sinoyd.lims.query.criteria.ItemColumnCriteria;
import com.sinoyd.lims.query.dto.DtoItemColumn;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ItemColumn服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Api(tags = "示例: ItemColumn服务")
 @RestController
 @RequestMapping("api/query/itemColumn")
 public class ItemColumnController extends BaseJpaController<DtoItemColumn, String,ItemColumnService> {


    /**
     * 分页动态条件查询ItemColumn
     * @param itemColumnCriteria 条件参数
     * @return RestResponse<List<ItemColumn>>
     */
     @ApiOperation(value = "分页动态条件查询ItemColumn", notes = "分页动态条件查询ItemColumn")
     @GetMapping
     public RestResponse<List<DtoItemColumn>> findByPage(ItemColumnCriteria itemColumnCriteria) {
         PageBean<DtoItemColumn> pageBean = super.getPageBean();
         RestResponse<List<DtoItemColumn>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, itemColumnCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ItemColumn
     * @param id 主键id
     * @return RestResponse<DtoItemColumn>
     */
     @ApiOperation(value = "按主键查询ItemColumn", notes = "按主键查询ItemColumn")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoItemColumn> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoItemColumn> restResponse = new RestResponse<>();
         DtoItemColumn itemColumn = service.findOne(id);
         restResponse.setData(itemColumn);
         restResponse.setRestStatus(StringUtil.isNull(itemColumn) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ItemColumn
     * @param itemColumn 实体列表
     * @return RestResponse<DtoItemColumn>
     */
     @ApiOperation(value = "新增ItemColumn", notes = "新增ItemColumn")
     @PostMapping
     public RestResponse<DtoItemColumn> create(@RequestBody DtoItemColumn itemColumn) {
         RestResponse<DtoItemColumn> restResponse = new RestResponse<>();
         restResponse.setData(service.save(itemColumn));
         return restResponse;
      }

     /**
     * 新增ItemColumn
     * @param itemColumn 实体列表
     * @return RestResponse<DtoItemColumn>
     */
     @ApiOperation(value = "修改ItemColumn", notes = "修改ItemColumn")
     @PutMapping
     public RestResponse<DtoItemColumn> update(@RequestBody DtoItemColumn itemColumn) {
         RestResponse<DtoItemColumn> restResponse = new RestResponse<>();
         restResponse.setData(service.update(itemColumn));
         return restResponse;
      }

    /**
     * "根据id批量删除ItemColumn
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ItemColumn", notes = "根据id批量删除ItemColumn")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }