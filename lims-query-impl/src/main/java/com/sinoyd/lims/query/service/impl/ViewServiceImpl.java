package com.sinoyd.lims.query.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.configuration.ScanEntityPackages;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.query.configuration.TableEntityData;
import com.sinoyd.lims.query.dto.DtoItemColumn;
import com.sinoyd.lims.query.dto.DtoView;
import com.sinoyd.lims.query.dto.DtoViewField;
import com.sinoyd.lims.query.dto.customer.DtoTableColumnName;
import com.sinoyd.lims.query.repository.ViewFieldRepository;
import com.sinoyd.lims.query.repository.ViewRepository;
import com.sinoyd.lims.query.service.ViewFieldService;
import com.sinoyd.lims.query.service.ViewService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;


/**
 * View操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
@Service
public class ViewServiceImpl extends BaseJpaServiceImpl<DtoView, String, ViewRepository> implements ViewService {

    @Value("${spring.datasource.primary.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource.primary.username}")
    private String username;

    @Value("${spring.datasource.primary.url}")
    private String url;

    @Value("${spring.datasource.primary.password}")
    private String password;

    @Autowired
    private ViewFieldService viewFieldService;

    @Autowired
    private ViewFieldRepository viewFieldRepository;

    @Override
    public void findByPage(PageBean<DtoView> pb, BaseCriteria viewCriteria) {
        pb.setEntityName("DtoView a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, viewCriteria);
    }

    /**
     * 新增搜索项
     *
     * @param entity 搜索项dto
     * @return 搜索项dto
     */
    @Transactional
    @Override
    public DtoView save(DtoView entity) {
        super.save(entity);
        List<DtoViewField> viewFieldList = entity.getViewFieldList();
        for (DtoViewField viewField : viewFieldList) {
            viewField.setViewId(entity.getId());
        }
        viewFieldService.save(viewFieldList);
        entity.setViewFieldList(viewFieldList);
        return entity;
    }

    /**
     * 修改搜索项
     *
     * @param entity 搜索项dto
     * @return 搜索项dto
     */
    @Transactional
    @Override
    public DtoView update(DtoView entity) {
        //编辑视图内容（要做比较，判断哪些删除，哪些新增）
        List<DtoViewField> oldViewFieldList = viewFieldRepository.findByViewIdIn(Collections.singletonList(entity.getId()));

        List<DtoViewField> viewFieldList = entity.getViewFieldList();
        for (DtoViewField viewField : viewFieldList) {
            viewField.setViewId(entity.getId());
        }

        //需要删除的viewField
        List<String> addFields = viewFieldList.stream().map(DtoViewField::getDbField).collect(Collectors.toList());
        List<DtoViewField> delFieldList = oldViewFieldList.stream().filter(p -> !addFields.contains(p.getDbField())).collect(Collectors.toList());
        List<String> delIds = delFieldList.stream().map(DtoViewField::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delIds)) {
            viewFieldService.logicDeleteById(delIds);
        }
        //需要新增的viewField
        List<String> oldFields = oldViewFieldList.stream().map(DtoViewField::getDbField).collect(Collectors.toList());
        List<DtoViewField> addFieldList = viewFieldList.stream().filter(p -> !oldFields.contains(p.getDbField())).collect(Collectors.toList());
        viewFieldService.save(addFieldList);

        //需要修改的viewFiled
        List<String> addDbFields = addFieldList.stream().map(DtoViewField::getDbField).collect(Collectors.toList());
        List<DtoViewField> updateFieldList = viewFieldList.stream().filter(p -> !addDbFields.contains(p.getDbField())).collect(Collectors.toList());
        Map<String, DtoViewField> dtFiled2DtoViewFieldMap = new HashMap<>();
        oldViewFieldList.forEach(dtoViewField -> dtFiled2DtoViewFieldMap.put(dtoViewField.getDbField(), dtoViewField));
        for (DtoViewField dto : updateFieldList) {
            DtoViewField oldViewField = dtFiled2DtoViewFieldMap.get(dto.getDbField());
            if (StringUtil.isNotNull(oldViewField)) {
                dto.setId(oldViewField.getId());
                dto.setCreator(oldViewField.getCreator());
                dto.setCreateDate(oldViewField.getCreateDate());
            }
        }
        viewFieldService.save(updateFieldList);

        //编辑视图
        return super.update(entity);
    }

    /**
     * 单个修改已选字段显示名称
     *
     * @param view 查询类型实体对象
     * @return DtoView
     */
    @Transactional
    @Override
    public DtoView updateViewField(DtoView view) {
        //查出原有的 ViewField 列表
        List<DtoViewField> oldViewFieldList = viewFieldRepository.findByViewIdIn(Collections.singletonList(view.getId()));
        //将原有的 ViewField 列表建立以 dbField 为 key, DtoViewField 为 value 的 map 映射
        Map<String, DtoViewField> dtFiled2DtoViewFieldMap = new HashMap<>();
        oldViewFieldList.forEach(dtoViewField -> dtFiled2DtoViewFieldMap.put(dtoViewField.getDbField(), dtoViewField));

        //需要更新的 viewField
        DtoViewField toUptViewField = view.getViewFieldList().get(0);
        toUptViewField.setViewId(view.getId());

        DtoViewField oldViewField = dtFiled2DtoViewFieldMap.get(toUptViewField.getDbField());
        // dbField 已存在，则将相关字段值复制过来，再修改
        if (StringUtil.isNotNull(oldViewField)) {
            toUptViewField.setId(oldViewField.getId());
            toUptViewField.setCreator(oldViewField.getCreator());
            toUptViewField.setCreateDate(oldViewField.getCreateDate());
        }

        DtoViewField resViewField = viewFieldService.save(toUptViewField);
        view.getViewFieldList().set(0, resViewField);
        return view;
    }

    /**
     * 删除搜索项
     *
     * @param ids 搜索项ids
     * @return 删除个数
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> viewIds = (List<String>) ids;
        //删除关联项
        List<DtoViewField> fieldList = viewFieldRepository.findByViewIdIn(viewIds);
        if (StringUtil.isNotEmpty(fieldList)) {
            viewFieldService.logicDeleteById(fieldList.stream().map(DtoViewField::getId).collect(Collectors.toList()));
        }
        //删除搜索项
        return super.logicDeleteById(ids);
    }

    /**
     * 检索单个检索项
     *
     * @param id 检索项id
     * @return 检索项
     */
    @Override
    public DtoView findOne(String id) {
        DtoView dtoView = super.findOne(id);
        //检查项字段
        List<DtoViewField> dtoViewFieldList = viewFieldRepository.findByViewIdIn(Collections.singletonList(id));
        dtoView.setViewFieldList(dtoViewFieldList);
        return dtoView;
    }

    @Override
    public List<DtoItemColumn> getItemColumnByViewId(String viewId) {
        List<DtoViewField> viewFieldList = viewFieldRepository.findByViewIdIn(Collections.singletonList(viewId));
        List<DtoItemColumn> itemColumnList = new ArrayList<>();
        for (DtoViewField viewField : viewFieldList) {
            DtoItemColumn itemColumn = new DtoItemColumn();
            itemColumn.setViewFieldName(viewField.getPageField());
            itemColumn.setDbFieldName(viewField.getDbField());
            itemColumn.setViewFieldId(viewField.getId());
            itemColumn.setItemId(UUIDHelper.GUID_EMPTY);
            itemColumn.setColumnLength(20);
            itemColumn.setSortType("");
            itemColumn.setSortSeq(0);
            itemColumn.setIsShow(true);
            itemColumn.setIsScreen(false);
        }
        return itemColumnList;
    }

    /**
     * 获取所有有效的检索类别
     *
     * @return 检索类别
     */
    @Override
    public Map<String, String> getTypeNameList() {
        List<DtoView> dtoViewList = this.findAll().stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
        Map<String, String> retViewList = new HashMap<>();
        for (DtoView dtoView : dtoViewList) {
            retViewList.put(dtoView.getId(), dtoView.getTypeName());
        }
        return retViewList;
    }

    /**
     * 获取数据库中存在的表和视图
     *
     * @return 返回表和视图
     * @throws Exception 报错
     */
    @Override
    public List<String> getDBTables() throws Exception {
        Connection conn = dataBaseConnect();
        //获取数据库元数据
        DatabaseMetaData dbMetaData = conn.getMetaData();
        //数据库表名List
        List<String> retTbList = new ArrayList<>();
        //类型()
        String[] types = {"TABLE", "VIEW"};
        ResultSet rs = dbMetaData.getTables(null, null, "%", types);
        while (rs.next()) {
            retTbList.add(rs.getString(3));
        }
        retTbList = retTbList.stream().filter(p -> p.toUpperCase().contains("TB_") || p.toUpperCase().contains("VI_")).collect(Collectors.toList());
        return retTbList;
    }

    /**
     * 获取表或者视图中的字段
     *
     * @param tableName 表或者视图的名称 todo 需要排除已选的table列
     * @return 表或者视图中的字段
     * @throws Exception 报错
     */
    @Override
    public List<DtoTableColumnName> getTableColumns(String tableName) throws Exception {
        List<DtoTableColumnName> retTbList = new ArrayList<>();
        //table表的字段获取
        if (TableEntityData.TableEntityMap.containsKey(tableName.toLowerCase()) && tableName.toUpperCase().contains("TB_")) {
            Class<?> cl = TableEntityData.TableEntityMap.get(tableName.toLowerCase());
            Field[] declaredFields = cl.getDeclaredFields();
            for (Field field : declaredFields) {

                if (field.getName().equalsIgnoreCase("serialVersionUID")) {
                    continue;
                }
                ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);

                String remark = field.getName();
                if (Optional.ofNullable(property).isPresent()) {
                    remark = property.value();
                }
                DtoTableColumnName tableColumnName = new DtoTableColumnName();
                tableColumnName.setColumn(field.getName());
                tableColumnName.setColumnName(remark);
                retTbList.add(tableColumnName);
            }

        }
        if (StringUtil.isNotEmpty(retTbList)) {
            return retTbList;
        }

        Connection conn = dataBaseConnect();
        // 获取连接
        DatabaseMetaData dbmd = conn.getMetaData();
        // 得到视图字段获取
        ResultSet resultSet = dbmd.getTables(null, "%", tableName, new String[]{"VIEW", "Table"});
        while (resultSet.next()) {
            String rstName = resultSet.getString("TABLE_NAME");
            if (rstName.equalsIgnoreCase(tableName)) {
                ResultSet rs = conn.getMetaData().getColumns(null, null, tableName.toUpperCase(), "%");

                while (rs.next()) {
                    String colName = rs.getString("COLUMN_NAME");

                    String remarks = rs.getString("REMARKS");
                    if (remarks == null || remarks.equals("")) {
                        remarks = colName;
                    }
                    DtoTableColumnName tableColumnName = new DtoTableColumnName();
                    tableColumnName.setColumn(colName);
                    tableColumnName.setColumnName(remarks);
                    retTbList.add(tableColumnName);
                }
            }
        }
        return retTbList;
    }

    /**
     * 获得数据库链接
     *
     * @return 获得数据库链接
     * @throws Exception 报错
     */
    private Connection dataBaseConnect() throws Exception {
        Connection conn = null;
        //1. JDBC连接MYSQL的代码很标准。
        String DRIVER = driverClassName;
        String URL = url;
        String USER = username;
        String PASSWORD = password;
//        String DRIVER = "com.mysql.jdbc.Driver";
//        String URL = "********************************************************************************";
//        String USER = "use";
//        String PASSWORD = "11111";
        Properties props = new Properties();
        props.setProperty("user", USER);
        props.setProperty("password", PASSWORD);
        //1.加载驱动程序
        Class.forName(DRIVER);
        //2.获得数据库链接
        conn = DriverManager.getConnection(URL, props);
        return conn;
    }

}