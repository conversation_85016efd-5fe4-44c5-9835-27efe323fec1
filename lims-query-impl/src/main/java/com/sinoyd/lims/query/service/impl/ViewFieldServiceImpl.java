package com.sinoyd.lims.query.service.impl;

import com.sinoyd.lims.query.dto.DtoViewField;
import com.sinoyd.lims.query.repository.ViewFieldRepository;
import com.sinoyd.lims.query.service.ViewFieldService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * ViewField操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @Service
public class ViewFieldServiceImpl extends BaseJpaServiceImpl<DtoViewField,String,ViewFieldRepository> implements ViewFieldService {

    @Override
    public void findByPage(PageBean<DtoViewField> pb, BaseCriteria viewFieldCriteria) {
        pb.setEntityName("DtoViewField a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, viewFieldCriteria);
    }
}