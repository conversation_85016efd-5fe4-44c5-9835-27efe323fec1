package com.sinoyd.lims.query.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * View查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ViewCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 类别
    */
    private String typeName;

    /**
     * 关键字
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.typeName)) {
            condition.append(" and typeName like :typeName");
            values.put("typeName",  "%" + this.typeName + "%");
        }

        if(StringUtil.isNotEmpty(this.key)){
            condition.append(" and typeDesc like :key");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}