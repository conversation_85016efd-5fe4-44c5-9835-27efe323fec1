package com.sinoyd.base.criteria;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/8
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentCriteria extends BaseCriteria {
    /**
     * 上传的开始日期
     */
    private String dtBegin;

    /**
     * 上传的结束日期
     */
    private String dtEnd;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 上传人的Id
     */
    private String creator;

    /**
     * 上传人的姓名
     */
    private String creatorName;

    /**
     * 存储路径
     */
    private String path;

    /**
     * 对象Id
     */
    private String folderId;

    /**
     * 对象Id
     */
    private List<String> folderIds = new ArrayList<>();

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 文件类型Id
     */
    private String docTypeId;

    /**
     * 文件类型Id集合
     */
    private List<String> docTypeIds = new ArrayList<>();

    /**
     * 是否文件管理查询
     */
    private Boolean isFolder = false;

    /**
     * 是否测点示意图查询
     */
    private Boolean isPointPic = false;

    /**
     * 项目登记开始时间
     */
    private String startTime;

    /**
     * 项目登记结束时间
     */
    private String endTime;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 关键字
     */
    private String projectKey;

    private String projectId;

    /**
     * 文件后缀
     */
    private String docSuffix;

    /**
     * 是否副本
     */
    private Boolean isTranscript;

    /**
     * 是否报告点位示意图
     */
    private Boolean isReportPointPic;

    /**
     * 标识集合
     */
    private List<String> ids;

    /**
     * 是否为远程请求（决定是否删除临时文件）
     */
    private Boolean isRemoting;

    /**
     * 对象Ids逗号拼接，
     * 用于远程调用get请求不能传集合时使用
     */
    private String folderIdsStr;

    /**
     * 电子签名状态
     */
    private Integer signStatus;

    /**
     * 移动端查询关键字（暂时为文件名称和编号）
     */
    private String mobileKey;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotNull(this.isPointPic) && this.isPointPic) {
//            condition.append(" and exists(select 1 from  DtoReceiveSampleRecord r, DtoReport pt where (t.id = r.projectId and r.id = p.folderId) or (t.id = pt.projectId and pt.id = p.folderId)) ");
//            if (StringUtils.isNotNullAndEmpty(startTime)) {
//                Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
//                condition.append(" and ").append("t.inputTime >= :from");
//                values.put("from", from);
//            }
//            if (StringUtils.isNotNullAndEmpty(endTime)) {
//                Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
//                Calendar endCalendar = Calendar.getInstance();
//                endCalendar.setTime(to);
//                endCalendar.add(Calendar.DAY_OF_YEAR, 1);
//                condition.append(" and ").append("t.inputTime < :to");
//                this.values.put("to", endCalendar.getTime());
//            }
//            if (StringUtils.isNotNullAndEmpty(projectTypeId)) {
//                condition.append(" and t.projectTypeId = :projectTypeId");
//                this.values.put("projectTypeId", this.projectTypeId);
//            }
//            if (StringUtils.isNotNullAndEmpty(projectKey)) {
//                condition.append(" and (t.projectName like :projectKey or t.projectCode like :projectKey or t.inspectedEnt like :projectKey)");
//                this.values.put("projectKey", "%" + this.projectKey + "%");
//            }
//            if (StringUtils.isNotNullAndEmpty(projectId)) {
//                condition.append(" and t.id = :projectId");
//                this.values.put("projectId", this.projectId);
//            }
        }
        if (StringUtils.isNotNullAndEmpty(fileName)) {
            condition.append(" and (fileName like :fileName)");
            values.put("fileName", "%" + this.fileName + "%");
        }

        if (StringUtils.isNotNullAndEmpty(folderName)) {
            condition.append(" and (folderName like :folderName)");
            values.put("folderName", "%" + this.folderName + "%");
        }

        if (StringUtils.isNotNullAndEmpty(path)) {
            condition.append(" and (path like :path)");
            values.put("path", "%" + this.path + "%");
        }

        if (StringUtils.isNotNullAndEmpty(folderId) && !folderId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and folderId = :folderId");
            values.put("folderId", folderId);
        } else if (StringUtil.isNotNull(isFolder) && isFolder) {
            //说明要根据folder表进行过滤
            condition.append(" and exists (select 1 from DtoFolder b where p.folderId = b.id)");
        }
        if (StringUtils.isNotNullAndEmpty(docTypeId) && !docTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and docTypeId = :docTypeId");
            values.put("docTypeId", docTypeId);
        }
        if (folderIds.size() > 0) {
            condition.append(" and folderId in :folderIds");
            values.put("folderIds", folderIds);
        }
        if (docTypeIds.size() > 0) {
            condition.append(" and docTypeId in :docTypeIds");
            values.put("docTypeIds", docTypeIds);
        }
        if (StringUtils.isNotNullAndEmpty(creatorName)) {
            condition.append(" and (uploadPerson like :creatorName)");
            values.put("creatorName", "%" + this.creatorName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(creator) && !creator.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and uploadPersonId = :creator");
            values.put("creator", creator);
        }

        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and ").append("createDate >= :from");
            values.put("from", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(to);
            endCalendar.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and ").append("createDate < :to");
            this.values.put("to", endCalendar.getTime());
        }
        // 文件后缀
        if (StringUtil.isNotEmpty(this.docSuffix)) {
            condition.append(" and docSuffix like :docSuffix");
            this.values.put("docSuffix", "%" + this.docSuffix + "%");
        }
        if (StringUtil.isNotNull(isTranscript)) {
            condition.append(" and isTranscript = :isTranscript");
            this.values.put("isTranscript", this.isTranscript);
        }
        if (StringUtil.isNotEmpty(folderIdsStr)) {
            List<String> folderIdList = Arrays.stream(folderIdsStr.split(",")).collect(Collectors.toList());
            condition.append(" and folderId in :folderIdList");
            values.put("folderIdList", folderIdList);
        }
        // 电子签名状态,状态不为null时，默认移动端查询已推送的附件
        if (StringUtil.isNotNull(signStatus)) {
            // 移动端查询所有只查询已签和未签
            if (EnumBase.EnumSignStatus.所有.getValue().equals(signStatus)) {
                condition.append(" and p.signStatus in :signStatues");
                List<Integer> signStatues = Arrays.asList(EnumBase.EnumSignStatus.未签.getValue(), EnumBase.EnumSignStatus.已签.getValue());
                values.put("signStatues", signStatues);
            } else {
                condition.append(" and p.signStatus  = :signStatus");
                values.put("signStatus", signStatus);
            }
        }

        condition.append(" and p.isDeleted = 0");
        return condition.toString();
    }
}