package com.sinoyd.base.criteria;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

/**
 * 仪器查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019/04/29
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentCriteria extends BaseCriteria {

    /**
     * 仪器状态（-1代表所有）
     */
    private Integer status;

    /**
     * 仪器类型（空Guid代表所有）
     */
    private String instrumentTypeId;

    /**
     * 关键字：仪器名称、规格型号、出厂编号、编号
     */
    private String key;

    /**
     * 仪器设备管理员
     */
    private String administrator;

    /**
     * 是否过滤已出库的仪器
     */
    private Boolean filterOutInstrument;

    /**
     * 是否过虑出已有出入库记录的仪器 是："1"  否 ："0" 所有：""
     */
    private String outBound;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 仪器Id集合
     */
    private List<String> instrumentIds;

    /**
     * 是否按照有效期排序  是："1"  否 ："0"
     */
    private Boolean isOriginEndDateSort;

    /**
     * 测试项目关联仪器类型 1采样，2：实验室分析，4：现场分析
     */
    private Integer useType;

    /**
     * 测试项目id集合，用来关联测试项目选择仪器
     */
    private List<String> testIds = new ArrayList<>();

    /**
     * 所属科室
     */
    private String deptId;

    /**
     * 是否排除报废停用过期仪器
     */
    private Boolean isExcludeNotAvailable = false;

    /**
     * 排除仪器id
     */
    private List<String> excludeIds = new ArrayList<>();

    @Override
    public String getCondition() {
//        values.clear();
        StringBuffer condition = new StringBuffer();
        if (isExcludeNotAvailable){
            // 筛选正常和即将过期的仪器
            status = EnumBase.EnumInstrumentStatus.正常.getValue();
        }
        if (StringUtil.isNotNull(status) && status > -1) {
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
//            c.add(Calendar.DAY_OF_YEAR, 1);
            if (status.equals(EnumBase.EnumInstrumentStatus.过期.getValue())) {
                //直接状态是过期的，或者不在有效期范围的
                condition.append(" and ((year(originEndDate) <> 1753 and originEndDate< :date and state <> :state1 and state <> :state2) or state =:state)");
                values.put("date", c.getTime());
                values.put("state1", EnumBase.EnumInstrumentStatus.报废.getValue());
                values.put("state2", EnumBase.EnumInstrumentStatus.停用.getValue());
                values.put("state", status);
            } else if (status.equals(EnumBase.EnumInstrumentStatus.正常.getValue())) {
                //状态查询参数为正常时，也需要把即将过期的查出来
                condition.append(" AND ( ");
                condition.append(" ( state = :state AND ( originType = - 1 OR ( originType != - 1 AND ( originEndDate = '1753-1-1' OR originEndDate >= :date ))) ) ");
                condition.append(" OR (state = :state1) ");
                condition.append(" ) ");
                values.put("state", status);
                values.put("state1", EnumBase.EnumInstrumentStatus.即将过期.getValue());
                values.put("date", c.getTime());
            } else if (EnumBase.EnumInstrumentStatus.即将过期.getValue().equals(status)) {
                //过滤出状态为即将过期或者状态为正常，过期日期不为空，且即将在预警天数内过期的仪器对象
                condition.append(" and (state = :state1 or (state = :state2 and originType != -1 and originEndDate <> '1753-01-01' and originEndDate >= :date and originEndDate <= :expireDate))");
                values.put("date", c.getTime());
                values.put("state1", EnumBase.EnumInstrumentStatus.即将过期.getValue());
                values.put("state2", EnumBase.EnumInstrumentStatus.正常.getValue());
            } else {
                condition.append(" and state = :state");
                values.put("state", status);
            }
            //状态不为即将过期时要移除 expireDate 查询条件
            if (!status.equals(EnumBase.EnumInstrumentStatus.即将过期.getValue())) {
                values.remove("expireDate");
            }
        } else {
            values.remove("expireDate");
        }

        if (StringUtil.isNotEmpty(this.instrumentIds)) {
            condition.append(" and id in :instrumentIds");
            values.put("instrumentIds", this.instrumentIds);
        }

        if (StringUtils.isNotNullAndEmpty(instrumentTypeId)
                && !UUIDHelper.GUID_EMPTY.equals(instrumentTypeId)) {
            condition.append(" and instrumentTypeId = :instrumentTypeId");
            values.put("instrumentTypeId", this.instrumentTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(
                    " and (instrumentName like :key or model like :key or instrumentsCode like :key or serialNo like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(administrator) && !UUIDHelper.GUID_EMPTY.equals(administrator)) {
            condition.append(" and manager = :administrator");
            values.put("administrator", this.administrator);
        }
        if (StringUtil.isNotNull(filterOutInstrument) && filterOutInstrument) {
            condition.append(" and not exists (select 1 from DtoProjectInstrumentDetails where instrumentId = x.id and isStorage = 0 ) ");
        }
        if (StringUtil.isNotEmpty(outBound) && StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            InstrumentService instrumentService = SpringContextAware.getBean(InstrumentService.class);
            List<String> instrumentIdList = instrumentService.filterInstrumentId(receiveId);
            if (StringUtil.isEmpty(instrumentIdList)) {
                instrumentIdList.add(UUIDHelper.GUID_EMPTY);
            }
            if ("1".equals(outBound)) {
                condition.append(" and x.id in :instrumentIdList ");
            } else {
                condition.append(" and x.id not in :instrumentIdList ");
            }
            values.put("instrumentIdList", instrumentIdList);
        }
        if (StringUtil.isNotEmpty(deptId) && !UUIDHelper.GUID_EMPTY.equals(deptId)) {
            condition.append(" and belongDeptId = :deptId");
            values.put("deptId", this.deptId);
        }

        if (StringUtil.isNotEmpty(excludeIds)){
            condition.append(" and id not in :excludeIds");
            values.put("excludeIds", this.excludeIds);
        }

        condition.append(" and isDeleted =0");
        return condition.toString();
    }
}