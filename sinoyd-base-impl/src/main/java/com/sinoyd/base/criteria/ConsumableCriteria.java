package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * 消耗品管理查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/7
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumableCriteria extends BaseCriteria {

    // 关键字：消耗品名称、编号、规格
    private String key;
    // 类别（空id代表所有）
    private String categoryId;
    // 是否标样
    private Boolean isStandard;

    /**
     * 查询时间开始
     */
    private String startTime;
    /**
     * 查询时间结束
     */
    private String endTime;

    /**
     * 消耗品id
     */
    private String consumableId;// 消耗品Id

    /**
     * 过期状态 0：正常 1：即将过期 2：已过期
     */
    private String expireStatus;

    /**
     * 库存状态 -1：未选择   0：无库存   1：低库存   2：有库存
     */
    private String inventoryStatus;

    /**
     * 提醒人
     */
    private String sendWarnUserId;

    /**
     * 是否实验室加密
     */
    private Boolean isLabEncryption;

    /**
     * 领用日期开始日期
     */
    private String receiveStartTime;

    /**
     * 领用日期结束日期
     */
    private String receiveEndTime;

    /**
     * 开始入库日期
     */
    private String startStorageDate;

    /**
     * 结束入库时间
     */
    private String endStorageDate;


    @Override
    public String getCondition() {
//        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (x.codeInStation like :key or x.consumableCode like :key or consumableName like :key or x.specification like :key" +
                    " or exists (select 1 from DtoConsumableDetail d where d.parentId = x.id and d.productionCode like :key))");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(categoryId) && !UUIDHelper.GUID_EMPTY.equals(this.categoryId)) {
            condition.append(" and x.categoryId = :categoryId");
            values.put("categoryId", this.categoryId);
        }

        if (StringUtils.isNotNullAndEmpty(consumableId) && !UUIDHelper.GUID_EMPTY.equals(this.consumableId)) {
            condition.append(" and x.id = :consumableId");
            values.put("consumableId", this.consumableId);
        }

        //是否实验室加密
        if (StringUtils.isNotNullAndEmpty(this.getIsLabEncryption())) {
            condition.append(" and x.isLabEncryption = :isLabEncryption");
            values.put("isLabEncryption", isLabEncryption);
        }

        //提醒人
        if (StringUtils.isNotNullAndEmpty(this.sendWarnUserId) && !UUIDHelper.GUID_EMPTY.equals(this.sendWarnUserId)) {
            condition.append(" and x.sendWarnUserId = :sendWarnUserId");
            values.put("sendWarnUserId", this.sendWarnUserId);
        }
        /*
         * 修改:叶雨晨
         * 内容:进行一次非空判断
         */
        if (StringUtil.isNotNull(isStandard)) {
            if (isStandard) {
                condition.append(" and x.isStandard = 1");
            } else {
                condition.append(" and x.isStandard = 0");
            }
        }

        Calendar calendar = new GregorianCalendar();
        //开始时间查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.createDate >= :startTime");
            values.put("startTime", date);
        }
        //结束时间查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.createDate < :endTime");
            values.put("endTime", date);
        }

        if (StringUtil.isNotEmpty(this.receiveStartTime)) {
            Date date = DateUtil.stringToDate(this.receiveStartTime, DateUtil.YEAR);
            condition.append(" and a.occurrenceTime >= :receiveStartTime");
            values.put("receiveStartTime", date);
        }

        if (StringUtil.isNotEmpty(this.receiveEndTime)) {
            Date date = DateUtil.stringToDate(this.receiveEndTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.occurrenceTime < :receiveEndTime");
            values.put("receiveEndTime", date);
        }

        //按状态检索
        if (StringUtil.isNotEmpty(this.expireStatus)) {
            Date nowDate = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);
            if ("0".equals(this.expireStatus)) {
                //过滤出状态为正常的记录(不存在消耗品明细,或者不存在有效期在当前日期之前且有效期不为空的消耗品明细)
                condition.append(" AND ( ");
                condition.append(" ( NOT EXISTS ( SELECT 1 FROM DtoConsumableDetail AS yy WHERE yy.parentId = x.id ) ) ");
                condition.append(" OR ( NOT EXISTS (SELECT 1 FROM DtoConsumableDetail AS y WHERE y.parentId = x.id AND ( y.expiryDate < :nowDate AND y.expiryDate != '1753-01-01' ))) ");
                condition.append(" ) ");
                values.put("nowDate", nowDate);
                values.remove("expireDate");
            } else if ("1".equals(this.expireStatus)) {
                //过滤出状态为即将过期的记录
                condition.append(" and exists(select 1 from DtoConsumableDetail as y where y.parentId = x.id " +
                        "and y.expiryDate >= :nowDate and y.expiryDate <= :expireDate) ");
                values.put("nowDate", nowDate);
            } else if ("2".equals(this.expireStatus)) {
                //过滤出状态为已过期的记录
                condition.append(" and exists(select 1 from DtoConsumableDetail as y where y.parentId = x.id " +
                        "and y.expiryDate <> '1753-01-01' and y.expiryDate < :nowDate) ");
                values.put("nowDate", nowDate);
                values.remove("expireDate");
            }
        } else {
            values.remove("expireDate");
        }

        if (StringUtil.isNotEmpty(this.startStorageDate) && StringUtil.isNotEmpty(this.endStorageDate)) {
            Date start = DateUtil.stringToDate(this.startStorageDate, DateUtil.YEAR);
            Date end = DateUtil.stringToDate(this.endStorageDate, DateUtil.YEAR);
            calendar.setTime(end);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = calendar.getTime();
            condition.append(" and exists(select 1 from DtoConsumableDetail as y where y.parentId = x.id " +
                    "and y.storageDate >= :startStorageDate and y.storageDate < :endStorageDate) ");
            values.put("startStorageDate", start);
            values.put("endStorageDate", end);
        }else if (StringUtil.isNotEmpty(this.startStorageDate) && StringUtil.isEmpty(this.endStorageDate)) {
            Date start = DateUtil.stringToDate(this.startStorageDate, DateUtil.YEAR);
            condition.append(" and exists(select 1 from DtoConsumableDetail as y where y.parentId = x.id " +
                    "and y.storageDate >= :startStorageDate) ");
            values.put("startStorageDate", start);
        }else if (StringUtil.isNotEmpty(this.endStorageDate) && StringUtil.isEmpty(this.startStorageDate)) {
            Date end = DateUtil.stringToDate(this.endStorageDate, DateUtil.YEAR);
            calendar.setTime(end);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = calendar.getTime();
            condition.append(" and exists(select 1 from DtoConsumableDetail as y where y.parentId = x.id " +
                    "and y.storageDate < :endStorageDate) ");
            values.put("endStorageDate", end);
        }

        return condition.toString();
    }

}