package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 企业查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseCriteria extends BaseCriteria {

    /**
     * 企业类型
     */
    private Integer type;

    /**
     * 需排除的企业类型
     */
    private List<Integer> excludeTypeList;

    /**
     * 所属区域Id（空Guid代表所有）
     */
    private String areaId;
    /**
     * 所属区域Id（空Guid代表所有）
     */
    private List<String> areaIds;
    /**
     * 关键字：客户名称、组织机构代码、统一社会信用代码
     */
    private String key;

    /**
     * 污染源类型：（常量 LIM_PollutionSourceType：1工业污染源、2污水处理厂、3固废处理厂）
     */
    private List<String> pollutionSourceTypeList;

    /**
     * 是否精确查询
     */
    private Boolean isPrecision = Boolean.FALSE;

    /**
     * 判断是否查询
     */
    private Boolean isQuery = Boolean.TRUE;

    /**
     * 企业ids
     */
    private List<String> entIds;

    /**
     * 排除的企业ids
     */
    private List<String> existsEntIds;

    /**
     * 用于查询entIds
     */
    private String projectId;

    /**
     * 排污许可证方案同步是否成功
     */
    private Boolean isSyncPollutionDischarge;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotNull(this.type) && this.type != 0) {
            condition.append(" and bitand(type , :type)<>0");
            values.put("type", this.type);
        }

        if(StringUtil.isNotEmpty(excludeTypeList)){
            condition.append(" and type not in :excludeTypeList ");
            values.put("excludeTypeList", this.excludeTypeList);
        }

        if (StringUtil.isNotNull(this.entIds)) {
            if (this.entIds.size() > 0) {
                condition.append(" and id in :entIds");
                values.put("entIds", this.entIds);
            } else {
                condition.append(" and 1 = 2 ");
            }
        }

        if (StringUtil.isNotNull(this.existsEntIds)) {
            if (this.existsEntIds.size() > 0) {
                condition.append(" and id not in :existsEntIds");
                values.put("existsEntIds", this.existsEntIds);
            }
        }

        if (StringUtil.isNotNull(this.areaIds) && this.areaIds.size() > 0) {
            condition.append(" and areaId in :areaIds");
            values.put("areaIds", this.areaIds);
        }

        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (name like :key or socialCreditCode like :key or pollutionDischargeCode like :key)");
            values.put("key", "%" + this.key + "%");
        }


        //判断是否是精确查询
        if (!StringUtil.isNotNull(this.isPrecision)) {
            this.isPrecision = Boolean.FALSE;
        }
        if (!this.isPrecision) {
            if (StringUtil.isNotEmpty(this.pollutionSourceTypeList)) {
                condition.append(" and exists(select 1 from DtoEnterpriseExtend pe where pe.entId = p.id and ( ");
                for (int i = 0; i < this.pollutionSourceTypeList.size(); i++) {
                    String sourceType = this.pollutionSourceTypeList.get(i);
                    String key = "sourceType" + i;
                    condition.append(" pe.pollutionSourceType like :").append(key);
                    values.put(key, "%" + sourceType + "%");
                    if (i < this.pollutionSourceTypeList.size() - 1) {
                        condition.append(" or ");
                    }
                }
                condition.append(" ))");
            }
        } else {
            if (StringUtil.isNotEmpty(this.pollutionSourceTypeList)) {
                condition.append(" and exists(select 1 from DtoEnterpriseExtend pe where pe.entId = p.id and ( ");
                Integer count = (2 * this.pollutionSourceTypeList.size() - 1);
                for (int i = 0; i < this.pollutionSourceTypeList.size(); i++) {
                    String sourceType = this.pollutionSourceTypeList.get(i);
                    String key = "sourceType" + i;
                    condition.append(" pe.pollutionSourceType like :").append(key);
                    values.put(key, "%" + sourceType + "%");
                    if (i < this.pollutionSourceTypeList.size() - 1) {
                        condition.append(" and ");
                    }
                }
                condition.append(" ) and LENGTH(pe.pollutionSourceType) = :count)");
                values.put("count", count);
            } else {
                //污染源类型为空
                condition.append(" and ( exists(select 1 from DtoEnterpriseExtend pe where pe.entId = p.id " +
                        "and pe.pollutionSourceType = :type ) or not exists ( select 1 from DtoEnterpriseExtend pee " +
                        "where pee.entId = p.id ) )");
                values.put("type", "");
            }
        }

        if (StringUtil.isNotNull(this.isSyncPollutionDischarge)){
            condition.append(" and isSyncPollutionDischarge = :isSyncPollutionDischarge");
            values.put("isSyncPollutionDischarge", this.isSyncPollutionDischarge);
        }

        condition.append(" and isDeleted =0");
        return condition.toString();
    }

}