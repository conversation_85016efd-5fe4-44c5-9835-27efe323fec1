package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消费端消息监控查询条件
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/16
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MessageReceiveMonitorCriteria extends BaseCriteria {

    /**
     * 交换机、路由键、队列
     */
    private String key;

    /**
     * 是否发送成功
     */
    private Boolean isSuccess;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (m.exchange like :key or m.routingKey like :key or m.queue like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (this.isSuccess != null) {
            condition.append(" and m.isSuccess = :isSuccess ");
            values.put("isSuccess", this.isSuccess);
        }
        return condition.toString();
    }
}
