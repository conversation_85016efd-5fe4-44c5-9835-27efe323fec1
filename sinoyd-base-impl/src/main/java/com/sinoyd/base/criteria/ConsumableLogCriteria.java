package com.sinoyd.base.criteria;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消耗领用记录查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/3/11
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumableLogCriteria extends BaseCriteria {

    private String consumableId;// 消耗品Id

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtils.isNotNullAndEmpty(this.consumableId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.consumableId)) {
            condition.append(" and consumableId = :consumableId");
            values.put("consumableId", this.consumableId);
        }
        return condition.toString();
    }
}