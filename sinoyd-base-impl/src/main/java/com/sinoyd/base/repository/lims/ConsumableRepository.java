package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 消耗品管理仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface ConsumableRepository extends IBaseJpaPhysicalDeleteRepository<DtoConsumable, String> {

    /**
     * 根据codeInStation及id查询
     *
     * @param codeInStation 本站编号
     * @param consumableId  主键id
     * @return DtoConsumable
     */
    @Query("select d from DtoConsumable d  where d.id <> :consumableId " +
            "and d.codeInStation is not null and d.codeInStation <> '' and d.codeInStation = :codeInStation ")
    DtoConsumable findByCodeInStationAndId(@Param("codeInStation") String codeInStation, @Param("consumableId") String consumableId);

    /**
     * 根据详单的parentId查询消耗品信息
     * @param parentId 本站编号
     * @return DtoConsumable
     */
    // @Query("select from DtoConsumable p where p.id = :parentId")
    // DtoConsumable findConsumableByParentId(String parentId);

    /***
     * 获取相关消耗品id
     * @param isStandard 是否标准物质
     * @return
     */
    @Query("select d.id from DtoConsumable d where d.isStandard = :isStandard")
    List<String> findByIsStandard(@Param("isStandard") Boolean isStandard);

    /**
     * 根据id数组获取消耗品
     * @param ids
     * @return
     */
    @Query("select d from DtoConsumable d where d.id in :ids")
    List<DtoConsumable> findByIds(@Param("ids") Collection<String> ids);
} 