package com.sinoyd.base.repository.base;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.List;

/**
 * LIMS 默认Repository接口
 *
 * @param <T> 实体
 * @param <ID> 主键
 * <AUTHOR>
 * @version V1.0.0 2022/3/29
 * @since V100R001
 */
@NoRepositoryBean
public interface LimsRepository<T, ID extends Serializable> extends CrudRepository<T, ID>, JpaSpecificationExecutor<T> {
    /**
     * 批量新增
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    <S extends T> List<S> batchInsert(Iterable<S> entities);

    /**
     * 批量更新
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    <S extends T> List<S> batchUpdate(Iterable<S> entities);


    /**
     * 带null值更新
     * @param s   实体集合
     * @param <S> 实体类型
     * @return   实体
     */
    <S extends T> S saveWithNull(S s);


    /**
     * 带null值更新
     * @param entities   实体集合
     * @param <S> 实体类型
     * @return   实体
     */
    <S extends T> List<S> saveWithNull(Iterable<S> entities);
}
