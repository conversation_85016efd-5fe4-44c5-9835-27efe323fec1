package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
public interface EnterpriseRepository extends IBaseJpaRepository<DtoEnterprise, String>, LimsRepository<DtoEnterprise, String> {

    /**
     * 根据名称获取实体
     *
     * @param name 企业名称
     * @param type 企业类型
     * @return 企业实体
     */
    @Query("select p from DtoEnterprise p where p.isDeleted = 0 and p.name = :name  and  p.type=:type")
    List<DtoEnterprise> getByName(@Param("name") String name, @Param("type") Integer type);


    /**
     * 根据名称与id获取重复的条数
     *
     * @param id
     * @param name
     * @return
     */
    @Query("select count(p) from DtoEnterprise p where p.id <> :id and p.name = :name and  p.type=:type and p.isDeleted = 0")
    Integer getCountByName(@Param("id") String id, @Param("name") String name, @Param("type") Integer type);

    /**
     * 根据名称与社会信用代码与id获取重复条数
     *
     * @param id               企业id
     * @param socialCreditCode 社会信用代码
     * @param name             企业名称
     * @param type             企业类型
     * @return 条数
     */
    @Query("select count(p) from DtoEnterprise p where p.id <> :id and p.socialCreditCode = :socialCreditCode and p.name = :name and p.type=:type and p.isDeleted = 0")
    Integer getCountByNameAndSocialCreditCode(@Param("id") String id, @Param("socialCreditCode") String socialCreditCode, @Param("name") String name, @Param("type") Integer type);


    /**
     * 根据社会信用代码与id获取重复的条数
     *
     * @param id
     * @param socialCreditCode
     * @return
     */
    @Query("select count(p) from DtoEnterprise p where p.id <> :id and p.socialCreditCode = :socialCreditCode and  p.type=:type and p.isDeleted = 0")
    Integer getCountBySocialCreditCode(@Param("id") String id, @Param("socialCreditCode") String socialCreditCode, @Param("type") Integer type);


    /**
     * 获取所有假删的企业信息
     *
     * @return 返回排除假删的数据
     */
    @Override
    @Query("select p from DtoEnterprise p where p.isDeleted = 0")
    List<DtoEnterprise> findAll();

    /**
     * 根据id数组获取信息
     *
     * @param ids 主键ids
     * @return 返回信息
     */
    @Query("select d from DtoEnterprise d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoEnterprise> findAll(@Param("ids") Iterable<String> ids);

    /**
     * 返回所有的带假删的信息
     *
     * @return 返回带删除的信息
     */
    @Query("select p from DtoEnterprise p")
    List<DtoEnterprise> findAllDeleted();


    /**
     * 返回所有的带假删的信息
     *
     * @param ids 主键的ids
     * @return 返回带删除的信息
     */
    @Query("select p from DtoEnterprise p where p.id in :ids")
    List<DtoEnterprise> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 拼音字段不存在的企业
     *
     * @return 拼音字段不存在的企业
     */
    @Query("select p from DtoEnterprise p where (p.fullPinYin is null or p.fullPinYin = '' or p.pinYin is null or p.pinYin = '') and p.name is not null and p.isDeleted = 0")
    List<DtoEnterprise> findPinYinIsNotExit();


    /**
     * 根据企业类型查询
     *
     * @param type 企业类型
     * @return 企业集合
     */
    @Query("select t from DtoEnterprise t where t.isDeleted = false " +
            "and bitand (t.type , :type) <> 0 ")
    List<DtoEnterprise> findByIsDeletedFalseAndType(@Param("type") Integer type);

    /**
     * 根据名称获取id
     *
     * @return
     */
    @Query("select p.id from DtoEnterprise p where p.isDeleted = 0 and p.name = :name")
    String findEnterpriseIdByName(@Param("name") String name);

    /**
     * 根据社会信用代码查询
     *
     * @param socialCreditCode 社会信用代码
     * @return 查询结果
     */
    List<DtoEnterprise> findBySocialCreditCodeAndIsDeletedFalse(String socialCreditCode);
}