package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.DtoExcelParam;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */

@Api(tags = "文件管理: 文件管理服务")
@RestController
@RequestMapping("/api/base/document")
@Validated
public class DocumentController extends BaseJpaController<DtoDocument, String, DocumentService> {

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "按主键id查询文件", notes = "按主键id查询文件")
    @GetMapping("/{id}")
    public RestResponse<DtoDocument> findOne(@PathVariable String id) {
        RestResponse<DtoDocument> restResponse = new RestResponse<>();
        DtoDocument document = service.findOne(id);
        restResponse.setData(document);

        restResponse.setRestStatus(StringUtil.isNull(document) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 根据业务id查询
     *
     * @param objectIds 业务id集合
     * @return 结果
     */
    @PostMapping("/objects")
    public RestResponse<List<DtoDocument>> findAll(@RequestBody List<String> objectIds) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        restResp.setData(service.findByObjectIds(objectIds));
        return restResp;
    }

    /**
     * 分页获取文件
     *
     * @param documentCriteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询文件", notes = "分页动态条件查询文件")
    @GetMapping("")
    public RestResponse<List<DtoDocument>> findByPage(DocumentCriteria documentCriteria) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();

        PageBean<DtoDocument> page = super.getPageBean();
        service.findByPage(page, documentCriteria);

        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());
        return restResponse;

    }

    /**
     * 新增文档
     *
     * @param document
     * @return
     */
    @ApiOperation(value = "新增文档", notes = "新增文档")
    @PostMapping("")
    public RestResponse<DtoDocument> create(@Validated @RequestBody DtoDocument document) {
        RestResponse<DtoDocument> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoDocument data = service.save(document);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }


    /**
     * 批量删除(假删)
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除文档", notes = "根据id批量删除文档")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNotEmpty(ids) ? service.logicDeleteById(ids) : 0);
        return restResponse;
    }

    /**
     * 删除(假删)
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id批量删除文档", notes = "根据id批量删除文档")
    @DeleteMapping("/deleteByIds/{id}")
    public RestResponse<String> deleteByIds(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.deleteByFolderId(id));
        return restResponse;
    }

    /**
     * 单个删除(假删)
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除文档", notes = "根据id删除文档")
    @DeleteMapping("/{id}")
    public RestResponse<String> deleteOne(@PathVariable String id) {
        List<String> ids = new ArrayList<>();
        ids.add(id);
        return delete(ids);
    }


    @ApiOperation(value = "上传文档", notes = "上传文档")
    @PostMapping("/upload")
    public RestResponse<List<DtoDocument>> fileUpload(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.upload(request, null));
        return restResponse;
    }


    @ApiOperation(value = "上传报表模板文件", notes = "上传报表模板文件")
    @PostMapping("/uploadTemplate")
    public RestResponse<List<DtoDocument>> templateUpload(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.uploadReportConfig(request));
        return restResponse;
    }

    @PostMapping("/download/stream")
    public RestResponse<Map<String, byte[]>> fileDownAsStream(@RequestBody DtoDocument document) {
        RestResponse<Map<String, byte[]>> restResp = new RestResponse<>();
        restResp.setData(service.downloadAsStream(document));
        return restResp;
    }

    /**
     * 文件下载
     *
     * @param documentId 下载的Id
     * @param response   响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{documentId}")
    public RestResponse<String> fileDownload(@PathVariable String documentId, HttpServletResponse response) throws IOException {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(documentId, response));
        return restResp;
    }


    /**
     * 文件下载(返回绝对路径)
     *
     * @param documentId 下载的Id
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/downloadPath/{documentId}")
    public RestResponse<String> fileDownload(@PathVariable String documentId) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.downloadPath(documentId));
        restResp.setMsg(service.downloadPath(documentId));
        return restResp;
    }

    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/appDownload/{documentId}")
    public RestResponse<String> appDownload(@PathVariable String documentId, HttpServletResponse response) throws IOException {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(documentId, response));
        return restResp;
    }

    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download")
    public RestResponse<String> fileDownload(@RequestParam("folderId") String folderId, @RequestParam("path") String path, HttpServletResponse response) throws IOException {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(folderId, path, response));
        return restResp;
    }

    @ApiOperation(value = "LuckySheet同步服务器excel", notes = "LuckySheet同步服务器excel")
    @PostMapping("/syncExcel/{documentId}")
    public RestResponse<String> syncExcel(@PathVariable String documentId, @RequestBody List<DtoExcelParam> excelParams) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.syncExcel(documentId, excelParams));
        return restResp;
    }

    /**
     * 提供统一接口获取相应的文件路径
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @ApiOperation(value = "获取路径", notes = "获取路径")
    @PostMapping("/{code}")
    public RestResponse<String> getDocumentPath(@PathVariable String code, @RequestBody Map<String, Object> map) throws Exception {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPath(code, map));
        return restResp;
    }

    /**
     * 提供统一接口获取相应的文件路径（移动端使用）
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @ApiOperation(value = "获取路径", notes = "获取路径")
    @PostMapping("/pathByPlaceholder/{code}")
    public RestResponse<String> getDocumentPathByPlaceholder(@PathVariable String code, @RequestBody Map<String, Object> map) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPathByPlaceholder(code, map));
        return restResp;
    }

    @ApiOperation(value = "上传文档", notes = "上传文档")
    @PostMapping("/baseImage")
    public RestResponse<Boolean> uploadBase64Content(HttpServletRequest request) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.uploadBase64Content(request));
        return restResponse;
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览传输对象
     * @param response 响应流
     */
    @PostMapping("/preview/{code}")
    public void preview(@PathVariable String code, @RequestBody DocumentPreviewVO vo, HttpServletResponse response) {
        service.preview(code, vo, response);
    }

    /**
     * 文件复制
     *
     * @param document 数据
     * @return RestResponse<Void>
     */
    @PostMapping("/copyFile")
    public RestResponse<Void> copyFile(@Validated @RequestBody DtoDocument document) {
        RestResponse<Void> response = new RestResponse<>();
        service.copyFile(document.getCode(), document.getFolderId(), document.getId());
        return response;
    }

    /**
     * 命名文件
     *
     * @param document 数据
     * @return RestResponse<Void>
     */
    @PostMapping("/renameFile")
    public RestResponse<Void> renameFile(@RequestBody DtoDocument document) {
        RestResponse<Void> response = new RestResponse<>();
        service.renameFile(document.getId(), document.getFilename());
        return response;
    }

    /**
     * 测点示意图在线编辑更改
     *
     * @param request 请求
     * @return RestResponse<Void>
     */
    @PostMapping("/onlineEdit")
    public RestResponse<Void> onlineEdit(HttpServletRequest request) {
        RestResponse<Void> response = new RestResponse<>();
        service.onlineEdit(request);
        return response;
    }

    /**
     * 文件转blog
     *
     * @param document 数据对象
     * @return RestResponse<byte [ ]>
     */
    @PostMapping("/convertFile2Blob")
    public RestResponse<byte[]> convertFile2Blob(@RequestBody DtoDocument document) {
        RestResponse<byte[]> response = new RestResponse<>();
        response.setData(service.convertFile2Blob(document.getPath()));
        return response;
    }

    /**
     * 获取允许上传文件类型
     *
     * @return RestResponse<String>
     */
    @GetMapping("/getAllowFileSuffix")
    public RestResponse<String> getAllowFileSuffix() {
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.getAllowFileSuffix());
        return response;
    }

    /**
     * 将文件转成base64
     *
     * @param document 数据
     * @return RestResponse<String>
     */
    @PostMapping("/convertFileToBase64")
    public RestResponse<String> convertFileToBase64(@RequestBody DtoDocument document) {
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.convertFileToBase64(document.getPath()));
        return response;
    }
}