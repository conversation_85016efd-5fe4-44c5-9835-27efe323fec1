package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Api(tags = "客户管理: 客户管理服务")
@RestController
@RequestMapping("/api/base/enterprise")
@Validated
public class EnterpriseController extends BaseJpaController<DtoEnterprise, String, EnterpriseService> {


    /**
     * 新增
     *
     * @param enterprise
     */
    @ApiOperation(value = "新增客户", notes = "新增客户")
    @PostMapping("")
    public RestResponse<DtoEnterprise> create(@Validated @RequestBody DtoEnterprise enterprise) {
        RestResponse<DtoEnterprise> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoEnterprise data = service.save(enterprise);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改
     *
     * @param enterprise
     */
    @ApiOperation(value = "修改客户", notes = "修改客户")
    @PutMapping("")
    public RestResponse<DtoEnterprise> update(@Validated @RequestBody DtoEnterprise enterprise) {
        RestResponse<DtoEnterprise> restResp = new RestResponse<>();
        DtoEnterprise data = service.update(enterprise);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 单个删除 假删
     *
     * @param id
     */
    @ApiOperation(value = "根据id删除客户", notes = "根据id删除客户")
    @DeleteMapping("/{id}")
    public RestResponse<String> deleteOne(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除 假删
     *
     * @param ids
     */
    @ApiOperation(value = "根据id批量删除客户", notes = "根据id批量删除客户")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 单个获取
     *
     * @param id
     */
    @ApiOperation(value = "根据id查询客户", notes = "根据id查询客户")
    @GetMapping("/{id}")
    public RestResponse<DtoEnterprise> getById(@PathVariable String id) {
        RestResponse<DtoEnterprise> restResp = new RestResponse<>();
        DtoEnterprise enterprise = service.findOne(id);
        restResp.setData(enterprise);

        restResp.setRestStatus(StringUtil.isNull(enterprise) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 批量获取
     *
     * @param enterpriseCriteria 查询条件
     */
    @ApiOperation(value = "分页条件查询客户", notes = "分页条件查询客户")
    @GetMapping("")
    public RestResponse<List<DtoEnterprise>> findByPage(EnterpriseCriteria enterpriseCriteria) {
        RestResponse<List<DtoEnterprise>> restResp = new RestResponse<>();
        PageBean<DtoEnterprise> pb = super.getPageBean();
        service.findByPage(pb, enterpriseCriteria);
        restResp.setData(pb.getData());
        restResp.setCount(pb.getRowsCount());
        return restResp;
    }

    /**
     * 为拼音为空的企业添加拼音字段
     *
     * @return 为拼音为空的企业添加拼音字段
     */
    @ApiOperation(value = "为拼音为空的企业添加拼音字段", notes = "为拼音为空的企业添加拼音字段")
    @PostMapping("/changePinYin")
    public RestResponse<List<DtoEnterprise>> changePinYin() {
        RestResponse<List<DtoEnterprise>> restResp = new RestResponse<>();
        restResp.setData(service.changePinYin());
        return restResp;
    }

    /**
     * 据id查询企业(多个)
     *
     * @param ids 企业id集合
     * @return 企业集合
     */
    @ApiOperation(value = "根据id查询企业(多个)", notes = "据id查询企业(多个)")
    @PostMapping("/multiple")
    public RestResponse<List<DtoEnterprise>> findByIds(@RequestBody List<String> ids) {
        RestResponse<List<DtoEnterprise>> restResp = new RestResponse<>();
        restResp.setData(service.findAll(ids));
        return restResp;
    }

    /**
     * 查询全部企业
     *
     * @return 企业集合
     */
    @ApiOperation(value = "查询全部企业", notes = "查询全部企业")
    @PostMapping("/customer/all")
    public RestResponse<List<DtoEnterprise>> findAll() {
        RestResponse<List<DtoEnterprise>> restResp = new RestResponse<>();
        restResp.setData(service.findAllForCustomer());
        return restResp;
    }

    /**
     * 查询企业的扩展信息
     *
     * @param ids 企业id集合
     * @return 企业扩展信息集合
     */
    @ApiOperation(value = "查询企业的扩展信息", notes = "查询企业的扩展信息")
    @PostMapping("/extend/multiple")
    public RestResponse<List<DtoEnterpriseExtend>> findEnterpriseExtend(@RequestBody List<String> ids) {
        RestResponse<List<DtoEnterpriseExtend>> restResp = new RestResponse<>();
        restResp.setData(service.findEnterpriseExtend(ids));
        return restResp;
    }
}