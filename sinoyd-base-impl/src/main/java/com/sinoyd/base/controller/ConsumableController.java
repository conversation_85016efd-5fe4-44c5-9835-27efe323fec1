package com.sinoyd.base.controller;

import java.util.List;

import com.sinoyd.base.criteria.ConsumableCriteria;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.customer.DtoStandardTemp;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 消耗品管理
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Api(tags = "消耗品: 消耗品服务")
@RestController
@RequestMapping("/api/base/consumable")
@Validated
public class ConsumableController extends BaseJpaController<DtoConsumable, String, ConsumableService> {

    /**
     * 按详单parentId获取消耗品信息
     *
     * @param parentId 消耗品详单parentId
     * @return RestResponse<DtoConsumable>
     */
    @ApiOperation(value = "按详单parentId查询消耗品信息", notes = "按详单parentId查询消耗品信息")
    @GetMapping("/{parentId}")
    public RestResponse<DtoConsumable> getById(@PathVariable(name = "parentId") String parentId) {
        RestResponse<DtoConsumable> restResp = new RestResponse<>();
        DtoConsumable entity = service.findOne(parentId);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 分页获取消耗品信息
     *
     * @param criteria 查询条件
     * @return RestResponse<List   <   DtoConsumable>>
     */
    @ApiOperation(value = "分页动态条件查询消耗品/标样", notes = "分页动态条件查询消耗品/标样")
    @GetMapping
    public RestResponse<List<DtoConsumable>> findByPage(ConsumableCriteria criteria) {
        RestResponse<List<DtoConsumable>> restResp = new RestResponse<>();
        PageBean<DtoConsumable> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增消耗品
     *
     * @param consumable 消耗品基本信息
     * @return RestResponse<List   <   DtoConsumable>>
     */
    @ApiOperation(value = "新增消耗品", notes = "新增消耗品")
    @PostMapping
    public RestResponse<DtoConsumable> create(@Validated @RequestBody DtoConsumable consumable) {
        RestResponse<DtoConsumable> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoConsumable data = service.save(consumable);

        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改消耗品信息
     *
     * @param consumables 消耗品
     * @return RestResponse<List   <   DtoConsumable>>
     */
    @ApiOperation(value = "更新消耗品", notes = "更新消耗品")
    @PutMapping
    public RestResponse<DtoConsumable> update(@Validated @RequestBody DtoConsumable consumables) {
        RestResponse<DtoConsumable> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoConsumable data = service.update(consumables);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 批量删除消耗品
     *
     * @param ids 消耗品id集合
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除消耗品", notes = "删除消耗品")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        return restResp;
    }



    /**
     * 查询标准样品信息
     *
     * @param id 标样id
     * @return RestResponse<DtoStandardTemp>
     */
    @ApiOperation(value = "查询标准样品信息", notes = "查询标准样品信息")
    @GetMapping("/standard/{id}")
    public RestResponse<DtoStandardTemp> getStandardById(@PathVariable String id) {
        RestResponse<DtoStandardTemp> restResp = new RestResponse<>();
        DtoStandardTemp standardTemp = service.getStandardById(id);
        restResp.setData(standardTemp);
        return restResp;

    }

    /**
     * 分页获取标准样品信息
     * @param criteria 查询条件
     * @return RestResponse<List < DtoStandardTemp>>
     */
    // @ApiOperation(value = "分页动态条件查询标准样品", notes = "分页动态条件查询标准样品")
    // @GetMapping("/standard")
    // public RestResponse<List<DtoStandardTemp>> findStandardByPage(ConsumableCriteria criteria) 
    // {
    //     System.out.println("分页标样。。。");
    //     RestResponse<List<DtoStandardTemp>> restRespTemp = new RestResponse<>();
    //     RestResponse<List<DtoConsumable>> restResp = new RestResponse<>();
    //     PageBean<DtoConsumable> page = super.getPageBean();
    //     service.findByPage(page, criteria);
    //     service.getStandardById(criteria.ge);

    //     restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
    //     restResp.setData(page.getData());
    //     restResp.setCount(page.getRowsCount());

    //     return restRespTemp;
    // }

    /**
     * 新增标准样品信息
     *
     * @param entity 准样品信息json字符串
     * @return RestResponse<DtoStandardTemp>
     */
    @PostMapping("/createStandard")
    public RestResponse<DtoConsumable> createStandard(@Validated @RequestBody DtoConsumable entity) {
        RestResponse<DtoConsumable> restResp = new RestResponse<>();
        DtoConsumable standardTemp = service.createStandard(entity);
        restResp.setData(standardTemp);
        return restResp;
    }

    /**
     * 修改标准样品信息
     *
     * @param entity 准样品信息json字符串
     * @return 返回修改的标准样品信息
     */
    @PutMapping("/updateStandard")
    public RestResponse<DtoConsumable> updateStandard(@Validated @RequestBody DtoConsumable entity) {
        RestResponse<DtoConsumable> restResp = new RestResponse<>();
        DtoConsumable standardTemp = service.updateStandard(entity);
        restResp.setData(standardTemp);
        return restResp;
    }

    /**
     * 复制标准样品
     *
     * @param entity 实体
     * @return
     */
    @PostMapping("/copy")
    public RestResponse<Void> copy(@Validated @RequestBody DtoConsumable entity) {
        RestResponse<Void> response = new RestResponse<>();
        service.copy(entity.getId(), entity.getTimes());
        return response;
    }
}