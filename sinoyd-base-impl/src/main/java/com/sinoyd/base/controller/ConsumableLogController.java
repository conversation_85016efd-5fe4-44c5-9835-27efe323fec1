package com.sinoyd.base.controller;

import java.util.List;

import com.sinoyd.base.criteria.ConsumableLogCriteria;
import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.base.service.ConsumableLogService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 领用记录
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Api(tags = "消耗品领用: 消耗品领用服务")
@RestController
@RequestMapping("/api/base/consumableLog")
@Validated
public class ConsumableLogController extends BaseJpaController<DtoConsumableLog,String,ConsumableLogService> {

    /**
     * 分页查询领用记录
     * @param criteria 领用记录查询条件
     * @return RestResponse<List<DtoConsumableLog>>
     */
    @ApiOperation(value = "分页查询领用记录", notes = "分页查询领用记录")
    @GetMapping
    public RestResponse<List<DtoConsumableLog>> findByPage(ConsumableLogCriteria criteria){
        RestResponse<List<DtoConsumableLog>> restResp = new RestResponse<>();
        
        PageBean<DtoConsumableLog> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 新增领用记录
     * @param entity 领用实体
     * @return RestResponse<DtoConsumableLog>
     */
    @ApiOperation(value = "新增领用记录", notes = "新增领用记录")
    @PostMapping
    public RestResponse<DtoConsumableLog> create(@Validated @RequestBody DtoConsumableLog entity)
    {
        RestResponse<DtoConsumableLog> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoConsumableLog data = service.save(entity);
        restResp.setData(data);

        return restResp;
    }

    /**
     * 删除最新的一条领用记录
     *
     * @param consumableLogId 领用记录id
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "删除最新的一条领用记录", notes = "删除最新的一条领用记录")
    @DeleteMapping("/{id}")
    public RestResponse<Integer> delete(@PathVariable(name = "id") String consumableLogId){
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setData(service.logicDeleteById(consumableLogId));
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }
}