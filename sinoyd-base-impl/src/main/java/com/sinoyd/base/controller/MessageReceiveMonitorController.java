package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.MessageReceiveMonitorCriteria;
import com.sinoyd.base.dto.lims.DtoMessageReceiveMonitor;
import com.sinoyd.base.service.MessageReceiveMonitorService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消费端消息队列监控 Controller
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/16
 * @since V100R001
 */
@RestController
@RequestMapping("/api/base/messageReceiveMonitor")
public class MessageReceiveMonitorController extends BaseJpaController<DtoMessageReceiveMonitor, String, MessageReceiveMonitorService> {


    /**
     * 分页查询
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    @GetMapping
    public RestResponse<List<DtoMessageReceiveMonitor>> findByPage(MessageReceiveMonitorCriteria criteria) {
        RestResponse<List<DtoMessageReceiveMonitor>> response = new RestResponse<>();
        PageBean<DtoMessageReceiveMonitor> pb = super.getPageBean();
        service.findByPage(pb, criteria);
        List<DtoMessageReceiveMonitor> data = pb.getData();
        response.setRestStatus(StringUtil.isNotEmpty(data) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        response.setData(data);
        response.setCount(pb.getRowsCount());
        return response;
    }


    /**
     * 重新发送消息
     *
     * @param id 消息监控ID
     * @return 重新发送消息
     */
    @PostMapping("/reSend/{id}")
    public RestResponse<Void> reSendMessage(@PathVariable("id") String id) {
        RestResponse<Void> response = new RestResponse<>();
        service.reSendMessage(id);
        return response;
    }
}
