package com.sinoyd.base.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * WebSocket状态配置服务
 *
 * <AUTHOR>
 * @version V1.0.0 2025/6/26
 * @since V100R001
 */
@Api(tags = "WebSocket状态配置服务")
@RestController
@RequestMapping("/api/websockets/status")
@Validated
public class WebSocketStatusController {

    @Value("${websocket.enabled:false}")
    private boolean websocketEnabled;

    @GetMapping()
    public RestResponse<Boolean> getStatus() {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setData(websocketEnabled);
        return restResp;
    }
}
