package com.sinoyd.base.controller;

import java.util.List;

import com.sinoyd.base.criteria.AnalyzeItemCriteria;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.ApiOperation;

/**
 * 分析项目管理接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@RestController
@RequestMapping("/api/base/analyzeItem")
@Validated
public class    AnalyzeItemController extends BaseJpaController<DtoAnalyzeItem, String, AnalyzeItemService> {

    /**
     * 根据id查询分析项目
     *
     * @param id 主键ID
     * @return 返回分析项目
     */
    @ApiOperation(value = "根据id查询分析项目", notes = "根据id查询分析项目")
    @GetMapping("/{id}")
    @BusinessLog
    public RestResponse<DtoAnalyzeItem> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoAnalyzeItem> restResp = new RestResponse<>();

        DtoAnalyzeItem entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询分析项目
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "分页动态条件查询分析项目", notes = "分页动态条件查询分析项目")
    @GetMapping
    public RestResponse<List<DtoAnalyzeItem>> findByPage(AnalyzeItemCriteria criteria) {

        RestResponse<List<DtoAnalyzeItem>> restResp = new RestResponse<>();

        PageBean<DtoAnalyzeItem> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增分析项目
     *
     * @param entity 保存实体
     * @return 返回分析项目结果
     */
    @ApiOperation(value = "新增分析项目", notes = "新增分析项目")
    @PostMapping
    public RestResponse<DtoAnalyzeItem> save(@Validated @RequestBody DtoAnalyzeItem entity) {

        RestResponse<DtoAnalyzeItem> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeItem data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改分析项目
     *
     * @param entity 保存实体
     * @return 返回分析项目结果
     */
    @ApiOperation(value = "修改分析项目", notes = "修改分析项目")
    @Transactional
    @PutMapping
    public RestResponse<DtoAnalyzeItem> update(@Validated @RequestBody DtoAnalyzeItem entity) {

        RestResponse<DtoAnalyzeItem> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeItem data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除分析项目(逻辑删除)
     *
     * @param id 删除id
     * @return 返回删除行数
     */
    @ApiOperation(value = "根据id删除分析项目(逻辑删除)", notes = "根据id删除分析项目(逻辑删除)")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        restResp.setCount(service.logicDeleteById(id));

        return restResp;
    }

    /**
     * 批量删除分析项目
     *
     * @param ids 删除ids
     * @return 返回删除行数
     */
    @ApiOperation(value = "批量删除分析项目", notes = "批量删除分析项目")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        restResp.setCount(service.logicDeleteById(ids));

        return restResp;
    }

    /**
     * 批量转换分析项目拼音(全拼)
     * @return
     */
    @ApiOperation(value = "批量转换分析项目拼音(全拼)", notes = "批量转换分析项目拼音(全拼)")
    @PostMapping("/changePinYin")
    public  RestResponse<List<DtoAnalyzeItem>> changePinYinFull() {
        RestResponse<List<DtoAnalyzeItem>> restResp = new RestResponse<>();
        restResp.setData(service.changePinYinFull());
        return restResp;
    }

}