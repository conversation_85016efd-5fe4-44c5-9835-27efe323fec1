package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.factory.quality.QualityReplace;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * 回收率
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
@Slf4j
public class RecoveryServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        String qcAddedValue = "";
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcTestValue = valueList.get(1);
            //加入量值
            String qcValue = valueList.get(2);
            //原样的出证结果
            String testValue = samValue;
            if (valueList.size() > 3) {
                if("出证结果".equals(controlLimit.getCheckItemOther())) {
                    testValue = valueList.get(3);
                }
            }
            if (DivationUtils.isNumber(samValue) && DivationUtils.isNumber(qcTestValue) && DivationUtils.isNumber(qcValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = controlLimit.getRangeConfigData();//controlLimit.getRangeConfig();
                Boolean flag = true;
                if (StringUtil.isNotEmpty(qcRangeLimit) && DivationUtils.isNumber(testValue)) {
                    flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(testValue), calculationService);
                }
                if (flag) {
                    //增值
                    BigDecimal zz = BigDecimal.ZERO;
                    //加标回收率
                    BigDecimal qcRecoverRate = BigDecimal.ZERO;
                    //倍数1000
                    BigDecimal baseValue1000 = new BigDecimal(1000);
                    //倍数100
                    BigDecimal baseValue100 = new BigDecimal(100);
                    try {
                        BigDecimal realSampleTestValueDecimal = new BigDecimal(samValue);
                        BigDecimal qcTestValueDecimal = new BigDecimal(qcTestValue);
                        BigDecimal qcValueDecimal = new BigDecimal(qcValue);
                        //区分回收率是替代回收，还是加标回收
                        if (new QualityReplace().qcTypeValue().equals(controlLimit.getQcType())) {
                            //加标回收率
                            qcRecoverRate = realSampleTestValueDecimal.divide(qcValueDecimal, 20, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        } else {
                            zz = (qcTestValueDecimal.subtract(realSampleTestValueDecimal)).multiply(baseValue1000).divide(baseValue1000);
                            //加标回收率
                            qcRecoverRate = zz.divide(qcValueDecimal, 20, BigDecimal.ROUND_HALF_UP).multiply(baseValue100);
                        }
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                        throw new BaseException("加标回收率计算发生错误");
                    }
                    //保留三位有效位数
                    //绝对值
                    retStr = qcRecoverRate.abs().toString();
                    //正好是三位整数
                    if (retStr.indexOf(".") == 3) {
                        retStr = qcRecoverRate.setScale(0, RoundingMode.HALF_EVEN).toString();
                    } else {
                        retStr = String.format("%s", qcRecoverRate.setScale(1, RoundingMode.HALF_EVEN).toString());
                    }
                    qcAddedValue = zz.toString();
                }
            }
        }
        //增值
        map.put("qcAddedValue", qcAddedValue);
        //回收率
        map.put("qcRate", retStr);
    }
}
