package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.customer.DtoEvaluationValueCopy;
import com.sinoyd.base.dto.customer.DtoEvaluationValueTemp;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.base.service.EvaluationLevelService;
import com.sinoyd.base.service.EvaluationValueService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * EvaluationValue操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
@Service
public class EvaluationValueServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEvaluationValue, String, EvaluationValueRepository> implements EvaluationValueService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private EvaluationCriteriaService evaluationCriteriaService;

    @Autowired
    @Lazy
    private EvaluationLevelService evaluationLevelService;

    private DimensionRepository dimensionRepository;

    @Override
    public void findByPage(PageBean<DtoEvaluationValue> pageBean, BaseCriteria evaluationValueCriteria) {
        pageBean.setEntityName("DtoEvaluationValue a,DtoAnalyzeItem b");
        pageBean.setSelect("select a,b.analyzeItemName");
        super.findByPage(pageBean, evaluationValueCriteria);
        List<DtoEvaluationValue> dataList = pageBean.getData();
        List<DtoEvaluationValue> newDataList = new ArrayList<>();

        Iterator<DtoEvaluationValue> iterator = dataList.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (iterator.hasNext()) {
            Object obj = iterator.next();
            Object[] objData = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoEvaluationValue dtoEvaluationValue = (DtoEvaluationValue) objData[0];
            dtoEvaluationValue.setAnalyzeItemName((String) objData[1]);
            newDataList.add(dtoEvaluationValue);
        }
        // 量纲赋值
        List<String> dimensionIds = newDataList.stream().map(DtoEvaluationValue::getDimensionId).distinct().collect(Collectors.toList());
        List<DtoDimension> dimensionList = StringUtil.isNotEmpty(dimensionIds) ? dimensionRepository.findAll(dimensionIds) : new ArrayList<>();
        newDataList.forEach(data ->
                dimensionList.stream().filter(p -> p.getId().equals(data.getDimensionId())).findFirst().ifPresent(dimension ->
                        data.setDimensionName(dimension.getDimensionName())));
        pageBean.setData(newDataList);
    }


    @Override
    public List<DtoEvaluationValue> findEvaluationValueByLevelId(List<String> levelIds) {
        return repository.findByLevelIdIn(levelIds);
    }

    @Override
    @Transactional
    public List<DtoEvaluationValue> addAnalyzeItem(DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        List<String> analyzeItemIds = dtoEvaluationValueTemp.getAnalyzeItemIds();
        if (StringUtil.isEmpty(analyzeItemIds)) {
            throw new BaseException("请选择相应的分析项目！");
        }
        //条件id
        String levelId = dtoEvaluationValueTemp.getLevelId();
        String evaluationId = dtoEvaluationValueTemp.getEvaluationId();
        List<DtoEvaluationValue> dtoEvaluationValues = repository.findByLevelId(levelId);
        //存在的分析项目id
        List<String> existAnalyzeItemIds = dtoEvaluationValues.stream().map(DtoEvaluationValue::getAnalyzeItemId).collect(Collectors.toList());
        List<DtoEvaluationValue> dtoEvaluationValueList = new ArrayList<>();
        for (String analyzeItemId : analyzeItemIds) {
            if (!existAnalyzeItemIds.contains(analyzeItemId)) {
                DtoEvaluationValue dtoEvaluationValue = new DtoEvaluationValue();
                dtoEvaluationValue.setAnalyzeItemId(analyzeItemId);
                dtoEvaluationValue.setEvaluationId(evaluationId);
                dtoEvaluationValue.setLevelId(levelId);
                dtoEvaluationValueList.add(dtoEvaluationValue);
            }
        }

        if (dtoEvaluationValueList.size() > 0) {
            List<DtoEvaluationValue> items = super.save(dtoEvaluationValueList);
            dtoEvaluationValues.addAll(items);//将新保存的集合加入到原先的集合中，再保存到redis中
            saveRedis(evaluationId, levelId, dtoEvaluationValues);
            dtoEvaluationValueList = items;
        }
        //要删除的分析项目ids
        List<String> deleteAnalyzeItemIds = existAnalyzeItemIds.stream().filter(p -> !analyzeItemIds.contains(p)).collect(Collectors.toList());

        if (deleteAnalyzeItemIds.size() > 0) {
            List<String> ids = dtoEvaluationValues.stream().filter(p -> deleteAnalyzeItemIds.contains(p.getAnalyzeItemId())).map(DtoEvaluationValue::getId).distinct().collect(Collectors.toList());
            if (ids.size() > 0) {
                logicDeleteById(ids);
                deleteRedis(evaluationId, levelId, analyzeItemIds);
            }
        }
        return dtoEvaluationValueList;
    }

    @Override
    @Transactional
    public List<DtoEvaluationValue> copyAnalyzeItem(DtoEvaluationValueCopy dtoEvaluationValueCopy) {
        List<String> analyzeItemIds = dtoEvaluationValueCopy.getAnalyzeItemIds();
        if (StringUtil.isEmpty(analyzeItemIds)) {
            throw new BaseException("请选择相应的分析项目！");
        }
        List<String> destinationLevelIds = dtoEvaluationValueCopy.getDestinationLevelIds();
        if (StringUtil.isEmpty(analyzeItemIds)) {
            throw new BaseException("请选择相应的条件项！");
        }
        List<DtoEvaluationValue> dtoEvaluationValueList = new ArrayList<>();
        String sourceLevelId = dtoEvaluationValueCopy.getSourceLevelId();
        //排除当前源的等级id
        destinationLevelIds = destinationLevelIds.stream().filter(p -> !p.equals(sourceLevelId)).collect(Collectors.toList());
        if (destinationLevelIds.size() > 0) { //防止前端只选了当前与源一样的条件项，并且只选了它
            //源数据
            List<DtoEvaluationValue> sourceValues = repository.findByLevelId(sourceLevelId);
            List<DtoEvaluationValue> dtoEvaluationAllValues = repository.findByLevelIdIn(destinationLevelIds);
            String evaluationId = dtoEvaluationValueCopy.getEvaluationId();
            for (String levelId : destinationLevelIds) {
                List<DtoEvaluationValue> itemList = new ArrayList<>();
                List<DtoEvaluationValue> dtoEvaluationValues = dtoEvaluationAllValues.stream().filter(p -> p.getLevelId().equals(levelId)).collect(Collectors.toList());
                //存在的分析项目id
                List<String> existAnalyzeItemIds = dtoEvaluationValues.stream().map(DtoEvaluationValue::getAnalyzeItemId).collect(Collectors.toList());
                for (String analyzeItemId : analyzeItemIds) {
                    if (!existAnalyzeItemIds.contains(analyzeItemId)) {
                        DtoEvaluationValue sourceValue = sourceValues.stream().filter(p -> p.getAnalyzeItemId().equals(analyzeItemId)).findFirst().orElse(null);
                        String upperLimit = "";//上限值
                        String upperLimitSymble = "";//上限运算符
                        String lowerLimit = "";//下限值
                        String lowerLimitSymble = "";//下限的运算符\
                        if (StringUtil.isNotNull(sourceValue)) {
                            upperLimit = sourceValue.getUpperLimit();
                            upperLimitSymble = sourceValue.getUpperLimitSymble();
                            lowerLimit = sourceValue.getLowerLimit();
                            lowerLimitSymble = sourceValue.getLowerLimitSymble();
                        }
                        DtoEvaluationValue dtoEvaluationValue = new DtoEvaluationValue();
                        dtoEvaluationValue.setAnalyzeItemId(analyzeItemId);
                        dtoEvaluationValue.setEvaluationId(evaluationId);
                        dtoEvaluationValue.setLevelId(levelId);
                        dtoEvaluationValue.setUpperLimit(upperLimit);
                        dtoEvaluationValue.setUpperLimitSymble(upperLimitSymble);
                        dtoEvaluationValue.setLowerLimit(lowerLimit);
                        dtoEvaluationValue.setLowerLimitSymble(lowerLimitSymble);
                        dtoEvaluationValueList.add(dtoEvaluationValue);
                        itemList.add(dtoEvaluationValue);

                    }
                }
                dtoEvaluationValues.addAll(itemList);//将新保存的集合加入到原先的集合中，再保存到redis中
                saveRedis(evaluationId, levelId, dtoEvaluationValues);
            }
        }
        if (dtoEvaluationValueList.size() > 0) {
            List<DtoEvaluationValue> items = super.save(dtoEvaluationValueList);
            return items;
        }
        return dtoEvaluationValueList;
    }

    @Transactional
    @Override
    public Integer updateSymbol(DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        List<String> ids = dtoEvaluationValueTemp.getIds(); //主键ids
        String evaluationId = dtoEvaluationValueTemp.getEvaluationId(); //评价标准id
        String levelId = dtoEvaluationValueTemp.getLevelId();//等级条件id
        String lowerLimitSymbol = dtoEvaluationValueTemp.getLowerLimitSymbol();
        String upperLimitSymbol = dtoEvaluationValueTemp.getUpperLimitSymbol();
        Integer count = repository.updateSymbol(lowerLimitSymbol, upperLimitSymbol, ids);
        updateRedis(evaluationId, levelId, lowerLimitSymbol, upperLimitSymbol,null, ids);
        return count;
    }

    @Transactional
    @Override
    public DtoEvaluationValue update(DtoEvaluationValue dtoEvaluationValue) {
        DtoEvaluationValue item = super.update(dtoEvaluationValue);
        updateRedis(item.getEvaluationId(), item.getLevelId(), item);
        return item;
    }

    @Override
    @Transactional
    public List<DtoEvaluationValue> update(Collection<DtoEvaluationValue> entities) {
        entities.forEach(p -> updateRedis(p.getEvaluationId(), p.getLevelId(), p));
        return super.update(entities);
    }

    @Transactional
    @Override
    public Integer deleteEvaluationValue(DtoEvaluationValueTemp dtoEvaluationValueTemp) {

        //评价标准ID
        String evaluationId = dtoEvaluationValueTemp.getEvaluationId();

        String levelId = dtoEvaluationValueTemp.getLevelId();

        //删除的ids
        List<String> ids = dtoEvaluationValueTemp.getIds();

        //带删除分析项目ids
        List<String> analyzeItemIds = dtoEvaluationValueTemp.getAnalyzeItemIds();

        Integer count = logicDeleteById(ids);
        deleteRedis(evaluationId, levelId, analyzeItemIds);
        return count;

    }

    @Override
    @Transactional
    public Integer batchUpdateDimension(DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        List<String> ids = dtoEvaluationValueTemp.getIds(); //主键ids
        String evaluationId = dtoEvaluationValueTemp.getEvaluationId(); //评价标准id
        String levelId = dtoEvaluationValueTemp.getLevelId();//等级条件id
        String dimensionId = dtoEvaluationValueTemp.getDimensionId();
        Integer count = repository.batchUpdateDimension(dimensionId, ids);
        updateRedis(evaluationId, levelId, null, null,dimensionId, ids);
        return count;
    }

    /**
     * 保存redis中
     *
     * @param evaluationId 评价标准id
     * @param levelId      评价等级id
     * @param newItems     新的集合
     */
    private void saveRedis(String evaluationId, String levelId, List<DtoEvaluationValue> newItems) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };

            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);

            List<DtoEvaluationLevel> evaluationLevels = item.getEvaluationLevel();
            Boolean isFlag = true;
            if (StringUtil.isNotNull(evaluationLevels)) {
                Optional<DtoEvaluationLevel> optional = evaluationLevels.stream().filter(p -> p.getId().equals(levelId)).findFirst();
                if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                    isFlag = false;
                    DtoEvaluationLevel dtoEvaluationLevel = optional.get();

                    evaluationLevels.remove(dtoEvaluationLevel);//先剔除

                    //再重新增加
                    dtoEvaluationLevel.setEvaluationValue(newItems);

                    evaluationLevels.add(dtoEvaluationLevel);

                }
            }
            if (isFlag) { //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                DtoEvaluationLevel dtoEvaluationLevel = evaluationLevelService.findOne(levelId);
                if (StringUtil.isNotNull(dtoEvaluationLevel)) {
                    dtoEvaluationLevel.setEvaluationValue(newItems);
                    evaluationLevels.add(dtoEvaluationLevel);
                }
            }
            item.setEvaluationLevel(evaluationLevels);
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(evaluationId);
        }
    }


    /**
     * 修改上限限值的时候保存redis
     *
     * @param evaluationId 评价标准id
     * @param levelId      等级id
     * @param newItem      新的数据集合
     */
    private void updateRedis(String evaluationId, String levelId, DtoEvaluationValue newItem) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };

            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);

            List<DtoEvaluationLevel> evaluationLevels = item.getEvaluationLevel();
            Boolean isFlag = true;
            if (StringUtil.isNotNull(evaluationLevels)) {
                Optional<DtoEvaluationLevel> optional = evaluationLevels.stream().filter(p -> p.getId().equals(levelId)).findFirst();
                if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                    isFlag = false;
                    DtoEvaluationLevel dtoEvaluationLevel = optional.get();
                    evaluationLevels.remove(dtoEvaluationLevel);//先移除
                    List<DtoEvaluationValue> dtoEvaluationValues = dtoEvaluationLevel.getEvaluationValue();
                    if (StringUtil.isNull(dtoEvaluationValues)) {  //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                        dtoEvaluationValues = repository.findByLevelId(levelId);
                    }
                    Optional<DtoEvaluationValue> optionalDtoEvaluationValue = dtoEvaluationValues.stream().filter(p -> p.getId().equals(newItem.getId())).findFirst();
                    if (StringUtil.isNotNull(optionalDtoEvaluationValue) && optionalDtoEvaluationValue.isPresent()) {
                        //排除已有的数据，重新添加
                        dtoEvaluationValues.remove(optionalDtoEvaluationValue.get());
                    }
                    dtoEvaluationValues.add(newItem);
                    dtoEvaluationLevel.setEvaluationValue(dtoEvaluationValues);
                    evaluationLevels.add(dtoEvaluationLevel);
                }
            }
            if (isFlag) { //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                DtoEvaluationLevel dtoEvaluationLevel = evaluationLevelService.findOne(levelId);
                if (StringUtil.isNotNull(dtoEvaluationLevel)) {
                    List<DtoEvaluationValue> newItems = repository.findByLevelId(levelId);
                    Optional<DtoEvaluationValue> optionalDtoEvaluationValue = newItems.stream().filter(p -> p.getId().equals(newItem.getId())).findFirst();
                    if (StringUtil.isNotNull(optionalDtoEvaluationValue) && optionalDtoEvaluationValue.isPresent()) {
                        //排除已有的数据，重新添加
                        newItems.remove(optionalDtoEvaluationValue.get());
                    }
                    newItems.add(newItem);
                    dtoEvaluationLevel.setEvaluationValue(newItems);
                    evaluationLevels.add(dtoEvaluationLevel);
                }
            }
            item.setEvaluationLevel(evaluationLevels);
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(evaluationId);
        }
    }

    /**
     * 修改redis中运算符号
     *
     * @param evaluationId     评价标准id
     * @param levelId          等级id
     * @param lowerLimitSymbol 下限值的符号
     * @param upperLimitSymbol 上限值的符号
     * @param ids              主键ids
     */
    private void updateRedis(String evaluationId, String levelId, String lowerLimitSymbol, String upperLimitSymbol,String dimensionId, List<String> ids) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };

            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);

            List<DtoEvaluationLevel> evaluationLevels = item.getEvaluationLevel();
            if (StringUtil.isNotNull(evaluationLevels)) {
                Optional<DtoEvaluationLevel> optional = evaluationLevels.stream().filter(p -> p.getId().equals(levelId)).findFirst();
                if (StringUtil.isNotNull(optional) && optional.isPresent()) {

                    DtoEvaluationLevel dtoEvaluationLevel = optional.get();
                    evaluationLevels.remove(dtoEvaluationLevel);//也要先移除
                    List<DtoEvaluationValue> dtoEvaluationValues = dtoEvaluationLevel.getEvaluationValue();
                    if (StringUtil.isNull(dtoEvaluationValues)) {  //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                        dtoEvaluationValues = repository.findByLevelId(levelId);
                    }
                    //需要修改的集合
                    List<DtoEvaluationValue> dtoEvaluationValueList = dtoEvaluationValues.stream().filter(p -> ids.contains(p.getId())).distinct().collect(Collectors.toList());

                    dtoEvaluationValues.removeAll(dtoEvaluationValueList);//先移除，再添加

                    if (StringUtil.isEmpty(dtoEvaluationValueList)) {  //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                        dtoEvaluationValueList = repository.findAll(ids);
                    }
                    if (StringUtil.isNotNull(dtoEvaluationValueList)) {

                        for (DtoEvaluationValue dtoEvaluationValue : dtoEvaluationValueList) {
                            if (StringUtil.isNotNull(upperLimitSymbol)){
                                dtoEvaluationValue.setUpperLimitSymble(upperLimitSymbol);
                            }
                            if (StringUtil.isNotNull(lowerLimitSymbol)){
                                dtoEvaluationValue.setLowerLimitSymble(lowerLimitSymbol);
                            }
                            if (StringUtil.isNotNull(dimensionId)){
                                dtoEvaluationValue.setDimensionId(dimensionId);
                            }
                        }
                        dtoEvaluationValues.addAll(dtoEvaluationValueList);//先移除，再添加
                    }
                    dtoEvaluationLevel.setEvaluationValue(dtoEvaluationValues);
                    evaluationLevels.add(dtoEvaluationLevel);
                } else { //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                    DtoEvaluationLevel dtoEvaluationLevel = evaluationLevelService.findOne(levelId);
                    if (StringUtil.isNotNull(dtoEvaluationLevel)) {
                        List<DtoEvaluationValue> newItems = repository.findByLevelId(levelId);
                        dtoEvaluationLevel.setEvaluationValue(newItems);
                        evaluationLevels.add(dtoEvaluationLevel);
                    }
                }
            } else { //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                DtoEvaluationLevel dtoEvaluationLevel = evaluationLevelService.findOne(levelId);
                if (StringUtil.isNotNull(dtoEvaluationLevel)) {
                    List<DtoEvaluationValue> newItems = repository.findByLevelId(levelId);
                    dtoEvaluationLevel.setEvaluationValue(newItems);
                    evaluationLevels.add(dtoEvaluationLevel);
                }
            }
            item.setEvaluationLevel(evaluationLevels);
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(evaluationId);
        }
    }

    /**
     * 从redis中删除
     *
     * @param evaluationId   评价ID
     * @param levelId        评价等级id
     * @param analyzeItemIds 要删除的分析项目id
     */
    private void deleteRedis(String evaluationId, String levelId, List<String> analyzeItemIds) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };

            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);

            List<DtoEvaluationLevel> evaluationLevels = item.getEvaluationLevel();
            Boolean isFlag = true;
            if (StringUtil.isNotNull(evaluationLevels)) {
                Optional<DtoEvaluationLevel> optional = evaluationLevels.stream().filter(p -> p.getId().equals(levelId)).findFirst();
                if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                    isFlag = false;
                    DtoEvaluationLevel dtoEvaluationLevel = optional.get();

                    evaluationLevels.remove(dtoEvaluationLevel);//先剔除

                    //再重新增加
                    List<DtoEvaluationValue> dtoEvaluationValues = dtoEvaluationLevel.getEvaluationValue();

                    if (StringUtil.isEmpty(dtoEvaluationValues)) { //说明没有初始化过，要从数据库中找再初始化到Redis
                        dtoEvaluationValues = repository.findByLevelId(levelId);
                    }

                    if (StringUtil.isNotNull(dtoEvaluationValues)) {
                        //过滤删除的评价评价值
                        dtoEvaluationValues = dtoEvaluationValues.stream().filter(p -> !analyzeItemIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());
                    } else {
                        dtoEvaluationValues = new ArrayList<>();
                    }
                    dtoEvaluationLevel.setEvaluationValue(dtoEvaluationValues);

                    evaluationLevels.add(dtoEvaluationLevel);

                }
            }
            if (isFlag) {
                //这个重新触发几率较小，当触发的时候下次不会再触发的， 只是保证读取不到，从数据库中读取，再会写到redis
                DtoEvaluationLevel dtoEvaluationLevel = evaluationLevelService.findOne(levelId);
                if (StringUtil.isNotNull(dtoEvaluationLevel)) {
                    List<DtoEvaluationValue> dtoEvaluationValues = repository.findByLevelId(levelId);
                    if (StringUtil.isNotNull(dtoEvaluationValues)) {
                        //过滤删除的评价评价值
                        dtoEvaluationValues = dtoEvaluationValues.stream().filter(p -> !analyzeItemIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());

                    } else {
                        dtoEvaluationValues = new ArrayList<>();
                    }
                    dtoEvaluationLevel.setEvaluationValue(dtoEvaluationValues);
                    evaluationLevels.add(dtoEvaluationLevel);

                }
            }
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(evaluationId);
        }
    }

    @Autowired
    @Lazy
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }
}