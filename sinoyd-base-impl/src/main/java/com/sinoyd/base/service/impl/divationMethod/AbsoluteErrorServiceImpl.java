package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.QcRulesConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sinoyd.base.utils.base.DivationUtils.standardDeviationBigDecimal;

/**
 * 绝对误差
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
public class AbsoluteErrorServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            //均值结果
            String avgValue = valueList.get(2);
            // 是否数据对差计算
            Boolean discrepancy = false;
            if (StringUtil.isNotNull(controlLimit.getDataDiscrepancy())) {
                discrepancy = controlLimit.getDataDiscrepancy();
            }
            if (DivationUtils.isNumber(samValue) && DivationUtils.isNumber(qcValue) && DivationUtils.isNumber(avgValue)) {
                //判断数据误差是否使用绝对误差 -- 检查项范围
                String qcRangeLimit = controlLimit.getRangeConfigData();//controlLimit.getRangeConfig();
                Boolean flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(avgValue), calculationService);
                if (flag) {
                    if (!discrepancy){
                        if (valueList.size() > 3) {
                            List<String> yyPxValList = valueList.stream().skip(3).limit(valueList.size()).collect(Collectors.toList());
                            int[] rules = DivationUtils.getStdDevRoundingRuleByXml(SpringContextAware.getBean(QcRulesConfig.class));
                            //多个平行样的情况，相对偏差按照标准差的方式进行计算
                            retStr = multiAbsoluteDeviation(yyPxValList, avgValue, rules[0], rules[1]);
                        } else {
                            String deviationFormula = map.getOrDefault("deviationFormula", "").toString();
                            //b-a
                            if (StringUtil.isNotEmpty(deviationFormula)) {
                                Map<String, Object> paramMap = new HashMap<>();
                                paramMap.put("a", new BigDecimal(samValue));
                                paramMap.put("b", new BigDecimal(qcValue));
                                retStr = calculationService.calculationExpression(deviationFormula, paramMap).toString();
                            } else {
                                retStr = (new BigDecimal(qcValue).subtract(new BigDecimal(samValue))).toString();
                            }
                        }
                    }else {
                        List<String> diffList = controlLimit.getDiscrepancyList();
                        String diffAvg = diffList.get(0);
                        if (DivationUtils.isNumber(diffAvg)){
                            retStr = diffAvg;
                        }
                    }
                }
            }
        }
        map.put("qcRate", retStr);
    }

    /**
     * 多个平行样时，计算绝对偏差(标准差S)
     *
     * @param strValues    数据列表
     * @param avg          平均值
     * @param deviationSig 标准差有效位
     * @param deviationDec 标准差小数位
     * @return 绝对偏差
     */
    private String multiAbsoluteDeviation(List<String> strValues, String avg, int deviationSig, int deviationDec) {
        CalculateService calculateService = SpringContextAware.getBean(CalculateService.class);
        //计算标准差
        BigDecimal s = standardDeviationBigDecimal(strValues, avg);
        //标准差s需要修约，修约方式为：保留三位有效，两位小数。（修约规则常量维护）
        s = new BigDecimal(calculateService.revise(deviationSig, deviationDec, s.toPlainString()));
        return s.toString();
    }
}
