package com.sinoyd.base.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.ISyncDataService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumRequestTarget;
import com.sinoyd.commons.service.IRCCQueryService;
import com.sinoyd.commons.vo.QueryRequestParamVO;
import com.sinoyd.frame.base.entity.BaseEntity;
import io.swagger.models.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 同步数据通用实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/14
 */
@Service
@Slf4j
@SuppressWarnings({"rawtypes", "unchecked"})
public abstract class SyncDataServiceImpl<T extends BaseEntity, ID extends Serializable, R extends JpaRepository<T, ID>>
        implements ISyncDataService<T> {

    private WebSocketServer webSocketServer;

    private R repository;

    private IRCCQueryService rccQueryService;

    private DocumentService documentService;

    private FilePathConfig filePathConfig;

    protected RedisTemplate redisTemplate;


    @Transactional
    @Override
    public void sync(List<T> sourceDataList, List<T> limsDeleteDataList) {
        List<ID> sourceIds = (List<ID>) sourceDataList.stream().map(T::getId).collect(Collectors.toList());
        sync(sourceIds, sourceDataList, limsDeleteDataList);
    }

    /**
     * 是否需要在websocket中打印消息
     *
     * @return true: 打印; false: 不打印
     */
    public abstract boolean needPrintMessage();

    /**
     * 更新缓存
     */
    public void saveRedis(T t) {

    }

    /**
     * 获取不需要同步的属性名称
     *
     * @return 不需要同步的属性名称
     */
    public List<String> getUnSyncFields() {
        return Stream.of("orgId", "domainId", "creator", "createDate", "modifier", "modifyDate")
                .collect(Collectors.toList());
    }

    /**
     * 获取需要在websocket中打印的属性名称
     *
     * @return 属性名称
     */
    public List<String> getPrintFieldNames() {
        return Collections.singletonList("id");
    }


    /**
     * 同步数据
     *
     * @param sourceIds      原数据id集合
     * @param sourceDataList 原数据集合
     */
    private void sync(Collection<ID> sourceIds,
                      List<T> sourceDataList,
                      List<T> limsDeleteDataList) {
        printMessage("本次待同步数据共 " + sourceDataList.size() + " 条...");
        printMessage("开始同步...");
        List<T> limsDataList = repository.findAll(sourceIds);
        if (StringUtil.isNotEmpty(limsDeleteDataList)) {
            sourceDataList.addAll(limsDataList);
        }
        List<T> syncDataList = new ArrayList<>();
        for (int i = 0; i < sourceDataList.size(); i++) {
            T t = sourceDataList.get(i);
            Optional<T> optional = limsDataList.stream().filter(p -> p.getId().equals(t.getId())).findFirst();
            if (StringUtil.isNotEmpty(getUnSyncFields()) && optional.isPresent()) {
                BeanUtils.copyProperties(t, optional.get(), getUnSyncFields().toArray(new String[0]));
                syncDataList.add(optional.get());
            } else {
                syncDataList.add(t);
            }
            printMessage(String.format("...[%d/%d]同步" + getPrintFieldValue(t, getPrintFieldNames()) + "...", i + 1, sourceDataList.size()));
        }
        printMessage("同步完成，数据保存中...");
        repository.save(syncDataList);
        for (T t : syncDataList) {
            saveRedis(t);
        }
        printMessage("数据同步完成...");
    }


    /**
     * 同步附件
     *
     * @param sourceIds 源业务id集合
     */
    protected void syncAttachment(List<String> sourceIds) {
        syncAttachment(sourceIds, false);
    }

    /**
     * 同步附件
     *
     * @param sourceIds      源业务id集合
     * @param isReportConfig 是否报表模板配置
     */
    protected void syncAttachment(List<String> sourceIds, boolean isReportConfig) {
        printMessage("开始同步相关附件...");
        String rootPath = isReportConfig ? filePathConfig.getTemplatePath() : filePathConfig.getFilePath();
        try {
            //删除LIMS下的对应的附件记录及文件
            deleteLIMSDocuments(sourceIds, isReportConfig);
            //从RCC中查询出相关业务id的附件，并进行附件记录和附件同步
            QueryRequestParamVO queryParams = new QueryRequestParamVO();
            queryParams.setUri("/api/base/document/objects");
            queryParams.setOrgCodes(Collections.singletonList("RCC"));
            Map<String, Object> requstParamMap = new HashMap<>();
            requstParamMap.put("", sourceIds);
            queryParams.setBizFilter(requstParamMap);
            queryParams.setHttpMethod(HttpMethod.POST.name());
            queryParams.setTarget(EnumRequestTarget.RCC.getValue());
            List<DtoDocument> rccDocumentList = documentsJson2List(rccQueryService.findList(queryParams).getData());
            if (StringUtil.isNotEmpty(rccDocumentList)) {
                //同步附件记录
                documentService.save(rccDocumentList);
                //同步附件
                List<String> rccFilePathList = rccDocumentList.stream().map(DtoDocument::getPath).collect(Collectors.toList());
                for (String path : rccFilePathList) {
                    String srcPath = rootPath + File.separator + path;
                    String destPath = rootPath + File.separator + path;
                    File sourceFile = new File(srcPath);
                    if (sourceFile.exists()){
                        FileUtils.copyFile(sourceFile, new File(destPath));
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("往LIMS中同步附件发生错误...");
        }
        printMessage("相关附件同步完成...");
    }

    /**
     * json转实体列表
     *
     * @param data json数据
     * @return 实体列表
     */
    private List<DtoDocument> documentsJson2List(Object data) {
        String json = JSONObject.toJSONString(data);
        return JSONObject.parseObject(json, new TypeReference<List<DtoDocument>>() {
        }.getType());
    }

    /**
     * 根据业务id，在LIMS中删除对应的附件记录及附件
     *
     * @param sourceIds 业务id
     */
    private void deleteLIMSDocuments(List<String> sourceIds, boolean isReportConfig) {
        String rootPath = isReportConfig ? filePathConfig.getTemplatePath() : filePathConfig.getFilePath();
        //先删除LIMS下的对应的附件
        List<DtoDocument> limsDocumentList = documentService.findByObjectIds(sourceIds);
        if (StringUtil.isNotEmpty(limsDocumentList)) {
            List<String> filePathList = new ArrayList<>();
            limsDocumentList.forEach(p -> filePathList.add(rootPath + File.separator + p.getPath()));
            List<String> documentIds = limsDocumentList.stream().map(DtoDocument::getId).collect(Collectors.toList());
            //删除文档记录
            documentService.logicDeleteById(documentIds);
            //删除文件
            filePathList.forEach(f -> deleteDirectory(f));
        }
    }

    /**
     * 递归删除文件及目录
     *
     * @param path 目录或者文件路径
     */
    private void deleteDirectory(String path) {
        File deleteFile = new File(path);
        File[] allContents = deleteFile.listFiles();
        if (allContents != null) {
            for (File file : allContents) {
                if (file.isDirectory()) {
                    deleteDirectory(file.getPath());
                } else {
                    file.delete();
                }
            }
        }
        deleteFile.delete();
    }

    /**
     * websocket打印消息
     *
     * @param message 消息内容
     */
    private void printMessage(String message) {
        if (needPrintMessage()) {
            webSocketServer.sendMessage(message);
        }
    }

    /**
     * 获取在websocket中输出消息中需要显示的属性值
     *
     * @param t          实例
     * @param fieldNames 属性名称集合
     * @return 属性值
     */
    private String getPrintFieldValue(T t, List<String> fieldNames) {
        List<String> values = new ArrayList<>();
        try {
            Class clazz = getGenericClass();
            while (clazz != null) {
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    if (fieldNames.contains(field.getName())) {
                        values.add(field.get(t).toString());
                    }
                }
                clazz = clazz.getSuperclass();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("数据同步发生错误，已回滚并终止...");
        }
        return String.join("、", values);
    }

    /**
     * 获取 T.class
     *
     * @return T.class
     */
    private Class<T> getGenericClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    @Autowired(required = false)
    public void setRepository(R repository) {
        this.repository = repository;
    }

    @Autowired
    public void setWebSocketServer(WebSocketServer webSocketServer) {
        this.webSocketServer = webSocketServer;
    }

    @Autowired
    public void setRccQueryService(IRCCQueryService rccQueryService) {
        this.rccQueryService = rccQueryService;
    }

    @Autowired
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
