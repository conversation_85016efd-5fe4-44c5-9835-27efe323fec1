package com.sinoyd.base.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.criteria.InstrumentCriteria;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 仪器管理-基本信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Service
public class InstrumentServiceImpl extends BaseJpaServiceImpl<DtoInstrument, String, InstrumentRepository>
        implements InstrumentService {

    @Autowired
    private EnterpriseRepository enterpriseRepository;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private CodeService codeService;

    private CommonRepository commonRepository;

    /***
     * 分页查询
     *
     * @param page 分页条件
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoInstrument> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrument x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        DtoCode dtoCodeInst = codeService.findByCode("BASE_ExpirationAlertTime_Instrument");
        int alertDays = 0;
        if (StringUtil.isNotNull(dtoCodeInst)) {
            alertDays = Integer.valueOf(dtoCodeInst.getDictValue());
        }
        Map<String, Object> values = criteria.getValues();
        //设置预警过期时间查询条件
        Calendar expiryDateCalendar = new GregorianCalendar();
        Date curDate = new Date();
        expiryDateCalendar.setTime(curDate);
        //当前日期加上预警天数
        expiryDateCalendar.add(Calendar.DATE, alertDays);
        values.put("expireDate", expiryDateCalendar.getTime());
        //仪器使用记录id和对应使用人id及名称的映射
        Map<String, String[]> projectInstId2UserNameMap = new HashMap<>();
        //仪器id和对应仪器出入库id映射
        Map<String, String> instId2ProjectInstIdMap = new HashMap<>();
        InstrumentCriteria instrumentCriteria = (InstrumentCriteria) criteria;
        String receiveId = StringUtil.isNotEmpty(instrumentCriteria.getReceiveId()) ? instrumentCriteria.getReceiveId() : UUIDHelper.GUID_EMPTY;
        String outBound = StringUtil.isNotEmpty(instrumentCriteria.getOutBound()) ? instrumentCriteria.getOutBound() : "";
        getInstId2ProjectInstIdMap(receiveId, projectInstId2UserNameMap, instId2ProjectInstIdMap);
        //与测试项目关联仪器放最上面
        List<String> topInstrumentIds = new ArrayList<>();
        if (StringUtil.isNotNull(instrumentCriteria.getUseType()) && StringUtil.isNotEmpty(instrumentCriteria.getTestIds())) {
            topInstrumentIds = commonRepository.find("select distinct(instrumentId) from DtoTest2Instrument where testId in :testIds and useType = :useType", new HashMap<String, Object>() {{
                put("testIds", instrumentCriteria.getTestIds());
                put("useType", instrumentCriteria.getUseType());
            }});
        }
        int pageNum = page.getPageNo();
        int rowsPerPage = page.getRowsPerPage();
        boolean sortFlag = false;
        if (!"0".equals(outBound) && StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            //送样单添加仪器使用记录，是否出库查询条件不为"否"时，需要对查询结果做排序，因此先查出所有仪器，再按照一定规则进行排序，最后手动截取需要查询的页码对应的仪器数据
            page.setPageNo(1);
            page.setRowsPerPage(Integer.MAX_VALUE);
            sortFlag = true;
        }
        if (StringUtil.isNotNull(instrumentCriteria.getIsOriginEndDateSort()) && instrumentCriteria.getIsOriginEndDateSort()) {
            String sort = "originEndDate+" + page.getSort();
            page.setSort(sort);
        }
        super.findByPage(page, criteria);
        page.setPageNo(pageNum);
        page.setRowsPerPage(rowsPerPage);

        List<DtoInstrument> instruments = page.getData();

        List<DtoCode> instrumentTypeList = codeService.findCodes("LIM_InstrumentType");
        //将路径转成base64字符
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DAY_OF_YEAR, 1);
        for (DtoInstrument dtoInstrument : instruments) {
            Integer status = ((InstrumentCriteria) criteria).getStatus();
            if (StringUtil.isNull(status) || status == -1) {
                //前端仪器状态参数未传递,需要对查询结果中的仪器对象重置其过期状态
                Integer state = dtoInstrument.getState();
                Date originEndDate = dtoInstrument.getOriginEndDate();

                if (StringUtil.isNotNull(state) && (state.intValue() == EnumBase.EnumInstrumentStatus.正常.getValue() || state == -1)) {
                    //过期日期为空,则设置其状态为正常
                    if ("1753-01-01".equals(DateUtil.dateToString(originEndDate, DateUtil.YEAR)) && dtoInstrument.getOriginType() != -1) {
                        dtoInstrument.setState(EnumBase.EnumInstrumentStatus.正常.getValue());
                        continue;
                    }
                    //原本状态为正常的要判断是否已过期或者即将过期
                    if (dtoInstrument.getOriginType() != -1 && originEndDate.compareTo(curDate) < 0) {
                        dtoInstrument.setState(EnumBase.EnumInstrumentStatus.过期.getValue());
                    } else if (state == -1) {
                        dtoInstrument.setState(EnumBase.EnumInstrumentStatus.正常.getValue());
                    }
                } else if (StringUtil.isNotNull(state) && (state.intValue() == EnumBase.EnumInstrumentStatus.过期.getValue() || state == -1)) {
                    if (dtoInstrument.getOriginType() != -1 && originEndDate.compareTo(curDate) < 0) {
                        dtoInstrument.setState(EnumBase.EnumInstrumentStatus.过期.getValue());
                    } else if (originEndDate.compareTo(curDate) >= 0) {
                        dtoInstrument.setState(EnumBase.EnumInstrumentStatus.正常.getValue());
                    }
                }
            } else if (status.intValue() == EnumBase.EnumInstrumentStatus.正常.getValue()
                    || status.intValue() == EnumBase.EnumInstrumentStatus.过期.getValue()) {
                //前端仪器状态参数为正常，过期时，需要重置查询结果的仪器状态为相应的状态
                if (!status.equals(dtoInstrument.getState())) {
                    dtoInstrument.setState(status);
                }
            }
            //仪器类型
            DtoCode dtoCode = instrumentTypeList.stream().filter(item -> item.getDictCode().equals(dtoInstrument.getInstrumentTypeId())).findFirst().orElse(null);
            // 仪器类型名称
            String instrumentTypeName = "";
            if (StringUtil.isNotNull(dtoCode)) {
                assert dtoCode != null;
                instrumentTypeName = dtoCode.getDictName();
            }
            dtoInstrument.setInstrumentTypeName(instrumentTypeName);
//            dtoInstrument.setState(getInstrumentState(originEndDate, state, c));
            dtoInstrument.setBase64Content(documentService.convertBase64Content(dtoInstrument.getPhotoUrl(), 100, 100));
            dtoInstrument.setExpireAlertDays(String.valueOf(alertDays));
            //设置是否出库，使用人字段
            setOutBoundUser(dtoInstrument, projectInstId2UserNameMap, instId2ProjectInstIdMap);
        }
        //是否出库不为否时，仪器列表排序项目关联的已出库的仪器放在最上面，其中和送样单采样人相关的放在最上面,其他的按照原有逻辑排序
        List<DtoInstrument> sortInstrumentList = sortInstrumentForRecord(instruments, receiveId, outBound, sortFlag, page, topInstrumentIds);
        page.setData(sortInstrumentList);
    }

    /**
     * 是否出库不为否时，仪器列表排序项目关联的已出库的仪器放在最上面，其中和送样单采样人相关的放在最上面,其他的按照原有逻辑排序
     *
     * @param instrumentList 仪器列表
     * @param receiveId      送样单id
     * @param outBound       是否出库
     * @param sortFlag       排序标记
     * @param page           查询分页对象
     * @return 排序好的仪器列表
     */
    private List<DtoInstrument> sortInstrumentForRecord(List<DtoInstrument> instrumentList, String receiveId, String outBound, boolean sortFlag,
                                                        PageBean<DtoInstrument> page, List<String> topInstrumentIds) {
        List<DtoInstrument> sortInstrumentList = new ArrayList<>();
        List<DtoInstrument> otherInstrumentList = new ArrayList<>();
        // 定义根据出入库
        List<DtoInstrument> sortByProject = new ArrayList<>();
        if (sortFlag) {
            //获取送样单对应的采样人id
            List<String> samplingPersonIdList = commonRepository.find("select samplingPersonId from DtoSamplingPersonConfig spc where spc.objectId = :receiveId and objectType = 1",
                    Collections.singletonMap("receiveId", receiveId));
            // 根据送样单查询项目id
            List<Object> projectIdList = commonRepository.find("select projectId from DtoReceiveSampleRecord r where id = :receiveId",
                    Collections.singletonMap("receiveId", receiveId));
            String projectId = StringUtil.isNotEmpty(projectIdList) ? projectIdList.get(0).toString() : "";

            if ("".equals(outBound)) {
                //查询所有仪器时，先把和项目关联的已出库的仪器置顶
                for (DtoInstrument instrument : instrumentList) {
                    if ("1".equals(instrument.getOutBound())) {
                        // 筛选出仪器出入库绑定的项目中包含当前项目的仪器
                        if (instrument.getProjectIds().contains(projectId)) {
                            sortByProject.add(instrument);
                        } else {
                            sortInstrumentList.add(instrument);
                        }
                    } else {
                        otherInstrumentList.add(instrument);
                    }
                }
            } else {
                sortInstrumentList.addAll(instrumentList);
            }
            // 没有绑定项目编号的出库仪器，先按照出库时间倒序排序
            sortInstrumentList = sortInstrumentByOutDate(sortInstrumentList);
            //把已出库的仪器中和送样单采样人有关的仪器再次置顶
            List<DtoInstrument> sortedInstrumentList = sortInstrumentBySamplingPerson(sortByProject, samplingPersonIdList);
            // 优先排列所属项目为当前项目的仪器,然后是其他项目出库记录或者是未配置项目的出库记录仪器
            sortedInstrumentList.addAll(sortInstrumentList);
            if (StringUtil.isNotEmpty(otherInstrumentList)) {
                sortedInstrumentList.addAll(otherInstrumentList);
            }
            //把选择对应测试项目关联的仪器置顶
            sortedInstrumentList = sortInstrumentByTest(sortedInstrumentList, topInstrumentIds);
            sortedInstrumentList = sortedInstrumentList.stream().skip((long) (page.getPageNo() - 1) * page.getRowsPerPage()).limit(page.getRowsPerPage()).collect(Collectors.toList());
            return sortedInstrumentList;
        } else {
            //排序标记为false(是否出库为否,或者不是送样单添加仪器使用记录页面查询时)不进行额外排序
            sortInstrumentList.addAll(instrumentList);
            return sortInstrumentList;
        }
    }

    /**
     * 把已出库的仪器中和送样单采样人有关的仪器置顶
     *
     * @param instrumentList       仪器列表
     * @param samplingPersonIdList 采样人id列表
     * @return 排序好的仪器列表
     */
    private List<DtoInstrument> sortInstrumentBySamplingPerson(List<DtoInstrument> instrumentList, List<String> samplingPersonIdList) {
        List<DtoInstrument> sortInstrumentList = new ArrayList<>();
        List<DtoInstrument> otherInstrumentList = new ArrayList<>();
        if (StringUtil.isNotEmpty(samplingPersonIdList)) {
            for (DtoInstrument instrument : instrumentList) {
                List<String> userIdList = new ArrayList<>(Arrays.asList(instrument.getUserId().split(",")));
                int size = userIdList.size();
                userIdList.removeAll(samplingPersonIdList);
                if (userIdList.size() < size) {
                    sortInstrumentList.add(instrument);
                } else {
                    otherInstrumentList.add(instrument);
                }
            }
            if (StringUtil.isNotEmpty(otherInstrumentList)) {
                sortInstrumentList.addAll(otherInstrumentList);
            }
            return sortInstrumentList;
        } else {
            //采样人为空，则不排序
            return instrumentList;
        }
    }

    /**
     * 根据仪器出库时间排序
     *
     * @param instrumentList 仪器列表
     * @return List<DtoInstrument>
     */
    private List<DtoInstrument> sortInstrumentByOutDate(List<DtoInstrument> instrumentList) {
        List<DtoInstrument> sortInstrumentList = new ArrayList<>();
        List<DtoInstrument> otherInstrumentList = new ArrayList<>();
        if (StringUtil.isNotEmpty(instrumentList)) {
            // 区分有出库时间和没有出库时间的仪器
            sortInstrumentList = instrumentList.stream().filter(p -> StringUtil.isNotEmpty(p.getOutDateStr())).collect(Collectors.toList());
            // 按照出库时间倒序排序
            sortInstrumentList.sort(Comparator.comparing((DtoInstrument p) -> DateUtil.stringToDate(p.getOutDateStr(), DateUtil.YEAR)).reversed());
            otherInstrumentList = instrumentList.stream().filter(p -> !StringUtil.isNotEmpty(p.getOutDateStr())).collect(Collectors.toList());
            sortInstrumentList.addAll(otherInstrumentList);
            return sortInstrumentList;
        } else {
            return instrumentList;
        }
    }

    /**
     * 根据测试项目绑定仪器置顶
     *
     * @param instrumentList   仪器列表
     * @param topInstrumentIds 选择测试项目绑定的仪器id列表
     * @return List<DtoInstrument>
     */
    private List<DtoInstrument> sortInstrumentByTest(List<DtoInstrument> instrumentList, List<String> topInstrumentIds) {
        List<DtoInstrument> sortInstrumentList = new ArrayList<>();
        List<DtoInstrument> otherInstrumentList = new ArrayList<>();
        if (StringUtil.isNotEmpty(topInstrumentIds)) {
            for (DtoInstrument instrument : instrumentList) {
                if (topInstrumentIds.contains(instrument.getId())) {
                    sortInstrumentList.add(instrument);
                } else {
                    otherInstrumentList.add(instrument);
                }
            }
            if (StringUtil.isNotEmpty(otherInstrumentList)) {
                sortInstrumentList.addAll(otherInstrumentList);
            }
            return sortInstrumentList;
        } else {
            return instrumentList;
        }
    }

    /**
     * 设置是否出库，使用人字段
     *
     * @param instrument                仪器对象
     * @param projectInstId2UserNameMap 仪器使用记录id和对应使用人名称的映射
     * @param instId2ProjectInstIdMap   仪器id和对应仪器出入库id映射
     */
    private void setOutBoundUser(DtoInstrument instrument, Map<String, String[]> projectInstId2UserNameMap, Map<String, String> instId2ProjectInstIdMap) {
        instrument.setOutBound("0");
        instrument.setUser("");
        if (instId2ProjectInstIdMap.containsKey(instrument.getId())) {
            instrument.setOutBound("1");
            String projectInstId = instId2ProjectInstIdMap.get(instrument.getId());
            String[] userInfo = projectInstId2UserNameMap.getOrDefault(projectInstId, null);
            String user = StringUtil.isNotNull(userInfo) ? userInfo[1] : "";
            String userId = StringUtil.isNotNull(userInfo) ? userInfo[0] : "";
            String projectIds = StringUtil.isNotNull(userInfo) ? userInfo[2] : "";
            String outDateStr = StringUtil.isNotNull(userInfo) ? userInfo[3] : "";
            instrument.setUser(user.replace(",", "，"));
            instrument.setUserId(userId);
            instrument.setProjectIds(projectIds);
            instrument.setOutDateStr(outDateStr);

        }
    }

    /**
     * 获取仪器出入库记录id和对应使用人名称的映射,以及仪器id和对应仪器出入库id映射的关系
     *
     * @param receiveId 送样单id
     */
    private void getInstId2ProjectInstIdMap(String receiveId, Map<String, String[]> projectInstId2UserNameMap, Map<String, String> instId2ProjectInstIdMap) {
        if (StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            Map<String, Object> map = this.filterProjectInstrumentId(receiveId);
            List<List<String>> projectInstrumentInfoList = (List<List<String>>) map.get("projectInstrumentInfoList");
            for (List<String> info : projectInstrumentInfoList) {
                projectInstId2UserNameMap.put(info.get(0), new String[]{info.get(1), info.get(2), info.get(3), info.get(4)});
            }
            List<String> filterIdList = (List<String>) map.get("filterIdList");
            if (StringUtil.isEmpty(filterIdList)) {
                filterIdList.add(UUIDHelper.GUID_EMPTY);
            }
            List<Object[]> objsList = commonRepository.find("select projectInstrumentId, instrumentId from DtoProjectInstrumentDetails where projectInstrumentId in :projectInstrumentId",
                    Collections.singletonMap("projectInstrumentId", filterIdList));
            for (Object[] obj : objsList) {
                instId2ProjectInstIdMap.put(obj[1].toString(), obj[0].toString());
            }
        }
    }

    /**
     * 保存仪器
     */
    @Transactional
    @Override
    public DtoInstrument save(DtoInstrument entity) {
        // 根据仪器编号跟rowGuid判断是否已存在
        Integer count = repository.countByInsCode(entity.getInstrumentsCode(), entity.getId());
        if (count > 0) {
            throw new BaseException("已存在相同编号的仪器:" + entity.getInstrumentsCode());
        }
        entity = addSupplier(entity);
        if (StringUtils.isNotNullAndEmpty(entity.getInstrumentName())) {
            entity.setFullPinYin(PinYinUtil.getFullSpell(entity.getInstrumentName()));
            entity.setPinYin(PinYinUtil.getFirstSpell(entity.getInstrumentName()));
        }
        this.getInstrumentState(entity);
        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoInstrument update(DtoInstrument entity) {
        Integer count = repository.countByInsCode(entity.getInstrumentsCode(), entity.getId());
        if (count > 0) {
            throw new BaseException("已存在相同编号的仪器:" + entity.getInstrumentsCode());
        }
        entity = addSupplier(entity);
        if (StringUtils.isNotNullAndEmpty(entity.getInstrumentName())) {
            entity.setFullPinYin(PinYinUtil.getFullSpell(entity.getInstrumentName()));
            entity.setPinYin(PinYinUtil.getFirstSpell(entity.getInstrumentName()));
        }
        this.getInstrumentState(entity);
        return super.update(entity);
    }


    @Override
    public DtoInstrument findOne(String id) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DAY_OF_YEAR, 1);
        DtoInstrument instrument = super.findOne(id);
        if (StringUtil.isNotNull(instrument)) {
            Integer state = instrument.getState();
            Date originEndDate = instrument.getOriginEndDate();
            instrument.setBase64Content(documentService.convertBase64Content(instrument.getPhotoUrl()));
            //将正常状态，但是过了有效期的改成过期状态，前端显示过期状态
            instrument.setState(getInstrumentState(originEndDate, state, c));
        }
        return instrument;
    }

    private DtoInstrument addSupplier(DtoInstrument entity) {
        DtoEnterprise enterprise = new DtoEnterprise();

        //判断是否新增供应商
        if (StringUtils.isNotNullAndEmpty(entity.getSaleName())) {
            enterprise = enterpriseRepository.getByName(entity.getSaleName(),
                    EnumBase.EnumEnterpriseType.供应商.getValue()).stream().findFirst().orElse(null);
            if (StringUtil.isNull(enterprise)) {
                enterprise = new DtoEnterprise();
                enterprise.setName(entity.getSaleName());
                enterprise.setType(EnumBase.EnumEnterpriseType.供应商.getValue());
                enterprise.setOrderNum(0);
                enterpriseRepository.save(enterprise);
            }
        } else {
            entity.setSaleName("");
        }
        return entity;
    }

    /**
     * 批量保存仪器
     */
    @Override
    public List<DtoInstrument> save(Collection<DtoInstrument> entities) {

        List<DtoInstrument> saves = new ArrayList<>();

        entities.forEach(x -> saves.add(this.save(x)));

        return saves;
    }

    /***
     * 复制仪器
     */
    @Override
    public DtoInstrument copy(String instrumentId, String instrumentsCode, String serialNo) {
        // 找到要复制的仪器基本信息
        DtoInstrument instrument = super.findOne(instrumentId);
        // 复制仪器信息
        DtoInstrument newInstrument = new DtoInstrument();
        BeanUtils.copyProperties(instrument, newInstrument);
        newInstrument.setInstrumentsCode(instrumentsCode);
        newInstrument.setSerialNo(serialNo);
        // 将仪器的id和rowGuid重置
        // newInstrument.setId(null);
        newInstrument.setId(UUID.randomUUID().toString());
        Integer count = repository.countByInsCode(instrumentsCode, newInstrument.getId());
        if (count > 0) {
            throw new BaseException("已存在相同编号的仪器:" + instrumentsCode);
        }
        return super.save(newInstrument);
    }

    /***
     * 获取制造厂商
     * @return
     */
    @Override
    public List<DtoInstrument> getFactoryNameList() {
        List<DtoInstrument> list = new ArrayList<>();

        List<String> names = repository.findFactoryName();

        for (String name : names) {
            DtoInstrument item = new DtoInstrument();
            item.setFactoryName(name);
            list.add(item);
        }
        list = list.stream().sorted(Comparator.comparing(DtoInstrument::getFactoryName).reversed()).collect(Collectors.toList());

        return list;
    }

    /***
     * 获取溯源单位
     * @return
     */
    @Override
    public List<DtoInstrument> getOriginUnitList() {
        List<DtoInstrument> list = new ArrayList<>();

        List<String> names = repository.findOriginUnit();

        for (String name : names) {
            DtoInstrument item = new DtoInstrument();
            item.setOriginUnit(name);
            list.add(item);
        }
        list = list.stream().sorted(Comparator.comparing(DtoInstrument::getOriginUnit).reversed()).collect(Collectors.toList());

        return list;
    }

    @Override
    public DtoInstrument findInstrumentAttachment(String id, String instrumentName) {
        DtoInstrument dtoInstrument = repository.findOne(id);
        if (StringUtil.isNull(dtoInstrument)) {
            dtoInstrument = new DtoInstrument();
            dtoInstrument.setId(id);
            dtoInstrument.setInstrumentName(instrumentName);
        }
        return dtoInstrument;
    }

    @Override
    public List<DtoInstrument> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoInstrument> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 重新获取仪器状态
     *
     * @param originEndDate 有效气
     * @param state         状态
     * @param c             当前时间
     * @return 返回状态
     */
    private Integer getInstrumentState(Date originEndDate, Integer state, Calendar c) {
        if (StringUtil.isNotNull(originEndDate)) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(originEndDate);
            if ((!DateUtil.dateToString(originEndDate, DateUtil.YEAR).equals("1753-01-01")
                    && cal.getTime().before(c.getTime())
                    && !state.equals(EnumBase.EnumInstrumentStatus.停用.getValue())
                    && !state.equals(EnumBase.EnumInstrumentStatus.报废.getValue())) ||
                    state.equals(EnumBase.EnumInstrumentStatus.过期.getValue())) {
                state = EnumBase.EnumInstrumentStatus.过期.getValue();
            }
        }
        return state;
    }

    /**
     * 判定仪器是否过期
     *
     * @param instrument 仪器实体
     */
    private void getInstrumentState(DtoInstrument instrument) {
        Date originEndDate = getOriginEndDate(instrument.getOriginDate(), instrument.getOriginCyc());
        if (StringUtil.isNotNull(originEndDate)) {
            instrument.setOriginEndDate(originEndDate);
        } else {
            instrument.setOriginEndDate(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        }
        if (EnumBase.EnumInstrumentStatus.正常.getValue().equals(instrument.getState())) {
            if (StringUtil.isNotNull(originEndDate) && originEndDate.compareTo(new Date()) < 0) {
                instrument.setState(EnumBase.EnumInstrumentStatus.过期.getValue());
            }
        }
    }

    /**
     * 获取相应的有效期
     *
     * @param originDate 开始时间
     * @param originCyc  周期
     * @return 返回想要的数据
     */
    private Date getOriginEndDate(Date originDate, BigDecimal originCyc) {
        if (StringUtil.isNotNull(originDate) && StringUtil.isNotNull(originCyc)) {
            Calendar c = Calendar.getInstance();
            c.setTime(originDate);
            c.set(Calendar.HOUR_OF_DAY, 23);
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            c.add(Calendar.MONTH, originCyc.intValue());
            //有效期 = 溯源日期+溯源周期-1
            c.add(Calendar.DAY_OF_YEAR, -1);
            return c.getTime();
        }
        return null;
    }

    /**
     * 根据项目id,送样单采样人过滤已出库的仪器id
     *
     * @param receiveId 送样单id
     * @return 仪器id列表
     */
    @Override
    public List<String> filterInstrumentId(String receiveId) {
        List<String> instrumentIdList = new ArrayList<>();
        Map<String, Object> map = filterProjectInstrumentId(receiveId);
        List<String> filterIdList = (List<String>) map.get("filterIdList");
        //根据仪器出入库记录id，查询出入库明细中的仪器id
        if (StringUtil.isNotEmpty(filterIdList)) {
            CommonRepository commonRepository = SpringContextAware.getBean(CommonRepository.class);
            instrumentIdList = commonRepository.find("select instrumentId from DtoProjectInstrumentDetails pid where projectInstrumentId in :filterIdList",
                    Collections.singletonMap("filterIdList", filterIdList));
        }
        return instrumentIdList;
    }

    /**
     * 根据项目id,送样单采样人过滤出仪器出入库记录id
     *
     * @param receiveId 送样单id
     * @return 仪器出入库记录id列表
     */
    @Override
    public Map<String, Object> filterProjectInstrumentId(String receiveId) {
        List<String> filterIdList = new ArrayList<>();
        List<List<String>> projectInstrumentInfoList = new ArrayList<>();
//        List<Object> projectIdList = commonRepository.find("select projectId from DtoReceiveSampleRecord r where id = :receiveId",
//                Collections.singletonMap("receiveId", receiveId));
//        if (StringUtil.isNotEmpty(projectIdList)) {
//            String projectId = projectIdList.get(0).toString();
        // BUG2024110701408 不按照项目id 进行过滤。改为查询所有未入库的记录。
        StringBuilder sql = new StringBuilder("select DISTINCT pi.id, pi.userIds, pi.userNames, pi.projectId, d.outDate from DtoProjectInstrument pi , DtoProjectInstrumentDetails d where  pi.isDeleted = 0 ");
        sql.append(" and d.projectInstrumentId = pi.id and d.isStorage = 0");
        List<Object[]> objectList = commonRepository.find(sql.toString(),
                new HashMap<>());
        if (StringUtil.isNotEmpty(objectList)) {
            //Bug BUG2023061996440 不按照送样单对应的采样人进行过滤
            for (Object[] objects : objectList) {
                filterIdList.add(objects[0].toString());
                projectInstrumentInfoList.add(new ArrayList<>(Arrays.asList(objects[0].toString(),
                        objects[1].toString(),
                        objects[2].toString(),
                        objects[3] == null ? "" : objects[3].toString(),
                        objects[4] == null ? "" : objects[4].toString())));
            }
//                //获取送样单对应的采样人id
//                List<String> samplingPersonIdList = commonRepository.find("select samplingPersonId from DtoSamplingPersonConfig spc where spc.objectId = :receiveId and objectType = 1",
//                        Collections.singletonMap("receiveId", receiveId));
//                //按照采样人过滤仪器出入库记录
//                for (Object[] objects : objectList) {
//                    String id = objects[0].toString();
//                    if (StringUtil.isEmpty(samplingPersonIdList)) {
//                        //送样单的采样人为空，则不按照采样人过滤仪器出入库记录
//                        filterIdList.add(id);
//                        projectInstrumentInfoList.add(new ArrayList<>(Arrays.asList(id, objects[2].toString())));
//                    } else {
//                        String userIds = objects[1].toString();
//                        List<String> userIdList = new ArrayList<>(Arrays.asList(userIds.split(",")));
//                        if (StringUtil.isNotEmpty(userIdList)) {
//                            int oriSize = userIdList.size();
//                            userIdList.removeAll(samplingPersonIdList);
//                            if (userIdList.size() < oriSize) {
//                                //仪器出入库记录的采样人和送样单的采样人有交集则满足过滤条件
//                                filterIdList.add(id);
//                                projectInstrumentInfoList.add(new ArrayList<>(Arrays.asList(id, objects[2].toString())));
//                            }
//                        }
//                    }
//                }
        }
//        }
        Map<String, Object> map = new HashMap<>();
        map.put("filterIdList", filterIdList);
        map.put("projectInstrumentInfoList", projectInstrumentInfoList);
        return map;
    }

    @Override
    public List<DtoInstrument> getOverDueData() {
        StringBuilder detailBuilder = new StringBuilder("select x");
        detailBuilder.append(" from DtoInstrument as x")
                .append(" where isDeleted = 0 and state in (1,3) ")
                .append(" and DATE_FORMAT( originEndDate, '%Y-%m-%d' ) != '1753-01-01'")
                .append(" and DATE_FORMAT(originEndDate, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') ");
        List<DtoInstrument> list = commonRepository.find(detailBuilder.toString());
        Date now = new Date();
        for (DtoInstrument dtoInstrument : list) {
            // 过期天数
            dtoInstrument.setExpirationDays(CalendarUtil.getDaysBetween(dtoInstrument.getOriginEndDate(), now));
        }
        //过期天数倒序 仪器名称顺序
        list.sort(Comparator.comparing(DtoInstrument::getExpirationDays, Comparator.reverseOrder()).thenComparing(DtoInstrument::getInstrumentName));
        return list;
    }

    @Override
    public List<DtoInstrument> getWillOverDueData() {
        int alertDays = 30;
        DtoCode dtoCode = codeService.findByCode("BASE_ExpirationAlertTime_Instrument");
        if (StringUtil.isNotNull(dtoCode)) {
            alertDays = Integer.valueOf(dtoCode.getDictValue());
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, alertDays);
        StringBuilder detailBuilder = new StringBuilder("select x");
        detailBuilder.append(" from DtoInstrument as x")
                .append(" where isDeleted = 0 and state in (1,3) ")
                .append(" and DATE_FORMAT( originEndDate, '%Y-%m-%d' ) != '1753-01-01'")
                .append(" and DATE_FORMAT( originEndDate, '%Y-%m-%d') > DATE_FORMAT(now(), '%Y-%m-%d') ")
                .append(" and DATE_FORMAT( originEndDate, '%Y-%m-%d') < DATE_FORMAT('")
                .append(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR)).append("','%Y-%m-%d')");
        List<DtoInstrument> list = commonRepository.find(detailBuilder.toString());
        Date now = new Date();
        for (DtoInstrument dtoInstrument : list) {
            // 过期天数
            dtoInstrument.setExpirationDays(CalendarUtil.getDaysBetween(now, dtoInstrument.getOriginEndDate()));
        }
        //过期天数倒序 仪器名称顺序
        list.sort(Comparator.comparing(DtoInstrument::getExpirationDays).thenComparing(DtoInstrument::getInstrumentName));
        return list;
    }

    @Override
    public Integer getLastOrderNum() {
        int num = 0;
        List<DtoInstrument> expandList = repository.findAll();
        expandList = expandList.stream().filter(p -> StringUtil.isNotNull(p.getOrderNum()) && !p.getOrderNum().equals(-1)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(expandList)) {
            expandList.sort(Comparator.comparing(DtoInstrument::getOrderNum).reversed());
            num = expandList.get(0).getOrderNum();
        }
        return num + 1;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }
}