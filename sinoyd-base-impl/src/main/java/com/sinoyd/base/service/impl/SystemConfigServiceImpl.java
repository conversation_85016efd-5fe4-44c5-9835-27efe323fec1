package com.sinoyd.base.service.impl;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.base.repository.lims.SystemConfigRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.SystemConfigService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统信息管理配置实现
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/12/8
 */
@Service
@Slf4j
public class SystemConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSystemConfig, String, SystemConfigRepository> implements SystemConfigService {


    private DocumentService documentService;

    private IConfigService configService;

    /**
     * 上传下载使用的查询方法
     *
     * @param key id 主键
     * @return DtoSystemConfig 系统信息管理配置DTO
     */
    @Override
    public DtoSystemConfig findAttachment(String key) {
        return super.findOne(key);
    }

    /**
     * 查询表中的记录（有且只有一条）
     *
     * @return DtoSystemConfig
     */
    @Override
    public DtoSystemConfig findSystemConfigOne() {
        List<DtoSystemConfig> systemConfigList = super.findAll();
        DtoSystemConfig dtoSystemConfig = systemConfigList.stream().findFirst().orElse(new DtoSystemConfig());
        try {
            PageBean<DtoDocument> pageBean = new PageBean<>();
            pageBean.setRowsPerPage(Integer.MAX_VALUE);
            DocumentCriteria criteria = new DocumentCriteria();
            criteria.setFolderId(dtoSystemConfig.getId());
            criteria.setDocTypeId("PRO_DocumentExtendType_LOGO");
            documentService.findByPage(pageBean, criteria);
            List<DtoDocument> documentList = pageBean.getData();
            DtoDocument dtoDocument = documentList.stream().findFirst().orElse(null);
            if (dtoDocument != null) {
                String base64Content = documentService.convertBase64Content(dtoDocument.getPath());
                dtoSystemConfig.setLogoPic(base64Content);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(e.getMessage());
        }
        setCode(dtoSystemConfig);
        return dtoSystemConfig;
    }

    /**
     * 在线编辑方式
     *
     * @return 配置内容
     */
    @Override
    public Map<String, Boolean> configMode() {
        ConfigModel configModel = configService.findConfig("sys.editing.mode");
        Map<String, Boolean> mode = new HashMap<>();
        mode.put("iWebOffice", false);
        mode.put("luckySheet", false);
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            String[] configList = configModel.getConfigValue().split(";");
            Arrays.asList(configList).forEach(p -> {
                if (p.contains(".")) {
                    mode.put(p.split("\\.")[0], Boolean.valueOf(p.split("\\.")[1]));
                }
            });
        }
        return mode;
    }

    @Override
    public String getWatermarkConfig() {
        ConfigModel configModel = configService.findConfig("sys.watermark.configData");
        if(configModel!=null){
            return configModel.getConfigValue();
        }
        return "";
    }

    /**
     * 当前项目版本号
     *
     * @param dtoSystemConfig 版本信息
     */
    protected void setCode(DtoSystemConfig dtoSystemConfig) {
        //当前项目版本号
        dtoSystemConfig.setEditionCode("ent-5.4.82");
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    @Lazy
    public void setIConfigService(IConfigService configService) {
        this.configService = configService;
    }
}