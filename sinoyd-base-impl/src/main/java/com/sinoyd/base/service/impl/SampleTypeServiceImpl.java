package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.criteria.SampleTypeCriteria;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.enums.EnumBase.EnumSampleTypeCategory;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 检测类型-检测模板管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Service
public class SampleTypeServiceImpl extends BaseJpaServiceImpl<DtoSampleType, String, SampleTypeRepository>
        implements SampleTypeService {

    @Autowired
    @Lazy
    private IndustryTypeService industryTypeService;

    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 分页获取行业类型
     *
     * @param pageBean           封装分页类
     * @param sampleTypeCriteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoSampleType> pageBean, BaseCriteria sampleTypeCriteria) {
        pageBean.setEntityName("DtoSampleType p");
        pageBean.setSelect("select p");

        SampleTypeCriteria criteria = (SampleTypeCriteria) sampleTypeCriteria;
        int pageNum = pageBean.getPageNo();
        int rowsPerPage = pageBean.getRowsPerPage();
        if (EnumSampleTypeCategory.模板.getValue().equals(criteria.getCategory())) {
            pageBean.setPageNo(1);
            pageBean.setRowsPerPage(Integer.MAX_VALUE);
        }

//        //如果查询的是模板，如果查询的是大类，那么大类下小类的模板一起查询出来
//        SampleTypeCriteria criteria= (SampleTypeCriteria) sampleTypeCriteria;
//        if(criteria.getCategory()==EnumSampleTypeCategory.模板.getValue()
//                && StringUtils.isNotNullAndEmpty(criteria.getParentId()) && !UUIDHelper.GuidEmpty().equals(criteria.getParentId()))
//        {
//            List<DtoSampleType> types=repository.getListByBigSampleType(criteria.getParentId());
//            if(types!=null&&types.size()>0)
//            {
//                List<String> ids=types.stream().map(p->p.getId()).distinct().collect(Collectors.toList());
//                ids.add(criteria.getParentId());
//
//                criteria.setParentId("");
//                criteria.setParentIds((ArrayList<String>) ids);
//            }
//        }
//        super.findByPage(pageBean, criteria);
        super.findByPage(pageBean, sampleTypeCriteria);
        pageBean.setPageNo(pageNum);
        pageBean.setRowsPerPage(rowsPerPage);
        if (StringUtil.isEmpty(pageBean.getData())) {
            return;
        }
        if (EnumSampleTypeCategory.模板.getValue().equals(criteria.getCategory())) {
            List<DtoSampleType> allTemplates = pageBean.getData();
            allTemplates.sort(Comparator.comparing(DtoSampleType::getTypeName, Comparator.reverseOrder()));
            List<DtoSampleType> allSampleTypes = new ArrayList<>();
            List<DtoSampleType> bigSampleTypes = repository.findByCategory(EnumSampleTypeCategory.检测类型大类.getValue());
            bigSampleTypes.sort(Comparator.comparing(DtoSampleType::getOrderNum, Comparator.reverseOrder()));
            List<DtoSampleType> childSampleTypes = repository.findByCategory(EnumSampleTypeCategory.检测类型小类.getValue());
            childSampleTypes.sort(Comparator.comparing(DtoSampleType::getOrderNum, Comparator.reverseOrder()));
            bigSampleTypes.forEach(bigType -> {
                allSampleTypes.add(bigType);
                allSampleTypes.addAll(childSampleTypes.stream().filter(s -> s.getParentId().equals(bigType.getId())).collect(Collectors.toList()));
            });
            List<DtoSampleType> sortedSampleTypes = new ArrayList<>();
            for (DtoSampleType sampleType : allSampleTypes) {
                sortedSampleTypes.addAll(allTemplates.stream().filter(t -> t.getParentId().equals(sampleType.getId())).collect(Collectors.toList()));
            }
            allTemplates.removeAll(sortedSampleTypes);
            sortedSampleTypes.addAll(allTemplates);
            sortedSampleTypes = sortedSampleTypes.stream().skip((long) (pageBean.getPageNo() - 1) * pageBean.getRowsPerPage()).limit(pageBean.getRowsPerPage()).collect(Collectors.toList());
            pageBean.setData(sortedSampleTypes);
        }
        List<DtoSampleType> list = pageBean.getData();


        //行业类型ids
        List<String> industryTypeIds = list.stream().map(DtoSampleType::getIndustryTypeId).distinct().collect(Collectors.toList());


        List<DtoIndustryType> industryTypeList = industryTypeService.findAll(industryTypeIds);
        List<DtoSampleType> sampleTypeList = repository.getList();
        for (DtoSampleType sampleType : list) {

            String industryTypeName = "";//所属行业类型
            String sampleTypeName = "";//所属检测类型
            if (StringUtil.isNotNull(sampleType.getIndustryTypeId()) && !sampleType.getIndustryTypeId().equals(UUIDHelper.GUID_EMPTY)) {
                Optional<DtoIndustryType> optionalDtoIndustryType = industryTypeList.stream().filter(p -> p.getId().equals(sampleType.getIndustryTypeId())).findFirst();
                if (StringUtil.isNotNull(optionalDtoIndustryType) && optionalDtoIndustryType.isPresent()) {
                    industryTypeName = optionalDtoIndustryType.get().getIndustryName();
                }
            }

            if (StringUtil.isNotNull(sampleType.getParentId()) && !sampleType.getParentId().equals(UUIDHelper.GUID_EMPTY)) {
                Optional<DtoSampleType> optionalDtoSampleType = sampleTypeList.stream().filter(p -> p.getId().equals(sampleType.getParentId())).findFirst();
                if (StringUtil.isNotNull(optionalDtoSampleType) && optionalDtoSampleType.isPresent()) {
                    sampleTypeName = optionalDtoSampleType.get().getTypeName();
                }
            }

            sampleType.setIndustryTypeName(industryTypeName);
            sampleType.setSampleTypeName(sampleTypeName);
        }


        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    /**
     * 更新检测类型/模板
     *
     * @param sampleType 检测类型
     * @return 更新检测类型
     */
    @Transactional
    @Override
    public DtoSampleType update(DtoSampleType sampleType) {
        // 判断名称重复
        Integer count = repository.countByNameAndParentId(sampleType.getTypeName(), sampleType.getId(),
                sampleType.getParentId());
        Integer category = sampleType.getCategory();
        // 不同行业类型下的检测类型大类可以重名
        if (category.equals(EnumSampleTypeCategory.检测类型大类.getValue())) {
            Integer index = repository.countByIndustryTypeId(sampleType.getId(), sampleType.getIndustryTypeId(),
                    sampleType.getTypeName());
            if (index > 0) {
                throw new BaseException("已存在相同名称的检测类型！");
            }
        } else {
            if (count > 0) {
                throw new BaseException("已存在相同名称的检测类型！");
            }
        }
        DtoSampleType dtoSampleType = super.update(sampleType);
        saveRedis(dtoSampleType);
        return dtoSampleType;
    }

    /**
     * 新增检测类型/模板
     *
     * @param sampleType 检测类型
     * @return 更新检测类型
     */
    @Transactional
    @Override
    public DtoSampleType save(DtoSampleType sampleType) {
        // 判断名称重复
        Integer count = repository.countByNameAndParentId(sampleType.getTypeName(), sampleType.getId(),
                sampleType.getParentId());
        Integer category = sampleType.getCategory();
        // 不同行业类型下的检测类型大类可以重名
        if (category.equals(EnumSampleTypeCategory.检测类型大类.getValue())) {
            Integer index = repository.countByIndustryTypeId(sampleType.getId(), sampleType.getIndustryTypeId(),
                    sampleType.getTypeName());
            if (index > 0) {
                throw new BaseException("已存在相同名称的检测类型！");
            }
        } else if (count > 0) {
            throw new BaseException("已存在相同名称的检测类型！");
        }
        DtoSampleType dtoSampleType = super.save(sampleType);
        saveRedis(dtoSampleType);
        return dtoSampleType;
    }

    /**
     * 根据样品小类id获取样品大类 如果传入的大类id，返回null
     *
     * @param id 样品小类id
     * @return 获取样品大类
     */
    @Override
    public DtoSampleType getBigSampleType(String id) {
        Integer category = repository.findCategoryById(id);
        if (category.equals(EnumSampleTypeCategory.检测类型大类.getValue())) {
            return null;
        } else {
            return repository.getBigSampleType(id);
        }

    }

    @Override
    public List<DtoSampleType> findAllBigSampleType() {
        return repository.findByCategory(EnumSampleTypeCategory.检测类型大类.getValue());
    }

    @Override
    public List<DtoSampleType> findRedisByIds(List<String> ids) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_SampleType.getValue());
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, ids);
        return setMaps(dataList, ids);
    }

    /**
     * 单独假删(级联删除下属的所有子类以及模板)
     */
    @Transactional
    @Override
    public void delete(String id) {
        List<String> ids = new ArrayList<>();
        ids.add(id);
        List<String> childrenIds = repository.getChildrenIds(id);
        if (StringUtil.isNotEmpty(childrenIds)) {
            ids.addAll(childrenIds);
            for (String parentId : childrenIds) {
                // 找到小类下模板的id
                List<String> sonIds = repository.getChildrenIds(parentId);
                if (StringUtil.isNotEmpty(sonIds)) {
                    ids.addAll(sonIds);
                }
            }
        }
        repository.logicDeleteById(ids, new Date());
    }

    // 按照排序值倒序排序
    @Override
    public List<TreeNode> getSampleTypeListByIndustryId(String industryId) {
        List<DtoSampleType> sampleTypeList;
        if (!industryId.equals(UUIDHelper.GUID_EMPTY)) {
            sampleTypeList = repository.getListByIndustryId(industryId);
        } else {
            sampleTypeList = repository.getListByNull();
        }
        List<TreeNode> nodeLists = new ArrayList<>();// 最终返回的行业类型-检测类型树

        // 将检测类型转换成树类型
        List<TreeNode> sampleLists = new ArrayList<>();
        for (DtoSampleType var : sampleTypeList) {
            TreeNode node = new TreeNode();
            node.setId(var.getId());
            node.setParentId(var.getParentId());
            node.setCategory(var.getCategory());
            node.setLabel(var.getTypeName());
            node.setOrderNum(var.getOrderNum());
            node.setExtent1(var.getIcon());
            node.setChildren(new ArrayList<>());
            sampleLists.add(node);
        }
        // 遍历检测类型,将list类型转换成tree树

        TreeNode node = new TreeNode();
        node.setId(UUIDHelper.GUID_EMPTY);
        node.setLabel("所有");
        node.setChildren(new ArrayList<>());

        // sampleLists = sampleLists.stream().map(i -> i *
        // i).distinct().collect(Collectors.toList());;
        List<TreeNode> lists = sampleLists.stream().sorted(Comparator.comparing(TreeNode::getOrderNum).reversed())
                .collect(Collectors.toList());
        sampleLists.stream().sorted(Comparator.comparing(TreeNode::getOrderNum).reversed());
        for (TreeNode var : lists) {
            for (TreeNode li : lists) {
                if (li.getParentId().equals(var.getId())) {
                    var.getChildren().add(li);
                    var.setIsLeaf(false);
                } else {
                    var.setIsLeaf(true);
                }
            }
            // 检测类型大类
            if (var.getCategory().equals(1) && var.getParentId().equals(UUIDHelper.GUID_EMPTY)) {
                node.getChildren().add(var);
            }
        }

        nodeLists.add(node);
        return nodeLists;
    }


    @Override
    public DtoSampleType findOne(String id) {
//        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_SampleType.getValue());
//        Object json = redisTemplate.opsForHash().get(key, id);
//        if (StringUtils.isNotNullAndEmpty(json)) {
//            //解析相应的配置数据
//            TypeLiteral<DtoSampleType> typeLiteral = new TypeLiteral<DtoSampleType>() {
//            };
//            DtoSampleType dtoSampleType = JsonIterator.deserialize(json.toString(), typeLiteral);
//            return dtoSampleType;
//        }
//        DtoSampleType dtoSampleType = super.findOne(id);
//        if (dtoSampleType != null) {
//            saveRedis(dtoSampleType);
//        }
        return super.findOne(id);
    }

    /**
     * 返回父类下所有的子类检测类型
     *
     * @param parentId 父类id
     * @return 返回父类下所有的子类检测类型
     */
    @Override
    public List<DtoSampleType> findByParentId(String parentId) {
        return repository.findByParentId(parentId);
    }

    /**
     * 返回父类下所有的子类检测类型
     *
     * @param parentIds 父类ids
     * @return 返回父类下所有的子类检测类型
     */
    @Override
    public List<DtoSampleType> findByParentIds(Collection<String> parentIds) {
        return repository.findByParentIdIn(parentIds);
    }


    @Override
    public List<DtoSampleType> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoSampleType> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 保存相应的redis数据
     *
     * @param item 检测类型的实体对象
     */
    @Override
    public void saveRedis(DtoSampleType item) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_SampleType.getValue());
        redisTemplate.opsForHash().put(key, item.getId(), JsonStream.serialize(item));
    }

    @Override
    public List<String> loadSampleTypeIds(String industryId) {
        List<DtoSampleType> sampleTypeList = repository.findByIndustryTypeIdAndCategory(industryId, EnumSampleTypeCategory.检测类型大类.getValue());
        if (StringUtil.isNotEmpty(sampleTypeList)) {
            return sampleTypeList.stream().map(DtoSampleType::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<DtoSampleType> findSampleType() {
        return repository.getListWithDeleted();
    }


    /**
     * 保存相应的redis数据
     *
     * @param ids 检测ids
     */
    private void deleteRedis(List<String> ids) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_SampleType.getValue());
        redisTemplate.opsForHash().delete(key, ids);
    }


    /**
     * 从redis中获取相应的数据
     *
     * @param dataList 数据集合
     * @return 返回测试项目数据
     */
    private List<DtoSampleType> setMaps(List<Object> dataList, List<String> ids) {
        List<DtoSampleType> itemList = new ArrayList<>();
        TypeLiteral<DtoSampleType> typeLiteral = new TypeLiteral<DtoSampleType>() {
        };
        List<String> existIds = new ArrayList<>();
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) {
                try {
                    DtoSampleType item = JsonIterator.deserialize(s.toString(), typeLiteral);
                    if (StringUtil.isNotNull(item)) {
                        existIds.add(item.getId());
                        itemList.add(item);
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
        }
        //将未缓存到redis的数据缓存起来（需要假删数据，防止业务库调用之后找不到这些数据）
        List<String> newIds = ids.stream().filter(p -> !existIds.contains(p)).collect(Collectors.toList());
        if (newIds.size() > 0) {
            List<DtoSampleType> dtoSampleTypes = repository.findAllDeleted(newIds);
            itemList.addAll(dtoSampleTypes);
            Map<String, Object> map = new HashMap<>();
            for (DtoSampleType dtoSampleType : dtoSampleTypes) {
                map.put(dtoSampleType.getId(), JsonStream.serialize(dtoSampleType));
            }
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_SampleType.getValue());
            redisTemplate.opsForHash().putAll(key, map);

        }
        return itemList;
    }
}