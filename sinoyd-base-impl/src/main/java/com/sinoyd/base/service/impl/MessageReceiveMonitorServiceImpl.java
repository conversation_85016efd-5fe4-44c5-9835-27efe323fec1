package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoMessageReceiveMonitor;
import com.sinoyd.base.repository.lims.MessageReceiveMonitorRepository;
import com.sinoyd.base.service.MessageReceiveMonitorService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.commons.enums.EnumMsgReSendStatus;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消费端消息监控服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/16
 * @since V100R001
 */
@Service
@Slf4j
public class MessageReceiveMonitorServiceImpl extends
        BaseJpaPhysicalDeleteServiceImpl<DtoMessageReceiveMonitor, String, MessageReceiveMonitorRepository> implements MessageReceiveMonitorService {

    private RabbitTemplate rabbitTemplate;

    @Override
    public void findByPage(PageBean<DtoMessageReceiveMonitor> page, BaseCriteria criteria) {
        page.setEntityName("DtoMessageReceiveMonitor m");
        page.setSelect("select m");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public void reSendMessage(String id) {
        DtoMessageReceiveMonitor messageMonitor = findOne(id);
        try {
            //重发消息
            rabbitTemplate.convertAndSend(messageMonitor.getExchange(),
                    messageMonitor.getRoutingKey(), messageMonitor.getMessage());
            //检查重发状态
            messageMonitor.setIsSuccess(true);
            messageMonitor.setSendStatus(EnumMsgReSendStatus.重发成功.getValue());
        } catch (Exception e) {
            // 记录日志
            log.error("消息重发异常", e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    @Transactional
    public void createAndSave(String message, String exchange, String routingKey, String queue, String reason) {
        DtoMessageReceiveMonitor monitor = new DtoMessageReceiveMonitor(message, exchange, routingKey, queue, false, reason);
        save(monitor);
    }

    @Autowired
    public void setRabbitTemplate(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }
}
