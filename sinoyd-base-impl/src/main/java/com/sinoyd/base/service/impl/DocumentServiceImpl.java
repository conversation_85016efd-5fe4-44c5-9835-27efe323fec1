package com.sinoyd.base.service.impl;

import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.aspose.cells.Worksheet;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.DtoExcelParam;
import com.sinoyd.base.dto.customer.DtoPathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoLogForLuckySheet;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.LogForLuckySheetService;
import com.sinoyd.base.utils.EsapiUtil;
import com.sinoyd.base.utils.base.StringCheckUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.preview.DocumentPreviewFactory;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.ReadXMLUtil;
import com.sinoyd.frame.util.UUIDHelper;
import dm.jdbc.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文档接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
@Slf4j
public class DocumentServiceImpl extends BaseJpaServiceImpl<DtoDocument, String, DocumentRepository> implements DocumentService {

    /**
     * 全局的数据，将第一次的xml的读取完的数据放置缓存中
     */
    public static List<DtoPathConfig> pathConfigs = new ArrayList<>();
    private static final String TEMPLE_PATH = "templePath";

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private LogForLuckySheetService logForLuckySheetService;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private StringCheckUtil stringCheckUtil;

    @Override
    public void findByPage(PageBean<DtoDocument> pageBean, BaseCriteria documentCriteria) {
        pageBean.setEntityName("DtoDocument p");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, documentCriteria);
        //查询报告点位示意图时，需要一并返回报告样品对应的送样单的点位示意图
        addPointPicForRecord(pageBean, documentCriteria);
        fillingIsLock(pageBean.getData());
    }

    /**
     * 获取报告所选样品对应的送样单的点位示意图
     *
     * @param pageBean     分页对象
     * @param baseCriteria 查询条件对象
     */
    private void addPointPicForRecord(PageBean<DtoDocument> pageBean, BaseCriteria baseCriteria) {
        DocumentCriteria documentCriteria = (DocumentCriteria) baseCriteria;
        Boolean isReportPointPic = documentCriteria.getIsReportPointPic();
        if (StringUtil.isNotNull(isReportPointPic) && isReportPointPic && "pointPic".equals(documentCriteria.getDocTypeId())) {
            String reportId = documentCriteria.getFolderId();
            if (StringUtil.isNotEmpty(reportId) && !UUIDHelper.GUID_EMPTY.equals(reportId)) {
                List<String> sampleIdList = commonRepository.find("select objectId from DtoReportDetail r where r.reportId = :reportId",
                        Collections.singletonMap("reportId", reportId));
                if (StringUtil.isNotEmpty(sampleIdList)) {
                    List<String> receiveIdList = commonRepository.find("select distinct receiveId from DtoSample p where p.id in :ids and p.isDeleted = 0",
                            Collections.singletonMap("ids", sampleIdList));
                    receiveIdList = receiveIdList.stream().filter(p -> StringUtil.isNotEmpty(p) && !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(receiveIdList)) {
                        List<DtoDocument> pointPicForRecord = repository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(receiveIdList, "pointPic");
                        pointPicForRecord = pointPicForRecord.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(pointPicForRecord)) {
                            List<DtoDocument> pointPicList = pageBean.getData();
                            pointPicList.addAll(pointPicForRecord);
                            pointPicList.sort(Comparator.comparing(DtoDocument::getCreateDate).reversed().thenComparing(DtoDocument::getFilename));
                            pageBean.setData(pointPicList);
                            pageBean.setRowsCount(pointPicList.size());
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public DtoDocument save(DtoDocument document) {
        return super.save(document);
    }

    @Override
    @Transactional
    public Map<String, byte[]> downloadAsStream(DtoDocument document) {
        DtoDocument dbDocument;
        if (StringUtil.isNotEmpty(document.getId())) {
            dbDocument = super.findOne(document.getId());
        } else if (StringUtil.isNotEmpty(document.getFolderId()) && StringUtil.isNotEmpty(document.getDocTypeId())) {
            List<DtoDocument> documents = repository.findByFolderIdAndDocTypeIdOrderByCreateDateDesc(document.getFolderId(), document.getDocTypeId());
            dbDocument = documents.stream().filter(p -> !p.getIsDeleted()).max(Comparator.comparing(DtoDocument::getCreateDate))
                    .orElseThrow(() -> new BaseException("文件不存在，请确认"));
        } else {
            throw new BaseException("...... the parameter is incorrect for attachment download......");
        }
        // 以流的形式下载文件
        String absolutePath = filePathConfig.getFilePath() + dbDocument.getPath();
        String fileName = dbDocument.getFilename();
        InputStream is = null;

        try {
            is = new BufferedInputStream(Files.newInputStream(Paths.get(absolutePath)));
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            Map<String, byte[]> result = new HashMap<>();
            result.put(fileName, buffer);

            //更新下载次数
            Integer downLoadTimes = dbDocument.getDownloadTimes();
            downLoadTimes += 1;
            dbDocument.setDownloadTimes(downLoadTimes);
            super.update(dbDocument);

            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("文件流关闭失败", e);
                }
            }
        }
    }

    @Transactional
    @Override
    public String download(String documentId, HttpServletResponse response) throws IOException {
        //下载还要修正下载次数
        DtoDocument document = super.findOne(documentId);
        //luckysheet 下载原始记录单的时候，传的参数是工作单的id，所以下载的时候数据要通过folderId获取
        if (StringUtil.isNull(document)) {
            Optional<DtoDocument> documentOptional = repository.findByFolderId(documentId).stream()
                    .filter(p -> p.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD))
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (documentOptional.isPresent()) {
                document = documentOptional.get();
            }
        }
        return download(document, response);
    }


    /**
     * 下载文件（返回文件绝对路径）
     *
     * @param documentId 文件id
     * @return 文件绝对路径
     */
    @Transactional
    @Override
    public String downloadPath(String documentId) {
        //下载还要修正下载次数
        DtoDocument document = super.findOne(documentId);
        //luckysheet 下载原始记录单的时候，传的参数是工作单的id，所以下载的时候数据要通过folderId获取
        if (StringUtil.isNull(document)) {
            Optional<DtoDocument> documentOptional = repository.findByFolderId(documentId).stream()
                    .filter(p -> p.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD))
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (documentOptional.isPresent()) {
                document = documentOptional.get();
            }
        }
        if (StringUtil.isNull(document)) {
            throw new BaseException("文件不存在,请确认");
        }
        //下载还要修正下载次数
        Integer downLoadTimes = document.getDownloadTimes();
        if (StringUtil.isNull(downLoadTimes)) {
            downLoadTimes = 0;//防止是NULL，系统出错
        }
        downLoadTimes += 1;
        document.setDownloadTimes(downLoadTimes);
        super.update(document);
        return filePathConfig.getFilePath() + document.getPath();
    }

    @Transactional
    @Override
    public String download(String objectId, String path, HttpServletResponse httpServletResponse) throws IOException {
        List<DtoDocument> documents = repository.findByFolderId(objectId);
        Optional<DtoDocument> optional = documents.stream().filter(p -> p.getPath().equals(path)).findFirst();
        if (StringUtil.isNotNull(optional) && optional.isPresent()) {
            DtoDocument document = optional.get();
            return download(document, httpServletResponse);
        }
        throw new BaseException("文件不存在,请确认");
    }

    @Transactional
    @Override
    public String syncExcel(String documentId, List<DtoExcelParam> excelParams) {
        DtoDocument document = super.findOne(documentId);
        if (StringUtil.isNull(document)) {
            Optional<DtoDocument> documentOptional = repository.findByFolderId(documentId).stream()
                    .filter(p -> p.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD))
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (documentOptional.isPresent()) {
                document = documentOptional.get();
            }
        }
        String path = filePathConfig.getFilePath() + document.getPath();
        FileInputStream file = null;
        FileOutputStream outfile = null;
        XSSFWorkbook workbook = null;
        List<DtoLogForLuckySheet> logs = new ArrayList<>();
        List<String> sheetNames = excelParams.stream().map(DtoExcelParam::getSheetName).distinct().collect(Collectors.toList());
        try {
            file = new FileInputStream(path);
            workbook = new XSSFWorkbook(file);
            outfile = new FileOutputStream(new File(path));
            for (String sheetName : sheetNames) {
                DtoLogForLuckySheet log = new DtoLogForLuckySheet();
                log.setObjectId(documentId);
                CurrentPrincipalUser principalUser = PrincipalContextUser.getPrincipal();
                log.setOperatorId(principalUser.getUserId());
                log.setOperatorName(principalUser.getUserName());
                log.setOperateTime(new Date());
                log.setOperateInfo("LuckySheet单元格值修改");
                StringBuffer remark = new StringBuffer().append("修改").append(sheetName).append("页");
                XSSFSheet sheet = workbook.getSheet(sheetName);
                List<DtoExcelParam> params2sheet = excelParams.stream().filter(e -> sheetName.equals(e.getSheetName())).collect(Collectors.toList());
                //调整单元格值和格式的参数
                List<DtoExcelParam> cellValueStyleParams = params2sheet.stream().filter(v -> !(StringUtil.isNotNull(v.getRowHeight()) || StringUtil.isNotNull(v.getColWidth())))
                        .collect(Collectors.toList());
                for (DtoExcelParam param : cellValueStyleParams) {
                    Cell cell = sheet.getRow(param.getRow()).getCell(param.getCol(), Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    cell.setCellValue(param.getValue());
                    CellStyle cellStyle = cell.getCellStyle();
                    Font font = workbook.getFontAt(cellStyle.getFontIndex());
                    Font newFont = workbook.createFont();
                    newFont.setBold(font.getBold());
                    newFont.setFontHeight(font.getFontHeight());
                    newFont.setColor(font.getColor());
                    newFont.setFontName(font.getFontName());
                    newFont.setItalic(font.getItalic());
                    newFont.setStrikeout(font.getStrikeout());
                    newFont.setTypeOffset(font.getTypeOffset());
                    newFont.setUnderline(font.getUnderline());
//                    newFont.setCharSet(font.getCharSet());
                    if (StringUtil.isNotNull(param.getFontSize()) && MathUtil.isNumber(param.getFontSize())) {
                        newFont.setFontHeightInPoints(Short.parseShort(param.getFontSize()));
                    }
                    cellStyle.setFont(newFont);
                    cell.setCellStyle(cellStyle);
                    remark.append(",修改单元格").append(excelColIndexToStr(param.getCol() + 1)).append(param.getRow() + 1)
                            .append("的值").append("由'").append(param.getOldValue()).append("'修改为'").append(param.getValue()).append("'");
                }
                remark.append("。");
                log.setRemark(remark.toString());
                logs.add(log);
                //设置行高
                params2sheet.stream().filter(v -> StringUtil.isNotNull(v.getRowHeight())).forEach(v -> sheet.getRow(v.getRow()).setHeightInPoints(v.getRowHeight().shortValue()));
                //设置列宽
                params2sheet.stream().filter(v -> StringUtil.isNotNull(v.getColWidth())).forEach(v -> sheet.setColumnWidth(v.getCol(), v.getColWidth() * 256));
            }
            if (StringUtil.isNotEmpty(logs)) {
                logForLuckySheetService.save(logs);
            }
            //触发表格中所有单元格公式的计算
            XSSFFormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            evaluator.evaluateAll();
            workbook.write(outfile);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("同步Excel文件出错:" + e.getMessage());
        } finally {
            try {
                if (workbook != null) {
                    workbook.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            try {
                if (file != null) {
                    file.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            try {
                if (outfile != null) {
                    outfile.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }

    @Override
    @Transactional
    public List<DtoDocument> upload(HttpServletRequest request, List<String> allowedSuffixList) {
        return uploadFile(request, false, allowedSuffixList);
    }

    @Override
    @Transactional
    public List<DtoDocument> uploadReportConfig(HttpServletRequest request) {
        return uploadFile(request, true, null);
    }

    /**
     * 上传路径
     *
     * @param request           上传内容
     * @param isReportType      报告类型
     * @param allowedSuffixList 文件类型
     * @return 内容
     */
    private List<DtoDocument> uploadFile(HttpServletRequest request, Boolean isReportType, List<String> allowedSuffixList) {
        String docTypeId = request.getParameter("docTypeId");
        String docTypeName = request.getParameter("docTypeName");
        String folderId = request.getParameter("folderId");
        String folderName = request.getParameter("folderName");
        String uploadPath = request.getParameter("path");
        //移动端附件视频的时长，用于移动端显示时长使用
        String timeCount = request.getParameter("timeCount");
        Boolean systemDraw = Boolean.FALSE;
        String isSystemDraw = request.getParameter("isSystemDraw");
        String deleteId = request.getParameter("deleteId");
        if (StringUtil.isNotNull(isSystemDraw) && "true".equals(isSystemDraw)) {
            systemDraw = Boolean.TRUE;
        }
        // 需要删除的旧文件，作用于只能上传一个附件的情况
        if (StringUtil.isNotEmpty(deleteId)){
            this.logicDeleteById(deleteId);
        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        //判断上传文件类型是否为报表类型
        List<DtoDocument> documents;
        //判断是否已经存在文件，有则覆盖文件
        Optional<DtoDocument> documentOptional = Optional.empty();
        if (isReportType) {
            documents = findByObjectId(folderId);
            if (StringUtil.isNotEmpty(documents)) {
                documentOptional = documents.stream().findFirst();
            }
        }

        String filePath = filePathConfig.getFilePath();
        //2022-08-03 报表模板上传的前置路径地址，用生成模板的前置路径
        if (isReportType) {
            filePath = filePathConfig.getTemplatePath();
        }
        List<DtoDocument> dtoDocuments = new ArrayList<>();
        //根据uploadPath 处理附件（可能需要存储到不同路径下面）
        String newPath = StringUtils.isNotNullAndEmpty(uploadPath) ? "/" + uploadPath : "";
        //创建文件目录
        String checkPath = filePath + newPath;
        if (!EsapiUtil.isValidFilePath(checkPath)) {
            throw new BaseException("非法的文件路径");
        }
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        //将返回的fileNames存储到document
        for (MultipartFile multipartFile : files) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            //后缀名
            String fileName = multipartFile.getOriginalFilename();
            log.info("... fileName: " + fileName);
            //校验上传文件名称是否包含特殊字符
            if (stringCheckUtil.checkSpecialString(fileName.substring(0,fileName.lastIndexOf(".")))){
                throw new BaseException("上传的文件名称不能包含特殊字符！例如[!@#$%^&?，。/]...");
            }
            //物理名称
            String physicalName = dateFormat.format(new Date()) + "_" + fileName;
            if (isReportType) {
                physicalName = fileName;
            }
            String downPath = newPath + "/" + physicalName;
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            if (checkFile(fileType, allowedSuffixList)) {
                DtoDocument dtoDocument = new DtoDocument();
                if (isReportType) {
                    documentOptional.ifPresent(p -> dtoDocument.setId(p.getId()));
                }
                dtoDocument.setFolderId(folderId);
                dtoDocument.setFolderName(folderName);
                dtoDocument.setFilename(fileName);
                dtoDocument.setPhysicalName(physicalName);
                dtoDocument.setPath(downPath);
                dtoDocument.setIsTranscript(false);
                dtoDocument.setDocTypeId(docTypeId);
                dtoDocument.setDocTypeName(docTypeName);
                dtoDocument.setDocSize(Integer.parseInt(String.valueOf(multipartFile.getSize())));
                dtoDocument.setDocSuffix(fileType);
                dtoDocument.setDownloadTimes(0);
                dtoDocument.setOrderNum(0);
                dtoDocument.setRemark(timeCount);
                dtoDocument.setUploadPerson(PrincipalContextUser.getPrincipal().getUserName());
                dtoDocument.setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
                dtoDocument.setIsStick(false);
                dtoDocument.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
                dtoDocument.setIsSystemDraw(systemDraw);
                File file = new File(new File(checkPath).getAbsoluteFile() + File.separator + physicalName);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                try {
                    multipartFile.transferTo(file);
                    dtoDocuments.add(dtoDocument);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }

            } else {
                throw new BaseException("不允许上传该文件类型【" + fileType + "】的文件");
            }

        }
        return super.save(dtoDocuments);
    }

    @Override
    public String getDocumentPath(String code, Map<String, Object> map) throws Exception {

        DtoPathConfig dtoPathConfig = getPathConfigByCode(code);
        if (StringUtil.isNull(dtoPathConfig)) {
            throw new BaseException("附件归档路径未找到");
        }

        //类的地址
        String className = dtoPathConfig.getClassName();
        //方法名
        String methodName = dtoPathConfig.getMethod();

        //占位符
        String placeholder = dtoPathConfig.getPlaceholder();

        //路径
        String placeholderPath = dtoPathConfig.getPath();

        //处理相应的数据源
        String path = placeholderPath;

        if (StringUtils.isNotNullAndEmpty(className)) {

            Class cls = Class.forName(className);
            Set<String> keys = map.keySet();
            Class<?>[] classes = new Class[keys.size()];
            Object[] objects = new Object[keys.size()];
            Integer i = 0;
            //计算方法参数
            for (String key : keys) {
                Object value = map.get(key);
                classes[i] = value.getClass();
                objects[i] = value;
                i++;
            }
            Method method = cls.getMethod(methodName, classes);
            Object data = method.invoke(SpringContextAware.getBean(cls), objects);
            if (StringUtils.isNotNullAndEmpty(placeholder)) {
                //如果返回的直接是字符串，直接替换
                if (data instanceof String) {
                    path = path.replace("{" + placeholder + "}", String.valueOf(data));
                } else {
                    //根据占位符得出相应的数据字段名称
                    String[] fieldNames = placeholder.split(",");
                    if (StringUtil.isNotNull(data)) {
                        Map<String, Object> dataMap = JsonIterator.deserialize(JsonStream.serialize(data), Map.class);
                        for (String fieldName : fieldNames) {
                            String value = String.valueOf(dataMap.get(fieldName));
                            if (!StringUtils.isNotNullAndEmpty(value)) {
                                value = "";//防止是null值
                            }
                            path = path.replace("{" + fieldName + "}", value.trim().replace("/", "-"));
                        }
                    }
                }
            }
        }
        return path;
    }

    @Override
    public String getDocumentPathByPlaceholder(String code, Map<String, Object> map) {
        DtoPathConfig dtoPathConfig = getPathConfigByCode(code);
        if (StringUtil.isNull(dtoPathConfig)) {
            throw new BaseException("附件归档路径未找到");
        }
        //占位符
        String placeholder = dtoPathConfig.getPlaceholder();

        //路径
        String placeholderPath = dtoPathConfig.getPath();

        //处理相应的数据源
        String path = placeholderPath;

        if (StringUtils.isNotNullAndEmpty(placeholder)) {
            //根据占位符得出相应的数据字段名称
            String[] fieldNames = placeholder.split(",");
            for (String fieldName : fieldNames) {
                String value = String.valueOf(map.getOrDefault(fieldName, ""));
                path = path.replace("{" + fieldName + "}", value.trim().replace("/", "-"));
            }
        }
        return path;
    }

    @Override
    public String convertBase64Content(String url, Integer width, Integer height) {
        if (StringUtils.isNotNullAndEmpty(url) && url.lastIndexOf(".") >= 0) {
            String filePath = filePathConfig.getFilePath();
            String fileType = url.substring(url.lastIndexOf("."));
            String path = filePath + url;
            File file = new File(path);
            if (file.exists()) {
                String newPath = resize(url, width, height);
                return getImageStr(newPath, fileType);
            }
        }
        return "";
    }

    @Override
    public String convertBase64Content(String url) {
        if (StringUtils.isNotNullAndEmpty(url) && url.lastIndexOf(".") >= 0) {
            String filePath = filePathConfig.getFilePath();
            String fileType = url.substring(url.lastIndexOf("."));
            String path = filePath + url;
            File file = new File(path);
            if (file.exists()) {
                return getImageStr(path, fileType);
            }
        }
        return "";
    }

    @Override
    public String convertFileToBase64(String url) {
        File file = new File(filePathConfig.getFilePath() + url);
        if (file.exists()) {
            byte[] fileContent = new byte[(int) file.length()];
            FileInputStream fis = null;
            try {
                fis = new FileInputStream(file);
                fis.read(fileContent);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                FileUtil.close(fis);
            }
            // 使用Java 8内置的Base64转换工具类
            String encodedBase64 = Base64.getEncoder().encodeToString(fileContent);
            return encodedBase64;
        }
        return "";
    }

    @Override
    @Transactional
    public Integer deleteByFolderId(String folderId) {
        return repository.deleteByFolderId(folderId);
    }

    @Override
    @Transactional
    public Boolean uploadBase64Content(
            HttpServletRequest request) {
        String base64Str = request.getParameter("base64");
        String docTypeId = request.getParameter("docTypeId");
        String docTypeName = request.getParameter("docTypeName");
        String folderId = request.getParameter("folderId");
        String folderName = request.getParameter("folderName");
        String uploadPath = request.getParameter("path");
        String fileName = request.getParameter("fileName");
        //对字节数组字符串进行Base64解码并生成图片
        if (StringUtil.isNull(base64Str)) //图像数据为空
        {
            return false;
        }
        List<String> fileTypes = new ArrayList<>();
        fileTypes.add("bmp");
        fileTypes.add("jpeg");
        fileTypes.add("gif");
        fileTypes.add("psd");
        fileTypes.add("png");
        fileTypes.add("tiff");
        fileTypes.add("jpg");
        //防止前端传了data:image/png;base64,的格式，后端进行处理替换
        for (String fileType : fileTypes) {
            base64Str = base64Str.replace("data:image/" + fileType + ";base64,", "");
        }
        BASE64Decoder decoder = new BASE64Decoder();
        OutputStream out = null;
        try {
            //Base64解码
            byte[] b = decoder.decodeBuffer(base64Str);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {//调整异常数据
                    b[i] += 256;
                }
            }
            String filePath = filePathConfig.getFilePath();
            //根据uploadPath 处理附件（可能需要存储到不同路径下面）
            String newPath = StringUtils.isNotNullAndEmpty(uploadPath) ? "/" + uploadPath : "";
            //创建文件目录
            String checkPath = filePath + newPath;
            //生成jpeg图片
            File fileStream = new File(checkPath);
            if (!fileStream.exists()) {
                fileStream.mkdirs();
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");

            //物理名称
            String physicalName = dateFormat.format(new Date()) + "_" + fileName;

            String downPath = newPath + "/" + physicalName;

            String imgFilePath = checkPath + "/" + physicalName;//新生成的图片

            String fileType = fileName.substring(fileName.lastIndexOf("."));
            if (checkFile(fileType, null)) {
                out = new FileOutputStream(imgFilePath);

                out.write(b);
                out.flush();
                out.close();
                File file = new File(imgFilePath);
                DtoDocument dtoDocument = new DtoDocument();
                dtoDocument.setFolderId(folderId);
                dtoDocument.setFolderName(folderName);
                dtoDocument.setFilename(fileName);
                dtoDocument.setPhysicalName(physicalName);
                dtoDocument.setPath(downPath);
                dtoDocument.setIsTranscript(false);
                dtoDocument.setDocTypeId(docTypeId);
                dtoDocument.setDocTypeName(docTypeName);
                dtoDocument.setDocSize(Integer.parseInt(String.valueOf(file.length())));
                dtoDocument.setDocSuffix(fileType);
                dtoDocument.setDownloadTimes(0);
                dtoDocument.setOrderNum(0);
                dtoDocument.setRemark("");
                dtoDocument.setUploadPerson(PrincipalContextUser.getPrincipal().getUserName());
                dtoDocument.setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
                dtoDocument.setIsStick(false);
                dtoDocument.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
                super.save(dtoDocument);
                return true;
            } else {
                throw new BaseException("不允许上传该文件类型【" + fileType + "】的文件");
            }
        } catch (Exception e) {
            return false;
        } finally {
            FileUtil.close(out);
        }
    }


    /**
     * 将质控申请下的附件,拷贝一份到文件质控明细下
     *
     * @param fileId  文件质控明细id
     * @param foldId  质控申请id
     * @param newPath 新路径
     */
    @Transactional
    @Override
    public void syncFileControlApplyDetail(String fileId, String foldId, String newPath) {
        List<DtoDocument> dtoDocuments = new ArrayList<>();
        // 根据folderId查询相关附件记录
        List<DtoDocument> documents = repository.findByFolderId(foldId);
        if (StringUtil.isNotEmpty(documents)) {
            // 创建文件根目录
            String filePath = filePathConfig.getFilePath();
            // 日期格式
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            // 获取当前用户
            CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
            // 进行附件的拷贝
            for (DtoDocument document : documents) {
                // 获取文件路径
                File file = new File(filePathConfig.getFilePath() + document.getPath());
                // 获取文件名
                String filename = document.getFilename();
                if (!file.exists()) {
                    throw new BaseException("文件" + filename + "不存在,请确认");
                }
                // 文件物理名称(时间戳+文件名称)
                String physicalName = dateFormat.format(new Date()) + "_" + document.getFilename();
                FileInputStream fileInputStream = null;
                FileOutputStream fileOutputStream = null;
                try {
                    // 读取文件流
                    fileInputStream = new FileInputStream(file);
                    // 写入文件流
                    fileOutputStream = new FileOutputStream(new File(filePath + "/" + newPath + physicalName));
                    byte[] temp = new byte[1024 * 1024];
                    int i = fileInputStream.read(temp);
                    while (i != -1) {
                        fileOutputStream.write(temp, 0, temp.length);
                        fileOutputStream.flush();
                        i = fileInputStream.read(temp);
                    }
                    DtoDocument dtoDocument = new DtoDocument();
                    dtoDocument.setFolderId(fileId);
                    dtoDocument.setFilename(document.getFilename());
                    dtoDocument.setPhysicalName(physicalName);
                    dtoDocument.setPath("/" + newPath + physicalName);
                    dtoDocument.setIsTranscript(false);
                    dtoDocument.setDocTypeId(UUIDHelper.GUID_EMPTY);
                    dtoDocument.setDocSize(document.getDocSize());
                    dtoDocument.setDocSuffix(document.getDocSuffix());
                    dtoDocument.setDownloadTimes(0);
                    dtoDocument.setOrderNum(0);
                    dtoDocument.setRemark("");
                    dtoDocument.setUploadPerson(user.getUserName());
                    dtoDocument.setUploadPersonId(user.getUserId());
                    dtoDocument.setIsStick(false);
                    dtoDocument.setOrgId(user.getOrgId());
                    dtoDocuments.add(dtoDocument);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                } finally {
                    // 关闭流
                    if (fileInputStream != null) {
                        try {
                            fileInputStream.close();
                        } catch (IOException e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                    if (fileOutputStream != null) {
                        try {
                            fileOutputStream.close();
                        } catch (IOException e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(dtoDocuments)) {
            super.save(dtoDocuments);
        }
    }

    @Override
    public List<DtoDocument> findByObjectId(String objectId) {
        return repository.findByFolderId(objectId);
    }

    @Override
    public List<DtoDocument> findByObjectIds(Collection<String> objectIds) {
        return repository.findByFolderIdIn(objectIds);
    }

    //#region 私有方法

    /**
     * 根据图片地址转换为base64编码字符串
     *
     * @param imgFile 图片文件
     * @return 返回base64相关内容
     */
    private String getImageStr(String imgFile, String fileType) {
        List<String> fileTypes = new ArrayList<>();
        fileTypes.add(".bmp");
        fileTypes.add(".jpeg");
        fileTypes.add(".gif");
        fileTypes.add(".psd");
        fileTypes.add(".png");
        fileTypes.add(".tiff");
        fileTypes.add(".jpg");
        if (!fileTypes.contains(fileType.toLowerCase())) {
            return "";
        }
        String imgStr = "";
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(imgFile);
            byte[] data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
            // 加密
            BASE64Encoder encoder = new BASE64Encoder();
            imgStr = encoder.encode(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            FileUtil.close(inputStream);
        }
        if (StringUtils.isNotNullAndEmpty(imgStr)) {
            return "data:image/jpeg;base64," + imgStr;
        } else {
            return imgStr;
        }
    }

    /**
     * 下载文档
     *
     * @param document 文档对象
     * @param response 相应流
     * @return 返回下载文件
     */
    private String download(DtoDocument document, HttpServletResponse response) throws IOException {
        if (StringUtil.isNull(document)) {
            throw new BaseException("文件不存在,请确认");
        }
        //下载还要修正下载次数
        Integer downLoadTimes = document.getDownloadTimes();
        if (StringUtil.isNull(downLoadTimes)) {
            downLoadTimes = 0;//防止是NULL，系统出错
        }
        downLoadTimes += 1;
        document.setDownloadTimes(downLoadTimes);
        super.update(document);
        String path = filePathConfig.getFilePath() + document.getPath();
        String filename = document.getFilename();
        return fileDownload(path, filename, response);
    }

    /**
     * 下载文档
     *
     * @param document 文档对象
     * @param response 相应流
     * @return 返回下载文件
     */
    @Override
    public String downloadReport(DtoDocument document, HttpServletResponse response) throws IOException {
        //下载还要修正下载次数
        Integer downLoadTimes = document.getDownloadTimes();
        if (StringUtil.isNull(downLoadTimes)) {
            downLoadTimes = 0;//防止是NULL，系统出错
        }
        downLoadTimes += 1;
        document.setDownloadTimes(downLoadTimes);
        super.update(document);
        String path = filePathConfig.getTemplatePath() + document.getPath();

        String filename = document.getFilename();
        return fileDownload(path, filename, response);

    }


    /**
     * 下载的公共方法封装
     *
     * @param path     下载路径
     * @param filename 文件名称
     * @param response 浏览器响应数据
     * @return 返回数据信息
     */
    @Override
    public String fileDownload(String path, String filename, HttpServletResponse response) {
        File file = new File(path);
        if (!file.exists()) {
//            throw new BaseException("文件不存在,请确认");
            return "文件不存在,请确认";
        }
        FileInputStream fileInputStream = null;
        OutputStream outputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(filename, "UTF-8"));
            outputStream = response.getOutputStream();

            byte[] bytes = new byte[2048];
            int len = 0;
            while ((len = fileInputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("下载文件出错!");
        } finally {
            FileUtil.close(fileInputStream);
            FileUtil.close(outputStream);
        }
        return null;
    }


    /**
     * 获取所有附件机制的路径
     *
     * @return 返回审核动作
     */
    private List<DtoPathConfig> getAllPathConfig() {
        List<Map<String, Object>> mapList = ReadXMLUtil.readXML("filePathConfig.xml");
        TypeLiteral<List<DtoPathConfig>> typeLiteral = new TypeLiteral<List<DtoPathConfig>>() {
        };
        return JsonIterator.deserialize(JsonStream.serialize(mapList), typeLiteral);
    }


    /**
     * 获取指定编号的配置信息
     *
     * @param code 配置编号
     * @return 返回配置信息
     */
    private DtoPathConfig getPathConfigByCode(String code) {
        if (StringUtil.isEmpty(pathConfigs)) {
            pathConfigs = getAllPathConfig();
        }
        Optional<DtoPathConfig> optionalDtoPathConfig = pathConfigs.stream().filter(p -> p.getCode().equals(code)).findFirst();
        if (StringUtil.isNotNull(optionalDtoPathConfig) && optionalDtoPathConfig.isPresent()) {
            return optionalDtoPathConfig.get();
        }
        return null;
    }


    /**
     * 返回压缩后的大小
     *
     * @param fileName 文件名称
     * @param w        宽度
     * @param h        高度
     * @return 返回数据
     * @throws IOException
     */
    private String resize(String fileName, Integer w, Integer h) {

        //输出路径
        String outFilePath = filePathConfig.getOutputPath();

        String outPath = outFilePath + fileName;

        //本身文件路径
        String filePath = filePathConfig.getFilePath();
        String path = filePath + fileName;

        File file = new File(path);// 读入文件
        //生成jpeg图片
        File fileStream = new File(outPath.replace(file.getName(), ""));
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        Image image = null;
        FileOutputStream out = null;
        try {
            image = ImageIO.read(file);

            // SCALE_SMOOTH 的缩略算法 生成缩略图片的平滑度的 优先级比速度高 生成的图片质量比较好 但速度慢

            BufferedImage img = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);

            img.getGraphics().drawImage(image, 0, 0, w, h, null); // 绘制缩小后的图

            File destFile = new File(outPath);

            out = new FileOutputStream(destFile); // 输出到文件流
            // 可以正常实现bmp、png、gif转jpg
            ImageIO.write(img, "jpeg", out);
            out.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            FileUtil.close(out);
        }
        return outPath;
    }

    @Override
    public String directoryToZip(String directory, HttpServletResponse response) {
        String result = "压缩成功";
        OutputStream outputStream = null;
        try {
            //压缩完成后压缩包放在response流中返回前端
            outputStream = response.getOutputStream();
            this.toZip(directory, outputStream, true);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            result = "压缩失败，报错原因" + e.getMessage();
        } finally {
            //完成压缩后删除临时文件夹
            File file = new File(directory);
            this.deleteDirectory(file);
            FileUtil.close(outputStream);
        }
        return result;
    }

    @Override
    public void preview(String code, DocumentPreviewVO vo, HttpServletResponse response) {
        String pathValue = filePathConfig.getFilePath();
        if (TEMPLE_PATH.equals(code)) {
            pathValue = filePathConfig.getTemplatePath();
        }
        String sourceFilePath = vo.getSourceFilePath();
        if (!"/".equals(sourceFilePath.substring(0, 1))) {
            sourceFilePath = String.format("/%s", sourceFilePath);
            vo.setSourceFilePath(sourceFilePath);
        }
        String filePath = pathValue + sourceFilePath;
        if (EsapiUtil.isValidFilePath(filePath, vo.getSourceFileName())) {
            DocumentPreviewFactory.previewAsPDF(pathValue, vo, response);
        } else {
            throw new BaseException("非法的文件路径");
        }
    }

    @Override
    @Transactional
    public void copyFile(String code, String folderId, String documentId) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", folderId);
        String filePath = filePathConfig.getFilePath();
        String path = "";
        try {
            path = "/" + getDocumentPath(code, map);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(e.getMessage());
        }
        //创建文件目录
        String checkPath = filePath + path;

        DtoDocument document = repository.findOne(documentId);
        if (StringUtil.isNotNull(document)) {
            File fileStream = new File(checkPath);
            if (!fileStream.exists()) {
                fileStream.mkdirs();
            }
            File source = new File(filePathConfig.getFilePath() + document.getPath());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String physicalName = dateFormat.format(new Date()) + "_" + document.getFilename();
            File file = new File(checkPath + "/" + physicalName);
            DtoDocument save = new DtoDocument();
            BeanUtils.copyProperties(document, save, "id", "folderId", "path", "downloadTimes", "orgId", "creator",
                    "createDate", "domainId", "modifier", "modifyDate");
            save.setFolderId(folderId);
            save.setPhysicalName(physicalName);
            save.setPath(path + "/" + physicalName);
            save.setUploadPerson(PrincipalContextUser.getPrincipal().getUserName());
            save.setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
            try {
                FileUtils.copyFile(source, file);
            } catch (Exception e) {
                throw new BaseException(e.getMessage());
            }
            repository.save(save);
        }
    }

    @Override
    @Transactional
    public void renameFile(String documentId, String fileName) {
        DtoDocument document = repository.findOne(documentId);
        if (StringUtil.isNotNull(document)) {
            document.setFilename(fileName);
            repository.save(document);
        }
    }

    @Override
    @Transactional
    public void onlineEdit(HttpServletRequest request) {
        String id = request.getParameter("id");
        String uploadPath = request.getParameter("path");
        DtoDocument document = repository.findOne(id);
        File originalFile = new File(filePathConfig.getFilePath() + document.getPath());
        try {
            FileUtils.deleteQuietly(originalFile);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        MultipartFile multipartFile = ((MultipartHttpServletRequest) request).getFiles("files").get(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String fileName = multipartFile.getOriginalFilename();
        String physicalName = dateFormat.format(new Date()) + "_" + fileName;
        String newPath = StringUtils.isNotNullAndEmpty(uploadPath) ? "/" + uploadPath : "";
        //创建文件目录
        String checkPath = filePathConfig.getFilePath() + newPath;
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        document.setPhysicalName(physicalName);
        document.setPath(newPath + "/" + physicalName);
        File file = new File(new File(checkPath).getAbsoluteFile() + File.separator + physicalName);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        try {
            multipartFile.transferTo(file);
        } catch (IOException e) {
            throw new BaseException(e.getMessage());
        }
        repository.save(document);

    }

    @Override
    public byte[] convertFile2Blob(String path) {
        File file = new File(filePathConfig.getFilePath() + path);
        FileInputStream fis = null;
        ByteArrayOutputStream bos = null;
        byte[] bytes = null;
        try {
            fis = new FileInputStream(file);
            bos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];

            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bytes = bos.toByteArray();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (Exception e) {
                    throw new BaseException(e);
                }
            }
            if (bos != null) {
                try {
                    bos.close();
                } catch (Exception e) {
                    throw new BaseException(e);
                }
            }
        }
        return bytes;
    }

    @Override
    public String getAllowFileSuffix() {
        return filePathConfig.getFileSuffix();
    }

    @Override
    public void batchZipDownload(List<String> documentIds, HttpServletResponse response) {
        List<DtoDocument> documents = repository.findAll(documentIds);
        String outputPath = filePathConfig.getOutputPath() + "/" + "compressed.zip";
        FileOutputStream fos = null;
        ZipOutputStream zos = null;
        try {
            fos = new FileOutputStream(outputPath);
            zos = new ZipOutputStream(fos);
            for (DtoDocument document : documents) {
                FileInputStream fis = new FileInputStream(filePathConfig.getFilePath() + document.getPath());
                zos.putNextEntry(new ZipEntry(document.getFilename()));
                byte[] buffer = new byte[1024];
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
                fis.close();
                //更新下载次数
                document.setDownloadTimes(document.getDownloadTimes() + 1);
            }
            zos.close();
            fileDownload(outputPath, "compressed.zip", response);
            repository.save(documents);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("下载压缩包出错！");
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 删除工具方法
     *
     * @param file
     */
    private void deleteDirectory(File file) {
        if (!file.exists()) {
            return;
        }
        if (file.isDirectory()) {
            File[] items = file.listFiles();
            if (items == null || items.length == 0) {
                file.delete();
            } else {
                for (int i = 0; i < items.length; i++) {
                    deleteDirectory(items[i]);
                }
            }
        } else {
            file.delete();
        }
    }

    //#endregion

    private void toZip(String srcDir, OutputStream out, boolean KeepDirStructure)
            throws RuntimeException {
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            File sourceFile = new File(srcDir);
            compress(sourceFile, zos, sourceFile.getName(), KeepDirStructure);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 压缩方法
     * 可选是否保持目录结构，如果不保持则所有子路径中的文件会压缩到同一个文件夹中，
     * 要注意，如果子路径中的文件名相同，则会进行覆盖操作。
     *
     * @param sourceFile       压缩文件路径
     * @param zipOutputStream  zip输出流
     * @param zipFileName      压缩后的zip文件名
     * @param KeepDirStructure 是否保持目录结构
     * @throws Exception 抛出异常
     */
    private void compress(File sourceFile, ZipOutputStream zipOutputStream, String zipFileName,
                          boolean KeepDirStructure) throws Exception {
        byte[] buf = new byte[1024 * 2];
        //如果需要压缩的文件不是目录
        if (sourceFile.isFile()) {
            zipOutputStream.putNextEntry(new ZipEntry(zipFileName));
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zipOutputStream.write(buf, 0, len);
            }
            zipOutputStream.closeEntry();
            in.close();
        }
        //如果需要压缩的文件是目录
        else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 需要保留原来的文件结构时,需要对空文件夹进行处理
                if (KeepDirStructure) {
                    // 空文件夹的处理
                    zipOutputStream.putNextEntry(new ZipEntry(zipFileName + "/"));
                    // 没有文件，不需要文件的copy
                    zipOutputStream.closeEntry();
                }
            } else {
                for (File file : listFiles) {
                    // 判断是否需要保留原来的文件结构
                    if (KeepDirStructure) {
                        // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                        // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                        compress(file, zipOutputStream, zipFileName + "/" + file.getName(), KeepDirStructure);
                    } else {
                        compress(file, zipOutputStream, file.getName(), KeepDirStructure);
                    }
                }
            }
        }
    }

    /**
     * 判断是否为允许的上传文件类型,true表示允许
     *
     * @param fileType 文件类型
     */
    private Boolean checkFile(String fileType, List<String> allowedSuffixList) {
        //设置允许上传文件类型
        String suffixList = StringUtil.isEmpty(allowedSuffixList) ? filePathConfig.getFileSuffix() : String.join(",", allowedSuffixList);

        if (StringUtils.isNotNullAndEmpty(suffixList)) {
            // 获取文件后缀
            String suffixType = fileType.substring(fileType.lastIndexOf(".")
                    + 1, fileType.length());
            //所有的类型
            String[] suffixAll = suffixList.split(",");
            //类型匹配
            for (String suffix : suffixAll) {
                //如果有一个匹配，那就成功
                if (suffix.equalsIgnoreCase(suffixType.trim())) {
                    return true;
                }
            }
            return false;
        }
        //不配置默认允许
        return true;
    }

    /**
     * 上传文件时校验文件大小
     *
     * @param files 文件集合
     */
    private void validateFileSize(List<MultipartFile> files) {
        long totalFileSize = 0L;
        for (MultipartFile file : files) {
            totalFileSize += file.getSize();
            if (file.getSize() > filePathConfig.getSingleFileSize() * 1024 * 1024) { //配置的文件限制大小单位是MB
                throw new BaseException("单个文件大小不能超过" + filePathConfig.getSingleFileSize() + "MB");
            }
        }
        if (totalFileSize > filePathConfig.getTotalFileSize() * 1024 * 1024) { //配置的文件限制大小单位是MB
            throw new BaseException("总上传文件大小不能超过" + filePathConfig.getTotalFileSize() + "MB");
        }
    }

    /**
     * 填充文件是否锁字段
     *
     * @param dataList
     */
    private void fillingIsLock(List<DtoDocument> dataList) {
        for (DtoDocument document : dataList) {
            if (!document.getPath().endsWith("xls") && !document.getPath().endsWith("xlsx")) {
                continue;
            }
            String fullPath = filePathConfig.getFilePath() + document.getPath();
            try {
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(new Workbook(fullPath));
                for (int i = 0; i < designer.getWorkbook().getWorksheets().getCount(); i++) {
                    Worksheet u = designer.getWorkbook().getWorksheets().get(i);
                    //当前excel是否锁定
                    if (u.isProtected()) {
                        document.setIsLock(1);
                    } else {
                        document.setIsLock(0);
                    }
                }
            } catch (Exception e) {
                continue;
            }
        }
    }

    /**
     * 根据列索引转换成单元格
     *
     * @param columnIndex 列索引
     * @return 单元格值
     */
    private String excelColIndexToStr(int columnIndex) {
        if (columnIndex <= 0) {
            return null;
        }
        String columnStr = "";
        columnIndex--;
        do {
            if (columnStr.length() > 0) {
                columnIndex--;
            }
            columnStr = ((char) (columnIndex % 26 + (int) 'A')) + columnStr;
            columnIndex = (int) ((columnIndex - columnIndex % 26) / 26);
        } while (columnIndex > 0);
        return columnStr;
    }

}