package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.ConsumableDetailRepository;
import com.sinoyd.base.repository.lims.ConsumableRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消耗品详单接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Service
public class ConsumableDetailServiceImpl extends
        BaseJpaPhysicalDeleteServiceImpl<DtoConsumableDetail, String, ConsumableDetailRepository> implements ConsumableDetailService {

    @Autowired
    private EnterpriseRepository enterpriseRepository;

    @Autowired
    protected ConsumableRepository consumableRepository;

    @Autowired
    private RedisTemplate redisTemplate;

    /***
     * 分页查询
     *
     * @param page 分页条件
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoConsumableDetail> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoConsumableDetail x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        super.findByPage(page, criteria);
        List<DtoConsumableDetail> list = page.getData();
        List<String> detailIds = new ArrayList<>();
        for (DtoConsumableDetail d : list) {
            if ( StringUtil.isNotNull(d.getParentId()) && !d.getParentId().equals(UUIDHelper.GUID_EMPTY)
                    && !detailIds.contains(d.getParentId())) {
                detailIds.add(d.getParentId());
            }
        }

        List<DtoConsumable> consumableList = consumableRepository.findByIds(detailIds);
        if ( StringUtil.isNotEmpty(consumableList)) {
            for (DtoConsumableDetail d : list) {
                List<DtoConsumable> consumableByIdList = consumableList.stream().filter(p -> p.getId().equals(d.getParentId())).collect(Collectors.toList());
                if ( StringUtil.isNotEmpty(consumableByIdList)) {
                    DtoConsumable consumable = consumableByIdList.get(0);
                    d.setConsumableName(consumable.getConsumableName());
                    d.setSpecification(consumable.getSpecification());
                    d.setUnit(consumable.getUnit());
                    d.setCodeInStation(consumable.getCodeInStation());
                    d.setConsumableCode(consumable.getConsumableCode());
                }
            }
        }
        page.setData(list);
    }

    @Transactional
    @Override
    public DtoConsumableDetail save(DtoConsumableDetail entity) {

        entity = addSupplier(entity);

        if (!StringUtils.isNotNullAndEmpty(entity.getManufacturerName())) {
            entity.setManufacturerName("");
        }
        DtoConsumableDetail item = super.save(entity);
        item.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        item.setCreatorName(PrincipalContextUser.getPrincipal().getUserName());
        return item;
    }

    @Override
    @Transactional
    public DtoConsumableDetail addSupplier(DtoConsumableDetail entity) {
        DtoEnterprise enterprise;

        //判断是否新增供应商并且绑定id
        if (StringUtils.isNotNullAndEmpty(entity.getSupplierName())) {
            enterprise = enterpriseRepository.getByName(entity.getSupplierName(),
                    EnumBase.EnumEnterpriseType.供应商.getValue()).stream().findFirst().orElse(null);
            if (StringUtil.isNull(enterprise)) {
                enterprise = new DtoEnterprise();
                enterprise.setName(entity.getSupplierName());
                enterprise.setType(EnumBase.EnumEnterpriseType.供应商.getValue());
                enterprise.setOrderNum(0);
                enterpriseRepository.save(enterprise);
            }
            entity.setSupplierId(enterprise.getId());
        } else {
            entity.setSupplierName("");
            entity.setSupplierId(UUIDHelper.GUID_EMPTY);
        }
        return entity;
    }

    @Transactional
    @Override
    public DtoConsumableDetail saveAndChangeConsumable(DtoConsumableDetail entity) {
        DtoConsumableDetail dto = save(entity);

        //修改消耗品库存
        BigDecimal sum = modifyInventory(entity.getParentId());

        Map<String, Object> map = JsonIterator.deserialize(JsonStream.serialize(dto), Map.class);
        map.put("storage", sum);
        //利用通知的方式，告知相关的关联表，入库记录修改了
        //保证数据通知的时候，只存储1次入库记录
        map.put("newId", UUIDHelper.NewID());
        redisTemplate.convertAndSend(EnumBase.EnumBASRedisChannel.BAS_ConsumableDetail_Create.name(), JsonStream.serialize(map));
        return dto;
    }

    @Transactional
    @Override
    public DtoConsumableDetail update(DtoConsumableDetail entity) {

        entity = addSupplier(entity);

        if (!StringUtils.isNotNullAndEmpty(entity.getManufacturerName())) {
            entity.setManufacturerName("");
        }
        DtoConsumableDetail dto = super.update(entity);

        //修改消耗品库存
        modifyInventory(entity.getParentId());

        return dto;
    }

    @Transactional
    @Override
    public void delete(String id) {
        DtoConsumableDetail entity = repository.findOne(id);
        super.delete(entity);
        //修改消耗品库存
        modifyInventory(entity.getParentId());

    }

    /*** 修改消耗品库存
     *
     * @param parentId 消耗品Id
     */
    private BigDecimal modifyInventory(String parentId) {
        DtoConsumable consumable = consumableRepository.findOne(parentId);
        List<DtoConsumableDetail> list = repository.findByParentId(parentId);
        BigDecimal sum = BigDecimal.ZERO;
        if (list.size() > 0) {
            sum = list.stream().map(DtoConsumableDetail::getStorage).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumable.setInventory(sum);
        }
        consumableRepository.save(consumable);
        return sum;
    }


    /***
     * 获取生产厂商
     * @param isStandard 是否标准物质
     * @return
     */
    @Override
    public List<DtoConsumableDetail> getManufacturerList(Boolean isStandard) {
        List<DtoConsumableDetail> list = new ArrayList<>();
        List<String> ids = consumableRepository.findByIsStandard(isStandard);
        if (ids.size() > 0) {
            List<String> names = repository.findManufacturerName(ids);

            for (String name : names) {
                DtoConsumableDetail item = new DtoConsumableDetail();
                item.setManufacturerName(name);
                list.add(item);
            }
            list = list.stream().sorted(Comparator.comparing(DtoConsumableDetail::getManufacturerName).reversed()).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<DtoConsumableDetail> findByParentIds(List<String> consumableIds) {
        return  repository.findByParentIds(consumableIds);
    }
}