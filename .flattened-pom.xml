<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>1.5.15.RELEASE</version>
  </parent>
  <groupId>com.sinoyd.lims.od</groupId>
  <artifactId>lims-od</artifactId>
  <version>ent-5.4.82-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>lims-od</name>
  <description>LIMS 嗅辨模块</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>lims-od-public</module>
    <module>lims-od-arch</module>
    <module>lims-od-impl</module>
  </modules>
  <properties>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <od.version>ent-5.4.82</od.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <pro.version>ent-5.4.82</pro.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.sinoyd.lims.od</groupId>
        <artifactId>lims-od-public</artifactId>
        <version>${od.version}-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.sinoyd.lims.od</groupId>
        <artifactId>lims-od-arch</artifactId>
        <version>${od.version}-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.sinoyd.lims.od</groupId>
        <artifactId>lims-od-impl</artifactId>
        <version>${od.version}-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.sinoyd.lims.pro</groupId>
        <artifactId>lims-pro-impl</artifactId>
        <version>${pro.version}-SNAPSHOT</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
              <pomElements>
                <parent>expand</parent>
                <distributionManagement>remove</distributionManagement>
                <repositories>remove</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
    </plugins>
  </build>
</project>
