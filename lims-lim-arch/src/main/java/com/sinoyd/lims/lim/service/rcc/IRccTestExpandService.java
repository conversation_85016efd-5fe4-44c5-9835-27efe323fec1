package com.sinoyd.lims.lim.service.rcc;

import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目拓展资源中心服务接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
public interface IRccTestExpandService {

    /**
     * 选择同步（根据测试项目id进行同步）
     *
     * @param rccTestIds 配置中心测试项目id
     */
    void sync(Collection<String> rccTestIds);


    /**
     * 根据测试项目id查询
     *
     * @param testIds 测试项目id集合
     * @return 测试项目扩展信息
     */
    List<DtoTestExpand> findRccByTestIds(Collection<String> testIds);
}
