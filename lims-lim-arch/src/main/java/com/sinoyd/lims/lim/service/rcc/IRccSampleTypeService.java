package com.sinoyd.lims.lim.service.rcc;

import com.sinoyd.base.dto.rcc.DtoSampleType;

import java.util.Collection;
import java.util.List;

/**
 * 检测类型资源中心服务接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/19
 */
public interface IRccSampleTypeService {

    /**
     * 查询Rcc全部检测类型
     *
     * @return 检测类型集合
     */
    List<DtoSampleType> findRccAll();

    /**
     * 同步所有检测类型
     */
    void sync();

    /**
     * 选择同步检测类型
     *
     * @param rccSampleTypeIds 配置中心检测类型id
     */
    void sync(Collection<String> rccSampleTypeIds);
}
