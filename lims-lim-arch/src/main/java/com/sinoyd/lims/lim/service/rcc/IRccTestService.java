package com.sinoyd.lims.lim.service.rcc;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoTest;

import java.util.Collection;
import java.util.List;

/**
 * 资源中心测试项目服务接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
public interface IRccTestService {

    /**
     * 从RCC中分页查询测试项目
     *
     * @param baseCriteria 查询条件
     * @return 结果
     */
    RestResponse<List<DtoTest>> findByPageFromRcc(PageBean<DtoTest> pb, BaseCriteria baseCriteria);

    /**
     * 根据测试项目id从rcc中查询测试项目
     *
     * @param testIds 测试项目id集合
     * @return 测试项目集合
     */
    List<DtoTest> findRccAllByIds(Collection<String> testIds);

    /**
     * 查询Rcc全部测试项目
     *
     * @return 测试项目集合
     */
    List<DtoTest> findRccAll();

    /**
     * 同步所有测试项目
     */
    void sync();

    /**
     * 同步本站的所有测试项目
     */
    void syncStation();

    /**
     * 选择同步测试项目
     *
     * @param rccTestIds 资源中心测试项目id
     */
    void sync(Collection<String> rccTestIds);


    /**
     * 作废测试项目
     *
     * @param rccTestIds 资源中心测试项目id
     */
    void abolish(Collection<String> rccTestIds);


    /**
     * 启用作废的测试项目
     *
     * @param rccTestIds 资源中心测试项目id
     */
    void enable(Collection<String> rccTestIds);
}
