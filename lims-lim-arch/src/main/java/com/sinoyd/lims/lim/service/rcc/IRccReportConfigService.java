package com.sinoyd.lims.lim.service.rcc;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;

import java.util.Collection;
import java.util.List;

/**
 * 资源中心报表配置服务接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
public interface IRccReportConfigService {

    /**
     * 从RCC中分页查询报表配置
     *
     * @param baseCriteria 查询条件
     * @return 结果
     */
    RestResponse<List<DtoReportConfig>> findByPageFromRcc(PageBean<DtoReportConfig> pb, BaseCriteria baseCriteria);

    /**
     * 根据报表配置id从rcc中查询
     *
     * @param ids 报表配置id集合
     * @return 报表配置集合
     */
    List<DtoReportConfig> findRccAllByIds(Collection<String> ids);

    /**
     * 查询Rcc全部报表配置
     *
     * @return 报表配置集合
     */
    List<DtoReportConfig> findRccAll();

    /**
     * 根据业务id集合查询rcc 文档信息
     *
     * @param folderIds 业务id集合
     * @return 文档实体集合
     */
    List<DtoDocument> findRccDocument(Collection<String> folderIds);

    /**
     * 从RCC同步全部报表配置到LIMS
     */
    void sync();

    /**
     * 从RCC同步报表配置到LIMS
     *
     * @param reportConfigIds 报表配置id集合
     */
    void sync(Collection<String> reportConfigIds);
}
