package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoOcrDataContainer;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;

import java.util.function.Consumer;

/**
 * ocr对象服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigService extends IBaseJpaService<DtoOcrConfig, String> {
    /**
     * 获取附件上传路径
     *
     * @param id 主键
     * @return DtoOcrConfig
     */
    DtoOcrConfig findAttachPath(String id);

    /**
     * ocr 识别接口
     *
     * @param ocrConfigRecord 识别参数
     * @return 识别结果
     */
    DtoOcrDataContainer ocrRecognize(DtoOcrConfigRecord ocrConfigRecord);

    void callStreamApi(DtoOcrConfigRecord ocrConfigRecord, Consumer<String> onData);
}
