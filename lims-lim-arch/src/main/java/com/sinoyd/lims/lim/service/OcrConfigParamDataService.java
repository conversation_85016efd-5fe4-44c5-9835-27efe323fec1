package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;

import java.util.List;

/**
 * ocr对象参数服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigParamDataService extends IBaseJpaService<DtoOcrConfigParamData, String> {
}
