package com.sinoyd.lims.lim.service;


import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;

import java.util.List;
import java.util.Map;

/**
 * ocr对象参数数据服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigParamService extends IBaseJpaService<DtoOcrConfigParam, String> {
    /**
     * 参数类型下拉数据源
     * @return 数据
     */
    List<Map<String, Object>> getParamTypeSelectList();
}
