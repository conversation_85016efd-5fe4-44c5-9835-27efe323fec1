package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoCurveTemp;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * 曲线管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/18
 * @since V100R001
 */
@Validated
public interface CurveService extends IBaseJpaService<DtoCurve, String> {

    /**
     * 根据测试项目找到相应的曲线信息
     *
     * @param testIds 测试项目ids
     * @return 返回测试项目曲线
     */
    List<DtoCurve> findByTestIds(List<String> testIds);

    /**
     * 获取标线信息
     *
     * @param id 标准曲线id
     * @return 标线信息
     */
    DtoCurveTemp findDetail(String id);

    /**
     * 获取标线信息
     *
     * @param id 标准曲线id
     * @return 标线信息
     */
    List<DtoCurveTemp> findList(String id);

    /**
     * 新增标线
     *
     * @param temp        标准曲线信息
     * @param isCalculate 是否计算
     * @return 曲线
     */
    DtoCurveTemp save(DtoCurveTemp temp, Boolean isCalculate);

    /**
     * 批量保存标线
     *
     * @param tempList 标准曲线信息
     * @return 曲线
     */
    @Validated
    List<DtoCurveTemp> saveList(@Valid List<DtoCurveTemp> tempList);

    /**
     * 修改标线
     *
     * @param temp        标准曲线信息
     * @param isCalculate 是否计算
     * @return 曲线
     */
    DtoCurveTemp update(DtoCurveTemp temp, Boolean isCalculate);

    /**
     * 修改标线
     *
     * @param tempList        标准曲线信息
     * @return 曲线
     */
    List<DtoCurveTemp> updateList(List<DtoCurveTemp> tempList);

    /**
     * 计算标线
     *
     * @param temp 标准曲线信息
     * @return 曲线
     */
    DtoCurveTemp calculate(DtoCurveTemp temp);

    /**
     * 复制曲线
     *
     * @param id 需要复制的曲线id
     * @return 曲线
     */
    DtoCurveTemp copy(String id);

    /**
     * 复制曲线
     *
     * @param id 需要复制的曲线id
     * @return 曲线
     */
    List<DtoCurveTemp> copyList(String id);

    /**
     * 获取上次填写的最新曲线信息
     *
     * @param testId 测试项目id
     * @return String
     */
    DtoCurve getLastNewCurveInfo(String testId);
}