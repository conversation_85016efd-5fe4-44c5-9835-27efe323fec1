package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.od.dto.DtoTask;

import java.util.List;
import java.util.Map;

/**
 * 嗅辨任务操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
public interface TaskService extends IBaseJpaService<DtoTask, String> {
    /**
     * 获取任务类型枚举
     *
     * @return 枚举数据
     */
    List<Map<String, Object>> findTaskType();

    /**
     * 自动创建任务
     *
     * @param source 数据源
     */
    void autoCreateTask(Map<String, Object> source);


    /**
     * 根据检测单号删除
     * @param workSheetCode  检测单号
     */
    void deleteByWork(String workSheetCode);
}
