#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes for ChunkPool::allocate
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:272), pid=8644, tid=0x0000000000006ba4
#
# JRE version: OpenJDK Runtime Environment (8.0_462-b08) (build 1.8.0_462-b08)
# Java VM: OpenJDK 64-Bit Server VM (25.462-b08 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x000001e1d8621000):  VMThread [stack: 0x00000034e9400000,0x00000034e9500000] [id=27556]

Stack: [0x00000034e9400000,0x00000034e9500000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)

VM_Operation (0x00000034eadfeef0): ParallelGCFailedAllocation, mode: safepoint, requested by thread 0x000001e1885a9800


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x000001e18d3d6000 JavaThread "commons-pool-evictor-thread" [_thread_blocked, id=8496, stack(0x00000034ec100000,0x00000034ec200000)]
  0x000001e18bd76000 JavaThread "container-0" [_thread_blocked, id=17232, stack(0x00000034ebf00000,0x00000034ec000000)]
  0x000001e18bd74000 JavaThread "ContainerBackgroundProcessor[StandardEngine[Tomcat]]" daemon [_thread_blocked, id=19248, stack(0x00000034ebe00000,0x00000034ebf00000)]
  0x000001e18a2dc800 JavaThread "Abandoned connection cleanup thread" daemon [_thread_blocked, id=23480, stack(0x00000034eb900000,0x00000034eba00000)]
  0x000001e18a2dd800 JavaThread "Tomcat JDBC Pool Cleaner[414493378:1757041103555]" daemon [_thread_blocked, id=2500, stack(0x00000034eb800000,0x00000034eb900000)]
  0x000001e1885a5800 JavaThread "RMI TCP Connection(6)-192.168.30.34" daemon [_thread_in_native, id=27540, stack(0x00000034eb700000,0x00000034eb800000)]
  0x000001e1885a9800 JavaThread "RMI TCP Connection(idle)" daemon [_thread_blocked, id=22364, stack(0x00000034ead00000,0x00000034eae00000)]
  0x000001e181968800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=13012, stack(0x00000034eb600000,0x00000034eb700000)]
  0x000001e18195f800 JavaThread "RMI TCP Connection(idle)" daemon [_thread_blocked, id=14900, stack(0x00000034eb500000,0x00000034eb600000)]
  0x000001e181960800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=9332, stack(0x00000034eb300000,0x00000034eb400000)]
  0x000001e1802f9000 JavaThread "Service Thread" daemon [_thread_blocked, id=28076, stack(0x00000034eb200000,0x00000034eb300000)]
  0x000001e1802fb800 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=19872, stack(0x00000034eb100000,0x00000034eb200000)]
  0x000001e1802fa800 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=21728, stack(0x00000034eb000000,0x00000034eb100000)]
  0x000001e180300000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=27976, stack(0x00000034eaf00000,0x00000034eb000000)]
  0x000001e1802fa000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=13048, stack(0x00000034eae00000,0x00000034eaf00000)]
  0x000001e1802fd000 JavaThread "IntelliJ Suspend Helper" daemon [_thread_blocked, id=27996, stack(0x00000034eac00000,0x00000034ead00000)]
  0x000001e1804a4800 JavaThread "rebel-messaging-executor-55" daemon [_thread_blocked, id=9040, stack(0x00000034eab00000,0x00000034eac00000)]
  0x000001e1804a4000 JavaThread "rebel-IDENotificationsImpl-PostCycle" daemon [_thread_blocked, id=24012, stack(0x00000034ea500000,0x00000034ea600000)]
  0x000001e18049d000 JavaThread "rebel-build-info" daemon [_thread_in_native, id=23968, stack(0x00000034ea300000,0x00000034ea400000)]
  0x000001e1804a1800 JavaThread "rebel-change-detector-thread" daemon [_thread_blocked, id=14188, stack(0x00000034ea200000,0x00000034ea300000)]
  0x000001e18049e800 JavaThread "rebel-debugger-thread" daemon [_thread_blocked, id=18368, stack(0x00000034ea100000,0x00000034ea200000)]
  0x000001e180499800 JavaThread "rebel-debugger-attach-notifier" daemon [_thread_blocked, id=22752, stack(0x00000034ea000000,0x00000034ea100000)]
  0x000001e18049c000 JavaThread "rebel-heartbeat-thread" daemon [_thread_blocked, id=4284, stack(0x00000034e9f00000,0x00000034ea000000)]
  0x000001e1804a1000 JavaThread "rebel-redeploy-thread" daemon [_thread_blocked, id=16688, stack(0x00000034e9e00000,0x00000034e9f00000)]
  0x000001e18049b800 JavaThread "rebel-leaseManager-1" daemon [_thread_blocked, id=28628, stack(0x00000034e9d00000,0x00000034e9e00000)]
  0x000001e180498000 JavaThread "rebel-weak-reaper" daemon [_thread_blocked, id=23888, stack(0x00000034e9c00000,0x00000034e9d00000)]
  0x000001e18049d800 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_in_native, id=24844, stack(0x00000034eaa00000,0x00000034eab00000)]
  0x000001e18049f000 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_blocked, id=17928, stack(0x00000034ea900000,0x00000034eaa00000)]
  0x000001e180499000 JavaThread "rebel-fsnotify-ShutdownOnTermination" daemon [_thread_in_native, id=9448, stack(0x00000034ea800000,0x00000034ea900000)]
  0x000001e1802fe800 JavaThread "rebel-logger" daemon [_thread_blocked, id=23192, stack(0x00000034ea700000,0x00000034ea800000)]
  0x000001e1803cf800 JavaThread "rebel-CacheKeepAlive" daemon [_thread_blocked, id=4844, stack(0x00000034ea600000,0x00000034ea700000)]
  0x000001e1fdb02800 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=10536, stack(0x00000034e9b00000,0x00000034e9c00000)]
  0x000001e1fdb01800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=12660, stack(0x00000034e9a00000,0x00000034e9b00000)]
  0x000001e1fdae2000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=19820, stack(0x00000034e9900000,0x00000034e9a00000)]
  0x000001e1fdad8800 JavaThread "Attach Listener" daemon [_thread_blocked, id=5304, stack(0x00000034e9800000,0x00000034e9900000)]
  0x000001e1fdab9800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=8200, stack(0x00000034e9700000,0x00000034e9800000)]
  0x000001e1fc490000 JavaThread "Finalizer" daemon [_thread_blocked, id=22184, stack(0x00000034e9600000,0x00000034e9700000)]
  0x000001e1d84a7000 JavaThread "Reference Handler" daemon [_thread_blocked, id=24360, stack(0x00000034e9500000,0x00000034e9600000)]
  0x000001e1d8456800 JavaThread "main" [_thread_blocked, id=10116, stack(0x00000034e8900000,0x00000034e8a00000)]

Other Threads:
=>0x000001e1d8621000 VMThread [stack: 0x00000034e9400000,0x00000034e9500000] [id=27556]
  0x000001e182bb3000 WatcherThread [stack: 0x00000034eb400000,0x00000034eb500000] [id=27640]

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001e1d84526b0] Threads_lock - owner thread: 0x000001e1d8621000
[0x000001e1d8451db0] Heap_lock - owner thread: 0x000001e1885a9800

heap address: 0x00000005c4600000, size: 8122 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 2205184K, used 125416K [0x0000000716d00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2079744K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000795c00000)
  from space 125440K, 99% used [0x0000000795c00000,0x000000079d67a1a8,0x000000079d680000)
  to   space 214016K, 0% used [0x00000007b2f00000,0x00000007b2f00000,0x00000007c0000000)
 ParOldGen       total 573440K, used 458136K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 79% used [0x00000005c4600000,0x00000005e0566130,0x00000005e7600000)
 Metaspace       used 124833K, capacity 127462K, committed 128000K, reserved 1163264K
  class space    used 12649K, capacity 13207K, committed 13312K, reserved 1048576K

Card table byte_map: [0x000001e1e91e0000,0x000001e1ea1c0000] byte_map_base: 0x000001e1e63bd000

Marking Bits: (ParMarkBitMap*) 0x00000000550a48d0
 Begin Bits: [0x000001e1eac60000, 0x000001e1f2b48000)
 End Bits:   [0x000001e1f2b48000, 0x000001e1faa30000)

Polling page: 0x000001e1d83a0000

CodeCache: size=245760Kb used=27005Kb max_used=27005Kb free=218754Kb
 bounds [0x000001e1d9e20000, 0x000001e1db890000, 0x000001e1e8e20000]
 total_blobs=15166 nmethods=14525 adapters=560
 compilation: enabled

Compilation events (10 events):
Event: 57.017 Thread 0x000001e1802fb800 14771   !   1       sun.reflect.GeneratedSerializationConstructorAccessor6::newInstance (49 bytes)
Event: 57.017 Thread 0x000001e1802fb800 nmethod 14771 0x000001e1db87ecd0 code [0x000001e1db87ee60, 0x000001e1db87f170]
Event: 57.018 Thread 0x000001e1802fb800 14772       1       java.io.ObjectInputStream::readHandle (163 bytes)
Event: 57.019 Thread 0x000001e1802fb800 nmethod 14772 0x000001e1db87f3d0 code [0x000001e1db87f660, 0x000001e1db880190]
Event: 57.020 Thread 0x000001e1802fb800 14773       1       java.io.ObjectInputStream$HandleTable::size (5 bytes)
Event: 57.020 Thread 0x000001e1802fb800 nmethod 14773 0x000001e1db880e10 code [0x000001e1db880f60, 0x000001e1db881078]
Event: 57.021 Thread 0x000001e1802fb800 14774       1       java.io.ObjectInputStream$HandleTable::lookupObject (28 bytes)
Event: 57.021 Thread 0x000001e1802fb800 nmethod 14774 0x000001e1db8810d0 code [0x000001e1db881240, 0x000001e1db8813d8]
Event: 57.030 Thread 0x000001e1802fb800 14775       1       org.zeroturnaround.javarebel.watcher.DirWatcher::_jr$ig$currentRevision (8 bytes)
Event: 57.030 Thread 0x000001e1802fb800 nmethod 14775 0x000001e1db881550 code [0x000001e1db8816a0, 0x000001e1db8817d8]

GC Heap History (10 events):
Event: 31.427 GC heap after
Heap after GC invocations=17 (full 4):
 PSYoungGen      total 1563648K, used 0K [0x0000000716d00000, 0x000000078bd80000, 0x00000007c0000000)
  eden space 1493504K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000771f80000)
  from space 70144K, 0% used [0x0000000771f80000,0x0000000771f80000,0x0000000776400000)
  to   space 67072K, 0% used [0x0000000787c00000,0x0000000787c00000,0x000000078bd80000)
 ParOldGen       total 573440K, used 148689K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 25% used [0x00000005c4600000,0x00000005cd734698,0x00000005e7600000)
 Metaspace       used 95926K, capacity 98430K, committed 98560K, reserved 1136640K
  class space    used 10379K, capacity 10931K, committed 11008K, reserved 1048576K
}
Event: 36.020 GC heap before
{Heap before GC invocations=18 (full 4):
 PSYoungGen      total 1563648K, used 1493504K [0x0000000716d00000, 0x000000078bd80000, 0x00000007c0000000)
  eden space 1493504K, 100% used [0x0000000716d00000,0x0000000771f80000,0x0000000771f80000)
  from space 70144K, 0% used [0x0000000771f80000,0x0000000771f80000,0x0000000776400000)
  to   space 67072K, 0% used [0x0000000787c00000,0x0000000787c00000,0x000000078bd80000)
 ParOldGen       total 573440K, used 148689K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 25% used [0x00000005c4600000,0x00000005cd734698,0x00000005e7600000)
 Metaspace       used 108798K, capacity 111436K, committed 111616K, reserved 1148928K
  class space    used 11487K, capacity 12016K, committed 12032K, reserved 1048576K
Event: 36.063 GC heap after
Heap after GC invocations=18 (full 4):
 PSYoungGen      total 1823232K, used 67063K [0x0000000716d00000, 0x000000078bf00000, 0x00000007c0000000)
  eden space 1756160K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000782000000)
  from space 67072K, 99% used [0x0000000787c00000,0x000000078bd7de08,0x000000078bd80000)
  to   space 81408K, 0% used [0x0000000782000000,0x0000000782000000,0x0000000786f80000)
 ParOldGen       total 573440K, used 158638K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 27% used [0x00000005c4600000,0x00000005ce0eba48,0x00000005e7600000)
 Metaspace       used 108798K, capacity 111436K, committed 111616K, reserved 1148928K
  class space    used 11487K, capacity 12016K, committed 12032K, reserved 1048576K
}
Event: 39.998 GC heap before
{Heap before GC invocations=19 (full 4):
 PSYoungGen      total 1823232K, used 1823223K [0x0000000716d00000, 0x000000078bf00000, 0x00000007c0000000)
  eden space 1756160K, 100% used [0x0000000716d00000,0x0000000782000000,0x0000000782000000)
  from space 67072K, 99% used [0x0000000787c00000,0x000000078bd7de08,0x000000078bd80000)
  to   space 81408K, 0% used [0x0000000782000000,0x0000000782000000,0x0000000786f80000)
 ParOldGen       total 573440K, used 158638K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 27% used [0x00000005c4600000,0x00000005ce0eba48,0x00000005e7600000)
 Metaspace       used 118687K, capacity 121330K, committed 121728K, reserved 1159168K
  class space    used 12066K, capacity 12625K, committed 12672K, reserved 1048576K
Event: 40.061 GC heap after
Heap after GC invocations=19 (full 4):
 PSYoungGen      total 1837568K, used 81385K [0x0000000716d00000, 0x00000007a4000000, 0x00000007c0000000)
  eden space 1756160K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000782000000)
  from space 81408K, 99% used [0x0000000782000000,0x0000000786f7a670,0x0000000786f80000)
  to   space 108032K, 0% used [0x000000079d680000,0x000000079d680000,0x00000007a4000000)
 ParOldGen       total 573440K, used 195198K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 34% used [0x00000005c4600000,0x00000005d049f848,0x00000005e7600000)
 Metaspace       used 118687K, capacity 121330K, committed 121728K, reserved 1159168K
  class space    used 12066K, capacity 12625K, committed 12672K, reserved 1048576K
}
Event: 47.600 GC heap before
{Heap before GC invocations=20 (full 4):
 PSYoungGen      total 1837568K, used 1837545K [0x0000000716d00000, 0x00000007a4000000, 0x00000007c0000000)
  eden space 1756160K, 100% used [0x0000000716d00000,0x0000000782000000,0x0000000782000000)
  from space 81408K, 99% used [0x0000000782000000,0x0000000786f7a670,0x0000000786f80000)
  to   space 108032K, 0% used [0x000000079d680000,0x000000079d680000,0x00000007a4000000)
 ParOldGen       total 573440K, used 195198K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 34% used [0x00000005c4600000,0x00000005d049f848,0x00000005e7600000)
 Metaspace       used 124025K, capacity 126662K, committed 127104K, reserved 1163264K
  class space    used 12570K, capacity 13111K, committed 13184K, reserved 1048576K
Event: 47.701 GC heap after
Heap after GC invocations=20 (full 4):
 PSYoungGen      total 2187776K, used 105888K [0x0000000716d00000, 0x00000007a6d80000, 0x00000007c0000000)
  eden space 2079744K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000795c00000)
  from space 108032K, 98% used [0x000000079d680000,0x00000007a3de8388,0x00000007a4000000)
  to   space 125440K, 0% used [0x0000000795c00000,0x0000000795c00000,0x000000079d680000)
 ParOldGen       total 573440K, used 276341K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 48% used [0x00000005c4600000,0x00000005d53dd550,0x00000005e7600000)
 Metaspace       used 124025K, capacity 126662K, committed 127104K, reserved 1163264K
  class space    used 12570K, capacity 13111K, committed 13184K, reserved 1048576K
}
Event: 57.559 GC heap before
{Heap before GC invocations=21 (full 4):
 PSYoungGen      total 2187776K, used 2185632K [0x0000000716d00000, 0x00000007a6d80000, 0x00000007c0000000)
  eden space 2079744K, 100% used [0x0000000716d00000,0x0000000795c00000,0x0000000795c00000)
  from space 108032K, 98% used [0x000000079d680000,0x00000007a3de8388,0x00000007a4000000)
  to   space 125440K, 0% used [0x0000000795c00000,0x0000000795c00000,0x000000079d680000)
 ParOldGen       total 573440K, used 276341K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 48% used [0x00000005c4600000,0x00000005d53dd550,0x00000005e7600000)
 Metaspace       used 124833K, capacity 127462K, committed 128000K, reserved 1163264K
  class space    used 12649K, capacity 13207K, committed 13312K, reserved 1048576K
Event: 58.079 GC heap after
Heap after GC invocations=21 (full 4):
 PSYoungGen      total 2205184K, used 125416K [0x0000000716d00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2079744K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000795c00000)
  from space 125440K, 99% used [0x0000000795c00000,0x000000079d67a1a8,0x000000079d680000)
  to   space 214016K, 0% used [0x00000007b2f00000,0x00000007b2f00000,0x00000007c0000000)
 ParOldGen       total 573440K, used 458136K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 79% used [0x00000005c4600000,0x00000005e0566130,0x00000005e7600000)
 Metaspace       used 124833K, capacity 127462K, committed 128000K, reserved 1163264K
  class space    used 12649K, capacity 13207K, committed 13312K, reserved 1048576K
}
Event: 58.079 GC heap before
{Heap before GC invocations=22 (full 5):
 PSYoungGen      total 2205184K, used 125416K [0x0000000716d00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2079744K, 0% used [0x0000000716d00000,0x0000000716d00000,0x0000000795c00000)
  from space 125440K, 99% used [0x0000000795c00000,0x000000079d67a1a8,0x000000079d680000)
  to   space 214016K, 0% used [0x00000007b2f00000,0x00000007b2f00000,0x00000007c0000000)
 ParOldGen       total 573440K, used 458136K [0x00000005c4600000, 0x00000005e7600000, 0x0000000716d00000)
  object space 573440K, 79% used [0x00000005c4600000,0x00000005e0566130,0x00000005e7600000)
 Metaspace       used 124833K, capacity 127462K, committed 128000K, reserved 1163264K
  class space    used 12649K, capacity 13207K, committed 13312K, reserved 1048576K

Deoptimization events (0 events):
No events

Classes redefined (9 events):
Event: 1.128 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.ay, count=1
Event: 1.129 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.as, count=1
Event: 1.130 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.a, count=1
Event: 1.133 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.d, count=1
Event: 1.135 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.f, count=1
Event: 1.137 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.e, count=1
Event: 1.138 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.g, count=1
Event: 1.140 Thread 0x000001e1d8621000 redefined class name=com.zeroturnaround.jrebelbase.facade.ac, count=1
Event: 5.973 Thread 0x000001e1d8621000 redefined class name=java.lang.Throwable, count=2

Internal exceptions (10 events):
Event: 56.845 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/frame/base/entity/BaseEntityCustomizer> (0x0000000790b65838) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp, 
Event: 56.846 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': java/io/SerializableCustomizer> (0x0000000790b8f2f0) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 217]
Event: 56.866 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/lims/pro/dto/DtoLogForWorkSheetBeanInfo> (0x0000000790dcbb58) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp,
Event: 56.870 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/lims/pro/dto/DtoLogForWorkSheetCustomizer> (0x0000000790e32bc8) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cp
Event: 56.874 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/frame/base/entity/BaseEntityCustomizer> (0x0000000790ea1d28) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp, 
Event: 56.875 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': java/io/SerializableCustomizer> (0x0000000790ecb7e0) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 217]
Event: 56.884 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/frame/base/entity/BaseEntityCustomizer> (0x00000007910e6568) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp, 
Event: 56.887 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': java/io/SerializableCustomizer> (0x0000000791110020) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 217]
Event: 56.908 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/lims/pro/view/VProjectCountStatisticBeanInfo> (0x00000007913695f8) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictionary
Event: 56.916 Thread 0x000001e1d8456800 Exception <a 'java/lang/ClassNotFoundException': com/sinoyd/lims/pro/view/VProjectCountStatisticCustomizer> (0x00000007913d4958) thrown at [C:\wsjdk\Corretto8Src\installers\windows\zip\corretto-build\buildRoot\hotspot\src\share\vm\classfile\systemDictiona

Events (10 events):
Event: 57.557 Thread 0x000001e1d8456800 DEOPT UNPACKING pc=0x000001e1d9e675d8 sp=0x00000034e89fa078 mode 1
Event: 57.557 loading class org/hibernate/internal/CoreMessageLogger_$logger_zh
Event: 57.557 loading class org/hibernate/internal/CoreMessageLogger_$logger_zh done
Event: 57.557 Thread 0x000001e1d8456800 DEOPT PACKING pc=0x000001e1da2b0534 sp=0x00000034e89f9650
Event: 57.557 Thread 0x000001e1d8456800 DEOPT UNPACKING pc=0x000001e1d9e675d8 sp=0x00000034e89f9420 mode 1
Event: 57.557 Thread 0x000001e1d8456800 DEOPT PACKING pc=0x000001e1db6c20a4 sp=0x00000034e89f9750
Event: 57.557 Thread 0x000001e1d8456800 DEOPT UNPACKING pc=0x000001e1d9e675d8 sp=0x00000034e89f94e0 mode 1
Event: 57.557 Thread 0x000001e1d8456800 DEOPT PACKING pc=0x000001e1db6c1ff4 sp=0x00000034e89f9850
Event: 57.557 Thread 0x000001e1d8456800 DEOPT UNPACKING pc=0x000001e1d9e675d8 sp=0x00000034e89f95e0 mode 1
Event: 57.559 Executing VM operation: ParallelGCFailedAllocation


Dynamic libraries:
0x00007ff773430000 - 0x00007ff773476000 	D:\Programe Files(BC)\Java\Jdks\bin\java.exe
0x00007fff0dd10000 - 0x00007fff0df04000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fff0bde0000 - 0x00007fff0be9d000 	C:\Windows\System32\KERNEL32.DLL
0x00007fff0b720000 - 0x00007fff0b9e7000 	C:\Windows\System32\KERNELBASE.dll
0x00007fff0c310000 - 0x00007fff0c3ba000 	C:\Windows\System32\ADVAPI32.dll
0x00007fff0db80000 - 0x00007fff0dc1e000 	C:\Windows\System32\msvcrt.dll
0x00007fff0c3d0000 - 0x00007fff0c46b000 	C:\Windows\System32\sechost.dll
0x00007fff0c4f0000 - 0x00007fff0c613000 	C:\Windows\System32\RPCRT4.dll
0x00007fff0c930000 - 0x00007fff0cad0000 	C:\Windows\System32\USER32.dll
0x00007fff0bca0000 - 0x00007fff0bcc2000 	C:\Windows\System32\win32u.dll
0x00007fff0cd20000 - 0x00007fff0cd4a000 	C:\Windows\System32\GDI32.dll
0x00007fff0b9f0000 - 0x00007fff0bafa000 	C:\Windows\System32\gdi32full.dll
0x00007fff0bb00000 - 0x00007fff0bb9d000 	C:\Windows\System32\msvcp_win.dll
0x00007fff0b620000 - 0x00007fff0b720000 	C:\Windows\System32\ucrtbase.dll
0x00007ffefb950000 - 0x00007ffefbbea000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1_none_b555e41d4684ddec\COMCTL32.dll
0x00007fff0c900000 - 0x00007fff0c930000 	C:\Windows\System32\IMM32.DLL
0x00007ffef7560000 - 0x00007ffef7575000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\vcruntime140.dll
0x00007ffee6870000 - 0x00007ffee690b000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\msvcp140.dll
0x00000000548c0000 - 0x000000005511d000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\server\jvm.dll
0x00007fff0d9a0000 - 0x00007fff0d9a8000 	C:\Windows\System32\PSAPI.DLL
0x00007fff01e50000 - 0x00007fff01e5a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fff01e20000 - 0x00007fff01e47000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffefc7f0000 - 0x00007ffefc7f9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007fff0cb80000 - 0x00007fff0cbeb000 	C:\Windows\System32\WS2_32.dll
0x00007fff09dc0000 - 0x00007fff09dd3000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffefd960000 - 0x00007ffefd970000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\verify.dll
0x00007ffeef890000 - 0x00007ffeef8bb000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\java.dll
0x00007ffef8040000 - 0x00007ffef8076000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\jdwp.dll
0x00007fff07eb0000 - 0x00007fff07eb9000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\npt.dll
0x00007ffef7cb0000 - 0x00007ffef7ce2000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\instrument.dll
0x00007ffef7c60000 - 0x00007ffef7cae000 	C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2025.2\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll
0x00007fff0d260000 - 0x00007fff0d995000 	C:\Windows\System32\SHELL32.dll
0x00007fff0cad0000 - 0x00007fff0cb7e000 	C:\Windows\System32\shcore.dll
0x00007fff0cf00000 - 0x00007fff0d253000 	C:\Windows\System32\combase.dll
0x00007ffeee330000 - 0x00007ffeee348000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\zip.dll
0x00007fff095f0000 - 0x00007fff09d7f000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007fff0aee0000 - 0x00007fff0af0b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007fff0bd80000 - 0x00007fff0bdd5000 	C:\Windows\System32\shlwapi.dll
0x00007fff0b3a0000 - 0x00007fff0b3bf000 	C:\Windows\SYSTEM32\profapi.dll
0x00007fff05300000 - 0x00007fff0530a000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\dt_socket.dll
0x00007fff0ac60000 - 0x00007fff0acca000 	C:\Windows\system32\mswsock.dll
0x00007ffeee310000 - 0x00007ffeee32c000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\net.dll
0x00007ffeee280000 - 0x00007ffeee293000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\nio.dll
0x00007ffeee2a0000 - 0x00007ffeee2c4000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\sunec.dll
0x00007fff03710000 - 0x00007fff0371d000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\management.dll
0x00007fff03700000 - 0x00007fff0370e000 	D:\Programe Files(BC)\Java\Jdks\jre\bin\sunmscapi.dll
0x00007fff0b4c0000 - 0x00007fff0b61d000 	C:\Windows\System32\CRYPT32.dll
0x00007fff0af50000 - 0x00007fff0af77000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007fff0af10000 - 0x00007fff0af4b000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007fff0ae30000 - 0x00007fff0ae48000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007fff0a580000 - 0x00007fff0a5b4000 	C:\Windows\system32\rsaenh.dll
0x00007fff0bba0000 - 0x00007fff0bbc7000 	C:\Windows\System32\bcrypt.dll
0x00007fff0b320000 - 0x00007fff0b34e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007fff0bc20000 - 0x00007fff0bc9f000 	C:\Windows\System32\bcryptprimitives.dll
0x00007fff0ae50000 - 0x00007fff0ae5c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007fff0a900000 - 0x00007fff0a93b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007fff0c3c0000 - 0x00007fff0c3c9000 	C:\Windows\System32\NSI.dll
0x00007fff052e0000 - 0x00007fff052f7000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007fff052c0000 - 0x00007fff052dd000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007fff0a950000 - 0x00007fff0aa1a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007fff01a40000 - 0x00007fff01a4a000 	C:\Windows\System32\rasadhlp.dll
0x00007fff014f0000 - 0x00007fff0156f000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffeefec0000 - 0x00007ffeefed7000 	C:\Windows\system32\napinsp.dll
0x00007ffeefea0000 - 0x00007ffeefebb000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffeefe80000 - 0x00007ffeefe95000 	C:\Windows\system32\wshbth.dll
0x00007fff071e0000 - 0x00007fff071fd000 	C:\Windows\system32\NLAapi.dll
0x00007ffeefe60000 - 0x00007ffeefe72000 	C:\Windows\System32\winrnr.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:49361,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.2\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture15927565864942492894.props -Drebel.base=C:\Users\<USER>\.jrebel -Drebel.env.ide.plugin.build=a6ee74ac1af075e4e2f236f32248f094315a63c4 -Drebel.env.ide.plugin.version=2025.3.1 -Drebel.env.ide.version=2025.2 -Drebel.env.ide.product=IU -Drebel.env.ide=intellij -Drebel.notification.url=http://localhost:59411 -agentpath:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2025.2\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.sinoyd.EnableEurekaServerApplication
java_class_path (initial): D:\Programe Files(BC)\Java\Jdks\jre\lib\charsets.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\access-bridge-64.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\cldrdata.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\dnsns.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\jaccess.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\jfxrt.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\localedata.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\nashorn.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\sunec.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\sunjce_provider.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\sunmscapi.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\sunpkcs11.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\ext\zipfs.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\jce.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\jfr.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\jfxswt.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\jsse.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\management-agent.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\resources.jar;D:\Programe Files(BC)\Java\Jdks\jre\lib\rt.jar;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-webbackend\target\classes;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-monitor\lims-monitor-impl\target\classes;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-monitor\lims-monitor-arch\target\classes;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-monitor\lims-monitor-public\target\classes;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-lim\lims-lim-impl\target\classes;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-lim\lims-lim-arch\target\classes;D:\Sinoyd\LIMS5.2\LIMS5.2_Ent\ENT_LIMS\lims-lim\lims-lim-public\target\classes;D:\Sinoyd\Maven Repository\com\squareup\okhttp3\okhttp\4.9.3\okhttp-4.9.3.jar;D:\Sinoyd\Maven Repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;D:\Sinoyd\Maven Repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.4.0\kotlin-stdlib-common-1.4.0.jar;D:\Sinoyd\Maven Repository\org\jetbrains\kotlin\kotlin-s
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=D:\Programe Files(BC)\Java\Jdks
CLASSPATH=.;D:\Programe Files(BC)\Java\Jdks\lib;D:\Programe Files(BC)\Java\Jdks\lib\tools.jar
PATH=C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Programe Files(BC)\Git\cmd;D:\Programe Files(BC)\Redis\;D:\Programe Files(BC)\Java\Jdks\bin;D:\Programe Files(BC)\Maven\apache-maven-3.8.3\bin;D:\Programe Files(BC)\Nvm\nvm\v20.19.4;D:\Programe Files(BC)\Nvm\nvm\v20.19.4\node_global;D:\Programe Files(BC)\Nvm\nvm\v20.19.4\node_cache;D:\Programe Files(BC)\Nvm\nvm;D:\Programe Files(BC)\Nvm\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Programe Files(BC)\IDEA\IntelliJ IDEA 2025.2\bin;D:\Programe Files(BC)\Nvm\nvm\v20.19.4\node_global;D:\Programe Files(BC)\Nvm\nvm;D:\Programe Files(BC)\Nvm\nodejs;
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 85 Stepping 4, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 19041 (10.0.19041.292)

CPU:total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 85 stepping 4, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, rtm, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 33267344k(8898548k free), swap 43753104k(3800k free)

vm_info: OpenJDK 64-Bit Server VM (25.462-b08) for windows-amd64 JRE (1.8.0_462-b08), built on Jul 10 2025 21:31:50 by "Administrator" with MS VC++ 15.9 (VS2017)

time: Fri Sep  5 10:58:52 2025
timezone: Intel64 Family 6 Model 85 Stepping 4, GenuineIntel
elapsed time: 58.845445 seconds (0d 0h 0m 58s)

