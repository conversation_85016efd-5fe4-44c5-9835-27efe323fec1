package com.sinoyd.lims.query.dto;

import com.sinoyd.lims.query.entity.ViewField;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoViewField实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_QUERY_ViewField") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoViewField extends ViewField {
   private static final long serialVersionUID = 1L;

 }