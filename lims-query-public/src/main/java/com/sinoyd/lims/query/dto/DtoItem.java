package com.sinoyd.lims.query.dto;

import com.sinoyd.lims.query.dto.customer.DtoDbConditionColumn;
import com.sinoyd.lims.query.entity.Item;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.List;


/**
 * DtoItem实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_QUERY_Item") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoItem extends Item {
   private static final long serialVersionUID = 1L;

   @Transient
   private List<DtoItemColumn> itemColumnList;

   @Transient
   private DtoItemCondition itemCondition;

    @Transient
    private List<DtoDbConditionColumn> dbConditionColumnList;
 }