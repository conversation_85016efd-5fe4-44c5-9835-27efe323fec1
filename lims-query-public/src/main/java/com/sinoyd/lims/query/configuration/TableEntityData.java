package com.sinoyd.lims.query.configuration;

import com.sinoyd.frame.service.ClassUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.persistence.Table;
import java.util.*;

@Component
public class TableEntityData implements CommandLineRunner {

    public static Map<String, Class<?>> TableEntityMap = new HashMap<>();

    @Autowired
    ClassUtilService classUtilService;

    @Override
    public void run(String... args) throws Exception {
        for (String pack : args) {
            Set<Class<?>> clazz = classUtilService.getClasses(pack);
            if (clazz == null) {
                break;
            }
            for (Class<?> cla : clazz) {
                boolean isTable = cla.isAnnotationPresent(Table.class);//.是否是表实体
                if (isTable) {
                    Table table = cla.getAnnotation(Table.class);
                    while (Optional.ofNullable(cla.getSuperclass()).isPresent() &&
                            !cla.getSuperclass().equals(Object.class)) {//当父类为null的时候说明到达了最上层的父类(Object类).
                        cla = cla.getSuperclass(); //得到父类,然后赋给自己
                    }
                    TableEntityMap.put(table.name().toLowerCase(Locale.ROOT), cla);
                }
            }
        }
    }
}
