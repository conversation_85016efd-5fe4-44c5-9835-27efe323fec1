package com.sinoyd.lims.query.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 现场数据录入保存的dto
 * <AUTHOR>
 * @version V1.0.0 2020/2/25
 * @since V100R001
 */
@Data
public class DtoSelectBaseItem {
    /**
     * 查询内容
     */
    private List<DtoDbConditionColumn> dbConditionColumns = new ArrayList<>();

    /**
     * 自定义查询id
     */
    private String itemId;

    /**
     * 分页信息
     */
    private DtoBasePageBean basePageBean;
}
