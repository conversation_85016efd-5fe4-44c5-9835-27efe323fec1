package com.sinoyd.lims.query.dto.customer;

import lombok.Data;

import java.util.Map;

@Data
public class DtoDbConditionColumn {

    /**
     * 界面显示
     */
    private String itemName;

    /***
     * viewfieldId
     */
    private String dbItemId;

    /***
     * dbName
     */
    private String dbName;

    /**
     * 检索的等式符号（符号，对应填的值）
     */
    private Map<String,String> symbol;

    /**
     * 显示的等式符号
     */
    private String pageSymbol;

    /**
     * 显示排序
     */
    private Integer orderNum;
}
