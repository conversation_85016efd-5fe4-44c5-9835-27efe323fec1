package com.sinoyd.lims.query.enums;

import com.sinoyd.base.enums.EnumBase;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class EnumQuery {

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSortType {
        正序(0),
        倒序(1);

        private Integer value;

        public static String EnumSortType(Integer value) {
            for (EnumQuery.EnumSortType c : EnumQuery.EnumSortType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }
}
