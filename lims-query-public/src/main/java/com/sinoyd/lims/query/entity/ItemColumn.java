package com.sinoyd.lims.query.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * ItemColumn实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ItemColumn")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ItemColumn implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ItemColumn() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 用户查询表id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("用户查询表id")
    private String itemId;

    /**
     * 视图字段表id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("视图字段表id")
    private String viewFieldId;

    /**
     * 列宽
     */
    @Column(nullable = false)
    @ColumnDefault("5")
    @ApiModelProperty("列宽")
    private Integer columnLength;

    /**
     * 排序类型，升序、降序
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("排序类型，升序、降序")
    private String sortType;

    /**
     * 排序节点
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序节点")
    private Integer sortSeq;

    /**
     * 是否显示
     */
    @Column(nullable = false)
    @ApiModelProperty("是否显示")
    private Boolean isShow;

    /**
     * 是否过滤
     */
    @Column(nullable = false)
    @ApiModelProperty("是否过滤")
    private Boolean isScreen;

    /**
     * 默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件
     * ）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）")
    private Integer defaultControl;

    /**
     * 数据源
     */
    @Column(length = 2000)
    @ApiModelProperty("数据源")
    private String dataSource;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}