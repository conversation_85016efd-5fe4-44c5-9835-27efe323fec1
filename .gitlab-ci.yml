workflow:
  rules:
    - if: $CI_COMMIT_TAG

variables:
  TARGET_NAME: "lims-webbackend"

default:
  tags:
    - dind
  artifacts:
    paths:
      - .ci_job_status
      - application.jar
    expire_in: 2 hour
    when: always


stages:
  - build
  - package
  - manual_docker

build-jar:
  stage: build
  image:
    name: registry.dev.yd/sinoyd/registry/maven:3.8.4-openjdk-8
  cache:
    paths:
      - .m2/repository
  script:
    - cp m2-settings.xml /root/.m2/settings.xml
    - mvn $MAVEN_CLI_OPTS clean package -Dmaven.test.skip=true
    - cp target/*.jar application.jar
  after_script:
    - echo -n "编译JAR包" > .ci_job_status

build-image:
  stage: package
  services:
    - name: registry.dev.yd/sinoyd/registry/docker:stable-dind
      alias: docker
      command: [ "--registry-mirror", "https://dockerproxy.dev.yd" ]
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker login --username=$DOCKER_USER_JSYUANDA -p "$DOCKER_PWD_JSYUANDA" registry.cn-shanghai.aliyuncs.com
  script:
    - docker pull $CI_REGISTRY_IMAGE:latest || true
    - docker build --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG --tag $CI_REGISTRY_IMAGE:latest .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - docker push $CI_REGISTRY_IMAGE:latest
  after_script:
    - echo -n "Docker发布" > .ci_job_status

docker-build:
  stage: manual_docker
  services:
    - name: registry.dev.yd/sinoyd/registry/docker:stable-dind
      alias: docker
      command: [ "--registry-mirror", "https://dockerproxy.dev.yd" ]
  rules:
    - when: manual
  before_script:
    - docker login --username=$DOCKER_USER_JSYUANDA -p "$DOCKER_PWD_JSYUANDA" registry.cn-shanghai.aliyuncs.com
  script:
    - docker build --tag registry.cn-shanghai.aliyuncs.com/jsyuanda/lims52-cloud:$CI_COMMIT_TAG .
    - docker push registry.cn-shanghai.aliyuncs.com/jsyuanda/lims52-cloud:$CI_COMMIT_TAG