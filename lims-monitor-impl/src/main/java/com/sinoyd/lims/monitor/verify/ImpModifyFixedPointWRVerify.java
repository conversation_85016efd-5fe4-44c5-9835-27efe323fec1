package com.sinoyd.lims.monitor.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.monitor.dto.customer.DtoExpImpFixedPointWR;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 污染源点位导入校验器
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Data
public class ImpModifyFixedPointWRVerify implements IExcelVerifyHandler<DtoExpImpFixedPointWR> {


    private ImportUtils importUtils = new ImportUtils();

    /**
     * 所有测站数据
     */
    private final List<DtoStation> stationList;

    /**
     * 所有的区域数据
     */
    private final List<DtoArea> dbArea;

    /**
     * 控制等级
     */
    private final Map<String, List<DtoCode>> codeMap;

    /**
     * 所有企业数据
     */
    private final List<DtoEnterprise> enterpriseList;

    /**
     * 所有的检测类型
     */
    private final List<DtoSampleType> sampleTypes;

    /**
     * 系统中的所有污染源点位数据。
     */
    private final List<DtoFixedpoint> fixedpointList;

    public ImpModifyFixedPointWRVerify(List<DtoStation> stationList,
                                       List<DtoArea> dbArea,
                                       Map<String, List<DtoCode>> codeMap,
                                       List<DtoEnterprise> enterpriseList,
                                       List<DtoSampleType> sampleTypes,
                                       List<DtoFixedpoint> fixedpointList) {
        this.stationList = stationList;
        this.dbArea = dbArea;
        this.codeMap = codeMap;
        this.enterpriseList = enterpriseList;
        this.sampleTypes = sampleTypes;
        this.fixedpointList = fixedpointList;
    }

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpFixedPointWR fixedPoint) {
        //导入参数处理
        try {
            //去除空行
            if (importUtils.checkObjectIsNull(fixedPoint)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //去除首尾空格
            importUtils.strToTrim(fixedPoint);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder failStr = new StringBuilder("第" + (fixedPoint.getRowNum() + 1) + "行数据校验错误");

        //控制等级
        List<DtoCode> controlLevels = codeMap.get(LimConstants.ImportConstants.CONTROL_LEVEL);
        //污染源的点位类型
        List<DtoCode> pointTypeWR = codeMap.get(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR);
        //处理关联数据Id
        //处理是否启用
//        if (StringUtil.isNotEmpty(fixedPoint.getEnabledStatus())) {
//            fixedPoint.setIsEnabled(!"否".equals(fixedPoint.getEnabledStatus()));
//        }
        //处理测站id
//        if (StringUtil.isNotEmpty(fixedPoint.getStationName()) && StringUtil.isNotEmpty(stationList)) {
//            Optional<DtoStation> stationOp = stationList
//                    .stream().filter(p -> fixedPoint.getStationName().equals(p.getStname())).findFirst();
//            stationOp.ifPresent(p -> fixedPoint.setStationId(p.getId()));
//        }
        //处理污染源点位的关联Id

        //处理企业Id
        if (StringUtil.isNotEmpty(fixedPoint.getEnterpriseName()) && StringUtil.isNotEmpty(enterpriseList)) {
            Optional<DtoEnterprise> enterpriseOp = enterpriseList
                    .stream().filter(p -> fixedPoint.getEnterpriseName().equals(p.getName())).findFirst();
            enterpriseOp.ifPresent(p -> fixedPoint.setEnterpriseId(p.getId()));
        }
        //处理检测类型Id
        if (StringUtil.isNotEmpty(fixedPoint.getSampleTypeName()) && StringUtil.isNotEmpty(sampleTypes)) {
            Optional<DtoSampleType> sampleTypeOp = sampleTypes
                    .stream().filter(p -> fixedPoint.getSampleTypeName().equals(p.getTypeName())).findFirst();
            sampleTypeOp.ifPresent(p -> fixedPoint.setSampleTypeId(p.getId()));
        }


        //非空字段判断
        importUtils.checkIsNull(result, fixedPoint.getFolderTypeName(), "点位类型", failStr);
        importUtils.checkIsNull(result, fixedPoint.getPointName(), "点位名称", failStr);
//        importUtils.checkIsNull(result, fixedPoint.getStationName(), "所属测站", failStr);

        importUtils.checkIsNull(result, fixedPoint.getEnterpriseName(), "企业名称", failStr);
        importUtils.checkIsNull(result, fixedPoint.getSampleTypeName(), "检测类型", failStr);


        //判断点位类型是否存在
        isExistPointType(result, failStr, pointTypeWR, fixedPoint);
        //判断等级是否存在
//        isExistControlLevel(result, failStr, fixedPoint, controlLevels);
        //判断测站是否存在
//        isExistStation(result, failStr, fixedPoint, stationList);
        //判断企业是否存在以及检测类型是否存在(污染源点位)
        //判断企业是否存在
        isExistEnterprise(result, failStr, fixedPoint, enterpriseList);
        //判断监测类型是否存在
        isExistSampleType(result, failStr, fixedPoint, sampleTypes, fixedpointList);


        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 判断等级是否存在
     *
     * @param result        校验结果
     * @param failStr       校验错误信息
     * @param fixedPoint    当前导入的点位数据
     * @param controlLevels 所有的控制等级数据
     */
//    private void isExistControlLevel(ExcelVerifyHandlerResult result, StringBuilder failStr,
//                                     DtoExpImpFixedPointWR fixedPoint, List<DtoCode> controlLevels) {
//        //判断等级是否存在
//        if (StringUtil.isNotEmpty(fixedPoint.getLevelName())) {
//            List<DtoCode> existCode = controlLevels.stream()
//                    .filter(p -> fixedPoint.getLevelName().equals(p.getDictName()))
//                    .collect(Collectors.toList());
//            if (StringUtil.isEmpty(existCode)) {
//                result.setSuccess(false);
//                failStr.append(";等级不存在");
//            }
//        }
//    }

    /**
     * 判断测站是否存在
     *
     * @param result      校验结果
     * @param failStr     校验错误信息
     * @param fixedPoint  当前导入的点位信息
     * @param stationList 所有的测站数据
     */
//    private void isExistStation(ExcelVerifyHandlerResult result, StringBuilder failStr,
//                                DtoExpImpFixedPointWR fixedPoint, List<DtoStation> stationList) {
//        if (StringUtil.isNotEmpty(fixedPoint.getStationName()) && StringUtil.isNotEmpty(stationList)) {
//            List<DtoStation> existStation = stationList.stream()
//                    .filter(p -> fixedPoint.getStationName().equals(p.getStname()))
//                    .collect(Collectors.toList());
//            if (StringUtil.isEmpty(existStation)) {
//                result.setSuccess(false);
//                failStr.append(";所属测站不存在");
//            }
//        }
//    }

    /**
     * 判断企业是否存在（污染源点位）
     *
     * @param result         校验结果
     * @param failStr        校验错误信息
     * @param fixedPoint     当前导入点位信息
     * @param enterpriseList 所有的企业数据
     */
    private void isExistEnterprise(ExcelVerifyHandlerResult result, StringBuilder failStr,
                                   DtoExpImpFixedPointWR fixedPoint, List<DtoEnterprise> enterpriseList) {
        //判断企业是否存在
        if (StringUtil.isNotEmpty(fixedPoint.getEnterpriseName()) && StringUtil.isNotEmpty(enterpriseList)) {
            List<DtoEnterprise> existEnterprise = enterpriseList.stream()
                    .filter(p -> fixedPoint.getEnterpriseName().equals(p.getName()))
                    .collect(Collectors.toList());
            if (StringUtil.isEmpty(existEnterprise)) {
                result.setSuccess(false);
                failStr.append(";企业不存在");
            }
        }
    }


    /**
     * 判断检测类型是否存在（污染源点位）
     *
     * @param result         校验结果
     * @param failStr        校验错误信息
     * @param fixedPoint     当前导入的点位信息
     * @param sampleTypes    所有的检测类型
     * @param fixedpointList 系统中的所有污染源点位
     */
    private void isExistSampleType(ExcelVerifyHandlerResult result, StringBuilder failStr,
                                   DtoExpImpFixedPointWR fixedPoint, List<DtoSampleType> sampleTypes, List<DtoFixedpoint> fixedpointList) {
        if (StringUtil.isNotEmpty(fixedPoint.getSampleTypeName()) && StringUtil.isNotEmpty(sampleTypes)) {
            List<DtoSampleType> existSampleType = sampleTypes.stream()
                    .filter(p -> fixedPoint.getSampleTypeName().equals(p.getTypeName()))
                    .collect(Collectors.toList());
            if (StringUtil.isEmpty(existSampleType)) {
                result.setSuccess(false);
                failStr.append(";检测类型不存在");
            } else {
                // 校验如果数据id不为null，并且系统中存在数据，检测类型不能修改
                if (StringUtil.isNotEmpty(fixedPoint.getId())){
                    Optional<DtoFixedpoint> fixedpointOptional = fixedpointList.stream().filter(p -> fixedPoint.getId().equals(p.getId())).findFirst();
                    fixedpointOptional.ifPresent(p -> {
                        if (!existSampleType.get(0).getId().equals(p.getSampleTypeId())) {
                            result.setSuccess(false);
                            failStr.append(";检测类型不能修改");
                        }
                    });
                }
            }
        }
    }

    /**
     * 判断点位类型是否存在
     *
     * @param result      校验结果
     * @param failStr     校验错误信息
     * @param pointTypeWR 污染源点位类型
     * @param fixedPoint  当前导入的数据
     */
    private void isExistPointType(ExcelVerifyHandlerResult result, StringBuilder failStr,
                                  List<DtoCode> pointTypeWR,
                                  DtoExpImpFixedPointWR fixedPoint) {
        //判断点位类型是否存在
        if (StringUtil.isNotEmpty(fixedPoint.getFolderTypeName())) {
            List<DtoCode> existCode = pointTypeWR.stream()
                    .filter(p -> fixedPoint.getFolderTypeName().equals(p.getDictName()))
                    .collect(Collectors.toList());
            if (StringUtil.isEmpty(existCode)) {
                result.setSuccess(false);
                failStr.append(";点位类型不存在");
            }
        }
    }

}
