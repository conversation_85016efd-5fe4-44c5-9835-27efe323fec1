package com.sinoyd.lims.monitor.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.service.ExportFixedPointHJService;
import com.sinoyd.lims.monitor.service.ExportFixedPointWRService;
import com.sinoyd.lims.monitor.service.ImportFixedPointService;
import com.sinoyd.lims.monitor.service.ImportFixedPointWRService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 污染源/环境质量点位导入
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@Api(tags = "污染源/环境质量点位导入")
@RestController
@RequestMapping("/api/monitor/import")
public class ImportFixedPointController extends ExceptionHandlerController<ImportFixedPointService> {

    @Autowired
    private ExportFixedPointWRService exportFixedPointWRService;
    @Autowired
    private ExportFixedPointHJService exportFixedPointHJService;
    @Autowired
    private ImportFixedPointWRService importFixedPointWRService;

    /**
     * 污染源点位导入
     *
     * @param file         导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/fixedPointWR")
    public RestResponse<List<DtoFixedpoint>> importFixedPointWR(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, true);
        RestResponse<List<DtoFixedpoint>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importFixedPointWRService.importExcel(file, objectMap,response));
        return restResponse;
    }

    /**
     * 环境质量点位导入
     *
     * @param file         导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/fixedPointHJ")
    public RestResponse<List<DtoFixedpoint>> importFixedPointHJ(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, false);
        RestResponse<List<DtoFixedpoint>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.importExcel(file, objectMap,response));
        return restResponse;
    }


    /**
     * 污染源点位导入（修改）
     *
     * @param file         导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/importFixedPointWR")
    public RestResponse<List<DtoFixedpoint>> importUpdateFixedPointWR(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, true);
        RestResponse<List<DtoFixedpoint>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(exportFixedPointWRService.importExcel(file, objectMap,response));
        return restResponse;
    }

    /**
     * 环境质量点位导入（修改）
     *
     * @param file         导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/importFixedPointHJ")
    public RestResponse<List<DtoFixedpoint>> importFUpdateixedPointHJ(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, false);
        RestResponse<List<DtoFixedpoint>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(exportFixedPointHJService.importExcel(file, objectMap,response));
        return restResponse;
    }
}
