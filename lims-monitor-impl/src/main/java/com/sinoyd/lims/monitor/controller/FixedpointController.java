package com.sinoyd.lims.monitor.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.criteria.FixedpointCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.service.FixedpointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Fixedpoint服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Api(tags = "示例: Fixedpoint服务")
@RestController
@RequestMapping("api/monitor/fixedpoint")
public class FixedpointController extends BaseJpaController<DtoFixedpoint, String, FixedpointService> {


    /**
     * 分页动态条件查询Fixedpoint
     *
     * @param fixedpointCriteria 条件参数
     * @return RestResponse<List < Fixedpoint>>
     */
    @ApiOperation(value = "分页动态条件查询Fixedpoint", notes = "分页动态条件查询Fixedpoint")
    @GetMapping
    public RestResponse<List<DtoFixedpoint>> findByPage(FixedpointCriteria fixedpointCriteria) {
        PageBean<DtoFixedpoint> pageBean = super.getPageBean();
        RestResponse<List<DtoFixedpoint>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, fixedpointCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Fixedpoint
     *
     * @param id 主键id
     * @return RestResponse<DtoFixedpoint>
     */
    @ApiOperation(value = "按主键查询Fixedpoint", notes = "按主键查询Fixedpoint")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoFixedpoint> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoFixedpoint> restResponse = new RestResponse<>();
        DtoFixedpoint fixedpoint = service.findOne(id);
        restResponse.setData(fixedpoint);
        return restResponse;
    }

    /**
     * 批量修改频次污染源点位
     *
     * @param fixedPoint2Test 前端传参
     * @return RestResponse<DtoFixedpoint>
     */
    @ApiOperation(value = "批量修改频次", notes = "批量修改频次")
    @PutMapping(path = "/timesOrder")
    public RestResponse<DtoFixedpoint> updateTimesOrder(@RequestBody DtoFixedPoint2Test fixedPoint2Test) {
        RestResponse<DtoFixedpoint> restResponse = new RestResponse<>();
        service.updateTimesOrder(fixedPoint2Test);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 批量修改频次环境质量点位
     *
     * @param propertyPoint2Test 前端传参
     * @return RestResponse<DtoFixedpoint>
     */
    @ApiOperation(value = "批量修改频次", notes = "批量修改频次")
    @PutMapping(path = "/pointTimesOrder")
    public RestResponse<DtoFixedpoint> updatePointTimesOrder(@RequestBody DtoPropertyPoint2Test propertyPoint2Test) {
        RestResponse<DtoFixedpoint> restResponse = new RestResponse<>();
        service.updatePointTimesOrder(propertyPoint2Test);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 点位重复检查
     *
     * @param fixedPoint 点位实体
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "点位重复检查", notes = "点位重复检查")
    @PostMapping("/duplicate")
    public RestResponse<Boolean> duplicatePointCheck(@RequestBody DtoFixedpoint fixedPoint) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.duplicatePointCheck(fixedPoint));
        return restResponse;
    }

    /**
     * 新增Fixedpoint
     *
     * @param fixedpoint 实体列表
     * @return RestResponse<DtoFixedpoint>
     */
    @ApiOperation(value = "新增Fixedpoint", notes = "新增Fixedpoint")
    @PostMapping
    public RestResponse<DtoFixedpoint> create(@RequestBody @Validated DtoFixedpoint fixedpoint) {
        RestResponse<DtoFixedpoint> restResponse = new RestResponse<>();
        restResponse.setData(service.save(fixedpoint));
        return restResponse;
    }

    /**
     * 新增测试项目
     *
     * @param fixedpoint 实体列表
     * @return RestResponse<DtoFixedpoint>
     */
    @ApiOperation(value = "新增测试项目", notes = "新增测试项目")
    @PostMapping("/test")
    public RestResponse<Boolean> createTest(@RequestBody DtoFixedpoint fixedpoint) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.saveTest(fixedpoint);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 修改Fixedpoint
     *
     * @param fixedpoint 实体列表
     * @return RestResponse<DtoFixedpoint>
     */
    @ApiOperation(value = "修改Fixedpoint", notes = "修改Fixedpoint")
    @PutMapping
    public RestResponse<DtoFixedpoint> update(@RequestBody @Validated DtoFixedpoint fixedpoint) {
        RestResponse<DtoFixedpoint> restResponse = new RestResponse<>();
        restResponse.setData(service.update(fixedpoint));
        return restResponse;
    }

    /**
     * "根据id批量删除Fixedpoint
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Fixedpoint", notes = "根据id批量删除Fixedpoint")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 选择关联点位
     *
     * @param fixedPoint 点位对象
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "选择关联点位", notes = "选择关联点位")
    @PostMapping("/relation")
    public RestResponse<Boolean> selectRelationPoints(@RequestBody DtoFixedpoint fixedPoint) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.selectRelationPoints(fixedPoint);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 删除关联测试项目
     *
     * @param fixedPoint 点位对象
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "删除关联点位", notes = "删除关联点位")
    @DeleteMapping("/delTest")
    public RestResponse<Boolean> deleteTest(@RequestBody DtoFixedpoint fixedPoint) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.deleteTest(fixedPoint);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 选择关联监测计划
     *
     * @param fixedPoint 点位对象
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "选择关联点位", notes = "选择关联点位")
    @PostMapping("/relation/property")
    public RestResponse<Boolean> selectRelationProperty(@RequestBody DtoFixedpoint fixedPoint) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.selectRelationProperty(fixedPoint);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 删除关联监测计划
     *
     * @param fixedPoint 点位对象
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "删除关联监测计划", notes = "删除关联监测计划")
    @DeleteMapping("/relation/property")
    public RestResponse<Boolean> deleteRelationProperty(@RequestBody DtoFixedpoint fixedPoint) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.deleteRelationProperty(fixedPoint);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }


    /**
     * 转换环境质量点位拓展数据
     *
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "转换环境质量点位拓展数据", notes = "转换环境质量点位拓展数据")
    @GetMapping("/moveExtendData")
    public RestResponse<String> moveExtendData(){
        RestResponse<String> restResponse = new RestResponse<>();
        service.moveFixedPointExtendData(EnumMonitor.EnumPointType.环境质量.getValue());
        restResponse.setMsg("转换成功");
        return restResponse;
    }

    /**
     * 转换污染源点位拓展数据
     *
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "转换污染源点位拓展数据", notes = "转换污染源点位拓展数据")
    @GetMapping("/pollutionExtend")
    public RestResponse<String> movePollutionExtendData(){
        RestResponse<String> restResponse = new RestResponse<>();
        service.moveFixedPointExtendData(EnumMonitor.EnumPointType.污染源.getValue());
        restResponse.setMsg("转换成功");
        return restResponse;
    }

    /**
     * 复制污染源点位
     *
     * @return RestResponse<Void> 响应内容
     */
    @ApiOperation(value = "复制污染源点位", notes = "复制污染源点位")
    @PostMapping("/copyPoint")
    public RestResponse<DtoFixedpoint> copyPoint(@RequestBody DtoFixedpoint fixedpoint) {
        RestResponse<DtoFixedpoint> response = new RestResponse<>();
        response.setData(service.copyPoint(fixedpoint));
        return response;
    }

    /**
     * 复制污染源点位测试项目
     *
     * @return RestResponse<Void> 响应内容
     */
    @ApiOperation(value = "复制污染源点位测试项目", notes = "复制污染源点位测试项目")
    @PostMapping("/copyPointTest")
    public RestResponse<Void> copyPointTest(@RequestBody DtoFixedpoint fixedPoint) {
        RestResponse<Void> response = new RestResponse<>();
        service.copyPointTest(fixedPoint);
        return response;
    }
}