package com.sinoyd.lims.monitor.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.monitor.dto.customer.DtoImportFixedPointEQ;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.repository.rcc.FixedPointExpendRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.rcc.StationRepository;
import com.sinoyd.lims.monitor.service.ImportFixedPointService;
import com.sinoyd.lims.monitor.verify.FixedPointVerifyHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 环境质量/污染源导入实现
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@Service
@Slf4j
public class ImportFixedPointServiceImpl implements ImportFixedPointService {

    private FixedPointVerifyHandle fixedPointVerifyHandle;

    protected FixedPointExpendRepository fixedPointExpendRepository;

    private FixedpointRepository fixedpointRepository;

    private CodeService codeService;

    private SampleTypeRepository sampleTypeRepository;

    private EnterpriseRepository enterpriseRepository;

    private StationRepository stationRepository;

    private AreaService areaService;

    /**
     * 导入Excel
     *
     * @param file      传入的文件
     * @param objectMap 业务数据
     * @param response  响应体
     * @return 导入的数据
     * @throws Exception 异常
     */
    @Override
    @Transactional
    public List<DtoFixedpoint> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        //校验文件类型
        PoiExcelUtils.verifyFileType(file);
        //获取业务参数
        Boolean isPollution = (Boolean) objectMap.get(0);
        Map<String, Boolean> relationMap = new HashMap<>();
        relationMap.put("isPollution", isPollution);
        //获取所有的检测类型
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        //获取所有的企业
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findAll();
        //获取所有的测站
        List<DtoStation> stationList = stationRepository.findAll();
        //获取所有区域信息
        List<DtoArea> dbAreaList = areaService.findAll();
        //获取所有的控制等级
        List<DtoCode> controlLevels = codeService.findCodes(LimConstants.ImportConstants.CONTROL_LEVEL);
        //获取所有的点位类型（环境质量）
        List<DtoCode> pointTypeHJ = codeService.findCodes(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ);
        //获取所有的点位类型（污染源）
        List<DtoCode> pointTypeWR = codeService.findCodes(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR);
        Map<String, List<DtoCode>> codeMap = new HashMap<>();
        codeMap.put(LimConstants.ImportConstants.CONTROL_LEVEL, controlLevels);
        codeMap.put(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ, pointTypeHJ);
        codeMap.put(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR, pointTypeWR);
        //设置线程变量
        fixedPointVerifyHandle.getCodeTl().set(codeMap);
        fixedPointVerifyHandle.getDbAreaTl().set(dbAreaList);
        fixedPointVerifyHandle.getEnterpriseTl().set(enterpriseList);
        fixedPointVerifyHandle.getStationTl().set(stationList);
        fixedPointVerifyHandle.getSampleTypeTl().set(sampleTypes);
        fixedPointVerifyHandle.getRelationTl().set(relationMap);
        //获取导入校验后的数据
        ExcelImportResult<DtoImportFixedPointEQ> excelData = getExcelData(file, response);
        List<DtoImportFixedPointEQ> list = excelData.getList();
        list.removeIf(p -> StringUtil.isEmpty(p.getPointName()));
        if (StringUtil.isEmpty(list)) {
            throw new BaseException("文件中无数据或者上传文件模板错误，请检查后重新上传");
        }
        //清理线程变量
        fixedPointVerifyHandle.getCodeTl().remove();
        fixedPointVerifyHandle.getEnterpriseTl().remove();
        fixedPointVerifyHandle.getStationTl().remove();
        fixedPointVerifyHandle.getSampleTypeTl().remove();
        fixedPointVerifyHandle.getRelationTl().remove();
        //处理常量数据
        list = handleCode(list, controlLevels, pointTypeHJ, pointTypeWR, isPollution);
        //转换实体
        List<DtoFixedpoint> fixedPoints = importToEntity(list);
        //获取点位拓展数据
        List<DtoFixedPointExpend> fixedPointExpends = handleFixedPointExpand(fixedPoints, isPollution);
        //保存数据
        addData(fixedPoints);
        //保存点位拓展数据
        if (StringUtil.isNotEmpty(fixedPointExpends)) {
            fixedPointExpendRepository.save(fixedPointExpends);
        }
        return fixedPoints;
    }

    /**
     * 保存数据
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoFixedpoint> data) {
        if (StringUtil.isNotEmpty(data)) {
            fixedpointRepository.save(data);
        }
    }

    /**
     * 获取Excel中的数据
     *
     * @param file     传入的文件
     * @param response 响应体
     * @return 导入的数据
     * @throws Exception 异常
     */
    @Override
    public ExcelImportResult<DtoImportFixedPointEQ> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(fixedPointVerifyHandle);
        ExcelImportResult<DtoImportFixedPointEQ> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoImportFixedPointEQ.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "企业信息");
            PoiExcelUtils.downLoadExcel("企业导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 获取点位拓展数据
     *
     * @param fixedPoints 点位数据
     * @param isPollution 是否为污染源点位导入
     * @return 点位拓展数据
     */
    private List<DtoFixedPointExpend> handleFixedPointExpand(List<DtoFixedpoint> fixedPoints, Boolean isPollution) {
        //点位拓展数据集合
        List<DtoFixedPointExpend> fixedPointExpends = new ArrayList<>();
        if (isPollution) {
            for (DtoFixedpoint fixedPoint : fixedPoints) {
                DtoFixedPointExpend expend = new DtoFixedPointExpend();
                //设置点位id
                expend.setFixedPointId(fixedPoint.getId());
                //设置水体id为空
                expend.setWaterId("");
                //当是污染源点位导入时,设置设备启用时间与设备运投日期
                expend.setCraftFacilityUseDate(new Date());
                expend.setPurificateFacilityUseDate(new Date());
                //添加到保存集合中
                fixedPointExpends.add(expend);
            }
        }
        return fixedPointExpends;
    }

    /**
     * 处理数据的常量
     *
     * @param importList   导入的数据
     * @param controlLevel 所有的控制等级
     * @param pointTypeHJ  环境质量点位类型
     * @param pointTypeWR  污染源点位类型
     * @param isPollution  是否为污染源的点位导入
     * @return 处理后的数据
     */
    private List<DtoImportFixedPointEQ> handleCode(List<DtoImportFixedPointEQ> importList, List<DtoCode> controlLevel,
                                                   List<DtoCode> pointTypeHJ, List<DtoCode> pointTypeWR, Boolean isPollution) {
        List<DtoImportFixedPointEQ> result = new ArrayList<>();
        //循环处理数据
        for (DtoImportFixedPointEQ dtoImport : importList) {
            //处理点位类型
            if (StringUtil.isNotEmpty(dtoImport.getFolderTypeName())) {
                if (isPollution) {
                    //设置类型
                    dtoImport.setPointType(EnumMonitor.EnumPointType.污染源.getValue());
                    //设置污染源的点位类型
                    if (StringUtil.isNotEmpty(pointTypeWR)) {
                        Optional<DtoCode> pointTypeOp = pointTypeWR
                                .stream().filter(p -> dtoImport.getFolderTypeName().equals(p.getDictName())).findFirst();
                        pointTypeOp.ifPresent(p -> dtoImport.setFolderType(p.getDictCode()));
                    }
                } else {
                    //设置类型
                    dtoImport.setPointType(EnumMonitor.EnumPointType.环境质量.getValue());
                    //设置环境质量的点位类型
                    if (StringUtil.isNotEmpty(pointTypeHJ)) {
                        String folderTypeNames = dtoImport.getFolderTypeName();
                        Set<String> folderTypeNameList = getFolderTypeNames(folderTypeNames);
                        List<String> folderTypes = new ArrayList<>();
                        for (String folderType : folderTypeNameList) {
                            Optional<DtoCode> pointTypeOp = pointTypeHJ
                                    .stream().filter(p -> folderType.equals(p.getDictName())).findFirst();
                            pointTypeOp.ifPresent(p -> folderTypes.add(p.getDictCode()));
                        }
                        dtoImport.setFolderType(String.join(",", folderTypes));
                    }
                }
            }
            //设置控制等级
            if (StringUtil.isNotEmpty(dtoImport.getLevelName()) && StringUtil.isNotEmpty(controlLevel)) {
                Optional<DtoCode> controlLevelOp = controlLevel.stream().filter(p -> dtoImport.getLevelName().equals(p.getDictName())).findFirst();
                controlLevelOp.ifPresent(p -> dtoImport.setLevel(p.getDictCode()));
            }
            result.add(dtoImport);
        }
        return result;
    }

    /**
     * 获取导出的所有点位类型
     *
     * @param folderTypeNames 点位类型导入字符串
     * @return 点位类型字符串集合
     */
    private Set<String> getFolderTypeNames(String folderTypeNames) {
        if (StringUtil.isNotEmpty(folderTypeNames)) {
            if (folderTypeNames.contains("、")) {
                return Arrays.stream(folderTypeNames.split("、")).collect(Collectors.toSet());
            }else{
                return Stream.of(folderTypeNames).collect(Collectors.toSet());
            }
        }
        return new HashSet<>();
    }

    /**
     * 转换为需要保存的实体
     *
     * @param importList 导入的数据
     * @return 实体集合
     */
    private List<DtoFixedpoint> importToEntity(List<DtoImportFixedPointEQ> importList) {
        //点位数据
        List<DtoFixedpoint> fixedPoints = new ArrayList<>();
        for (DtoImportFixedPointEQ dto : importList) {
            DtoFixedpoint fixedPoint = new DtoFixedpoint();
            //赋值属性
            BeanUtils.copyProperties(dto, fixedPoint);
            //处理周期，次数默认为1
            fixedPoint.setCycleOrder(LimConstants.ImportConstants.DEFAULT_ORDER_NUM);
            fixedPoint.setTimesOrder(LimConstants.ImportConstants.DEFAULT_ORDER_NUM);
            fixedPoint.setEvaluationId("");
            fixedPoint.setEvaluationLevelId("");
            //根据所属区域名称获取区域数据
            String areaId = "";
            if (StringUtil.isNotEmpty(dto.getProvinceAreaName()) && StringUtil.isNotEmpty(dto.getProvinceId())) {
                areaId = dto.getProvinceId();
            }
            if (StringUtil.isNotEmpty(dto.getCityAreaName()) && StringUtil.isNotEmpty(dto.getCityId())) {
                areaId = dto.getCityId();
            }
            if (StringUtil.isNotEmpty(dto.getAreaName()) && StringUtil.isNotEmpty(dto.getAreaId())) {
                areaId = dto.getAreaId();
            }
            fixedPoint.setAreaId(areaId);
            //添加数据
            fixedPoints.add(fixedPoint);
        }
        return fixedPoints;
    }

    @Autowired
    public void setFixedPointVerifyHandle(FixedPointVerifyHandle fixedPointVerifyHandle) {
        this.fixedPointVerifyHandle = fixedPointVerifyHandle;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setStationRepository(StationRepository stationRepository) {
        this.stationRepository = stationRepository;
    }

    @Autowired
    public void setFixedpointRepository(FixedpointRepository fixedpointRepository) {
        this.fixedpointRepository = fixedpointRepository;
    }

    @Autowired
    public void setFixedPointExpendRepository(FixedPointExpendRepository fixedPointExpendRepository) {
        this.fixedPointExpendRepository = fixedPointExpendRepository;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }
}
