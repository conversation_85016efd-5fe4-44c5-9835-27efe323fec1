package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * Fixedpoint数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedpointRepository extends IBaseJpaRepository<DtoFixedpoint, String> {

    /**
     * 通过 ids的集合查询对象集合
     *
     * @param ids
     * @return List<DtoFixedpoint>
     */
    List<DtoFixedpoint> findByIdIn(List<String> ids);


    /**
     * 根据点位类型和点位名称统计记录数
     *
     * @param folderType 点位类型
     * @param pointName  点位名称
     * @return 记录数
     */
    Integer countByFolderTypeAndPointName(String folderType, String pointName);

    /**
     * 根据点位类型和点位名称和企业id统计记录数
     *
     * @param folderType 点位类型
     * @param pointName  点位名称
     * @param enterpriseId  企业id
     * @return 记录数
     */
    Integer countByFolderTypeAndPointNameAndEnterpriseId(String folderType, String pointName, String enterpriseId);

    /**
     * 根据点位类型、点位名称、id统计记录数
     *
     * @param folderType 点位类型
     * @param pointName  点位名称
     * @param id         主键
     * @return 记录数
     */
    Integer countByFolderTypeAndPointNameAndIdNot(String folderType, String pointName, String id);

    /**
     * 根据点位类型、点位名称、id统计记录数
     *
     * @param folderType 点位类型
     * @param pointName  点位名称
     * @param enterpriseId  企业id
     * @param id         主键
     * @return 记录数
     */
    Integer countByFolderTypeAndPointNameAndEnterpriseIdAndIdNot(String folderType, String pointName, String enterpriseId ,String id);


    /**
     * 根据企业id和点位类型获取点位集合
     * @param entId 企业id
     * @param type 点位类型
     * @return List<DtoFixedpoint>
     */
    List<DtoFixedpoint> findByEnterpriseIdAndPointType(String entId, Integer type);

    /**
     * 根据企业id查询企业下点位数据
     *
     * @param enterpriseId 企业id
     * @return 点位数据集合
     */
    List<DtoFixedpoint> findByEnterpriseId(String enterpriseId);


    /**
     * 根据企业id、是否启用和点位类型获取点位集合
     * @param entId 企业id
     * @param enable 是否启用
     * @param type 点位类型
     * @return List<DtoFixedpoint>
     */
    List<DtoFixedpoint> findByEnterpriseIdAndIsEnabledAndPointType(String entId, Boolean enable, Integer type);

    /**
     * 通过点位类型和是否启用获取点位集合
     * @param enable 是否启用
     * @param type 点位类型
     * @return List<DtoFixedpoint>
     */
    List<DtoFixedpoint> findByIsEnabledAndPointType(Boolean enable, Integer type);

    /**
     * 通过点位类型获取点位集合
     * @param type 点位类型
     * @return List<DtoFixedpoint>
     */
    List<DtoFixedpoint> findByPointType(Integer type);
}