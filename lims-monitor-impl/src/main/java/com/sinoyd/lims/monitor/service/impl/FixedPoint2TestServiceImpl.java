package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.repository.rcc.FixedPoint2TestRepository;
import com.sinoyd.lims.monitor.service.FixedPoint2TestService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;


/**
 * FixedPoint2Test操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class FixedPoint2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFixedPoint2Test, String, FixedPoint2TestRepository> implements FixedPoint2TestService {


    @Override
    public void findByPage(PageBean<DtoFixedPoint2Test> pb, BaseCriteria fixedPoint2TestCriteria) {
        pb.setEntityName("DtoFixedPoint2Test a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fixedPoint2TestCriteria);
    }

    @Override
    public List<DtoFixedPoint2Test> findByFixedPointIdIn(Collection<String> fixedPointIds) {
        return repository.findByFixedPointIdIn(fixedPointIds);
    }
}