package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.entity.Sample;
import com.sinoyd.lims.probase.repository.ProjectBaseRepository;
import com.sinoyd.lims.probase.repository.ReceiveSampleRecordBaseRepository;
import com.sinoyd.lims.probase.repository.SampleBaseRepository;
import com.sinoyd.lims.probase.service.CheckProjectFactoryService;
import com.sinoyd.lims.probase.service.CheckRecRecordFactoryService;
import com.sinoyd.lims.probase.service.CheckSampleFactoryService;
import com.sinoyd.lims.probase.service.ProBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProBaseServiceImpl implements ProBaseService {

    @Autowired
    private SampleBaseRepository sampleBaseRepository;

    @Autowired
    private ProjectBaseRepository projectBaseRepository;

    @Autowired
    private ReceiveSampleRecordBaseRepository receiveSampleRecordBaseRepository;

    @Autowired
    private ProjectTypeService projectTypeService;

    @Autowired
    private CheckProjectFactoryService checkProjectFactoryService;

    @Autowired
    private CheckRecRecordFactoryService checkRecRecordFactoryService;

    @Autowired
    private CheckSampleFactoryService checkSampleFactoryService;


    @Autowired
    private CommonRepository commonRepository;

    //#region 纠正状态

    /**
     * 核对样品状态
     *
     * @param ids 样品id列表
     */
    @Transactional
    @Override
    public void checkSample(List<String> ids) {
        if (StringUtil.isNotNull(ids) && ids.size() > 0) {
            List<DtoSample> sampleList = sampleBaseRepository.findAll(ids);
            checkSamples(sampleList);
        }
    }

    /**
     * 核对送样单状态
     *
     * @param ids 送样单id列表
     */
    @Transactional
    @Override
    public void checkReceiveSampleRecord(List<String> ids) {
        if (StringUtil.isNotNull(ids) && ids.size() > 0) {
            List<DtoReceiveSampleRecord> recList = receiveSampleRecordBaseRepository.findAll(ids);
            checkReceiveSampleRecords(recList);
        }
    }

    /**
     * 核对送样单状态
     *
     * @param recList 送样单id列表
     */
    @Override
    public void checkReceiveSampleRecords(List<DtoReceiveSampleRecord> recList) {
        if (StringUtil.isNotNull(recList) && recList.size() > 0) {
            List<String> proIds = recList.stream().map(DtoReceiveSampleRecord::getProjectId).distinct().collect(Collectors.toList());
            List<DtoProject> proList = projectBaseRepository.findAll(proIds);
            for (DtoReceiveSampleRecord rec : recList) {
                String type = "";
                Optional<DtoProject> opt = proList.stream().filter(p -> p.getId().equals(rec.getProjectId())).findFirst();
                if (opt.isPresent()) {
                    DtoProject project = opt.get();
                    type = project.getProjectTypeId();
                }
                bizReceiveSampleRecord(type, rec);
            }

            List<String> pids = recList.stream().map(DtoReceiveSampleRecord::getProjectId).filter(projectId -> !projectId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());

            if (StringUtil.isNotNull(pids) && pids.size() > 0) {
                checkProject(pids);
            }
        }
    }

    /**
     * 核对项目状态
     *
     * @param ids 项目id列表
     */
    @Transactional
    @Override
    public void checkProject(List<String> ids) {

        for (String id : ids) {
            DtoProject pro = projectBaseRepository.findOne(id);
            bizProject(pro);
        }
    }

    @Override
    public void checkSamples(List<DtoSample> samples) {
        Date t1 = new Date();
        List<String> proIds = samples.stream().filter(s -> !s.getProjectId().equals(UUIDHelper.GUID_EMPTY)).map(Sample::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> proList = projectBaseRepository.findAll(proIds);
        List<DtoProject> projects = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        //纠正样品状态
        for (DtoSample sample : samples) {
            String type = "";
            String projectId = sample.getProjectId();
            //说明有样品id的
            if (StringUtils.isNotNullAndEmpty(projectId) && !projectId.equals(UUIDHelper.GUID_EMPTY)) {
                Optional<DtoProject> opt = proList.stream().filter(p -> p.getId().equals(sample.getProjectId())).findFirst();
                if (opt.isPresent()) {
                    DtoProject project = opt.get();
                    type = project.getProjectTypeId();
                    map.put(sample.getReceiveId(), type);
                    if (!projects.contains(project)) {
                        projects.add(project);
                    }
                }
            } else if (StringUtils.isNotNullAndEmpty(sample.getAssociateSampleId())) {
                DtoSample associateSample = sampleBaseRepository.findOne(sample.getAssociateSampleId());
                if (StringUtil.isNotNull(associateSample)
                        && StringUtils.isNotNullAndEmpty(associateSample.getProjectId())
                        && !associateSample.getProjectId().equals(UUIDHelper.GUID_EMPTY)) {
                    DtoProject project = projectBaseRepository.findOne(associateSample.getProjectId());
                    type = project.getProjectTypeId();
                    map.put(sample.getReceiveId(), type);
                    if (!projects.contains(project)) {
                        projects.add(project);
                    }
                }
            }
            bizSample(type, sample);
        }
        List<String> rids = samples.stream().filter(s -> !s.getReceiveId().equals(UUIDHelper.GUID_EMPTY)).map(Sample::getReceiveId).distinct().collect(Collectors.toList());

        for (String rid : rids) {
            String projectType = map.get(rid);
            bizReceiveSampleRecord(projectType, rid);
        }
        //更新项目信息
        for (DtoProject project : projects) {
            bizProject(project);
        }
        Date t4 = new Date();
        System.out.println("t4-t3" + (t4.getTime() - t1.getTime()));
    }

    @Override
    public void checkSample(List<DtoSample> samples, List<DtoReceiveSampleRecord> recordList) {
        List<String> proIds = recordList.stream().map(DtoReceiveSampleRecord::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> proList = projectBaseRepository.findAll(proIds);
        //纠正样品状态
        for (DtoSample sample : samples) {
            if (StringUtils.isNotNullAndEmpty(sample.getProjectId()) && !sample.getProjectId().equals(UUIDHelper.GUID_EMPTY)) {
                DtoProject project = proList.stream().filter(p -> p.getId().equals(sample.getProjectId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(project)) {
                    bizSample(project.getProjectTypeId(), sample);
                }
            } else {
                DtoReceiveSampleRecord record = recordList.stream().filter(p -> p.getId().equals(sample.getReceiveId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(record)) {
                    DtoProject project = proList.stream().filter(p -> p.getId().equals(record.getProjectId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(project)) {
                        bizSample(project.getProjectTypeId(), sample);
                    }
                }
            }
        }
        //纠正送样单状态
        for (DtoReceiveSampleRecord record : recordList) {
            DtoProject project = proList.stream().filter(p -> p.getId().equals(record.getProjectId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(project)) {
                bizReceiveSampleRecord(project.getProjectTypeId(), record);
            }
        }
        for (DtoProject project : proList) {
            bizProject(project);
        }
    }

    @Override
    public void checkSample(List<DtoSample> samples, DtoProject dtoProject) {
        for (DtoSample sample : samples) {
            bizSample(dtoProject.getProjectTypeId(), sample);
        }
        List<String> rids = samples.stream().map(Sample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
        for (String rid : rids) {
            //手动更新送样单信息
            bizReceiveSampleRecord(dtoProject.getProjectTypeId(), rid);
        }
        //手动更新项目状态信息
        bizProject(dtoProject);
    }

    //#region 私有纠正状态

    /**
     * 核对样品
     *
     * @param sample 样品信息
     */
    private void bizSample(String type, DtoSample sample) {
        String packageName = "";
        if (StringUtils.isNotNullAndEmpty(type)) {
            packageName = projectTypeService.getConfigValue(type, "checkSample");
        }
        if (!StringUtils.isNotNullAndEmpty(packageName)) {
            packageName = "com.sinoyd.lims.probase.service.CheckSampleService";
        }
        checkSampleFactoryService.checkSampleByType(packageName, sample);

    }

    /**
     * 核对送样单
     *
     * @param sentId 送样单id
     */
    private void bizReceiveSampleRecord(String type, String sentId) {
        String packageName = "";
        if (StringUtils.isNotNullAndEmpty(type)) {
            packageName = projectTypeService.getConfigValue(type, "checkReceiveSampleRecord");
        }
        if (!StringUtils.isNotNullAndEmpty(packageName)) {
            packageName = "com.sinoyd.lims.probase.service.CheckRecRecordService";
        }
        checkRecRecordFactoryService.checkRecRecordByType(packageName, sentId);

    }

    /**
     * 核对送样单
     *
     * @param receiveSampleRecord 送样单信息
     */
    private void bizReceiveSampleRecord(String type, DtoReceiveSampleRecord receiveSampleRecord) {
        String packageName = "";
        if (StringUtils.isNotNullAndEmpty(type)) {
            packageName = projectTypeService.getConfigValue(type, "checkReceiveSampleRecord");
        }
        if (!StringUtils.isNotNullAndEmpty(packageName)) {
            packageName = "com.sinoyd.lims.probase.service.CheckRecRecordService";
        }
        checkRecRecordFactoryService.checkRecRecordByType(packageName, receiveSampleRecord);
    }

    /**
     * 核对项目状态
     *
     * @param objPro 项目
     */
    private void bizProject(DtoProject objPro) {
        String packageName = projectTypeService.getConfigValue(objPro.getProjectTypeId(), "checkProject");
        if (!StringUtils.isNotNullAndEmpty(packageName)) {
            packageName = "com.sinoyd.lims.probase.service.CheckProjectService";
        }
        checkProjectFactoryService.checkProjectByType(packageName, objPro.getId());
    }
    //#endregion

    //#endregion
}
