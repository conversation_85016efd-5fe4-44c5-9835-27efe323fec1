<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinoyd.lims.od</groupId>
    <artifactId>lims-od</artifactId>
    <version>ent-5.4.82-SNAPSHOT</version>
  </parent>
  <groupId>com.sinoyd.lims.od</groupId>
  <artifactId>lims-od-impl</artifactId>
  <version>ent-5.4.82-SNAPSHOT</version>
  <name>lims-od-impl</name>
  <description>LIMS嗅辨模块实现层</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.sinoyd.lims.od</groupId>
      <artifactId>lims-od-arch</artifactId>
    </dependency>
  </dependencies>
  <build>
    <finalName>lims-od-impl</finalName>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>run-allatori</id>
            <phase>package</phase>
            <goals>
              <goal>exec</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <executable>java</executable>
          <arguments>
            <argument>-Xms128m</argument>
            <argument>-Xmx512m</argument>
            <argument>-jar</argument>
            <argument>${basedir}/lib/allatori.jar</argument>
            <argument>${basedir}/lib/allatori.xml</argument>
          </arguments>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
