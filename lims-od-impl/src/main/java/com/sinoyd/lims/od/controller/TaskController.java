package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.TaskCriteria;
import com.sinoyd.lims.od.dto.DtoTask;
import com.sinoyd.lims.od.service.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * task服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: 嗅辨任务服务")
@RestController
@RequestMapping("api/ods/task")
@Validated
public class TaskController extends BaseJpaController<DtoTask, String, TaskService> {

    /**
     * 分页动态条件查询Task
     *
     * @param taskCriteria 条件参数
     * @return RestResponse<List < Task>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoTask>> findByPage(TaskCriteria taskCriteria) {
        PageBean<DtoTask> pageBean = super.getPageBean();
        RestResponse<List<DtoTask>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, taskCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询嗅辨任务
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询嗅辨任务", notes = "根据id查询嗅辨任务")
    @GetMapping("/{id}")
    public RestResponse<DtoTask> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoTask> restResp = new RestResponse<>();
        DtoTask entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增嗅辨任务
     *
     * @param task 嗅辨任务实体
     * @return 新增的嗅辨任务实体
     */
    @ApiOperation(value = "新增嗅辨任务", notes = "新增嗅辨任务")
    @PostMapping("")
    public RestResponse<DtoTask> create(@Validated @RequestBody DtoTask task) {
        RestResponse<DtoTask> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoTask data = service.save(task);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新嗅辨任务
     *
     * @param task 嗅辨任务实体
     * @return 更新后的嗅辨任务实体
     */
    @ApiOperation(value = "更新嗅辨任务", notes = "更新嗅辨任务")
    @PutMapping("")
    public RestResponse<DtoTask> update(@Validated @RequestBody DtoTask task) {
        RestResponse<DtoTask> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoTask data = service.update(task);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


    /**
     * 根据id查询嗅辨任务
     *
     * @return
     */
    @ApiOperation(value = "根据id查询嗅辨任务", notes = "根据id查询嗅辨任务")
    @GetMapping("/taskType")
    public RestResponse<List<Map<String,Object>>> findTaskType() {
        RestResponse<List<Map<String,Object>>> restResp = new RestResponse<>();
        restResp.setData(service.findTaskType());
        restResp.setRestStatus(StringUtil.isNull(restResp.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 根据检测单号删除嗅辨任务
     *
     */
    @ApiOperation(value = "根据检测单号删除嗅辨任务", notes = "根据检测单号删除嗅辨任务")
    @DeleteMapping("/deleteByWork/{workSheetCode}")
    public RestResponse<Void> deleteByWork(@PathVariable(name = "workSheetCode") String workSheetCode) {
        RestResponse<Void> restResp = new RestResponse<>();
        service.deleteByWork(workSheetCode);
        return restResp;
    }
}
