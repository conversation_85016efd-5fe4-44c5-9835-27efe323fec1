package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.dto.customer.DtoSampleResultTemp;
import com.sinoyd.lims.od.repository.*;
import com.sinoyd.lims.od.service.LabSeqService;
import com.sinoyd.lims.od.service.OdSampleService;
import com.sinoyd.lims.od.service.SampleResultService;
import com.sinoyd.lims.od.strategy.context.SampleResultContext;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 样品结果接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class SampleResultServiceImpl extends BaseJpaServiceImpl<DtoSampleResult, String, SampleResultRepository> implements SampleResultService {


    private LabSeqService labSeqService;

    private OdSampleService odSampleService;

    private TaskRepository taskRepository;

    private SampleResultContext sampleResultContext;

    private AnalyseDataService analyseDataService;

    /**
     * 根据样品id查询默认实验组
     *
     * @param sampleId 嗅辨样品id
     * @return 默认实验组
     */
    @Override
    public List<DtoGroupSummaryTemp> findDefaultGroupBySampleId(String sampleId) {
        DtoOdSample odSample = odSampleService.findOne(sampleId);
        DtoTask task = taskRepository.findOne(odSample.getTaskId());
        // 根据任务类型获取结果数据
        return sampleResultContext.findDefaultGroupBySampleId(task.getTaskType(), sampleId);
    }


    /**
     * 查询样品结果
     *
     * @param sampleResultTemp 样品结果汇总传输实体
     * @return 样品结果
     */
    @Override
    @Transactional
    public DtoSampleResult calculate(DtoSampleResultTemp sampleResultTemp) {
        DtoOdSample odSample = odSampleService.findOne(sampleResultTemp.getSampleId());
        DtoTask task = taskRepository.findOne(odSample.getTaskId());
        // 根据任务类型获取结果数据
        DtoSampleResult sampleResult = sampleResultContext.calculate(task.getTaskType(), sampleResultTemp);
        // 更新样品结果
        return super.save(sampleResult);
    }

    /**
     * 获取实验组汇总信息
     *
     * @param groupSummaryTemps 实验数据汇总临时传输实体
     * @return 固定源汇总结果
     */
    @Override
    public List<DtoLabGroup> findGroupSummary(List<DtoGroupSummaryTemp> groupSummaryTemps) {
        DtoGroupSummaryTemp groupSummaryTemp = groupSummaryTemps.get(0);
        return sampleResultContext.findGroupSummary(groupSummaryTemp.getTaskType(), groupSummaryTemps);
    }

    @Override
    public DtoSampleResult findBySampleId(String sampleId) {
        DtoSampleResult sampleResult = repository.findBySampleId(sampleId);
        DtoOdSample odSample = odSampleService.findOne(sampleId);
        if (StringUtil.isNull(sampleResult)) {
            sampleResult = new DtoSampleResult();
            sampleResult.setSampleId(sampleId);
        }
        // 样品状态
        sampleResult.setSampleState(odSample.getSampleState());

        return sampleResult;
    }

    @Override
    @Transactional
    public DtoSampleResult complete(String sampleId) {
        DtoSampleResult sampleResult = repository.findBySampleId(sampleId);
        if (StringUtil.isNull(sampleResult) || StringUtil.isEmpty(sampleResult.getOdourConsistence())) {
            throw new BaseException("样品臭气浓度为空，无法完成实验");
        }
        DtoOdSample odSample = odSampleService.completeOdSample(sampleId);
        sampleResult.setSampleState(odSample.getSampleState());
        // 样品结果臭气浓度反推至LIMS
        analyseDataService.updateByOdsSample(odSample.getSampleCode(), sampleResult.getOdourConsistence());
        return sampleResult;
    }


    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        List<DtoLabSeq> labSeqList = labSeqService.findByGroupIdIn(idList);
        if (StringUtil.isNotEmpty(labSeqList)) {
            labSeqService.logicDeleteById(labSeqList.stream().map(DtoLabSeq::getId).collect(Collectors.toList()));
        }
        return super.logicDeleteById(ids);
    }


    @Autowired
    @Lazy
    public void setLabSeqService(LabSeqService labSeqService) {
        this.labSeqService = labSeqService;
    }


    @Autowired
    @Lazy
    public void setTaskRepository(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    @Autowired
    @Lazy
    public void setSampleResultContext(SampleResultContext sampleResultContext) {
        this.sampleResultContext = sampleResultContext;
    }

    @Autowired
    @Lazy
    public void setOdSampleService(OdSampleService odSampleService) {
        this.odSampleService = odSampleService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }
}
