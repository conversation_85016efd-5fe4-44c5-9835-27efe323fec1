package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

/**
 * 混标导入实体
 * <AUTHOR>
 * @version V1.0.0 2022/7/11
 * @since V100R001
 */
@Data
public class DtoImportConsumableOfMixed implements IExcelModel, IExcelDataModel {

    private Integer rowNum;

    private String errorMsg;

    /**
     * 分析项目id
     */
    @Excel(name = "分析项目id",isColumnHidden = true)
    private String analyzeItemId;

    /**
     * 标样id
     */
    @Excel(name = "标样id",isColumnHidden = true)
    private String consumableId;

    /**
     * 量纲id
     */
    @Excel(name = "量纲id",isColumnHidden = true)
    private String dimensionId;


    /**
     * 分析项目名称
     */
    @Excel(name = "分析项目",orderNum = "1",width = 17)
    private String analyzeItemName;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位",orderNum = "2",width = 17)
    private String dimensionName;

    /**
     * 浓度
     */
    @Excel(name = "浓度",orderNum = "3",width = 20)
    private String concentration;

    /**
     * 不确定度
     */
    @Excel(name = "不确定度",orderNum = "4",width = 17)
    private String uncertainty;

    @Excel(name = "范围低点",orderNum = "5",width = 10)
    private String rangeLow;

    @Excel(name = "范围高点",orderNum = "6",width = 10)
    private String rangeHigh;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
