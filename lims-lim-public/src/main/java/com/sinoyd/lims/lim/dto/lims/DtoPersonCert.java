package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.PersonCert;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoPersonCert实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_PersonCert")
 @Data
 @DynamicInsert
 public  class DtoPersonCert extends PersonCert {

    /**
     * base64Content的内容
     */
    @Transient
    private String base64Content;

    /**
     * 人员姓名
     */
    @Transient
    private String personName;

    /**
     * 人员状态 1代表在职 2代表离职 3代表休假  10删除
     */
    @Transient
    private Integer personStatus;
}