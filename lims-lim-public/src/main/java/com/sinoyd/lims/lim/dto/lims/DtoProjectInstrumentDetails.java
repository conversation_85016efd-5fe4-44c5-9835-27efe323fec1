package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.ProjectInstrumentDetails;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoProjectInstrumentDetails实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ProjectInstrumentDetails")
@Data
@DynamicInsert
public class DtoProjectInstrumentDetails extends ProjectInstrumentDetails {
    private static final long serialVersionUID = 1L;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Transient
    private String instrumentsCode;

    /**
     * 规格型号
     */
    @Transient
    private String model;

    /**
     * 仪器名称
     */
    @Transient
    private String factoryName;

    /**
     * 仪器id列表
     */
    @Transient
    private List<String> instrumentIdList;

    /**
     * 入库人姓名
     */
    @Transient
    private String inPersonName;

    /**
     * 出库人姓名
     */
    @Transient
    private String outPersonName;

    /**
     * 仪器出入库记录主表id
     */
    @Transient
    private String projectInstrumentId;

    public DtoProjectInstrumentDetails(String projectInstrumentId, String instrumentName, String instrumentsCode, boolean isStorage) {
        this.setProjectInstrumentId(projectInstrumentId);
        this.instrumentName = instrumentName;
        this.instrumentsCode = instrumentsCode;
        this.setIsStorage(isStorage);
    }

    public DtoProjectInstrumentDetails() {
    }
}