package com.sinoyd.lims.lim.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 版本发布管理实体
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/11/9
 **/
@MappedSuperclass
@ApiModel(description = "PublishSystemVersion")
@Data
@EntityListeners(AuditingEntityListener.class)
public class PublishSystemVersion implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public PublishSystemVersion() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    @Column(length = 50)
    String id = UUIDHelper.NewID();

    /**
     * 标题
     */
    @Column(length = 200, nullable = false)
    @ApiModelProperty("标题")
    @Length(message = "标题{validation.message.length}", max = 200)
    String title;

    /**
     * 版本号
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("版本号")
    @Length(message = "版本号{validation.message.length}", max = 100)
    String versionNum;

    /**
     * 发布人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("发布人")
    @Length(message = "发布人{validation.message.length}", max = 50)
    String publishPerson;

    /**
     * 发布日期
     */
    @DateTimeFormat(pattern = DateUtil.FULL)
    @JsonFormat(pattern = DateUtil.FULL, timezone = "GMT+8")
    @Column(nullable = false)
    @ApiModelProperty("发布日期")
    Date publishDate;

    /**
     * flayway版本
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("flayway版本")
    @Length(message = "flayway版本{validation.message.length}", max = 50)
    String flaywayVersion;

    /**
     * 是否产品 1是 0不是
     */
    @ColumnDefault("1")
    @Column(nullable = false)
    @ApiModelProperty("是否产品")
    Boolean isProduct = true;

    /**
     * 是否发布 1是 0不是
     */
    @ColumnDefault("1")
    @Column(nullable = false)
    @ApiModelProperty("是否发布")
    Boolean isPublish = true;

    /**
     * 是否模板 1是 0不是
     */
    @ColumnDefault("0")
    @Column(nullable = false)
    @ApiModelProperty("是否模板")
    Boolean isTemplate = false;

    /**
     * 是否配置文件 1是 0不是
     */
    @ColumnDefault("0")
    @Column(nullable = false)
    @ApiModelProperty("是否配置文件")
    Boolean isConfig = false;

    /**
     * 更新内容
     */
    @Column(nullable = false)
    @ApiModelProperty("更新内容")
    String updateContent;

    /**
     * 部署注意事项
     */
    @ApiModelProperty("部署注意事项")
    String deployContent;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }
}
