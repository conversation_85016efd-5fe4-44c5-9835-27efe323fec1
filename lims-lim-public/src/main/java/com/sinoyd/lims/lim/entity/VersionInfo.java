package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;


/**
 * VersionInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/9
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "VersionInfo")
@Data
@EntityListeners(AuditingEntityListener.class)
public class VersionInfo implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public VersionInfo() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 版本号
     */
    @Column(length = 50)
    @ApiModelProperty("版本号")
    @Length(message = "版本号{validation.message.length}", max = 50)
    private String version;

    /**
     * 更新内容
     */
    @Column(length = 2000)
    @ApiModelProperty("更新内容")
    @Length(message = "更新内容{validation.message.length}", max = 2000)
    private String verValue;

    /**
     * 附件路径
     */
    @ApiModelProperty("附件路径")
    @Length(message = "附件路径{validation.message.length}", max = 255)
    private String verUrl;

    /**
     * 上传时间
     */
    @Column(nullable = false)
    @ApiModelProperty("上传时间")
    private Date verTime;

    /**
     * 版本类型
     */
    @Column(nullable = false)
    @ApiModelProperty("版本类型")
    @Length(message = "版本类型{validation.message.length}", max = 50)
    private String verType;

    /**
     * 版本二维码
     */
    @ApiModelProperty("版本二维码")
    @Length(message = "版本二维码{validation.message.length}", max = 300)
    private String verCode;

    /**
     * 二维码地址
     */
    @ApiModelProperty("二维码地址")
    @Length(message = "二维码地址{validation.message.length}", max = 3000)
    private String codeUrl;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }
}