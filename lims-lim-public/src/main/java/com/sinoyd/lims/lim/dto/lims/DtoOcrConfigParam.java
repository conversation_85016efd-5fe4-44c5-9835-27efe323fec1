package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.OcrConfigParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoOcrConfigParam
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_lim_ocrConfigParam")
@Data
@DynamicInsert
public class DtoOcrConfigParam extends OcrConfigParam {
    /**
     *参数类型名称
     */
    @Transient
    private String paramTypeName;

    /**
     *分析项目名称
     */
    @Transient
    private String analyzeItemName;
}
