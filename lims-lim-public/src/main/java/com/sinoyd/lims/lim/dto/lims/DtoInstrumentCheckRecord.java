package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.InstrumentCheckRecord;

import java.util.List;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoInstrumentCheckRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_InstrumentCheckRecord")
 @Data
 @DynamicInsert
 public  class DtoInstrumentCheckRecord extends InstrumentCheckRecord {

    @Transient
    private List<String> instrumentIds;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 规格型号
     */
    @Transient
    private String model;

    /**
     * 仪器编号
     */
    @Transient
    private String instrumentsCode;

    /**
     * 出厂编号
     */
    @Transient
    private String serialNo;
}