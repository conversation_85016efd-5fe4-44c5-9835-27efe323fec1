package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;


/**
 * AnalyzeMethod实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "AnalyzeMethod")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AnalyzeMethod implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AnalyzeMethod() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 方法名称
     */
    @ApiModelProperty("方法名称")
    @Length(message = "方法名称{validation.message.length}", max = 255)
    private String methodName;

    /**
     * 标准编号
     */
    @Column(length = 50)
    @ApiModelProperty("标准编号")
    @Length(message = "标准编号{validation.message.length}", max = 200)
    private String countryStandard;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 标准实施日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("标准实施日期")
    private Date effectiveDate;

    /**
     * 受控编号
     */
    @Column(length = 50)
    @ApiModelProperty("受控编号")
    @Length(message = "受控编号{validation.message.length}", max = 50)
    private String methodCode;

    /**
     * 是否可以同时完成
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否可以同时完成")
    private Boolean isCompleteTogether;

    /**
     * 是否受控
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否受控")
    private Boolean isControlled;

    /**
     * 是否制备
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否制备")
    private Boolean isPreparation;

    /**
     * 制备方法
     */
    @Column(length = 200)
    @ApiModelProperty("制备方法")
    private String preparedMethod;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 父级Id（Guid）（预留，例：XXX方法，拉伸测试）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父级Id（Guid）（预留，例：XXX方法，拉伸测试）")
    private String parentId;

    /**
     * 国家标准名称（预留）
     */
    @Column(length = 100)
    @ApiModelProperty("国家标准名称（预留）")
    private String countryStandardName;

    /**
     * 年度（预留）
     */
    @Column(length = 50)
    @ApiModelProperty("年度（预留）")
    private String yearSn;

    /**
     * 有效天数（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("有效天数（预留）")
    private Integer effectiveDays;

    /**
     * 警告天数（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("警告天数（预留）")
    private Integer warningDays;

    /**
     * 是否现行有效（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否现行有效（预留）")
    private Boolean isInforce;

    /**
     * 方法状态EnumAnalyzeMethodStatus：用(1),停用(2),废止(3)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("方法状态EnumAnalyzeMethodStatus：启用(1),停用(2),废止(3)")
    private Integer status;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 是否按样品录入 1：是 0：否
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否按样品录入")
    private Boolean isInputBySample;

    /**
     * 别名
     */
    @Column(length = 50)
    @ApiModelProperty("别名")
    @Length(message = "别名{validation.message.length}", max = 50)
    private String alias;

    /**
     * 是否可跨天完成
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否可跨天完成")
    private Boolean isCrossDay;

    /**
     * 是否采样方法
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否采样方法")
    private Boolean isSamplingMethod = false;

    /**
     * 检测类型id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;


    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

}