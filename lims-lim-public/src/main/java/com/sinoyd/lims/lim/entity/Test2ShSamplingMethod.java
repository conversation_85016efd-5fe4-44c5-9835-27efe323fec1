package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * Test2ShSamplingMethod实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/17
 **/
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Test2ShSamplingMethod")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Test2ShSamplingMethod extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Test2ShSamplingMethod() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键标识
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测试项目id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 检测类型id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 监管平台采样方法id
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台采样方法id")
    private String shSamplingMethodId;

    /**
     * 监管平台采样方法名称
     */
    @Column(length = 255)
    @ApiModelProperty("监管平台采样方法名称")
    @Length(message = "监管平台采样方法名称{validation.message.length}", max = 255)
    private String shSamplingMethodName;

    /**
     * 所属机构ID
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属机构ID")
    private String orgId;

    /**
     * 所属实验室ID
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室ID")
    private String domainId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    @ApiModelProperty("更新人")
    private String modifier;

    /**
     * 更新时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("更新时间")
    private Date modifyDate;

    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }
}
