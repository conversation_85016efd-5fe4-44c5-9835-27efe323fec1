package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoCurveDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 标线信息
 * <AUTHOR>
 * @version V1.0.0 2019/12/26
 * @since V100R001
 */
@Data
public class DtoCurveTemp extends LimsBaseEntity {
    /**
     * 标线id
     */
    private String id = UUIDHelper.NewID();


    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 配置人员id
     */
    private String configPersonId;

    /**
     * 配置人员姓名
     */
    private String configName;

    /**
     * 斜率范围
     */
    private String kRange;

    /**
     * 截距范围
     */
    private String bRange;

    /**
     * 相关系数范围
     */
    private String coefficientRange;

    /**
     * 相关系数
     */
    private String coefficient;

    /**
     * 配置日期
     */
    private Date configDate;

    /**
     * 周期（天）
     */
    private Integer period;

    /**
     * 曲线零点
     */
    private String zeroPoint;

    /**
     * 斜率a
     */
    private String kValue;

    /**
     * 截距b
     */
    private String bValue;

    /**
     * 实数c
     */
    private String cValue;

    /**
     * 是否双曲线
     */
    private Boolean isDouble;

    /**
     * 双曲线名称
     */
    private String doubleName;

    /**
     * 曲线类型
     */
    private Integer curveType;

    /**
     * 曲线模型
     */
    private Integer curveMode;

    /**
     * 测试项目名称
     */
    private String testName;

    /**
     * 斜率有效位数
     */
    private Integer kValueFormat;

    /**
     * 截距有效位数
     */
    private Integer bValueFormat;

    /**
     * 实数有效位数
     */
    private Integer cValueFormat;

    /**
     * 斜率小数位数
     */
    private Integer kDecimalFormat;

    /**
     * 截距小数位数
     */
    private Integer bDecimalFormat;

    /**
     * 实数小数位数
     */
    private Integer cDecimalFormat;

    /**
     * 方程
     */
    private String equation;

    /**
     * 强制零点
     */
    private Boolean forcedZero;

    /**
     * 曲线明细
     */
    private List<DtoCurveDetail> curveDetail = new ArrayList<>();

    /**
     * 曲线信息
     */
    @Length(message = "曲线信息{validation.message.length}", max = 100)
    private String curveInfo;

    /**
     * 关联曲线id
     */
    private String relevanceId = UUIDHelper.GUID_EMPTY;

    /**
     * 空白1
     */
    private String blankOne;


    /**
     * 空白2
     */
    private String blankTwo;

    /**
     * 空白2
     */
    private String blankAvgDimensionId;

    /**
     * 量纲行
     */
    private DtoCurveDetail dimensionRow;
}
