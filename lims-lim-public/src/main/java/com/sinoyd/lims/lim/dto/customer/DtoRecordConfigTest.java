package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

/**
 * 测试项目相关的记录单
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Data
public class DtoRecordConfigTest {

    /**
     * 记录单id
     */
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 报表id
     */
    private String reportConfigId;


    /**
     * 记录单名称
     */
    private String recordName;


    /**
     * 记录单编号
     */
    private String recordCode;

    /**
     * 获取测试项目相关的数据
     *
     * @param id             主键id
     * @param testId         测试项目id
     * @param reportConfigId 模板id
     * @param recordName     记录单名称
     * @param recordCode     记录单编号
     */
    public DtoRecordConfigTest(String id,
                               String testId,
                               String reportConfigId,
                               String recordName,
                               String recordCode) {
        this.setId(id);
        this.setTestId(testId);
        this.setReportConfigId(reportConfigId);
        this.setRecordName(recordName);
        this.setRecordCode(recordCode);
    }
}
