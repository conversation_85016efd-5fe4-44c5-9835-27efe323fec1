package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

/**
 * 订单明细点位导入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/4/8
 * @since V100R001
 */
@Data
public class DtoImportOrderFolder extends PoiBaseEntity {

    @Excel(name = "点位名称", orderNum = "20", width = 17)
    private String folderName;

}
