package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * InstrumentInspect实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="InstrumentInspect")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class InstrumentInspect implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  InstrumentInspect() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 仪器Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器Id")
	private String instrumentId;
    
    /**
    * 期间核查人员id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("期间核查人员id")
    private String inspectPersonId;
    
    /**
    * 期间核查人员
    */
    @Column(length=50)
    @ApiModelProperty("期间核查人员")
    @Length(max = 50, message = "期间核查人员长度不能超过50")
	private String inspectPerson;
    
    /**
    * 期间核查时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("期间核查时间")
    private Date inspectTime;
    
    /**
    * 期间核查内容
    */
    @Column(length=1000)
    @ApiModelProperty("期间核查内容")
    @Length(max = 1000, message = "期间核查内容长度不能超过1000")
	private String inspectContent;
    
    /**
    * 期间核查结果(枚举：EnumInspectResult：1合格、0不合格)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("期间核查结果(枚举：EnumInspectResult：1合格、0不合格)")
    private Integer inspectResult;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(max = 1000, message = "备注长度不能超过1000")
	private String remark;
    
    /**
    * 费用
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @Digits(integer=18,fraction=2,message="费用整数位精度18小数位经度2")
    @ApiModelProperty("费用")
    private BigDecimal cost;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }