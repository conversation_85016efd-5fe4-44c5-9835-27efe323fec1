package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Test实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/5
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Test")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Test extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Test() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 父级Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父级Id")
    private String parentId;

    /**
     * 分析方法Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析方法Id（Guid）")
    private String analyzeMethodId;

    /**
     * 分析方法名称
     */
    @ApiModelProperty("分析方法名称")
    @Length(message = "分析方法名称{validation.message.length}", max = 255)
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    @Column(length = 100)
    @ApiModelProperty("国家标准")
    private String redCountryStandard;

    /**
     * 分析项目Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析项目Id（Guid）")
    private String analyzeItemId;

    /**
     * 分析项目名称
     */
    @Column(length = 100)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 100)
    private String redAnalyzeItemName;

    /**
     * 分析项目全拼
     */
    @ApiModelProperty("分析项目全拼")
    @Length(message = "分析项目全拼{validation.message.length}", max = 255)
    private String fullPinYin;

    /**
     * 分析项目拼音缩写
     */
    @Column(length = 100)
    @ApiModelProperty("分析项目拼音缩写")
    @Length(message = "分析项目拼音缩写{validation.message.length}", max = 100)
    private String pinYin;

    /**
     * 样品类型（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("样品类型（Guid）")
    private String sampleTypeId;

    /**
     * 测试编码
     */
    @Column(length = 50)
    @ApiModelProperty("测试编码")
    @Length(message = "测试编码{validation.message.length}", max = 50)
    private String testCode;

    /**
     * 计量单位（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位（Guid）")
    private String dimensionId;

    /**
     * 检出限
     */
    @Column(length = 50)
    @ApiModelProperty("检出限")
    @Length(message = "检出限{validation.message.length}", max = 50)
    private String examLimitValue;

    /**
     * 样品有效期（h）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("样品有效期（h）")
    private BigDecimal validTime = new BigDecimal(-1);

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum = 0;

    /**
     * 有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("有效位数")
    private Integer mostSignificance = -1;

    /**
     * 小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("小数位数")
    private Integer mostDecimal = -1;

    /**
     * 测试资质(枚举EnumCert：0非认可认证、1认证、2认可、4认证认可）
     */
    @Column(nullable = false)
    @ColumnDefault("4")
    @ApiModelProperty("测试资质(枚举EnumCert：0非认可认证、1认证、2认可、4认证认可）")
    private Integer cert = 4;

    /**
     * 测试名称
     */
    @Column(length = 1000)
    @ApiModelProperty("测试名称")
    @Length(message = "测试名称{validation.message.length}", max = 1000)
    private String testName;

    /**
     * 是否采测分包
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否采测分包")
    private Boolean isOutsourcing = false;

    /**
     * 是否分析分包
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否分析分包")
    private Boolean isSamplingOut = false;


    /**
     * 是否现场数据
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否现场数据")
    private Boolean isCompleteField = false;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 是否做质控平行
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否做质控平行")
    private Boolean isQCP = false;

    /**
     * 是否做质控空白
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否做质控空白")
    private Boolean isQCB = false;

    /**
     * 是否做串联样
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否做串联样")
    private Boolean isSeries = false;

    /**
     * 是否做运输空白
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否做运输空白")
    private Boolean isQCTransport = false;

    /**
     * 是否做设备空白
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否做设备空白")
    private Boolean isQCInstrument = false;

    /**
     * 是否做现场空白
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否做现场空白")
    private Boolean isQCLocal = false;

    /**
     * 是否启用公式
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否启用公式")
    private Boolean isUseFormula = true;

    /**
     * 小于检出限出证结果
     */
    @Column(length = 50)
    @ApiModelProperty("小于检出限出证结果")
    @Length(message = "小于检出限出证结果{validation.message.length}", max = 50)
    private String examLimitValueLess;


    /**
     * 批次
     */
    @ColumnDefault("1")
    @ApiModelProperty("批次")
    private Integer timesOrder;

    /**
     * 样品数量
     */
    @ColumnDefault("1")
    @ApiModelProperty("样品数量")
    private Integer samplePeriod;

    /**
     * 测定下限
     */
    @Column(length = 50)
    @ApiModelProperty("测定下限")
    @Length(message = "测定下限{validation.message.length}", max = 50)
    private String lowerLimit;

    /**
     * 总称（冗余分析项目名称）
     */
    @Column(length = 1000)
    @ApiModelProperty("总称（冗余分析项目名称）")
    @Length(message = "总称{validation.message.length}", max = 1000)
    private String totalTestName;

    /**
     * 是否显示总称
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否显示总称")
    private Boolean isShowTotalTest = false;

    /**
     * 是否总称
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否总称")
    private Boolean isTotalTest = false;

    /**
     * 是否启用嵌套公式(检测结果计算出证结果)默认false
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否启用嵌套公式(检测结果计算出证结果)默认false")
    private Boolean isUseQTFormula = false;

    /**
     * 斜率有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("斜率有效位数")
    private Integer kValueFormat = -1;

    /**
     * 斜率小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("斜率小数位数")
    private Integer kDecimalFormat = -1;

    /**
     * 截距有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("截距有效位数")
    private Integer bValueFormat = -1;

    /**
     * 截距小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("截距小数位数")
    private Integer bDecimalFormat = -1;

    /**
     * 实数有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("实数有效位数")
    private Integer cValueFormat = -1;

    /**
     * 实数小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("实数小数位数")
    private Integer cDecimalFormat = -1;

    /**
     * 采样费金额
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("采样费金额")
    private BigDecimal samplingCharge = new BigDecimal(0);

    /**
     * 检测费金额
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("检测费金额")
    private BigDecimal testingCharge = new BigDecimal(0);

    /**
     * 报告计量单位（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("报告计量单位（Guid）")
    private String reportDimensionId;

    /**
     * 报告有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("报告有效位数")
    private Integer reportMostSignificance = -1;

    /**
     * 报告小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("报告小数位数")
    private Integer reportMostDecimal = -1;

    /**
     * 备注（预留）
     */
    @Column(length = 1000)
    @ApiModelProperty("备注（预留）")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 是否填写仪器使用记录（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否填写仪器使用记录（预留）")
    private Boolean isInsUseRecord = false;

    /**
     * 是否是检测机构传输过来的测试项目（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否是检测机构传输过来的测试项目（预留）")
    private Boolean isSubSync = false;

    /**
     * 录入方式（预留）（常量Int，常量名称Lim_TestInputMode）（0：默认，1：生物多样性）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("录入方式（预留）（常量Int，常量名称Lim_TestInputMode）（0：默认，1：生物多样性）")
    private Integer inputMode = 0;

    /**
     * 均值计算方式（0：算术均值，1：原样值，2：几何均值）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("均值计算方式（0：算术均值，1：原样值，2：几何均值）")
    private Integer averageCompute = 0;

    /**
     * 实验室编号（预留）
     */
    @Column(length = 50)
    @ApiModelProperty("实验室编号（预留）")
    @Length(message = "实验室编号{validation.message.length}", max = 50)
    private String domainCode;

    /**
     * 年份（预留）
     */
    @Column(length = 100)
    @ApiModelProperty("年份（预留）")
    @Length(message = "年份{validation.message.length}", max = 100)
    private String redYearSn;

    /**
     * 分配时长（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("分配时长（预留）")
    private Integer testTimelen = -1;

    /**
     * 基础工作量（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("基础工作量（预留）")
    private BigDecimal basicWorkload = new BigDecimal(-1);

    /**
     * 单位工作量（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("单位工作量（预留）")
    private BigDecimal unitWorkload = new BigDecimal(-1);

    /**
     * 关联系统测试项目编号
     */
    @Column(length = 50)
    @ApiModelProperty("关联系统测试项目编号")
    @Length(message = "关联系统测试项目编号{validation.message.length}", max = 100)
    private String externalId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 修约方式（参考枚举 EnumReviseType）（1：先修约再比较，2：先比较再修约）
     */
    @Column(nullable = false)
    @ColumnDefault("2")
    @ApiModelProperty("修约方式（参考枚举 EnumReviseType）（1：先修约再比较，2：先比较再修约）")
    private Integer reviseType;

    /**
     * 计算方式, 参考枚举 EnumCalculateWay（0： 先修约后计算，1：先计算后修约）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("计算方式, 参考枚举 EnumCalculateWay（0： 先修约后计算，1：先计算后修约）")
    private Integer calculateWay = 0;

    /**
     * 合并基数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("合并基数")
    private Integer mergeBase = 0;

    /**
     * 分析时长（天数）
     */
    @Column(nullable = false)
    @ColumnDefault("2")
    @ApiModelProperty("分析时长")
    private Integer analyseDayLen = 5;

    /**
     * 验证状态 0未验证 1已验证
     */
    @ColumnDefault("0")
    @ApiModelProperty("验证状态 0未验证 1已验证")
    private Integer validate;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer usageNum;

    /**
     * 空气污染物
     */
    @ApiModelProperty("空气污染物")
    @Length(message = "空气污染物{validation.message.length}", max = 100)
    private String airPollution;

    /**
     * 是否作废
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否作废")
    private Boolean isAbolish = false;

    /**
     * 是否资源中心作废
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否资源中心作废")
    private Boolean isRccAbolish = false;

    /**
     * 是否科学计数法
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否科学计数法")
    private Boolean isSci = false;

    /**
     * 报告浓度计算方式(EnumComputeMode)
     */
    @ApiModelProperty("报告浓度计算方式(EnumComputeMode)")
    private Integer potencyComputeMode;

    /**
     * 有效期提示说明
     */
    @ApiModelProperty("有效期提示说明")
    @Length(message = "有效期提示说明{validation.message.length}", max = 1000)
    private String tips;

    /**
     * 监管平台分析方法id
     */
    @Column(length = 25)
    @ColumnDefault("")
    @ApiModelProperty("监管平台分析项目id")
    @Length(message = "监管平台分析项目id{validation.message.length}", max = 50)
    private String shMethodId;

    /**
     * 监管平台分析方法名称
     */
    @Column(length = 25)
    @ColumnDefault("")
    @ApiModelProperty("监管平台分析项目名称")
    @Length(message = "监管平台分析项目名称{validation.message.length}", max = 255)
    private String shMethodName;

//    /**
//     * 监管平台采样方法id
//     */
//    @Column(length = 25)
//    @ColumnDefault("")
//    @ApiModelProperty("监管平台采样方法id")
//    @Length(message = "监管平台采样方法id{validation.message.length}", max = 50)
//    private String shSamplingMethodId;
//
//    /**
//     * 监管平台采样方法名称
//     */
//    @Column(length = 25)
//    @ColumnDefault("")
//    @ApiModelProperty("监管平台采样方法名称")
//    @Length(message = "监管平台采样方法名称{validation.message.length}", max = 255)
//    private String shSamplingMethodName;


    /**
     * 采样方法方法Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样方法方法Id（Guid）")
    private String samplingMethodId;

    /**
     * 上海监管平台分析方法匹配状态，枚举管理{@link com.sinoyd.lims.lim.enums.EnumLIM.EnumShTestMatchStatus}
     */
    @ColumnDefault("0")
    @ApiModelProperty("验证状态 0未匹配 1异常匹配 2完美匹配")
    private Integer shMatchStatus;

    /**
     * 上海监管平台分析方法匹配信息
     */
    @Column
    @ApiModelProperty("上海监管平台分析方法匹配信息")
    private String shMatchMessage;

    /**
     * 是否嗅辨
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否嗅辨")
    private Boolean isOd = false;

    /**
     * 是否出力系数折算默认false
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否出力系数折算默认false")
    private Boolean coefficientConversion = false;

    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }
}