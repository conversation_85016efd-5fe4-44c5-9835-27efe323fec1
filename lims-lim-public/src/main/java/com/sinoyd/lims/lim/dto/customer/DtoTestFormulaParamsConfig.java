package com.sinoyd.lims.lim.dto.customer;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 测试项目公式参数
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
@Data
public class DtoTestFormulaParamsConfig {

    /**
     * 排序号
     */
    private Integer orderNum;

    /**
     * 参数别名
     */
    private String alias;


    /**
     * 是否必填
     */
    private Boolean isMust;


    /**
     * 是否允许修改
     */
    private Boolean isEditable;

    /**
     * 是否需要进行计算
     */
    private Boolean isCalculate;

    /**
     * 公式参数检出限
     */
    private String detectionLimit;

    /**
     * 计算方式：枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零
     */
    private Integer calculationMode;

    /**
     * 斜杠计算值
     */
    private Integer slashValue;


    /**
     * 量纲
     */
    private String dimension;
}
