package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoCurveTemp;
import com.sinoyd.lims.lim.entity.Curve;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * DtoCurve实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Curve")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoCurve extends Curve {
    public void loadFromTemp(DtoCurveTemp temp) {
        this.setId(temp.getId());
        if (StringUtil.isNotNull(temp.getTestId())) {
            this.setTestId(temp.getTestId());
        }

        if (StringUtil.isNotNull(temp.getConfigPersonId())) {
            this.setConfigPersonId(temp.getConfigPersonId());
        }

        if (StringUtil.isNotNull(temp.getConfigName())) {
            this.setConfigName(temp.getConfigName());
        }

        if (StringUtil.isNotNull(temp.getKRange())) {
            this.setKRange(temp.getKRange());
        }

        if (StringUtil.isNotNull(temp.getBRange())) {
            this.setBRange(temp.getBRange());
        }

        if (StringUtil.isNotNull(temp.getCoefficientRange())) {
            this.setCoefficientRange(temp.getCoefficientRange());
        }

        if (StringUtil.isNotNull(temp.getCoefficient())) {
            this.setCoefficient(temp.getCoefficient());
        }

        if (StringUtil.isNotNull(temp.getConfigDate())) {
            this.setConfigDate(temp.getConfigDate());
        }

        if (StringUtil.isNotNull(temp.getPeriod())) {
            this.setPeriod(temp.getPeriod());
        }

        if (StringUtil.isNotNull(temp.getZeroPoint())) {
            this.setZeroPoint(temp.getZeroPoint());
        }

        if (StringUtil.isNotNull(temp.getKValue())) {
            this.setKValue(temp.getKValue());
        }

        if (StringUtil.isNotNull(temp.getBValue())) {
            this.setBValue(temp.getBValue());
        }

        if (StringUtil.isNotNull(temp.getCValue())) {
            this.setCValue(temp.getCValue());
        }

        if (StringUtil.isNotNull(temp.getIsDouble())) {
            this.setIsDouble(temp.getIsDouble());
        }

        if (StringUtil.isNotNull(temp.getCurveType())) {
            this.setCurveType(temp.getCurveType());
        }

        if (StringUtil.isNotNull(temp.getDoubleName())) {
            this.setDoubleName(temp.getDoubleName());
        }

        if (StringUtil.isNotNull(temp.getCurveMode())) {
            this.setCurveMode(temp.getCurveMode());
        }

        if (StringUtil.isNotNull(temp.getKValueFormat())) {
            this.kValueFormat = temp.getKValueFormat();
        }

        if (StringUtil.isNotNull(temp.getBValueFormat())) {
            this.bValueFormat = temp.getBValueFormat();
        }

        if (StringUtil.isNotNull(temp.getCValueFormat())) {
            this.cValueFormat = temp.getCValueFormat();
        }

        if (StringUtil.isNotNull(temp.getKDecimalFormat())){
            this.kDecimalFormat = temp.getKDecimalFormat();
        }

        if (StringUtil.isNotNull(temp.getBDecimalFormat())){
            this.bDecimalFormat = temp.getBDecimalFormat();
        }

        if (StringUtil.isNotNull(temp.getCDecimalFormat())){
            this.cDecimalFormat = temp.getCDecimalFormat();
        }

        if(StringUtil.isNotNull(temp.getForcedZero())){
            this.setForcedZero(temp.getForcedZero());
        }
        if (StringUtil.isNotNull(temp.getCurveInfo())){
            this.setCurveInfo(temp.getCurveInfo());
        }
        if (StringUtil.isNotNull(temp.getRelevanceId())){
            this.setRelevanceId(temp.getRelevanceId());
        }
        if (StringUtil.isNotNull(temp.getDbExtendMap())){
            this.setDbExtendMap(temp.getDbExtendMap());
        }
        if (StringUtil.isNotNull(temp.getBlankOne())){
            this.setBlankOne(temp.getBlankOne());
        }
        if (StringUtil.isNotNull(temp.getBlankTwo())){
            this.setBlankTwo(temp.getBlankTwo());
        }
        if (StringUtil.isNotNull(temp.getBlankAvgDimensionId())){
            this.setBlankAvgDimensionId(temp.getBlankAvgDimensionId());
        }
    }

    /*
     * 分析项目
     */
    @Transient
    private String redAnalyzeItemName;

    /*
     * 分析方法
     */
    @Transient
    private String redAnalyzeMethodName;

    /*
     * 国家标准
     */
    @Transient
    private String redCountryStandard;

    /*
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 斜率有效位数
     */
    @Transient
    private Integer kValueFormat;

    /**
     * 斜率小数位数
     */
    @Transient
    private Integer kDecimalFormat;

    /**
     * 截距有效位数
     */
    @Transient
    private Integer bValueFormat;

    /**
     * 截距小数位数
     */
    @Transient
    private Integer bDecimalFormat;

    /**
     * 实数有效位数
     */
    @Transient
    private Integer cValueFormat;

    /**
     * 实数小数位数
     */
    @Transient
    private Integer cDecimalFormat;

    /*
     * 有效日期
     */
    @Transient
    private Date expireDate;

    /*
     * 方程
     */
    @Transient
    private String equation;

    /*
     * 量纲行
     */
    @Transient
    private DtoCurveDetail dimensionRow;
}