package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;


/**
 * ParamsConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ParamsConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ParamsConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ParamsConfig() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 对象id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("对象id")
    private String objId;

    /**
     * 参数id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("参数id")
    private String paramsId;

    /**
     * 参数使用名称
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("参数使用名称")
    @Length(message = "参数使用名称{validation.message.length}", max = 50)
    private String alias;

    /**
     * 默认值
     */
    @Column(length = 50)
    @ApiModelProperty("默认值")
    @Length(message = "默认值{validation.message.length}", max = 50)
    private String defaultValue;

    /**
     * 计量单位
     */
    @Column(length = 50)
    @ApiModelProperty("计量单位")
    @Length(message = "计量单位{validation.message.length}", max = 50)
    private String dimension;

    /**
     * 计量单位id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位id")
    private String dimensionId;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 对象类型（枚举EnumParamsConfigType：1.检测（样品）类型，2.测试项目，3.企业（预留）,4.方法（预留）,5.采样单（预留）6.原始记录单7.报告（预留）8.样品-采样单参数也是检测（样品）类型上的参数，只是进行设置用到采样单分组（检测（样品）类型公共参数）9.原始记录单-检测单参数（原始记录单上面部分的计算，如化学需氧量、BOD5）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("对象类型（枚举EnumParamsConfigType：1.检测（样品）类型，2.测试项目，3.企业（预留）,4.方法（预留）,5.采样单（预留）6.原始记录单7.报告（预留）8.样品-采样单参数也是检测（样品）类型上的参数，只是进行设置用到采样单分组（检测（样品）类型公共参数）9.原始记录单-检测单参数（原始记录单上面部分的计算，如化学需氧量、BOD5）")
    private Integer type;

    /**
     * 默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件
     * ）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）")
    private Integer defaultControl;

    /**
     * 数据源
     */
    @Column(length = 4000)
    @ApiModelProperty("数据源")
    @Length(message = "数据源{validation.message.length}", max = 4000)
    private String dataSource;

    /**
     * isDeleted
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("isDeleted")
    private Boolean isDeleted = false;

    /**
     * 是否必填（用于测试公式，检测类型参数）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否必填（用于测试公式，检测类型参数）")
    private Boolean isRequired;

    /**
     * 有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("有效位数")
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("小数位数")
    private Integer mostDecimal;

    /**
     * 分析项目Id（用于检测类型相关分析项目）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目Id（用于检测类型相关分析项目）")
    private String analyzeItemId;

    /**
     * 父节点Id（用于检测类型相关分析项目）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父节点Id（用于检测类型相关分析项目）")
    private String parentId;

    /**
     * 页面上是否显示，默认显示
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("页面上是否显示，默认显示")
    private Boolean isShow;

    /**
     * 是否有公式，有配置公式更新这个字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否有公式，有配置公式更新这个字段")
    private Boolean isFormula;

    /**
     * 公式Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("公式Id")
    private String formulaId;

    /**
     * 是否所有参数配置完成（用于记录单配置参数颜色区分）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否所有参数配置完成（用于记录单配置参数颜色区分）")
    private Boolean isAllConfig;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 参数类型（枚举EnumParamsType：1.公共参数、2.样品参数、3.分析项目参数、4.点位参数）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("参数类型（枚举EnumParamsType：1.公共参数、2.样品参数、3.分析项目参数、4.点位参数）")
    private Integer paramsType;

    /**
     * 参考文本
     */
    @Column(nullable = false)
    @ApiModelProperty("参考文本")
    @Length(message = "参考文本{validation.message.length}", max = 1000)
    private String referenceText;


    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

}