package com.sinoyd.lims.lim.enums;


import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.customer.DtoAnalyzeCertHistory;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentHistory;
import com.sinoyd.lims.lim.dto.customer.DtoSamplingCertHistory;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * EnumLIM(枚举类)
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/14
 * @since V100R001
 */
public class EnumLIM {

    /**
     * 南通统计表的数据类型
     */
    public enum EnumNTStatisticDataType {
        样品数据("sampleList"),
        样品编号("sampleIds"),
        采样人数据("samplingPersons"),
        参数数据("paramsDataList"),
        点位数据("sampleFolderList"),
        点位编号("sampleFolderIds"),
        点位签到数据("folderSignList"),
        例行点位数据("fixedPointList"),
        例行点位编号("fixedPointIds"),
        测站编号("stationIds"),
        测站数据("stationList"),
        质控数据("qualityControls"),
        监测计划编号("propertyIds"),
        监测计划数据("properties"),
        监测计划关联项目("project2FixedProperties"),
        监测计划关联点位("property2PointsByPoint"),
        评价标准记录数据("evaluationRecordList"),
        评价标准等级数据("evaluationLevelList"),
        评价标准限值数据("evaluationValueList"),
        分析数据("analyseDataList"),
        分析详细数据("analyseDetailDataList"),
        项目编号("projectIds"),
        项目数据("projectList"),
        项目类型数据("projectTypeList"),
        项目类型编号("projectTypeIds"),
        比对数据("sampleJudgeDataList"),
        比对数据类型("sampleJudgeType");
        private String keyCode;

        public String getCode() {
            return keyCode;
        }

        EnumNTStatisticDataType(String keyCode) {
            this.keyCode = keyCode;
        }
    }

    /**
     * 偏差公式
     */
    public enum EnumDeviationFormula {
        公式1("Abs([a]-[b])/([a]+[b])", "|(a-b)|/(a+b)"),
        公式2("Abs([a]-[b])/[a]", "|(a-b)|/a"),
        公式3("Abs([a]-[b])/(([a]+[b])/2)", "|(a-b)|/((a+b)/2)");

        private String value;
        private String code;

        private EnumDeviationFormula(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public static String getValueByCode(String code) {
            for (EnumDeviationFormula c : EnumDeviationFormula.values()) {
                if (c.code.equals(code)) {
                    return c.value;
                }
            }
            return "";
        }

        public static String getCodeByValue(String value) {
            for (EnumDeviationFormula c : EnumDeviationFormula.values()) {
                if (c.value.equals(value)) {
                    return c.code;
                }
            }
            return "";
        }


    }

    /**
     * 偏差质控类型
     */
    @Getter
    public enum EnumQualityControlDeviationType {
        默认(0, -1, -1),
        曲线校核(1, 2, 64),
        室内平行(2, 2, 1),
        校正系数检验(3, 2, 4096);

        private Integer code;
        private Integer qcGrade;
        private Integer qcType;

        EnumQualityControlDeviationType(Integer code, Integer qcGrade, Integer qcType) {
            this.code = code;
            this.qcGrade = qcGrade;
            this.qcType = qcType;
        }

        public static EnumQualityControlDeviationType getByCode(Integer code) {
            for (EnumQualityControlDeviationType c : EnumQualityControlDeviationType.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }

        public static EnumQualityControlDeviationType getByQcGradeAndQcType(Integer qcGrade, Integer qcType) {
            for (EnumQualityControlDeviationType c : EnumQualityControlDeviationType.values()) {
                if (c.getQcGrade().equals(qcGrade) && c.getQcType().equals(qcType)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 日历日期类型
     */
    public enum EnumCalendarDateType {

        工作日(0),
        休息日(1);

        private Integer value;

        private EnumCalendarDateType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }


    }

    /**
     * 是否
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumBoolenType {
        所有(-1),
        否(0),
        是(1);
        private Integer value;
    }


    /**
     * 分析项目关系类型
     */
    public enum EnumAnalyzeItemRelationType {

        自检(1),
        上报(2);

        private Integer value;

        private EnumAnalyzeItemRelationType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumAnalyzeItemRelationType(Integer value) {
            for (EnumAnalyzeItemRelationType c : EnumAnalyzeItemRelationType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 收款状态
     */
    public enum EnumCollectionStatus {

        未收款(0),
        部分收款(1),
        已收款(2);

        private Integer value;

        private EnumCollectionStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumCollectionStatus(Integer value) {
            for (EnumCollectionStatus c : EnumCollectionStatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 合同状态
     */
    public enum EnumContractStatus {

        未签(0),
        已签(1);

        private Integer value;

        private EnumContractStatus(int value) {
            this.value = value;
        }

        /**
         * 根据常量的Int值返回常量名称
         *
         * @param value
         * @return
         */
        public static String getName(Integer value) {
            for (EnumContractStatus contractStatus : EnumContractStatus.values()) {
                if (value.equals(contractStatus.getValue())) {
                    return contractStatus.name();
                }
            }
            return null;
        }

        /**
         * 获取枚举的Int值
         *
         * @return
         */
        public int getValue() {
            return value;
        }

    }

    /**
     * 控件类型
     */
    @Getter
    @AllArgsConstructor
    public enum EnumDefaultControl {

        文本控件(1, ""),
        日期控件(2, ""),
        数字控件(3, ""),
        下拉框控件(4, ""),
        RadioGroup控件(5, ""),
        CheckBoxGroup控件(6, ""),
        日期时间控件(7, "yyyy-MM-dd HH:mm:ss"),
        文本区域控件(8, ""),
        时间控件(9, "HH:mm:ss"),
        开关控件(10, ""),
        下拉树控件(11, ""),
        日期时间控件无秒(12, "yyyy-MM-dd HH:mm"),
        时间控件无秒(13, "HH:mm");


        private final Integer value;

        /**
         * 日期控件的格式化字符串
         */
        private final String remark;

        /**
         * 根据枚举值获取枚举项
         *
         * @param value 枚举值
         * @return 控件类型枚举
         */
        public static EnumDefaultControl getEnumItem(Integer value) {
            for (EnumDefaultControl defaultControl : EnumDefaultControl.values()) {
                if (value.equals(defaultControl.getValue())) {
                    return defaultControl;
                }
            }
            return null;
        }

        /**
         * 根据常量的Int值返回常量名称
         *
         * @param value 控件类型枚举值
         * @return 控件类型枚举名称
         */
        public static String getName(Integer value) {
            for (EnumDefaultControl defaultControl : EnumDefaultControl.values()) {
                if (value.equals(defaultControl.getValue())) {
                    return defaultControl.name();
                }
            }
            return null;
        }
    }

    /**
     * 多数据源控件的数据源类型
     */
    public enum EnumControlDataSourceType {

        无(0),
        接口请求(1),
        常量数据(2),
        自定义数据(3);


        private Integer value;

        private EnumControlDataSourceType(int value) {
            this.value = value;
        }

        /**
         * 根据常量的Int值返回常量名称
         *
         * @param value
         * @return
         */
        public static String getName(Integer value) {
            for (EnumControlDataSourceType defaultControl : EnumControlDataSourceType.values()) {
                if (value.equals(defaultControl.getValue())) {
                    return defaultControl.name();
                }
            }
            return null;
        }

        /**
         * 获取枚举的Int值
         *
         * @return
         */
        public Integer getValue() {
            return value;
        }

    }


    /**
     * 支出种类
     */
    public enum EnumExpenditureCategory {

        支出(1),
        收入(2);

        private Integer value;

        private EnumExpenditureCategory(int value) {
            this.value = value;
        }

        /**
         * 根据常量的Int值返回常量名称
         *
         * @param value
         * @return
         */
        public static String getName(Integer value) {
            for (EnumExpenditureCategory expenditureCategory : EnumExpenditureCategory.values()) {
                if (value.equals(expenditureCategory.getValue())) {
                    return expenditureCategory.name();
                }
            }
            return null;
        }

        /**
         * 获取枚举的Int值
         *
         * @return
         */
        public int getValue() {
            return value;
        }

    }

    /**
     * 申请类型（文档管理）
     */
    public enum EnumFileControlApplyType {

        受控申请(1),
        修订申请(2),
        废止申请(3);


        private Integer value;

        private EnumFileControlApplyType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumFileControlApplyType(Integer value) {
            for (EnumFileControlApplyType c : EnumFileControlApplyType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 申请类型流程状态
     */
    public enum EnumFileControlStatus {

        未受控(1),
        受控申请中(2),
        修订中(3),
        废止中(4),
        已受控(5),
        已废止(6),
        已修订(7);

        private Integer value;

        private EnumFileControlStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumFileControlStatus(Integer value) {
            for (EnumFileControlStatus c : EnumFileControlStatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 分组规则
     */
    public enum EnumGroupType {

        分组规则(1),
        分组(2);

        private Integer value;

        private EnumGroupType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumGroupType(Integer value) {
            for (EnumGroupType c : EnumGroupType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }


    }

    /**
     * 收付款状态
     */
    public enum EnumMoneyType {

        收款(1),
        付款(2),
        坏账(3);

        private Integer value;

        private EnumMoneyType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumMoneyType(Integer value) {
            for (EnumMoneyType c : EnumMoneyType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }


    }


    /**
     * 参数类型-用于检测类型参数
     */
    public enum EnumParamsType {

        公共参数(1),
        样品参数(2),
        分析项目参数(3),
        点位参数(4);

        private Integer value;

        private EnumParamsType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumParamsType(Integer value) {
            for (EnumParamsType c : EnumParamsType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 参数对象类型
     */
    public enum EnumParamsConfigType {

        样品参数(1),
        分析项目参数(2),
        企业参数(3),
        方法参数(4),
        采样单参数(5),
        原始记录单数据参数(6),
        报告参数(7),
        采样单分组样品参数(8),
        原始记录单表头参数(9);


        private Integer value;

        private EnumParamsConfigType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumParamsConfigType(Integer value) {
            for (EnumParamsConfigType c : EnumParamsConfigType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }


    }

    /**
     * 测试资质
     */
    public enum EnumTestCert {

        非认证认可(0),
        认证(1),
        认可(2),
        认证认可(4);


        private Integer value;

        private EnumTestCert(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumTestCert(Integer value) {
            for (EnumTestCert c : EnumTestCert.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 付款状态
     */
    public enum EnumPaySatus {

        未付款(0),
        部分付款(1),
        已付款(2);

        private Integer value;

        private EnumPaySatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumPaySatus(Integer value) {
            for (EnumPaySatus c : EnumPaySatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 测试项目是否配置质控限值
     */
    public enum EnumQCRangeStatus {

        所有(-1),
        已配置(1),
        未配置(2);

        private Integer value;

        private EnumQCRangeStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumQCRangeStatus(Integer value) {
            for (EnumQCRangeStatus c : EnumQCRangeStatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    /**
     * 公告状态
     */
    public enum EnumNoticeMsgStatus {
        正常(1),
        置顶(2),
        精华(3),
        假删(4);

        private Integer value;

        private EnumNoticeMsgStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static String EnumNoticeMsgStatus(Integer value) {
            for (EnumNoticeMsgStatus c : EnumNoticeMsgStatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }


    /**
     * 可配置编号的类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumIdentifierConfig {
        项目编号(1),
        样品编号(2),
        质控样编号(3),
        送样单编号(4),
        报告编号(5),
        检测单编号(6),
        质控任务编号(7),
        订单编号(8),
        合同编号(9),
        回收报告编号(10);
        private Integer value;

        public static EnumIdentifierConfig getEnumIdentifierConfig(Integer value) {
            for (EnumIdentifierConfig c : EnumIdentifierConfig.values()) {
                if (c.value.equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 人员的状态（离职，在职，休假）
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumPersonStatus {
        在职(1),
        离职(2),
        休假(3);
        private Integer value;

        public static EnumPersonStatus getEnumPerson(Integer value) {
            for (EnumPersonStatus c : EnumPersonStatus.values()) {
                if (c.value.equals(value)) {
                    return c;
                }
            }
            return null;
        }

        public static String getName(Integer value) {
            EnumPersonStatus personStatus = getEnumPerson(value);
            if (StringUtil.isNotNull(personStatus)) {
                return personStatus.name();
            }
            return "";
        }
    }

    /**
     * 人员的性别（男, 女）枚举EnumSex
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSex {
        男(1),
        女(2);
        private Integer value;

        public static EnumSex getEnumSex(Integer value) {
            for (EnumSex c : EnumSex.values()) {
                if (c.value.equals(value)) {
                    return c;
                }
            }
            return null;
        }

        public static String getName(Integer value) {
            EnumSex sex = getEnumSex(value);
            if (StringUtil.isNotNull(sex)) {
                return sex.name();
            }
            return "";
        }
    }

    /**
     * 环境记录对象类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumEnvRecObjType {
        所有(-1),
        采样(1),
        实验室分析(2),
        现场分析(4);
        private Integer value;
    }

    /**
     * 曲线类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCurveType {
        直线型(0),
        Log型(1),
        二次型(2);
        private Integer value;
    }

    /**
     * 曲线模型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCurveModel {
        普通(0),
        紫外(1),
        荧光(2),
        石墨(3),
        离子电极(4);
        private Integer value;
    }

    /**
     * LIM相关的redis key定义
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLIMRedis {

        /**
         * 可配置编号的redis key,定义hash 主要是根据类型来获取相应的配置，用于样品编号、项目编号等
         */
        LIM_OrgId_IdentifierConfig("LIM:{0}:IdentifierConfig"),

        /**
         * 测试项目的redis key,定义hash 根据测试项目id获取对应json信息
         */
        LIM_OrgId_Test("LIM:{0}:Test"),

        /**
         * 测试项目扩展信息的redis key,定义hash 根据测试项目id获取对应的测试扩展json信息，主要用于数据修约
         */
        LIM_OrgId_TestExpand("LIM:{0}:TestExpand"),

        /**
         * 人员相关的岗位信息redis，key 定义hash 根据测试项目id获取对应的人员岗位json信息，主要用于数据修约
         */
        LIM_OrgId_Person2Test("LIM:{0}:Person2Test"),

        /**
         * 测试项目公式的redis key,定义hash 根据测试项目id获取对应公式的json数组
         */
        LIM_OrgId_TestFormula("LIM:{0}:TestFormula"),

        /**
         * 测试项目参数的redis key,定义hash 根据测试项目id获取对应参数配置的json数组
         */
        LIM_OrgId_TestParamsConfig("LIM:{0}:TestParamsConfig"),


        /**
         * 测试项目公式的部分公式的redis key,定义hash 根据公式id获取对应部分公式数据
         */
        LIM_OrgId_ParamsPartFormula("LIM:{0}:ParamsPartFormula"),

        /**
         * 分析项目排序的redis key,定义hash 根据排序id获取排序下的指标json数组
         */
        LIM_OrgId_AnalyzeItemSort("LIM:{0}:AnalyzeItemSort"),

        /**
         * 项目类型的redis key,定义hash 根据项目类型id获取项目类型的json信息
         */
        LIM_OrgId_ProjectType("LIM:{0}:ProjectType"),

        /***
         * 消息发送培训redis key,定义hash 根据消息配置编码获取消息配置的json信息
         */
        LIM_OrgId_MessageSendConfig("LIM:{0}:MessageSendConfig"),

        /***
         * 首页代办数字
         */
        LIM_Home_TaskNumber("LIM:{0}:TaskNumber"),

        /***
         * 办公管理首页代办数字
         */
        LIM_Home_OfficeManage("LIM:{0}:OfficeManage"),

        /**
         * 实验室分析录入选项卡代办数量
         */
        LIM_Card_AnalyseDataCardNum("LIM:{0}:AnalyseDataCardNum"),

        LIM_OrgId_AuditWorkSheetDetail("PRO:{0}:AuditWorkSheetDetail"),

        LIM_OrgId_AwaitWorkSheetDetail("PRO:{0}:AwaitWorkSheetDetail"),
        LIM_OrgId_AwaitSampleDetail("PRO:{0}:AwaitSampleDetail"),
        LIM_OrgId_FinishWorkSheetDetail("PRO:{0}:FinishWorkSheetDetail"),
        LIM_Limit_Type("limitType"),
        /**
         * 测试项目依赖数据
         */
        LIM_OrgId_TestDependentData("LIM:{0}:TestDependentData"),
        /**
         * 公式
         */
        LIM_OrgId_ParamsFormula("LIM:{0}:TestDependentData"),

        LIM_OrgId_TestMigrateData("LIM:{0}:TestMigrateData"),

        /**
         * 仪器接入远程请求token key
         */
        LIM_OrgId_InstrumentGatherToken("LIM:{0}:InstrumentGatherToken"),

        /**
         * 仪器接入在线状态key
         */
        LIM_InstrumentGatherOnlineStatus("LIM:InstrumentGatherOnlineStatus"),

        /**
         * 提醒消息
         */
        MESSAGE_SEND_RECORD("LIM:messageSendRecord");


        private String value;

        /**
         * 将组织机构id替换成真实key
         *
         * @param value 枚举传入的key
         * @return 返回相应的key值
         */
        public static String getRedisKey(String value) {
            String orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
            return value.replace("{0}", orgId);
        }
    }

    /**
     * LIM相关的redis key 通道的定义
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLIMRedisChannel {

        //测试人员配置修改之后的通道
        LIM_Person2Test_Save,
        //合同修改删除的通道
        LIM_Contract_UpdateDelete,
        //公共的缓存
        LIM_Notice_Cache,
        //快速导航缓存
        LIM_FastNavigation_Cache;
    }

    /**
     * 0.测试公式 1.检测类型参数公式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumParamsFormulaObjectType {
        测试公式(0),
        检测类型参数公式(1);
        private Integer value;
    }

    /**
     * 0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumPartFormulaType {
        修约公式(0),
        检测类型参数公式(1),
        加标公式(2),
        BOD5公式(3),
        参数公式(4),
        串联公式(5),
        折算公式(6),
        替代公式(7);
        private Integer value;
    }

    /**
     * 原始公式类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumOrignFormulatType {

        手写html(0),
        图片(1);

        private Integer value;
    }

    /**
     * 公式参数的原始类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSourceType {
        无(0),
        样品(1),
        测试(2),
        企业(3),
        原始记录单(4);
        private Integer value;
    }

    /**
     * 公式参数小于参数检出限时的计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCalculationMode {
        原始值(0),
        检出限一半(1),
        取零(2);
        private Integer value;

        /**
         * 根据名称获取值
         *
         * @param name 名称
         * @return 直接返回名称
         */
        public static Integer getValueByName(String name) {
            for (EnumCalculationMode enumCalculationMode : EnumCalculationMode.values()) {
                if (enumCalculationMode.toString().equals(name)) {
                    return enumCalculationMode.getValue();
                }
            }
            return null;
        }
    }

    /**
     * 报告组件折算浓度计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumConversionCalculationMode {
        按样品(0),
        按批次(1);

        private Integer value;

        /**
         * 根据名称获取值
         *
         * @param name 名称
         * @return 直接返回名称
         */
        public static Integer getValueByName(String name) {
            for (EnumConversionCalculationMode enumCalculationMode : EnumConversionCalculationMode.values()) {
                if (enumCalculationMode.toString().equals(name)) {
                    return enumCalculationMode.getValue();
                }
            }
            return null;
        }
    }

    /**
     * 报告组件排放速率计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSpeedCalculationMode {
        按样品(0),
        按批次(1);

        private Integer value;

        /**
         * 根据名称获取值
         *
         * @param name 名称
         * @return 直接返回名称
         */
        public static Integer getValueByName(String name) {
            for (EnumSpeedCalculationMode enumCalculationMode : EnumSpeedCalculationMode.values()) {
                if (enumCalculationMode.toString().equals(name)) {
                    return enumCalculationMode.getValue();
                }
            }
            return null;
        }
    }

    /**
     * 化合物均值计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCompoundAvgCalculationMode {
        按样品(0),
        按批次(1);

        private Integer value;

        /**
         * 根据名称获取值
         *
         * @param name 名称
         * @return 直接返回名称
         */
        public static Integer getValueByName(String name) {
            for (EnumCompoundAvgCalculationMode enumCalculationMode : EnumCompoundAvgCalculationMode.values()) {
                if (enumCalculationMode.toString().equals(name)) {
                    return enumCalculationMode.getValue();
                }
            }
            return null;
        }
    }

    /**
     * 质控限值检查项类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCheckItemType {
        出证结果(1),
        公式参数(2);
        private Integer value;

        /**
         * 根据值获取名称
         *
         * @param value 值
         * @return 直接返回名称
         */
        public static String getName(Integer value) {
            for (EnumCheckItemType enumCheckItemType : EnumCheckItemType.values()) {
                if (enumCheckItemType.getValue().equals(value)) {
                    return enumCheckItemType.toString();
                }
            }
            return "";
        }
    }

    /**
     * 质控限值
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumQCType {

        平行(1),
        空白(2),
        加标(4),
        标准(8),
        原样加原样(16),
        串联样(32),
        曲线校核(64),
        洗涤剂(128),
        运输空白(256),
        仪器空白(512),
        试剂空白(1024),
        罐空白(2048),
        校正系数检验(4096),
        替代物(8192),
        阴性对照试验(16384),
        阳性对照试验(32768),
        采样介质空白(65536),
        空白加标(131072),
        质控样(262144),
        替代样(524288),
        现场空白(1048576),
        //2097152 宿迁个性化用到了
        稀释水(4194304);

        private Integer value;

        /**
         * 根据值获取名称
         *
         * @param value 值
         * @return 直接返回名称
         */
        public static String getName(Integer value) {
            for (EnumQCType enumQCType : EnumQCType.values()) {
                if (enumQCType.getValue().equals(value)) {
                    return enumQCType.toString();
                }
            }
            return "";
        }
    }


    /**
     * 质控范围类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumQCRangeType {
        相对偏差(0),
        绝对偏差(1);
        private Integer value;

        /**
         * 根据值获取名称
         *
         * @param value 值
         * @return 直接返回名称
         */
        public static String getName(Integer value) {
            for (EnumQCRangeType enumQCRangeType : EnumQCRangeType.values()) {
                if (enumQCRangeType.getValue().equals(value)) {
                    return enumQCRangeType.name();
                }
            }
            return "";
        }
    }

    /**
     * 质控限值检查项类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumReserveType {
        领取(1),
        处置(2);
        private Integer value;

        /**
         * 根据值获取名称
         *
         * @param value 值
         * @return 直接返回名称
         */
        public static String getName(Integer value) {
            for (EnumReserveType enumReserveType : EnumReserveType.values()) {
                if (enumReserveType.getValue().equals(value)) {
                    return enumReserveType.toString();
                }
            }
            return "";
        }
    }


    /**
     * 消息的读取状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumMessageSendRecordStatus {
        未读(0),
        已读(1);
        private Integer value;
    }

    /**
     * 操作运算符
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumOperators {
        大于(">"),
        大于等于(">="),
        小于("<"),
        小于等于("<="),
        并且("and");
        private String value;
    }

    /**
     * 配置枚举
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumAllConfig {
        全部(-1),
        未配置(1),
        已配置(2);
        private Integer value;
    }

    /**
     * 记录单类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumRecordType {
        采样记录单(1),
        原始记录单(2),
        报告(3);
        private Integer value;
    }

    /**
     * 仪器使用记录类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumInsUseObjType {
        采样(1),
        实验室分析(2),
        现场分析(4);
        private Integer value;
    }

    /**
     * 仪器送检方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCheckWay {
        送检(1),
        上门(2);
        private Integer value;
    }

    /**
     * 计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCalculateWay {
        先修约后计算(0),
        先计算后修约(1);
        private Integer value;
    }

    /**
     * 均值计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumAverageCompute {
        算术均值(0),
        原样值(1),
        几何均值(2);
        private Integer value;
    }

    /**
     * 仪器出入库状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumProjectInstrumentStatus {
        所有(0),
        待入库(1),
        部分入库(2),
        已入库(3);
        private Integer value;
    }

    /**
     * 仪器出入库明细状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumStorageStatus {
        所有(0),
        待入库(1),
        已入库(2);
        private Integer value;
    }

    /**
     * 数据同步类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumDataSyncType {
        量纲(1),
        参数(2),
        行业类型(3),
        检测类型(4),
        检测类型参数(5),
        分析项目(6),
        参数配置(7),
        分析方法(8),
        测试项目(9),
        测试项目修约配置(10),
        测试项目公式(11),
        测试项目参数部分公式(12),
        测试项目公式参数(13),
        报表配置(14),
        报表配置应用(15),
        评价标准(16),
        评价标准等级(17),
        评价标准限值(18),
        评价标准分析项目(19),
        采样单配置(20),
        原始记录单配置(21),
        记录单测试项目配置(22),
        原始记录单参数配置(23),
        原始记录单参数公式配置(24);
        private Integer value;
    }

    public enum EnumQualified {
        合格(1),
        不合格(0);

        private Integer value;

        EnumQualified(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return this.value;
        }

        public static String getName(Integer value) {
            for (EnumQualified enumQualified : EnumQualified.values()) {
                if (value.equals(enumQualified.getValue())) {
                    return enumQualified.name();
                }
            }
            return null;
        }
    }

    /**
     * 质控评价状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumQcSamplePassStatus {

        否(0),
        是(1),
        未评价(2);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumQcSamplePassStatus c : EnumQcSamplePassStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 质控等级
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumQCGrade {
        外部质控(1),

        内部质控(2);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static String getName(Integer value) {
            for (EnumQCGrade c : EnumQCGrade.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 首页项目的模块信息
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumHomeTaskModule {
        项目登记("projectRegister", "DtoStatusForProject a", ""),
        例行登记("routineRegister", "DtoStatusForProject a", ""),
        多企业污染源("pollutionEnterprises", "DtoStatusForProject a", ""),
        项目审核("projectAudit", "DtoStatusForProject a", ""),
        技术下达("projectIssue", "DtoStatusForProject a", ""),
        方案编制("makeSolution", "DtoStatusForProject a", ""),
        方案审核("auditSolution", "DtoStatusForProject a", ""),
        方案确认("confirmSolution", "DtoStatusForProject a", ""),
        采样准备("prepareSample", "DtoStatusForProject a", ""),
        现场委托送样("localSendSample", "DtoStatusForProject a", ""),
        现场任务("localTask", "DtoStatusForRecord a,DtoReceiveSampleRecord b,DtoProject c", ""),//要进行特殊处理
        现场数据复核("localDataCheck", "DtoStatusForRecord a,DtoReceiveSampleRecord b,DtoProject c", ""),
        现场数据审核("localDataAudit", "DtoStatusForRecord a,DtoReceiveSampleRecord b,DtoProject c", LimCodeHelper.LOCAL_AUDIT_AUTH),
        样品交接("sampleReceive", "DtoStatusForRecord a,DtoReceiveSampleRecord b,DtoProject c", ""),
        样品分配("sampleAssign", "DtoReceiveSubSampleRecord a,DtoReceiveSampleRecord b,DtoProject c", ""),
        实验室分析("analyseDataManage", "", ""),
        实验室待检("awaitSample", "DtoAnalyseData a,DtoSample s", ""),
        实验室检测("awaitWorkSheet", "DtoWorkSheetFolder a", ""),
        实验室审核("auditWorkSheet", "DtoWorkSheetFolder a", ""),
        实验室已完成("finishWorkSheet", "DtoWorkSheetFolder a", ""),
        报告编制("reportEdit", "DtoStatusForProject a", ""),
        报告校核("reportCheck", "DtoStatusForReport a", ""),
        报告审核("reportAudit", "DtoStatusForReport a", ""),
        报告复核("reportReview", "DtoStatusForReport a", ""),
        报告签发("reportSign", "DtoStatusForReport a", LimCodeHelper.REPORT_SIGN_AUTH),
        任务办结("projectEnd", "DtoStatusForProject a", ""),
        费用管理("costInfo", "DtoStatusForCostInfo a", ""),
        费用审核("costInfoAudit", "DtoStatusForCostInfo a", ""),
        费用审批("costInfoApprove", "DtoStatusForCostInfo a", LimCodeHelper.COST_APPROVE_AUTH),
        质控任务登记("qcRegister", "DtoStatusForProject a", ""),
        数据汇总("qcCollect", "DtoStatusForProject a", ""),
        评价结果("qcEvaluate", "DtoStatusForProject a", ""),
        重点项目("keyProject", "", ""),
        公告("notice", "", ""),
        我的审批("oaTask", "", ""),
        我已发起("oaTaskApply", "", ""),// 不需要在application.yml配置
        待我审批("oaTaskAudit", "", ""),// 不需要在application.yml配置
        我已审批("oaTaskAuditComplete", "", ""), // 不需要在application.yml配置
        快速导航("fastNavigation", "", ""),//快速导航
        消息("message", "", "");
        private String value;

        /**
         * 实体名称
         */
        private String entityName;

        /**
         * 权限编码
         */
        private String authCode;

        /**
         * 根据名称返回枚举值
         *
         * @param value 编号
         * @return 返回枚举值
         */
        public static EnumHomeTaskModule getByValue(String value) {
            for (EnumHomeTaskModule c : EnumHomeTaskModule.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 首页项目的缓存类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumProjectTaskCache {
        权限(1),
        人员(2);
        private Integer value;
    }

    /**
     * 送样单状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveRecordStatus {
        新建(1),

        已经送样(2),

        待数据确认(6),

        已数据确认(14);

        private Integer value;
    }

    /**
     * 领样单状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveSubRecordStatus {
        无状态(0),

        有实验室数据(1),

        有现场数据(2),

        已领取(4),

        已确认领样(8),

        已提交(16),

        已现场复核(32),

        已实验室复核(64),

        可确认(128),

        已确认(256);

        private Integer value;
    }

    /**
     * 质控项目模块枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumQCProjectModule {
        项目进度("qcInquiry", 0),

        项目登记("qcRegister", 1),

        数据汇总("qcCollect", 2),

        评价结果("qcEvaluate", 3);

        private String code;

        private Integer value;

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumQCProjectModule getByName(String name) {
            for (EnumQCProjectModule c : EnumQCProjectModule.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 根据编码返回枚举值
         *
         * @param code 编码
         * @return 返回枚举值
         */
        public static EnumQCProjectModule getByCode(String code) {
            for (EnumQCProjectModule c : EnumQCProjectModule.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 报告模块枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportModule {
        报告编制("reportEdit", 1),

        报告审核("reportAudit ", 2),

        报告校核("reportCheck ", 4),

        报告复核("reportReview ", 5),

        报告签发("reportSign ", 3);

        private String code;

        private Integer value;
    }

    /**
     * 送样单状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveRecordModule {
        委托现场送样("localSendSample"),

        现场数据录入("localDataInput"),

        现场数据复核("localDataCheck"),

        现场数据审核("localDataAudit"),

        样品交接("sampleReceive");

        private String value;
    }

    /**
     * 项目模块枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectModule {
        项目进度("inquiry", 0, 100),

        项目登记("register", 1, 10),

        现场任务("localTask", 2, 100),

        技术审核("audit", 3, 20),

        方案确认("confirm", 13, 100),

        项目下达("issue", 4, 30),

        委托现场送样("localSendSample", 5, 100),

        采样准备("prepareSample", 6, 100),

        报告管理("report", 7, 100),

        新增报告检索("reportSearch", 14, 100),

        任务办结("end", 8, 60),

        环境例行登记("routineMonitor", 9, 100),

        污染源例行登记("pollutionMonitor", 10, 100),

        项目支出("projectExpend", 100, 100),

        方案编制("makeSolution", 11, 40),

        方案审核("auditSolution", 12, 50);

        private String code;

        private Integer value;

        private Integer sortNum;

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumProjectModule getByName(String name) {
            for (EnumProjectModule c : EnumProjectModule.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 根据编码返回枚举值
         *
         * @param code 编码
         * @return 返回枚举值
         */
        public static EnumProjectModule getByCode(String code) {
            for (EnumProjectModule c : EnumProjectModule.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 检出限类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumExamLimitType {
        ND("ND", "ND"),
        小于DL("小于DL", "＜DL"),
        检出限L("检出限L", "检出限+L"),
        小于检出限("小于检出限", "＜+检出限");

        private String key;

        private String value;

        /**
         * 根据编号返回枚举值
         *
         * @param key 编号
         * @return 返回枚举值
         */
        public static EnumExamLimitType getByCode(String key) {
            for (EnumExamLimitType c : EnumExamLimitType.values()) {
                if (c.getKey().equals(key)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 岗位类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPostType {
        现场(1),
        分析(2);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static String getName(Integer value) {
            for (EnumPostType c : EnumPostType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 文件权限类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumDocAuthorityType {
        文件删除("LIM_DocumentAuthorityType_DeleteFile"),

        文件预览("LIM_DocumentAuthorityType_Preview"),

        文件夹新增("LIM_DocumentAuthorityType_AddFolder"),

        文件夹删除("LIM_DocumentAuthorityType_DeleteFolder"),

        文件夹修改("LIM_DocumentAuthorityType_UpdateFolder"),

        文件上传("LIM_DocumentAuthorityType_Upload"),

        文件下载("LIM_DocumentAuthorityType_Download");

        private String value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static String getName(Integer value) {
            for (EnumPostType c : EnumPostType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 试剂配置类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReagentType {
        一般试剂(1),
        标准溶液(2);
        private Integer value;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOperateType {

        无修改(0),

        新增(1),

        修改(2),

        删除(3);

        private Integer value;

        public static String getName(Integer value) {
            for (EnumOperateType c : EnumOperateType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPojectApproveStatus {

        登记中,

        审核中,

        审核不通过,

        已完成;

    }

    /**
     * 执行周期
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumDealCycle {

        每月(1),

        每半月(2),

        每周(3),

        其他(4);

        private Integer value;

        public static EnumDealCycle getByValue(Integer value) {
            for (EnumDealCycle enumDealCycle : EnumDealCycle.values()) {
                if (enumDealCycle.getValue().equals(value)) {
                    return enumDealCycle;
                }
            }
            return 其他;
        }

    }

    /**
     * 编号生成规则
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCodeGenerateRule {

        自动生成(1),

        手动生成(2);

        private Integer value;
    }

    /**
     * 验证状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCodeValidate {

        未验证(0),

        已验证(1);

        private Integer value;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumMarkersData {
        /**
         * 数据标记
         */
        MARKERS_DATA,
        /**
         * 验证状态
         */
        VALIDATE;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOdsEvent{
        /**
         * 创建任务
         */
        CREATE_TASK,
        /**
         * 验证状态
         */
        ADD;
    }

    /**
     * 考核管理步骤
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumExamineStep {
        考核下达(10),
        考核分配(20),
        考核填报(30),
        考核审核(40);

        private Integer value;

        public static String getStepNameByValue(Integer value) {
            for (EnumExamineStep c : EnumExamineStep.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 考核状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumExamineStatus {
        编辑中(10),
        已下达(20),
        考核中(30),
        待审核(40),
        审核通过(50),
        审核不通过(60);

        private Integer value;

        public static String getStatusNameByValue(Integer value) {
            for (EnumExamineStatus c : EnumExamineStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 考核处理状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumExamineHandleStatus {
        待处理(0),
        已处理(1);

        private Integer value;
    }


    /**
     * 固定资产状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAssetsStatus {
        使用中(1),

        已报废(2);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumAssetsStatus c : EnumAssetsStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 考核处理状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOcrConfigParamType {
        样品参数(1),
        现场数据(2);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumOcrConfigParamType c : EnumOcrConfigParamType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 测试项目迁移导入枚举
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumImportTestType {

        测试项目表(10, "TB_LIM_Test", "测试项目"),
        分析项目表(20, "TB_BASE_AnalyzeItem", "分析项目"),
        分析方法表(30, "TB_LIM_AnalyzeMethod", "分析方法"),
        量纲表(40, "TB_BASE_Dimension", "量纲"),
        参数表(50, "TB_LIM_Params", "参数"),
        测试拓展表(60, "TB_LIM_TestExpand", "测试拓展"),
        质控限值表(70, "TB_BASE_QualityControlLimit", "质控限值"),
        计算公式表(80, "TB_LIM_ParamsFormula", "测试项目公式"),
        公式参数表(90, "TB_LIM_ParamsTestFormula", "公式参数"),
        拓展公式表(100, "TB_LIM_ParamsPartFormula", "公式拓展");
        private Integer value;
        private String source;
        private String checkItem;
    }

    /**
     * 分析方法状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAnalyzeMethodStatus {
        启用(1),
        停用(2),
        废止(3);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumAnalyzeMethodStatus c : EnumAnalyzeMethodStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 日志审计操作类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumTestOperateLogOperateType {
        新增(1),
        删除(2),
        修改(3);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumTestOperateLogOperateType c : EnumTestOperateLogOperateType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 消耗品采购物资类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum EnumMaterialType {

        消耗品(1),

        标样(2);

        /**
         * 枚举值
         */
        private final Integer value;
    }

    /**
     * 采样方案状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSamplingArrangeStatus {
        编辑中(1),
        审核中(2),
        审核退回(3),
        审核通过(4);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumSamplingArrangeStatus c : EnumSamplingArrangeStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 仪器接入数据类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumInstrumentGatherDataType {
        实时数据("R"),
        工况数据("G"),
        结果数据("CN2083"),
        通道数据("Channel");

        private String value;

        public static String getByValue(String value) {
            for (EnumInstrumentGatherDataType c : EnumInstrumentGatherDataType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 查新计划执行周期
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumNewSearchPlanDealCycle {

        每月(1),

        每2月(2),

        每6月(3),

        每年(4);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumNewSearchPlanDealCycle enumDealCycle : EnumNewSearchPlanDealCycle.values()) {
                if (enumDealCycle.getValue().equals(value)) {
                    return enumDealCycle.name();
                }
            }
            return "";
        }

    }

    /**
     * 查新任务状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumNewSearchStatus {

        所有(-1),

        新建(0),

        待处理(1),

        已处理(2);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumNewSearchStatus status : EnumNewSearchStatus.values()) {
                if (status.getValue().equals(value)) {
                    return status.name();
                }
            }
            return "";
        }

    }

    /**
     * 查新计划状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPlanStatus {

        所有(-1),

        新建(0),

        启用(1),

        停用(2);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumNewSearchStatus status : EnumNewSearchStatus.values()) {
                if (status.getValue().equals(value)) {
                    return status.name();
                }
            }
            return "";
        }

    }

    /**
     * 查新计划类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumNewSearchPlanType {

        标准查新(1);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumNewSearchPlanType type : EnumNewSearchPlanType.values()) {
                if (type.getValue().equals(value)) {
                    return type.name();
                }
            }
            return "";
        }
    }

    /**
     * 上岗证类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPersonCertType {

        采样("7"),
        分析("6"),
        其他("5");

        private String value;
    }

    /**
     * 证书历史记录类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCertHistoryInfoType {

        采样人员持证信息(1, DtoSamplingCertHistory.class),
        分析人员持证信息(2, DtoAnalyzeCertHistory.class),
        仪器有效期信息(3, DtoInstrumentHistory.class);

        private Integer value;

        private Class clazz;
    }

    /**
     * 移动端电子签名类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum MobileSignType {

        委托方(true, "委托方（签字）"),

        送样人(false, "送样人");

        /**
         * 是否向下签名
         */
        private Boolean isDown;

        /**
         * 签名类型
         */
        private String personType;

        public static MobileSignType getByValue(String personType) {
            for (MobileSignType type : MobileSignType.values()) {
                if (type.getPersonType().equals(personType)) {
                    return type;
                }
            }
            return null;
        }

    }

    /**
     * 消息类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum MessageType {
        机构证书("orgCertificate"),
        上岗证("jobCertificate"),
        仪器检定校准("instrumentCheck"),
        仪器维护("instrumentMaintain"),
        仪器出库("instrumentStorage"),
        消耗品过期("consumableOverdue"),
        消耗品即将过期("consumableWillOverdue"),
        消耗品标样库存提醒("consumableStorage"),
        留样超期消息提醒("sampleDisposalManage"),
        报告预警提醒("reportWarning"),
        标准查新任务("newSearchTask"),
        标准查新确认("newSearchResult");

        private String value;

        /**
         * 消息类型名称
         *
         * @param value 枚举值
         * @return 名称
         */
        public static String getNameByValue(String value) {
            String result = null;
            for (MessageType status : MessageType.values()) {
                if (status.value.equals(value)) {
                    result = status.name();
                }
            }
            return result;
        }
    }

    /**
     * 测试项目监管平台测试项目匹配状态
     *
     * <AUTHOR>
     * @version V5.2.0
     * @since 2025/05/13
     */
    @Getter
    @AllArgsConstructor
    public enum EnumShTestMatchStatus {
        未匹配(0),
        异常匹配(1),
        完美匹配(2);

        private Integer value;
    }

}