package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * ParamsTestFormula实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ParamsTestFormula")
@Data
public class ParamsTestFormula implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ParamsTestFormula() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 对象id（如测试公式id）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("对象id（如测试公式id）")
    private String objId;

    /**
     * 参数id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("参数id")
    private String paramsId;

    /**
     * 参数名称
     */
    @Column(length = 50)
    @ApiModelProperty("参数名称")
    @Length(message = "参数名称{validation.message.length}", max = 50)
    private String paramsName;

    /**
     * 参数别名
     */
    @Column(length = 50)
    @ApiModelProperty("参数别名")
    @Length(message = "参数别名{validation.message.length}", max = 50)
    private String alias;

    /**
     * 默认值
     */
    @Column(length = 50)
    @ApiModelProperty("默认值")
    @Length(message = "默认值{validation.message.length}", max = 50)
    private String defaultValue;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 检测单模板中的变量名
     */
    @Column(length = 50)
    @ApiModelProperty("检测单模板中的变量名")
    @Length(message = "检测单模板中的变量名{validation.message.length}", max = 50)
    private String aliasInReport;

    /**
     * 计量单位id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位id")
    private String dimensionId;

    /**
     * 计量单位
     */
    @Column(length = 50)
    @ApiModelProperty("计量单位")
    private String dimension;

    /**
     * 类型（枚举EnumSourceType：0.无,1.样品，2.测试，3.企业，4.原始记录单）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("类型（枚举EnumSourceType：0.无,1.样品，2.测试，3.企业，4.原始记录单）")
    private Integer sourceType;

    /**
     * 是否必填
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否必填")
    private Boolean isMust;

    /**
     * 是否允许修改
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否允许修改")
    private Boolean isEditable;

    /**
     * 检出限
     */
    @Column
    @ApiModelProperty("检出限")
    @Length(message = "检出限{validation.message.length}", max = 50)
    private String detectionLimit;

    /**
     * 计算方式：枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零
     */
    @Column
    @ColumnDefault("-1")
    @ApiModelProperty("计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）")
    private Integer calculationMode;

    /**
     * 斜杠计算值
     */
    @Column
    @ApiModelProperty("斜杠计算值")
    private Integer slashValue;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}