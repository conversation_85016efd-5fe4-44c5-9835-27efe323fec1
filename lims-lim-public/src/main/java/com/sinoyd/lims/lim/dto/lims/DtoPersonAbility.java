package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.PersonAbility;

import java.util.Collection;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoPersonAbility实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_PersonAbility")
@Data
@DynamicInsert
public class DtoPersonAbility extends PersonAbility {

    @Transient
    private Collection<String> testIds;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 人员的证书编号
     */
    @Transient
    private String personCertCode;

    /**
     * 人员的证书名称
     */
    @Transient
    private String personCertName;

    /**
     * 检测类型上的图标
     */
    @Transient
    private String icon;

    /**
     * 是否现场
     */
    @Transient
    private Boolean isCompleteField;

    /**
     * 检测资质
     */
    @Transient
    private Integer cert;

    /**
     * 过期天数
     */
    @Transient
    private Integer expirationDays;

    /**
     * 人员名称
     */
    @Transient
    private String person;

    /**
     * 证书状态
     */
    @Transient
    private String certState;

    /**
     * 状态排序值
     */
    @Transient
    private Integer orderNum;

    /**
     * 能力类型名称
     */
    @Transient
    private String abilityTypeName;

    /**
     * 能力类型名称
     */
    @Transient
    private String casNum;
}