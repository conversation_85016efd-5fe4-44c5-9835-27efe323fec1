package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;


/**
 * ItemRelationParams实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ItemRelationParams")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ItemRelationParams implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ItemRelationParams() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 分析项目关系
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析项目关系")
    private String relationId;

    /**
     * 分析项目Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析项目Id")
    private String analyzeItemId;

    /**
     * 分析项目名称
     */
    @Column(length = 50)
    @ApiModelProperty("分析项目名称")
    private String analyzeItemName;

    /**
     * 位置（1.左，2.右，-1.没有）
     */
    @Column(nullable = false)
    @ApiModelProperty("位置（1.左，2.右，-1.没有）")
    private Integer position;

    /**
     * 排序值（预留：列表显示排序用）
     */
    @Column(nullable = false)
    @ApiModelProperty("排序值（预留：列表显示排序用）")
    private Integer orderNum;


    /**
     * 小于检出限计算方式，关联枚举EnumLessExamLimit
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("小于检出限计算方式，关联枚举EnumLessExamLimit")
    private String limitValueConfig;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

}