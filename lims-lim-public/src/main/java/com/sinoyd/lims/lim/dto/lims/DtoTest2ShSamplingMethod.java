package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Test2ShSamplingMethod;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.*;

/**
 * DtoTest2ShSamplingMethod实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/17
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Test2ShSamplingMethod")
@Data
@DynamicInsert
public class DtoTest2ShSamplingMethod extends Test2ShSamplingMethod {

    /**
     * 默认构造函数
     */
    public DtoTest2ShSamplingMethod() {
        super();
    }

    /**
     * 带参构造函数
     *
     * @param testId 测试项目id
     * @param sampleTypeId 检测类型id
     */
    public DtoTest2ShSamplingMethod(String testId, String sampleTypeId) {
        super();
        this.setTestId(testId);
        this.setSampleTypeId(sampleTypeId);
    }
}
