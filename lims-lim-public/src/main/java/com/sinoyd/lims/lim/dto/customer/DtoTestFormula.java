package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;

import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 测试项目公式
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/9
 * @since V100R001
 */
@Data
public class DtoTestFormula {

    /**
     * 公式id
     */
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 测试项目名称
     */
    private String redAnalyzeItemName;


    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;


    /**
     * 标准编号
     */
    private String redCountryStandard;


    /**
     * 公式
     */
    private String formula;


    /**
     * 检测类型id
     */
    private String sampleTypeId;


    /**
     * 检测类型名称
     */
    private String sampleTypeName;


    /**
     * 配置日期
     */
    private Date configDate;


    /**
     * 是否配置修约公式
     */
    private Boolean isRevision = false;

    /**
     * 是否配置加标公式
     */
    private Boolean isJB = false;

    /**
     * 是否配置BOD5公式
     */
    private Boolean isBOD5 = false;


    /**
     * 是否配置折算公式
     */
    private Boolean isConversion = false;


    /**
     * 是否配置串联
     */
    private Boolean isCL = false;


    /**
     * 原始公式
     */
    private String orignFormula;

    /**
     * 原始公式类型
     */
    private Integer orignFormulatType = EnumLIM.EnumOrignFormulatType.图片.getValue();


    /**
     * 公式相应的参数
     */
    private List<DtoParamsTestFormula> paramsTestFormulas = new ArrayList<>();


    /**
     * JB公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulasJB = new ArrayList<>();

    /**
     * 修约公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulasRevision = new ArrayList<>();


    /**
     * 部分参数公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulas = new ArrayList<>();


    /**
     * BOD5公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulasBOD5 = new ArrayList<>();

    /**
     * 折算公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulasConversion = new ArrayList<>();

    /**
     * 串联公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulasCL = new ArrayList<>();

    /**
     * 分析方法是否停用
     */
    @Transient
    private Boolean isDeactivate = Boolean.FALSE;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 是否资源中心作废
     */
    private Boolean isRccAbolish = false;



    /**
     * 默认构造函数
     */
    public DtoTestFormula() {

    }


    /**
     * 测试项目公式
     *
     * @param id                   主键id
     * @param testId               测试项目id
     * @param redAnalyzeItemName   测试项目名称
     * @param redAnalyzeMethodName 方法名称
     * @param redCountryStandard   标准编号
     * @param formula              公式
     * @param sampleTypeId         样品类型id
     * @param sampleTypeName       样品类型名称
     * @param configDate           配置日期
     */
    public DtoTestFormula(String id,
                          String testId,
                          String redAnalyzeItemName,
                          String redAnalyzeMethodName,
                          String redCountryStandard,
                          String formula, String sampleTypeId,
                          String sampleTypeName, Date configDate) {
        this.id = id;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.formula = formula;
        this.sampleTypeId = sampleTypeId;
        this.sampleTypeName = sampleTypeName;
        this.configDate = configDate;
    }

    /**
     * 测试项目公式
     *
     * @param id                   主键id
     * @param testId               测试项目id
     * @param redAnalyzeItemName   测试项目名称
     * @param redAnalyzeMethodName 方法名称
     * @param redCountryStandard   标准编号
     * @param formula              公式
     * @param sampleTypeId         样品类型id
     * @param sampleTypeName       样品类型名称
     * @param configDate           配置日期
     */
    public DtoTestFormula(String id,
                          String testId,
                          String redAnalyzeItemName,
                          String redAnalyzeMethodName,
                          String redCountryStandard,
                          String formula, String sampleTypeId,
                          String sampleTypeName, Date configDate, String analyzeMethodId,
                          Boolean isRccAbolish) {
        this(id, testId, redAnalyzeItemName, redAnalyzeMethodName, redCountryStandard, formula, sampleTypeId, sampleTypeName, configDate);
        this.analyzeMethodId = analyzeMethodId;
        this.isRccAbolish = isRccAbolish;
    }

}
