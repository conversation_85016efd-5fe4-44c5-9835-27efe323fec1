package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.base.entity.Document;
import com.sinoyd.lims.lim.entity.OcrConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoOcrConfig
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_lim_ocrConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoOcrConfig extends OcrConfig {

    /**
     * ocr图片附件实体
     */
    @Transient
    private Document document;
}
