package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * Curve实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Curve")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Curve extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Curve() {
        this.configPersonId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        this.configName = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserName() : "";
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测试项目id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 配置人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("配置人员id")
    private String configPersonId;

    /**
     * 关联曲线id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("关联曲线id")
    private String relevanceId;

    /**
     * 配置人员姓名
     */
    @Column(length = 50)
    @ApiModelProperty("配置人员姓名")
    private String configName;

    /**
     * 斜率范围
     */
    @Column(length = 50)
    @ApiModelProperty("斜率范围")
    @Length(message = "斜率范围{validation.message.length}", max = 50)
    private String kRange;

    /**
     * 截距范围
     */
    @Column(length = 50)
    @ApiModelProperty("截距范围")
    @Length(message = "截距范围{validation.message.length}", max = 50)
    private String bRange;

    /**
     * 相关系数范围
     */
    @Column(length = 50)
    @ApiModelProperty("相关系数范围")
    @Length(message = "相关系数范围{validation.message.length}", max = 50)
    private String coefficientRange;

    /**
     * 相关系数
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("相关系数")
    @Length(message = "相关系数{validation.message.length}", max = 50)
    private String coefficient;

    /**
     * 配置日期
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("配置日期")
    private Date configDate;

    /**
     * 周期（天）
     */
    @Column(nullable = false)
    @ColumnDefault("30")
    @ApiModelProperty("周期（天）")
    private Integer period;

    /**
     * 曲线零点
     */
    @Column(length = 50)
    @ApiModelProperty("曲线零点")
    @Length(message = "曲线零点{validation.message.length}", max = 50)
    private String zeroPoint;

    /**
     * 强制零点
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否强制零点")
    private Boolean forcedZero;

    /**
     * 斜率a
     */
    @Column(length = 50)
    @ApiModelProperty("斜率a")
    @Length(message = "斜率a{validation.message.length}", max = 50)
    private String kValue;

    /**
     * 截距b
     */
    @Column(length = 50)
    @ApiModelProperty("截距b")
    @Length(message = "截距b{validation.message.length}", max = 50)
    private String bValue;

    /**
     * 实数c
     */
    @Column(length = 50)
    @ApiModelProperty("实数c")
    @Length(message = "实数c{validation.message.length}", max = 50)
    private String cValue;

    /**
     * 是否双曲线
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否双曲线")
    private Boolean isDouble;

    /**
     * 双曲线名称
     */
    @Column(length = 50)
    @ApiModelProperty("双曲线名称")
    @Length(message = "双曲线名称{validation.message.length}", max = 50)
    private String doubleName;

    /**
     * 曲线类型（枚举(EnumCurveType:0直线型 1Log型  2二次)）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("曲线类型（枚举(EnumCurveType:0直线型 1Log型  2二次)）")
    private Integer curveType;

    /**
     * 曲线模型（枚举（EnumCurveModel：0普通 1紫外 2荧光 3石墨  4离子电极））
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("曲线模型（枚举（EnumCurveModel：0普通 1紫外 2荧光 3石墨  4离子电极））")
    private Integer curveMode;

    /**
     * 曲线信息
     */
    @Column(length = 50)
    @ApiModelProperty("曲线信息")
    @Length(message = "曲线信息{validation.message.length}", max = 100)
    private String curveInfo;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;


    /**
     * 空白1
     */
    @Column(length = 50)
    @ApiModelProperty("空白1")
    @Length(message = "空白1{validation.message.length}", max = 50)
    private String blankOne;


    /**
     * 空白2
     */
    @Column(length = 50)
    @ApiModelProperty("空白2")
    @Length(message = "空白2{validation.message.length}", max = 50)
    private String blankTwo;


    /**
     * 空白（均）量纲标识
     */
    @Column(length = 50)
    @ApiModelProperty("空白（均）量纲标识")
    @Length(message = "空白（均）量纲标识{validation.message.length}", max = 50)
    private String blankAvgDimensionId;
}