-- 报告导出数据详情报配置
DELETE FROM TB_LIM_ReportConfig WHERE id = '2c6d65f3-eeba-45cc-8d3e-71863410e09f';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('2c6d65f3-eeba-45cc-8d3e-71863410e09f', 1, 'ReportDataDetail', '报告详情数据.xlsx', 'LIMReportForms/报告详情数据.xlsx',
        'output/LIMReportForms/报告详情数据.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.ReportDataDetailExportService', '{"sort":"orderNum-"}', '', 0, 1, '',
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-07-04 10:30:44',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-07-04 10:31:27',
        'com.sinoyd.lims.pro.criteria.DetailDataCriteria', 'LIMReportForms', 'export/ReportDataDetail', 0, '', NULL,
        '', '', '', 0, NULL);


DELETE FROM TB_LIM_ReportApply WHERE id = 'bb83fe30-6153-4447-aa16-d6b4327ede91';
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('bb83fe30-6153-4447-aa16-d6b4327ede91', '2c6d65f3-eeba-45cc-8d3e-71863410e09f', 'ReportEditNew', '报告编制V2.0',
        'ReportDataDetail', '数据导出', 0, 0, 1, '', '报告信息:数据预览', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-07-04 10:31:58', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-07-04 10:31:58', NULL);
