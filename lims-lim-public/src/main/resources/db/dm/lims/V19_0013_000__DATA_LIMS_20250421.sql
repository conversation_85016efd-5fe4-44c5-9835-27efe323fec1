-- 更新交接单应用场景备注
UPDATE tb_lim_reportapply set remark ='交接单' where id in (
                                                         '3d8feb5c-85aa-4ec3-be3a-034475a7b618',
                                                         '5857cc30-d401-45d3-b080-5fc08fd103ff',
                                                         '775556b8-45d1-4aed-9776-f677b378564b',
                                                         '85141e4e-7092-4f11-9552-d491be5384d6');
-- 更新现场任务交接单location
UPDATE tb_lim_reportapply set location = '送样单信息:交接单' where id = '775556b8-45d1-4aed-9776-f677b378564b';

-- 更新交接单按钮类型为下拉
UPDATE tb_lim_reportapply set type = 1 where id in (
                                                    '3d8feb5c-85aa-4ec3-be3a-034475a7b618',
                                                    '775556b8-45d1-4aed-9776-f677b378564b',
                                                    '85141e4e-7092-4f11-9552-d491be5384d6');

delete
from tb_lim_reportconfig
where id in (
    'f7561f17-bb0e-4bc5-9f45-2ef89145b534'
    );

-- 新增交接单分包
INSERT INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('f7561f17-bb0e-4bc5-9f45-2ef89145b534', 1, 'OutSampleReceive', 'SINOYD-LIMS-CY-24-01 样品交接单(分包).xlsx',
        'LIMReportForms/SINOYD-LIMS-CY-24-01 样品交接单(分包).xlsx', 'output/LIMReportForms/样品交接单(分包).xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.proReport.DeliveryReceiptReportService',
        '{"isSignature": true,"isSamplingOut":true}', '', 6661, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-18 15:50:54', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-18 16:52:19', '', 'LIMReportForms', 'OutSampleReceive', 0, '',
        NULL, '', 'JSYD-054', '', 0, NULL);


delete
from tb_lim_reportapply
where id in (
             '3d8feb5c-85aa-38ec3-be3a-034475a7b614',
             '5857cc30-d691-45d3-b080-5fc08fd103f5',
             '85141e4e-7092-4f34-9552-d491be5384d6',
             '7755506h-45d1-4aed-9776-f677b3785646'
    );
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('3d8feb5c-85aa-38ec3-be3a-034475a7b614', 'f7561f17-bb0e-4bc5-9f45-2ef89145b534', 'SampleReceive', '样品交接',
        'OutSampleReceive', '交接单(分包)', 1, 0, 1, '交接单(分包)', '样品交接', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('5857cc30-d691-45d3-b080-5fc08fd103f5', 'f7561f17-bb0e-4bc5-9f45-2ef89145b534', 'ProjectRegister', '项目登记',
        'OutSampleReceive', '交接单(分包)', 0, 0, 1, '交接单(分包)', '项目登记:样品登记', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-09-26 16:19:53', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-09-26 16:19:53', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('7755506h-45d1-4aed-9776-f677b3785646', 'f7561f17-bb0e-4bc5-9f45-2ef89145b534', 'LocalTask', '现场任务',
        'OutSampleReceive', '交接单(分包)', 1, 0, 1, '交接单(分包)', '送样单信息:交接单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-09-19 14:31:18', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-09-19 14:31:18', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('85141e4e-7092-4f34-9552-d491be5384d6', 'f7561f17-bb0e-4bc5-9f45-2ef89145b534', 'SampleReceive', '样品交接',
        'OutSampleReceive', '交接单(分包)', 1, 0, 1, '交接单(分包)', '样品交接:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-11-25 16:24:47', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-11-25 16:24:47', NULL);

