-- 新增废气比对报告在线仪器信息组件
delete
from TB_LIM_ReportModule
where id = '93ed01a0-3cf3-11f0-8cfa-02420a0000da';

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('93ed01a0-3cf3-11f0-8cfa-02420a0000da', 'dtGasCompareOicTable', '标准版废气比对在线仪器信息表组件', 'dtGasCompareOicTable',
        'dtGasCompareOicSource', 10000, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-05-30 09:09:38', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-05-30 09:09:38', '0', '0', 0, 0, 0, 1);

update TB_LIM_ReportModule
set sonTableJson = '["dtGasCompareTable", "dtGasCompareDivTable", "dtGasCompareInstTable", "outParallelStdDataSource", "dtGasCompareOicTable"]'
where moduleCode = 'gasCompareDataSource';