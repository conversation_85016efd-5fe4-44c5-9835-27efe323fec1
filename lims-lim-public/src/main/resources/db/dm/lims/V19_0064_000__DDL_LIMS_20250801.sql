-- 样品更新受检单位触发器调整，考虑加原样
CREATE OR REPLACE   TRIGGER "TRI_TB_PRO_SAMPLE_INSPECTEDENT"
    BEFORE  UPDATE
                       ON "TB_PRO_SAMPLE"
                       referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

                       for each row
DECLARE
var_inspectedEntId  VARCHAR ( 50 ) ;

var_inspectedEnt  VARCHAR ( 255 ) ;
BEGIN
IF
NEW.inspectedEntId <> OLD.inspectedEntId THEN-- 质控样 --
		IF
			old.projectId = '00000000-0000-0000-0000-000000000000'
			AND old.qcId <> '00000000-0000-0000-0000-000000000000' THEN
SELECT
    p.inspectedEntId,
    p.inspectedEnt INTO var_inspectedEntId,
    var_inspectedEnt
FROM
    TB_PRO_Sample r,
    TB_PRO_Project p,
    TB_LIM_ProjectType t,
    TB_PRO_QualityControl c
WHERE
        r.isDeleted = 0
  AND c.id = old.qcId
  AND c.associateSampleId = r.id
  AND p.isDeleted = 0
  AND p.id = r.projectId
  AND t.isDeleted = 0
  AND p.projectTypeId = t.id
  AND t.mark <> 'S';

END IF;-- 加原样 --
IF
old.projectId = '00000000-0000-0000-0000-000000000000'
			AND old.qcId = '00000000-0000-0000-0000-000000000000'
			AND old.sampleCategory = 3 THEN
SELECT
    p.inspectedEntId,
    p.inspectedEnt INTO var_inspectedEntId,
    var_inspectedEnt
FROM
    TB_PRO_Sample r,
    TB_PRO_Project p,
    TB_LIM_ProjectType t
WHERE
        r.isDeleted = 0
  AND old.associateSampleId = r.id
  AND p.isDeleted = 0
  AND p.id = r.projectId
  AND t.isDeleted = 0
  AND p.projectTypeId = t.id
  AND t.mark <> 'S';

END IF;-- 原样 --
		IF
old.projectId <> '00000000-0000-0000-0000-000000000000' THEN
SELECT
    p.inspectedEntId,
    p.inspectedEnt INTO var_inspectedEntId,
    var_inspectedEnt
FROM
    TB_PRO_Project p,
    TB_LIM_ProjectType t
WHERE
        p.isDeleted = 0
  AND p.id = old.projectId
  AND t.isDeleted = 0
  AND p.projectTypeId = t.id
  AND t.mark <> 'S';

END IF;

SET new.inspectedEntId = var_inspectedEntId;

SET new.inspectedEnt = var_inspectedEnt;

END IF;

END;