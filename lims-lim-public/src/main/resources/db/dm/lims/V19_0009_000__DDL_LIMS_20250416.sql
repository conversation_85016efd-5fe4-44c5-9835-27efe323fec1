-- ---------------------------------------------------------------
-- 创建消费端消息监控表映射
-- ---------------------------------------------------------------
CREATE TABLE "TB_BASE_MessageReceiveMonitor"
(
    "id"         VARCHAR(50)  NOT NULL,
    "message"    TEXT         NOT NULL,
    "exchange"   VARCHAR(255),
    "routingKey" VARCHAR(255),
    "queue"      VARCHAR(255),
    "isSuccess"  BIT          NOT NULL,
    "reason"     VARCHAR(255) NOT NULL,
    "sendStatus" int          NOT NULL DEFAULT 2,
    "orgId"      VARCHAR(50)  NOT NULL,
    "domainId"   VARCHAR(50)  NOT NULL,
    "creator"    <PERSON><PERSON>HA<PERSON>(50)  NOT NULL,
    "createDate" TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    "modifier"   VARCHAR(50)  NOT NULL,
    "modifyDate" TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP()
);
ALTER TABLE "TB_BASE_MessageReceiveMonitor"
    ADD CONSTRAINT PRIMARY KEY ("id");

COMMENT
ON TABLE "TB_BASE_MessageReceiveMonitor" IS '消费端消息监控表';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."id" IS '主键';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."message" IS '消息内容';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."exchange" IS '交换机';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."routingKey" IS '路由键';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."queue" IS '队列';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."isSuccess" IS '是否发送成功';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."reason" IS '原因';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."sendStatus" IS '重发状态';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."orgId" IS '所属机构ID';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."domainId" IS '所属实验室ID';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."modifier" IS '更新人';
COMMENT
ON COLUMN "TB_BASE_MessageReceiveMonitor"."modifyDate" IS '更新时间';