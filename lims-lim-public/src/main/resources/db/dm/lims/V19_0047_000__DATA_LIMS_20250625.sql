-- --------------------------------------------------------------------------
-- 添加排污许可证方案同步和方案获取定时任务
-- --------------------------------------------------------------------------
DELETE FROM TB_BASE_Job WHERE id = '430c3c87-c15a-484c-8e84-bab0f0be33a7';
INSERT INTO TB_BASE_Job(id, jobName, jobGroup, invokeTarget, cronExpression,
                        misfirePolicy, isConcurrent, status, remark, isDeleted, orgId,
                        creator, createDate, domainId, modifier, modifyDate)
VALUES ('430c3c87-c15a-484c-8e84-bab0f0be33a7', '企业排污许可证方案同步', 'DEFAULT',
        'pollutantDischargePermitProcessor.syncUnSuccessData()', '0 0/10 * * * ?', 3, 0, 1, '', 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-05-06 15:10:42',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-05-06 16:40:11');

DELETE FROM TB_BASE_Job WHERE id = '4c9abe11-ac64-43bb-8efa-14fa887fb350';
INSERT INTO TB_BASE_Job(id, jobName, jobGroup, invokeTarget, cronExpression,
                        misfirePolicy, isConcurrent, status, remark, isDeleted, orgId,
                        creator, createDate, domainId, modifier, modifyDate)
VALUES ('4c9abe11-ac64-43bb-8efa-14fa887fb350', '企业排污许可证方案获取', 'DEFAULT',
        'pollutantDischargePermitProcessor.syncMonitorData()', '0 0/10 * * * ?', 3, 0, 1, '', 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-05-06 15:08:57',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-05-06 16:40:13');
