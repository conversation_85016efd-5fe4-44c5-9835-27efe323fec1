-- 油气回收比对报告组件配置
DELETE
FROM TB_LIM_ReportConfig
WHERE id = '41cb7d9c-930c-4859-9e07-ac26f6ddfa68';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('41cb7d9c-930c-4859-9e07-ac26f6ddfa68', 1, 'GasRecycleBdStd', '.doc', 'Report/标准版报告.doc',
        'output/Report/油气回收比对报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{"sort":"orderNum-"}', '', 0, 4,
        '油气回收比对报告', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-09-01 13:27:52', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-09-01 13:30:37', 'reportId,sortId', 'Report', 'GasRecycleBdStd', 0, '', NULL, '', '', '', 0, NULL);

DELETE
FROM TB_LIM_ReportApply
WHERE id = '7a737d2d-726c-4c92-a035-a3ae926c2176';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('7a737d2d-726c-4c92-a035-a3ae926c2176', '41cb7d9c-930c-4859-9e07-ac26f6ddfa68', 'ReportEditNew', '报告编制V2.0',
        'GasRecycleBdStd', '油气回收比对报告', 1, 1, 1, '', '报告编制:电子报告', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-01 13:29:03', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-01 13:29:03', NULL);

DELETE
FROM TB_LIM_RecordConfig
WHERE id = '6d497cc4-8c8d-4a30-825f-5f692a9eddd3';

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark, isDeleted, orgId,
                                creator, createDate, domainId, modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('6d497cc4-8c8d-4a30-825f-5f692a9eddd3', '油气回收比对报告', 3, '41cb7d9c-930c-4859-9e07-ac26f6ddfa68',
        '00000000-0000-0000-0000-000000000000', '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-01 14:19:00', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-01 14:19:00', '', 0);

DELETE
FROM TB_LIM_ReportModule
WHERE id in ('30320ee8-60d5-4e4c-aa2d-4274f2f92cdc', 'd9a252f4-d471-4882-812d-0e09a26e71c0',
             '', 'fa489d5a-a53b-4b76-9550-25edfaf8ce9c',
             'dd0deeee-594b-4db8-abdb-0152bc9d8c65');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('30320ee8-60d5-4e4c-aa2d-4274f2f92cdc', 'dtMbxBdStdTable', '标准版油气回收比对密闭性检测结果表组件', 'dtMbxBdStdTable',
        'dtMbxBdStdSource', 20, 0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-09-01 14:23:18', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-09-01 14:23:18', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('d9a252f4-d471-4882-812d-0e09a26e71c0', 'dtYzBdStdTable', '标准版油气回收比对液阻检测结果表组件', 'dtYzBdStdTable',
        'dtYzBdStdSource', 20, 0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-09-01 14:26:22', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-09-01 14:26:22', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('fa489d5a-a53b-4b76-9550-25edfaf8ce9c', 'gasRecycleBdStdDataSource', '标准版油气回收比对报告检测数据主表',
        'dtDataSource', '', 0, 0,
        '["dtGasRecycleHeadStdTable", "dtYzBdStdTable", "dtMbxBdStdTable", "dtQybBdStdTable"]', 1,
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-09-01 14:31:13',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-09-01 14:31:13', '0', '0',
        0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('dd0deeee-594b-4db8-abdb-0152bc9d8c65', 'dtQybBdStdTable', '标准版油气回收比对报告气液比检测结果表组件',
        'dtQybBdStdTable', 'dtQybBdStdSource', 20, 0, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-09-01 14:28:25', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-09-01 14:28:25', '0', '0', 0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportConfig2Module
WHERE id in ('486ffb68-f160-4229-b44b-411b20adb346', '00c5e940-01c2-4f2f-bae6-4e50b2c0f103',
             '2ad1cd00-ac3b-40c9-9cfc-8b2200f0ceae');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('486ffb68-f160-4229-b44b-411b20adb346', '41cb7d9c-930c-4859-9e07-ac26f6ddfa68',
        'fa489d5a-a53b-4b76-9550-25edfaf8ce9c');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('00c5e940-01c2-4f2f-bae6-4e50b2c0f103', '41cb7d9c-930c-4859-9e07-ac26f6ddfa68',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('2ad1cd00-ac3b-40c9-9cfc-8b2200f0ceae', '41cb7d9c-930c-4859-9e07-ac26f6ddfa68',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');
