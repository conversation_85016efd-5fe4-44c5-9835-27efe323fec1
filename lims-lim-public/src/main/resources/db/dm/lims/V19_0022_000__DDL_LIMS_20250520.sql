-- ----------------------------------------------------
-- 测试项目表中添加上海监管平台测试项目匹配状态字段
-- ----------------------------------------------------
ALTER TABLE "TB_LIM_TEST"
    ADD COLUMN "shMatchStatus" INT NOT NULL DEFAULT 0;

COMMENT
ON COLUMN "TB_LIM_TEST"."shMatchStatus" IS '上海监管平台测试项目匹配状态，0：未匹配，1：异常匹配，2：完美匹配';

-- ----------------------------------------------------
-- 测试项目表中添加【上海监管平台测试项目匹配信息】息字段
-- ----------------------------------------------------
ALTER TABLE "TB_LIM_TEST"
    ADD COLUMN "shMatchMessage" VARCHAR(255) NULL;

COMMENT
ON COLUMN "TB_LIM_TEST"."shMatchMessage" IS '上海监管平台测试项目匹配信息';



-- ----------------------------------------------------
-- 附件表中添加【上海监管平台附件ID】字段
-- ----------------------------------------------------
ALTER TABLE "TB_BASE_DOCUMENT"
    ADD COLUMN "shDocumentId" VARCHAR(50) NULL;

COMMENT
ON COLUMN "TB_BASE_DOCUMENT"."shDocumentId" IS '上海监管平台附件ID';

-- ----------------------------------------------------
-- 上海监管平台项目推送表中的【监管平台甲方企业名称】修改为可为空
-- ----------------------------------------------------
ALTER TABLE "TB_PRO_PROJECTCONTRACT" MODIFY shanghaiEntName VARCHAR (255) NULL;

-- ----------------------------------------------------
-- 上海监管平台项目推送表中的添加【推送采样类型与任务类型是否一致】字段
-- ----------------------------------------------------
ALTER TABLE "TB_PRO_PROJECTCONTRACT"
    ADD COLUMN isShProjectTypeMatch BIT DEFAULT 1;

COMMENT
ON COLUMN "TB_PRO_PROJECTCONTRACT"."isShProjectTypeMatch" IS '推送采样类型与任务类型是否一致';