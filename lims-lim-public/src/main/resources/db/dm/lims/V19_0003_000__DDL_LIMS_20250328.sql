ALTER TABLE tb_base_consumable ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_base_consumable.rangeLow is '范围低点';
ALTER TABLE tb_base_consumable ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_base_consumable.rangeHigh is '范围高点';
ALTER TABLE tb_base_consumableofmixed ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_base_consumableofmixed.rangeLow is '范围低点';
ALTER TABLE tb_base_consumableofmixed ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_base_consumableofmixed.rangeHigh is '范围高点';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_pro_qualitycontrol.rangeLow is '范围低点';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_pro_qualitycontrol.rangeHigh is '范围高点';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_pro_qualitymanage.rangeLow is '范围低点';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_pro_qualitymanage.rangeHigh is '范围高点';