-- 海洋沉积物报告报表配置
DELETE
FROM TB_LIM_ReportConfig
WHERE id = 'd70cfbd9-fb3a-400f-a805-267a75d90f7c';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('d70cfbd9-fb3a-400f-a805-267a75d90f7c', 1, 'MarineStd', '标准版海洋沉积物报告.doc', 'Report/标准版报告.doc',
        'output/Report/海洋沉积物报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{"sort":"orderNum-"}', NULL,
        17000, 4, '海洋沉积物报告', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-09-12 09:42:35', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-09-12 13:30:18', 'reportId,sortId', 'Report', 'MarineStd', 0, '', NULL, '1.0', 'LET-', '', 0,
        NULL);

DELETE
FROM TB_LIM_ReportApply
WHERE id = '29656026-75e1-4935-93cc-b55073dc4e8f';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('29656026-75e1-4935-93cc-b55073dc4e8f', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c', 'ReportEditNew', '报告编制',
        'MarineStd', '海洋沉积物报告', 1, 1, 1, NULL, '报告编制:电子报告', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-09-12 10:26:24', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-09-12 10:26:24', NULL);

DELETE
FROM TB_LIM_RecordConfig
WHERE id = 'f709e9d7-9cdb-4b18-b707-4a6a637d3efe';

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark, isDeleted, orgId,
                                creator, createDate, domainId, modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('f709e9d7-9cdb-4b18-b707-4a6a637d3efe', '海洋沉积物报告', 3, 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        '00000000-0000-0000-0000-000000000000', NULL, 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-12 10:30:51', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-12 08:44:06', NULL, 9955);

DELETE
FROM TB_LIM_ReportModule
WHERE id IN ('f035069c-edb5-4264-8aec-9502a71d2a31', 'f09342d4-8abc-4987-83d7-f60861a332e2');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('f035069c-edb5-4264-8aec-9502a71d2a31', 'dtMarineStdTable', '标准版海洋沉积物检测结果表组件',
        'dtMarineStdTable', 'dtMarineSource', 5, 13, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-09-12 10:39:57', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-09-12 10:39:57', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('f09342d4-8abc-4987-83d7-f60861a332e2', 'marineStdDataSource', '标准版海洋沉积物报告检测数据主表',
        'dtDataSource', '', 0, 0,
        '["dtWaterHeadStdTable", "dtMarineStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource", "dtKbStdTable"]',
        1, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-09-12 10:38:34',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-09-12 10:38:34', '0', '0',
        0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportConfig2Module
WHERE id IN ('22384ecb-5949-48ab-9430-3c01ab0e53e8',
             '26424b1c-7790-476f-941c-d61e332b7194',
             '48920af7-0824-439f-b377-2fc1a22da3d4',
             '7c6f557b-b597-4eae-8aff-2640c5c08684',
             '82a4dbfe-a0c7-4419-b18d-2ff5e0c906cc',
             '83865d91-5f9b-484f-8422-15775cf2e004',
             '9b653840-dd29-4ae9-8fae-72e7ffdb140e',
             'b8cfeff8-62fb-4b4a-a2fd-3e697c8b616f');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('22384ecb-5949-48ab-9430-3c01ab0e53e8', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('26424b1c-7790-476f-941c-d61e332b7194', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('48920af7-0824-439f-b377-2fc1a22da3d4', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('7c6f557b-b597-4eae-8aff-2640c5c08684', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        'bf67cab8-6760-4a4c-84f5-ff89e789161b');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('82a4dbfe-a0c7-4419-b18d-2ff5e0c906cc', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        '6130da13-013a-4a25-a856-bd1f16fc691b');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('83865d91-5f9b-484f-8422-15775cf2e004', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('9b653840-dd29-4ae9-8fae-72e7ffdb140e', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        '844acab5-8883-44d1-b8fa-af039526e967');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b8cfeff8-62fb-4b4a-a2fd-3e697c8b616f', 'd70cfbd9-fb3a-400f-a805-267a75d90f7c',
        'f09342d4-8abc-4987-83d7-f60861a332e2');

DELETE
FROM TB_LIM_ReportModule2GroupType
WHERE id = 'd3eed8b8-6083-47b6-ad2f-52d4045f3caa';

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('d3eed8b8-6083-47b6-ad2f-52d4045f3caa', 'b8cfeff8-62fb-4b4a-a2fd-3e697c8b616f',
        'sampleData_samplingTimeBegin_dateGroupType', 9);


