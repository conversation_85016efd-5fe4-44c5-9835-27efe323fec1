delete
from TB_LIM_ReportConfig
where id = '21b98d94-40dd-11f0-8cfa-02420a0000da';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('21b98d94-40dd-11f0-8cfa-02420a0000da', 1, 'YQHSMBXSamplingRecord', 'LET-WI-S-106-F01油气回收密闭性检测原始记录表.xlsx',
        'Sampling/LET-WI-S-106-F01油气回收密闭性检测原始记录表.xlsx', 'output/SamplingRecords/LET-WI-S-106-F01油气回收密闭性检测原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.YQHSMBXSamplingRecordService', '', '',
        9999, 3, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-11-13 17:07:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-06-04 08:47:09', '', 'Sampling', 'YQHSMBXSamplingRecord', 0, '[projectCode]', '', '', '', '', 1, 22);

delete
from TB_LIM_ReportApply
where reportConfigId = '21b98d94-40dd-11f0-8cfa-02420a0000da';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('2f7c78fd-40dd-11f0-8cfa-02420a0000da', '21b98d94-40dd-11f0-8cfa-02420a0000da', 'PrepareSample', '采样准备', '采样单',
        '油气回收密闭性检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-13 17:28:27', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-13 17:28:27', 1);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('2f7ce962-40dd-11f0-8cfa-02420a0000da', '21b98d94-40dd-11f0-8cfa-02420a0000da', 'LocalTask', '现场任务', '采样单',
        '油气回收密闭性检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-13 17:28:46', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-13 17:28:46', NULL);

delete
from TB_LIM_ReportConfig
where id = 'add25cb4-40dc-11f0-8cfa-02420a0000da';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark,
                                isDeleted, orgId, creator, createDate, domainId, modifier,
                                modifyDate, dataMethod, typeCode, strUrl, isDefineFileName,
                                defineFileName, beanName, versionNum, controlNum, reportName,
                                validate, usageNum)
VALUES ('add25cb4-40dc-11f0-8cfa-02420a0000da', 1, 'YQHSQYBSamplingRecord', 'LET-WI-S-106-F01油气回收气液比原始记录表.xlsx',
        'Sampling/LET-WI-S-106-F01油气回收气液比原始记录表.xlsx', 'output/SamplingRecords/LET-WI-S-106-F01油气回收气液比原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.YQHSQYBSamplingRecordService', '', '',
        9999, 3, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-11-13 17:07:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-06-04 08:42:45', '', 'Sampling', 'YQHSQYBSamplingRecord', 0, '[projectCode]', '', '', '', '', 1, 22);


delete
from TB_LIM_ReportApply
where reportConfigId = 'add25cb4-40dc-11f0-8cfa-02420a0000da';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('e7cb4113-40dc-11f0-8cfa-02420a0000da', 'add25cb4-40dc-11f0-8cfa-02420a0000da', 'PrepareSample', '采样准备', '采样单',
        '油气回收气液比原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-13 17:28:27', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-13 17:28:27', 1);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('e7cba7d5-40dc-11f0-8cfa-02420a0000da', 'add25cb4-40dc-11f0-8cfa-02420a0000da', 'LocalTask', '现场任务', '采样单',
        '油气回收气液比原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-13 17:28:46', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-13 17:28:46', NULL);

delete
from TB_LIM_ReportConfig
where id = '132fb059-40db-11f0-8cfa-02420a0000da';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('132fb059-40db-11f0-8cfa-02420a0000da', 1, 'YQHSYZSamplingRecord', 'LET-WI-S-106-F01油气回收液阻检测原始记录表.xlsx',
        'Sampling/LET-WI-S-106-F01油气回收液阻检测原始记录表.xlsx', 'output/SamplingRecords/LET-WI-S-106-F01油气回收液阻检测原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.YQHSYZSamplingRecordService', '', '',
        9999, 3, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-11-13 17:07:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-06-04 08:32:15', '', 'Sampling', 'YQHSYZSamplingRecord', 0, '[projectCode]', '', '', '', '', 1, 22);

delete
from TB_LIM_ReportApply
where reportConfigId = '132fb059-40db-11f0-8cfa-02420a0000da';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('b60cfed4-40db-11f0-8cfa-02420a0000da', '132fb059-40db-11f0-8cfa-02420a0000da', 'PrepareSample', '采样准备', '采样单',
        '油气回收液阻检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-13 17:28:27', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-13 17:28:27', 1);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('b612e98b-40db-11f0-8cfa-02420a0000da', '132fb059-40db-11f0-8cfa-02420a0000da', 'LocalTask', '现场任务', '采样单',
        '油气回收液阻检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-13 17:28:46', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-13 17:28:46', NULL);

