delete from TB_LIM_ReportConfig where id = '973ff1fd-82ed-4034-9d98-6b51fa77f153';

-- 加油站报告报表配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('973ff1fd-82ed-4034-9d98-6b51fa77f153', 1, 'GasStationStd', '标准版加油站监测报告.doc', 'Report/标准版报告.doc',
        'output/Report/加油站监测报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', NULL,
        0, 4, '加油站监测报告', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-06-27 14:19:13', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-06-27 16:23:29', 'reportId,sortId', 'Report', 'GasStationStd', 0, '', NULL, NULL, NULL, NULL, 0, NULL);

delete from TB_LIM_ReportApply where id = '402e2e10-2743-45f4-9ffa-a307ac138358';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact,
                               isShow, remark, location, orgId, creator, createDate, domainId,
                               modifier, modifyDate, blankFill)
VALUES ('402e2e10-2743-45f4-9ffa-a307ac138358', '973ff1fd-82ed-4034-9d98-6b51fa77f153', 'ReportEditNew', '报告编制V2.0',
        'GasStationStd', '加油站监测报告', 1, 1, 1, '', '报告编制:电子报告', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-06-27 14:21:05', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-06-27 14:21:05', NULL);

delete from TB_LIM_RecordConfig where id = 'cee18c57-c1dd-4c80-b555-46fe8cf58a0c';

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark,
                                isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate,
                                sampleTypeIds, orderNum)
VALUES ('cee18c57-c1dd-4c80-b555-46fe8cf58a0c', '加油站报告', 3, '973ff1fd-82ed-4034-9d98-6b51fa77f153',
        '00000000-0000-0000-0000-000000000000', '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-06-27 13:37:48', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-06-27 13:37:48', '', 0);

delete from TB_LIM_ReportModule where id in ('1dada012-60f5-4a41-af27-fe014c248577', 'fd32a590-474e-4c39-b024-66016828dddf',
                                             '408668bf-746d-4769-ad28-0c0e14bd33c0', '58992fa0-0f30-4ccc-9801-b68999e16635',
                                             'd3579ed7-4c92-4e73-b405-39653ed28313');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount,
                                testCount, sonTableJson, isCompound, orgId, creator, createDate,
                                domainId, modifier, modifyDate, totalTest, auxiliaryInstrument,
                                conversionCalculationMode, speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('1dada012-60f5-4a41-af27-fe014c248577', 'dtGasStationHeadTable', '标准版加油站表头组件', 'dtGasStationHeadTable', '', 0,
        0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-06-27 13:15:08',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-06-27 13:15:08', '0', '0',
        0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount,
                                testCount, sonTableJson, isCompound, orgId, creator, createDate,
                                domainId, modifier, modifyDate, totalTest, auxiliaryInstrument,
                                conversionCalculationMode, speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('fd32a590-474e-4c39-b024-66016828dddf', 'dtGasStationQybTable', '标准版加油站气液比检测结果表组件', 'dtGasStationQybTable',
        'dtQybSource', 10, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-06-27 13:23:01', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-06-27 13:23:01', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('408668bf-746d-4769-ad28-0c0e14bd33c0', 'dtGasStationMbxTable', '标准版加油站密闭性检测结果表组件', 'dtGasStationMbxTable',
        'dtMbxSource', 10, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-06-27 13:24:26', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-06-27 13:24:26', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('58992fa0-0f30-4ccc-9801-b68999e16635', 'dtGasStationYzTable', '标准版加油站液阻检测结果表组件', 'dtGasStationYzTable',
        'dtYzSource', 10, 3, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-06-27 13:26:15', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-06-27 13:26:15', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount,
                                testCount, sonTableJson, isCompound, orgId, creator, createDate,
                                domainId, modifier, modifyDate, totalTest, auxiliaryInstrument,
                                conversionCalculationMode, speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('d3579ed7-4c92-4e73-b405-39653ed28313', 'gasStationStdDataSource', '标准版加油站报告检测数据主表', 'dtDataSource', '', 0, 0,
        '["dtGasStationHeadTable", "dtGasStationQybTable", "dtGasStationMbxTable", "dtGasStationYzTable"]',
        1, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-06-27 13:29:31',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-06-27 13:29:31', '0', '0',
        0, 0, 0, 1);

delete from TB_LIM_ReportConfig2Module where id in ('3da92213-aeed-4698-8e63-73e7889568b9', 'c2af299c-6c03-40fe-b4d1-823fd0b80ad4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('3da92213-aeed-4698-8e63-73e7889568b9', '973ff1fd-82ed-4034-9d98-6b51fa77f153',
        'd3579ed7-4c92-4e73-b405-39653ed28313');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('c2af299c-6c03-40fe-b4d1-823fd0b80ad4', '973ff1fd-82ed-4034-9d98-6b51fa77f153',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
