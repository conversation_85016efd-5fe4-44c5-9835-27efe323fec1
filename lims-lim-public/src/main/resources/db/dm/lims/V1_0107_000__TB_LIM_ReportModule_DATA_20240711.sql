-- 社会生活噪声报告组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('4f5f20ad-e13c-4b31-aa04-bd89bc33aac7', 'dtSocialNoiseHeadStdTable', '标准版社会生活噪声表头组件',
        'dtSocialNoiseHeadStdTable', '', 0, 0, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:05:33', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:05:33', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('29b12294-f422-4b0a-8d8d-46c888532429', 'dtSocialNoiseTable', '标准版社会生活噪声检测结果表组件', 'dtSocialNoiseTable',
        'dtSocialNoiseSource', 10, 7, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:11:26', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:11:26', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('7c8e8608-d827-456c-9193-478028d4060d', 'socialNoiseDataSource', '标准版社会生活噪声报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtSocialNoiseHeadStdTable\", \"dtSocialNoiseTable\"]', 1, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:56:09', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:56:09', '0', '0', 0, 0, 0);


-- 废气比对报告组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('f4383146-83e4-451a-94c8-df5babbdb25b', 'dtGasCompareTable', '标准版废气比对检测结果表组件', 'dtGasCompareTable',
        'dtGasCompareSource', 10000, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:05:59', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:05:59', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('4dbc7c06-391d-4111-84d6-cfd058d72494', 'dtGasCompareDivTable', '标准版废气比对检测结果表组件（数据对差）', 'dtGasCompareDivTable',
        'dtGasCompareDivSource', 10000, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:08:20', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:08:20', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('030ab6a7-b101-4b40-bfd8-cee47b6b1250', 'dtGasCompareInstTable', '标准版废气比对仪器信息表组件', 'dtGasCompareInstTable',
        'dtGasCompareInstSource', 10000, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:09:38', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:09:38', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('f94f8a55-1fb0-42c1-a87b-d4b078a2c697', 'gasCompareDataSource', '标准版废气比对报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtGasCompareTable\", \"dtGasCompareDivTable\", \"dtGasCompareInstTable\", \"outParallelStdDataSource\"]',
        1, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-07-09 21:12:13',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-07-09 21:12:13', '0', '0',
        0, 0, 0);