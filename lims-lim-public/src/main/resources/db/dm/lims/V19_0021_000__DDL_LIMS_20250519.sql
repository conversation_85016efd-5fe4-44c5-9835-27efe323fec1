-- 分析数据统计数据
CREATE TABLE "TB_PRO_ANALYSESTATISTICSDATA"
(
    "ID" VARCHAR(50) NOT NULL,
    "PROJECTID" VARCHAR(50) DEFAULT '' NOT NULL,
    "SAMPLEID" VARCHAR(50) DEFAULT '' NOT NULL,
    "<PERSON><PERSON><PERSON>CODE" VARCHAR(50) DEFAULT '',
    "WORKSHEETFOLDERID" VARCHAR(50) DEFAULT '' NOT NULL,
    "TESTID" VARCHAR(50) DEFAULT '' NOT NULL,
    "PROJECTTYPEID" VARCHAR(50) DEFAULT '' NOT NULL,
    "PROJECTTYPENAME" VARCHAR(255),
    "SAMPLETYPEID" VARCHAR(50) DEFAULT '' NOT NULL,
    "SAMPLETYPENAME" VARCHAR(255),
    "REDANALYZEITEMNAME" VARCHAR(100) DEFAULT '',
    "RECEIVETIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "ANALYSEDATASTATUS" VARCHAR(50) DEFAULT '',
    "ANALYSTID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "ANALYSTNAME" VARCHAR(50) DEFAULT '',
    "RECEIVEID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "RECEIVESTATUS" VARCHAR(50) DEFAULT '',
    "DATASTATUS" INT DEFAULT 1 NOT NULL,
    "STATUS" VARCHAR(50) DEFAULT '',
    "ANALYZETIME" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00' NOT NULL,
    "ANALYSEDAYLEN" INT DEFAULT 2 NOT NULL,
    "SAMPLINGTIMEBEGIN" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00' NOT NULL,
    "TESTISDELETED" BIT,
    "QCTYPE" INT DEFAULT (-1) NOT NULL,
    "QCGRADE" INT DEFAULT (-1) NOT NULL,
    "ORGID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "MODIFYDATE" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "RECEIVESAMPLEDATE" TIMESTAMP(0),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE  INDEX "IDX_OPTIMIZED" ON "TB_PRO_ANALYSESTATISTICSDATA"("SAMPLETYPEID" ASC,"RECEIVETIME" ASC,"ANALYSTID" ASC,"ANALYSEDATASTATUS" ASC,"PROJECTTYPEID" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE  INDEX "IDX_ANALYSEDATA1" ON "TB_PRO_ANALYSESTATISTICSDATA"("RECEIVETIME" ASC,"SAMPLETYPEID" ASC,"PROJECTTYPEID" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE  INDEX "IDX_OTHER_CONDITIONS" ON "TB_PRO_ANALYSESTATISTICSDATA"("ANALYSTID" ASC,"ANALYSEDATASTATUS" ASC,"PROJECTTYPEID" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "TB_PRO_ANALYSESTATISTICSDATA" IS '分析数据统计表';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ID" IS 'id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."PROJECTID" IS '项目id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."SAMPLEID" IS '样品id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."SAMPLECODE" IS '样品编号';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."WORKSHEETFOLDERID" IS '工作单id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."TESTID" IS '测试id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."PROJECTTYPEID" IS '项目类型id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."PROJECTTYPENAME" IS '项目类型名称';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."SAMPLETYPEID" IS '样品类型id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."SAMPLETYPENAME" IS '样品类型名称';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."REDANALYZEITEMNAME" IS '分析项目名称';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."RECEIVETIME" IS '登记时间';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ANALYSEDATASTATUS" IS '数据状态（处理后）';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ANALYSTID" IS '分析人员Id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ANALYSTNAME" IS '分析人员';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."RECEIVEID" IS '送样单id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."RECEIVESTATUS" IS '领样单状态';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."DATASTATUS" IS '数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."STATUS" IS '分析数据状态';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ANALYZETIME" IS '数据分析时间';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ANALYSEDAYLEN" IS '分析时长（天数）';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."SAMPLINGTIMEBEGIN" IS '采样时间';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."TESTISDELETED" IS '测试项目假删';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."QCTYPE" IS '质控类型';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."QCGRADE" IS '质控等级';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."ORGID" IS '组织机构id';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."CREATEDATE" IS '创建时间';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."MODIFYDATE" IS '修改时间';

COMMENT ON COLUMN "TB_PRO_ANALYSESTATISTICSDATA"."RECEIVESAMPLEDATE" IS '交接时间';

