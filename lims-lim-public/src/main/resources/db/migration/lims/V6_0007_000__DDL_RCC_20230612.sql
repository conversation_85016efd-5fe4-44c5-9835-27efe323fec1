-- ------------------------------------------------------------
-- 创建点位类型拓展字段配置表（TB_MONITOR_PointExtendConfig）
-- ------------------------------------------------------------
CREATE TABLE TB_MONITOR_PointExtendConfig
(
    id             varchar(50) primary key NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '标识',
    pointType      varchar(50) NULL COMMENT '点位类型(常量控制)',
    paramsId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数Id',
    filedName      varchar(50) NULL COMMENT '拓展字段名称',
    filed<PERSON><PERSON><PERSON>     varchar(50) NULL COMMENT '拓展字段别名',
    defaultValue   varchar(100) NULL COMMENT '默认值',
    orderNum       int(11) NULL COMMENT '排序值',
    dataSource     varchar(2000) NULL COMMENT '数据源',
    codeDataSource varchar(100) NULL COMMENT '常量数据源(常量类型)',
    dataSourceUrl  varchar(100) NULL COMMENT '数据源请求接口',
    urlReturnKey   varchar(100) NULL COMMENT 'Key对应字段',
    urlReturnValue varchar(100) NULL COMMENT 'Value对应字段',
    treeChildFiled varchar(100) NULL COMMENT '下拉树对应子数据字段',
    dataSourceType int(11) NOT NULL DEFAULT 1 COMMENT '下拉框数据源类型（枚举EnumControlDataSourceType:0.无 1.接口请求 2.常量数据源 3.自定义数据源）',
    defaultControl int(11) NOT NULL DEFAULT 1 COMMENT '默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）',
    requiredInd    bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否必填',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '点位类型拓展字段配置表';

-- ------------------------------------------------------------
-- 创建点位拓展字段数据表（TB_MONITOR_PointExtendData）
-- ------------------------------------------------------------
CREATE TABLE TB_MONITOR_PointExtendData
(
    id             varchar(50) primary key NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '标识',
    pointType      varchar(50) NULL COMMENT '点位类型(常量控制)',
    extendConfigId varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位拓展配置Id',
    fixedPointId   varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位Id',
    filedName      varchar(50) NULL COMMENT '拓展字段名称',
    filedAlias     varchar(50) NULL COMMENT '拓展字段别名',
    filedValue     varchar(100) NULL COMMENT '拓展字段值',
    orderNum       int(11) NULL COMMENT '排序值',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '点位拓展字段数据表';

-- ------------------------------------------------------------
-- 修改点位表点位类型字段长度
-- ------------------------------------------------------------
ALTER TABLE TB_MONITOR_Fixedpoint MODIFY COLUMN folderType varchar(1000) NOT NULL COMMENT '点位类型（常量），多个类型以逗号隔开';