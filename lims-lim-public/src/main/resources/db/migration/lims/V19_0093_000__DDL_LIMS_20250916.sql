CREATE TABLE TB_LIM_Test2ShSamplingMethod
(
    id                   VARCHAR(50) NOT NULL COMMENT '主键标识',
    testId               VARCHAR(50) NOT NULL COMMENT '测试项目id',
    sampleTypeId         VARCHAR(50) NOT NULL COMMENT '检测类型id',
    shSamplingMethodId   VARCHAR(50) NULL COMMENT '监管平台采样方法id',
    shSamplingMethodName VARCHAR(255) NULL COMMENT '监管平台采样方法名称',
    orgId                VARCHAR(50) NOT NULL COMMENT '所属机构ID',
    domainId             VARCHAR(50) NOT NULL COMMENT '所属实验室ID',
    creator              VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier             VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT '测试项目与监管平台采样方法关联表';

insert into TB_LIM_Test2ShSamplingMethod (id, testId, sampleTypeId, shSamplingMethodId, shSamplingMethodName, orgId, domainId, creator, createDate, modifier, modifyDate)
select UUID(), t.id, st.id, t.shSamplingMethodId, t.shSamplingMethodName, t.orgId, t.domainId, t.creator, NOW(), t.modifier,NOW()
from tb_lim_test t,
     tb_base_sampletype st
where st.parentId = t.sampleTypeId
  and st.isDeleted = 0
