-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- ---------------- Base模块 --------------------
-- ----------------------------------------------

create table TB_BASE_Consumable
(
    id                varchar(50)                                                not null comment '主键'
        primary key,
    consumableName    varchar(50)                                                not null comment '名称',
    pinYin            varchar(50) comment '拼音',
    fullPinYin        varchar(100) comment '全拼',
    codeInStation     varchar(20) comment '编号（本站编号）',
    consumableCode    varchar(20) comment '标样编号',
    specification     varchar(50) comment '规格',
    unit              varchar(50) comment '单位名称',
    unitId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '单位Id',
    grade             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '等级常量（Guid）（BASE_ConsumableGrade:进口、分析纯、FMP、高纯等）',
    categoryId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '类别常量（Guid）（BASE_ConsumableCategory:高压气体、易制毒品、化学试剂等）',
    warningNum        decimal(18, 4)                                             not null comment '警告数量',
    sendWarnUserId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '提醒人id（Guid）',
    alias             varchar(255) comment '别名',
    isPoison          bit         default b'0'                                   not null comment '是否易制毒',
    keepCondition     varchar(1000) comment '保存条件',
    safetyInstruction varchar(1000) comment '安全须知',
    remark            varchar(1000) comment '备注',
    isStandard        bit         default b'0'                                   not null comment '是否标样（0代表消耗品，1代表标样）',
    dilutedSolution   varchar(255) comment '稀释液',
    dilutionMethod    varchar(255) comment '稀释方法',
    concentration     varchar(255) comment '浓度',
    uncertainty       varchar(255) comment '不确定度',
    isMixedStandard   bit         default b'0'                                   not null comment '是否混标（0代表否  1代表是）',
    inventory         decimal(18, 4) comment '库存数量',
    sendWarnTime      datetime    default '1753-01-01 00:00:00'                  not null comment '（预留3.2）提醒时间',
    standard          varchar(50) comment '（预留3.2）标值',
    nationalStandard  varchar(100) comment '（预留3.2）国家标准',
    constantVolumeM   varchar(100) comment '（预留3.2）定容介质',
    molecularWeight   varchar(100) comment '（预留3.2）分子量',
    molecularFormula  varchar(100) comment '（预留3.2）分子式',
    useWay            varchar(255) comment '（预留3.2）使用方法',
    density           varchar(100) comment '（预留3.2）密度',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

create table TB_BASE_ConsumableDetail
(
    id               varchar(50)                                                   not null comment '主键'
        primary key,
    parentId         varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '消耗品/标样id（Guid）',
    unitId           varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '单位Id（冗余）（Guid）',
    unitName         varchar(100) comment '单位名称（冗余）',
    inventory        decimal(18, 4)                                                not null comment '入库数量',
    storage          decimal(18, 4)                                                not null comment '库存数量',
    unitPrice        decimal(18, 2) comment '单价',
    productionCode   varchar(50) comment '生产批号',
    storageDate      datetime       default '1753-01-01 00:00:00'                  not null comment '入库时间',
    manufacturerName varchar(50) comment '生产厂商名字',
    expiryDate       datetime       default '1753-01-01 00:00:00'                  not null comment '有效日期',
    supplierId       varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '供应厂商id（Guid）',
    supplierName     varchar(100) comment '供应商名字',
    checkerId        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '验收人id（Guid）',
    purchasingDate   datetime       default '1753-01-01 00:00:00'                  not null comment '验收日期',
    checkerResult    int            default 1                                      not null comment '验收结论：枚举（EnumCheckerResult：1合格、2不合格、3过期)',
    appearance       varchar(100) comment '外观',
    checkItem        varchar(100) comment '检验/验证项目',
    buyReason        varchar(255) comment '购买原因',
    keepPlace        varchar(100) comment '存放位置',
    remark           varchar(1000) comment '备注',
    isLocked         bit            default b'0'                                   not null comment '是否锁定',
    productionDate   datetime       default '1753-01-01 00:00:00'                  not null comment '（预留3.2）生产日期',
    purchaser        varchar(50) comment '（预留3.2）购买人',
    codeInType       varchar(50) comment '（预留3.2）类型内编号',
    expiredStatus    int            default 0                                      not null comment '（预留3.2）过期是否处理(枚举EnumExpiredStatus：0.未处理 1 提交处理 2  处理通过 3.处理未通过)',
    purchaseNum      varchar(255) comment '（预留3.2）购买证明号',
    validTime        datetime       default '1753-01-01 00:00:00'                  not null comment '（预留3.2）证书有效期',
    purchaseCount    decimal(18, 4) default -1.0000                                not null comment '（预留3.2）准购数量',
    storageNum       varchar(255) comment '（预留3.2）入库单号',
    buyCount         decimal(18, 4) default -1.0000                                not null comment '（预留3.2）购买数量',
    orgId            varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    sendWarnUserId   varchar(255)
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


create table TB_BASE_ConsumableLog
(
    id                   varchar(50)                                                not null comment '主键'
        primary key,
    consumablePickId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '领料单Id',
    pickNum              varchar(50) comment '领用单号',
    userId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '领用人',
    amount               decimal(18, 4)                                             not null comment '领用数量',
    remark               varchar(1000) comment '备注',
    balance              decimal(18, 4)                                             not null comment '领用结存',
    occurrenceTime       datetime    default '1753-01-01 00:00:00'                  not null comment '领用时间',
    consumableDetailId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '领用批次Id',
    consumableId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '消耗品Id',
    consumingPersonsName varchar(50) comment '领用人名字',
    pickTypeId           int         default 0                                      not null comment '（3.2预留）领用类型(枚举EnumPickType：0.领用1.盘库)',
    orgId                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate           datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate           datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

create table TB_BASE_ConsumableOfMixed
(
    id            varchar(50)                                                not null comment '主键'
        primary key,
    consumableId  varchar(50)                                                not null comment '标样id（Guid）',
    analyzeItemId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id（Guid）',
    concentration varchar(255) comment '浓度',
    uncertainty   varchar(255) comment '不确定度',
    dimensionId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位id（Guid）',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

create table TB_BASE_Document
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    folderId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件夹Id（Guid）',
    folderName     varchar(255) comment '文件夹名称',
    filename       varchar(255) comment '文件名称',
    physicalName   varchar(255) comment '物理文件名',
    path           varchar(500) comment '文件路径',
    isDeleted      bit         default b'0'                                   not null comment '假删',
    isTranscript   bit         default b'0'                                   not null comment '是否副本',
    docTypeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件类型（常量BASE_DocumentExtendType 用于pro项目附件）',
    docTypeName    varchar(255) comment '文件类型名称',
    docSize        int         default 0                                      not null comment '文件大小',
    docSuffix      varchar(10) comment '文件后缀',
    downloadTimes  int         default 0                                      not null comment '下载次数',
    orderNum       int         default 0                                      not null comment '排序值',
    remark         varchar(1000) comment '备注',
    uploadPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '上传人Id',
    uploadPerson   varchar(50) comment '上传人姓名',
    isStick        bit         default b'0'                                   not null,
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

create table TB_BASE_Enterprise
(
    id                   varchar(50)                                                 not null comment 'id'
        primary key,
    name                 varchar(100)                                                not null comment '企业名称（污染源名称）（lims）',
    shortName            varchar(100) null comment '企业简称',
    code                 varchar(20) null comment '企业编码',
    socialCreditCode     varchar(50) null comment '社会信用代码',
    pinYin               varchar(50) null comment '拼音缩写（lims）',
    fullPinYin           varchar(100) null comment '全拼（lims）',
    buildStatus          int unsigned default 1 not null comment '建设状态（枚举EnumBuildStatus：0.建设中 1.已完成）',
    runDate              datetime     default '1753-01-01 00:00:00'                  not null comment '投产日期（lims）',
    passDate             datetime     default '1753-01-01 00:00:00'                  not null comment '成立时间',
    businessTypeId       varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '行业类型Id（Guid）（常量：BASE_BussniessType）',
    regTypeId            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '注册类型Id（Guid）（常量：BASE_RegType）',
    longitude            varchar(20) null comment '企业经度',
    latitude             varchar(20) null comment '企业纬度',
    longitudeOther       varchar(20) null comment '经度（其他地图）',
    latitudeOther        varchar(20) null comment '纬度（其他地图）',
    contactFax           varchar(50) null comment '联系人传真（lims）',
    contactMan           varchar(20) null comment '联系人（lims）',
    contactPhoneNumber   varchar(50) null comment '联系电话（lims）',
    contactTelPhone      varchar(50) null comment '联系手机（lims）',
    address              varchar(100) null comment '企业地址（lims）',
    email                varchar(50) null comment '联系邮箱（lims）',
    postalCode           varchar(10) null comment '邮政编码（lims）',
    corporationCode      varchar(20) null comment '法人代码（lims）',
    corporationName      varchar(50) null comment '法人代表（lims）',
    registeredCapital    decimal(18)  default -1                                     not null comment '注册资金（lims）',
    areaId               varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '所在地区Id（Guid）（lims）',
    areaName             varchar(1000) null comment '所属区域（lims）',
    nature               varchar(255) null comment '企业性质（lims）',
    businessScope        varchar(255) null comment '经营范围（lims）',
    acreage              varchar(50) null comment '占地面积（lims）',
    houseNature          varchar(255) null comment '厂房性质（lims）',
    owner                varchar(50) null comment '产权所有人（lims）',
    industrialPark       varchar(255) null comment '所在工业园区（lims）',
    employeeNumber       int          default -1                                     not null comment '员工人数（lims）',
    runDaysPerYear       int          default -1                                     not null comment '年正常生产天数（lims）',
    productValuePerYear  varchar(50) null comment '年产值（lims）',
    tax                  varchar(50) null comment '利税（lims）',
    type                 int          default 2                                      not null comment '企业种类(枚举EnumEntType：1.污染源 2.客户  4：供应商 8：分包商）使用按位与表示客户为污染源（lims） ',
    qualityCertification varchar(1000) null comment '质量认证（lims）',
    isEligibility        bit          default b'1'                                   not null comment '是否合格（lims）',
    orderNum             int          default 0                                      not null comment '排序号（lims）',
    isDeleted            bit          default b'0'                                   not null comment '假删字段（lims）',
    url                  varchar(100) null comment '企业网址（lims）',
    scope                varchar(255) null comment '规模（lims）',
    info                 mediumtext null comment '企业简介（lims）',
    openDate             datetime     default '1753-01-01 00:00:00'                  not null comment '开业日期（lims）',
    remark               varchar(1000) null comment '备注（lims）',
    systemType           int          default 1                                      not null comment '系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)',
    externalId           varchar(100) default '00000000-0000-0000-0000-000000000000' null comment '关联系统客户编号',
    orgId                varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator              varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate           datetime     default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId             varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier             varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate           datetime     default CURRENT_TIMESTAMP                      not null comment '修改时间',
    entrustRate          varchar(50) null,
    industryKind         varchar(100) null comment '所属行业'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

create table TB_BASE_EnterpriseExtend
(
    id                  varchar(50)                                                not null comment 'id'
        primary key,
    entId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '企业Id',
    pollutionSourceType varchar(300) null comment '污染源类型（常量 LIM_PollutionSourceType 多个类型用;隔开',
    isUsed              bit         default b'0'                                   not null comment '是否使用（账号）',
    attentionDegree     int         default -1                                     not null comment '关注程度（污染源）（EnumAttentionDegree：1：国控，2：省控，4：市控，8：区控）',
    isBreak             bit         default b'0'                                   not null comment '是否违约（客户）',
    subRate             varchar(50) null comment '分包费率（客户）',
    entrustRate         varchar(50) null comment '委托费率（客户）',
    breakInfo           varchar(1000) null comment '违约信息（客户）',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    pollutionCode       varchar(50) null comment '污染源编号'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

create table TB_BASE_Folder
(
    id         varchar(50)                                                not null comment '主键'
        primary key,
    folderName varchar(50) comment '文件夹名称',
    parentId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父级Id（Guid）',
    orderNum   int         default 0                                      not null comment '排序值',
    remark     varchar(1000) comment '备注',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_Instrument
(
    id                 varchar(50)                                                   not null comment 'id'
        primary key,
    instrumentName     varchar(50)                                         null comment '设备名称',
    pinYin             varchar(50)                                         null comment '拼音缩写',
    fullPinYin         varchar(100)                                        null comment '全拼',
    model              varchar(200)                                        null comment '规格型号',
    instrumentTypeId   varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '仪器类型（常量：BASE_InstrumentType）',
    insRange           varchar(200)                                        null comment '量程',
    nicetyRate         varchar(100)                                        null comment '准确度等级',
    price              decimal(18, 2)                                                not null comment '仪器价格',
    place              varchar(100)                                        null comment '所在地',
    manager            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '管理人员id（Guid）',
    managerName        varchar(50)                                         null comment '管理员名称（冗余）',
    isShowOnReport     bit            default b'1'                                   not null comment '是否出证(是、否)',
    useConditions      varchar(1000)                                                 null,
    state              int            default 1                                      not null comment '状态(枚举：EnumInstrumentStatus：0报废、1正常、2停用、3过期)',
    instrumentsCode    varchar(20)                                         null comment '本站编号',
    serialNo           varchar(100)                                        null comment '出厂编号',
    factoryName        varchar(100)                                        null comment '制造厂商名称',
    saleName           varchar(100)                                        null comment '供应商',
    purchaseDate       datetime       default '1753-01-01 00:00:00'                  not null comment '购置日期',
    belongDeptId       varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属科室id（Guid）',
    deptName           varchar(50)                                         null comment '所属科室名称（冗余）',
    fixedAssetsCode    varchar(20)                                         null comment '固定资产号',
    controlMeasures    varchar(1000)                                                 null,
    useMethod          varchar(1000)                                                 null,
    maintenanceCyc     decimal(18, 1) default -1.0                                   not null comment '维护周期(周)',
    maintenanceContent varchar(1000)                                       null comment '维护内容',
    maintenanceDate    datetime       default '1753-01-01 00:00:00'                  not null comment '最近日期（维护）',
    inspectPeriod      decimal(18, 1) default -1.0                                   not null comment '核查周期(月)',
    inspectMethod      varchar(1000)                                       null comment '核查方法',
    inspectResult      int            default 1                                      not null comment '核查结果(枚举：EnumInspectResult：1合格、0不合格)',
    inspectDate        datetime       default '1753-01-01 00:00:00'                  not null comment '最近日期（核查）',
    originCyc          decimal(18, 1) default 12.0                                   not null comment '溯源周期(月)',
    originType         int            default -1                                     not null comment '溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)',
    originUnit         varchar(100)                                        null comment '溯源单位',
    originDate         datetime       default '1753-01-01 00:00:00'                  not null comment '最近日期（溯源）',
    originEndDate      datetime       default '1753-01-01 00:00:00'                  not null comment '过期日期（溯源）',
    originResult       int            default 1                                      not null comment '溯源结果(枚举：EnumOriginResult：1合格、0不合格)',
    originRemark       varchar(1000)                                       null comment '溯源备注',
    instrColor         varchar(100)                                        null comment '颜色(玻璃仪器特有)',
    remark             varchar(1000)                                       null comment '备注',
    recentOpenDate     datetime       default '1753-01-01 00:00:00'                  not null comment '最近开启日期，与最近使用日期、最近检定日期不同',
    alarmThreshold     double         default 0                                      not null comment '设备开启阀值，即设备在该时长内没有启动则不允许使用，单位是天',
    isDeleted          bit            default b'0'                                   not null comment '是否删除',
    photoUrl           varchar(500)                                        null comment '图片路径',
    orgId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间',
    productDate        datetime       default '1753-01-01 00:00:00'                  not null,
    productCountry     varchar(50)                                         null,
    constractNo        varchar(50)                                         null,
    scrapTime          datetime       default '1753-01-01 00:00:00'                  null comment '报废时间',
    isInspected        bit            default b'1'                                   not null comment '是否核查',
    inspectMode        varchar(50)                                                   null comment '核查方式',
    inspectReason      varchar(200)                                                  null comment '核查原因',
    responsibleOffice  varchar(50)                                                   null comment '责任科室'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_Job
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    jobName        varchar(50)                                                not null comment '任务名称',
    jobGroup       varchar(50)                                                not null comment '任务组名',
    invokeTarget   varchar(500)                                               not null comment '调用目标字符串',
    cronExpression varchar(255)                                               not null comment 'cron执行表达式',
    misfirePolicy  int         default 3                                      not null comment '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
    isConcurrent   bit         default b'1'                                   not null comment '是否并发执行（1允许 0禁止）',
    status         int         default 0                                      not null comment '状态（1正常 0暂停）',
    remark         varchar(500) null comment '备注信息',
    isDeleted      bit         default b'0'                                   not null comment '是否删除（0不删除 1删除）',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '定时任务调度表' ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_JobInfo
(
    id               varchar(50)                                                not null comment '主键id'
        primary key,
    jobId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '任务主键',
    beginTime        varchar(10) null comment '每日发送的开始时间',
    endTime          varchar(10) null comment '每日发送的结束时间',
    isIncludeDefault bit         default b'0'                                   not null comment '是否包含默认人员',
    remindDays       int         default 0                                      not null comment '提醒天数',
    sendType         int         default 1                                      not null comment '1：平台  2：短信  4：APP  8：微信  16：钉钉',
    isDeleted        bit         default b'0'                                   not null comment '假删',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '任务详细信息' ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_JobInformPerson
(
    id        varchar(50)                                                not null comment '主键'
        primary key,
    jobInfoId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '任务信息id',
    personId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '人员id',
    orgId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室'
) comment '任务信息通知人' ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_LogForLuckySheet
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    operatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者id',
    operatorName varchar(50) null comment '操作者名字',
    operateTime  datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo  varchar(500) null comment '操作类型（新建、保存、修改等）',
    objectId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    remark       mediumtext null comment '备注',
    comment      varchar(1000) null comment '说明',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室'
) comment 'lucksheet操作记录表' ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_SystemConfig
(
    id                 varchar(50)                                                not null comment '主键'
        primary key,
    fullName           varchar(100)                                               not null comment '系统名称全写',
    shortName          varchar(50) null comment '系统名称简写',
    welcomeWord        varchar(255) null comment '欢迎登陆语',
    companyName        varchar(255) null comment '企业名称',
    companyAddress     varchar(1000) null comment '企业地址',
    companyPostCode    varchar(20) null comment '企业邮编',
    companyPhone       varchar(50) null comment '企业联系方式',
    companyEnglishName varchar(255) null comment '企业英文名称',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '系统信息管理配置' ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_BASE_SampleType
(
    id                  varchar(50)                                                not null comment '主键'
        primary key,
    industryTypeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '行业类型（Guid）',
    parentId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父节点（Guid）',
    typeCode            varchar(50) comment '唯一编号（预留）',
    typeName            varchar(50) comment '类型名称',
    remark              varchar(1000) comment '备注',
    laboratoryId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室（Guid）（预留）',
    shortName           varchar(50) comment '简称',
    isDeleted           bit         default b'0'                                   not null comment '是否删除',
    keepLongTime        int         default -1                                     not null comment '保留时长（预留）',
    reportingCycle      int         default -1                                     not null comment '报告周期（预留）',
    category            int         default -1                                     not null comment '样品分类(枚举EnumSampleCategory：1.大类2.样品类型3.模板,4.方案模板 )',
    orderNum            int         default 0                                      not null comment '排序值',
    icon                varchar(255) comment '图标',
    systemType          int         default 1                                      not null comment '系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate          datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate          datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    defaultLabelGroupId varchar(50)                                                null comment '默认标签分组id',
    fieldTaskGroupId    varchar(50)                                                null comment '现场任务分组id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;
