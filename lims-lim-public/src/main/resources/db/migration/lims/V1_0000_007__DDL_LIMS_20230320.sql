-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 ------------
-- ---------------- Pro模块 --------------------
-- ----------------------------------------------

create table TB_PRO_AnalyseBiologyData
(
    id            varchar(50)                                                not null
        primary key,
    parentId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父级Id',
    analyseDataId varchar(50)                                                not null comment '数据Id',
    taxonomyId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '生物Id',
    testValue     varchar(100)                                               null comment '数据结果',
    testValueD    decimal(20, 10)                                            not null comment '数据结果（用于计算）',
    dimension     varchar(50)                                                null comment '计量单位',
    dimensionId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位Id',
    volume        varchar(50)                                                null comment '体积',
    countValue    int         default 0                                      not null comment '计数值（统计量）',
    times         int         default 0                                      not null comment '次数',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '生物子数据' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_AnalyseData
(
    id                   varchar(50)                                                    not null comment 'id'
        primary key,
    workSheetId          varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '工作单子Id',
    workSheetFolderId    varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '工作单Id',
    subId                varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '分包单位id',
    sampleId             varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '样品Id',
    testId               varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '测试id',
    redAnalyzeItemName   varchar(100)                                                   null comment '分析项目名称',
    redAnalyzeMethodName varchar(255)                                                   null comment '分析方法名称',
    redCountryStandard   varchar(255)                                                   null,
    analyseItemId        varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    analyzeMethodId      varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id',
    qcId                 varchar(50)     default '00000000-0000-0000-0000-000000000000' not null,
    qcType               int             default -1                                     not null,
    qcGrade              int             default -1                                     not null,
    isQm                 bit             default b'0'                                   not null comment '是否是质控任务',
    receiveSubId         varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '领样单id',
    mostSignificance     int             default -1                                     not null comment '有效位数',
    mostDecimal          int             default -1                                     not null comment '小数位数',
    examLimitValue       varchar(50)                                                    null comment '检出限',
    dimensionId          varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '单位Id',
    dimension            varchar(50)                                                    null comment '单位（字符串）',
    testValue            varchar(100)                                                   null comment '出证结果',
    testOrignValue       varchar(100)                                                   null comment '检测结果（未修约）',
    testValueD           decimal(20, 10) default 0.0000000000                           not null comment '参与运算的值（检测结果的数值）（已修约）',
    testValueDstr        varchar(100)                                                   null comment '检测结果（已修约）',
    status               varchar(50)                                                    null comment '数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）',
    dataStatus           int             default 1                                      not null comment '数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）',
    dataChangeStatus     int             default 0                                      not null comment '数据变更状态（枚举EnumDataChangeStatus： 0.未变更 1.新增 2.修改 3.删除）（针对已经编制报告的数据修改状态-样品数据增删改）',
    analystId            varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '分析人员Id',
    analystName          varchar(50)                                                    null comment '分析人员',
    analyzeTime          datetime        default '1753-01-01 00:00:00'                  not null comment '数据分析时间',
    dataInputTime        datetime        default '1753-01-01 00:00:00'                  not null comment '数据录入时间',
    isDataEnabled        bit             default b'0'                                   not null comment '有效性（数据确认，是否出具在报告上）',
    isCompleteField      bit             default b'0'                                   not null comment '是否在现场完成(根据实际情况填写)',
    isOutsourcing        bit             default b'0'                                   not null comment '是否分包',
    isDeleted            bit             default b'0'                                   not null comment '是否删除',
    requireDeadLine      datetime        default '1753-01-01 00:00:00'                  not null comment '要求完成时间',
    grade                int             default 0                                      not null comment '等级(EnumProjectGrade：0.一般 1.紧急 2.特急)',
    deptId               varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '所属科室ID',
    isQualified          bit             default b'1'                                   not null comment '国检_是否合格(数据复验后是否仍超出限量值)',
    repeatTimes          int             default 0                                      not null comment '国检_复验次数',
    testTaskId           varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '水利_测试任务id',
    isPostCert           bit             default b'1'                                   not null comment '是有上岗证',
    certEffectiveTime    datetime        default '1753-01-01 00:00:00'                  not null comment '上岗证有效期',
    orgId                varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator              varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate           datetime        default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId             varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier             varchar(50)     default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate           datetime        default CURRENT_TIMESTAMP                      not null comment '修改时间',
    sampleTypeId         varchar(50)     default '00000000-0000-0000-0000-000000000000' null comment '样品检测类型id-冗余',
    gatherCode           varchar(1000)                                                  null comment '采集编号',
    qcInfo               varchar(100)                                                   null comment '质控信息',
    seriesValue          varchar(100)                                                   null comment '串联中间结果',
    finishTime           datetime        default '1753-01-01 00:00:00'                  not null comment '分析完成日期'
) comment '分析数据' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_AnalyseData
    on TB_PRO_AnalyseData (sampleId, analyzeTime, isDeleted);

create index IX_TB_PRO_AnalyseData2
    on TB_PRO_AnalyseData (orgId, qcId);

create index IX_TB_PRO_AnalyseData3
    on TB_PRO_AnalyseData (orgId, sampleId, isDeleted, isOutsourcing, isCompleteField, workSheetFolderId);

create index IX_TB_PRO_AnalyseData4
    on TB_PRO_AnalyseData (sampleId, orgId, isDeleted);

create index IX_TB_PRO_AnalyseData5
    on TB_PRO_AnalyseData (workSheetFolderId, sampleId, orgId, analystId, isDeleted);

create index IX_TB_PRO_AnalyseData6
    on TB_PRO_AnalyseData (sampleId, orgId, testId, analystId, workSheetId, isOutsourcing, isCompleteField,
                           dataStatus, isDeleted);

create table TB_PRO_AnalyseOriginalRecord
(
    id            varchar(50)                                                not null
        primary key,
    analyseDataId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '数据ID',
    json          mediumtext                                                 null comment '原始数据字符串',
    testFormulaId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '公式Id',
    testFormula   varchar(255)                                               null comment '公式',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    constraint UIX_TB_PRO_AnalyseOriginalRecord
        unique (analyseDataId)
) comment '分析数据公式数据' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;

create table TB_PRO_Comment
(
    id                varchar(50)                                                not null
        primary key,
    parentId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '冗余用于回复',
    objectId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评论关联id（项目id、我的审批、工作单、报告id）',
    objectType        int         default 0                                      not null comment '对象类型（枚举EnumCommentObjectType：1.项目 2.我的审批 3.工作单 4.报告）',
    commentPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评论人id（Guid）',
    commentPersonName varchar(50)                                                null comment '评论人名称',
    commentTime       datetime    default CURRENT_TIMESTAMP                      not null comment '评论时间',
    comment           mediumtext                                                 null comment '评论内容',
    commentType       int         default 1                                      not null comment '评论类型（枚举EnumCommentType：1、留言）',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_CommentComplimentDetail
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    commentId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评论Id',
    complimentorId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作人Id',
    complimentorName varchar(50)                                                null comment '操作人的姓名',
    complimentNumber int         default 0                                      not null comment '操作次数（考虑一个人多次点赞）',
    complimentDate   datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    complimentType   int         default 0                                      not null comment '评论类型（0：赞 1：踩）',
    `option`         mediumtext                                                 null comment '内容',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_CostInfo
(
    id                 varchar(50)                                                not null comment '主键id'
        primary key,
    projectId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    samplingCost       decimal(18, 2)                                             not null comment '采样总费',
    analyzeCost        decimal(18, 2)                                             not null comment '分析总费',
    reportCost         decimal(18, 2)                                             not null comment '报告费',
    climbCost          decimal(18, 2)                                             not null comment '登高费',
    expertCost         decimal(18, 2)                                             not null comment '专家费',
    otherCost          decimal(18, 2)                                             not null comment '其它费',
    laborNum           int         default 0                                      not null comment '人数',
    laborDay           int         default 0                                      not null comment '人天',
    laborUnit          decimal(18, 2)                                             not null comment '元/人天',
    laborCost          decimal(18, 2)                                             not null comment '人工费',
    carNum             int         default 0                                      not null comment '车辆数',
    carDay             int         default 0                                      not null comment '车辆天',
    carUnit            decimal(18, 2)                                             not null comment '元/辆天',
    carCost            decimal(18, 2)                                             not null comment '车辆费',
    expectedTotalCost  decimal(18, 2)                                             not null comment '总计',
    reportRate         decimal(18, 2)                                             not null comment '报告折扣率',
    offerRate          decimal(18, 2)                                             not null comment '折扣率',
    taxManageCost      decimal(18, 2)                                             not null comment '税收管理费',
    acturalTotalCost   decimal(18, 2)                                             not null comment '报价',
    schemeChangeStatus int         default 0                                      not null comment '方案变更状态（ 枚举EnumSchemeChangeStatus：0.未变更 1.已变更）',
    status             varchar(20)                                                not null comment '状态（新建、审核中、审批中、审核不通过、审批不通过、已完成）',
    isDeleted          bit         default b'0'                                   not null comment '假删字段',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '费用信息' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_CostInfoDetail
(
    id                   varchar(50)                                                not null comment '主键Id'
        primary key,
    costInfoId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '费用id',
    testId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    redAnalyzeItemName   varchar(50)                                                null comment '分析项目名称',
    redAnalyzeMethodName varchar(255)                                               null comment '分析方法名称',
    redCountryStandard   varchar(100)                                               null comment '标准编号',
    sampleTypeId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测类型id',
    sampleNum            int         default 0                                      not null comment '样品个数',
    samplingConfigCost   decimal(18, 2)                                             null comment '配置上的采样费（当时的配置）',
    samplingCost         decimal(18, 2)                                             not null comment '采样费',
    analyzeConfigCost    decimal(18, 2)                                             null comment '配置上的分析费（当时的配置）',
    analyzeCost          decimal(18, 2)                                             not null comment '分析费',
    testRate             decimal(18, 2)                                             not null comment '折扣率',
    totalCost            decimal(18, 2)                                             not null comment '合计',
    orgId                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate           datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate           datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '费用信息明细' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_DetailAnalyseData
(
    id             varchar(50)                                                not null comment '主键id'
        primary key,
    detailDataId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '详细数据id',
    testId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试id',
    analyseItemId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    examLimitValue varchar(50)                                                null comment '检出限',
    dimensionId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '单位Id',
    dimension      varchar(50)                                                null comment '单位（字符串）',
    testValue      varchar(100)                                               null comment '出证结果',
    testOrignValue varchar(100)                                               null comment '检测结果（未修约）',
    testValueD     decimal(20, 10)                                            not null comment '参与运算的值（检测结果的数值）（已修约）',
    testValueDstr  varchar(100)                                               null comment '检测结果（已修约）',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '组织机构id',
    status         varchar(50)                                                null comment '数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）',
    dataStatus     int         default 1                                      not null comment '数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）',
    isDataEnabled  bit         default b'0'                                   not null comment '有效性（数据确认，是否出具在报告上）',
    isDeleted      bit         default b'0'                                   not null comment '假删字段',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '详细的分析数据' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create index IX_TB_PRO_DetailAnalyseData
    on TB_PRO_DetailAnalyseData (detailDataId, analyseItemId, isDataEnabled, isDeleted, orgId);

create table TB_PRO_DetailData
(
    id                varchar(50)                                                not null comment '样品id'
        primary key,
    projectId         varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '项目id',
    receiveId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '送样记录id',
    projectCode       varchar(50)                                                null comment '流水编号',
    projectTypeId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目类型id（外键）',
    projectName       varchar(100)                                               null comment '项目名称',
    inputTime         datetime    default CURRENT_TIMESTAMP                      not null comment '项目登记时间(登记时间不能改)',
    inceptTime        datetime    default CURRENT_TIMESTAMP                      not null comment '委托时间',
    customerId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '委托单位id',
    customerName      varchar(100)                                               null comment '委托单位',
    sampleCode        varchar(50)                                                null comment '样品编号',
    sampleFolderId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位id',
    cycleOrder        int         default 0                                      not null comment '采样周期序数',
    timesOrder        int         default 0                                      not null comment '每周期次数序数',
    sampleOrder       int         default 0                                      not null comment '每次样品序数',
    redFolderName     varchar(100)                                               null comment '冗余-点位',
    samplingTimeBegin datetime    default '1753-01-01 00:00:00'                  not null comment '采样开始时间',
    samplingTimeEnd   datetime    default '1753-01-01 00:00:00'                  not null comment '采样结束时间',
    inspectedEntId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '受检单位Id',
    inspectedEnt      varchar(100)                                               null comment '受检单位',
    sampleTypeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测类型id',
    watchSpot         varchar(100)                                               null comment '点位名称',
    fixedPointId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '断面id（断面扩展id）',
    folderCode        varchar(50)                                                null comment '点位号',
    paramsData        mediumtext                                                 null comment '参数数据',
    isDeleted         bit         default b'0'                                   not null comment '假删字段',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '详细数据' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_Document2Log
(
    documentId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文档id',
    logId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '日志id',
    id         varchar(50)                                                not null comment '主键' primary key
) comment '文档相关日志' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_EvaluationRecord
(
    id                varchar(50)                                                not null
        primary key,
    objectId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null,
    objectType        int         default 0                                      not null comment '类型（枚举EnumEvaluationType：1、项目 2、点位 3、样品  4、数据）',
    folderPlan        int         default -1                                     not null comment '点位计划（枚举EnumEvaluationPlan：1、实际 2、计划）',
    evaluationId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价标准id',
    evaluationLevelId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价等级id',
    testId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    upperLimitValue   varchar(50)                                                null comment '上限',
    lowerLimitValue   varchar(50)                                                null comment '下限',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    upperLimitSymble  varchar(50)                                                null comment '上限运算符',
    lowerLimitSymble  varchar(50)                                                null comment '下限运算符'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_Explore
(
    id          varchar(50)                                                not null comment '主键id' primary key,
    projectId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    principalId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '踏勘负责人id',
    exploreDate datetime    default CURRENT_TIMESTAMP                      not null comment '踏勘日期',
    remarks     varchar(2000)                                              null comment '踏勘说明',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '踏勘表' ENGINE = InnoDB
                   CHARACTER SET = utf8
                   COLLATE = utf8_general_ci
                   ROW_FORMAT = Dynamic;

create table TB_PRO_ExpressageInfo
(
    id               varchar(50)                                                not null
        primary key,
    projectId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    reportId         varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '报告id（冗余，可能会精确到报告）',
    addressee        varchar(100)                                               null comment '收件单位',
    recipients       varchar(50)                                                null comment '收件人',
    recipientsPhone  varchar(50)                                                null comment '收件人电话',
    consigneeAddress varchar(255)                                               null comment '收件地址',
    expressCompany   varchar(100)                                               null comment '快递公司',
    expressNumber    varchar(50)                                                null comment '快递单号',
    sender           varchar(50)                                                null comment '寄件人',
    sendDate         datetime    default CURRENT_TIMESTAMP                      not null comment '寄件日期',
    remark           varchar(200)                                               null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_ExpressageInfo2Report
(
    id               varchar(50)                                                not null comment '主键id'
        primary key,
    expressageInfoId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '快递id',
    reportId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '报告id'
) comment '快递报告关联' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_FolderSign
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    sampleFolderId varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '点位id',
    cycleOrder     int         default -1                                     not null comment '周期',
    signTime       datetime    default CURRENT_TIMESTAMP                      not null comment '签到时间',
    signPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '签到人id',
    signPersonName varchar(50)                                                null comment '签到人名称',
    signLon        varchar(50)                                                null comment '签到经度',
    signLat        varchar(50)                                                null comment '签到纬度',
    signTip        varchar(200)                                               null comment '签到说明',
    voiceTip       varchar(500)                                               null comment '语音说明',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'

) comment '点位签到表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_LogForCost
(
    id               varchar(50)                                                    not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000'     not null comment '操作者Id',
    operatorName     varchar(50)                                                    null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                          not null comment '操作时间',
    operateInfo      varchar(500)                                                   null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000'     null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                    null comment '下一步操作人名字',
    logType          int         default 0                                          not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000'     not null comment '对象id',
    objectType       int         default 0                                          not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                     null comment '说明',
    opinion          varchar(1000)                                                  null comment '意见（评审意见等）',
    remark           varchar(1000)                                                  null comment '备注',
    orgId            varchar(50) default '000000000000-0000-0000-0000-000000000000' not null
) comment '项目日志' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForCost
    on TB_PRO_LogForCost (objectId, orgId);

create table TB_PRO_LogForData
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '数据日志' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForData
    on TB_PRO_LogForData (objectId, orgId);

create table TB_PRO_LogForPlan
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '方案日志' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForPlan
    on TB_PRO_LogForPlan (objectId, orgId);

create table TB_PRO_LogForProject
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '项目日志' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForProject
    on TB_PRO_LogForProject (objectId, orgId);

create table TB_PRO_LogForRecord
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '送样单、领样单日志' ENGINE = InnoDB
                              CHARACTER SET = utf8
                              COLLATE = utf8_general_ci
                              ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForRecord
    on TB_PRO_LogForRecord (objectId, orgId);

create table TB_PRO_LogForReport
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '报告日志' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForReport
    on TB_PRO_LogForReport (objectId, orgId);

create table TB_PRO_LogForSample
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '样品日志' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForSample
    on TB_PRO_LogForSample (objectId, orgId);

create table TB_PRO_LogForWorkSheet
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          int         default 0                                      not null comment '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       int         default 0                                      not null comment '对象类型（工作单、项目、数据等）',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见（评审意见等）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '工作单单日志' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create index IX_TB_PRO_LogForWorkSheet
    on TB_PRO_LogForWorkSheet (objectId, orgId);


create table TB_PRO_OADepartmentExpend
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    deptId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '部门ID',
    typeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '种类(常量编码: OA_ExpenditureType )',
    amount      decimal(18, 2)                                             not null comment '金额',
    expendDate  datetime    default '1753-01-01 00:00:00'                  not null comment '支出日期',
    description varchar(255)                                               null comment '说明',
    isDeleted   bit         default b'0'                                   not null comment '假删',
    isConfirm   bit         default b'0'                                   not null comment '是否确认',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '部门支出' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_OAProjectExpend
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    projectId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目标识',
    typeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '种类(常量编码:OA_ExpenditureType )',
    amount      decimal(18, 2)                                             not null comment '金额',
    expendDate  datetime    default '1753-01-01 00:00:00'                  not null comment '支出日期',
    description varchar(255)                                               null comment '申请说明',
    isDeleted   bit         default b'0'                                   not null comment '假删',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    isConfirm   bit         default b'0'                                   not null comment '是否确认'
) comment '项目支出' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_OATask
(
    id                  varchar(50)                                                 not null comment 'id'
        primary key,
    title               varchar(255)                                                not null comment '标题',
    description         varchar(255)                                                null comment '说明',
    sponsor             varchar(50)                                                 not null comment '发起人账号',
    sponsorId           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '发起人标识',
    sponsorName         varchar(100)                                                null comment '发起人名称',
    submitTime          datetime     default CURRENT_TIMESTAMP                      not null comment '提交时间',
    completeTime        datetime     default '1753-01-01 00:00:00'                  not null comment '完成时间',
    currentAssignee     varchar(50)                                                 not null comment '当前办理人账号',
    currentAssigneeId   varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '当前办理人标识',
    currentAssigneeName varchar(50)                                                 null comment '当前办理人名称',
    currentTaskDefKey   varchar(50)                                                 null comment '当前环节key',
    currentTaskName     varchar(50)                                                 null comment '当前环节名称',
    procTypeCode        varchar(50)                                                 not null comment '工作流类型编码(枚举EnumProcTypeCode：contract: 合同审批、projectExpend: 项目支出、departmentExpend: 部门支出、instrumentPurchase: 仪器采购、instrumentRepair: 仪器维修、instrumentScrap: 仪器报废、consumable：领料、consumablePurchase: 消耗品采购、fileControl:文件受控、fileRevision: 文件修订、fileAbolish: 文件废止)',
    procTypeId          varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '工作流类型标识(常量编码: OA_Processtype)',
    procTypeName        varchar(50)                                                 null comment '工作流类型名称',
    procInstId          varchar(100) default '00000000-0000-0000-0000-000000000000' not null comment '工作流实例Id',
    status              varchar(50)                                                 not null comment '状态名称(字符串，枚举EnumOATaskStatus：审批通过、审批中、审批拒绝)',
    dataStatus          int          default 0                                      not null comment '状态（数值，枚举EnumOATaskStatus：0.审批中 1.审批通过 2.审批拒绝)',
    deptId              varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '部门ID',
    deptName            varchar(100)                                                null comment '部门名称',
    isDeleted           bit          default b'0'                                   not null comment '假删',
    orgId               varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator             varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate          datetime     default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate          datetime     default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '审批任务信息' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_OATaskHandleLog
(
    id            varchar(50)                                                not null comment 'id'
        primary key,
    taskId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '任务标识',
    isAgree       bit         default b'1'                                   not null comment '是否同意',
    comment       varchar(255)                                               null comment '批注',
    completeTime  datetime    default CURRENT_TIMESTAMP                      not null comment '办理时间',
    assigneeId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '办理人id',
    assignee      varchar(50)                                                null comment '办理人账号',
    assigneeName  varchar(100)                                               null comment '办理人名称',
    actTaskId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '工作流任务id',
    actTaskDefKey varchar(50)                                                null comment '工作流任务节点key',
    actTaskName   varchar(50)                                                null comment '工作流任务名称',
    isFirstStep   bit         default b'0'                                   not null comment '是否为发起申请步骤',
    isDeleted     bit         default b'0'                                   not null comment '假删',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    jurorId       varchar(500)                                               null comment '陪审人id'
) comment '审批任务流程日志' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;

create table TB_PRO_OATaskRelation
(
    taskId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '审批任务id',
    objectId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联id',
    id       varchar(50)                                                not null comment '主键'
        primary key
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_OrderForm
(
    id              varchar(50)                                                not null comment '主键' primary key,
    orderCode       varchar(50)                                                not null comment '订单号',
    projectTypeId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '业务类型',
    orderName       varchar(50)                                                null comment '订单名称',
    salesPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '业务员Id',
    salesPersonName varchar(50)                                                null comment '业务员名称',
    orderDate       datetime    default CURRENT_TIMESTAMP                      not null comment '订单日期',
    timeLimit       int         default 30                                     not null comment '报价期限',
    enterpriseId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '客户Id',
    enterpriseName  varchar(50)                                                null comment '客户名称',
    linkPerson      varchar(50)                                                null comment '联系人',
    linkPhone       varchar(50)                                                null comment '联系电话',
    address         varchar(50)                                                null comment '地址',
    orderStatus     int         default 0                                      not null comment '订单状态',
    pushStatus      int         default 0                                      not null comment '推送状态',
    isDeleted       bit         default b'0'                                   not null comment '是否删除',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    flowType        int         default 0                                      not null comment '流程类型',
    firstPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '一审人员',
    secondPersonId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '二审人员',
    threePersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '三审人员',
    registrantId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '登记人员Id',
    registrantName  varchar(50)                                                null comment '登记人员名称',
    customerOrderNo varchar(50)                                                null comment '客户订单编号',
    grantStatus     int         default -1                                     not null comment '签单状态（签订,不签订）',
    signDate        datetime    default CURRENT_TIMESTAMP                      not null comment '签单日期'
) comment '订单信息' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_OrderQuotation
(
    id             varchar(50)                                                   not null comment 'id' primary key,
    orderId        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '订单id',
    testPrice      decimal(18, 2) default 0.00                                   not null comment '检测费小计',
    testDiscount   decimal(18, 2) default 0.00                                   not null comment '检测折扣率',
    discountPrice  decimal(18, 2) default 0.00                                   not null comment '折后检测费',
    otherPrice     decimal(18, 2) default 0.00                                   not null comment '其他费用小计',
    preTax         decimal(18, 2) default 0.00                                   not null comment '税前报价',
    taxRate        decimal(18, 2) default 0.00                                   not null comment '税率',
    totalDiscount  decimal(18, 2) default 0.00                                   not null comment '总价折扣率',
    totalPrice     decimal(18, 2) default 0.00                                   not null comment '总价',
    finalQuotation decimal(18, 2) default 0.00                                   not null comment '最后报价',
    isDeleted      bit            default b'0'                                   not null comment '是否删除',
    orgId          varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '订单总报价' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_OtherDetail
(
    id          varchar(50)                                                not null comment 'id' primary key,
    orderId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '订单id',
    quotationId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '总计id',
    typeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '费用项目id',
    standard    decimal(18, 2)                                             not null comment '收费标准',
    unit        varchar(50)                                                null comment '单位',
    days        decimal(18, 2)                                             not null comment '天数',
    count       decimal(18, 2)                                             not null comment '数量',
    price       decimal(18, 2)                                             not null comment '小计',
    quotedPrice decimal(18, 2)                                             not null comment '报价',
    remark      varchar(1000)                                              null comment '备注',
    isDeleted   bit         default b'0'                                   not null comment '是否删除',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '其他费用明细' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_ParamsData
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    objectId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象Id',
    objectType     int         default 0                                      not null comment '对象类型（枚举EnumParamsDataType1:样品，2:工作单，3:企业，4.采样单）',
    paramsConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '参数配置id',
    paramsName     varchar(100)                                               null comment '参数名称（使用别名）',
    paramsValue    varchar(500)                                               null,
    dimension      varchar(50)                                                null comment '计量单位',
    dimensionId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位Id',
    orderNum       int         default 0                                      not null comment '排序',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    groupId        varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '分组Id'
) comment '参数数据表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create index UIX_TB_PRO_ParamsData
    on TB_PRO_ParamsData (objectId, paramsConfigId, groupId);

create table TB_PRO_PerformanceStatisticForLocalData
(
    id                   varchar(50)                                                 not null comment 'id'
        primary key,
    receiveId            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '送样单id',
    recordCode           varchar(20)                                                 null comment '送样单号',
    testId               varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    recorderId           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '送样登记人',
    sendTime             datetime     default '1753-01-01 00:00:00'                  not null comment '分析日期',
    samplingTime         datetime     default '1753-01-01 00:00:00'                  not null,
    sampleTypeId         varchar(50)  default '00000000-0000-0000-0000-000000000000' null comment '检测类型id',
    redAnalyzeItemName   varchar(100) default '00000000-0000-0000-0000-000000000000' null comment '分析项目名称',
    redAnalyzeMethodName varchar(255) default '00000000-0000-0000-0000-000000000000' null comment '分析方法名称',
    analyseItemId        varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    analyzeMethodId      varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id',
    sample               int          default 0                                      not null comment '样品数',
    localeGap            int          default 0                                      not null comment '全程序空白样品数',
    parallel             int          default 0                                      not null comment '室内平行样品数',
    valid                int          default 0                                      not null comment '有效数据',
    orgId                varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '工作量统计（现场数据）' ENGINE = InnoDB
                                 CHARACTER SET = utf8
                                 COLLATE = utf8_general_ci
                                 ROW_FORMAT = Dynamic;



create table TB_PRO_PerformanceStatisticForReportData
(
    id            varchar(50)                                                 not null comment 'id' primary key,
    projectId     varchar(100) default '00000000-0000-0000-0000-000000000000' null comment '项目id',
    reportTime    datetime     default '1753-01-01 00:00:00'                  not null comment '报告时间',
    reportMakerId varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '编制报告人id',
    report        int          default 0                                      not null comment '报告数',
    orgId         varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_PerformanceStatisticForSampleData
(
    id               varchar(50)                                                 not null comment 'id'
        primary key,
    receiveId        varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '送样单id',
    projectId        varchar(100) default '00000000-0000-0000-0000-000000000000' null comment '项目id',
    recordCode       varchar(20)                                                 null comment '送样单号',
    samplingPersonId varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '采样人员',
    sendTime         datetime     default '1753-01-01 00:00:00'                  not null comment '送样日期',
    samplingTime     datetime     default '1753-01-01 00:00:00'                  not null comment '采样时间',
    senderName       varchar(100)                                                null comment '送样人员',
    sampleTypeId     varchar(50)  default '00000000-0000-0000-0000-000000000000' null comment '检测类型id',
    sample           int          default 0                                      not null comment '样品数',
    orgId            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '采样项目的绩效' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_PRO_PerformanceStatisticForWorkSheetData
(
    id                   varchar(50)                                                not null comment 'id'
        primary key,
    workSheetFolderId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '工作单id',
    workSheetCode        varchar(20)                                                null comment '检测单编号',
    testId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    analystId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析人Id',
    analyzeTime          datetime    default '1753-01-01 00:00:00'                  not null comment '分析日期',
    sampleTypeId         varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '检测类型id',
    redAnalyzeItemName   varchar(100)                                               null comment '分析项目名称',
    redAnalyzeMethodName varchar(255)                                               null comment '分析方法名称',
    analyseItemId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    analyzeMethodId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id',
    sample               int         default 0                                      not null comment '样品数',
    localeGap            int         default 0                                      not null comment '全程序空白样品数',
    interiorGap          int         default 0                                      not null comment '室内空白样品数',
    parallel             int         default 0                                      not null comment '平行样品数',
    addition             int         default 0                                      not null comment '加标样品数',
    curveItem            int         default 0                                      not null comment '曲线条数',
    curveEntries         int         default 0                                      not null comment '曲线个数',
    point                int         default 0                                      not null comment '带点个数',
    qcSample             int         default 0                                      not null comment '带质控数',
    valid                int         default 0                                      not null comment '有效数据',
    orgId                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '工作量统计（工作单数据）' ENGINE = InnoDB
                                   CHARACTER SET = utf8
                                   COLLATE = utf8_general_ci
                                   ROW_FORMAT = Dynamic;

create index IX_TB_PRO_PerformanceStatisticForWorkSheetData
    on TB_PRO_PerformanceStatisticForWorkSheetData (orgId, analyzeTime, analystId);

create table TB_PRO_Project
(
    id                 varchar(50)                                                 not null comment 'id'
        primary key,
    parentId           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '父级项目id',
    projectCode        varchar(50)                                                 null comment '流水编号',
    projectTypeId      varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '项目类型id（外键）',
    projectName        varchar(100)                                                null comment '项目名称',
    status             varchar(50)                                                 not null comment '项目状态(存枚举字符串EnumProjectStatus)',
    samplingStatus     int          default 1                                      not null comment '委托现场送样装填（EnumSampledStatus 1.未采毕 2.已采毕）',
    reportStatus       int          default 0                                      not null comment '报告流程状态（EnumReportStatus： 0.未完成，1.已完成）',
    inputTime          datetime     default CURRENT_TIMESTAMP                      not null comment '项目登记时间(登记时间不能改)',
    inceptPersonId     varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '登记人id',
    inceptTime         datetime     default CURRENT_TIMESTAMP                      not null comment '委托时间',
    lastModifyTime     datetime     default CURRENT_TIMESTAMP                      not null comment '项目最后修改时间',
    expectedCharge     decimal(18, 2)                                              null comment '预计收费',
    actualCharges      decimal(18, 2)                                              null comment '实际收费',
    isStress           bit          default b'0'                                   not null comment '是否着重关注',
    grade              int          default 0                                      not null comment '项目等级(EnumProjectGrade：0.一般 1.紧急 2.特急)',
    customerId         varchar(500) default '00000000-0000-0000-0000-000000000000' not null,
    customerName       varchar(1000)                                               null,
    inspectedEntId     varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '受检单位Id',
    inspectedEnt       varchar(100)                                                null comment '受检单位',
    inspectedLinkMan   varchar(50)                                                 null comment '受检方联系人',
    inspectedLinkPhone varchar(50)                                                 null comment '受检方联系电话',
    inspectedAddress   varchar(100)                                                null comment '受检方地址',
    customerOwner      varchar(50)                                                 null comment '法人代表',
    customerAddress    varchar(100)                                                null comment '地址',
    linkMan            varchar(50)                                                 null comment '联系人',
    linkPhone          varchar(50)                                                 null comment '电话',
    linkEmail          varchar(50)                                                 null comment '电子邮件',
    linkFax            varchar(50)                                                 null comment '传真',
    zipCode            varchar(10)                                                 null comment '邮编',
    isDeleted          bit          default b'0'                                   not null comment '假删字段',
    monitorPurp        varchar(255)                                                null comment '监测目的',
    monitorMethods     varchar(255)                                                null comment '监测方式',
    customerRequired   varchar(1000)                                               null comment '客户委托内容',
    sampleType         varchar(1000)                                               null comment '样品类型',
    postMethod         int          default 0                                      not null comment '发送方式(常量 PRO_PostMethod)',
    remark             varchar(1000)                                               null comment '备注',
    qcInfo             varchar(255)                                                null comment '质控信息',
    controlInfo        varchar(255)                                                null comment '监督信息',
    projectAddress     varchar(100)                                                null comment '项目地址',
    docNumber          varchar(50)                                                 null comment '项目批准机关及文号',
    compDate           varchar(10)                                                 null comment '建设竣工日期',
    invAmount          varchar(20)                                                 null comment '投资总额',
    analyzeMethod      int          default 0                                      not null comment '监测方法(0:无,1.按现行国标行标执行2.客户指定方法)',
    reportMethod       varchar(255)                                                null comment '检验结果告知方式(1.出具正规报告2.电话告知)',
    saveCondition      varchar(255)                                                null comment '保存条件',
    saveDate           varchar(255)                                                null comment '保存期限',
    reportNum          varchar(255)                                                null comment '检测报告数',
    isOnline           int          default 0                                      not null comment '是否网上登记(0.否 1. 是)',
    pushStatus         int          default 0                                      not null comment '推送状态(0：不发布，1：已发布，2：已办结）',
    qrCodeUrl          varchar(500)                                                null comment '二维码图片存储地址',
    sampleQuantity     varchar(10)                                                 null comment '国检_样品数量（出具在报告中）',
    isQualified        bit          default b'0'                                   not null comment '国检_是否合格(样品是否含有不合格项)',
    sampleDescription  varchar(255)                                                null comment '国检_样品描述',
    sampleNameCustomer varchar(100)                                                null comment '国检_委托品名',
    samKind            varchar(50)                                                 null comment '国检_样品贸易类型（出口/进口/内贸）',
    tradeAreaCode      varchar(50)                                                 null comment '国检_贸易区代码',
    batchCode          varchar(20)                                                 null comment '国检_批号',
    testCode           varchar(20)                                                 null comment '国检_客户编号（报验号）',
    prodCompany        varchar(100)                                                null comment '国检_生产厂家',
    compAddress        varchar(100)                                                null comment '国检_产地',
    extendInt1         int          default -1                                     not null comment '预留int类型1',
    extendInt2         int          default -1                                     not null comment '预留int类型2',
    extendInt3         int          default -1                                     not null comment '预留int类型3',
    extendStr1         varchar(255)                                                null comment '预留string类型1',
    extendStr2         varchar(255)                                                null comment '预留string类型2',
    extendStr3         varchar(255)                                                null comment '预留string类型3',
    extendGuid1        varchar(50)                                                 null comment '预留Guid类型1',
    extendGuid2        varchar(50)                                                 null comment '预留Guid类型2',
    extendGuid3        varchar(50)                                                 null comment '预留Guid类型3',
    extendDate1        datetime     default CURRENT_TIMESTAMP                      not null comment '预留Date类型1',
    extendDate2        datetime     default CURRENT_TIMESTAMP                      not null comment '预留Date类型2',
    extendDate3        datetime     default CURRENT_TIMESTAMP                      not null comment '预留Date类型3',
    json               mediumtext                                                  null comment '冗余json信息',
    orgId              varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime     default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime     default CURRENT_TIMESTAMP                      not null comment '修改时间',
    orderId            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '订单id',
    isMultiEnterprise  bit          default b'0'                                   not null comment '是否多企业'
) comment '下达的项目、任务' ENGINE = InnoDB
                            CHARACTER SET = utf8
                            COLLATE = utf8_general_ci
                            ROW_FORMAT = Dynamic;

create index IX_TB_PRO_Project
    on TB_PRO_Project (orgId, inceptTime, projectTypeId, inceptPersonId, status, inputTime);

create table TB_PRO_Project2Contract
(
    id         varchar(50)                                                not null comment '主键'
        primary key,
    projectId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    contractId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同id'
) comment '项目与合同的关联表' ENGINE = InnoDB
                               CHARACTER SET = utf8
                               COLLATE = utf8_general_ci
                               ROW_FORMAT = Dynamic;

create table TB_PRO_Project2Customer
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    projectId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    customerId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '企业id',
    customerName varchar(100)                                               null comment '企业名称'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_Project2FixedProperty
(
    projectId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    fixedPropertyId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '断面属性id',
    id              varchar(50)                                                not null comment '主键'
        primary key
) comment '例行监测、污染源任务下达的断面属性' ENGINE = InnoDB
                                              CHARACTER SET = utf8
                                              COLLATE = utf8_general_ci
                                              ROW_FORMAT = Dynamic;

create table TB_PRO_Project2WorkSheetFolder
(
    id                varchar(50) not null comment '主键'
        primary key,
    projectId         varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '项目id',
    workSheetFolderId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '检测单id'
) comment '项目与检测单的关联表' ENGINE = InnoDB
                                 CHARACTER SET = utf8
                                 COLLATE = utf8_general_ci
                                 ROW_FORMAT = Dynamic;

create table TB_PRO_ProjectPlan
(
    id                  varchar(50)                                                not null comment 'id'
        primary key,
    projectId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    leaderId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目负责人id',
    reportMakerId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制报告人id',
    schemeMakerId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制方案人id',
    spotPersonId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '现场负责人id',
    supervisorId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '监督人id',
    deadLine            datetime    default '1753-01-01 00:00:00'                  not null comment '要求完成时间',
    reportDate          datetime    default '1753-01-01 00:00:00'                  not null comment '预计出具报告时间',
    requireAnalyzeDate  datetime    default '1753-01-01 00:00:00'                  not null comment '预计完成分析日期',
    requireSamplingDate datetime    default '1753-01-01 00:00:00'                  not null comment '预计完成采样日期',
    responsePerson      varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '责任人（质量控制计划）',
    requires            varchar(1000)                                              null comment '测试项目或具体要求的文字说明',
    testMethodRequires  varchar(1000)                                              null comment '测试标准文字说明',
    remark              varchar(1000)                                              null comment '备注',
    isUseMethod         bit         default b'0'                                   not null comment '是否同意使用非标准方法',
    isEvaluate          bit         default b'0'                                   not null comment '是否评价',
    isWarning           bit         default b'0'                                   not null comment '是否警告',
    warningDay          int         default 0                                      not null comment '提前警告天数',
    isFeedback          bit         default b'0'                                   not null comment '是否反馈',
    isContract          bit         default b'0'                                   not null comment '是否拟订合同',
    subName             varchar(255)                                               null comment '分包单位',
    subItems            varchar(1000)                                              null comment '分包项目',
    subMethod           int         default 0                                      not null comment '分包方式(枚举EnumSubMethod:0.无1.客户指定2.本站联系)',
    isOutsourcing       int         default 0                                      not null comment '分包情况(枚举EnumOutSourcing: 0.不分包1.全部分包2.部分分包)',
    isMakePlan          bit         default b'1'                                   not null comment '是否编制方案',
    reportMakerIIId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '辅助编制报告人id',
    qcGrade             int         default -1                                     not null comment '质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）',
    qcType              int         default -1                                     not null comment '质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核)
   ',
    qcSource            varchar(255)                                               null comment '质量来源（质量控制——外部质控）',
    judgment            varchar(255)                                               null comment '判断依据（质量控制计划）',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    constraint UIX_TB_PRO_ProjectPlan
        unique (orgId, projectId)
) comment '任务计划' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_ProjectPlan
    on TB_PRO_ProjectPlan (orgId, projectId, leaderId, reportMakerId);

create table TB_PRO_ProjectPushLog
(
    id           varchar(50)                                                not null comment '主键'
        primary key,
    title        varchar(255)                                               null comment '标题',
    message      mediumtext                                                 null comment '信息',
    url          varchar(255)                                               null comment '地址',
    personId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '人员id',
    pushTime     datetime    default CURRENT_TIMESTAMP                      not null comment '推送时间',
    pushStatus   bit         default b'0'                                   not null comment '推送状态',
    pushResponse varchar(1000)                                              null comment '推送返回信息',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_QCData
(
    id          varchar(50)                                                not null comment '主键'
        primary key,
    userId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '用户id',
    testId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试id',
    paramsName  varchar(255)                                               null,
    lastTime    datetime    default CURRENT_TIMESTAMP                      not null comment '最新配置时间',
    stdValue    varchar(100)                                               null comment '方差值',
    avgValue    double      default 0                                      not null comment '平均值',
    upAssist    double      default 0                                      not null comment '上辅助线',
    downAssist  double      default 0                                      not null comment '下辅助线',
    upWarning   double      default 0                                      not null comment '上警告线',
    downWarning double      default 0                                      not null comment '下警告线',
    upControl   double      default 0                                      not null comment '上控制线',
    downControl double      default 0                                      not null comment '下控制线',
    dataType    int         default 0                                      not null comment '数据类型(枚举EnumDataType：1.空白 2.加标)',
    dataStr     mediumtext                                                 null comment '数据字符串',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_QCResultEvaluation
(
    id               varchar(50)                                                not null comment '主键id'
        primary key,
    projectId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目Id',
    qcSituation      varchar(1000)                                              null comment '质量控制情况',
    resultCompare    varchar(1000)                                              null comment '结果比较',
    evaluationResult varchar(1000)                                              null comment '评价结果',
    createPerson     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价人员',
    createTime       datetime    default CURRENT_TIMESTAMP                      not null comment '评价时间',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_QualityControl
(
    id                             varchar(50)                                                not null comment '主键'
        primary key,
    associateSampleId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联样品Id（平行样的关联样品）',
    qcGrade                        int         default -1                                     not null comment '质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）',
    qcType                         int         default -1                                     not null comment '质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）',
    qcValue                        varchar(50)                                                null comment '质控值（标准样的值/加标的值/平行样为空）',
    qcVolume                       varchar(50)                                                null comment '加标体积',
    qaId                           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '添加质控人员id',
    qcTime                         datetime    default CURRENT_TIMESTAMP                      not null comment '添加质控时间',
    qcTestValue                    varchar(50)                                                null comment '测定值',
    realSampleTestValue            varchar(50)                                                null comment '样值（可以是质量的也可以是浓度的）',
    qcCode                         varchar(50)                                                null comment '标样编号',
    qcOriginValue                  varchar(50)                                                null comment '原样的检测结果（找限值范围）',
    qcValidDate                    datetime    default '1753-01-01 00:00:00'                  not null comment '标样的有效期',
    orgId                          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    qcStandardDate                 datetime                                                   null comment '标样的配置日期',
    qcStandardId                   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '标样Id',
    qcConcentration                varchar(50)                                                null comment '加标液浓度',
    qcVolumeDimensionId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '加标体积/标准溶液加入量量纲id',
    qcValueDimensionId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '加入标准量/标准物质加入量/替代物加入量量纲id',
    qcTestValueDimensionId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测定值量纲id',
    realSampleTestValueDimensionId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样值量纲id',
    qcConcentrationDimensionId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '加标液浓度量纲'
) comment '质控信息' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_QualityControl
    on TB_PRO_QualityControl (orgId, associateSampleId, qcGrade, qcType);

create table TB_PRO_QualityManage
(
    id                 varchar(50)                                                not null comment 'id'
        primary key,
    anaId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联样品数据id',
    qmType             int         default -1                                     not null comment '质控类型（枚举EnumQMType：1.标样，2.加标样，3.其他）',
    qmValue            varchar(255)                                               null comment '质控值（标准样的值/加标的值/其他为空）',
    qmRange            varchar(255)                                               null comment '范围',
    qmVolume           varchar(50)                                                null comment '加标体积',
    qmPersonId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '添加质控人员id',
    qmTime             datetime    default CURRENT_TIMESTAMP                      not null comment '添加质控时间',
    qmTestValue        varchar(50)                                                null comment '测定值',
    stTestValue        varchar(50)                                                null comment '样值（可以是质量的也可以是浓度的）',
    qmCode             varchar(255)                                               null comment '标准编号',
    qmOriginValue      varchar(50)                                                null comment '原样的检测结果（找限值范围）',
    instrumentId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器id',
    isMixedStandard    bit         default b'0'                                   not null comment '是否混标',
    redAnalyzeItemName varchar(100)                                               null comment '分析项目名称',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '质量管理' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_QuotationDetail
(
    id                 varchar(50)                                                   not null comment 'id' primary key,
    orderId            varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '订单id',
    quotationId        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '总计id',
    sampleTypeId       varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '样品类型id',
    redAnalyseItemName varchar(255)                                                  null comment '分析项目名称',
    analyseItemId      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    redAnalyseMethod   varchar(255)                                                  null comment '分析方法名称',
    analyseMethodId    varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id',
    testId             varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    folderName         varchar(2000)                                                 null comment '点位名称',
    projectCount       int            default 1                                      not null comment '任务数',
    cycleOrder         int            default 1                                      not null comment '周期数',
    timesOrder         int            default 1                                      not null comment '批次数',
    sampleOrder        int            default 1                                      not null comment '总检数',
    inspectedCount     int            default 0                                      not null comment '已检数',
    residueCount       int            default 1                                      not null comment '剩检数',
    sampleCount        int            default 1                                      not null comment '样品数量',
    samplingPrice      decimal(18, 2) default 0.00                                   not null comment '采样费',
    analysePrice       decimal(18, 2) default 0.00                                   not null comment '分析费',
    charge             decimal(18, 2) default 0.00                                   not null comment '小计',
    quotationPrice     decimal(18, 2) default 0.00                                   not null comment '总价',
    isDeleted          bit                                                           not null comment '是否删除',
    orgId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间',
    isTotal            bit            default b'0'                                   not null comment '是否总称'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_QuotationDetail2Test
(
    id       varchar(50) not null comment '主键' primary key,
    detailId varchar(50) not null default '00000000-0000-0000-0000-000000000000',
    testId   varchar(50) not null default '00000000-0000-0000-0000-000000000000'
) comment '费用详情与测试项目关系' ENGINE = InnoDB
                                   CHARACTER SET = utf8
                                   COLLATE = utf8_general_ci
                                   ROW_FORMAT = Dynamic;

create table TB_PRO_ReceiveSampleRecord
(
    id                varchar(50)                                                not null comment '主键'
        primary key,
    projectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目ID',
    recordCode        varchar(20)                                                null comment '送样单号',
    samplingTime      datetime    default CURRENT_TIMESTAMP                      not null comment '采样时间',
    sendTime          datetime    default CURRENT_TIMESTAMP                      not null comment '送样时间',
    senderId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '送样人（内部人员）ID',
    senderName        varchar(100)                                               null comment '送样人（采样负责人）',
    receiveTime       datetime    default CURRENT_TIMESTAMP                      not null comment '登记时间',
    recorderId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '登记人id',
    receiveType       int         default 0                                      not null comment '接样类型（枚举EnumReceiveType：1.内部 2.外部 3.现场）',
    status            varchar(50)                                                null comment '送样单状态（字符串，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）',
    receiveStatus     int         default 1                                      not null comment '送样单状态（ 用于判断，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）',
    infoStatus        int         default 1                                      not null comment '信息状态（枚举EnumReceiveInfoStatus：1.信息登记中，2.信息复核中 3.信息审核中 4.已确认）',
    reportCode        varchar(20)                                                null comment '分析数据报告编号',
    uploadStatus      int         default 0                                      not null comment '移动端状态(0：未提交，1：数据录入中，2：已数据同步)',
    uploadTime        datetime    default '1753-01-01 00:00:00'                  not null comment '上传时间（移动端）',
    validAnalyzeId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '有证采样人员（用于图片签名）',
    checkerId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '复核人Id',
    checkTime         datetime    default '1753-01-01 00:00:00'                  not null comment '复核时间',
    validCheckerId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '有证复核人员id',
    auditorId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '审核人Id',
    auditTime         datetime    default '1753-01-01 00:00:00'                  not null comment '审核时间',
    validAuditerId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '有证采样审核人员',
    backOpinion       varchar(1000)                                              null comment '退回意见（最新一个）',
    remark            varchar(255)                                               null comment '备注',
    json              mediumtext                                                 null comment '冗余json信息',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    sortId            varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '排序id',
    receiveSampleDate datetime                                                   null comment '接样日期'
) comment '送样单' ENGINE = InnoDB
                   CHARACTER SET = utf8
                   COLLATE = utf8_general_ci
                   ROW_FORMAT = Dynamic;

create index IX_TB_PRO_ReceiveSampleRecord
    on TB_PRO_ReceiveSampleRecord (orgId, sendTime, projectId);

create table TB_PRO_ReceiveSubSampleRecord
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    receiveId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '送样单ID',
    projectId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '登记项目ID',
    code            varchar(20)                                                null comment '领样单编号',
    status          varchar(50) default '0'                                    null comment '领样单状态（枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认））',
    subStatus       int         default 0                                      not null comment '领样单状态（按位与，枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认）',
    receivePersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '领样人id',
    receiveName     varchar(50)                                                null comment '领取人',
    receiveTime     datetime    default '1753-01-01 00:00:00'                  not null comment '领取时间',
    checkerId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '现场数据复核人Id',
    checkerName     varchar(50)                                                null comment '现场数据复核人',
    checkTime       datetime    default '1753-01-01 00:00:00'                  not null comment '现场数据复核时间',
    auditorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '现场数据审核人Id',
    auditorName     varchar(50)                                                null comment '现场数据审核人名称',
    auditTime       datetime    default '1753-01-01 00:00:00'                  not null comment '现场数据审核时间',
    backOpinion     varchar(1000)                                              null comment '退回意见（最新一个）',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属科室Id',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '领样单（一张送样单按科室分成1张或多张领样单）' ENGINE = InnoDB
                                                        CHARACTER SET = utf8
                                                        COLLATE = utf8_general_ci
                                                        ROW_FORMAT = Dynamic;

create index IX_TB_PRO_ReceiveSubSampleRecord
    on TB_PRO_ReceiveSubSampleRecord (receiveId, orgId);

create table TB_PRO_ReceiveSubSampleRecord2Sample
(
    receiveSubSampleRecordId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '领样单id',
    sampleId                 varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '样品id',
    id                       varchar(50) not null comment '主键'
        primary key
) comment '领样单和样品关联表' ENGINE = InnoDB
                               CHARACTER SET = utf8
                               COLLATE = utf8_general_ci
                               ROW_FORMAT = Dynamic;

create index IX_TB_PRO_ReceiveSubSampleRecord2Sample
    on TB_PRO_ReceiveSubSampleRecord2Sample (receiveSubSampleRecordId, sampleId);

create table TB_PRO_Report
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    reportTypeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '报告类型id',
    projectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    status            varchar(50)                                                not null comment '报告状态',
    dataChangeStatus  int         default 1                                      not null comment '数据变更状态( 枚举EnumReportChangeStatus1.未变更 2.已变更)（针对已经编制报告的数据修改状态-样品数据增删改）',
    code              varchar(100)                                               null comment '报告编号',
    folderName        varchar(100)                                               null comment '点位名称（单位名称）',
    sampleName        varchar(50)                                                null comment '样品名称',
    testName          varchar(50)                                                null comment '检验类别',
    createPersonId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '添加人员',
    createTime        datetime    default CURRENT_TIMESTAMP                      not null comment '添加时间',
    reportYear        varchar(10)                                                null comment '报告年份',
    securityCode      varchar(100)                                               null comment '防伪码',
    certifiedPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '报告持证人员id',
    reportNum         int         default 0                                      not null comment '报告份数',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    grantStatus       int         default 1                                      not null comment '发放状态（未发放,已发放,已回收）',
    firstInstanceId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '一审人id',
    secondInstanceId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '二审人id'
) comment '项目报告' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_ReportDetail
(
    id         varchar(50)                                                not null comment 'id'
        primary key,
    reportId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '报告ID',
    objectId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联ID',
    objectType int         default 1                                      not null comment '关联类型(枚举EnumReportDetailType 1.样品 2.断面属性)',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '项目报告详情关联的样品或者断面属性' ENGINE = InnoDB
                                               CHARACTER SET = utf8
                                               COLLATE = utf8_general_ci
                                               ROW_FORMAT = Dynamic;

create index IX_TB_PRO_ReportDetail
    on TB_PRO_ReportDetail (objectId, reportId, orgId);

create table TB_PRO_ReportRecover
(
    id            varchar(50)                                                not null comment '主键id'
        primary key,
    projectId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    recoverPerson varchar(50)                                                null comment '回收人',
    recoverTime   datetime    default CURRENT_TIMESTAMP                      not null comment '回收日期',
    recoverReason varchar(100)                                               null comment '回收理由',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_ReportRecover2Report
(
    id        varchar(50) not null comment '主键id'
        primary key,
    recoverId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '回收id',
    reportId  varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '报告id'
) comment '回收报告关联' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_Sample
(
    id                     varchar(50)                                                not null comment 'id'
        primary key,
    code                   varchar(50)                                                null comment '样品编号',
    subProjectId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '子项目的id',
    projectId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    receiveId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '送样记录id',
    sampleFolderId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位id',
    samplingFrequencyId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '频次id',
    cycleOrder             int         default 0                                      not null comment '采样周期序数',
    timesOrder             int         default 0                                      not null comment '每周期次数序数',
    sampleOrder            int         default 0                                      not null comment '每次样品序数',
    redFolderName          varchar(100)                                               null comment '冗余-点位',
    redAnalyzeItems        varchar(1000)                                              null comment '冗余-分析项目',
    samplingPersonId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '采样人id',
    sampleTypeId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测类型id',
    sampleCategory         int         default 0                                      not null comment '样品类别（EnumSampleCategory：0.原样 1.质控样 2.串联样 3.原样加原样 4.比对样）',
    inspectedEnt           varchar(100)                                               null comment '受检单位',
    inspectedEntId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '受检单位ID',
    inceptTime             datetime    default CURRENT_TIMESTAMP                      not null comment '样品登记时间',
    samplingTimeBegin      datetime    default '1753-01-01 00:00:00'                  not null comment '采样开始时间',
    samplingTimeEnd        datetime    default '1753-01-01 00:00:00'                  not null comment '采样结束时间',
    status                 varchar(50)                                                not null comment '样品状态（过程状态，枚举字符串EnumSampleStatus）',
    samplingConfig         int         default 0                                      not null comment '采样分配状态（枚举EnumSamplingConfig：0.未分配 1.已分配）',
    samplingStatus         int         default 0                                      not null comment '采样状态（枚举EnumSamplingStatus：1.不需要取样 2.需要取样还未取样 4.采样中 8.已经完成取样）',
    innerReceiveStatus     int         default 0                                      not null comment '领样状态（枚举EnumInnerReceiveStatus：1.不能领取 2.可以领取 6.已经领取 12.已确认领取）',
    ananlyzeStatus         int         default 0                                      not null comment '分析状态（枚举EnumAnalyzeStatus：1.不需要分析 2.不能分析 4.可以分析 8.正在分析 16.分析完成）',
    storeStatus            int         default 0                                      not null comment '存储状态（枚举EnumStoreStatus：1.不能存储 2.可以存储 4.已经存储 8.可以销毁 16.已经销毁 32.已经被提取）',
    makeStatus             int         default 0                                      not null comment '制样状态（枚举EnumMakeStatus：1.不需要制样 2.需要制样还未制样 6.已经完成制样）',
    dataChangeStatus       int         default 0                                      not null comment '数据变更状态（ 枚举EnumSampleChangeStatus：0.未变更 1.已变更）（针对已经编制报告的数据修改状态-样品数据增删改）',
    customerCode           varchar(50)                                                null comment '客户样品编号',
    qcId                   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '质控id',
    associateSampleId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '质控样的原样Id',
    isDeleted              bit         default b'0'                                   not null comment '假删字段',
    parentSampleId         varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '父级样品（从父级样品制样而来）',
    makeSamPerId           varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '制样人id',
    isPrint                int         default 0                                      not null comment '是否已打印（枚举EnumPrintStatus：0.未打印1.已打印）',
    isKeep                 bit         default b'0'                                   not null comment '是否留样',
    keepLongTime           int         default 0                                      not null comment '样品保留天数',
    storageConditions      varchar(255)                                               null comment '保存条件',
    loseEfficacyTime       datetime    default '1753-01-01 00:00:00'                  not null comment '样品过期时间',
    pack                   varchar(50)                                                null comment '包装/规格',
    sampleWeight           varchar(50)                                                null comment '样品重量',
    weightOrQuantity       varchar(50)                                                null comment '样品数量',
    samColor               varchar(50)                                                null comment '颜色',
    sampleExplain          varchar(1000)                                              null comment '样品特征',
    volume                 varchar(50)                                                null comment '样品体积(string)',
    samplingPlace          varchar(100)                                               null comment '采样点位置',
    remark                 varchar(1000)                                              null comment '备注',
    samKind                int         default 0                                      not null comment '样品的性质（枚举EnumSampleKind：0.分析样 1.备样 2.分析后备样）',
    isQualified            bit         default b'1'                                   not null comment '国检_是否合格(检测项目是否有不合格项)',
    sampleSource           varchar(100)                                               null comment '国检_样品来源',
    originalStatus         varchar(100)                                               null comment '国检_到达实验室状态',
    isReturned             bit         default b'0'                                   not null comment '国检_是否需要退还',
    preTreatmentCases      varchar(1000)                                              null comment '前处理情况',
    unqualifiedReason      varchar(1000)                                              null comment '不合格原因',
    disposeMeasure         varchar(1000)                                              null comment '处理措施',
    consistencyValidStatus int         default 0                                      not null comment '验证状态',
    lon                    varchar(20)                                                null comment '经度（实际）',
    lat                    varchar(20)                                                null comment '纬度（实际）',
    signerId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '签到人id',
    signTime               datetime    default '1753-01-01 00:00:00'                  not null comment '签到时间',
    samplingRecordId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '采样单Id（预留）',
    isOutsourcing          int         default 0                                      not null comment '预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）',
    blindType              int         default 0                                      not null,
    lastNewSubmitTime      datetime    default '1753-01-01 00:00:00'                  not null comment '最新一次检测单数据提交时间',
    orgId                  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate             datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate             datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '样品' ENGINE = InnoDB
                 CHARACTER SET = utf8
                 COLLATE = utf8_general_ci
                 ROW_FORMAT = Dynamic;

create index IX_TB_PRO_Sample
    on TB_PRO_Sample (orgId, samplingTimeBegin, receiveId, isDeleted, code);

create index IX_TB_PRO_Sample2
    on TB_PRO_Sample (orgId, isDeleted, projectId, sampleTypeId, code, redFolderName, inspectedEnt);

create index IX_TB_PRO_Sample3
    on TB_PRO_Sample (orgId, isDeleted, receiveId, sampleTypeId, sampleFolderId);

create index IX_TB_PRO_Sample4
    on TB_PRO_Sample (receiveId, orgId, isDeleted);

create index IX_TB_PRO_Sample5
    on TB_PRO_Sample (associateSampleId, orgId, isDeleted);

create index IX_TB_PRO_Sample6
    on TB_PRO_Sample (projectId, orgId, isDeleted);

create index IX_TB_PRO_Sample7
    on TB_PRO_Sample (samplingFrequencyId, orgId, isDeleted);

create index IX_TB_PRO_Sample8
    on TB_PRO_Sample (isDeleted, code, inspectedEnt);

create table TB_PRO_SampleDispose
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    sampleId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品编号',
    sampleSource    varchar(100)                                               null comment '样品来源',
    sampleCount     int         default 1                                      not null comment '样品数量',
    reserveDate     datetime    default '1753-01-01 00:00:00'                  not null comment '保留日期',
    reserveLocation varchar(100)                                               null comment '保存位置',
    redAnalyzeItems varchar(1000)                                              null comment '分析项目名称，多个英文逗号间隔',
    disposePersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '处置人员id',
    disposeDate     datetime    default '1753-01-01 00:00:00'                  not null comment '处置日期',
    disposeSolution varchar(50)                                                not null comment '处置方式',
    disposeRemarks  varchar(1000)                                              null comment '处置备注',
    isDeleted       bit         default b'0'                                   not null comment '是否删除',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室id',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '最近修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '最新修改时间',
    isDisposed      bit         default b'0'                                   not null comment '是否处置',
    saveCondition   varchar(100)                                               null comment '保存条件'
) comment '留样信息' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_SampleDispose2Test
(
    id                 varchar(50)                                                not null comment '主键'
        primary key,
    sampleDisposeId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '留样处置id',
    testId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    redAnalyzeItemName varchar(100)                                               null comment '分析项目名称'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_SampleFolder
(
    id                        varchar(50)                                                   not null comment '主键'
        primary key,
    projectId                 varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    subProjectId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '子项目的id',
    watchSpot                 varchar(100)                                                  null comment '点位名称',
    fixedPointId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '断面id（断面扩展id）',
    sampleTypeId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '检测类型id',
    folderCode                varchar(50)                                                   null comment '点位号',
    chargeRate                decimal(18, 2) default 1.00                                   not null comment '费用系数（默认1）',
    folderTypeId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '点位类型（常量 PRO_FolderType：进口、出口）',
    folderTypeName            varchar(50)                                                   null comment '点位类型名称',
    redAnalyzeItems           varchar(1000)                                                 null comment '分析项目',
    lon                       varchar(20)                                                   null comment '经度（计划）',
    lat                       varchar(20)                                                   null comment '纬度（计划）',
    grade                     int            default 1                                      not null comment '地图级别（默认1）',
    exhaustPipeHeight         varchar(20)                                                   null comment '排气管高度',
    isOutsourcing             int            default 0                                      not null comment ' 预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）',
    craftFacilityName         varchar(100)                                                  null comment '工艺设备名称',
    purificateFacilityName    varchar(100)                                                  null comment '净化设备名称',
    pollutionType             varchar(100)                                                  null comment '污染源种类',
    boilerMakeUnit            varchar(100)                                                  null comment '锅炉制造单位',
    equipmentTypeName         varchar(100)                                                  null comment '名称(型号)',
    boilerUseDate             datetime       default '1753-01-01 00:00:00'                  not null comment '锅炉投运日期',
    chimneyHeight             varchar(20)                                                   null comment '烟囱高度',
    purificateFacilityUnit    varchar(100)                                                  null comment '净化设备制造单位',
    purificateFacilityType    varchar(100)                                                  null comment '净化设备型号',
    purificateFacilityUseDate datetime       default '1753-01-01 00:00:00'                  not null comment '净化设备投运日期',
    fuelType                  varchar(50)                                                   null comment '燃料类型',
    stoveFacilityCode         varchar(50)                                                   null comment '炉窖设备编号',
    craftFacilityUseDate      datetime       default '1753-01-01 00:00:00'                  not null comment '工艺设备/启用时间',
    isTransition              bit            default b'0'                                   not null comment '是否进行折算',
    inspectedEntId            varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '受检单位ID',
    inspectedEnt              varchar(100)                                                  null comment '受检单位',
    orgId                     varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '点位信息表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create index IX_TB_PRO_SampleFolder
    on TB_PRO_SampleFolder (orgId, projectId, sampleTypeId);

create table TB_PRO_SampleGroup
(
    id                  varchar(50)                                                not null comment '主键id' primary key,
    receiveId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '送样单id',
    sampleId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品id',
    sampleTypeGroupId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品分组id',
    sampleTypeGroupName varchar(50)                                                null comment '样品分组名称',
    analyseItemNames    varchar(2000)                                              null comment '分析项目名称',
    hasScanned          bit         default b'0'                                   not null comment '是否已扫码',
    scannedTime         datetime    default '1753-01-01 00:00:00'                  not null comment '扫码时间',
    scanner             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '扫码人id',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate          datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate          datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    fixer               varchar(1000)                                              null comment '固定剂',
    containerName       varchar(255)                                               null comment '容器名称',
    remark              varchar(1000)                                              null comment '备注'
) comment '样品分组表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_SampleReserve
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    sampleId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品编号',
    reserveDate     datetime    default CURRENT_TIMESTAMP                      not null comment '操作日期',
    reservePersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作人员id',
    isDeleted       bit         default b'0'                                   not null comment '是否删除',
    reserveType     int         default 0                                      not null comment '类型',
    disposeMethod   varchar(100)                                               null comment '处置方式（1.领取，2.处置）',
    remark          varchar(100)                                               null comment '备注',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室id',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '最近修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '最新修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_SampleReserve2Test
(
    id                 varchar(50)                                                not null comment '主键'
        primary key,
    reserveId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '领取数据标识',
    analyzeItemId      varchar(50) default '00000000-0000-0000-0000-000000000000' null,
    redAnalyseItemName varchar(50)                                                null comment '分析项目名称'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_SamplingCarConfig
(
    id         varchar(50)                                                not null comment '主键id'
        primary key,
    objectId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType int         default -1                                     not null comment '对象类型(枚举EnumSamplingCarType：0.任务1.送样单)',
    carId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '车辆的id',
    driverId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '驾驶员id',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '采样的车辆分配' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_PRO_SamplingFrequency
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    sampleFolderId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位id',
    periodCount    int         default 0                                      not null comment '周期',
    timePerPeriod  int         default 0                                      not null comment '次数',
    folderType     int         default 0                                      not null comment '点位类型（枚举EnumFolderType：0.无类型 1.昼间 2.夜间）',
    samplePerTime  int         default 1                                      not null comment '每次样品数',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '点位频次' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create index IX_TB_PRO_SamplingFrequency
    on TB_PRO_SamplingFrequency (orgId, sampleFolderId);

create table TB_PRO_SamplingFrequencyTest
(
    id                   varchar(50)                                                not null comment 'id'
        primary key,
    samplingFrequencyId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位频次id',
    sampleFolderId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位id',
    testId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试id',
    analyseItemId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    analyzeMethodId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id',
    redAnalyzeItemName   varchar(100)                                               null comment '分析项目名称',
    redAnalyzeMethodName varchar(100)                                               null comment '分析方法名称',
    redCountryStandard   varchar(255)                                               null,
    isCompleteField      bit         default b'0'                                   not null,
    isOutsourcing        bit         default b'0'                                   not null,
    orgId                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '点位频次所做的测试' ENGINE = InnoDB
                               CHARACTER SET = utf8
                               COLLATE = utf8_general_ci
                               ROW_FORMAT = Dynamic;

create index IX_TB_PRO_SamplingFrequencyTest
    on TB_PRO_SamplingFrequencyTest (orgId, sampleFolderId, samplingFrequencyId);

create index IX_TB_PRO_SamplingFrequencyTest2
    on TB_PRO_SamplingFrequencyTest (orgId, samplingFrequencyId);

create index IX_TB_PRO_SamplingFrequencyTest3
    on TB_PRO_SamplingFrequencyTest (orgId, sampleFolderId);

create table TB_PRO_SamplingPersonConfig
(
    id               varchar(50)                                                not null comment '主键'
        primary key,
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象Id',
    objectType       int         default 0                                      not null comment '对象类型(枚举EnumSamplingType：0.任务1.送样单)',
    samplingPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '采样人Id',
    samplingPerson   varchar(50)                                                null comment '采样人',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_StatusForCostInfo
(
    id                varchar(50)                                                not null comment '主键id'
        primary key,
    costInfoId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '费用id',
    module            varchar(50)                                                not null comment '模块编码（枚举EnumCostInfoModule）',
    status            int         default 1                                      not null comment '状态（枚举 EnumStatus 1待处理 2已处理）',
    currentPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '当前操作人Id',
    currentPersonName varchar(50)                                                null comment '当前操作人名称',
    nextPersonId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '下一步操作人Id',
    nextPersonName    varchar(50)                                                null comment '下一步操作人名称',
    lastNewOpinion    mediumtext                                                 null comment '最新一条意见',
    extendStr1        varchar(255)                                               null comment '预留string类型1',
    extendStr2        varchar(255)                                               null comment '预留string类型1',
    extendStr3        varchar(255)                                               null comment '预留string类型1',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '费用状态表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_StatusForProject
(
    id                varchar(50)                                                not null comment '主键id'
        primary key,
    projectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    module            varchar(50)                                                not null comment '模块编码（枚举EnumProjectModule）',
    status            int         default 1                                      not null comment '状态（枚举 EnumStatus 1待处理 2已处理）',
    currentPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '当前操作人Id',
    currentPersonName varchar(50)                                                null comment '当前操作人名称',
    nextPersonId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '下一步操作人Id',
    nextPersonName    varchar(50)                                                null comment '下一步操作人名称',
    lastNewOpinion    mediumtext                                                 null comment '最新一条意见',
    extendStr1        varchar(255)                                               null comment '预留string类型1',
    extendStr2        varchar(255)                                               null comment '预留string类型1',
    extendStr3        varchar(255)                                               null comment '预留string类型1',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '项目状态表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create index IX_TB_PRO_StatusForProject
    on TB_PRO_StatusForProject (orgId, projectId, module);

create table TB_PRO_StatusForRecord
(
    id                varchar(50)                                                not null comment '主键id'
        primary key,
    receiveId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '送样单id',
    module            varchar(50)                                                not null comment '模块编码（枚举EnumRecordModule）',
    status            int         default 1                                      not null comment '状态（枚举 EnumStatus 1待处理 2已处理）',
    currentPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '当前操作人Id',
    currentPersonName varchar(50)                                                null comment '当前操作人名称',
    nextPersonId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '下一步操作人Id',
    nextPersonName    varchar(50)                                                null comment '下一步操作人名称',
    lastNewOpinion    mediumtext                                                 null comment '最新一条意见',
    extendStr1        varchar(255)                                               null comment '预留string类型1',
    extendStr2        varchar(255)                                               null comment '预留string类型1',
    extendStr3        varchar(255)                                               null comment '预留string类型1',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '送样单状态表' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create index IX_TB_PRO_StatusForRecord
    on TB_PRO_StatusForRecord (orgId, receiveId, module);

create table TB_PRO_StatusForReport
(
    id                varchar(50)                                                not null comment '主键id'
        primary key,
    reportId          varchar(50)                                                not null comment '报告id',
    module            varchar(50)                                                not null comment '模块编码（枚举EnumReportModule）',
    status            int         default 1                                      not null comment '状态（枚举 EnumStatus 1待处理 2已处理）',
    currentPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '当前操作人Id',
    currentPersonName varchar(50)                                                null comment '当前操作人名称',
    nextPersonId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '下一步操作人Id',
    nextPersonName    varchar(50)                                                null comment '下一步操作人名称',
    lastNewOpinion    mediumtext                                                 null comment '最新一条意见',
    extendStr1        varchar(255)                                               null comment '预留string类型1',
    extendStr2        varchar(255)                                               null comment '预留string类型1',
    extendStr3        varchar(255)                                               null comment '预留string类型1',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '报告状态表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_SubmitRecord
(
    id               varchar(50)                                                not null comment '主键'
        primary key,
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联Id',
    objectType       int         default 0                                      not null comment '关联对象类型（枚举EnumSubmitObjectType：1.项目 2.工作单 3.送样单 4.现场领样单 5.分析领样单）',
    submitType       int         default 0                                      not null comment '操作类型（枚举EnumSubmitType：0.无 1.项目登记 2.任务下达 ....）',
    submitTime       datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    submitPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作人',
    submitPersonName varchar(50)                                                null comment '操作人名称',
    nextPerson       varchar(200)                                               null comment '下一步操作人（名字）',
    submitRemark     varchar(200)                                               null comment '操作意见',
    stateFrom        varchar(50)                                                null comment '操作前状态',
    stateTo          varchar(50)                                                null comment '操作后状态',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_PRO_Survey
(
    id           varchar(50)                                                not null comment '主键'
        primary key,
    projectId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目ID',
    surveyTime   datetime    default '1753-01-01 00:00:00'                  not null comment '踏勘时间',
    surveyUserId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '踏勘人id',
    surveyNote   varchar(1000)                                              null comment '现场踏勘情况（详细文本信息）',
    problem      varchar(1000)                                              null comment '企业存在问题(原常量数值+是否合格 数值存放)',
    entDelegate  varchar(50)                                                null comment '企业签字人',
    confirmTime  datetime    default '1753-01-01 00:00:00'                  not null comment '签字确认时间',
    remark       varchar(1000)                                              null comment '备注',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '现场踏勘' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_WorkSheet
(
    id                 varchar(50)                                                not null comment 'id'
        primary key,
    parentId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '主表Id',
    testId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试Id',
    recordId           varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '原始记录单id',
    analyseItemId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    redAnalyzeItemName varchar(100)                                               null comment '分析项目名称',
    remark             varchar(1000)                                              null comment '备注',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '工作单子表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_PRO_WorkSheetCalibrationCurve
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    worksheetId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '工作单id',
    standardCurveId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '标准曲线id',
    checkDate       datetime    default '1753-01-01 00:00:00'                  not null comment '校准日期',
    remark          varchar(1000)                                              null comment '备注',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '校准曲线' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_PRO_WorkSheetCalibrationCurveDetail
(
    id                          varchar(50)                                                not null comment '主键'
        primary key,
    workSheetCalibrationCurveId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '工作单校准曲线id',
    analyseCode                 varchar(50)                                                null comment '分析编号',
    addVolume                   varchar(50)                                                null comment '标准溶液加入体积',
    addAmount                   varchar(50)                                                null comment '标准物加入量',
    absorbance                  varchar(50)                                                null comment '吸光度A',
    lessBlankAbsorbance         varchar(50)                                                null comment '减空白吸光度',
    absorbanceB                 varchar(50)                                                null comment '吸光度B',
    relativeDeviation           varchar(50)                                                null comment '相对偏差',
    aValueTTZ                   varchar(50)                                                null comment '220吸光度',
    aValueTSF                   varchar(50)                                                null comment '275吸光度',
    orgId                       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '校准曲线详情' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_PRO_WorkSheetFolder
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    workSheetCode    varchar(20)                                                null comment '工作单号',
    createTime       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    analystId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析人Id',
    analystName      varchar(50)                                                null comment '分析人名称',
    analyzeTime      datetime    default '1753-01-01 00:00:00'                  not null on update CURRENT_TIMESTAMP comment '分析日期',
    analyzeMethodId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测方法id',
    checkerId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '复核人id',
    checkerName      varchar(50)                                                null comment '复核人名称',
    checkDate        datetime    default '1753-01-01 00:00:00'                  not null comment '复核日期',
    auditorId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '审核人id',
    auditorName      varchar(50)                                                null comment '审核人姓名',
    auditDate        datetime    default '1753-01-01 00:00:00'                  not null comment '审核日期',
    status           varchar(50)                                                null comment '工作单状态（字符串，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.工作单拒绝 8.已经提交 24.复核通过 32.审核通过）',
    workStatus       int         default 1                                      not null comment '工作单状态（int，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.工作单拒绝 8.已经提交 24.复核通过 32.审核通过）',
    backOpinion      varchar(1000)                                              null comment '退回意见（最新一个）',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    certificatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '上岗证人员id',
    certificatorName varchar(50)                                                null comment '上岗证人员姓名',
    sortId           varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '排序id',
    finishTime       datetime    default '1753-01-01 00:00:00'                  not null comment '分析完成日期'
) comment '工作单' ENGINE = InnoDB
                   CHARACTER SET = utf8
                   COLLATE = utf8_general_ci
                   ROW_FORMAT = Dynamic;

create index IX_TB_PRO_WorkSheetFolder
    on TB_PRO_WorkSheetFolder (orgId, analyzeTime, analystId, workStatus, workSheetCode);

create table TB_PRO_WorkSheetReagent
(
    id                    varchar(50)                                                not null comment 'id'
        primary key,
    worksheetFolderId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '工作单id',
    reagentConfigId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '试剂配置记录id',
    reagent               varchar(1000)                                              null comment '配置记录',
    context               varchar(1000)                                              null comment '需求的配置过程 ',
    reagentName           varchar(255)                                               null comment '试剂名称',
    reagentSpecification  varchar(255)                                               null comment '试剂规格',
    configurationSolution varchar(255)                                               null comment '配置溶液',
    configDate            datetime    default CURRENT_TIMESTAMP                      not null comment '配置日期',
    expiryDate            datetime    default '1753-01-01 00:00:00'                  not null comment '有效期',
    course                varchar(1000)                                              null comment '稀释过程记录',
    opinion               varchar(1000)                                              null comment '其他情况',
    orgId                 varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '工作单配置试剂配置' ENGINE = InnoDB
                               CHARACTER SET = utf8
                               COLLATE = utf8_general_ci
                               ROW_FORMAT = Dynamic;

create table TB_PRO_OrderContract
(
    id                  varchar(50)                                                not null comment 'id'
        primary key,
    orderId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '订单id',
    contractCode        varchar(50)                                                not null comment '合同编号',
    contractName        varchar(255)                                               not null comment '合同名称',
    contractNature      varchar(50)                                                not null comment '合同性质',
    firstEntId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '甲方id',
    firstEntName        varchar(255)                                               not null comment '甲方名称',
    firstEntPersonName  varchar(50)                                                not null comment '甲方联系人',
    secondEntName       varchar(255)                                               not null comment '乙方名称',
    secondEntPersonName varchar(50)                                                not null comment '乙方联系人',
    secondEntType       varchar(50)                                                null comment '乙方企业类型',
    totalAmount         decimal(18, 2)                                             null comment '合同金额',
    registrant          varchar(50)                                                null comment '登记人',
    signDate            datetime    default '1753-01-01 00:00:00'                  null comment '签订日期',
    excuteStartTime     datetime    default '1753-01-01 00:00:00'                  null comment '合同履行开始时间',
    excuteEndTime       datetime    default '1753-01-01 00:00:00'                  null comment '合同履行结束时间',
    signPersonId        text comment '签订人员id',
    isHavingSub         bit         default b'0'                                   null comment '是否有分包项',
    subAmount           decimal(18, 2)                                             null comment '分包金额',
    subOrgs             varchar(255)                                               null comment '分包机构',
    summary             varchar(1000)                                              null comment '合同概述',
    assessRecord        varchar(1000)                                              null comment '评审记录',
    isDeleted           bit         default b'0'                                   not null comment '假删',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate          datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    modifier            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate          datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '合同信息' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;


create table TB_PRO_OutSourceData
(
    id                varchar(50)                                                not null comment '主键'
        primary key,
    analyseDataId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析数据id',
    analyzeMethodName varchar(255)                                               null comment '分析方法名称',
    analyzeMethodId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id',
    testValue         varchar(50)                                                null comment '出证结果',
    dimensionName     varchar(50)                                                null comment '量纲名称',
    dimensionId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '量纲id',
    state             int         default 0                                      not null comment '状态（0未确认 1已确认）',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '分包数据表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;


create table TB_PRO_QualityControlEvaluate
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    objectId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id（分析数据Id）',
    qcId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '质控信息id',
    checkItem      varchar(100)                                               null comment '检查项（对应质控限值配置中的检查项，针对质控样，检查项默认“出证结果”）',
    judgingMethod  int                                                        null comment '评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）',
    isPass         bit                                                        null comment '是否合格（是否合格判定不满足判定条件时为空）',
    checkItemValue varchar(100)                                               null comment '检查项值',
    allowLimit     varchar(50)                                                null comment '允许限值',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '质控评价信息表' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;





