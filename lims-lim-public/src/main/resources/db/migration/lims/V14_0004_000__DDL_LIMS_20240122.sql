create table TB_PRO_LogForAnalyzeMethod
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作信息',
    objectId         varchar(50) NOT NULL DEFAULT '' COMMENT '分析方法id',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    INDEX            IX_TB_PRO_LogForAnalyzeMethod(objectId, orgId) USING BTREE
)  COMMENT = '分析方法状态日志';