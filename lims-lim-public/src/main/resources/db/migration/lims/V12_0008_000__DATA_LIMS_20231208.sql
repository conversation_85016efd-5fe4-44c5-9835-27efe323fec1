INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, validate, usageNum) VALUES ('a72e8361-b566-4fa6-a3ff-5fa84ee75ab2', 1, 'SwimingBathPoolRecord', 'SINOYD-LIMS-CY-27-01游泳池、公共浴池采样记录单.xlsx', '/Sampling/SINOYD-LIMS-CY-27-01游泳池、公共浴池采样记录单.xlsx', 'output/SamplingRecords/SINOYD-LIMS-CY-27-01游泳池、公共浴池采样记录单.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.SwimingBathPoolRecordService', '', '', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-08 14:12:49', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-08 14:12:49', '', 'Sampling', 'SwimingBathPoolRecord', b'0', '', NULL, '', '', '', 0, NULL);
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, validate, usageNum) VALUES ('dfe43963-0ec5-4e21-a611-6e16aedd8f36', 1, 'QYHJJCCDFBRecord', 'SINOYD-LIMS-ZY-37-01 企业环境检测测定点分布示意图.xls', '/Sampling/SINOYD-LIMS-ZY-37-01 企业环境检测测定点分布示意图.xls', 'output/SamplingRecords/SINOYD-LIMS-ZY-37-01 企业环境检测测定点分布示意图.xls', 'application/excel', 'com.sinoyd.lims.sampling.service.tz.samplingReport.TzCommonRecordService', '', 'pageByProject', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-08 13:14:25', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-08 13:14:25', '', 'Sampling', 'QYHJJCCDFBRecord', b'0', '', NULL, '', '', '', 0, NULL);
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, validate, usageNum) VALUES ('79265c6b-2b79-492e-aa9d-ffa690d5e98d', 1, 'LocalFXJLRecord', 'SINOYD-LIMS-CY-28-01现场监测分析记录表.xlsx', '/Sampling/SINOYD-LIMS-CY-28-01现场监测分析记录表.xlsx', 'output/SamplingRecords/SINOYD-LIMS-CY-28-01现场监测分析记录表.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.tz.samplingReport.TzCommonRecordService', '', 'pageByTest', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-08 10:57:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-08 10:57:59', '', 'Sampling', 'LocalFXJLRecord', b'0', '', NULL, '', '', '', 0, NULL);
