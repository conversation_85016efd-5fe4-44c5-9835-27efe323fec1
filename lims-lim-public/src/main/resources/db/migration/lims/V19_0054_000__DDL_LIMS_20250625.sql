-- 测试项目新增是否嗅辨开关字段
ALTER TABLE TB_PRO_QuotationDetail
    ADD COLUMN projectInterval varchar(50) NULL DEFAULT '' COMMENT '监测间隔';

-- 企业中添加排污许可证编号
ALTER TABLE TB_BASE_Enterprise
    ADD COLUMN pollutionDischargeCode varchar(255) NULL DEFAULT '' COMMENT '排污许可证编号';


-- 污染源点位关联测试项目数据添加监测间隔
ALTER TABLE TB_MONITOR_FixedPoint2Test
    ADD COLUMN projectInterval varchar(50) NULL DEFAULT '' COMMENT '监测间隔';


DROP TABLE IF EXISTS TB_BASE_PollutionDischargeSync;
CREATE TABLE TB_BASE_PollutionDischargeSync
(
    id           VARCHAR(50) NOT NULL COMMENT '主键',
    enterpriseId VARCHAR(50) NOT NULL COMMENT '企业id',
    requestTime  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发起时间',
    isSuccess    BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否同步成功',
    dataContent  LONGTEXT COMMENT '数据内容',
    PRIMARY KEY (id)
) COMMENT = '排污许可证同步';


-- 企业中添加排污许可证方案同步状态
ALTER TABLE TB_BASE_Enterprise
    ADD COLUMN isSyncPollutionDischarge bit(1) NULL DEFAULT b'0' COMMENT '排污许可证方案同步状态';