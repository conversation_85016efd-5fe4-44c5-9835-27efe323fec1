
ALTER TABLE TB_PRO_AnalyseData ADD isSamplingOut bit(1) not NULL DEFAULT b'0' COMMENT '是否采样分包';

ALTER TABLE TB_PRO_SamplingFrequencyTest ADD isSamplingOut bit(1) not NULL DEFAULT b'0' COMMENT '是否采样分包';


-- 样品中添加制备状态
ALTER TABLE TB_PRO_Sample
    ADD COLUMN preparedStatus int(11) NOT NULL DEFAULT 0 COMMENT '制备状态';


-- 样品表添加索引
ALTER TABLE TB_PRO_Sample
    ADD index INDEX_SamplePrepared(`code`,`isDeleted`,`orgId`,`preparedStatus`,`sampleTypeId`,`redFolderName`) COMMENT '用于样品制备查询';

-- 测试项目数据表添加索引
ALTER TABLE TB_PRO_AnalyseData
    ADD index IX_TB_PRO_AnalyseData7(`sampleId`, `orgId`, `isDeleted`, `analyzeMethodId`, `redAnalyzeItemName`, `redAnalyzeMethodName`) COMMENT '用于样品制备查询';

-- 创建样品制备表
DROP TABLE IF EXISTS TB_PRO_SamplePreparation;
CREATE TABLE TB_PRO_SamplePreparation
(
    id                   varchar(50) PRIMARY KEY NOT NULL DEFAULT '' COMMENT '标识',
    sampleId             varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品id',
    analyzeItemNames     varchar(100) NULL COMMENT '制备的样品下的分析项目名称',
    preparationBeginTime datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '制备开始时间',
    preparationEndTime   datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '制备结束时间',
    preparedPersonId     varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '制备人id',
    preparedPersonName   varchar(50) NULL COMMENT '制备人名称',
    method               varchar(200)            NOT NULL DEFAULT '' COMMENT '制备方法',
    content              varchar(500) NULL COMMENT '制备内容',
    orgId                varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator              varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate           datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId             varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier             varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate           datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
)
