-- 首页订单模块待办定时任务服务脚本
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy,
                         isConcurrent, status, remark, isDeleted, orgId, creator,
                         createDate, domainId, modifier, modifyDate)
VALUES ('7881d8e5-3980-4abd-8465-a5f50cd0945c', '首页订单登记待办统计', 'HOME',
        'orderRegisterStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-29 15:46:06',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-29 15:48:16');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy,
                         isConcurrent, status, remark, isDeleted, orgId, creator,
                         createDate, domainId, modifier, modifyDate)
VALUES ('b7f8a0e3-8a55-4357-ac03-85abcb8c2b48', '首页订单审核待办统计', 'HOME', 'orderAuditStatisticsTask.statisticsProjectNums',
        '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-12-29 15:46:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-12-29 15:47:13');
