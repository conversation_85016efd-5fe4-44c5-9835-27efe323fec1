-- 标准版报告质控说明组件配置调整
INSERT INTO tb_lim_reportmodule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode)
VALUES ('844acab5-8883-44d1-b8fa-af039526e967', 'qcStdDataSource', '标准版质控说明数据主表', 'dtQcDataSource', '', 0, 0,
        '[\"standardStdData\", \"innerBlankStdData\", \"OuterBlankStdData\", \"TransportBlankStdData\", \"SiteBlankStdData\", \"EquipBlankStdData\", \"innerParallelStdData\", \"markStdData\", \"replaceStdData\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-05-27 15:24:50',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-05-27 15:24:50', '0', '0',
        0, 0, 0);


delete
from TB_LIM_ReportConfig2Module
where reportModuleId in (
    select id
    from tb_lim_reportmodule
    where moduleCode in
          ('standardStdData', 'innerBlankStdData', 'OuterBlankStdData', 'TransportBlankStdData', 'SiteBlankStdData',
           'EquipBlankStdData', 'innerParallelStdData', 'markStdData', 'replaceStdData'));

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'SyStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'WasteWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'NormalWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'SurfaceWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'GroundWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'OrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'NoiseStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'SludgeStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'FlyAshStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'SolidStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'ComprehensiveStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '844acab5-8883-44d1-b8fa-af039526e967' from TB_LIM_ReportConfig where reportCode = 'SoilStd';