-- 新增标准版噪声昼夜报告组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('28f33429-0140-489e-837d-e7e535887907', 'noiseDayNightDataSource', '标准版噪声昼夜报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtTableHeadDay\", \"dtTableHeadDayNight\", \"dtNoiseDayNightTable\"]', b'1',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-03-11 15:07:01',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-03-11 15:07:01');

-- 新增标准版噪声报告昼间表头组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('7626547e-d18c-485b-97ac-783095ce7c4b', 'dtTableHeadDay', '标准版噪声昼间表头组件', 'dtTableHeadDay', '', 0, 0, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-03-11 15:15:25',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-03-11 15:15:25');

-- 新增标准版噪声报告昼夜表头组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('ed98fe49-0f55-458c-9bb1-10f07878d1ed', 'dtTableHeadDayNight', '标准版噪声昼夜表头组件', 'dtTableHeadDayNight', '', 0, 0,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-03-11 15:16:05',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-03-11 15:16:05');

-- 新增标准版噪声昼夜检测结果表组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('075c534a-e189-4e16-850c-77c62b610820', 'dtNoiseDayNightTable', '标准版噪声昼夜检测结果表组件', 'dtNoiseDayNightTable',
        'dtNoiseSource', 10, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-03-11 15:35:25', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-03-11 15:35:25');


-- 标准版噪声报告组件配置调整
update TB_LIM_ReportConfig2Module
set reportmoduleId = '28f33429-0140-489e-837d-e7e535887907'
where id = '64e4e214-ff8b-49aa-b358-ffa141557627'
  and reportconfigid = '76707b2e-4981-4fce-896a-18ced00ad760';
