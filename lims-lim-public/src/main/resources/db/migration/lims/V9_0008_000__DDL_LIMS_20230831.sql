ALTER TABLE TB_QA_InternalAuditImplementPlan
    ADD COLUMN auditedDeptId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '审核部门id';

ALTER TABLE TB_QA_InternalAuditPlanReport
    MODIFY COLUMN makerTime datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '编制日期';

ALTER TABLE TB_QA_MonitoringPlanCheckInfo
    ADD COLUMN orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    ADD COLUMN creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    ADD COLUMN createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    ADD COLUMN domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '实验室id',
    ADD COLUMN modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    ADD COLUMN modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间';