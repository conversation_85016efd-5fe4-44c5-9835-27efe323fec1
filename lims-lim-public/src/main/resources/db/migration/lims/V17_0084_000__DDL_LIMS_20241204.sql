DROP PROCEDURE IF EXISTS `add_col`;
<PERSON><PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_Test' AND column_name = 'samplingMethodId')) THEN
ALTER TABLE TB_LIM_Test ADD COLUMN samplingMethodId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样方法Id（Guid）';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;