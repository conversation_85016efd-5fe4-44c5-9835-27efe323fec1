-- ---------------------------------------------------------------
-- 创建消费端消息监控表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_MessageReceiveMonitor;
CREATE TABLE TB_BASE_MessageReceiveMonitor
(
    id         VARCHAR(50)  NOT NULL COMMENT '主键',
    message    TEXT         NOT NULL COMMENT '消息内容',
    exchange   VARCHAR(255) COMMENT '交换机',
    routingKey VARCHAR(255) COMMENT '路由键',
    queue      VARCHAR(255) COMMENT '队列',
    isSuccess  BIT(1) NOT NULL COMMENT '是否发送成功',
    reason     VARCHAR(255) NOT NULL COMMENT '原因',
    sendStatus int(11) NOT NULL DEFAULT 2 COMMENT '重发状态',
    orgId      VARCHAR(50)  NOT NULL COMMENT '所属机构ID',
    domainId   VARCHAR(50)  NOT NULL COMMENT '所属实验室ID',
    creator    VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier   VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '消费端消息监控表';
