-- 饮食油烟报告配置脚本

DELETE
FROM TB_LIM_ReportConfig
where id = 'a1782d39-61e6-481a-aa83-de66fccfce9c';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('a1782d39-61e6-481a-aa83-de66fccfce9c', 1, 'CookingFumeStd', '标准版报告.doc', 'Report/标准版报告.doc',
        'output/Report/饮食油烟报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', NULL,
        850, 4, '饮食油烟报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-07-14 14:02:29', '5f7bcf90feb545968424b0a872863876', '636d7cfd-232a-4ff1-9d5b-5649db8a79b3',
        '2025-07-14 15:07:29', 'reportId,sortId', 'Report', 'CookingFumeStd', b'0', '', NULL, '1.0', 'LET-', '', 0,
        NULL);

DELETE
FROM TB_LIM_ReportApply
where id = '9c6194cb-eac8-4ed7-8a10-d33b606174b0';


INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('9c6194cb-eac8-4ed7-8a10-d33b606174b0', 'a1782d39-61e6-481a-aa83-de66fccfce9c', 'ReportEditNew', '报告编制',
        'UnOrgGasStd', '饮食油烟报告', 1, 1, 1, NULL, '报告编制:电子报告', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-07-14 14:02:49', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-07-14 14:02:49', NULL);

DELETE
FROM TB_LIM_RecordConfig
where id = '0606053e-2851-4015-8e2b-ff7bfb267795';

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('0606053e-2851-4015-8e2b-ff7bfb267795', '饮食油烟报告', 3, 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        '00000000-0000-0000-0000-000000000000', NULL, b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-07-14 15:26:57', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-07-14 08:46:12', NULL, 0);

DELETE
FROM TB_LIM_ReportModule
where id = '5a716219-108f-456c-aa8e-808f79992dee';

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate, totalTest,
                                auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('5a716219-108f-456c-aa8e-808f79992dee', 'dtCookFumeToStdTable', '标准版饮食油烟检测结果表组件（批次）', 'dtCookFumeToStdTable',
        'dtCookFumeToSource', 0, 0, NULL, b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-07-14 17:59:40', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-07-14 17:59:40', '1', '0', 0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportModule
where id = '092a70fc-2d85-4b60-bc2f-3bdf7a4f477d';

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate, totalTest,
                                auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('092a70fc-2d85-4b60-bc2f-3bdf7a4f477d', 'dtCookFumeSmkStdTable', '标准版饮食油烟烟气参数表组件（批次）', 'dtCookFumeSmkStdTable',
        'dtCookFumeSmkSource', 0, 5, NULL, b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-07-14 18:13:58', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-07-14 18:13:58', '1', '0', 0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportModule
where id = '86dc6c16-93f3-441a-9f2b-8dbaca883b84';

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate, totalTest,
                                auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('86dc6c16-93f3-441a-9f2b-8dbaca883b84', 'dtCookFumeSmkZsSpdDataSource', '标准版有组织表头烟气参数样品检测数据主表（批次）',
        'dtOrgSmkZsSpdDataSource', NULL, 0, 0,
        '[\"dtOrgToZtHeadStdTable\", \"dtCookFumeSmkStdTable\", \"dtCookFumeToStdTable\", \"dtOrgToCpdStdTable\", \"dtOrgToKbStdTable\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-04-26 17:22:44',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-07-14 18:22:44', '1', '0',
        0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportModule
where id = '6126dcdf-8209-48d5-872f-88b659f750b1';

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate, totalTest,
                                auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('6126dcdf-8209-48d5-872f-88b659f750b1', 'cookFumeToStdDataSource', '标准版饮食油烟报告检测数据主表（批次）', 'dtDataSource', NULL,
        0, 0, '[\"dtCookFumeSmkZsSpdDataSource\",  \"outParallelStdDataSource\"]', b'1',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-07-14 18:18:39',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-07-14 18:18:39', '1', '0',
        0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportConfig2Module
where reportConfigId = 'a1782d39-61e6-481a-aa83-de66fccfce9c';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('007deb91-6ec0-4502-af5d-3c07afd906b3', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        '6126dcdf-8209-48d5-872f-88b659f750b1');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('26b46dbb-f978-4324-af94-94520b7b8ec1', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('2d9ccf87-801c-4de2-abe3-1ec1edfd72b5', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        'bf67cab8-6760-4a4c-84f5-ff89e789161b');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('9497746e-e1a9-40e8-9606-a5bf1523362f', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        'f8673c96-4113-4eed-8eeb-3235615cb5f9');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('973aae29-d92c-44d6-8017-0f127636302b', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b879f144-9813-4aaf-9592-743ecba69cc5', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        'ac693c4a-7752-4473-a42d-3e38988c1f5d');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('c5fb7785-9b50-4368-8f99-7905cd40befe', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        '844acab5-8883-44d1-b8fa-af039526e967');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('cc53d955-0785-4c58-b4f8-d74862adf67d', 'a1782d39-61e6-481a-aa83-de66fccfce9c',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

DELETE
FROM TB_LIM_ReportModule2GroupType
where id in ('22f3a30c-52ec-46e9-9fee-a7c9cee79c86', 'ca55cea6-264b-4a5e-b001-fa84790bad45');

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('22f3a30c-52ec-46e9-9fee-a7c9cee79c86', '007deb91-6ec0-4502-af5d-3c07afd906b3',
        'sampleData_sampleFolderId_fieldGroupType', 10);

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('ca55cea6-264b-4a5e-b001-fa84790bad45', '007deb91-6ec0-4502-af5d-3c07afd906b3',
        'sampleData_samplingTimeBegin_dateGroupType', 9);
