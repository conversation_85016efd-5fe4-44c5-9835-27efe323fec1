-- 业务表索引统一新增修改
DROP INDEX UIX_TB_PRO_AnalyseOriginalRecord ON TB_PRO_AnalyseOriginalRecord;
CREATE INDEX UIX_TB_PRO_AnalyseOriginalRecord ON TB_PRO_AnalyseOriginalRecord ( analyseDataId, isDeleted );
CREATE INDEX UIX_TB_PRO_EvaluationRecord ON TB_PRO_EvaluationRecord ( objectId, isDeleted );
DROP INDEX UIX_TB_PRO_ParamsData ON TB_PRO_ParamsData;
CREATE INDEX UIX_TB_PRO_ParamsData ON TB_PRO_ParamsData ( objectId, paramsConfigId, groupId, objectType, orgId, isDeleted );
DROP INDEX IX_TB_PRO_QualityControl ON TB_PRO_QualityControl;
CREATE INDEX IX_TB_PRO_QualityControl ON TB_PRO_QualityControl ( orgId, associateSampleId, qcGrade, qcType, isDeleted );
CREATE INDEX IX_TB_PRO_QualityControlEvaluate ON TB_PRO_QualityControlEvaluate ( objectId, isDeleted );
CREATE INDEX IX_TB_PRO_SampleGroup ON TB_PRO_SampleGroup ( receiveId, sampleId, isDeleted );
CREATE INDEX IX_TB_PRO_WorkSheetCalibrationCurve ON TB_PRO_WorkSheetCalibrationCurve ( worksheetId, standardCurveId, isDeleted );
CREATE INDEX IX_TB_PRO_WorkSheet ON TB_PRO_WorkSheet ( parentId, testId, isDeleted );
CREATE INDEX IX_TB_PRO_WorkSheetCalibrationCurveDetail ON TB_PRO_WorkSheetCalibrationCurveDetail ( workSheetCalibrationCurveId, isDeleted );
DROP INDEX IX_TB_PRO_ReceiveSubSampleRecord2Sample ON TB_PRO_ReceiveSubSampleRecord2Sample;
CREATE INDEX IX_TB_PRO_ReceiveSubSampleRecord2Sample ON TB_PRO_ReceiveSubSampleRecord2Sample ( receiveSubSampleRecordId, sampleId, isDeleted );
CREATE INDEX IX_TB_PRO_WorkSheetReagent ON TB_PRO_WorkSheetReagent ( worksheetFolderId, reagentConfigId, isDeleted );