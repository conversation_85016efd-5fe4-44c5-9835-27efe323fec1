INSERT INTO `tb_base_job` (`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c78', '首页方案审核待办统计', 'HOME', 'auditSolutionStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7a', '首页实验室审核待办统计', 'HOME', 'laboratoryAuditStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7b', '首页实验室检测中代办统计', 'HOME', 'laboratoryCheckingStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7c', '首页实验室待待检测代办统计', 'HOME', 'laboratoryWaitingCheckStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7d', '首页现场任务数据审核待办统计', 'HOME', 'localDataAuditStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:23:42');

INSERT INTO `tb_base_job` (`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7e', '首页现场任务数据复核待办统计', 'HOME', 'localDataCheckStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:24:12');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7f', '首页现场任务待办统计', 'HOME', 'localMissionStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:23:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7g', '首页委托现场送样待办统计', 'HOME', 'localSendSampleStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:33:47');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7h', '首页方案编制待办统计', 'HOME', 'makeSolutionStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7i', '首页重点污染源多企业待办统计', 'HOME', 'pollutionEnterprisesStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7j', '首页采样准备待办统计', 'HOME', 'prepareSampleStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7k', '首页项目审核待办统计', 'HOME', 'projectAuditStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7l', '首页任务办结待办统计', 'HOME', 'projectEndStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7m', '首页项目下达待办统计', 'HOME', 'projectIssueStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7n', '首页项目登记待办统计', 'HOME', 'projectRegisterStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');


INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7o', '首页数据汇总待办统计', 'HOME', 'qcCollectStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7p', '首页评价结果待办统计', 'HOME', 'qcEvaluateStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7r', '首页质控任务登记待办统计', 'HOME', 'qcRegisterStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7s', '首页报告审核待办统计', 'HOME', 'reportAuditStatisticTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7t', '首页报告校核待办统计', 'HOME', 'reportCheckStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7u', '首页报告编制待办统计', 'HOME', 'reportEditStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7v', '首页报告复核待办统计', 'HOME', 'reportReviewStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7w', '首页报告签发待办统计', 'HOME', 'reportSignStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7x', '首页例行项目登记待办统计', 'HOME', 'rontineRegisterStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7y', '首页样品分配待办统计', 'HOME', 'sampleAssignStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

INSERT INTO `tb_base_job`(`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`, `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9634c7z', '首页样品交接待办统计', 'HOME', 'sampleReceiveStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');