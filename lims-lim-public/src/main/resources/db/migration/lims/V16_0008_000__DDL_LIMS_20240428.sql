-- ocr新增识别记录表
CREATE TABLE tb_lim_ocrConfigRecord (
                                           id varchar(50) NOT NULL COMMENT 'id',
                                           configId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'OCR对象标识',
                                           sampleCode varchar(50) NOT NULL  COMMENT '样品编号',
                                           groupId varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分组标识',
                                           groupName varchar(255)  COMMENT '分组名称',
                                           filePath varchar(500) NOT NULL  COMMENT '文件保存路径',
                                           originData text  COMMENT 'ocr原始数据',
                                           isDeleted  bit  default b'0' not null comment '假删字段',
                                           orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                           domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                           creator VARCHA<PERSON> ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                           createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
                                           modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                           PRIMARY KEY (id)
);
CREATE INDEX UIX_tb_lim_ocrConfigRecord ON tb_lim_ocrConfigRecord ( configId,sampleCode,groupId,isDeleted,orgId);
-- 字段移动到记录表上
alter table tb_lim_ocrconfigparamdata drop column configId;
alter table tb_lim_ocrconfigparamdata drop column sampleId;
alter table tb_lim_ocrconfigparamdata drop column collectNo;
alter table tb_lim_ocrconfigparamdata drop column filePath;
alter table tb_lim_ocrconfigparamdata drop column originData;
ALTER TABLE tb_lim_ocrconfigparamdata ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE tb_lim_ocrconfigparamdata ADD COLUMN recordId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '识别记录标识';
CREATE INDEX UIX_tb_lim_ocrconfigparamdata ON tb_lim_ocrconfigparamdata ( recordId,isDeleted,orgId);

alter table tb_lim_ocrconfigparamdatahistory drop column configId;
alter table tb_lim_ocrconfigparamdatahistory drop column sampleId;
alter table tb_lim_ocrconfigparamdatahistory drop column collectNo;
alter table tb_lim_ocrconfigparamdatahistory drop column filePath;
alter table tb_lim_ocrconfigparamdatahistory drop column originData;
ALTER TABLE tb_lim_ocrconfigparamdatahistory ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE tb_lim_ocrconfigparamdatahistory ADD COLUMN recordId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '识别记录标识';
CREATE INDEX UIX_tb_lim_ocrconfigparamdatahistory ON tb_lim_ocrconfigparamdatahistory ( recordId,isDeleted,orgId);
