-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关DDL脚本 ------------
-- ---------------- LIM模块 --------------------
-- ----------------------------------------------

create table TB_LIM_AnalyzeItemRelation
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    analyzeItemId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目Id',
    analyzeItemName varchar(50)                                                null comment '分析项目名称',
    formula         varchar(500)                                               null comment '公式',
    configDate      datetime    default CURRENT_TIMESTAMP                      not null comment '配置日期',
    type            int         default -1                                     not null comment '类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_AnalyzeItemRelationParams
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    relationId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目关系',
    analyzeItemId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目Id',
    analyzeItemName varchar(50)                                                null comment '分析项目名称',
    orderNum        int         default 0                                      not null comment '排序值（预留：列表显示排序用）',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_LIM_AnalyzeMethod
(
    id                  varchar(50)                                                not null comment 'id'
        primary key,
    methodName          varchar(255)                                               null comment '方法名称',
    countryStandard     varchar(50)                                                null comment '标准编号',
    isDeleted           bit         default b'0'                                   not null comment '是否删除',
    effectiveDate       datetime    default '1753-01-01 00:00:00'                  not null comment '标准实施日期',
    methodCode          varchar(50)                                                null comment '受控编号',
    isCompleteTogether  bit         default b'0'                                   not null comment '是否可以同时完成',
    isControlled        bit         default b'1'                                   not null comment '是否受控',
    remark              varchar(1000)                                              null comment '备注',
    parentId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父级Id（Guid）（预留，例：XXX方法，拉伸测试）',
    countryStandardName varchar(100)                                               null comment '国家标准名称（预留）',
    yearSn              varchar(50)                                                null comment '年度（预留）',
    effectiveDays       int         default -1                                     not null comment '有效天数（预留）',
    warningDays         int         default -1                                     not null comment '警告天数（预留）',
    isInforce           bit         default b'1'                                   not null comment '是否现行有效（预留）',
    status              varchar(50)                                                null comment '状态（预留）',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate          datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate          datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    isInputBySample     bit         default b'0'                                   not null comment '是否按样品录入 1：是  0：否',
    alias               varchar(50)                                                null comment '别名',
    isCrossDay          bit         default b'0'                                   not null comment '是否跨天完成'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_LIM_Cost
(
    id                   varchar(50)                                                   not null comment 'id'
        primary key,
    testId               varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    redAnalyzeItemName   varchar(50)                                                   null comment '分析项目名称',
    redAnalyzeMethodName varchar(255)                                                  null comment '分析方法名称',
    redCountryStandard   varchar(50)                                                   null comment '标准编号',
    sampleTypeId         varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '检测类型id',
    samplingCost         decimal(18, 2) default 0.00                                   not null comment '采样费',
    analyzeCost          decimal(18, 2) default 0.00                                   not null comment '分析费',
    orgId                varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate           datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId             varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier             varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate           datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CostRule
(
    id         varchar(50)                                                   not null comment 'id'
        primary key,
    laborCost  decimal(18, 2)                                                not null comment '人工费单价',
    carCost    decimal(18, 2)                                                not null comment '车辆费单价',
    testRate   decimal(18, 2) default 100.00                                 not null comment '检测费折扣率',
    offerRate  decimal(18, 2) default 100.00                                 not null comment '报价折扣率',
    reportRate decimal(18, 2) default 100.00                                 not null comment '报告费费率',
    orgId      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CostRuleForEnt
(
    id         varchar(50)                                                   not null comment 'id'
        primary key,
    entId      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '企业id',
    laborCost  decimal(18, 2)                                                not null comment '人工费单价',
    testRate   decimal(18, 2) default 100.00                                 not null comment '检测费折扣率',
    offerRate  decimal(18, 2) default 100.00                                 not null comment '报价折扣率',
    orgId      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间',
    constraint UIX_TB_LIM_CostRuleForEnt
        unique (entId)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_Params
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    paramCode    varchar(50)                                                null comment '唯一编号',
    paramName    varchar(50)                                                null comment '参数名称',
    remark       varchar(255)                                               null comment '备注',
    variableName varchar(50)                                                null comment '变量名称',
    regex        varchar(100)                                               null comment '正则表达式验证',
    dimension    varchar(50)                                                null comment '计量单位',
    dimensionId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位Id（Guid）',
    isDeleted    bit         default b'0'                                   not null comment '是否删除',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '参数（样品属性参数、公式的参数、仪器的数据参数）' ENGINE = InnoDB
                                                         CHARACTER SET = utf8
                                                         COLLATE = utf8_general_ci
                                                         ROW_FORMAT = Dynamic;

create table TB_LIM_Params2ParamsFormula
(
    id             varchar(50)                                                not null comment 'id'
        primary key,
    recordId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '记录单Id',
    paramsConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '参数配置Id',
    objectId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id（测试公式id-记录单参数，测试项目id-工作单参数）',
    formula        varchar(1000)                                              null comment '公式',
    isEnabled      bit         default b'0'                                   not null,
    isDeleted      bit         default b'0'                                   not null comment 'isDeleted',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_ParamsConfig
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    objId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    paramsId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '参数id',
    alias            varchar(50)                                                not null comment '参数使用名称',
    defaultValue     varchar(50)                                                null comment '默认值',
    dimension        varchar(50)                                                null comment '计量单位',
    dimensionId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位id',
    orderNum         int         default 0                                      not null comment '排序值',
    type             int         default -1                                     not null comment '对象类型（枚举EnumParamsConfigType：1.检测（样品）类型，2.测试项目，3.企业（预留）,4.方法（预留）,5.采样单（预留）6.原始记录单7.报告（预留）8.样品-采样单参数也是检测（样品）类型上的参数，只是进行设置用到采样单分组（检测（样品）类型公共参数）9.原始记录单-工作单参数（原始记录单上面部分的计算，如化学需氧量、BOD5）',
    defaultControl   int         default 1                                      not null comment '默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）',
    dataSource       varchar(2000)                                              null comment '数据源',
    isDeleted        bit         default b'0'                                   not null comment 'isDeleted',
    isRequired       bit         default b'0'                                   not null comment '是否必填（用于测试公式，检测类型参数）',
    mostSignificance int         default -1                                     not null comment '有效位数',
    mostDecimal      int         default -1                                     not null comment '小数位数',
    analyzeItemId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目Id（用于检测类型相关分析项目）',
    parentId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父节点Id（用于检测类型相关分析项目）',
    isShow           bit         default b'1'                                   not null comment '页面上是否显示，默认显示',
    isFormula        bit         default b'0'                                   not null comment '是否有公式，有配置公式更新这个字段',
    formulaId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '公式Id',
    isAllConfig      bit         default b'0'                                   not null comment '是否所有参数配置完成（用于记录单配置参数颜色区分）',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    paramsType       int         default -1                                     not null comment '参数类型（枚举EnumParamsType：1.公共参数、2.样品参数、3.分析项目参数、4.点位参数）'
) comment '参数配置表（样品类型，企业，测试）' ENGINE = InnoDB
                                           CHARACTER SET = utf8
                                           COLLATE = utf8_general_ci
                                           ROW_FORMAT = Dynamic;

create table TB_LIM_ParamsFormula
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    objectId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '参数Id（测试项目、检测类型参数）',
    formula           varchar(1000)                                              null comment '公式',
    configDate        datetime    default CURRENT_TIMESTAMP                      not null comment '配置日期',
    orignFormula      mediumtext                                                 null comment '原始公式',
    orignFormulatType int         default 0                                      not null comment '原始公式类型（枚举EnumOrignFormulatType：0:手写html,1:图片）',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    objectType        int         default -1                                     not null comment '类型（枚举EnumParamsFormulaObjectType：0.测试公式 1.检测类型参数公式）',
    sampleTypeId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测类型id（仅用于测试项目）',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_LIM_ParamsPartFormula
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    formulaId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '公式Id',
    formula          varchar(1000)                                              not null comment '部分公式',
    paramsName       varchar(100)                                               null,
    mostSignificance int         default -1                                     not null comment '有效位数',
    mostDecimal      int         default -1                                     not null comment '小数位数',
    orderNum         int         default 0                                      not null comment '排序值',
    formulaType      int         default -1                                     not null comment '类型（EnumPartFormulaType：0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    detectionLimit   varchar(50)                                                null comment '测试项目部分公式检出限',
    calculationMode  int                                                        null comment '计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）',
    useTestLimit     bit         default b'1'                                   not null comment '是否使用测试项目检出限'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_ParamsTestFormula
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    objId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id（如测试公式id）',
    paramsId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '参数id',
    paramsName      varchar(50)                                                null comment '参数名称',
    alias           varchar(50)                                                null comment '参数别名',
    defaultValue    varchar(50)                                                null comment '默认值',
    orderNum        int         default 0                                      not null comment '排序值',
    aliasInReport   varchar(50)                                                null comment '工作单模板中的变量名',
    dimensionId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位id',
    dimension       varchar(50)                                                null comment '计量单位',
    sourceType      int         default 0                                      not null comment '类型（枚举EnumSourceType：0.无,1.样品，2.测试，3.企业，4.原始记录单）',
    isMust          bit         default b'0'                                   not null comment '是否必填',
    isEditable      bit         default b'1'                                   not null comment '是否允许修改',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    detectionLimit  decimal(10, 2)                                             null comment '参数检出限',
    calculationMode int                                                        null comment '计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）'
) comment '测试公式与参数的关联表' ENGINE = InnoDB
                                   CHARACTER SET = utf8
                                   COLLATE = utf8_general_ci
                                   ROW_FORMAT = Dynamic;


create table TB_LIM_ProjectType
(
    id         varchar(50)                                                not null comment 'id'
        primary key,
    parentId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父节点id',
    mark       varchar(255)                                               null comment '标识',
    orderNum   int                                                        not null comment '排序值',
    remark     text                                                       null comment '备注',
    workflowId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '工作流id',
    isDeleted  tinyint     default 0                                      not null comment '假删',
    config     longtext                                                   null comment '配置信息',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    name       varchar(255)                                               null comment '项目类型名称'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_LIM_RecordConfig
(
    id             varchar(50)                                                not null comment 'id'
        primary key,
    recordName     varchar(100)                                               not null comment '名称',
    recordType     int         default -1                                     not null comment '记录单类型(枚举EnumRecordType：1:采样记录单,2:原始记录单,3:报告)',
    reportConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '报表模板Id',
    sampleTypeId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测类型Id(小类)',
    remark         varchar(255)                                               null comment '备注',
    isDeleted      bit         default b'0'                                   not null comment '假删',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_RecordConfig2ParamsConfig
(
    id             varchar(50)                                                not null comment '主键id'
        primary key,
    recordConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '采样单配置id',
    paramsConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '参数配置id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_RecordConfig2Test
(
    id             varchar(50)                                                not null comment '主键id'
        primary key,
    recordConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '记录单配置id',
    testId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id'
) comment '原始记录单与测试项目多对多关系' ENGINE = InnoDB
                                           CHARACTER SET = utf8
                                           COLLATE = utf8_general_ci
                                           ROW_FORMAT = Dynamic;

create table TB_LIM_ReportApply
(
    id             varchar(50)                                                not null comment '主键id'
        primary key,
    reportConfigId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '配置id',
    module         varchar(50)                                                null comment '所属模块',
    moduleName     varchar(50)                                                null comment '所属模块名称',
    code           varchar(50)                                                null comment '控件编码',
    name           varchar(50)                                                null comment '显示名称',
    type           int         default 0                                      not null comment '控件类型',
    isRedact       int         default 0                                      not null comment '是否可编辑',
    isShow         int         default 0                                      not null comment '是否显示名称',
    remark         varchar(500)                                               null comment '备注',
    location       varchar(50)                                                null comment '地址',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_LIM_SampleTypeGroup
(
    id            varchar(50)                                                not null comment 'id'
        primary key,
    parentId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父节点（Guid）',
    groupType     int         default -1                                     not null comment '分组类型（枚举EnumGroupType：1.分组规则，2.分组）',
    groupName     varchar(50)                                                null comment '分组名称',
    sampleTypeId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品类型的id（Guid）',
    fixer         varchar(1000)                                              null comment '固定剂',
    containerName varchar(255)                                               null comment '容器名称',
    saveCondition varchar(1000)                                              null comment '保存条件',
    orderNum      int         default 0                                      not null comment '排序值',
    remark        varchar(1000)                                              null comment '备注',
    volumeType    varchar(100)                                               null comment '体积类型',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_SampleTypeGroup2Test
(
    sampleTypeGroupId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '分组的id',
    testId            varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '测试项目id',
    id                varchar(50) not null comment '主键' primary key
) comment '测试项目分组信息和测试项目关系' ENGINE = InnoDB
                                           CHARACTER SET = utf8
                                           COLLATE = utf8_general_ci
                                           ROW_FORMAT = Dynamic;


create table TB_LIM_TestQCRange
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    testId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试标识',
    rangeConfig varchar(50)                                                null comment '数值范围',
    relLimit    varchar(50)                                                null comment '平行：相对偏差，其他质控类型：偏差',
    absLimit    varchar(50)                                                null comment '绝对偏差',
    qcGrade     int         default -1                                     not null comment '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）',
    qcType      int         default -1                                     not null comment '质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_TestQCRemindConfig
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    qcGrade         int         default -1                                     not null comment '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）',
    qcType          int         default -1                                     not null comment '质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）',
    qcRemindPercent int         default 10                                     not null comment '质控百分比',
    isDefault       bit         default b'0'                                   not null comment '是否默认',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_TestQCRemindConfig2Test
(
    configId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '质控比例配置id',
    testId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    id       varchar(50)                                                not null
        primary key
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_VersionInfo
(
    id         varchar(50)                                                not null comment '主键' primary key,
    version    varchar(50)                                                null comment '版本号',
    verValue   varchar(2000)                                              null comment '更新内容',
    verUrl     varchar(255)                                               null comment '附件路径',
    verTime    datetime                                                   not null comment '上传时间',
    verType    varchar(50)                                                not null comment '版本类型',
    verCode    varchar(300)                                               null comment '版本二维码',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    codeUrl    varchar(3000)                                              null comment '二维码链接'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_Lim_AppConfig
(
    id           varchar(50)      not null comment '主键'
        primary key,
    name         varchar(100)     not null comment '应用名称',
    code         varchar(50)      not null comment '应用编码',
    linkAddress  varchar(1000)    null comment '链接地址',
    roleId       varchar(1000)    null comment '角色',
    status       bit default b'1' null comment '启用状态',
    orderNum     int default 0    null comment '排序值',
    remark       varchar(1000)    null comment '备注',
    orgId        varchar(50)      not null comment '组织机构id',
    creator      varchar(50)      not null comment '创建人',
    createDate   datetime         not null comment '创建时间',
    domainId     varchar(50)      not null comment '所属实验室',
    modifier     varchar(50)      not null comment '修改人',
    modifyDate   datetime         not null comment '修改时间',
    type         varchar(50)      null comment '应用分类',
    typeOrderNum int              not null comment '类型排序值'
) comment 'app应用配置表' ENGINE = InnoDB
                          CHARACTER SET = utf8
                          COLLATE = utf8_general_ci
                          ROW_FORMAT = Dynamic;

create table TB_LIM_CalendarDate
(
    id           varchar(50) not null comment '主键'
        primary key,
    holidayName  varchar(50) null comment '节假日名称',
    calendarDate date        not null comment '日历日期',
    weekday      int         not null comment '星期数（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六）',
    type         int         not null comment '类型（0：工作日，1：休息日）',
    orgId        varchar(50) not null comment '组织机构id',
    creator      varchar(50) not null comment '创建人',
    createDate   datetime    not null comment '创建时间',
    domainId     varchar(50) not null comment '所属实验室',
    modifier     varchar(50) not null comment '修改人',
    modifyDate   datetime    not null comment '修改时间'
) comment '日历日期' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_LIM_HolidayConfig
(
    id          varchar(50) not null comment '主键'
        primary key,
    year        int         not null comment '年份',
    holidayName varchar(50) not null comment '节假日名称',
    beginDate   date        not null comment '开始时间',
    endDate     date        not null comment '结束时间',
    orgId       varchar(50) not null comment '组织机构id',
    creator     varchar(50) not null comment '创建人',
    createDate  datetime    not null comment '创建时间',
    domainId    varchar(50) not null comment '所属实验室',
    modifier    varchar(50) not null comment '修改人',
    modifyDate  datetime    not null comment '修改时间'
) comment '节假日管理配置' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_LIM_PublishSystemVersion
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    title          varchar(200)                                               not null comment '标题',
    versionNum     varchar(100)                                               not null comment '版本号',
    publishPerson  varchar(50)                                                not null comment '发布人',
    publishDate    datetime                                                   not null comment '发布日期',
    flaywayVersion varchar(50)                                                not null comment 'flayway版本',
    isProduct      bit         default b'1'                                   not null comment '是否产品 0否 1是',
    isPublish      bit         default b'1'                                   not null comment '是否产品 0否 1是',
    isTemplate     bit         default b'0'                                   not null comment '是否产品 0否 1是',
    isConfig       bit         default b'0'                                   not null comment '是否产品 0否 1是',
    updateContent  text                                                       not null comment '更新内容',
    deployContent  text                                                       null comment '部署注意事项',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '版本发布管理表' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_LIM_WorkdayConfig
(
    id         varchar(50) not null comment '主键'
        primary key,
    workday    varchar(20) not null comment '工作日（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六 以英文逗号拼接）',
    weekendDay varchar(20) not null comment '休息日（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六 以英文逗号拼接）',
    year       int         not null comment '年份',
    orgId      varchar(50) not null comment '组织机构id',
    creator    varchar(50) not null comment '创建人',
    createDate datetime    not null comment '创建时间',
    domainId   varchar(50) not null comment '所属实验室',
    modifier   varchar(50) not null comment '修改人',
    modifyDate datetime    not null comment '修改时间'
) comment '工作休息日管理配置' ENGINE = InnoDB
                               CHARACTER SET = utf8
                               COLLATE = utf8_general_ci
                               ROW_FORMAT = Dynamic;







