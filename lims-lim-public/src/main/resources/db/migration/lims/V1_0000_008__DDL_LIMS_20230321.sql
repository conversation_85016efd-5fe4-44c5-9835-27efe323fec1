-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- ---------------- qa模块 --------------------
-- ----------------------------------------------

create table TB_QA_AnnualPlan
(
    id             varchar(50)                                                not null comment '主键' primary key,
    year           int         default 0                                      not null comment '年度',
    planType       varchar(50)                                                not null comment '年度计划类型',
    makePersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '制定人Id',
    makePersonName varchar(50)                                                null comment '制定人名称',
    makeTime       datetime                                                   not null comment '制定日期',
    auditContent   varchar(2000)                                              not null comment '审核内容',
    auditPurpose   varchar(2000)                                              not null comment '审核目的',
    remark         varchar(2000)                                              null comment '备注',
    status         varchar(50)                                                null comment '状态：编制中、审核中、审核通过、审核不通过',
    isDeleted      bit         default b'0'                                   not null comment '是否删除',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '年度计划表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_QA_CustomerComplaintRegist
(
    id                 varchar(50)                                                not null comment '主键' primary key,
    complaintName      varchar(250)                                               not null comment '投诉方名称',
    entId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '投诉企业',
    complaintPerson    varchar(50)                                                not null comment '投诉人员',
    complaintPersonId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '投诉人员Id',
    problemDescription varchar(2000)                                              not null comment '投诉问题描述',
    phone              varchar(20)                                                null comment '电话',
    email              varchar(100)                                               null comment '邮箱地址',
    type               varchar(50)                                                null comment '投诉方式（常量）',
    complaintDate      datetime                                                   not null comment '投诉日期',
    registPerson       varchar(50)                                                not null comment '登记人员',
    registPersonId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '登记人员Id',
    registDate         datetime                                                   not null comment '登记日期',
    level              varchar(50)                                                null comment '投诉级别',
    finshDate          datetime                                                   null comment '要求完成日期',
    opinion            varchar(1000)                                              null comment '成立意见',
    status             varchar(50)                                                not null comment '状态',
    isDeleted          bit         default b'0'                                   not null comment '是否删除',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_QA_InternalAuditImplementPlan
(
    id             varchar(50)                                                not null comment '主键' primary key,
    auditTime      datetime                                                   not null comment '审核时间',
    auditedDept    varchar(100)                                               not null comment '审核部门',
    personInCharge varchar(50)                                                not null comment '责任人',
    auditor        varchar(50)                                                not null comment '审核人员',
    auditElement   varchar(2000)                                              null comment '审核要素',
    isDeleted      bit         default b'0'                                   not null comment '是否删除',
    status         varchar(50)                                                null,
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    auditPlanId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null
) comment '内审实施计划' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_InternalAuditPlan
(
    id           varchar(50)                                                not null comment '主键' primary key,
    annualPlanId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '年度计划id',
    auditTime    datetime                                                   not null comment '审核时间',
    attendee     varchar(2000)                                              not null comment '审核参与人员',
    auditPurp    varchar(2000)                                              not null comment '审核目的',
    auditScope   varchar(2000)                                              not null comment '审核范围',
    auditContent varchar(2000)                                              not null comment '审核内容',
    auditGist    varchar(2000)                                              not null comment '审核依据',
    status       varchar(50)                                                null comment '状态',
    isDeleted    bit         default b'0'                                   not null comment '是否删除',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '内审计划信息' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_InternalAuditPlanReport
(
    id             varchar(50)                                                not null comment '主键' primary key,
    internalPlanId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '内审计划id',
    makerId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制人id',
    makerName      varchar(50)                                                null comment '编制人名字',
    makerTime      datetime                                                   not null comment '编制日期',
    auditReview    varchar(2000)                                              not null comment '审核综述',
    auditResult    varchar(2000)                                              not null comment '审核结论',
    isDeleted      bit         default b'0'                                   not null comment '是否删除',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '内审报告信息' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_InternalCheckInfo
(
    id              varchar(50)                                                not null comment '主键' primary key,
    implementPlanId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '内审的实施计划id',
    checkContent    varchar(2000)                                              not null comment '检查项的内容',
    auditor         varchar(50)                                                not null comment '审核人员',
    checkResult     int         default 0                                      not null comment '检查结果（1：符合、2：基本符合、3：不符合、4：不适用）',
    remark          varchar(2000)                                              null comment '备注信息',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    isDeleted       bit         default b'0'                                   not null comment '是否删除',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '检查项的信息' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_Log
(
    id               varchar(50)                                                not null comment '主键' primary key,
    operatorId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作者Id',
    operatorName     varchar(50)                                                null comment '操作者名字',
    operateTime      datetime                                                   not null comment '操作时间',
    operateInfo      varchar(50)                                                not null comment '操作类型(EnumOperationInfo, 1: 新增， 2: 审核)',
    nextOperatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '下一步操作人Id',
    nextOperatorName varchar(50)                                                null comment '下一步操作人名字',
    logType          varchar(50)                                                not null comment '日志类型',
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '对象id',
    objectType       varchar(50)                                                not null comment '对象类型',
    comment          mediumtext                                                 null comment '说明',
    opinion          varchar(1000)                                              null comment '意见',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) comment '年度计划日志表' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_QA_ManagementReviewPlan
(
    id                    varchar(50)                                                not null comment '主键' primary key,
    annualPlanId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '年度计划id',
    reviewPurp            varchar(2000)                                              not null comment '评审目的',
    attendee              varchar(2000)                                              not null comment '参加人员',
    reviewContent         varchar(2000)                                              not null comment '评审内容',
    reviewPrepareRequired varchar(2000)                                              null comment '评审准备工作要求(评审主要议程)',
    reviewTime            datetime                                                   not null comment '评审时间',
    reviewAddr            varchar(255)                                               not null comment '评审地点',
    host                  varchar(50)                                                not null comment '主持人',
    recorder              varchar(50)                                                null comment '记录人',
    status                varchar(50)                                                not null comment '评审状态',
    isDeleted             bit         default b'0'                                   not null comment '是否删除',
    orgId                 varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate            datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate            datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '管理评审' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_QA_MonitoringPlan
(
    id         varchar(50)                                                not null comment '主键id' primary key,
    marker     varchar(50)                                                not null comment '制定人',
    markDate   datetime                                                   not null comment '制定日期',
    planName   varchar(255)                                               null comment '计划名称',
    status     varchar(50)                                                null comment '计划状态（计划编制中、审核不通过、计划审核中、质量监督中、已办结）',
    isDeleted  bit         default b'0'                                   not null comment '是否删除',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '质量监督计划' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_MonitoringPlanCheckInfo
(
    id             varchar(50)   not null comment '主键' primary key,
    planDetailId   varchar(50)   not null default '00000000-0000-0000-0000-000000000000' comment '质量监督明细',
    checkContent   varchar(2000) null comment '检查项的内容',
    checkResult    varchar(2000) not null comment '检查结果',
    checkDate      datetime      not null comment '检查时间',
    checker        varchar(50)   not null comment '检查人员Id',
    checkName      varchar(255)  null comment '检查人员Name',
    dutyPersonId   varchar(50)   not null default '00000000-0000-0000-0000-000000000000' comment '责任人',
    dutyPersonName varchar(50)   null comment '责任人名称',
    dutyDept       varchar(255)  null comment '责任部门'
) comment '质量计划的检查项' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;

create table TB_QA_MonitoringPlanDetail
(
    id           varchar(50)                                                not null comment '主键id' primary key,
    planId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计划id',
    dutyPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '责任人',
    dutyDept     varchar(255)                                               null comment '责任部门',
    status       varchar(50)                                                not null comment '状态 （0：未提交，2：已提交）',
    content      varchar(2000)                                              null comment '监督内容',
    isDeleted    bit         default b'0'                                   not null comment '是否删除',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '质量监督明细' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_NotConformItem
(
    id               varchar(50)                                                not null comment 'id' primary key,
    sourceId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '来源id（常量（GUID）：QA_Source）',
    ncProduceDept    varchar(50)                                                not null comment '不符合项责任科室',
    ncDeptPerson     varchar(50)                                                null comment '不符合项科室主任',
    ncDeptPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '不符合项科室主任Id',
    ncDescribe       varchar(2000)                                              null comment '不符合项描述',
    ncSourceType     varchar(50)                                                null comment '来源类型（常量（GUID）：QA_SourceType）',
    ncMainPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '不符合项主要负责人Id',
    ncMainPerson     varchar(50)                                                null comment '不符合项主要负责人',
    ncFindPerson     varchar(50)                                                null comment '不符合项发现人员',
    ncFindPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '不符合项发现人员Id',
    ncFindDate       datetime    default '1753-01-01 00:00:00'                  not null comment '不符合项发现日期',
    ncBasis          varchar(500)                                               not null comment '不符合项发现依据',
    ncType           varchar(50)                                                null comment '不符合项类型',
    ncCauseAnalysis  varchar(500)                                               null comment '不符合项原因分析',
    ncElement        varchar(500)                                               null comment '不符合要素',
    ncNature         varchar(500)                                               null comment '不符合项性质',
    ncItermNum       varchar(1000)                                              null comment '不符合项涉及条款号',
    ncIterm          varchar(2000)                                              null comment '不符合项涉及条款内容',
    correctMeasures  varchar(2000)                                              null comment '拟采取的纠正措施',
    expectFinishDate datetime    default '1753-01-01 00:00:00'                  not null comment '预计完成日期',
    finishDate       datetime    default '1753-01-01 00:00:00'                  not null comment '纠正措施完成日期',
    complete         varchar(2000)                                              null comment '确认纠正措施完成情况',
    effective        int         default -1                                     not null comment '验证纠正措施是否有效  0：无效，1：有效 ，2：未验证',
    verifierEvaluate varchar(2000)                                              null comment '验证及评价',
    potential        int         default 0                                      not null comment '是否是潜在问题（0：不符合项，1：潜在不符合项及预防措施）',
    restoreWork      int         default 0                                      not null comment '可否恢复工作',
    notifyCustomer   int         default 0                                      not null comment '是否通知客户',
    correcteAction   int         default 0                                      not null comment '是否需要采取纠正措施',
    status           varchar(50)                                                null comment '状态',
    isDeleted        bit         default b'0'                                   null comment '是否删除',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    verifierDate     datetime                                                   null comment '验证时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_QA_ReviewPlanReport
(
    id             varchar(50)                                                not null comment '主键' primary key,
    reviewPlanId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '管理评审id',
    makerId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制报告人id',
    makerName      varchar(50)                                                null comment '编制报告人名字',
    makeDate       datetime                                                   not null comment '报告编制日期',
    reviewPurp     varchar(2000)                                              not null comment '评审目的',
    reviewContent  varchar(2000)                                              not null comment '评审内容',
    reviewGist     varchar(2000)                                              not null comment '评审依据',
    reviewExpound  varchar(2000)                                              not null comment '评审阐述',
    reviewDecision varchar(2000)                                              not null comment '评审决议',
    isDeleted      bit         default b'0'                                   not null comment '是否删除',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '管理评审报告' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;

create table TB_QA_RiskAndAccident
(
    id               varchar(50)                                                not null comment '主键' primary key,
    discoverDate     datetime                                                   not null comment '发现日期',
    dutyDomainId     varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '责任科室',
    sourceType       varchar(50)                                                null comment '来源类型（常量：实验室内审、管理评审、质量监督、客户投诉、其他）',
    possibility      int         default 0                                      not null comment '风险机遇可能性（枚举）',
    seriousness      int         default 0                                      not null comment '风险机遇严重性（枚举）',
    directorId       varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '科室主任',
    dutyPersonId     varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '责任人',
    finderId         varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '发现人员',
    measure          varchar(500)                                               null comment '措施拟定',
    studyOutPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '拟定人',
    auditor          varchar(50)                                                null comment '审核人',
    affirmPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '确认人',
    achieveDate      datetime    default '1753-01-01 00:00:00'                  not null comment '完成日期',
    performance      varchar(500)                                               null comment '完成情况',
    affirmDate       datetime    default '1753-01-01 00:00:00'                  not null comment '确认日期',
    affirmEvaluate   varchar(500)                                               null comment '确认及评价',
    findDate         datetime    default '1753-01-01 00:00:00'                  not null comment '发现日期',
    coefficient      varchar(50)                                                null comment '风险机遇系数',
    description      varchar(500)                                               null comment '风险机遇描述',
    reason           varchar(500)                                               null comment '原因分析',
    status           varchar(50)                                                not null comment '评审状态',
    isDeleted        bit         default b'0'                                   not null comment '是否删除',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '风险与机遇' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_QA_SubmitRecord
(
    id               varchar(50)                                                not null comment '主键' primary key,
    objectId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联Id',
    objectType       int         default 0                                      not null comment '关联对象类型（枚举EnumQAObjType：1. 年度计划....）',
    submitType       int         default 0                                      not null comment '操作类型（枚举EnumQASubmitType：0.无 1.年度计划提交 ....）',
    submitTime       datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    submitPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作人',
    submitPersonName varchar(50)                                                null comment '操作人名称',
    nextPerson       varchar(200)                                               null comment '下一步操作人（名字）',
    submitRemark     varchar(200)                                               null comment '操作意见',
    stateFrom        varchar(50)                                                null comment '操作前状态',
    stateTo          varchar(50)                                                null comment '操作后状态',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    isNewest         bit         default b'1'                                   null
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

