DROP PROCEDURE IF EXISTS `add_col`;
<PERSON><PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_Test' AND column_name = ('airPollution'))) THEN
    ALTER TABLE TB_LIM_Test ADD COLUMN airPollution varchar(100) NULL DEFAULT '' COMMENT '空气污染物';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;