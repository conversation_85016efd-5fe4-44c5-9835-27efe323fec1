-- 流量校准记录表
CREATE TABLE TB_PRO_FlowCalibration
(
    id           varchar(50) NOT NULL COMMENT 'id',
    instrumentId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器id',
    calibrationDate    datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '校准时间',
    calibrationPeople      varchar(1000) NOT NULL DEFAULT '' COMMENT '校准人id，多个英文逗号拼接',
    calibrationType      int(10)  COMMENT '校准类型',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删字段',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流量校准记录表';
-- 流量校准数据表
CREATE TABLE TB_PRO_FlowCalibrationRow
(
    id           varchar(50) NOT NULL COMMENT 'id',
    flowCalibrationId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '校准记录id',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删字段',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流量校准数据表';
-- 流量校准数据参数表
CREATE TABLE TB_PRO_FlowCalibrationParamData
(
    id           varchar(50) NOT NULL COMMENT 'id',
    flowCalibrationRowId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '校准数据id',
    paramName    varchar(50) NOT NULL COMMENT '参数名称',
    paramValue   varchar(50) COMMENT '参数值',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删字段',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流量校准数据参数表';