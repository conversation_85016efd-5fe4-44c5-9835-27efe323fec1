-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关初始化数据脚本 -------
-- ------ TB_LIM_SerialIdentifierConfig ---------
-- ----------------------------------------------

INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('00f00ba8-6849-4cb4-808c-01759c39b489', null, '标样编号规则', 3, '[workSheet.code]BY[sn1-BY[workSheet.code]]', 8, 2, 65, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2020-05-06 16:29:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:22:00', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('0e581e5b-2048-43a3-858c-92f15ba1e27e', null, '室内空白编号规则', 3, '[workSheet.code]KB[sn1-kb[workSheet.code]]', 2, 2, 80, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2020-04-09 14:00:17', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 13:16:26', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('34727d35-d5f4-4698-a229-cf37cd03be94', null, '监测报告', 5, '环监（[reportType.year]）监字第[sn4-Report[reportType.year]]号', 0, 0, 100, '', '5f7bcf90feb545968424b0a872863876', false, 'fbe37a04742241d88dfec41f3e3762c8', '2019-11-06 08:24:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-11-14 17:13:21', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('38909540-6d60-4a58-a300-8e07eebc88e6', null, '室内平行编号规则', 3, '[sample.associateSampleCode]PX[sn1-px[sample.associateSampleCode]]', 1, 2, 68, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2020-03-04 13:24:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:18:27', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('3906ae34-2177-45ee-ae76-68b42a2343e4', '', '空白加标样编号规则', 3, '[sample.associateSampleCode]KBJB[sn1-[sample.associateSampleCode]]', 131072, 2, 0, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2022-08-01 09:40:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-01 09:42:01', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('44fe3396-86ce-4eb6-8e3c-b7afb1da347d', '', '阴性对照试验编号规则', 3, '[workSheet.code]YIN[sn1-YIN[workSheet.code]]', 16384, 2, 0, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-11-12 10:02:04', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-11-12 10:03:37', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('4d6eb7f6-b3d7-4c1f-8571-391053fb9262', null, '检测报告', 5, '环监（[reportType.year]）检字第[sn4-Report[reportType.year]]号', 0, 0, 90, '', '5f7bcf90feb545968424b0a872863876', false, 'fbe37a04742241d88dfec41f3e3762c8', '2019-11-06 08:09:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-11-14 17:13:26', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('55aac6f9-5a52-404a-83f8-73c782e20cb8', '', '校正系数检验编号规则', 3, '[workSheet.code]JZ[sn1-kb[workSheet.code]]', 4096, 2, 70, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:17:17', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:17:30', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('6c08f6af-72e9-46b2-a7f5-0e1ec2bd27c6', '', '运输空白编号规则', 3, '[sampleType.shortName][sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sn3-Sample[sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sampleType.shortName]]', 256, 1, 88, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:12:30', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-06 08:33:17', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('6C537734-6F25-4EBF-9CF7-E28C76C16B2B', '005', '辐射报告', 5, '环监（[reportType.year]）辐监字第[sn4-Report[reportType.year]]号', 0, 0, 80, '', '5f7bcf90feb545968424b0a872863876', false, '00000000-0000-0000-0000-000000000000', '2019-10-25 15:08:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-11-14 17:13:31', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('71E0CD85-52F3-43C4-B976-7348DBD94D4A', '7', '工作单号', 6, 'W[time(yy)][sn5-WORKSHEET[time(yy)]]', 0, 0, 100, '', '5f7bcf90feb545968424b0a872863876', false, '00000000-0000-0000-0000-000000000000', '2019-10-25 15:08:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-18 13:41:35', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('80fb6de1-d21f-475a-8d37-523a0985f3d8', '', '试剂空白编号规则', 3, '[workSheet.code]SJ[sn1-kb[workSheet.code]]', 1024, 2, 75, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-10-27 13:17:22', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:15:32', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('9A511912-353B-4687-9E4C-3A674C73C427', '003', '现场平行样编号', 3, '[sampleType.shortName][sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sn3-Sample[sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sampleType.shortName]]', 1, 1, 100, '', '5f7bcf90feb545968424b0a872863876', false, '00000000-0000-0000-0000-000000000000', '1753-01-01 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 13:12:45', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('AED909CD-6AB4-4284-8B79-7B70D9AD5C64', '001', '样品编号', 2, '[sampleType.shortName][sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sn3-Sample[sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sampleType.shortName]]', 0, 0, 100, '', '5f7bcf90feb545968424b0a872863876', false, '00000000-0000-0000-0000-000000000000', '1753-01-01 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-26 08:35:15', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('bbb3b90e-cce0-46a1-acdf-71baf5d23c5c', null, '全程序空白编号规则', 3, '[sampleType.shortName][sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sn3-Sample[sample.samplingTimeBegin(yy)][sample.samplingTimeBegin(MM)][sample.samplingTimeBegin(dd)][sampleType.shortName]]', 2, 1, 90, '全程序空白样', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '1753-01-01 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:12:48', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('c1579af8-0e98-46d0-8abe-ec41ecb088c4', null, '原样加原样规则', 3, '[sample.associateSampleCode] ', 16, 2, 0, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2020-01-10 16:08:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:23:41', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('C2BB03F3-7C8A-4558-9D48-C335044D0716', '004', '项目编号', 1, '[projectType.mark][project.inceptTime(yy)][project.inceptTime(MM)][sn3-PROJECT[projectType.mark][project.inceptTime(yy)][project.inceptTime(MM)]]', 0, 0, 100, '', '5f7bcf90feb545968424b0a872863876', false, '00000000-0000-0000-0000-000000000000', '2019-10-25 15:08:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-11-13 09:34:55', '5a171aec-e30f-4191-9580-2318b55d63de,6f5f9f23-b31f-4ade-9b5b-9eae02da100b,d2b8c15e-485e-423e-9d00-a9c0b2dacfc9,58a35737-6d3a-4f37-84b4-682fe11ece91,b527970f-604d-4266-a763-a3594bac7e33,93cbe1f2-493f-4e95-ab61-ccb97eb33ec2,976c3620-91ce-45a7-bfa4-2847c39afcc7,cdce6d5f-1b70-4e32-8d52-cf85ae80bd21,ff63d6de-8904-49fc-b8ae-d8124864dc2a,38872452-fe87-4dde-8da2-bf954f13ed02,432538b0-7f30-4441-81d6-da441dfa4638,5487b146-af5e-42e9-a00b-378324d5993e');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('c3d89711-ecfe-4299-887b-964dd6bc031d', null, '串联样编号规则', 3, '[sample.associateSampleCode]C[sn1-cl[sample.associateSampleCode]]', 32, 1, 85, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '1753-01-01 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 15:05:50', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('caddb7bb-aaea-4304-a409-f8d9a3667bee', null, '加标样编号规则', 3, '[sample.associateSampleCode]JB[sn1-jb[sample.associateSampleCode]]', 4, 2, 63, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2020-03-04 13:25:04', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:19:39', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('D40DB5CB-7478-42F3-832E-3AC3C2D95DC0', '6', '送样单编号', 4, '[time(yy)][time(MM)][time(dd)][sn2-RECEIVESAMPLERECORD[time(yy)][time(MM)][time(dd)]]', 0, 0, 100, '', '5f7bcf90feb545968424b0a872863876', false, '00000000-0000-0000-0000-000000000000', '2019-10-25 15:08:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-18 13:41:31', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('d65006ec-45b8-41ca-b2de-0100b665ec73', '', '罐空白编号规则', 3, '[workSheet.code]G[sn1-kb[workSheet.code]]', 2048, 2, 73, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:16:07', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:16:17', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('dbc0b5a2-fc25-4983-9933-179386cd554a', null, '质控任务编号', 7, '[projectType.mark][project.inceptTime(yy)][project.inceptTime(MM)][project.inceptTime(dd)][sn2-PROJECT[projectType.mark][project.inceptTime(yy)][project.inceptTime(MM)][project.inceptTime(dd)]]', 0, 0, 0, '121', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2020-04-07 13:37:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-18 13:41:37', null);
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('dd4b1a0a-926c-491b-ae66-9aec43004dc0', '', '阳性对照试验编号规则', 3, '[workSheet.code]YANG[sn1-YANG[workSheet.code]]', 32768, 2, 0, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-11-12 10:02:21', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-11-12 10:04:07', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('e0cb3179-0daf-48c6-95aa-a9f4cd082f81', '', '替代样编号规则', 3, '[workSheet.code]TD[sn1-BY[workSheet.code]]', 8192, 2, 58, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:21:30', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:21:54', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('fc8bf7bb-e035-448e-bfad-da3f11914feb', null, '曲线校核样编号规则', 3, '[workSheet.code]JH[sn1-kb[workSheet.code]]', 64, 2, 60, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-09-29 10:35:11', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:20:35', '00000000-0000-0000-0000-000000000000');
INSERT INTO TB_LIM_SerialIdentifierConfig (id, configCode, configName, configType, configRule, qcType, qcGrade, orderNum, remark, orgId, isDeleted, creator, createDate, domainId, modifier, modifyDate, projectTypeId) VALUES ('fff2b0d6-4c9a-4371-972e-c814e272666e', '', '仪器空白编号规则', 3, '[workSheet.code]YQ[sn1-kb[workSheet.code]]', 512, 2, 78, '', '5f7bcf90feb545968424b0a872863876', false, '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:13:42', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-10-27 14:14:53', '00000000-0000-0000-0000-000000000000');
