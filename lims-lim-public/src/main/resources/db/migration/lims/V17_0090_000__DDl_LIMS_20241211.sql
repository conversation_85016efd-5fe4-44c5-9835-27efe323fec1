DROP TABLE IF EXISTS TB_PRO_LocalTaskPeopleCompare;
CREATE TABLE TB_PRO_LocalTaskPeopleCompare
(
    id             varchar(50) NOT NULL COMMENT '主键',
    leaderId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '考核负责人标识',
    checkPeopleId  varchar(500)  NOT NULL DEFAULT '' COMMENT '考核人员标识,多个英文逗号拼接',
    checkDate      datetime DEFAULT '1753-01-01 00:00:00' NOT NULL COMMENT '考核日期',
    receiveId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '送样单标识',
    projectId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目标识',
    PRIMARY KEY (`id`)
) COMMENT '现场质控任务人员比对配置表';