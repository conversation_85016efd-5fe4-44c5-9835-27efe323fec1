-- tb_lim_paramsformula
drop trigger if exists after_insert_paramsformula;
DELIMITER $$
CREATE TRIGGER after_insert_paramsformula
    AFTER INSERT
    ON tb_lim_paramsformula
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId,
            new.domainId);
end $$
DELIMITER ;

drop trigger if exists after_update_paramsformula;
DELIMITER $$
CREATE TRIGGER after_update_paramsformula
    AFTER UPDATE
    ON tb_lim_paramsformula
    FOR EACH ROW
BEGIN
    IF
        new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
        if new.objectId != old.objectId or (new.objectId is null and old.objectId is not null) or
           (new.objectId is not null and old.objectId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'objectId', old.objectId,
                    new.objectId, new.orgId, new.domainId);
        END IF;
        if
                new.formula != old.formula or (new.formula is null and old.formula is not null) or
                (new.formula is not null and old.formula is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'formula', old.formula,
                    new.formula, new.orgId, new.domainId);
        END IF;
        if
                new.configDate != old.configDate or (new.configDate is null and old.configDate is not null) or
                (new.configDate is not null and old.configDate is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'configDate',
                    old.configDate, new.configDate, new.orgId, new.domainId);
        END IF;
        if
                new.orignFormula != old.orignFormula or (new.orignFormula is null and old.orignFormula is not null) or
                (new.orignFormula is not null and old.orignFormula is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'orignFormula',
                    old.orignFormula, new.orignFormula, new.orgId, new.domainId);
        END IF;
        if
                new.orignFormulatType != old.orignFormulatType or
                (new.orignFormulatType is null and old.orignFormulatType is not null) or
                (new.orignFormulatType is not null and old.orignFormulatType is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'orignFormulatType',
                    old.orignFormulatType, new.orignFormulatType, new.orgId, new.domainId);
        END IF;
        if
                new.objectType != old.objectType or (new.objectType is null and old.objectType is not null) or
                (new.objectType is not null and old.objectType is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'objectType',
                    old.objectType, new.objectType, new.orgId, new.domainId);
        END IF;
        if
                new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
                (new.sampleTypeId is not null and old.sampleTypeId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'sampleTypeId',
                    old.sampleTypeId, new.sampleTypeId, new.orgId, new.domainId);
        END IF;
        if
                new.validate != old.validate or (new.validate is null and old.validate is not null) or
                (new.validate is not null and old.validate is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'validate', old.validate,
                    new.validate, new.orgId, new.domainId);
        END IF;
        if
                new.usageNum != old.usageNum or (new.usageNum is null and old.usageNum is not null) or
                (new.usageNum is not null and old.usageNum is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'usageNum', old.usageNum,
                    new.usageNum, new.orgId, new.domainId);
        END IF;
    END IF;
end $$
DELIMITER ;

-- tb_lim_paramstestformula
drop trigger if exists after_insert_paramstestformula;
DELIMITER $$
CREATE TRIGGER after_insert_paramstestformula
    AFTER INSERT
    ON tb_lim_paramstestformula
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null,
            null, new.orgId, '00000000-0000-0000-0000-000000000000');
end $$
DELIMITER ;

drop trigger if exists after_update_paramstestformula;
DELIMITER $$
CREATE TRIGGER after_update_paramstestformula
    AFTER UPDATE
    ON tb_lim_paramstestformula
    FOR EACH ROW
BEGIN
    if new.objId != old.objId or (new.objId is null and old.objId is not null) or
       (new.objId is not null and old.objId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'objId',
                old.objId, new.objId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.paramsId != old.paramsId or (new.paramsId is null and old.paramsId is not null) or
            (new.paramsId is not null and old.paramsId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'paramsId', old.paramsId, new.paramsId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.paramsName != old.paramsName or (new.paramsName is null and old.paramsName is not null) or
            (new.paramsName is not null and old.paramsName is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'paramsName', old.paramsName, new.paramsName, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.alias != old.alias or (new.alias is null and old.alias is not null) or
            (new.alias is not null and old.alias is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'alias',
                old.alias, new.alias, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.defaultValue != old.defaultValue or (new.defaultValue is null and old.defaultValue is not null) or
            (new.defaultValue is not null and old.defaultValue is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'defaultValue', old.defaultValue, new.defaultValue, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
            (new.orderNum is not null and old.orderNum is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'orderNum', old.orderNum, new.orderNum, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.aliasInReport != old.aliasInReport or (new.aliasInReport is null and old.aliasInReport is not null) or
            (new.aliasInReport is not null and old.aliasInReport is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'aliasInReport', old.aliasInReport, new.aliasInReport, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.dimensionId != old.dimensionId or (new.dimensionId is null and old.dimensionId is not null) or
            (new.dimensionId is not null and old.dimensionId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'dimensionId', old.dimensionId, new.dimensionId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.dimension != old.dimension or (new.dimension is null and old.dimension is not null) or
            (new.dimension is not null and old.dimension is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'dimension', old.dimension, new.dimension, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.sourceType != old.sourceType or (new.sourceType is null and old.sourceType is not null) or
            (new.sourceType is not null and old.sourceType is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'sourceType', old.sourceType, new.sourceType, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.isMust != old.isMust or (new.isMust is null and old.isMust is not null) or
            (new.isMust is not null and old.isMust is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'isMust',
                old.isMust, new.isMust, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.isEditable != old.isEditable or (new.isEditable is null and old.isEditable is not null) or
            (new.isEditable is not null and old.isEditable is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'isEditable', old.isEditable, new.isEditable, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.detectionLimit != old.detectionLimit or
            (new.detectionLimit is null and old.detectionLimit is not null) or
            (new.detectionLimit is not null and old.detectionLimit is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'detectionLimit', old.detectionLimit, new.detectionLimit, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.calculationMode != old.calculationMode or
            (new.calculationMode is null and old.calculationMode is not null) or
            (new.calculationMode is not null and old.calculationMode is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'calculationMode', old.calculationMode, new.calculationMode, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
end $$
DELIMITER ;

-- tb_lim_paramspartformula
drop trigger if exists after_insert_paramspartformula;
DELIMITER $$
CREATE TRIGGER after_insert_paramspartformula
    AFTER INSERT
    ON tb_lim_paramspartformula
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null,
            null, new.orgId, '00000000-0000-0000-0000-000000000000');
end $$
DELIMITER ;

drop trigger if exists after_update_paramspartformula;
DELIMITER $$
CREATE TRIGGER after_update_paramspartformula
    AFTER UPDATE
    ON tb_lim_paramspartformula
    FOR EACH ROW
begin
    if new.formulaId != old.formulaId or (new.formulaId is null and old.formulaId is not null) or
       (new.formulaId is not null and old.formulaId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'formulaId', old.formulaId, new.formulaId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.formula != old.formula or (new.formula is null and old.formula is not null) or
            (new.formula is not null and old.formula is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'formula',
                old.formula, new.formula, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.paramsName != old.paramsName or (new.paramsName is null and old.paramsName is not null) or
            (new.paramsName is not null and old.paramsName is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'paramsName', old.paramsName, new.paramsName, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.mostSignificance != old.mostSignificance or
            (new.mostSignificance is null and old.mostSignificance is not null) or
            (new.mostSignificance is not null and old.mostSignificance is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'mostSignificance', old.mostSignificance, new.mostSignificance, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.mostDecimal != old.mostDecimal or (new.mostDecimal is null and old.mostDecimal is not null) or
            (new.mostDecimal is not null and old.mostDecimal is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'mostDecimal', old.mostDecimal, new.mostDecimal, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
            (new.orderNum is not null and old.orderNum is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'orderNum', old.orderNum, new.orderNum, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.detectionLimit != old.detectionLimit or
            (new.detectionLimit is null and old.detectionLimit is not null) or
            (new.detectionLimit is not null and old.detectionLimit is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'detectionLimit', old.detectionLimit, new.detectionLimit, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.calculationMode != old.calculationMode or
            (new.calculationMode is null and old.calculationMode is not null) or
            (new.calculationMode is not null and old.calculationMode is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'calculationMode', old.calculationMode, new.calculationMode, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if
            new.useTestLimit != old.useTestLimit or (new.useTestLimit is null and old.useTestLimit is not null) or
            (new.useTestLimit is not null and old.useTestLimit is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'useTestLimit', old.useTestLimit, new.useTestLimit, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
end $$
DELIMITER ;