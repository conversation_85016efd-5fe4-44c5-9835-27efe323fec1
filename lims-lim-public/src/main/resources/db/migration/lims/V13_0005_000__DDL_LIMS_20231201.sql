ALTER TABLE TB_PRO_OtherDetail
    ADD COLUMN formula varchar(1000) NULL DEFAULT '' COMMENT '计算公式';

ALTER TABLE TB_PRO_OrderForm
    ADD COLUMN inspectedEntId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '受检单位Id',
ADD COLUMN areaId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '行政区域Id',
ADD COLUMN inspectedEnt varchar(100) NULL DEFAULT '' COMMENT '受检单位',
ADD COLUMN inspectedLinkMan varchar(50) NULL DEFAULT '' COMMENT '受检方联系人',
ADD COLUMN inspectedLinkPhone varchar(50) NULL DEFAULT '' COMMENT '受检方联系电话',
ADD COLUMN inspectedAddress varchar(100) NULL DEFAULT '' COMMENT '受检方地址';

CREATE TABLE TB_PRO_LogForOrderForm
(
    id               varchar(50) NOT NULL COMMENT 'id',
    operatorId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '操作者Id',
    operatorName     varchar(50) NULL DEFAULT '' COMMENT '操作者名字',
    operateTime      datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '操作时间',
    operateInfo      varchar(500) NULL DEFAULT '' COMMENT '操作类型（新建、保存、修改等）',
    nextOperatorId   varchar(50) NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '下一步操作人Id',
    nextOperatorName varchar(50) NULL DEFAULT '' COMMENT '下一步操作人名字',
    logType          int(11) NOT NULL DEFAULT 0 COMMENT '日志类型（如项目的方案、合同，样品的信息、检测项目）',
    objectId         varchar(50) NOT NULL DEFAULT '' COMMENT '对象id',
    objectType       int(11) NOT NULL DEFAULT 0 COMMENT '对象类型（工作单、项目、数据等）',
    comment          mediumtext NULL COMMENT '说明',
    opinion          varchar(1000) NULL DEFAULT '' COMMENT '意见（评审意见等）',
    remark           varchar(1000) NULL DEFAULT '' COMMENT '备注',
    orgId            varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    PRIMARY KEY (id),
    INDEX            IX_TB_PRO_LogForOrderForm(objectId, orgId) USING BTREE
) COMMENT = '订单日志';