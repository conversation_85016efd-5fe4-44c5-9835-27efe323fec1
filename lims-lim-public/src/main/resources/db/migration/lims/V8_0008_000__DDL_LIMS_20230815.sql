CREATE TABLE TB_PRO_AutoTaskPlan
(
    id              varchar(50) NOT NULL COMMENT '主键',
    taskName        varchar(50) NOT NULL DEFAULT '' COMMENT '计划名称',
    taskCode        varchar(50) NOT NULL DEFAULT '' COMMENT '计划编码',
    dealCycle       int(11) NOT NULL DEFAULT 0 COMMENT '执行周期(EnumDealCycle)',
    dealNum         int(11) NOT NULL DEFAULT 0 COMMENT '执行数量',
    dealDate        varchar(50) NULL DEFAULT '' COMMENT '每月执行日期',
    projectCodeRule int(11) NOT NULL DEFAULT 0  COMMENT '项目编号 1-自动生成 2-手动生成(EnumCodeGenerateRule)',
    inputPersonId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '登记人',
    leaderId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '负责人',
    isStress        bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否着重关注',
    grade           int(11) NOT NULL DEFAULT 0 COMMENT '项目登记(EnumProjectGrade：0.一般 1.紧急 2.特急)',
    month           varchar(100) NULL DEFAULT '' COMMENT '应用月份(\",\"隔开)',
    orgId           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    domainId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    creator         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

CREATE TABLE TB_PRO_Task2FixedProperty
(
    id              varchar(50) NOT NULL COMMENT '主键',
    taskId          varchar(50) NOT NULL COMMENT '任务id',
    fixedPropertyId varchar(50) NOT NULL COMMENT '断面属性id',
    PRIMARY KEY (id)
);