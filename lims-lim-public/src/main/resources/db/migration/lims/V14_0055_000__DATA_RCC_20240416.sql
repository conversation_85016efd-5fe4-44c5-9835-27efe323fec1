-- pH现场分析原始记录单模板应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('4477a4cb-c354-4737-bb29-cc8983a020ba', '7d121986-3317-44cd-bb0b-f9a81261694b', 'PrepareSample', '采样准备', '采样单',
        'pH现场分析原始记录单', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-15 15:11:31', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-15 15:11:31', 1);
 
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('945e5583-5dd0-4d74-b487-2294e94c502c', '7d121986-3317-44cd-bb0b-f9a81261694b', 'LocalTask', '现场任务', '采样单',
        'pH现场分析原始记录单', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-15 15:12:55', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-15 15:12:55', NULL);