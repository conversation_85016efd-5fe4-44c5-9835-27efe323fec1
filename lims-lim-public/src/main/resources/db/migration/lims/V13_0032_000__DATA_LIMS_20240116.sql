delete from tb_lim_reportconfig where id ='29489e4c-34da-464e-8398-ed50d911cd11';
INSERT INTO tb_lim_reportconfig
(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES('29489e4c-34da-464e-8398-ed50d911cd11', 1, 'ConsumablePutIn', '消耗品入库清单_模板.xlsx', '/LIMReportForms/消耗品入库清单_模板.xlsx', 'output/LIMReportForms/消耗品入库清单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.ConsumablePutInExportService', '{"sort":"consumableName+specification"}', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-15 18:50:35', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-16 09:38:53', 'com.sinoyd.base.criteria.ConsumableCriteria', 'LIMReportForms', 'export/ConsumablePutIn', 0, '', NULL, '', '', '', 0, NULL);

delete from tb_lim_reportconfig where id ='9f456064-ba3a-4a2a-8165-ef4bebd022e2';
INSERT INTO tb_lim_reportconfig
(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES('9f456064-ba3a-4a2a-8165-ef4bebd022e2', 1, 'StandardPutIn', '标准物质入库清单_模板.xlsx', '/LIMReportForms/标准物质入库清单_模板.xlsx', 'output/LIMReportForms/标准物质入库清单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.StandardPutInExportService', '{"sort":"consumableName+specification"}', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-16 09:21:07', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-16 09:39:05', 'com.sinoyd.base.criteria.ConsumableCriteria', 'LIMReportForms', 'export/StandardPutIn', 0, '', NULL, '', '', '', 0, NULL);