-- 试剂配置导出报表应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('c53556e0-c159-42ed-9644-5f31556ae426', 'ba8e7106-c54e-413f-be35-6e69569277b5', 'AnalyzeMethodReagentConfig',
        '试剂配制记录', 'ReagentConfig', '生成一般试剂配置记录表', 0, 0, 1, '', '试剂配制记录:试剂配置记录', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-08-02 09:32:59', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-08-02 09:32:59');


INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('d5ecc8d7-2b4f-41b5-b4e7-d2ed9e0aeedb', 'ba8e7106-c54e-413f-be35-6e69569277b5', 'AnalyzeMethodReagentConfig',
        '试剂配制记录', 'ReagentConfig', '生成一般试剂配制记录表', 0, 0, 1, '', '试剂配制记录:分析方法列表', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-08-01 16:32:02', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-08-01 16:32:02');