CREATE TABLE TB_LIM_SamplingMethod
(
    id           varchar(50) NOT NULL COMMENT 'id',
    sampleTypeId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    methodName   varchar(255) NULL DEFAULT '' COMMENT '采样方法名称',
    alias        varchar(100) NULL DEFAULT '' COMMENT '别名',
    standardCode varchar(100) NULL DEFAULT '' COMMENT '标准编号',
    applyDate    datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '实施日期',
    status       int(11) NOT NULL DEFAULT -1 COMMENT '启用状态',
    remark       varchar(1000) NULL DEFAULT '' COMMENT '备注',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删标志',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

CREATE TABLE TB_PRO_Arrange2Method
(
    id               varchar(50) NOT NULL COMMENT 'id',
    samplingPlanId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样计划id',
    samplingMethodId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样方法id',
    sampleTypeId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    methodName       varchar(255) NULL DEFAULT '' COMMENT '采样方法名称',
    alias            varchar(100) NULL DEFAULT '' COMMENT '别名',
    standardCode     varchar(100) NULL DEFAULT '' COMMENT '标准编号',
    orgId            varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate       datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate       datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

ALTER TABLE TB_PRO_SamplingArrange
    ADD COLUMN kbFlag bit(1) NULL DEFAULT NULL COMMENT '空白样',
    ADD COLUMN pxFlag bit(1) NULL DEFAULT NULL COMMENT '平行样',
    ADD COLUMN status varchar(50) NULL DEFAULT '' COMMENT '状态',
    ADD COLUMN samplingPlanId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样计划id';