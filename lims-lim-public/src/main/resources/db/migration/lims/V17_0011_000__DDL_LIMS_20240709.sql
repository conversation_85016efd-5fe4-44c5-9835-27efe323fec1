-- 更新项目统计视图
DROP VIEW IF EXISTS VI_PRO_ProjectCountStatisticView;
create view VI_PRO_ProjectCountStatisticView as
select uuid()              AS id,
       p.id                AS projectId,
       pt.id               AS projectTypeId,
       pt.name             AS projectTypeName,
       ptp.name            AS parentName,
       p.inceptTime        AS inceptTime,
       s.samplingTimeBegin AS samplingTime,
       p.orgId             AS orgId
from TB_PRO_Project p
         join TB_LIM_ProjectType pt on p.isDeleted = 0 and p.projectTypeId = pt.id
    and (p.parentId <> '00000000-0000-0000-0000-000000000000' or p.projectTypeId <> '5a171aec-e30f-4191-9580-2318b55d63de' )
         left join TB_LIM_ProjectType ptp on ptp.id = pt.parentId
         left join TB_PRO_Sample s on s.projectId = p.id;
