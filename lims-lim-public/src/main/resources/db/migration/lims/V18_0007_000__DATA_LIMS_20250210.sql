-- 非道路移动柴油机械报告报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                               returnType, method, params, pageConfig, orderNum, bizType,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, dataMethod, typeCode, strUrl,
                                               isDefineFileName, defineFileName, beanName, versionNum,
                                               controlNum, reportName, validate, usageNum)
VALUES ('a7b2a5e1-94ac-4984-8e46-9eb710eb143a', 1, 'FdlStd', '标准版非道路移动机械监测报告.doc', 'Report/标准版报告.doc',
        'output/Report/非道路移动机械监测报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '非道路移动机械监测报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-08 14:12:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-08 14:12:01', 'reportId,sortId', 'Report', 'FdlStd', b'0', '', NULL, '', '', '', 0, NULL);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                                              isRedact, isShow, remark, location, orgId, creator,
                                              createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('2d66d782-95dc-4f79-acb6-e88abfade225', 'a7b2a5e1-94ac-4984-8e46-9eb710eb143a', 'ReportEditNew', '报告编制V2.0',
        'FdlStd', '非道路移动机械监测报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:16:31', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:16:31', NULL);


-- 柴油车监测报告报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                               returnType, method, params, pageConfig, orderNum, bizType,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, dataMethod, typeCode, strUrl,
                                               isDefineFileName, defineFileName, beanName, versionNum,
                                               controlNum, reportName, validate, usageNum)
VALUES ('743e63ff-8fb7-4abe-ac1b-b8c8daec0f46', 1, 'CycStd', '标准版柴油车监测报告.doc', 'Report/标准版报告.doc',
        'output/Report/柴油车监测报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '柴油车监测报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-02-08 14:19:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-02-08 14:19:13',
        'reportId,sortId', 'Report', 'CycStd', b'0', '', NULL, '', '', '', 0, NULL);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                                              isRedact, isShow, remark, location, orgId, creator,
                                              createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('66ce1996-c527-4cf4-9683-3f83df26edda', '743e63ff-8fb7-4abe-ac1b-b8c8daec0f46', 'ReportEditNew', '报告编制V2.0',
        'CycStd', '柴油车监测报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:21:05', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:21:05', NULL);


-- 汽油车监测报告报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                               returnType, method, params, pageConfig, orderNum, bizType,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, dataMethod, typeCode, strUrl,
                                               isDefineFileName, defineFileName, beanName, versionNum,
                                               controlNum, reportName, validate, usageNum)
VALUES ('4b8e42e9-6112-47a4-9840-6be411df9088', 1, 'QycStd', '标准版汽油车监测报告.doc', 'Report/标准版报告.doc',
        'output/Report/汽油车监测报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-02-08 14:34:25',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-02-08 14:34:25',
        'reportId,sortId', 'Report', 'QycStd', b'0', '', NULL, '', '', '', 0, NULL);

-- 报告模板配置
INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('d4f2a848-93b6-465c-b004-e88381bf56f3', '汽油车监测报告', 3, '4b8e42e9-6112-47a4-9840-6be411df9088',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-08 15:16:32', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-08 15:16:32', '', 0);
INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('eb473872-77f1-4529-bc2b-ed4c3837d84e', '柴油车监测报告', 3, '743e63ff-8fb7-4abe-ac1b-b8c8daec0f46',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-08 15:17:04', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-08 15:17:04', '', 0);
INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('eb654ee3-89f1-46db-b91e-c56f687bc9c4', '非道路移动机械监测报告', 3, 'a7b2a5e1-94ac-4984-8e46-9eb710eb143a',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-08 15:22:26', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-08 15:22:26', '', 0);

-- 报表应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                                              isRedact, isShow, remark, location, orgId, creator,
                                              createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('d2606413-16de-47aa-90dd-eebbb7b0c303', '4b8e42e9-6112-47a4-9840-6be411df9088', 'ReportEditNew', '报告编制V2.0',
        'QycStd', '汽油车监测报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:35:19', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:35:19', NULL);

-- 报告组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('12906046-fdf5-4424-9372-a6b9b53fda52', 'cycDataSource', '标准版柴油车报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtCycHeadStdTable\", \"dtCycStdTable\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 15:02:36', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 15:02:36', '0', '0', 0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('16b7568d-d16d-43cd-9963-913c7b584e86', 'dtCycHeadStdTable', '标准版柴油车表头组件', 'dtCycHeadStdTable', 'dtCycHeadSrc',
        0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-08 14:55:36', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-02-08 14:55:36', '0', '0', 0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('1cf32659-02f3-4612-96d4-a7619e160baf', 'dtQycHcStdTable', '标准版汽油车检测结果表组件（碳氢化合物）', 'dtQycHcStdTable', '', 0, 0,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-08 15:09:32',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-08 15:09:32', '0', '0',
        0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('1f802df3-9fae-44c5-b223-f04ea4012b29', 'dtCycStdTable', '标准版柴油车检测结果表组件', 'dtCycStdTable', '', 0, 0, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-08 14:58:59',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-08 14:58:59', '0', '0',
        0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('448dce2c-5746-494b-afab-a9c3fa458e43', 'qycDataSource', '标准版汽油车报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtQycHeadStdTable\", \"dtQycCoStdTable\", \"dtQycHcStdTable\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 15:13:03', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 15:13:03', '0', '0', 0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('8cb38fa5-2b12-4bd0-bbdf-87405b88e7c7', 'dtFdlStdTable', '标准版非道路检测结果表组件', 'dtFdlStdTable', '', 0, 0, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-08 14:48:02',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-08 14:48:02', '0', '0',
        0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('a90d6a69-a958-4614-9f63-f0588bd80edd', 'dtFdlHeadStdTable', '标准版非道路报告表头组件', 'dtFdlHeadStdTable',
        'dtFdlHeadSrc', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-08 14:47:03', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-02-08 14:47:03', '0', '0', 0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('d749ea9e-f891-4e14-b57c-88837a4cc801', 'dtQycHeadStdTable', '标准版汽油车报告表头组件', 'dtQycHeadStdTable',
        'dtQycHeadSrc', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-08 15:05:24', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-02-08 15:05:24', '0', '0', 0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('dfac5c15-32f0-4edd-911d-cb6f0257662b', 'dtQycCoStdTable', '标准版汽油车检测结果表组件（一氧化碳）', 'dtQycCoStdTable', '', 0, 0,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-08 15:07:21',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-08 15:07:21', '0', '0',
        0, 0, 0, 1);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('e084130b-3027-4ca3-b21e-f6efe9547e33', 'fdlDataSource', '标准版非道路报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtFdlHeadStdTable\", \"dtFdlStdTable\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:53:29', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-02-08 14:53:29', '0', '0', 0, 0, 0, 1);

-- 组件与模板关联关系配置
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('397da3d9-0378-444e-893f-ad414001bdeb', '4b8e42e9-6112-47a4-9840-6be411df9088',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('d23fbf4d-348e-491e-811e-597c222365a2', '4b8e42e9-6112-47a4-9840-6be411df9088',
        '448dce2c-5746-494b-afab-a9c3fa458e43');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('e141be53-e602-4e60-af5d-3629fe5392e3', 'a7b2a5e1-94ac-4984-8e46-9eb710eb143a',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('ec1fb088-b1ed-4518-b092-1ee28553dd1c', '743e63ff-8fb7-4abe-ac1b-b8c8daec0f46',
        '12906046-fdf5-4424-9372-a6b9b53fda52');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('ed0ddf3e-2cb8-45ac-a5df-fb73c4bdc65c', '743e63ff-8fb7-4abe-ac1b-b8c8daec0f46',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('fc7adadb-d830-4184-b95b-53194118b00d', 'a7b2a5e1-94ac-4984-8e46-9eb710eb143a',
        'e084130b-3027-4ca3-b21e-f6efe9547e33');
