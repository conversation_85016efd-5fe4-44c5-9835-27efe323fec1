
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                                               isRedact, isShow, remark, location, orgId, creator,
                                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('ade562ec-09ec-498b-a491-3dfe4d70b8a4', '63796f83-8e75-49d1-97b8-9ed27bd6bcc3', 'PrepareSample', '采样准备', '采样单',
        '饮食业油烟采样记录单', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-12-06 10:43:04', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-12-06 10:43:04', NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                                               isRedact, isShow, remark, location, orgId, creator,
                                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('b96a4d9b-8bb3-46f9-b20c-39d9ce10dc6d', '63796f83-8e75-49d1-97b8-9ed27bd6bcc3', 'LocalTask', '现场任务', '采样单',
        '饮食业油烟采样记录单', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-12-06 10:43:34', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-12-06 10:43:34', NULL);
