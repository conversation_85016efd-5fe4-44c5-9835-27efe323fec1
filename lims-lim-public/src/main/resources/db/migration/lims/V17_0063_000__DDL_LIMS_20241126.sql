-- 证书历史记录表
CREATE TABLE TB_LIM_CertHistoryInfo
(
    id           varchar(50) NOT NULL COMMENT 'id',
    detail       text COMMENT '详细信息',
    infoType     int(10) COMMENT '记录类型',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='证书历史记录表';

-- 证书历史记录附件表
CREATE TABLE TB_LIM_CertHistoryFile
(
    id           varchar(50) NOT NULL COMMENT 'id',
    documentId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '新附件标识',
    referenceId    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '原附件标识',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='证书历史记录表';