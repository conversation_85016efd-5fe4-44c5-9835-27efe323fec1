-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关DDL脚本 ------------
-- ---------------- Monitor模块 ------------------
-- ----------------------------------------------

create table TB_MONITOR_Fixedpoint
(
    id                varchar(50)                                                not null
        primary key,
    stationId         varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '测站id',
    stationName       varchar(255)                                               null comment '测站名称',
    pointName         varchar(255)                                               null comment '点位名称',
    isEnabled         bit         default b'1'                                   not null comment '是否启用',
    evaluationId      varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '评价标准',
    evaluationLevelId varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '评价等级',
    remark            varchar(2000)                                              null comment '备注',
    cycleOrder        int         default -1                                     not null comment '周期',
    timesOrder        int         default -1                                     not null comment '次数',
    internalCode      varchar(50)                                                null comment '内部编码',
    pointCode         varchar(50)                                                null comment '点位编号',
    level             varchar(50)                                                null comment '等级 ： 常量',
    lon               varchar(50)                                                null comment '经度',
    lat               varchar(50)                                                null comment '纬度',
    pointType         int         default 0                                      not null comment '类型（枚举：环境质量 1 污染源 2）',
    folderType        varchar(50)                                                not null comment '点位类型（常量），1：河流，2：湖库，3：饮用水，4：功能区噪声，5：区域环境噪声，6：交通噪声，7：底泥，8：大气）',
    sampleTypeId      varchar(50) default '00000000-0000-0000-0000-000000000000' null,
    enterpriseId      varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '所属企业',
    villageCode       varchar(255)                                               null comment '地址',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    orderNum          int         default 0                                      not null,
    examArea          varchar(50)                                                null comment '考核区域',
    areaId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所在地区Id（Guid）（lims）'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_FixedPoint2Point
(
    id           varchar(50) not null comment '主键' primary key,
    fixedPointId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '点位id',
    pointId      varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '关联点位Id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_FixedPoint2Test
(
    id           varchar(50) not null comment '主键' primary key,
    fixedPointId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '点位id',
    testId       varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '测试项目id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_FixedPointExpend
(
    id                        varchar(50)                                                not null comment '主键'
        primary key,
    fixedPointId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位id',
    waterId                   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '水体、河流id',
    functionZoneCode          varchar(255)                                               null comment '水域功能代码',
    watersl                   varchar(50)                                                null comment '网管断面级别',
    areasl                    varchar(50)                                                null comment '域管断面级别',
    rchal                     varchar(50)                                                null comment '交接断面管辖级别',
    ygwq                      varchar(255)                                               null comment '月取水量',
    dayhorbegin               int         default -1                                     not null comment '功能区噪声_昼间起始时',
    dayhorend                 int         default -1                                     not null comment '功能区噪声_昼间结束时',
    nighthorbegin             int         default -1                                     not null comment '功能区噪声_夜间起始时',
    nighthorend               int         default -1                                     not null comment '功能区噪声_夜间结束时',
    refer                     varchar(50)                                                null comment '测点参照物',
    gridLength                varchar(255)                                               null comment '区域噪声_网格边长',
    gridWidth                 varchar(255)                                               null comment '区域噪声_网格边宽',
    noiseSourceCode           varchar(255)                                               null comment '区域噪声_噪声声源代码',
    noiseFunZoneCode          varchar(255)                                               null comment '区域噪声_噪声功能区代码',
    gridCoverPeoples          varchar(255)                                               null comment '区域噪声_网格覆盖人口',
    rdsecName                 varchar(255)                                               null comment '道路噪声_路段名称',
    rdsecfromto               varchar(255)                                               null comment '道路噪声_路段起始点',
    railwayLength             varchar(255)                                               null comment '道路噪声_路段长度',
    railwayWidth              varchar(255)                                               null comment '道路噪声_路幅长度',
    rdLevel                   varchar(50)                                                null comment '道路噪声_道路等级',
    weekCalcu                 varchar(50)                                                null comment '大气_测点空气质量报告级别',
    so2pl                     varchar(50)                                                null comment '大气_二氧化硫区管测点级别',
    acidpl                    varchar(50)                                                null comment '大气_酸雨区管测点级别',
    airpl                     varchar(50)                                                null comment '大气_大气网管测点级别',
    acidp                     varchar(50)                                                null comment '大气_降水网管测点级别',
    underWaterTypeCode        varchar(255)                                               null comment '地下水_地下水类型代码',
    underWaterType            varchar(255)                                               null comment '地下水_地下水类型',
    exhaustPipeHeight         varchar(255)                                               null comment '排气管高度',
    craftFacilityName         varchar(255)                                               null comment '工艺设备名称',
    purificateFacilityName    varchar(255)                                               null comment '净化设备名称',
    pollutionType             varchar(255)                                               null comment '污染源种类',
    craftFacilityUseDate      datetime    default '1753-01-01 00:00:00'                  not null comment '工艺设备/启用时间',
    boilerMakeUnit            varchar(255)                                               null comment '锅炉制造单位',
    equipmentTypeName         varchar(255)                                               null comment '名称(型号)',
    boilerUseDate             datetime    default '1753-01-01 00:00:00'                  not null comment '锅炉投运日期',
    chimneyHeight             varchar(255)                                               null comment '烟囱高度',
    purificateFacilityUnit    varchar(255)                                               null comment '净化设备制造单位',
    purificateFacilityType    varchar(255)                                               null comment '净化设备型号',
    purificateFacilityUseDate datetime    default '1753-01-01 00:00:00'                  not null comment '净化设备投运日期',
    fuelType                  varchar(255)                                               null comment '燃料类型',
    stoveFacilityType         varchar(255)                                               null comment '炉窖设备型号',
    stoveFacilityCode         varchar(255)                                               null comment '炉窖设备编号',
    emissionFate              varchar(255)                                               null comment '排放去向',
    importAndExport           varchar(255)                                               null comment '进出口',
    orgId                     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator                   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate                datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId                  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier                  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate                datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'

) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_MONITOR_Station
(
    id         varchar(50)                                                not null comment '主键' primary key,
    stcode     varchar(50)                                                null comment '测站代码',
    stname     varchar(50)                                                null comment '测站名称',
    staddress  varchar(255)                                               null comment '测站位置',
    isEndable  bit         default b'1'                                   not null comment '是否启用',
    remark     varchar(2000)                                              null comment '备注',
    orderNum   int         default 0                                      not null comment '排序值',
    entId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属单位',
    isDeleted  bit         default b'0'                                   not null comment '假删',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_Water
(
    id         varchar(50)                                                not null comment '主键' primary key,
    parentId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联id',
    waterName  varchar(50)                                                null comment '水体名称',
    waterCode  varchar(50)                                                null comment '水体编码',
    waterType  varchar(50)                                                null comment '水体类型：常量（河流、饮用水水厂、水系、水功能区、应用水源地、流域、湖库）',
    isEnabled  bit         default b'1'                                   not null comment '是否启用',
    remark     varchar(2000)                                              null comment '备注',
    isDeleted  bit         default b'0'                                   null,
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_WaterExpand
(
    id                    varchar(50)                                                not null comment '主键' primary key,
    waterId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '水体id',
    waterFunctionZoneType varchar(50)                                                null comment '水功能区类型',
    startPlaceName        varchar(50)                                                null comment '起始位置',
    netWaterLevel         varchar(50)                                                null comment '网管湖库级别',
    areaWaterLevel        varchar(50)                                                null comment '域管湖库级别',
    lakesTypeCode         varchar(50)                                                null comment '湖库类型代码',
    waterl                varchar(50)                                                null comment '网管河流级别',
    awaterl               varchar(50)                                                null comment '域管河流级别',
    locationName          varchar(250)                                               null comment '水厂所在地名称',
    endPlaceName          varchar(50)                                                null comment '终止位置',
    waterFunctionZoneLen  varchar(50)                                                null comment '水功能区长度',
    yswq                  varchar(50)                                                null comment '年供水量',
    remark                varchar(255)                                               null comment '备注',
    orgId                 varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate            datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate            datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

