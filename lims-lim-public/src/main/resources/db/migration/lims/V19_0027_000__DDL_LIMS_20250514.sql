-- ----------------------------------------------------
-- 测试项目表中添加上海监管平台测试项目匹配状态字段
-- ----------------------------------------------------
ALTER TABLE TB_LIM_Test
    ADD COLUMN shMatchStatus INT(11) NOT NULL DEFAULT 0 COMMENT '上海监管平台测试项目匹配状态，0：未匹配，1：异常匹配，2：完美匹配';

-- ----------------------------------------------------
-- 测试项目表中添加【上海监管平台测试项目匹配信息】息字段
-- ----------------------------------------------------
ALTER TABLE TB_LIM_Test
    ADD COLUMN shMatchMessage VARCHAR(255) NULL COMMENT '上海监管平台测试项目匹配信息';

-- ----------------------------------------------------
-- 附件表中添加【上海监管平台附件ID】字段
-- ----------------------------------------------------
ALTER TABLE TB_BASE_Document
    ADD COLUMN shDocumentId VARCHAR(50) NULL COMMENT '上海监管平台附件ID';


-- ----------------------------------------------------
-- 上海监管平台项目推送表中的【监管平台甲方企业名称】修改为可为空
-- ----------------------------------------------------
ALTER TABLE TB_PRO_ProjectContract MODIFY COLUMN shanghaiEntName VARCHAR(255) NULL COMMENT '监管平台甲方企业名称';



-- ----------------------------------------------------
-- 上海监管平台项目推送表中的添加【推送采样类型与任务类型是否一致】字段
-- ----------------------------------------------------
ALTER TABLE TB_PRO_ProjectContract ADD COLUMN isShProjectTypeMatch BIT(1) DEFAULT 1 COMMENT '推送采样类型与任务类型是否一致';