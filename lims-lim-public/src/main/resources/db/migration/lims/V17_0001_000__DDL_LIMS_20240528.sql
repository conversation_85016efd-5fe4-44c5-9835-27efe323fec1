-- 新增检测单溶液标定表
CREATE TABLE TB_PRO_SolutionCalibration
(
    id              varchar(50)     NOT NULL                                                COMMENT '主键id',
    workSheetFolderId      varchar(50)    NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '检测单标识',
    calibrationSolutionName      varchar(100)                                    COMMENT '标定溶液名称',
    transferSolutionName varchar(100)                                              COMMENT '移取溶液名称',
    transferSolutionConcentration     varchar(50)                      COMMENT '移取溶液浓度',
    transferSolutionDimensionId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'                    COMMENT '移取溶液浓度量纲标识',
    mostSignificance      int              NOT NULL DEFAULT -1                              COMMENT '有效位数',
    mostDecimal           int              NOT NULL DEFAULT -1                              COMMENT '小数位数',
    averageConcentration         varchar(50)                                                COMMENT '标定浓度均值',
    averageConcentrationDimensionId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'                  COMMENT '标定浓度均值量纲标识',
    isDeleted            bit             default b'0'                                   not null comment '是否删除',
    orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '检测单溶液标定表';
create index IX_TB_PRO_SolutionCalibration
    on TB_PRO_SolutionCalibration (orgId, workSheetFolderId, isDeleted);

-- 新增检测单溶液标定记录表
CREATE TABLE TB_PRO_SolutionCalibrationRecord
(
    id              varchar(50)     NOT NULL                                                COMMENT '主键id',
    solutionCalibrationId      varchar(50)    NOT NULL  DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '溶液标定标识',
    transferVolume      varchar(50)                                                 COMMENT '溶液移取量',
    volumeStart       varchar(50)                                                  COMMENT 'V始',
    volumeEnd       varchar(50)                                                        COMMENT 'V终',
    concentration         varchar(50)                                                COMMENT '标定液浓度',
    mostSignificance      int              NOT NULL DEFAULT -1                              COMMENT '有效位数',
    mostDecimal           int              NOT NULL DEFAULT -1                              COMMENT '小数位数',
    isDeleted            bit             default b'0'                                   not null comment '是否删除',
    orgId           varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP (0)                  COMMENT '创建时间',
    domainId        varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP (0)                  COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '检测单溶液标定记录表';
create index IX_TB_PRO_SolutionCalibrationRecord
    on TB_PRO_SolutionCalibrationRecord (orgId, solutionCalibrationId, isDeleted);