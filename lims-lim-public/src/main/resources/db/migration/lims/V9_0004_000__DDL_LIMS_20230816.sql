-- 报告编号池表结构
CREATE TABLE `tb_pro_reportnumberpool`
(
    `id`           varchar(50)  NOT NULL COMMENT '主键',
    `year`         int(10) DEFAULT NULL COMMENT '报告年份',
    `code`         varchar(100) NOT NULL DEFAULT '' COMMENT '报告编号',
    `reportTypeId` varchar(50)  NOT NULL DEFAULT '' COMMENT '报告类型id',
    `status`       int(10) DEFAULT '0' COMMENT '编号状态 0:未使用 1:已使用 2: 已作废',
    `usedDate`     datetime              DEFAULT NULL COMMENT '使用日期',
    `orgId`        varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`      varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `domainId`     varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    `modifier`     varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    `modifyDate`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报告编号池';
