-- 油气回收报告组件配置
DELETE
FROM TB_LIM_ReportConfig
WHERE id = 'da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6', 1, 'GasRecycleStd', '标准版油气回收报告.doc', 'Report/标准版报告.doc',
        'output/Report/油气回收报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '油气回收报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-21 13:27:52', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-21 13:30:37', 'reportId,sortId', 'Report', 'GasRecycleStd', b'0', '', NULL, '', '', '', 0, NULL);

DELETE
FROM TB_LIM_ReportApply
WHERE id = 'fec1d7b5-26d5-4a2b-98f6-842c17d3bd93';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('fec1d7b5-26d5-4a2b-98f6-842c17d3bd93', 'da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6', 'ReportEditNew', '报告编制V2.0',
        'GasRecycleStd', '油气回收报告', 1, 1, 1, '', '报告编制:电子报告', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-21 13:29:03', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-21 13:29:03', NULL);

DELETE
FROM TB_LIM_RecordConfig
WHERE id = 'f47decb8-ba2c-4853-962b-508ce71d8231';

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark, isDeleted, orgId,
                                creator, createDate, domainId, modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('f47decb8-ba2c-4853-962b-508ce71d8231', '油气回收报告', 3, 'da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-21 14:19:00', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-21 14:19:00', '', 0);

DELETE
FROM TB_LIM_ReportModule
WHERE id in ('133bd4cc-2b3b-42f9-80e7-567f2cf4176f', '328d2f58-36ed-4e9e-950e-4308f034b69f',
             '8d2739ed-0dc1-4976-a3dd-e4e8b4aae2f1', 'a1c7bdd1-6514-4c27-bfee-d0ac4870f942',
             'f2be013f-2729-462a-9198-0291f2b9e199');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('133bd4cc-2b3b-42f9-80e7-567f2cf4176f', 'dtMbxStdTable', '标准版油气回收密闭性检测结果表组件', 'dtMbxStdTable',
        'dtMbxStdSource', 20, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-08-21 14:23:18', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-08-21 14:23:18', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('328d2f58-36ed-4e9e-950e-4308f034b69f', 'dtYzStdTable', '标准版油气回收液阻检测结果表组件', 'dtYzStdTable',
        'dtYzStdSource', 20, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-08-21 14:26:22', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-08-21 14:26:22', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('8d2739ed-0dc1-4976-a3dd-e4e8b4aae2f1', 'dtGasRecycleHeadStdTable', '标准版油气回收表头组件',
        'dtGasRecycleHeadStdTable', 'dtOilRecycleInstSource', 10000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-08-21 14:21:22', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-08-21 14:21:22', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('a1c7bdd1-6514-4c27-bfee-d0ac4870f942', 'gasRecycleStdDataSource', '标准版油气回收报告检测数据主表',
        'dtDataSource', '', 0, 0,
        '[\"dtGasRecycleHeadStdTable\", \"dtMbxStdTable\", \"dtYzStdTable\", \"dtQybStdTable\"]', b'1',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-08-21 14:31:13',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-08-21 14:31:13', '0', '0',
        0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('f2be013f-2729-462a-9198-0291f2b9e199', 'dtQybStdTable', '标准版油气回收报告气液比检测结果表组件',
        'dtQybStdTable', 'dtQybStdSource', 20, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-08-21 14:28:25', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-08-21 14:28:25', '0', '0', 0, 0, 0, 1);

DELETE
FROM TB_LIM_ReportConfig2Module
WHERE id in ('174f7a25-d802-4b0d-ac2b-35481729bd04', '7060cfaa-27af-4d5d-8ca8-fe62fe657657',
             '73fa5d5a-28ba-4dd1-a157-51480555f0cc');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('174f7a25-d802-4b0d-ac2b-35481729bd04', 'da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6',
        'a1c7bdd1-6514-4c27-bfee-d0ac4870f942');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('7060cfaa-27af-4d5d-8ca8-fe62fe657657', 'da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('73fa5d5a-28ba-4dd1-a157-51480555f0cc', 'da4e8a7d-0da2-4c41-9f0e-1da8acd99ed6',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');
