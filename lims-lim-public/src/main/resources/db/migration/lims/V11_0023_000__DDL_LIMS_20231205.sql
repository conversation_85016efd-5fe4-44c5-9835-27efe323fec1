DROP PROCEDURE IF EXISTS `add_col`;
<PERSON><PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_PRO_SampleJudgeData' AND column_name = 'testTimeStr')) THEN
        ALTER TABLE TB_PRO_SampleJudgeData
            ADD COLUMN testTimeStr varchar(50) NULL DEFAULT '' COMMENT '监测时间/段';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;