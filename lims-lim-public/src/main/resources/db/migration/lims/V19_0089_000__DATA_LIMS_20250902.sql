-- 新增送样报告样品数据组件
DELETE FROM TB_LIM_ReportModule WHERE id in ('3516284b-4555-49ef-b5f0-a46f5a68b86c', 'bdb83ad0-9151-4d43-8331-8792e17a3662');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('3516284b-4555-49ef-b5f0-a46f5a68b86c', 'dtSyFolderStdTable', '标准版送样检测结果表组件（点位行扩展）',
        'dtSyFolderStdTable', 'dtSyFolderSource', 5, 10, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-09-02 14:19:28', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-09-02 14:19:28', '0', '0', 0, 0, 0, 1);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('bdb83ad0-9151-4d43-8331-8792e17a3662', 'sYFolderStdDataSource', '标准版送样类报告检测数据主表（点位行扩展）',
        'dtDataSource', '', 0, 0,
        '[\"dtSyHeadStdTable\", \"dtSyFolderStdTable\", \"dtCompoundStdNewTable\", \"outParallelStdDataSource\"]', b'1',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-09-02 14:29:18',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-09-02 14:29:18', '0', '0',
        0, 0, 0, 1);

DELETE FROM TB_LIM_ReportConfig WHERE id = '37ef1063-2d98-4fca-8914-2362ffd9aaa3';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('37ef1063-2d98-4fca-8914-2362ffd9aaa3', 1, 'SyFolderStd', '标准版报告.doc', 'Report/标准版报告.doc',
        'output/Report/送样类报告（点位行扩展）.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '送样报告（点位行扩展）', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-09-02 14:34:11', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-09-02 14:34:11', 'reportId,sortId', 'Report', 'SyFolderStd', b'0', '', NULL, '', '', '', 0, NULL);

DELETE FROM TB_LIM_ReportApply WHERE id = 'a9ead03c-a0ca-466e-8dd8-f73ddf5b8f6f';

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('a9ead03c-a0ca-466e-8dd8-f73ddf5b8f6f', '37ef1063-2d98-4fca-8914-2362ffd9aaa3', 'ReportEditNew', '报告编制V2.0',
        'SyFolderStd', '送样报告（点位行扩展）', 1, 1, 1, '', '报告编制:电子报告', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-02 14:44:44', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-02 14:44:44', NULL);

DELETE FROM TB_LIM_RecordConfig WHERE id = 'ae320833-9741-40e6-89a3-b46c9a879f97';

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark, isDeleted, orgId,
                                creator, createDate, domainId, modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('ae320833-9741-40e6-89a3-b46c9a879f97', '送样类报告（点位行扩展）', 3, '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-02 14:48:45', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-09-02 14:48:45', '', 0);

DELETE FROM TB_LIM_ReportConfig2Module WHERE id in ('146e3bda-a4d5-4eab-a25b-17873148cf8b', '2ab25900-1f91-435f-8126-0d3adfb84af4', '31313dac-9122-43d9-bc19-340e1b927119',
                                                    '570caab9-1f83-4496-a3c2-5e785579e39f', 'bfeeee1d-e626-4aa0-bf73-67d0f7a78c8d', 'e6ece23f-4a50-4b29-a894-e8f3dd2d56bf');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('146e3bda-a4d5-4eab-a25b-17873148cf8b', '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('2ab25900-1f91-435f-8126-0d3adfb84af4', '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        '844acab5-8883-44d1-b8fa-af039526e967');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('31313dac-9122-43d9-bc19-340e1b927119', '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('570caab9-1f83-4496-a3c2-5e785579e39f', '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('bfeeee1d-e626-4aa0-bf73-67d0f7a78c8d', '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        '6130da13-013a-4a25-a856-bd1f16fc691b');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('e6ece23f-4a50-4b29-a894-e8f3dd2d56bf', '37ef1063-2d98-4fca-8914-2362ffd9aaa3',
        'bdb83ad0-9151-4d43-8331-8792e17a3662');

DELETE FROM TB_LIM_ReportModule2GroupType WHERE id = '8dfc3477-42cb-4caa-991f-0a11d22bfe13';

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('8dfc3477-42cb-4caa-991f-0a11d22bfe13', 'e6ece23f-4a50-4b29-a894-e8f3dd2d56bf',
        'sampleData_sampleTypeId_fieldGroupType', 10);
