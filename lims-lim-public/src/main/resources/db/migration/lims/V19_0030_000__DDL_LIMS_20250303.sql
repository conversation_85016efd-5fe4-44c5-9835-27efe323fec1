CREATE TABLE TB_Ods_Task
(
    `id`         VARCHAR(50)  NOT NULL COMMENT '主键',
    `taskType`   INT          NOT NULL DEFAULT -1 COMMENT '嗅辨类型，枚举管理',
    `taskName`   VARCHAR(100) NOT NULL COMMENT '任务名称',
    `gasMixerId` VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '配气员id',
    `odDate`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '嗅辨日期',
    `taskState`  INT          NOT NULL DEFAULT -1 COMMENT '任务状态，枚举管理',
    `isDeleted`  BIT(1)       NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`      VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`    VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '嗅辨任务表';

CREATE TABLE TB_Ods_Sample
(
    `id`           VARCHAR(50) NOT NULL COMMENT '主键',
    `taskId`       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '嗅辨任务id',
    `sampleCode`   VARCHAR(50) NOT NULL COMMENT '样品编号',
    `samplingDate` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '采样日期',
    `sampleState`  INT         NOT NULL DEFAULT -1 COMMENT '样品状态， 枚举管理',
    `isDeleted`    BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '嗅辨样品表';

CREATE TABLE TB_Ods_SampleResult
(
    `id`               VARCHAR(50) NOT NULL COMMENT '主键',
    `sampleId`         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品id',
    `rValue`           VARCHAR(20) COMMENT 'r相关系数(固定源)',
    `tValue`           VARCHAR(20) COMMENT 'T检验结果(固定源)',
    `xValue`           VARCHAR(20) COMMENT 'X平均阈值(固定源)',
    `aValue`           VARCHAR(20) COMMENT 'α幂参数(环境空气)',
    `bValue`           VARCHAR(20) COMMENT 'β幂参数(环境空气)',
    `odourConsistence` VARCHAR(20) COMMENT '臭气浓度',
    `isPassed`         BIT(1)      NOT NULL DEFAULT B'0' COMMENT '是否通过',
    `isDeleted`        BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`            VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '样品结果表';

CREATE TABLE TB_Ods_OdPerson
(
    `id`         VARCHAR(50) NOT NULL COMMENT '主键',
    `taskId`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '嗅辨任务id',
    `personId`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '嗅辨员id',
    `personSn`   VARCHAR(10) NOT NULL COMMENT '嗅辨员次序，A、B、C、D之类流水',
    `isDeleted`  BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '嗅辨员表';

CREATE TABLE TB_Ods_LabGroup
(
    `id`          VARCHAR(50) NOT NULL COMMENT '主键',
    `sampleId`    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品id',
    `sn`          INT         NOT NULL DEFAULT -1 COMMENT '序号(第几组实验)',
    `isCalculate` BIT(1)      NOT NULL DEFAULT B'0' COMMENT '是否作为最终分析结果',
    `calculateSn` INT         NOT NULL DEFAULT -1 COMMENT '最终分析结果显示顺序',
    `isDeleted`   BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '实验组表';

CREATE TABLE TB_Ods_LabSeq
(
    `id`           VARCHAR(50) NOT NULL COMMENT '主键',
    `groupId`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '实验组id',
    `sn`           INT         NOT NULL DEFAULT -1 COMMENT '实验次序号(第几次实验)',
    `dilutionRate` INT         NOT NULL DEFAULT -1 COMMENT '稀释倍数',
    `labState`     INT         NOT NULL DEFAULT -1 COMMENT '实验状态，枚举管理',
    `isDeleted`    BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '实验次序表';

CREATE TABLE TB_Ods_StationarySourceResult
(
    `id`           VARCHAR(50) NOT NULL COMMENT '主键',
    `groupId`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '实验组id',
    `avgThreshold` VARCHAR(20) COMMENT '平均阈值',
    `rsd`          VARCHAR(50) COMMENT '标准偏差',
    `isDeleted`    BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id, isDeleted)
) COMMENT = '固定源结果表';

CREATE TABLE TB_Ods_StationarySourceResultDetail
(
    `id`         VARCHAR(50) NOT NULL COMMENT '主键',
    `resultId`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '结果表id',
    `personId`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '嗅辨员id',
    `threshold`  VARCHAR(20) NOT NULL COMMENT '个人阈值',
    `isDeleted`  BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '固定源结果详情表表';

CREATE TABLE TB_Ods_EnvGasResult
(
    `id`           VARCHAR(50) NOT NULL COMMENT '主键',
    `groupId`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '实验组id',
    `dilutionRate` INT         NOT NULL DEFAULT -1 COMMENT '稀释倍数',
    `aCount`       INT         NOT NULL DEFAULT 0 COMMENT '答对的次数',
    `bCount`       INT         NOT NULL DEFAULT 0 COMMENT '不明的次数',
    `cCount`       INT         NOT NULL DEFAULT 0 COMMENT '答错的次数',
    `mValue`       VARCHAR(20) NOT NULL COMMENT 'M值',
    `isPassed`     BIT(1)      NOT NULL DEFAULT B'0' COMMENT '是否通过',
    `isDeleted`    BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '环境空气结果表';

CREATE TABLE TB_Ods_OdPersonAnswer
(
    `id`             VARCHAR(50) NOT NULL COMMENT '主键',
    `labSeqId`       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '实验次序id',
    `personId`       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '嗅辨员id',
    `personAnswer`   VARCHAR(10) COMMENT '嗅辨员测试结果',
    `standardAnswer` VARCHAR(10) NOT NULL COMMENT '标准答案',
    `evaluation`     INT                  DEFAULT -1 COMMENT '评价，枚举管理',
    `confidenceLvl`  INT                  DEFAULT -1 COMMENT '自信度，枚举管理',
    `isCompleted`    BIT(1)      NOT NULL DEFAULT B'0' COMMENT '是否嗅辨完成',
    `isDeleted`      BIT(1)      NOT NULL DEFAULT B'0' COMMENT '假删标识',
    `domainId`       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    `orgId`          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '更新人',
    `modifyDate`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '嗅辨员答案表';



