-- tb_lim_params2paramsformula
drop trigger if exists after_insert_params2paramsformula;
DELIMITER $$
CREATE TRIGGER after_insert_params2paramsformula
    AFTER INSERT
    ON tb_lim_params2paramsformula
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId,
            new.domainId);
end $$
DELIMITER ;

drop trigger if exists after_update_params2paramsformula;
DELIMITER $$
CREATE TRIGGER after_update_params2paramsformula
    AFTER UPDATE
    ON tb_lim_params2paramsformula
    FOR EACH ROW
BEGIN
    IF new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
        if new.recordId != old.recordId or (new.recordId is null and old.recordId is not null) or
           (new.recordId is not null and old.recordId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'recordId',
                    old.recordId, new.recordId, new.orgId, new.domainId);
        END IF;
        if new.paramsConfigId != old.paramsConfigId or (new.paramsConfigId is null and old.paramsConfigId is not null) or
           (new.paramsConfigId is not null and old.paramsConfigId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'paramsConfigId',
                    old.paramsConfigId, new.paramsConfigId, new.orgId, new.domainId);
        END IF;
        if new.objectId != old.objectId or (new.objectId is null and old.objectId is not null) or
           (new.objectId is not null and old.objectId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'objectId',
                    old.objectId, new.objectId, new.orgId, new.domainId);
        END IF;
        if new.formula != old.formula or (new.formula is null and old.formula is not null) or
           (new.formula is not null and old.formula is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'formula',
                    old.formula, new.formula, new.orgId, new.domainId);
        END IF;
        if new.isEnabled != old.isEnabled or (new.isEnabled is null and old.isEnabled is not null) or
           (new.isEnabled is not null and old.isEnabled is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'isEnabled',
                    old.isEnabled, new.isEnabled, new.orgId, new.domainId);
        END IF;
    END IF;
end $$
DELIMITER ;

-- tb_lim_paramsconfig
drop trigger if exists after_insert_paramsconfig;
DELIMITER $$
CREATE TRIGGER after_insert_paramsconfig
    AFTER INSERT
    ON tb_lim_paramsconfig
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId,
            new.domainId);
end $$
DELIMITER ;


drop trigger if exists after_update_paramsconfig;
DELIMITER $$
CREATE TRIGGER after_update_paramsconfig
    AFTER UPDATE
    ON tb_lim_paramsconfig
    FOR EACH ROW
BEGIN
    IF new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
       if new.objId != old.objId or (new.objId is null and old.objId is not null) or
           (new.objId is not null and old.objId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'objId',
                    old.objId, new.objId, new.orgId, new.domainId);
       END IF;
       if new.paramsId != old.paramsId or (new.paramsId is null and old.paramsId is not null) or
           (new.paramsId is not null and old.paramsId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'paramsId',
                    old.paramsId, new.paramsId, new.orgId, new.domainId);
       END IF;
       if new.alias != old.alias or (new.alias is null and old.alias is not null) or
           (new.alias is not null and old.alias is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'alias',
                    old.alias, new.alias, new.orgId, new.domainId);
       END IF;
       if new.defaultValue != old.defaultValue or (new.defaultValue is null and old.defaultValue is not null) or
           (new.defaultValue is not null and old.defaultValue is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'defaultValue',
                    old.defaultValue, new.defaultValue, new.orgId, new.domainId);
       END IF;
       if new.dimension != old.dimension or (new.dimension is null and old.dimension is not null) or
           (new.dimension is not null and old.dimension is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'dimension',
                    old.dimension, new.dimension, new.orgId, new.domainId);
       END IF;
       if new.dimensionId != old.dimensionId or (new.dimensionId is null and old.dimensionId is not null) or
           (new.dimensionId is not null and old.dimensionId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'dimensionId',
                    old.dimensionId, new.dimensionId, new.orgId, new.domainId);
       END IF;
       if new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
           (new.orderNum is not null and old.orderNum is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'orderNum',
                    old.orderNum, new.orderNum, new.orgId, new.domainId);
       END IF;
       if new.type != old.type or (new.type is null and old.type is not null) or
           (new.type is not null and old.type is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'type',
                    old.type, new.type, new.orgId, new.domainId);
       END IF;
       if new.defaultControl != old.defaultControl or (new.defaultControl is null and old.defaultControl is not null) or
           (new.defaultControl is not null and old.defaultControl is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'defaultControl',
                    old.defaultControl, new.defaultControl, new.orgId, new.domainId);
       END IF;
       if new.dataSource != old.dataSource or (new.dataSource is null and old.dataSource is not null) or
           (new.dataSource is not null and old.dataSource is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'dataSource',
                    old.dataSource, new.dataSource, new.orgId, new.domainId);
       END IF;
       if new.isRequired != old.isRequired or (new.isRequired is null and old.isRequired is not null) or
           (new.isRequired is not null and old.isRequired is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'isRequired',
                    old.isRequired, new.isRequired, new.orgId, new.domainId);
       END IF;
       if new.mostSignificance != old.mostSignificance or (new.mostSignificance is null and old.mostSignificance is not null) or
           (new.mostSignificance is not null and old.mostSignificance is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'mostSignificance',
                    old.mostSignificance, new.mostSignificance, new.orgId, new.domainId);
       END IF;
       if new.mostDecimal != old.mostDecimal or (new.mostDecimal is null and old.mostDecimal is not null) or
           (new.mostDecimal is not null and old.mostDecimal is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'mostDecimal',
                    old.mostDecimal, new.mostDecimal, new.orgId, new.domainId);
       END IF;
       if new.analyzeItemId != old.analyzeItemId or (new.analyzeItemId is null and old.analyzeItemId is not null) or
           (new.analyzeItemId is not null and old.analyzeItemId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'analyzeItemId',
                    old.analyzeItemId, new.analyzeItemId, new.orgId, new.domainId);
       END IF;
       if new.parentId != old.parentId or (new.parentId is null and old.parentId is not null) or
           (new.parentId is not null and old.parentId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'parentId',
                    old.parentId, new.parentId, new.orgId, new.domainId);
       END IF;
       if new.isShow != old.isShow or (new.isShow is null and old.isShow is not null) or
           (new.isShow is not null and old.isShow is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'isShow',
                    old.isShow, new.isShow, new.orgId, new.domainId);
       END IF;
       if new.isFormula != old.isFormula or (new.isFormula is null and old.isFormula is not null) or
           (new.isFormula is not null and old.isFormula is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'isFormula',
                    old.isFormula, new.isFormula, new.orgId, new.domainId);
       END IF;
       if new.formulaId != old.formulaId or (new.formulaId is null and old.formulaId is not null) or
           (new.formulaId is not null and old.formulaId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'formulaId',
                    old.formulaId, new.formulaId, new.orgId, new.domainId);
       END IF;
       if new.isAllConfig != old.isAllConfig or (new.isAllConfig is null and old.isAllConfig is not null) or
           (new.isAllConfig is not null and old.isAllConfig is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'isAllConfig',
                    old.isAllConfig, new.isAllConfig, new.orgId, new.domainId);
       END IF;
       if new.paramsType != old.paramsType or (new.paramsType is null and old.paramsType is not null) or
           (new.paramsType is not null and old.paramsType is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'paramsType',
                    old.paramsType, new.paramsType, new.orgId, new.domainId);
       END IF;
       if new.referenceText != old.referenceText or (new.referenceText is null and old.referenceText is not null) or
           (new.referenceText is not null and old.referenceText is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_paramsconfig', new.id, new.modifier, new.modifyDate, 3, 'referenceText',
                    old.referenceText, new.referenceText, new.orgId, new.domainId);
       END IF;
    END IF;
end $$
DELIMITER ;