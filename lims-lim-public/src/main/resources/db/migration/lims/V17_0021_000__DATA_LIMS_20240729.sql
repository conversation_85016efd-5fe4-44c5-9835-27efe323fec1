-- 区域噪声报告模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('fcbbe48e-a0fc-4ed0-a00a-e2a67741eef2', 1, 'AreaNoiseStd', '标准版区域噪声报告.doc', 'Report/标准版报告.doc',
        'output/Report/区域噪声报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '区域噪声报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-29 15:20:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-29 15:20:51',
        'reportId,sortId', 'Report', 'AreaNoiseStd', b'0', '', NULL, '', '', '', 0, NULL);

