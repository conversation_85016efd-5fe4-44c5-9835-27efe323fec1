-- 数据状态更新服务配置
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy,
                                         isConcurrent, status, remark, isDeleted, orgId, creator,
                                         createDate, domainId, modifier, modifyDate)
VALUES ('53a470c6-7134-4cc4-97d2-8c4d691a19c2', '领样单状态更新', 'DEFAULT',
        'updateStatusTask.process(\'ReceiveSubSampleRecordStatus\')', '0 0 * * * ?', 3, b'0', 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-16 16:36:20',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-16 17:32:51');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy,
                                         isConcurrent, status, remark, isDeleted, orgId, creator,
                                         createDate, domainId, modifier, modifyDate)
VALUES ('ad77f432-97f5-4ed8-bb79-79cf91f8fffc', '分析数据状态更新', 'DEFAULT',
        'updateStatusTask.process(\'AnalyseDataStatus\')', '0 0 * * * ?', 3, b'0', 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-16 16:35:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-16 17:32:48');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy,
                                         isConcurrent, status, remark, isDeleted, orgId, creator,
                                         createDate, domainId, modifier, modifyDate)
VALUES ('bc0c3a04-9c32-4183-b18e-6fbdbd5a3377', '样品状态更新', 'DEFAULT', 'updateStatusTask.process(\'SampleStatus\')',
        '0 0 * * * ?', 3, b'0', 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-04-16 13:06:45', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-05-16 17:32:38');
