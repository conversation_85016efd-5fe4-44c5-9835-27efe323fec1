CREATE TABLE `tb_lim_docauthorityconfig`
(
    `id`             VARCHAR(50) NOT NULL COMMENT 'id',
    `objectId`       VARCHAR(50) NOT NULL DEFAULT '' COMMENT '文件夹，文件Id（Guid）',
    `authCode`         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '权限编码（常量Guid，常量名称 LIM_AuthType）',
    `authName`       VARCHAR(250)         DEFAULT '' COMMENT '权限名称',
    `defaultOpenInd` bit(1)               DEFAULT NULL COMMENT '是否默认开启 1：是  0：否',
    `userId`         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员Id',
    `sortNum`        INT ( 11 ) DEFAULT NULL COMMENT '排序号',
    `orgId`          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8 ROW_FORMAT = DYNAMIC;


CREATE TABLE `tb_lim_training`
(
    `id`        VARCHAR(50) NOT NULL COMMENT '主键',
    `trainingName` VARCHAR(50) NOT NULL COMMENT '培训名称',
    `trainingDate` datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '培训日期',
    `way`       VARCHAR(50)          DEFAULT NULL COMMENT '培训方式  常量表示 LIM_Training ',
    `times`     DOUBLE ( 11, 1
) DEFAULT NULL COMMENT '培训时长',
	`lecturer` VARCHAR ( 50 ) COMMENT '培训讲师',
	`recorder` VARCHAR ( 50 )  COMMENT '记录人',
	`status` INT ( 1 ) NOT NULL DEFAULT '0' COMMENT '培训状态  0: 未开展  1：已开展',
	`auditInd` bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '是否审批 0: 否  1：是',
	`content` VARCHAR ( 255 ) DEFAULT NULL COMMENT '培训内容',
	`planPeopleNums` INT ( 11 ) DEFAULT NULL COMMENT '计划参加人数',
	`applicant` VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '申请人',
	`applicationDate` datetime DEFAULT '1753-01-01 00:00:00' COMMENT '申请日期',
	`isDeleted` bit ( 1 ) DEFAULT b'0' COMMENT '是否删除',
	`orgId` VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
	`creator` VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
	`createDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`domainId` VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
	`modifier` VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
	`modifyDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
PRIMARY KEY ( `id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8;


CREATE TABLE `tb_lim_training2participants`
(
    `id`             VARCHAR(50) NOT NULL COMMENT '主键',
    `trainingId`        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '培训id',
    `participantsId` VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参与人id',
    `participantsName` varchar(50) DEFAULT NULL COMMENT '参与人名称',
    PRIMARY KEY (`id`)
) ENGINE = INNODB DEFAULT CHARSET = utf8;