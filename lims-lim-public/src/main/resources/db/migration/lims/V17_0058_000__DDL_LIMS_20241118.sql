-- 流量校准与点位频次关联表
CREATE TABLE TB_PRO_FlowCalibration2Frequency
(
    id           varchar(50) NOT NULL COMMENT 'id',
    flowCalibrationId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '流量校准记录标识',
    sampleFolderId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位标识',
    periodCount int(11) NOT NULL DEFAULT 0 COMMENT '周期',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流量校准与点位频次关联表';

ALTER TABLE TB_PRO_FlowCalibration ADD COLUMN calibrationTypeName varchar(50)  COMMENT '校准类型名称';
