-- ----------------------------
-- 初始化点位类型拓展字段配置关联到的参数数据 TB_MONITOR_PointExtendConfig
-- ----------------------------
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('bbb33fb5-b62a-45ab-8777-d52a53190ebc', '', '所属水厂', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:19:40',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:19:40');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('27d548df-5f79-41c8-b550-0f35a5f762b0', '', '月取水量', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:10');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('4182bc30-5f1f-4568-9f21-9b9936bde787', '', '地下水类型代码', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:36', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:36');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('daae9fc2-e8e9-47ea-8ab9-f5b7b5b4d103', '', '测点参照物', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:04',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:04');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('ef29ae6f-f481-4a9d-be67-f788e2e272ad', '', '路段起始点', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:27:05',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:27:05');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('df906b33-2eeb-4138-ba3a-104381535d99', '', '域管断面级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:06:59', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:06:59');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('ff850282-e04f-489d-807e-36b636bf1fca', '', '所属河流', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:01:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:01:13');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('06efc1d8-01c4-41a0-b653-879b030f8065', '', '水域功能代码', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:04:24', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:04:24');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('fb9de5be-f704-4631-8df8-4482c3f8dddf', '', '地下水类型', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:20',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:20');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('d18d2122-ad54-47c9-a76c-d1e8a41501ec', '', '道路等级', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:15',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:15');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('3c23f209-de5e-4c33-8398-d95d6dc7dcda', '', '路段长宽', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:27:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:27:16');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('965aaa1c-3b60-4e6a-9887-d856d807a1fc', '', '网格边宽', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:20',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:20');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('b48ab82b-a8ec-4708-8863-4aa485802db8', '', '酸雨管测点级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:23:03', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:23:03');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('75a5409a-1074-43f1-bad1-b04ffb9c1a39', '', '路幅宽度', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:05',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:05');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('0fca2612-6e76-4555-8475-7ea313c4cee3', '', '网络覆盖人口', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:59', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:59');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('dbe3d412-8bb8-4e40-a3a7-13c06cbd1fda', '', '网管断面级别', '', 'watersl', '', '',
        '00000000-0000-0000-0000-000000000000', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-12 15:21:43', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-12 15:21:43');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('c695d4e0-dc94-41f9-b9ec-63ad536e3731', '', '昼间结束时', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:11',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:11');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('5f5e829b-989f-4327-9361-e91658b1797b', '', '大气网管测点级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:23:38', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:23:38');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('ed3c1eb8-9538-499a-b79f-e12e12d5fce7', '', '二氧化硫区管测点级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:22:17', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:22:17');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('9a740fa2-a360-4159-a8b8-515c4caf5632', '', '夜间起始时', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:37',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:37');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('327c7214-2ce7-4173-8f40-62e52d66633b', '', '噪声声源代码', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:32', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:32');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('4c521b60-5e0f-459c-8b65-aad32ccfff3d', '', '夜间结束时', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:49',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:49');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('1bc3f6df-3130-4f79-9b18-46ef9b508a4c', '', '交接断面管辖级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:09:59', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:09:59');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('04b0aa02-f2cb-47d2-b182-16985170cc9d', '', '昼间起始时', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:51');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('b3310c3a-e1ff-4910-ae72-0e030c998639', '', '网格边长', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:02',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:02');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('0ebf7ffa-167f-4013-840f-76c56f146ea6', '', '测点空气质量报告级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:47', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:47');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('f18aa23e-7ba9-4103-a7e6-c000d3fecff0', '', '路段名称', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:37',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:37');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('472846de-6b34-4d35-b6f0-d665fa8c9365', '', '所属湖库', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:15:01',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:15:01');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('29991d50-5fd5-4c69-b5a4-bb2d642157c6', '', '所属水体', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:32:30',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:32:30');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('55ad5d79-ddf9-432d-9739-f72ca2123f79', '', '噪声功能区代码', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:42', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:42');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex,
                          dimension, dimensionId, isDeleted, orgId, creator, createDate,
                          domainId, modifier, modifyDate)
VALUES ('a5dcef03-5fcd-421f-b9ac-9e23eb0938e3', '', '降水区管测点级别', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:17', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:17');


-- ----------------------------
-- 初始化点位类型拓展字段配置数据 TB_MONITOR_PointExtendConfig
-- ----------------------------
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('08da098e-7098-41ea-be91-1296357785a8', 'LIM_PointType_DrinkWater', 'bbb33fb5-b62a-45ab-8777-d52a53190ebc',
        '所属水厂', 'waterworks', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:02',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:22', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('1eb2b9a5-4248-4b7f-85d8-bfaca539f34d', 'LIM_PointType_DrinkWater', '27d548df-5f79-41c8-b550-0f35a5f762b0',
        '月取水量', 'ygwq', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:19',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:19', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('3a765ecd-fa98-4a89-9563-a8d0e6c1afd8', 'LIM_PointType_DXS', '4182bc30-5f1f-4568-9f21-9b9936bde787', '地下水类型代码',
        'underWaterTypeCode', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:36', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('51a02fdf-399f-4dce-837a-5cd941f5c561', 'LIM_PointType_GNQZS', 'daae9fc2-e8e9-47ea-8ab9-f5b7b5b4d103', '测点参照物',
        'refer', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:08', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:18', '', 0, '', '', '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('5c0a191d-692a-41a2-822e-e981c62bd7aa', 'LIM_PointType_JTZS', 'ef29ae6f-f481-4a9d-be67-f788e2e272ad', '路段起始点',
        'rdsecfromto', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:27:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:08', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('602a784a-a9c6-4dfa-ba39-23d80d4d2499', 'LIM_PointType_Lake', 'df906b33-2eeb-4138-ba3a-104381535d99', '域管断面级别',
        'areasl', '', 700, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_DomainSectionLevel', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:18:18',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:23', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('74a3944c-6e39-439f-9c4f-b81257f4ba29', 'LIM_PointType_River', 'ff850282-e04f-489d-807e-36b636bf1fca', '所属河流',
        'waterId', '', 1000, '[{\"key\":\"\",\"value\":\"\"}]', '', 11, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:04:02', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:36:27', 'monitor/water/getTree', 1, 'waterName', 'id',
        'chirdList');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('7bef919d-43b0-44b4-bc38-e42baeb4f0d9', 'LIM_PointType_Lake', '06efc1d8-01c4-41a0-b653-879b030f8065', '水域功能代码',
        'functionZoneCode', '', 900, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:16:02',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:08', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('8b6acc03-a12d-4e49-ba6e-4388007570b6', 'LIM_PointType_DXS', 'fb9de5be-f704-4631-8df8-4482c3f8dddf', '地下水类型',
        'underWaterType', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:24',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:39', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('8c92b2a7-da58-4999-9c5e-d7968d34e628', 'LIM_PointType_JTZS', 'd18d2122-ad54-47c9-a76c-d1e8a41501ec', '道路等级',
        'rdLevel', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:23', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:28', '', 0, '', '', '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('8eaad0ec-a14d-4c91-a3ee-7b450528681b', 'LIM_PointType_JTZS', '3c23f209-de5e-4c33-8398-d95d6dc7dcda', '路段长宽',
        'railwayLength', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:28:53',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:14', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('8f615dcd-723c-4cc8-96b2-75c86ef9b447', 'LIM_PointType_QYHJZS', '965aaa1c-3b60-4e6a-9887-d856d807a1fc', '网格边宽',
        'gridWidth', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:25', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:48', '', 0, '', '', '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('921b7934-5c92-4908-acd1-409ba90d8400', 'LIM_PointType_Air', 'b48ab82b-a8ec-4708-8863-4aa485802db8', '酸雨管测点级别',
        'acidpl', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_AcidRainLvl', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:23:27',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:19', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('9d4cb408-3e52-44e3-bfec-c36a31caef03', 'LIM_PointType_JTZS', '75a5409a-1074-43f1-bad1-b04ffb9c1a39', '路幅宽度',
        'railwayWidth', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:09',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:23', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('a462d288-5d11-429d-a283-622f2d66e820', 'LIM_PointType_QYHJZS', '0fca2612-6e76-4555-8475-7ea313c4cee3',
        '网络覆盖人口', 'gridCoverPeoples', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:32:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:42:05', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('a92725d2-ec1c-4c6a-9cdc-e978c1499cb7', 'LIM_PointType_River', 'dbe3d412-8bb8-4e40-a3a7-13c06cbd1fda', '网管断面级别',
        'watersl', '', 800, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_NetSectionLevel', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:06:00',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:48', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('ab74aeed-fa4c-4d6f-9383-e044ea658ef8', 'LIM_PointType_GNQZS', 'c695d4e0-dc94-41f9-b9ec-63ad536e3731', '昼间结束时',
        'dayhorend', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', '', 3, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:29', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:03', '', 0, '', '', '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('abeda2da-caae-4ebe-8109-e82ee08f5e3a', 'LIM_PointType_Air', '5f5e829b-989f-4327-9361-e91658b1797b', '大气网管测点级别',
        'airpl', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_AirNetLvl', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:07',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:25', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('b193834b-3a3e-41c0-9927-7319c5b72881', 'LIM_PointType_Air', 'ed3c1eb8-9538-499a-b79f-e12e12d5fce7',
        '二氧化硫区管测点级别', 'so2pl', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_SO2Lvl', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:22:36',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:12', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('b1aa3f99-689f-4de7-bd4d-a25015c6a7c5', 'LIM_PointType_GNQZS', '9a740fa2-a360-4159-a8b8-515c4caf5632', '夜间起始时',
        'nighthorbegin', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', '', 3, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:08', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('b2cef600-966e-49ed-acf1-cc5453c6a8b8', 'LIM_PointType_QYHJZS', '327c7214-2ce7-4173-8f40-62e52d66633b',
        '噪声声源代码', 'noiseSourceCode', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:36',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:42:00', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('b829b231-221d-4888-a772-fad7e5fea34d', 'LIM_PointType_River', '06efc1d8-01c4-41a0-b653-879b030f8065', '水域功能代码',
        'functionZoneCode', '', 900, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:04:48',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:40', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('c30e9f6b-ba9c-42a3-8bbb-11af23672e17', 'LIM_PointType_GNQZS', '4c521b60-5e0f-459c-8b65-aad32ccfff3d', '夜间结束时',
        'nighthorend', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', 3, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:57',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:14', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('c3bd8680-60fc-41cc-b2d6-f4f07582ffec', 'LIM_PointType_Lake', '1bc3f6df-3130-4f79-9b18-46ef9b508a4c',
        '交接断面管辖级别', 'rchal', '', 600, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_JoinSectionLevel', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:18:40',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:27', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('c8f7cf65-d143-49c1-aab2-7257ce2894dd', 'LIM_PointType_GNQZS', '04b0aa02-f2cb-47d2-b182-16985170cc9d', '昼间起始时',
        'dayhorbegin', '', 500, '[{\"key\":\"\",\"value\":\"\"}]', '', 3, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:56', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('cbed8be2-f7a6-45b5-a9f3-47f3534afef2', 'LIM_PointType_QYHJZS', 'b3310c3a-e1ff-4910-ae72-0e030c998639', '网格边长',
        'gridLength', '', 500, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:14', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:42', '', 0, '', '', '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('cc5b6944-a000-44c8-a5aa-3e8e32d227dd', 'LIM_PointType_Lake', 'dbe3d412-8bb8-4e40-a3a7-13c06cbd1fda', '网管断面级别',
        'watersl', '', 800, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_NetSectionLevel', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:17:35',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:16', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('ccea21ef-4cab-4720-a7f0-66228cc9d2ab', 'LIM_PointType_Air', '0ebf7ffa-167f-4013-840f-76c56f146ea6',
        '测点空气质量报告级别', 'weekCalcu', '', 500, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_AireQualityLvl', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:22:07',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:02', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('e4491bb7-a71d-41b8-872f-e3a4c06b8076', 'LIM_PointType_JTZS', 'f18aa23e-7ba9-4103-a7e6-c000d3fecff0', '路段名称',
        'rdsecName', '', 500, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:50', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:45', '', 0, '', '', '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('e4f5ce9a-488d-4ea8-b411-2955a7bab8cb', 'LIM_PointType_Lake', '472846de-6b34-4d35-b6f0-d665fa8c9365', '所属湖库',
        'waterId', '', 1000, '[{\"key\":\"\",\"value\":\"\"}]', '', 11, b'0', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:15:34', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:36:57', 'monitor/water/getTree', 1, 'waterName', 'id',
        'chirdList');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('e5ef1814-fec6-4bed-9837-50124bb420e1', 'LIM_PointType_River', '1bc3f6df-3130-4f79-9b18-46ef9b508a4c',
        '交接断面管辖级别', 'rchal', '', 600, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_JoinSectionLevel', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:10:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:58', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('e7e80983-fbdc-4494-a90e-bb68b0ebf5fd', 'LIM_PointType_River', 'df906b33-2eeb-4138-ba3a-104381535d99', '域管断面级别',
        'areasl', '', 700, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_DomainSectionLevel', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:07:41',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:02', '', 2, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('e9e40c36-ee06-436b-9ef4-5fbe51a54bdf', 'LIM_PointType_Sediment', '29991d50-5fd5-4c69-b5a4-bb2d642157c6',
        '所属水体', 'waterId', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', 11, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:33:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:42:12',
        'monitor/water/getTree', 1, 'waterName', 'id', 'chirdList');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('ee07edd0-f9a1-49cb-9a7d-1ed864db2d80', 'LIM_PointType_QYHJZS', '55ad5d79-ddf9-432d-9739-f72ca2123f79',
        '噪声功能区代码', 'noiseFunZoneCode', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', '', 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:46',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:56', '', 0, '', '',
        '');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue,
                                         orderNum, dataSource, codeDataSource, defaultControl, requiredInd, isDeleted,
                                         orgId, creator, createDate, domainId, modifier, modifyDate, dataSourceUrl,
                                         dataSourceType, urlReturnKey, urlReturnValue, treeChildFiled)
VALUES ('f06e758a-a061-41e4-b81b-be762690f399', 'LIM_PointType_Air', 'a5dcef03-5fcd-421f-b9ac-9e23eb0938e3', '降水区管测点级别',
        'acidp', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', 'LIM_RainLvl', 4, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:32',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:31', '', 2, '', '',
        '');