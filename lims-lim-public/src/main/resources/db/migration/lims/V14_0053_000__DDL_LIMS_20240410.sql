DROP TRIGGER IF EXISTS TRI_TB_PRO_Sample_InspectedEnt;

delimiter $$
CREATE TRIGGER TRI_TB_PRO_Sample_InspectedEnt BEFORE UPDATE ON TB_PRO_Sample FOR EACH ROW
BEGIN
    DECLARE var_inspectedEntId VARCHAR ( 50 );

	DECLARE var_inspectedEnt VARCHAR ( 255 );

	IF NEW.inspectedEntId <> OLD.inspectedEntId THEN
    -- 质控样 --
		IF old.projectId = '00000000-0000-0000-0000-000000000000' THEN
            SELECT
                p.inspectedEntId,
                p.inspectedEnt INTO var_inspectedEntId,
                var_inspectedEnt
            FROM
                TB_PRO_Sample r,
                TB_PRO_Project p,
                TB_LIM_ProjectType t,
                TB_PRO_QualityControl c
            WHERE
                    r.isDeleted = 0
              AND c.id = old.qcId
              AND c.associateSampleId = r.id
              AND p.isDeleted = 0
              AND p.id = r.projectId
              AND t.isDeleted = 0
              AND p.projectTypeId = t.id
              AND t.mark <> 'S';

        END IF;
        -- 原样 --
        IF old.projectId <> '00000000-0000-0000-0000-000000000000' THEN
            SELECT
                p.inspectedEntId,
                p.inspectedEnt INTO var_inspectedEntId,
                var_inspectedEnt
            FROM
                TB_PRO_Project p,
                TB_LIM_ProjectType t
            WHERE
                    p.isDeleted = 0
              AND p.id = old.projectId
              AND t.isDeleted = 0
              AND p.projectTypeId = t.id
              AND t.mark <> 'S';

        END IF;
		
		SET new.inspectedEntId = var_inspectedEntId;
		
		SET new.inspectedEnt = var_inspectedEnt;

END IF;

END $$
delimiter ;