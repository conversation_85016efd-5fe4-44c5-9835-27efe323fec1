
delete  from tb_lim_reportconfig where id = '05490313-1bac-418c-af7c-5123cf28f023';
INSERT INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('05490313-1bac-418c-af7c-5123cf28f023', 1, 'ReportExport', '报告查询导出模板.xlsx', '/LIMReportForms/报告查询导出模板.xlsx',
        'output/LIMReportForms/报告查询导出模板.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.ReportExportService', '{"sort":"createTime-"}', '', 0, 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-06-13 09:21:46',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-06-13 09:21:46',
        'com.sinoyd.lims.pro.criteria.ReportCriteria', 'LIMReportForms', 'export/ReportExport', b'0', '', NULL, '', '',
        '', 0, NULL);

delete  from tb_lim_reportapply where id = '1db62eb3-aa73-43af-9c17-986c933bfc05';
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('1db62eb3-aa73-43af-9c17-986c933bfc05', '05490313-1bac-418c-af7c-5123cf28f023', 'ReportQueryNew', '报告查询V2.0',
        'ReportExport', '导出报告', 0, 0, 1, '', '报告查询', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-06-13 09:27:15', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-06-13 09:27:15', NULL);
