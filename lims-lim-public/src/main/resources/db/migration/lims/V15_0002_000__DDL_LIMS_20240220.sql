CREATE TABLE tb_lim_testoperatelog (
                                  id varchar(50) NOT NULL  COMMENT 'id',
                                  tableName varchar(50) NOT NULL COMMENT '表名',
                                  tableId varchar(50) NOT NULL  COMMENT '表主键',
                                  operatorId varchar(50) NOT NULL  COMMENT '操作人标识',
                                  operatorDate datetime NOT NULL  COMMENT '操作时间',
                                  operateType int(10) NOT NULL  COMMENT '操作类型',
                                  operateField varchar(50)  COMMENT '操作字段',
                                  oldValue varchar(1000)  COMMENT '旧值',
                                  newValue varchar(1000)  COMMENT '新值',
                                  orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                  domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                  PRIMARY KEY (id)
) COMMENT='测试项目相关的表变动信息记录表';