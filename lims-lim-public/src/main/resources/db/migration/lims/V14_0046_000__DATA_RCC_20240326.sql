-- 地表水，地下水报告按采样日期分页
INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority) select uuid(), id, 'sampleData_samplingTimeBegin_dateGroupType', 9 from tb_lim_reportconfig2module where reportConfigId = (
    select reportConfigId from tb_lim_recordconfig where recordName = '地表水报告') and reportModuleId = (select id from tb_lim_reportmodule where moduleCode =  'groundWaterStdDataSource');

INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority) select uuid(), id, 'sampleData_samplingTimeBegin_dateGroupType', 9 from tb_lim_reportconfig2module where reportConfigId = (
    select reportConfigId from tb_lim_recordconfig where recordName = '地下水报告') and reportModuleId = (select id from tb_lim_reportmodule where moduleCode =  'groundWaterStdDataSource');