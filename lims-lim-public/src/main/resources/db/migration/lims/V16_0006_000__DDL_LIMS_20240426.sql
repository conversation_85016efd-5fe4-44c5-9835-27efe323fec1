-- ocr参数新增排序值
ALTER TABLE tb_lim_ocrconfigparam ADD COLUMN  orderNum int NOT NULL DEFAULT 0 COMMENT '排序值';

-- 评价信息
DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_PRO_EvaluationRecord' AND column_name = 'dimensionId')) THEN
        ALTER TABLE TB_PRO_EvaluationRecord
            ADD COLUMN dimensionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '量纲id';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;