-- 先删除旧的委托单应用场景配置
delete from tb_lim_reportapply
where id in (
             '04a9ba19-ba37-4754-9a66-7358603c1245',
             '5aa6adf6-ab1c-11ef-b551-c85b7631f195',
             '797b0867-86c2-406a-93b7-5d355b2ea2f7',
             'ed14283b-ab1b-11ef-b551-c85b7631f637'
    );

INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('04a9ba19-ba37-4754-9a66-7358603c1245', '65e28d8e-f693-4f4a-820a-224d7fe-3431', 'ProjectRegister', '普通类任务登记',
        'CommissionSheetSendSample', '委托单（送样）', 1, 0, 1, '', '项目登记:项目登记', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 11:04:30', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 11:04:30', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('5aa6adf6-ab1c-11ef-b551-c85b7631f195', 'f3bfd2b1-6357-4aab-8b99-9e6893f343ac', 'ProjectRegister', '普通类任务登记',
        'CommissionSheetSampling', '委托单（采样）', 1, 0, 1, '', '项目登记:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 12:48:39', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 12:48:39', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('797b0867-86c2-406a-93b7-5d355b2ea2f7', 'f3bfd2b1-6357-4aab-8b99-9e6893f343ac', 'ProjectRegister', '普通类任务登记',
        'CommissionSheetSampling', '委托单（采样）', 1, 0, 1, '', '项目登记:项目登记', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 12:48:39', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 12:48:39', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('ed14283b-ab1b-11ef-b551-c85b7631f637', '65e28d8e-f693-4f4a-820a-224d7fe-3431', 'ProjectRegister', '普通类任务登记',
        'CommissionSheetSendSample', '委托单（送样）', 1, 0, 1, '', '项目登记:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 11:04:30', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-09-07 11:04:30', NULL);

-- 样品交接单新增应用场景-电子表单
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('85141e4e-7092-4f11-9552-d491be5384d6', '11e986ca-8eca-45e9-b173-692069145836', 'SampleReceive', '样品交接',
        'SampleReceive', '交接单', 1, 0, 1, '', '样品交接:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-11-25 16:24:47', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-11-25 16:24:47', NULL);

