-- 假删原来的仪器出入库导入
UPDATE TB_LIM_ReportConfig
set isDeleted = 1
where id = '67963bc5-eac5-47ff-a926-857b7d1b7181';


INSERT
INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName,
                          returnType, method, params, pageConfig, orderNum, bizType,
                          remark, isDeleted, orgId, creator, createDate, domainId,
                          modifier, modifyDate, dataMethod, typeCode, strUrl,
                          isDefineFileName, defineFileName, beanName, versionNum, controlNum,
                          reportName, validate, usageNum)
VALUES ('7265cd9c-dbca-4ab3-94b2-3039cdddfb27', 1, 'ProjectInstrumentRecord', '仪器设备出入库记录表_模板.xlsx',
        'LIMReportForms/仪器设备出入库记录表_模板.xls', 'output/LIMReportForms/仪器设备出入库记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.ProjectInstrumentRecordService', '', '', 888, 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-03 10:04:14',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-03 10:04:14',
        'com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria', 'LIMReportForms', 'ProjectInstrumentRecord', b'0', '',
        NULL, '', '', '', 0, NULL);

