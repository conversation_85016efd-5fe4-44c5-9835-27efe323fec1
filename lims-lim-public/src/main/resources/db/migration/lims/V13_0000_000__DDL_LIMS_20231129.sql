-- 合同表新增甲方联系方式，备注
ALTER TABLE tb_pro_ordercontract ADD COLUMN firstEntPhone varchar(50) NOT NULL DEFAULT '' COMMENT '甲方联系方式';
ALTER TABLE tb_pro_ordercontract ADD COLUMN remark varchar(1000) DEFAULT '' COMMENT '备注';
-- 收款计划表新增收款项
ALTER TABLE tb_lim_contractcollectionplan ADD COLUMN collectItem int(10) NOT NULL DEFAULT -1 COMMENT '收款项';
-- 收款记录新增备注,是否开票，收款项，是否到款，到款日期
ALTER TABLE tb_lim_recandpayrecord ADD COLUMN remark varchar(1000) DEFAULT '' COMMENT '备注';
ALTER TABLE tb_lim_recandpayrecord ADD COLUMN hasInvoice bit(1) NOT NULL DEFAULT b'1' COMMENT '是否开票';
ALTER TABLE tb_lim_recandpayrecord ADD COLUMN isReceive bit(1) NOT NULL DEFAULT b'0' COMMENT '是否到款';
ALTER TABLE tb_lim_recandpayrecord ADD COLUMN collectItem int(10) NOT NULL DEFAULT -1 COMMENT '收款项';
ALTER TABLE tb_lim_recandpayrecord ADD COLUMN receiveDate datetime NOT NULL DEFAULT  '1753-01-01 00:00:00' COMMENT '到款日期';