-- 无组织报告分页方式调整
delete
from TB_LIM_ReportModule2Grouptype
where reportConfigModuleId = (select id
                              from TB_LIM_ReportConfig2Module
                              where reportConfigId =
                                    (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
                                and reportModuleId =
                                    (select id from TB_LIM_ReportModule where moduleCode = 'unOrgWeaToStdDataSource'));

delete
from TB_LIM_ReportModule2Grouptype
where reportConfigModuleId = (select id
                              from TB_LIM_ReportConfig2Module
                              where reportConfigId =
                                    (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
                                and reportModuleId =
                                    (select id from TB_LIM_ReportModule where moduleCode = 'unOrgGasStdDataSource'));

INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority)
select uuid(), id, 'sampleData_samplingTimeBegin_dateGroupType', 9
from TB_LIM_ReportConfig2Module
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'unOrgWeaToStdDataSource');

INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority)
select uuid(), id, 'sampleData_sampleFolderId_fieldGroupType', 10
from TB_LIM_ReportConfig2Module
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'unOrgWeaToStdDataSource');

--  新增有组织报告批次相关组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('a49934db-d45a-4176-b02c-185d858b1cc2', 'dtOrgToZsStdTable', '标准版有组织折算浓度检测结果表组件（批次）', 'dtOrgToZsStdTable',
        'dtOrgToZsSource', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:06:19', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:06:19', '1', '0', 0, 0);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('c2654afe-624b-4152-9055-784447c89a4a', 'orgToStdDataSource', '标准版有组织报告检测数据主表（批次）', 'dtDataSource', '', 0, 0,
        '[\"dtOrgSmkZsSpdDataSource\",  \"outParallelStdDataSource\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-27 11:18:39', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-04-27 11:18:39', '1', '0', 0, 0);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('2823a872-d972-49b5-9cb4-769c4028af32', 'dtOrgSmkZsSpdDataSource', '标准版有组织表头烟气参数样品检测数据主表（批次）',
        'dtOrgSmkZsSpdDataSource', '', 0, 0,
        '[\"dtOrgToHeadStdTable\", \"dtOrgSmkStdTable\", \"dtOrgToZsStdTable\", \"dtOrgToSpdStdTable\", \"dtOrgToCpdStdTable\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-04-26 17:22:44',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-04-26 17:22:44', '1', '0',
        0, 0);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('a33e5ca9-11d5-426e-91a1-f54140a5c2d3', 'dtOrgToSpdStdTable', '标准版有组织排放速率检测结果表组件（批次）', 'dtOrgToSpdStdTable',
        'dtOrgToSpdSource', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:07:02', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:07:02', '1', '0', 0, 0);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('d48a6879-fa80-4e61-99a8-d98d53d09ec1', 'dtOrgToHeadStdTable', '标准版有组织检测结果表头（批次）', 'dtOrgToHeadStdTable', '', 0,
        0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-04-26 16:54:43',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-04-26 16:54:43', '1', '0',
        0, 0);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('b6529d14-4f7f-42ba-82b7-278f822304c8', 'dtOrgToCpdStdTable', '标准版有组织化合物检测结果表组件（批次）', 'dtOrgToCpdStdTable',
        'dtOrgToCpdSource', 6, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:08:24', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:08:24', '1', '0', 0, 0);
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode)
VALUES ('b8844044-ae34-43f2-b70d-868a79a27115', 'dtOrgSmkStdTable', '标准版有组织烟气参数表组件（批次）', 'dtOrgSmkStdTable',
        'dtOrgSmkSource', 0, 4, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:04:58', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-26 17:04:58', '1', '0', 0, 0);

-- 有组织报告组件配置调整
update TB_LIM_ReportConfig2Module
set reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'orgToStdDataSource')
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'OrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'orgGasStdDataSource');

-- 有组织报告分页方式调整
delete
from TB_LIM_ReportModule2Grouptype
where reportConfigModuleId = (select id
                              from TB_LIM_ReportConfig2Module
                              where reportConfigId =
                                    (select id from TB_LIM_ReportConfig where reportCode = 'OrgGasStd')
                                and reportModuleId =
                                    (select id from TB_LIM_ReportModule where moduleCode = 'orgToStdDataSource'));

INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority)
select uuid(), id, 'sampleData_samplingTimeBegin_dateGroupType', 9
from TB_LIM_ReportConfig2Module
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'OrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'orgToStdDataSource');

INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority)
select uuid(), id, 'sampleData_sampleFolderId_fieldGroupType', 10
from TB_LIM_ReportConfig2Module
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'OrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'orgToStdDataSource');

-- 综合报告模板组件配置调整
update TB_LIM_ReportModule
set sonTableJson = '["normalWaterStdDataSource", "groundWaterStdDataSource", "groundWaterStdDataSource", "orgToStdDataSource", "unOrgWeaToStdDataSource", "solidStdDataSource", "noiseDayNightDataSource", "soilStdDataSource"]'
where moduleCode = 'comprehensiveStdDataSource';