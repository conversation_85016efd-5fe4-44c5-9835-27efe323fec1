-- 社会生活环境噪声检测原始记录报表应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('c4fddba6-4330-4eda-a568-d282b47fc187', '856db631-d6e3-4cd7-9269-91905856a734', 'LocalTask', '现场任务', '采样单',
        '社会生活环境噪声检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-05-18 17:20:42', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-05-18 17:20:42', 0);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('f257e0c4-3158-470f-ac73-2894a0438c51', '856db631-d6e3-4cd7-9269-91905856a734', 'PrepareSample', '采样准备', '采样单',
        '社会生活环境噪声检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-05-18 17:19:54', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-05-18 17:19:54', NULL);