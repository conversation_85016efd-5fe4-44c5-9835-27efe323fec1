-- 查新计划
CREATE TABLE TB_LIM_NewSearchPlan
(
    id            varchar(50)  NOT NULL COMMENT 'id',
    planName      varchar(100) NOT NULL COMMENT '计划名称',
    planType      int(11) NOT NULL COMMENT '计划类型',
    executor      varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '执行人',
    dealCycle     int(11) NOT NULL DEFAULT '0' COMMENT '执行周期EnumNewSearchPlanDealCycle',
    dealDate      int(11) NOT NULL COMMENT '每月执行日期(日)',
    taskStartDate datetime     NOT NULL COMMENT '任务开始日期',
    taskEndDate   datetime     NOT NULL COMMENT '任务结束日期',
    status        int(10) NOT NULL DEFAULT '0' COMMENT '计划状态 0:未提交 1:已提交',
    remark        varchar(255)          DEFAULT NULL COMMENT '备注',
    isDeleted     bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId         varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId      varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='查新计划';

-- 查新结果
CREATE TABLE TB_LIM_NewSearchResult
(
    id            varchar(50) NOT NULL COMMENT 'id',
    taskId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '查新任务id',
    isNewStandard bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否新标准',
    standardName  varchar(100)         DEFAULT NULL COMMENT '标准名称',
    standardNum   varchar(50)          DEFAULT NULL COMMENT '标准编号',
    year          varchar(50)          DEFAULT NULL COMMENT '年份',
    releaseDate   datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '发布日期',
    effectiveDate datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '实施日期',
    newSearchDate datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '查新日期',
    replaceNum    varchar(50)          DEFAULT NULL COMMENT '替代标准号',
    status        int(11) NOT NULL COMMENT '状态 0:新建 1:待处理 2:已处理',
    isConfirm     bit(1)               DEFAULT b'0' COMMENT '是否确认',
    confirmation  bit(1)               DEFAULT b'0' COMMENT '确认情况',
    isPropagate   bit(1)               DEFAULT b'0' COMMENT '是否宣贯',
    orgId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='查新结果';

-- 查新任务
CREATE TABLE TB_LIM_NewSearchTask
(
    id         varchar(50)  NOT NULL COMMENT 'id',
    taskName   varchar(100) NOT NULL COMMENT '任务名称',
    taskType   int(11) NOT NULL COMMENT '任务类型',
    status     int(11) NOT NULL COMMENT '任务状态 1:待处理 2:已处理',
    executor   varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '执行人',
    orgId      varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId   varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='查新任务';
