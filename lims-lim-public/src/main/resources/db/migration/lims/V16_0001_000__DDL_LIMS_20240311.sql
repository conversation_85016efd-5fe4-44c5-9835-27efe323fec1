DROP PROCEDURE IF EXISTS `add_table`;
DEL<PERSON>ITER $$
CREATE PROCEDURE add_table()
BEGIN
	declare table_count int;
    select count(*) into table_count from information_schema.tables where table_schema = database() and table_name = 'tb_pro_projectplan2person';
    if table_count = 0 THEN
            CREATE TABLE tb_pro_projectplan2person (
                 id varchar(50) NOT NULL  COMMENT 'id',
                 projectPlanId varchar(50) NOT NULL  COMMENT '项目计划id',
                 assessPersonId varchar(50) NOT NULL  COMMENT '考核人员id',
                 PRIMARY KEY (id)
            ) COMMENT='项目计划与考核人员(现场质控）关联表';
    END IF;
END $$
DELIMITER ;
CALL add_table ();
DROP PROCEDURE IF EXISTS `add_table`;