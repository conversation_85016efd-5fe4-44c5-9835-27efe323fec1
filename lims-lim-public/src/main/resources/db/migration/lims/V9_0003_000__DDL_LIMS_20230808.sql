DROP TABLE IF EXISTS TB_PRO_ProjectApproval;
CREATE TABLE TB_PRO_ProjectApproval
(
    id              varchar(50) NOT NULL COMMENT 'id',
    projectId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目id',
    approveDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '申请日期',
    modifyStatus    varchar(50) NULL DEFAULT '' COMMENT '修改状态',
    approvePersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '审核人员',
    comment         varchar(255) NULL DEFAULT '' COMMENT '意见',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS TB_PRO_SampleFolderTemplate;
CREATE TABLE TB_PRO_SampleFolderTemplate
(
    id              varchar(50) NOT NULL COMMENT 'id',
    approveId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目方案变更id',
    sampleFolderId  varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位id',
    operateType     int(11) NOT NULL DEFAULT 0 COMMENT '操作类型',
    watchSpot       varchar(100) NULL DEFAULT '' COMMENT '点位名称',
    sampleTypeId    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    redAnalyzeItems varchar(1000) NULL DEFAULT '' COMMENT '分析项目',
    lon             varchar(20) NULL DEFAULT '' COMMENT '经度',
    lat             varchar(20) NULL DEFAULT '' COMMENT '纬度',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS TB_PRO_SamplingFrequencyTemp;
CREATE TABLE TB_PRO_SamplingFrequencyTemp
(
    id                  varchar(50) NOT NULL COMMENT 'id',
    sampleFolderTempId  varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位修改方案id',
    samplingFrequencyId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位频次id',
    periodCount         int(11) NOT NULL DEFAULT 0 COMMENT '周期',
    timePerPeriod       int(11) NOT NULL DEFAULT 0 COMMENT '次数',
    operateType         int(11) NOT NULL DEFAULT 0 COMMENT '操作类型',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS TB_PRO_SamplingFrequencyTestTemp;
CREATE TABLE TB_PRO_SamplingFrequencyTestTemp
(
    id                      varchar(50) NOT NULL COMMENT 'id',
    samplingFrequencyTempId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位频次修改方案id',
    samplingFrequencyTestId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位频次测试项目关联表id',
    operateType             int(11) NOT NULL DEFAULT 0 COMMENT '操作类型',
    testId                  varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测试项目id',
    isSamplingOut           bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否采样分包',
    analyseItemId           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目id',
    analyzeMethodId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析方法id',
    redAnalyzeItemName      varchar(100) NULL DEFAULT '' COMMENT '分析项目名称',
    redAnalyzeMethodName    varchar(100) NULL DEFAULT '' COMMENT '分析方法名称',
    redCountryStandard      varchar(255) NULL DEFAULT '' COMMENT '国家标准',
    isCompleteField         bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否现场数据',
    isOutsourcing           bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否分包',
    PRIMARY KEY (id)
);