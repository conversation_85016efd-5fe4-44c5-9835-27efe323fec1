-- 环境院项目推送迁移产品相关表(上海环境院已存在）
DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
begin
	declare table_count int;
    select count(*) into table_count from information_schema.tables where table_schema = database() and table_name = 'TB_PRO_SHSamplingInstrument';
    if table_count = 0 THEN
        CREATE TABLE TB_PRO_SHSamplingInstrument
        (
            id           varchar(50) NOT NULL COMMENT 'id',
            taskId       varchar(50) NOT NULL DEFAULT '' COMMENT '采样任务id',
            instrumentId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器id',
            PRIMARY KEY (id)
        ) COMMENT = '监管平台采样仪器配置';
    END IF;
    select count(*) into table_count from information_schema.tables where table_schema = database() and table_name = 'TB_PRO_SHSamplingPerson';
    if table_count = 0 THEN
        CREATE TABLE TB_PRO_SHSamplingPerson
        (
            id       varchar(50) NOT NULL COMMENT 'id',
            taskId   varchar(50) NOT NULL DEFAULT '' COMMENT '采样任务id',
            personId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员id',
            PRIMARY KEY (id)
        ) COMMENT = '监管平台采样人员配置';
    END IF;
    select count(*) into table_count from information_schema.tables where table_schema = database() and table_name = 'TB_PRO_ProjectContract';
    if table_count = 0 THEN
        CREATE TABLE TB_PRO_ProjectContract
        (
            id            varchar(50) NOT NULL COMMENT '主键',
            contractId    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '合同id',
            projectId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目id',
            contractName  varchar(50) NULL DEFAULT NULL COMMENT '合同名称',
            taskPrice     decimal(18, 2) NULL COMMENT '任务金额',
            taskTypeId    varchar(50) NULL NULL COMMENT '任务类型id',
            taskType      varchar(50) NULL DEFAULT NULL COMMENT '任务类型',
            isSampleId    varchar(50) NULL DEFAULT NULL COMMENT '是否采样id',
            isSample      varchar(20) NULL DEFAULT NULL COMMENT '是否采样',
            taskSourceId  varchar(50) NULL COMMENT '任务来源id',
            taskSource    varchar(20) NULL DEFAULT NULL COMMENT '任务来源',
            taskLocation  varchar(1000) NULL DEFAULT NULL COMMENT '任务所在地',
            sampleContact varchar(50) NULL DEFAULT NULL COMMENT '采样联系人',
            contactPhone  varchar(20) NULL DEFAULT NULL COMMENT '联系电话',
            taskAddress   varchar(50) NULL DEFAULT NULL COMMENT '任务地址',
            fileExplain   varchar(255) NULL DEFAULT NULL COMMENT '附件说明',
            taskContent   text NULL COMMENT '任务概述',
            isPush        int(11) NULL DEFAULT 0 COMMENT '是否推送（0：否，1：是）',
            pId           varchar(50) COMMENT '上海监测站项目id',
            hasPush       int(11) NULL DEFAULT 0 COMMENT '是否已经推送（0：否，1：是）',
            isHandle      int(11) NULL DEFAULT 0 COMMENT '是否处理（0：否，1：是）',
            schemeHasPush int(11) NULL DEFAULT 0 COMMENT '方案是否已经推送（0：否，1：是）',
            planHasPush   int(11) NULL DEFAULT 0 COMMENT '计划是否已经推送（0：否，1：是）',
            reportHasPush int(11) NULL DEFAULT 0 COMMENT '报告是否已经推送（0：否，1：是）'
        );
    END IF;
END $$
DELIMITER ;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;


