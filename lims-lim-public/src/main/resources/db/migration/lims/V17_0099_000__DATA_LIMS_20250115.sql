-- 标准版报告添加废水带限值组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('157ea80b-702f-4da1-af3d-ef7d65e84a33', 'dtWasteWaterLmtStdTable', '标准版废水带限值检测结果表组件', 'dtWasteWaterLmtStdTable',
        'dtWasteWaterLmtSource', 3, 10, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-01-14 16:53:51', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-01-14 16:53:51', '0', '0', 0, 0, 0, 1);