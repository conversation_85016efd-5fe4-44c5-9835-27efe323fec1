-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关初始化数据脚本 -------
-- -------- 定时服务相关初始化数据 -------------------
-- ----------------------------------------------

INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('112abe4f-4679-4148-995a-1b424c5f6d3f', '消耗品标准样品过期提醒', 'MESSAGE', 'consumableOverdue.sendMsg(\'消耗品标准样品过期提醒\')', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:19:43', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:19:43');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8c11a244-0598-4422-a4fa-9cb3b22c6182', '数据查询服务', 'DEFAULT', 'dataProcessTask.process()', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:12:43', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:12:43');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('9b3c8d9a-5ffd-43d5-ae2c-5b5b6c2928c2', '消耗品标准样品库存提醒', 'MESSAGE', 'consumableStorage.sendMsg(\'消耗品标准样品库存提醒\')', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:18:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:18:47');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b9158aeb-d742-48d2-a07b-4dec718f0794', '仪器维护超期提醒', 'MESSAGE', 'instrumentMaintain.sendMsg(\'仪器维护超期提醒\')', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:15:14', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:15:14');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d9b9f187-427d-4278-90e9-9577e107c2da', '消耗品标准样品即将过期提醒', 'MESSAGE', 'consumableWillOverdue.sendMsg(\'消耗品标准样品即将过期提醒\')', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:16:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:16:02');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('de1be52d-3a79-4a52-a71f-f189af098dc4', '人员上岗证过期提醒', 'MESSAGE', 'jobCertificate.sendMsg(\'人员上岗证过期提醒\')', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:13:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:13:33');
INSERT INTO TB_BASE_Job (id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('fa862b06-17eb-41bd-94e9-cbaa22f2acf1', '仪器检定校准超期提醒', 'MESSAGE', 'instrumentCheck.sendMsg(\'仪器检定校准超期提醒\')', '0 0/15 * * * ?', 3, false, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:14:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:14:23');

INSERT INTO TB_BASE_JobInfo (id, jobId, beginTime, endTime, isIncludeDefault, remindDays, sendType, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1d82834f-1786-41fa-b97b-7588f2f3442a', 'fa862b06-17eb-41bd-94e9-cbaa22f2acf1', '08:00', '20:00', true, 30, 1, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:14:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:14:23');
INSERT INTO TB_BASE_JobInfo (id, jobId, beginTime, endTime, isIncludeDefault, remindDays, sendType, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('3c978e37-4313-477f-a00e-705153ee1da3', '9b3c8d9a-5ffd-43d5-ae2c-5b5b6c2928c2', '08:00', '20:00', true, 30, 1, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:18:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:18:47');
INSERT INTO TB_BASE_JobInfo (id, jobId, beginTime, endTime, isIncludeDefault, remindDays, sendType, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('51e99c9e-ac95-482b-99c2-3c5c81c8b658', 'b9158aeb-d742-48d2-a07b-4dec718f0794', '08:00', '20:00', true, 30, 1, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:15:14', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:15:14');
INSERT INTO TB_BASE_JobInfo (id, jobId, beginTime, endTime, isIncludeDefault, remindDays, sendType, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('690f797b-e322-45a1-a7ef-bc858b1c5f5b', 'd9b9f187-427d-4278-90e9-9577e107c2da', '08:00', '', true, 30, 1, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:16:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:16:02');
INSERT INTO TB_BASE_JobInfo (id, jobId, beginTime, endTime, isIncludeDefault, remindDays, sendType, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('bc683fc7-771f-42b3-bf3c-b16b76ed773b', '112abe4f-4679-4148-995a-1b424c5f6d3f', '08:00', '', true, 30, 1, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:19:43', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:19:43');
INSERT INTO TB_BASE_JobInfo (id, jobId, beginTime, endTime, isIncludeDefault, remindDays, sendType, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('dde7a2b3-b91e-42b4-8a55-803efcb0c2ea', 'de1be52d-3a79-4a52-a71f-f189af098dc4', '08:00', '20:00', false, 30, 1, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:13:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-13 16:13:33');
