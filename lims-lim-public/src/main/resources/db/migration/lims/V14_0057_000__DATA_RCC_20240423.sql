-- 新增无组织报告批次相关组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('2a89e1a2-201d-47cf-8b0c-f435f58ed218', 'dtUnOrgWeaHeadStdTable', '标准版无组织气象条件表头组件 （批次）',
        'dtUnOrgWeaHeadStdTable', '', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:23:24', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:23:24', '1', '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('521ca0a9-ecd9-427a-9dd2-56834c0c2578', 'dtUnOrgToHeadStdTable', '标准版无组织检测结果表头组件 （批次）', 'dtUnOrgToHeadStdTable',
        '', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-19 10:13:31', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-19 10:13:31', '1', '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('52d85129-2878-46fe-bdc0-6162f6140248', 'unOrgToStdDataSource', '标准版无组织报告样品数据主表（批次）', 'dtUnOrgToDataSource', '',
        0, 0,
        '[\"dtUnOrgToHeadStdTable\", \"dtUnOrgToStdTable\", \"dtUnOrgToCpdStdTable\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-04-19 10:32:26',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-04-19 10:32:26', '1',
        '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('6197edbb-43de-4c89-aa55-7f687d021455', 'dtUnOrgToStdTable', '标准版无组织检测结果表组件（批次）', 'dtUnOrgToStdTable',
        'dtUnOrgToSource', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-19 10:11:40', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-19 10:11:40', '1', '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('b1c8a053-0fd0-4869-a3c0-7172eae2346d', 'dtUnOrgToCpdStdTable', '标准版无组织化合物检测结果表组件（批次）', 'dtUnOrgToCpdStdTable',
        'dtUnOrgToCpdSource', 6, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:15:33', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:15:33', '1', '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('b9c0336e-0497-4108-b811-7b49848a588f', 'dtUnOrgWeaStdTable', '标准版无组织气象条件表组件（批次）', 'dtUnOrgWeaStdTable',
        'dtUnOrgWeaSource', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-04-19 10:24:13', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-04-19 10:24:13', '1', '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('e58dc0e5-06f2-498e-ad02-596ba4dafc8e', 'unOrgWeaStdDataSource', '标准版无组织报告气象条件数据主表（批次）', 'dtUnOrgWeaDataSource',
        '', 0, 0, '[\"dtUnOrgWeaHeadStdTable\", \"dtUnOrgWeaStdTable\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:36:09', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:36:09', '1', '0');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument)
VALUES ('5bfeb24e-2589-4c9f-af29-2beab9cd1a2f', 'unOrgWeaToStdDataSource', '标准版无组织报告检测数据主表（批次）', 'dtDataSource', '', 0,
        0, '[\"unOrgWeaStdDataSource\", \"unOrgToStdDataSource\",  \"outParallelStdDataSource"\]', b'1',
        '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:52:51', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-04-19 10:52:51', '1', '0');

-- 无组织报告组件配置调整
update TB_LIM_ReportConfig2Module
set reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'unOrgWeaToStdDataSource')
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'unOrgGasStdDataSource');

-- 无组织报告分页方式调整
INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority)
select uuid(), id, 'sampleData_samplingTimeBegin_dateGroupType', 9
from TB_LIM_ReportConfig2Module
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'unOrgWeaToStdDataSource');

INSERT INTO TB_LIM_ReportModule2Grouptype(id, reportConfigModuleId, groupTypeName, priority)
select uuid(), id, 'sampleData_sampleFolderId_fieldGroupType', 10
from TB_LIM_ReportConfig2Module
where reportConfigId = (select id from TB_LIM_ReportConfig where reportCode = 'UnOrgGasStd')
  and reportModuleId = (select id from TB_LIM_ReportModule where moduleCode = 'unOrgWeaToStdDataSource');

-- 综合报告模板组件配置调整
update TB_LIM_ReportModule
set sonTableJson = '["normalWaterStdDataSource", "groundWaterStdDataSource", "groundWaterStdDataSource", "orgGasStdDataSource", "unOrgWeaToStdDataSource", "solidStdDataSource", "noiseDayNightDataSource", "soilStdDataSource"]'
where moduleCode = 'comprehensiveStdDataSource';