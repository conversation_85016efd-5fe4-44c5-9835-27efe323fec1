-- 曲线
ALTER TABLE TB_LIM_Curve ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 校准曲线
ALTER TABLE TB_PRO_WorkSheetCalibrationCurve ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurve ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurve ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurve ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurve ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurve ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 校准曲线详情
ALTER TABLE TB_PRO_WorkSheetCalibrationCurveDetail ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurveDetail ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurveDetail ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurveDetail ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurveDetail ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_WorkSheetCalibrationCurveDetail ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 评价
ALTER TABLE TB_PRO_EvaluationRecord ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_EvaluationRecord ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_EvaluationRecord ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_EvaluationRecord ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_EvaluationRecord ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_EvaluationRecord ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 仪器使用记录
ALTER TABLE TB_LIM_InstrumentUseRecord ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 分包数据
ALTER TABLE TB_PRO_OutSourceData ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 参数数据
ALTER TABLE TB_PRO_ParamsData ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_ParamsData ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_ParamsData ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_ParamsData ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_ParamsData ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_ParamsData ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 质控信息
ALTER TABLE TB_PRO_QualityControl ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_QualityControl ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_QualityControl ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_QualityControl ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_QualityControl ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_QualityControl ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 质控评价信息表
ALTER TABLE TB_PRO_QualityControlEvaluate ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 报告
ALTER TABLE TB_PRO_Report ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';--
ALTER TABLE TB_PRO_ReportBaseInfo ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_ReportFolderInfo ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_ReportFolderSortInfo ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_ReportSampleInfo ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 点位信息表
ALTER TABLE TB_PRO_SampleFolder ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_SampleFolder ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_SampleFolder ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_SampleFolder ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_SampleFolder ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_SampleFolder ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 送样单
ALTER TABLE TB_PRO_ReceiveSampleRecord ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 样品分组
ALTER TABLE TB_PRO_SampleGroup ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
--
ALTER TABLE TB_PRO_SamplePreparation ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 采样单
ALTER TABLE TB_PRO_ReceiveSubSampleRecord ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 领样单样品关联表
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
ALTER TABLE TB_PRO_ReceiveSubSampleRecord2Sample ADD COLUMN orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
-- 工作单
ALTER TABLE TB_PRO_WorkSheetFolder ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
-- 工作单配置试剂配置
ALTER TABLE TB_PRO_WorkSheetReagent ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_WorkSheetReagent ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_WorkSheetReagent ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_WorkSheetReagent ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_WorkSheetReagent ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';
-- 工作单子表
ALTER TABLE TB_PRO_WorkSheet ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_WorkSheet ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_WorkSheet ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_WorkSheet ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_WorkSheet ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_WorkSheet ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';

-- 分析数据公式
ALTER TABLE TB_PRO_AnalyseOriginalRecord ADD COLUMN isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段';
ALTER TABLE TB_PRO_AnalyseOriginalRecord ADD COLUMN createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE TB_PRO_AnalyseOriginalRecord ADD COLUMN creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人';
ALTER TABLE TB_PRO_AnalyseOriginalRecord ADD COLUMN domainId VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室';
ALTER TABLE TB_PRO_AnalyseOriginalRecord ADD COLUMN modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人';
ALTER TABLE TB_PRO_AnalyseOriginalRecord ADD COLUMN modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间';