
delete from t_sys_config where id = 'db9df92d0efb4f088c23b22612a60b51';
INSERT INTO `t_sys_config`(`id`, `configName`, `configKey`, `configValue`, `enabled`, `sortNum`, `note`, `createDate`, `createUserGuid`, `createUserName`,
                           `updateDate`, `updateUserGuid`, `updateUserName`, `orgGuid`, `deptGuid`, `deleted`)
                           VALUES ('db9df92d0efb4f088c23b22612a60b51', '更新现场参数是否审核通过', 'sys.receive.params.audio',
                                   'false', b'1', 0, '当开关打开，实验室通过“更新现场参数”获取数据的时候，需要判断下送样单的状态，只有送样单审核通过后，才允许同步数据。否则需要给出提示：“现场数据还未审核通过，无法获取！”。开关关闭，同当前工功能保持一致，直接获取即可。',
                                   '2025-07-09 09:08:58', '59141356591b48e18e139aa54d9dd351', '超级管理员', NULL, NULL, NULL,
                                   '5f7bcf90feb545968424b0a872863876', NULL, b'0');
