DROP TABLE IF EXISTS TB_PRO_LogForAnalyzeMethod;
CREATE TABLE TB_PRO_LogForAnalyzeMethod
(
    id           varchar(50) NOT NULL COMMENT 'id',
    operatorId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '操作者Id',
    operatorName varchar(50) NULL DEFAULT NULL COMMENT '操作者名字',
    operateTime  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '操作时间',
    operateInfo  varchar(500) NULL DEFAULT NULL COMMENT '操作信息',
    objectId     varchar(50) NOT NULL DEFAULT '' COMMENT '分析方法id',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    PRIMARY KEY (id) USING BTREE,
    INDEX        IX_TB_PRO_LogForAnalyzeMethod(objectId, orgId) USING BTREE
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_PRO_LogForAnalyzeMethod'
    COMMENT = '分析方法状态日志';