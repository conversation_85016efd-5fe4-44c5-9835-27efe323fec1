-- --------------------------------------------------------
-- 创建评价标准分析项项目关联表映射
-- --------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_EvaluationAnalyzeItem;
CREATE TABLE TB_BASE_EvaluationAnalyzeItem
(
    id            varchar(50) NOT NULL COMMENT '主键',
    evaluationId  varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '评价标准id',
    analyzeItemId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目id',
    symbol        varchar(50) NULL DEFAULT NULL COMMENT '符号(3.2预留)',
    unit          varchar(50) NULL DEFAULT NULL COMMENT '单位(3.2预留)',
    remark        varchar(1000) NULL DEFAULT NULL COMMENT '备注(3.2预留)',
    orderNum      int(11) NOT NULL DEFAULT 0 COMMENT '排序值(3.2预留',
    orgId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id) USING BTREE
)ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_EvaluationAnalyzeItem'
    COMMENT '评价标准分析项项目关联表';

-- --------------------------------------------------------
-- 创建评价标准表映射
-- --------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_EvaluationCriteria;
CREATE TABLE TB_BASE_EvaluationCriteria
(
    id           varchar(50) NOT NULL COMMENT '主键',
    name         varchar(100) NULL DEFAULT NULL COMMENT '评价标准名称',
    code         varchar(20) NULL DEFAULT NULL COMMENT '标准代码',
    categoryId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '标准类型（常量BASE_EvaluateType：国标和地标）',
    sampleTypeId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型',
    startTime    datetime(0) NULL DEFAULT '1753-01-01 00:00:00' COMMENT '实施时间',
    endTime      datetime(0) NULL DEFAULT '1753-01-01 00:00:00' COMMENT '废止时间',
    status       int(11) NOT NULL DEFAULT 1 COMMENT '标准状态（枚举EnumEvaluateCriteriaStatus：1代表有效 2代表废止）',
    applyRange   varchar(1000) NULL DEFAULT NULL COMMENT '适用范围',
    remark       varchar(1000) NULL DEFAULT NULL COMMENT '备注',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    criteriaType int(10) NOT NULL DEFAULT 1 COMMENT '标准类别,关联枚举EnumBase.EnumEvaluationCriteriaType',
    PRIMARY KEY (id) USING BTREE
)ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_EvaluationCriteria'
    COMMENT '评价标准表';


-- --------------------------------------------------------
-- 创建评价标准等级表映射
-- --------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_EvaluationLevel;
CREATE TABLE TB_BASE_EvaluationLevel
(
    id           varchar(50) NOT NULL COMMENT '主键',
    evaluationId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '评价标准id',
    parentId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '父级id',
    name         varchar(255) NULL DEFAULT NULL COMMENT '等级名称（条件项名称）',
    describion   varchar(1000) NULL DEFAULT NULL COMMENT '条件描述',
    orderNum     int(11) NOT NULL DEFAULT 0 COMMENT '排序值（条件编码）',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id) USING BTREE
)ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_EvaluationLevel'
    COMMENT '评价标准等级表';


-- --------------------------------------------------------
-- 创建评价标准限值表映射
-- --------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_EvaluationValue;
CREATE TABLE TB_BASE_EvaluationValue
(
    id               varchar(50) NOT NULL COMMENT '主键',
    evaluationId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '评价标准id',
    levelId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '评价等级id',
    analyzeItemId    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目id',
    upperLimit       varchar(50) NULL DEFAULT NULL COMMENT '上限',
    upperLimitSymble varchar(50) NULL DEFAULT NULL COMMENT '上限运算符',
    lowerLimit       varchar(50) NULL DEFAULT NULL COMMENT '下限',
    lowerLimitSymble varchar(50) NULL DEFAULT NULL COMMENT '下限运算符',
    remark           varchar(1000) NULL DEFAULT NULL COMMENT '备注',
    orgId            varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate       datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate       datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    dimensionId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '量纲id',
    emissionRate     varchar(10) NULL DEFAULT NULL COMMENT '排放速率',
    PRIMARY KEY (id) USING BTREE
)ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_EvaluationValue'
    COMMENT '评价标准限值表';