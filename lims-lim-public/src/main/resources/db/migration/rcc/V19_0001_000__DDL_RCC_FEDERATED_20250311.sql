-- ---------------------------------------------------------------
-- 创建分析项目表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_AnalyzeItem;
CREATE TABLE TB_BASE_AnalyzeItem
(
    id              VARCHAR(50) NOT NULL COMMENT '主键',
    analyzeItemName VARCHAR(50) COMMENT '名称',
    analyzeItemCode VARCHAR(50) COMMENT '分析因子编号',
    variableName    VARCHAR(50) COMMENT '变量名称（预留，前台改为别名）',
    pinYin          VARCHAR(50) COMMENT '拼音缩写',
    fullPinYin      VARCHAR(100) COMMENT '全拼',
    isDeleted       BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orderNum        INT(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    orgId           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    casNum          VARCHAR(50) COMMENT 'CAS号',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_AnalyzeItem'
    COMMENT = '分析项目表';

-- ---------------------------------------------------------------
-- 创建量纲表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_Dimension;
CREATE TABLE TB_BASE_Dimension
(
    id              VARCHAR(50)     NOT NULL COMMENT '主键',
    dimensionName   VARCHAR(50)     NOT NULL COMMENT '量纲名称',
    dimensionTypeId VARCHAR(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '量纲类型（使用常量Guid，常量名称BASE_DimensionType）',
    code            VARCHAR(50) COMMENT '编号',
    remark          VARCHAR(1000) COMMENT '备注',
    baseValue       DECIMAL(38, 10) NOT NULL COMMENT '基准值',
    isDeleted       BIT(1)          NOT NULL DEFAULT b'0' COMMENT '假删',
    orgId           VARCHAR(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         VARCHAR(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId        VARCHAR(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        VARCHAR(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    orderNum        INT(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_Dimension'
    COMMENT = '量纲表';

-- ---------------------------------------------------------------
-- 创建行业类型表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_IndustryType;
CREATE TABLE TB_BASE_IndustryType
(
    id           VARCHAR(50) NOT NULL COMMENT '主键',
    parentId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '父级Id（Guid）（预留：多级行业类型使用）',
    industryName VARCHAR(50) COMMENT '名称',
    industryCode VARCHAR(20) COMMENT '编号',
    isDeleted    BIT(1)      NOT NULL DEFAULT b'0' COMMENT '假删',
    orderNum     INT(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    remark       VARCHAR(1000) COMMENT '备注',
    orgId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_IndustryType'
    COMMENT = '行业类型表';

-- ---------------------------------------------------------------
-- 创建质控限值表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_QualityControlLimit;
CREATE TABLE TB_BASE_QualityControlLimit
(
    id             VARCHAR(50) NOT NULL COMMENT '主键',
    testId         VARCHAR(50) NOT NULL COMMENT '测试项目标识',
    rangeConfig    VARCHAR(50) COMMENT '检查项范围',
    judgingMethod  INT(11) NOT NULL DEFAULT 1 COMMENT '评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）',
    allowLimit     VARCHAR(50) COMMENT '允许限值',
    qcGrade        INT(11) NOT NULL DEFAULT -1 COMMENT '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）',
    qcType         INT(11) NOT NULL DEFAULT -1 COMMENT '质控类型（枚举EnumQCType：1.平行,2.空白,4.加标,8.标准,16.原样加原样,32.串联样,64.曲线校核,128.洗涤剂,256.运输空白,512.仪器空白,1024.试剂空白,2048.罐空白,4096.校正系数检验,8192.替代物,16384.阴性对照试验,32768.阳性对照试验,65536.采样介质空白,131072.空白加标;）',
    qcTypeName     VARCHAR(50) COMMENT '质控类型名称',
    substituteId   VARCHAR(50) NOT NULL COMMENT '替代物标识',
    substituteName VARCHAR(50) COMMENT '代替物名称',
    formula        VARCHAR(50) COMMENT '穿透率公式',
    checkItem      INT(11) NOT NULL DEFAULT 1 COMMENT '检查项（枚举EnumCheckItemType:1.出证结果，2.公式参数）',
    checkItemOther VARCHAR(50) COMMENT '检查项内容',
    isCheckItem    INT(11) NOT NULL DEFAULT 1 COMMENT '是否需要检查项',
    orgId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    description    VARCHAR(255) COMMENT '技术说明',
    validate       INT(11) DEFAULT 0 COMMENT '验证状态 0未验证 1已验证',
    usageNum       INT(11) COMMENT '使用次数',
    dispositionId  VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '配置id',
    uncertainType  INT(11) NOT NULL DEFAULT 10 COMMENT '不确定度类型,关联EnumUncertainType',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_QualityControlLimit'
    COMMENT = '质控限值表';

-- ---------------------------------------------------------------
-- 创建质控限制公式配置映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_QualityLimitDisposition;
CREATE TABLE TB_BASE_QualityLimitDisposition
(
    id            VARCHAR(50) NOT NULL COMMENT 'id',
    qcGrade       INT(11) NOT NULL COMMENT '质控等级',
    qcType        INT(11) NOT NULL COMMENT '质控类型',
    formula       VARCHAR(255) COMMENT '公式',
    judgingMethod INT(11) NOT NULL COMMENT '评判方式',
    isAcquiesce   BIT(1) COMMENT '是否默认',
    orgId         VARCHAR(50) COMMENT '组织机构id',
    creator       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_QualityLimitDisposition'
    COMMENT = '质控限制公式配置表';

-- ---------------------------------------------------------------
-- 创建替代物信息映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_BASE_Substitute;
CREATE TABLE TB_BASE_Substitute
(
    id            VARCHAR(50)  NOT NULL COMMENT '主键',
    casCode       VARCHAR(50)  NOT NULL COMMENT 'CAS号',
    compoundName  VARCHAR(100) NOT NULL COMMENT '化合物名称',
    addition      VARCHAR(50)  NOT NULL COMMENT '加入量',
    isDeleted     BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId         VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    DATETIME     NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '创建时间',
    domainId      VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    modifier      VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '最近修改人',
    modifyDate    DATETIME     NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '最新修改时间',
    dimensionId   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '量纲id',
    dimensionName VARCHAR(100) COMMENT '量纲名称',
    PRIMARY KEY (id, compoundName)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_BASE_Substitute'
    COMMENT = '替代物信息';

-- ---------------------------------------------------------------
-- 创建分析项目关系表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_AnalyzeItemRelation;
CREATE TABLE TB_LIM_AnalyzeItemRelation
(
    id              VARCHAR(50) NOT NULL COMMENT 'id',
    analyzeItemId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目Id',
    analyzeItemName VARCHAR(50) COMMENT '分析项目名称',
    formula         VARCHAR(500) COMMENT '公式',
    configDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '配置日期',
    type            INT(11) NOT NULL DEFAULT -1 COMMENT '类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）',
    orgId           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_AnalyzeItemRelation'
    COMMENT = '分析项目关系表';

-- ---------------------------------------------------------------
-- 创建分析项目关系参数表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_AnalyzeItemRelationParams;
CREATE TABLE TB_LIM_AnalyzeItemRelationParams
(
    id              VARCHAR(50) NOT NULL COMMENT 'id',
    relationId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目关系',
    analyzeItemId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目Id',
    analyzeItemName VARCHAR(50) COMMENT '分析项目名称',
    orderNum        INT(11) NOT NULL DEFAULT 0 COMMENT '排序值（预留：列表显示排序用）',
    orgId           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_AnalyzeItemRelationParams'
    COMMENT = '分析项目关系参数表';

-- ---------------------------------------------------------------
-- 创建分析方法表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_AnalyzeMethod;
CREATE TABLE TB_LIM_AnalyzeMethod
(
    id                  VARCHAR(50) NOT NULL COMMENT 'id',
    methodName          VARCHAR(255) COMMENT '方法名称',
    countryStandard     VARCHAR(200) COMMENT '',
    isDeleted           BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    effectiveDate       DATETIME    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '标准实施日期',
    methodCode          VARCHAR(50) COMMENT '受控编号',
    isCompleteTogether  BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否可以同时完成',
    isControlled        BIT(1)      NOT NULL DEFAULT b'1' COMMENT '是否受控',
    remark              VARCHAR(1000) COMMENT '备注',
    parentId            VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '父级Id（Guid）（预留，例：XXX方法，拉伸测试）',
    countryStandardName VARCHAR(100) COMMENT '国家标准名称（预留）',
    yearSn              VARCHAR(50) COMMENT '年度（预留）',
    effectiveDays       INT(11) NOT NULL DEFAULT -1 COMMENT '有效天数（预留）',
    warningDays         INT(11) NOT NULL DEFAULT -1 COMMENT '警告天数（预留）',
    isInforce           BIT(1)      NOT NULL DEFAULT b'1' COMMENT '是否现行有效（预留）',
    orgId               VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator             VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId            VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier            VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    isInputBySample     BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否按样品录入 1：是  0：否',
    alias               VARCHAR(50) COMMENT '别名',
    isCrossDay          BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否跨天完成',
    isPreparation       BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否制备',
    preparedMethod      VARCHAR(200) COMMENT '制备方法',
    status              INT(11) NOT NULL DEFAULT 1 COMMENT '方法状态EnumAnalyzeMethodStatus：启用(1),停用(2),废止(3)',
    isSamplingMethod    BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否采样方法',
    sampleTypeId        VARCHAR(50)          DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_AnalyzeMethod'
    COMMENT = '分析方法表';

-- ---------------------------------------------------------------
-- 创建日历日期映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_CalendarDate;
CREATE TABLE TB_LIM_CalendarDate
(
    id           VARCHAR(50) NOT NULL COMMENT '主键',
    holidayName  VARCHAR(50) COMMENT '节假日名称',
    calendarDate DATE        NOT NULL COMMENT '日历日期',
    weekday      INT(11) NOT NULL COMMENT '星期数（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六）',
    type         INT(11) NOT NULL COMMENT '类型（0：工作日，1：休息日）',
    orgId        VARCHAR(50) NOT NULL COMMENT '组织机构id',
    creator      VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate   DATETIME    NOT NULL COMMENT '创建时间',
    domainId     VARCHAR(50) NOT NULL COMMENT '所属实验室',
    modifier     VARCHAR(50) NOT NULL COMMENT '修改人',
    modifyDate   DATETIME    NOT NULL COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_CalendarDate'
    COMMENT = '日历日期';

-- ---------------------------------------------------------------
-- 创建比对判定表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_CompareJudge;
CREATE TABLE TB_LIM_CompareJudge
(
    id                 VARCHAR(50) NOT NULL COMMENT 'id',
    analyzeItemId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目id',
    checkType          INT(11) COMMENT '检测类型（0-废水比对，1-废气比对）',
    defaultStandardNum INT(11) NOT NULL DEFAULT 0 COMMENT '默认标样数',
    orgId              VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator            VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    dataDiscrepancy    BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否数据对差',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_CompareJudge'
    COMMENT = '比对判定表';

-- ---------------------------------------------------------------
-- 创建参数（样品属性参数、公式的参数、仪器的数据参数）映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_Params;
CREATE TABLE TB_LIM_Params
(
    id           VARCHAR(50) NOT NULL COMMENT 'id',
    paramCode    VARCHAR(50) COMMENT '唯一编号',
    paramName    VARCHAR(50) COMMENT '参数名称',
    remark       VARCHAR(255) COMMENT '备注',
    variableName VARCHAR(50) COMMENT '变量名称',
    regex        VARCHAR(100) COMMENT '正则表达式验证',
    dimension    VARCHAR(50) COMMENT '计量单位',
    dimensionId  VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计量单位Id（Guid）',
    isDeleted    BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_Params'
    COMMENT = '参数（样品属性参数、公式的参数、仪器的数据参数）';

-- ---------------------------------------------------------------
-- 创建参数的公式表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_Params2ParamsFormula;
CREATE TABLE TB_LIM_Params2ParamsFormula
(
    id             VARCHAR(50) NOT NULL COMMENT 'id',
    recordId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '记录单Id',
    paramsConfigId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数配置Id',
    objectId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '对象id（测试公式id-记录单参数，测试项目id-工作单参数）',
    formula        VARCHAR(1000) COMMENT '公式',
    isEnabled      BIT(1)      NOT NULL DEFAULT b'0' COMMENT '',
    isDeleted      BIT(1)      NOT NULL DEFAULT b'0' COMMENT 'isDeleted',
    orgId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_Params2ParamsFormula'
    COMMENT = '参数的公式表';

-- ---------------------------------------------------------------
-- 创建计算公式表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ParamsFormula;
CREATE TABLE TB_LIM_ParamsFormula
(
    id                VARCHAR(50) NOT NULL COMMENT 'id',
    objectId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数Id（测试项目、检测类型参数）',
    formula           VARCHAR(1000) COMMENT '公式',
    configDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '配置日期',
    orignFormula      MEDIUMTEXT COMMENT '原始公式',
    orignFormulatType INT(11) NOT NULL DEFAULT 0 COMMENT '原始公式类型（枚举EnumOrignFormulatType：0:手写html,1:图片）',
    isDeleted         BIT(1)      NOT NULL DEFAULT b'0' COMMENT '假删',
    objectType        INT(11) NOT NULL DEFAULT -1 COMMENT '类型（枚举EnumParamsFormulaObjectType：0.测试公式 1.检测类型参数公式）',
    sampleTypeId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id（仅用于测试项目）',
    orgId             VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    validate          INT(11) DEFAULT 0 COMMENT '验证状态 0未验证 1已验证',
    usageNum          INT(11) COMMENT '使用次数',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ParamsFormula'
    COMMENT = '计算公式表';

-- ---------------------------------------------------------------
-- 创建部分公式表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ParamsPartFormula;
CREATE TABLE TB_LIM_ParamsPartFormula
(
    id               VARCHAR(50)   NOT NULL COMMENT 'id',
    formulaId        VARCHAR(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '公式Id',
    formula          VARCHAR(1000) NOT NULL COMMENT '部分公式',
    paramsName       VARCHAR(100) COMMENT '',
    mostSignificance INT(11) NOT NULL DEFAULT -1 COMMENT '有效位数',
    mostDecimal      INT(11) NOT NULL DEFAULT -1 COMMENT '小数位数',
    orderNum         INT(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    formulaType      INT(11) NOT NULL DEFAULT -1 COMMENT '类型（EnumPartFormulaType：0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）',
    orgId            VARCHAR(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    detectionLimit   VARCHAR(50) COMMENT '测试项目部分公式检出限',
    calculationMode  INT(11) COMMENT '计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）',
    useTestLimit     BIT(1)        NOT NULL DEFAULT b'1' COMMENT '是否使用测试项目检出限',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ParamsPartFormula'
    COMMENT = '部分公式表';

-- ---------------------------------------------------------------
-- 创建测试公式与参数的关联表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ParamsTestFormula;
CREATE TABLE TB_LIM_ParamsTestFormula
(
    id              VARCHAR(50) NOT NULL COMMENT 'id',
    objId           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '对象id（如测试公式id）',
    paramsId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数id',
    paramsName      VARCHAR(50) COMMENT '参数名称',
    alias           VARCHAR(50) COMMENT '参数别名',
    defaultValue    VARCHAR(50) COMMENT '默认值',
    orderNum        INT(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    aliasInReport   VARCHAR(50) COMMENT '工作单模板中的变量名',
    dimensionId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计量单位id',
    dimension       VARCHAR(50) COMMENT '计量单位',
    sourceType      INT(11) NOT NULL DEFAULT 0 COMMENT '类型（枚举EnumSourceType：0.无,1.样品，2.测试，3.企业，4.原始记录单）',
    isMust          BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否必填',
    isEditable      BIT(1)      NOT NULL DEFAULT b'1' COMMENT '是否允许修改',
    orgId           VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    detectionLimit  VARCHAR(50) COMMENT '参数检出限',
    calculationMode INT(11) COMMENT '计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ParamsTestFormula'
    COMMENT = '测试公式与参数的关联表';

-- ---------------------------------------------------------------
-- 创建版本发布管理表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_PublishSystemVersion;
CREATE TABLE TB_LIM_PublishSystemVersion
(
    id             VARCHAR(50)  NOT NULL COMMENT '主键',
    title          VARCHAR(200) NOT NULL COMMENT '标题',
    versionNum     VARCHAR(100) NOT NULL COMMENT '版本号',
    publishPerson  VARCHAR(50)  NOT NULL COMMENT '发布人',
    publishDate    DATETIME     NOT NULL COMMENT '发布日期',
    flaywayVersion VARCHAR(50)  NOT NULL COMMENT 'flayway版本',
    isProduct      BIT(1)       NOT NULL DEFAULT b'1' COMMENT '是否产品 0否 1是',
    isPublish      BIT(1)       NOT NULL DEFAULT b'1' COMMENT '是否产品 0否 1是',
    isTemplate     BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否产品 0否 1是',
    isConfig       BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否产品 0否 1是',
    updateContent  TEXT         NOT NULL COMMENT '更新内容',
    deployContent  TEXT COMMENT '部署注意事项',
    orgId          VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId       VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_PublishSystemVersion'
    COMMENT = '版本发布管理表';

-- ---------------------------------------------------------------
-- 创建报表应用数据表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_RecordConfig;
CREATE TABLE TB_LIM_RecordConfig
(
    id             VARCHAR(50)  NOT NULL COMMENT 'id',
    recordName     VARCHAR(100) NOT NULL COMMENT '名称',
    recordType     INT(11) NOT NULL DEFAULT -1 COMMENT '记录单类型(枚举EnumRecordType：1:采样记录单,2:原始记录单,3:报告)',
    reportConfigId VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报表模板Id',
    sampleTypeId   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型Id(小类)',
    remark         VARCHAR(255) COMMENT '备注',
    isDeleted      BIT(1)       NOT NULL DEFAULT b'0' COMMENT '假删',
    orgId          VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId       VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    sampleTypeIds  VARCHAR(400) COMMENT '检测类型id列表，多个id用逗号隔开',
    orderNum       INT(11) NOT NULL DEFAULT 0 COMMENT '排序值',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_RecordConfig'
    COMMENT = '报表应用数据表';

-- ---------------------------------------------------------------
-- 创建报表应用参数配置关联表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_RecordConfig2ParamsConfig;
CREATE TABLE TB_LIM_RecordConfig2ParamsConfig
(
    id             VARCHAR(50) NOT NULL COMMENT '主键id',
    recordConfigId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样单配置id',
    paramsConfigId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数配置id',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_RecordConfig2ParamsConfig'
    COMMENT = '报表应用参数配置关联表';

-- ---------------------------------------------------------------
-- 创建原始记录单与测试项目多对多关系映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_RecordConfig2Test;
CREATE TABLE TB_LIM_RecordConfig2Test
(
    id             VARCHAR(50) NOT NULL COMMENT '主键id',
    recordConfigId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '记录单配置id',
    testId         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测试项目id',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_RecordConfig2Test'
    COMMENT = '原始记录单与测试项目多对多关系';

-- ---------------------------------------------------------------
-- 创建报表配置场景应用配置表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ReportApply;
CREATE TABLE TB_LIM_ReportApply
(
    id             VARCHAR(50) NOT NULL COMMENT '主键id',
    reportConfigId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '配置id',
    module         VARCHAR(50) COMMENT '所属模块',
    moduleName     VARCHAR(50) COMMENT '所属模块名称',
    code           VARCHAR(50) COMMENT '控件编码',
    name           VARCHAR(50) COMMENT '显示名称',
    type           INT(11) NOT NULL DEFAULT 0 COMMENT '控件类型',
    isRedact       INT(11) NOT NULL DEFAULT 0 COMMENT '是否可编辑',
    isShow         INT(11) NOT NULL DEFAULT 0 COMMENT '是否显示名称',
    remark         VARCHAR(500) COMMENT '备注',
    location       VARCHAR(50) COMMENT '地址',
    orgId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    blankFill      INT(11) COMMENT '是否空白填充 1：是 0：否',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ReportApply'
    COMMENT = '报表配置场景应用配置表';

-- ---------------------------------------------------------------
-- 创建报告组件配置表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ReportConfig2Module;
CREATE TABLE TB_LIM_ReportConfig2Module
(
    id             VARCHAR(50) NOT NULL COMMENT 'id',
    reportConfigId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报表配置id',
    reportModuleId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告组件id（常量维护）',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ReportConfig2Module'
    COMMENT = '报告组件配置表';

-- ---------------------------------------------------------------
-- 创建报告组件信息表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ReportModule;
CREATE TABLE TB_LIM_ReportModule
(
    id                         VARCHAR(50)  NOT NULL COMMENT '主键id',
    moduleCode                 VARCHAR(100) NOT NULL COMMENT '组件编码',
    moduleName                 VARCHAR(100) NOT NULL COMMENT '组件名称',
    tableName                  VARCHAR(100) NOT NULL COMMENT '组件主表名称',
    sourceTableName            VARCHAR(100) COMMENT '组件数据行表名称',
    sampleCount                INT(11) NOT NULL DEFAULT 0 COMMENT '组件每页样品数量',
    testCount                  INT(11) NOT NULL DEFAULT 0 COMMENT '组件每页测试项目数量',
    sonTableJson               VARCHAR(500) COMMENT '子组件配置信息（适用于复合组件）',
    isCompound                 BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否复合组件',
    orgId                      VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator                    VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate                 DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId                   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier                   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate                 DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    totalTest                  VARCHAR(255) COMMENT '是否总称',
    auxiliaryInstrument        VARCHAR(255) COMMENT '是否辅助仪器',
    conversionCalculationMode  INT(11) NOT NULL DEFAULT 0 COMMENT '报告折算浓度计算方式（0.按样品, 1.按批次）',
    speedCalculationMode       INT(11) NOT NULL DEFAULT 0 COMMENT '报告排放速率计算方式（0.按样品, 1.按批次）',
    compoundAvgCalculationMode INT(11) NOT NULL DEFAULT 0 COMMENT '化合物均值计算方式 枚举 EnumCompoundAvgCalculationMode：0.按样品, 1.按批次',
    gasParamSplitMode          INT(11) NOT NULL DEFAULT 1 COMMENT '烟气参数拆分方式 枚举 EnumGasParamSplitMode：0.按样品, 1.按批次',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ReportModule'
    COMMENT = '报告组件信息表';

-- ---------------------------------------------------------------
-- 创建报告各个组件配置的分页方式表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_ReportModule2GroupType;
CREATE TABLE TB_LIM_ReportModule2GroupType
(
    id                   VARCHAR(50) NOT NULL COMMENT '主键id',
    reportConfigModuleId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告组件配置id',
    groupTypeName        VARCHAR(255) COMMENT '分页类型名称（包含数据源，属性名称，分页方式）',
    priority             INT(11) NOT NULL DEFAULT -1 COMMENT '优先级（最外层分页的优先级最高）',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_ReportModule2GroupType'
    COMMENT = '报告各个组件配置的分页方式表';

-- ---------------------------------------------------------------
-- 创建App版本管理表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_LIM_VersionInfo;
CREATE TABLE TB_LIM_VersionInfo
(
    id         VARCHAR(50) NOT NULL COMMENT '主键',
    version    VARCHAR(50) COMMENT '版本号',
    verValue   VARCHAR(2000) COMMENT '更新内容',
    verUrl     VARCHAR(255) COMMENT '附件路径',
    verTime    DATETIME    NOT NULL COMMENT '上传时间',
    verType    VARCHAR(50) NOT NULL COMMENT '版本类型',
    verCode    VARCHAR(300) COMMENT '版本二维码',
    orgId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    domainId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    codeUrl    VARCHAR(3000) COMMENT '二维码链接',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_LIM_VersionInfo'
    COMMENT = 'App版本管理表';


