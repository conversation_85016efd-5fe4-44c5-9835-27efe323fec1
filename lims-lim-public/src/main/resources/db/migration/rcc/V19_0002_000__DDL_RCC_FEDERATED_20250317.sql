-- --------------------------------------------------------
-- 创建机构管理表映射
-- --------------------------------------------------------
DROP TABLE IF EXISTS TB_COMMOM_InstitutionManage;
CREATE TABLE TB_COMMOM_InstitutionManage
(
    id          VARCHAR(50)  NOT NULL COMMENT '主键',
    name        VARCHAR(50)  NOT NULL COMMENT '机构名称',
    code        VARCHAR(50)  NOT NULL COMMENT '机构编码',
    remoteUrl   VARCHAR(500) NOT NULL COMMENT 'LIMS远程请求地址',
    isAutoPush  BIT(1)       NOT NULL COMMENT '是否自动推送',
    isEnableRcc BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否启用资源中心机制',
    isDeleted   BIT(1)       NOT NULL DEFAULT b'0' COMMENT '假删 标识',
    orgId       VARCHAR(50)  NOT NULL COMMENT '所属机构ID',
    domainId    VARCHAR(50)  NOT NULL COMMENT '所属实验室ID',
    creator     VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier    VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = FEDERATED
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    CONNECTION = 'mysql://${rccDbUserName}:${rccDbPassword}@${rccDbHost}:${rccDbPort}/${rccDbName}/TB_COMMOM_InstitutionManage'
    COMMENT '机构管理表';

