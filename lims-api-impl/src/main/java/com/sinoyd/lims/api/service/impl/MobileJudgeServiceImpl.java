package com.sinoyd.lims.api.service.impl;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.api.service.MobileJudgeService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.repository.SampleJudgeDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.SampleJudgeDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class MobileJudgeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleJudgeData, String, SampleJudgeDataRepository> implements MobileJudgeService {

    private SampleRepository sampleRepository;

    private TestRepository testRepository;

    private SampleJudgeDataService sampleJudgeDataService;

    @Override
    public List<Map<String, String>> getAnalyzeItemByFolderId(String folderId, Integer cycValue) {
        List<Map<String, String>> data = new ArrayList<>();
        List<DtoSample> samples = sampleRepository.findBySampleFolderId(folderId).stream().filter(s -> cycValue.equals(s.getCycleOrder())).collect(Collectors.toList());
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdIn(sampleIds);
        List<String> testIds = judgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        for (DtoTest test : tests) {
            Map<String, String> map = new HashMap<>();
            map.put("testId", test.getId());
            map.put("analyzeItemName", test.getRedAnalyzeItemName());
            data.add(map);
        }
        return data;
    }

    @Override
    public List<DtoSampleJudgeData> findByFolderIdForApp(String folderId, Integer cycValue, String testId) {
        List<DtoSample> samples = sampleRepository.findBySampleFolderId(folderId).stream().filter(s -> cycValue.equals(s.getCycleOrder())).collect(Collectors.toList());
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdInAndTestId(sampleIds, testId);
        sampleJudgeDataService.fillingTransientFields(judgeDataList);
//      // 比对样排序，替代样跟随原样
        for (DtoSampleJudgeData sampleJudgeData : judgeDataList) {
            sampleJudgeData.setTdOrderNum(getSortOrderNum(samples, judgeDataList, sampleJudgeData));
        }
        judgeDataList.sort(Comparator.comparing(DtoSampleJudgeData::getTdOrderNum));
        return judgeDataList;
    }

    /**
     * ]
     * 获取排序值
     *
     * @param sampleList    样品ids
     * @param judgeDataList 比对数据集合
     * @param judgeData     比对诗句
     * @return 排序值
     */
    private String getSortOrderNum(List<DtoSample> sampleList, List<DtoSampleJudgeData> judgeDataList, DtoSampleJudgeData judgeData) {
        AtomicReference<String> tdOrderNum = new AtomicReference<>(String.format("%s-%s", judgeData.getOrderNum(), judgeData.getSampleCode()));
        if (judgeData.getCompareType().equals(EnumBase.EnumJudgingType.替代样比对.getValue())) {
            // 根据替代样找到对应的原样
            sampleList.stream().filter(p -> judgeData.getSampleId().equals(p.getId())).findFirst().ifPresent(sample -> {
                sampleList.stream().filter(v -> sample.getAssociateSampleId().equals(v.getId())).findFirst().ifPresent(yySample -> {
                    judgeDataList.stream().filter(w -> yySample.getId().equals(w.getSampleId()) &&
                            w.getTestId().equals(judgeData.getTestId())).findFirst().ifPresent(yyJude -> {
                        // 在原样的排序值基础上加1
                        tdOrderNum.set(String.format("%s-%s-%s", yyJude.getOrderNum(), yySample.getCode(), "30"));
                    });
                });
            });
        }
        return tdOrderNum.get();
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

}
