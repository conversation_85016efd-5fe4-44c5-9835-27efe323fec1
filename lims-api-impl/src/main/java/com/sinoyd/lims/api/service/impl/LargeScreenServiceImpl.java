package com.sinoyd.lims.api.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.dto.customer.DtoSampleSchedule;
import com.sinoyd.lims.api.dto.customer.DtoWorkSheetTask;
import com.sinoyd.lims.api.service.LargeScreenService;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestPost2TestRepository;
import com.sinoyd.lims.lim.repository.lims.TestPostRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.AnalyseDataFutureService;
import com.sinoyd.lims.pro.service.SampleService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 大屏（实验室专题）管理接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
@Service
@Slf4j
public class LargeScreenServiceImpl implements LargeScreenService {

    private CommonRepository commonRepository;
    private ProjectRepository projectRepository;
    private SampleRepository sampleRepository;
    private StatusForRecordRepository statusForRecordRepository;
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;
    private SampleTypeRepository sampleTypeRepository;
    private TestRepository testRepository;
    private AnalyseDataRepository analyseDataRepository;
    private CodeService codeService;
    private Project2WorkSheetFolderRepository project2WorkSheetFolderRepository;
    private TestPostRepository testPostRepository;
    private TestPost2TestRepository testPost2TestRepository;
    private PersonService personService;
    private IOrgService orgService;
    private SampleService sampleService;
    private QualityControlRepository qualityControlRepository;
    private AnalyseDataFutureService analyseDataFutureService;
    private SampleGroupRepository sampleGroupRepository;


    private static final String[] analyseTypes = {"wait", "test", "checkDeal", "auditDeal", "affirm"};


    @Override
    public List<Map<String, String>> sampleOverview(String date) {
        // 原样数据
        StringBuilder condition = new StringBuilder();
        condition.append("SELECT t1");
        condition.append(" FROM DtoSample t1 ");
        condition.append(" WHERE 1 = 1 ");
        condition.append(" AND t1.sampleCategory = 0 AND t1.isDeleted = 0 ");
        condition.append(" AND t1.samplingTimeBegin = :samplingTimeBegin ");
        Map<String, Object> values = new HashMap<>();
        Date samplingTimeBegin = DateUtil.stringToDate(date, DateUtil.YEAR);
        values.put("samplingTimeBegin", samplingTimeBegin);
        List<DtoSample> sampleList = commonRepository.find(condition.toString(), values);
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
        Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, p -> p));
        // 原样id
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        // 获取所选原样的现场关联样
        List<DtoSample> localAssociateSampleList = this.getLocalAssociateSample(sampleIds);
        //空白样可以添加串联样 所有质控都可以添加串联样
        List<String> qcSamIds = localAssociateSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(qcSamIds)) {
            List<DtoSample> kbclAssociateSampleList = this.getLocalAssociateSample(qcSamIds);
            sampleList.addAll(kbclAssociateSampleList);
        }
        sampleList.addAll(localAssociateSampleList);

        // 根据检测类型分组
        Map<String, List<DtoSample>> sampleMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleTypeId));
        List<Map<String, String>> result = new ArrayList<>();
        for (String key : sampleMap.keySet()) {
            Map<String, String> map = new HashMap<>();
            map.put("count", String.valueOf(sampleMap.get(key).size()));
            DtoSampleType sampleType = sampleTypeMap.get(key);
            map.put("sampleTypName", sampleType.getTypeName());
            map.put("typeCode", sampleType.getTypeCode());
            result.add(map);
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> analyseOverview() {
        // 获取年份下所有原样的检测数据a
        StringBuilder condition = new StringBuilder();
        condition.append("select a.dataStatus from DtoAnalyseData a, DtoSample t1 where 1 = 1 ");
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and a.sampleId = t1.id ");
        // 查询所有样品下的所有
        condition.append(" and t1.isDeleted = 0 ");
        condition.append(" and a.analyzeTime >= :form");
        condition.append(" and a.analyzeTime <= :end");
        Map<String, Object> values = new HashMap<>();
        // 设置年初年尾条件
        setYearConditions(values);
        List<Integer> data = commonRepository.find(condition.toString(), values);

        // 定义各种分析类型
        int count = 0;
        Map<String, Integer> numMap = new HashMap<>();
        for (String type : analyseTypes) {
            numMap.put(type, 0);
        }
        // 循环迭代获取JPQL中查询返回的属性

        for (Integer datum : data) {
            if (StringUtil.isNotNull(datum)) {
                setAnalyseNumAndStatus(datum, numMap);
                count++;
            }
        }
        List<Map<String, Object>> list = new ArrayList<>();
        for (String type : analyseTypes) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", type);
            map.put("num", numMap.get(type));
            int percentage = (int) ((double) numMap.get(type) / count * 100);
            map.put("percentage", percentage + "%");
            list.add(map);
        }
        return list;
    }


    @Override
    public List<DtoSampleSchedule> sampleList(String date) {
        StringBuilder condition = new StringBuilder();
        condition.append("select new com.sinoyd.lims.api.dto.customer.DtoSampleSchedule( ");
        condition.append(" t1.id, t1.recordCode, t1.projectId, json_value(t1.json,'$.labSampleTypes') as sampleType, t1.senderName)");
        condition.append(" from DtoReceiveSampleRecord t1");
        condition.append(" where 1 =1 and t1.isDeleted = 0 and t1.sendTime = :sendTime");
        Map<String, Object> values = new HashMap<>();
        Date sendTime = DateUtil.stringToDate(date, DateUtil.YEAR);
        values.put("sendTime", sendTime);
        List<DtoSampleSchedule> dtoSampleSchedules = commonRepository.find(condition.toString(), values);

        List<String> projectIds = dtoSampleSchedules.stream().map(DtoSampleSchedule::getProjectId).distinct().collect(Collectors.toList());
        List<String> recordIds = dtoSampleSchedules.stream().map(DtoSampleSchedule::getId).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = projectRepository.findAll(projectIds);
        Map<String, DtoProject> dtoProjectMap = projectList.stream().collect(Collectors.toMap(DtoProject::getId, p -> p));

        List<DtoSample> sampleList = StringUtil.isNotEmpty(recordIds) ? sampleRepository.findByReceiveIdIn(recordIds) : new ArrayList<>();
        Map<String, List<DtoSample>> sampleOfMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getReceiveId));

        List<DtoStatusForRecord> statusForRecords = StringUtil.isNotEmpty(recordIds) ? statusForRecordRepository.findByReceiveIdIn(recordIds) : new ArrayList<>();
        Map<String, List<DtoStatusForRecord>> statusForMap = statusForRecords.stream().collect(Collectors.groupingBy(DtoStatusForRecord::getReceiveId));

        List<DtoReceiveSubSampleRecord> receiveSubSampleRecordList = receiveSubSampleRecordRepository.findByReceiveIdIn(recordIds);
        Map<String, List<DtoReceiveSubSampleRecord>> receiveSubMap = receiveSubSampleRecordList.stream().collect(Collectors.groupingBy(DtoReceiveSubSampleRecord::getReceiveId));

        // 筛选检测类型为空的数据
        List<String> existsNullList = dtoSampleSchedules.stream().filter(p -> p.getSampleType().equals("null")
                || StringUtil.isEmpty(p.getSampleType())).map(DtoSampleSchedule::getId).collect(Collectors.toList());
        List<String> sampleTypeIds = sampleList.stream().filter(p -> existsNullList.contains(p.getReceiveId())).map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();

        for (DtoSampleSchedule dtoSampleSchedule : dtoSampleSchedules) {
            // 项目信息
            DtoProject dtoProject = dtoProjectMap.getOrDefault(dtoSampleSchedule.getProjectId(), new DtoProject());
            dtoSampleSchedule.setProjectCode(dtoProject.getProjectCode());
            dtoSampleSchedule.setProjectName(dtoProject.getProjectName());
            // 样品信息
            List<DtoSample> samples = sampleOfMap.getOrDefault(dtoSampleSchedule.getId(), new ArrayList<>());
            dtoSampleSchedule.setSampleNum(samples.size());
            String test = samples.stream().map(DtoSample::getRedAnalyzeItems)
                    .flatMap(p -> Arrays.stream(p.split(",")))
                    .distinct().collect(Collectors.joining(","));
            dtoSampleSchedule.setTest(test);
            // 检测类型
            if (dtoSampleSchedule.getSampleType().equals("null") || StringUtil.isEmpty(dtoSampleSchedule.getSampleType())) {
                List<String> sampleTypeOFSample = samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
                String sampleTypeName = sampleTypeList.stream().filter(p -> sampleTypeOFSample.contains(p.getId())).map(DtoSampleType::getTypeName).collect(Collectors.joining("，"));
                dtoSampleSchedule.setSampleType(sampleTypeName);
            }

            // 状态信息
            List<DtoStatusForRecord> forRecords = statusForMap.getOrDefault(dtoSampleSchedule.getId(), new ArrayList<>());
            Optional<DtoStatusForRecord> first = forRecords.stream().filter(p -> p.getModule().equals(EnumLIM.EnumHomeTaskModule.样品交接.getValue()) && EnumPRO.EnumStatus.已处理.getValue().equals(p.getStatus())).findFirst();
            String innerStatus = "未交接";
            if (first.isPresent()) {
                innerStatus = "已交接";
            }
            String receiveSubstatus = "";
            dtoSampleSchedule.setInnerStatus(innerStatus);
            List<DtoReceiveSubSampleRecord> sampleRecords = receiveSubMap.getOrDefault(dtoSampleSchedule.getId(), new ArrayList<>());
            DtoReceiveSubSampleRecord subSampleRecord = sampleRecords.stream().filter(p -> p.getCode().contains(EnumPRO.EnumSubRecordType.分析.getValue())).findFirst().orElse(null);
            if (StringUtil.isNull(subSampleRecord)) {
                receiveSubstatus = "已分配";
            } else {
                if (EnumPRO.EnumReceiveSubRecordStatusName.测试中.toString().equals(subSampleRecord.getStatus())) {
                    receiveSubstatus = "已分配";
                } else {
                    receiveSubstatus = "未分配";
                }
            }
            dtoSampleSchedule.setReceiveSubstatus(receiveSubstatus);
        }

        return dtoSampleSchedules;
    }


    @Override
    public List<DtoWorkSheetTask> workSheetTask(DtoWorkSheetTask dtoWorkSheetTask) {
        // 项目维度，查询已交接并且未检测完成的项目
        StringBuilder condition = new StringBuilder();
        condition.append("select p from DtoProject p where 1 = 1 ");
        condition.append(" and EXISTS ( SELECT 1 from DtoSample s where p.id = s.projectId and s.innerReceiveStatus <> 1 and s.ananlyzeStatus <> 16 and s.isDeleted = 0) ");
        condition.append(" and p.inputTime >= :form");
        condition.append(" and p.inputTime <= :end");
        condition.append(" and p.isDeleted = 0 ");
        Map<String, Object> values = new HashMap<>();
        if (StringUtil.isNotEmpty(dtoWorkSheetTask.getAnalystId())) {
            condition.append(" and EXISTS ( SELECT 1 from DtoSample s ,DtoAnalyseData t2 where p.id = s.projectId  and s.id = t2.sampleId and s.isDeleted = 0 and t2.analystId = :analystId)");
            values.put("analystId", dtoWorkSheetTask.getAnalystId());
        }
        // 筛选一个月前的项目
        Calendar calendar = Calendar.getInstance();
        Date calendarTime = calendar.getTime();
        calendar.add(Calendar.MONTH, -1);
        Date oneMonThAgo = calendar.getTime();
        values.put("form", oneMonThAgo);
        values.put("end", calendarTime);
        List<DtoProject> projectList = commonRepository.find(condition.toString(), values);

        // 查询当天检测通过的检测单
        StringBuilder sql = new StringBuilder();
        sql.append("select w from DtoWorkSheetFolder w where 1 = 1 and w.isDeleted = 0 ");
        sql.append(" and w.workStatus = '32'");
        sql.append(" and w.auditDate >= :nowForm ");
        sql.append(" and w.auditDate <= :nowEnd ");
        Map<String, Object> values2 = new HashMap<>();
        Date startOfDay = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR) + " 00:00:00", DateUtil.FULL);
        Date endOfDay = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR) + " 23:59:59", DateUtil.FULL);
        values2.put("nowForm", startOfDay);
        values2.put("nowEnd", endOfDay);
        if (StringUtil.isNotEmpty(dtoWorkSheetTask.getAnalystId())) {
            sql.append(" and EXISTS (select 1 from DtoAnalyseData t1 where t1.workSheetId = w.id and t1.analystId = :analystId)");
            values2.put("analystId", dtoWorkSheetTask.getAnalystId());
        }
        List<DtoWorkSheetFolder> workSheetFolders = commonRepository.find(sql.toString(), values2);
        // 根据检测单获取项目信息，并组装项目信息
        List<String> workSheetFolderIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getId).distinct().collect(Collectors.toList());
        List<DtoProject2WorkSheetFolder> project2WorkSheetFolders = project2WorkSheetFolderRepository.findByWorkSheetFolderIdIn(workSheetFolderIds);
        List<String> projectIds = project2WorkSheetFolders.stream().map(DtoProject2WorkSheetFolder::getProjectId).distinct().collect(Collectors.toList());
        projectList.addAll(StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>());
        projectList = projectList.stream().distinct().collect(Collectors.toList());
        // 根据项目获取样品和分析数据
        List<String> projectIdAll = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        List<DtoSample> sampleListAll = StringUtil.isNotEmpty(projectIdAll) ?
                sampleRepository.findByProjectIdIn(projectIdAll).stream()
                        .filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue()))
                        .collect(Collectors.toList()) : new ArrayList<>();
        Map<String, List<DtoSample>> sampleMap = sampleListAll.stream().collect(Collectors.groupingBy(DtoSample::getProjectId));
        List<String> sampleIdsAll = sampleListAll.stream().map(DtoSample::getId).collect(Collectors.toList());
        // 过滤掉分包数据
        List<DtoAnalyseData> analyseDataListAll = new ArrayList<>();
        // 多线程获取分析数据
        futureGetAnalyseData(analyseDataListAll, sampleIdsAll);
        // 过滤掉已确认和现场数据
        analyseDataListAll = analyseDataListAll.stream().filter(p -> !p.getIsSamplingOut() && !p.getIsOutsourcing()
                && !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()) && !p.getIsCompleteField()).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(dtoWorkSheetTask.getAnalystId())) {
            analyseDataListAll = analyseDataListAll.stream().filter(p -> dtoWorkSheetTask.getAnalystId().equals(p.getAnalystId())).collect(Collectors.toList());
        }
        // 获取所有测试项目
        List<String> testIdsAll = analyseDataListAll.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testListAll = StringUtil.isNotEmpty(testIdsAll) ? testRepository.findAllDeleted(testIdsAll) : new ArrayList<>();
        List<String> parentTestIds = testListAll.stream().map(DtoTest::getParentId).filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toList());
        // 总称测试项目
        List<DtoTest> testOfTotal = StringUtil.isNotEmpty(parentTestIds) ? testRepository.findAllDeleted(parentTestIds) : new ArrayList<>();

        boolean allocateByPost = false;
        // 定义测试项目对应岗位
        Map<String, DtoTestPost> testId2PostMap = new HashMap<>();
        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            allocateByPost = true;
            List<DtoTestPost> allTestPostList = testPostRepository.findAll();
            Map<String, DtoTestPost> testPostMap = allTestPostList.stream().collect(Collectors.toMap(DtoTestPost::getId, dto -> dto));
            List<DtoTestPost2Test> allTestPost2TestList = testPost2TestRepository.findAll();
            for (DtoTestPost2Test allTestPost2Test : allTestPost2TestList) {
                if (!testId2PostMap.containsKey(allTestPost2Test.getTestId()) && testPostMap.containsKey(allTestPost2Test.getTestPostId())) {
                    //一个测试项目只能配置一个岗位，因此不考虑重复key的情况
                    testId2PostMap.put(allTestPost2Test.getTestId(), testPostMap.get(allTestPost2Test.getTestPostId()));
                }
            }
            // 根据分析岗位，筛选对应的项目和分析数据
            if (StringUtil.isNotEmpty(dtoWorkSheetTask.getTestPostId())) {
                // 获取岗位下的测试项目
                List<String> testOfPostIds = allTestPost2TestList.stream().filter(p -> dtoWorkSheetTask.getTestPostId().equals(p.getTestPostId())).map(DtoTestPost2Test::getTestId).collect(Collectors.toList());
                // 根据测试项目筛选项目和分析数据
                analyseDataListAll = analyseDataListAll.stream().filter(p -> testOfPostIds.contains(p.getTestId())).collect(Collectors.toList());
                List<String> sampleOfIds = analyseDataListAll.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<String> projects = sampleListAll.stream().filter(p -> sampleOfIds.contains(p.getId())).map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
                projectList = projectList.stream().filter(p -> projects.contains(p.getId())).collect(Collectors.toList());
            }
        }

        List<DtoWorkSheetTask> workSheetTasks = new ArrayList<>();
        // 先按照项目分组，在按照测试项目分组
        for (DtoProject dtoProject : projectList) {
            String projectId = dtoProject.getId();
            List<DtoSample> sampleList = sampleMap.getOrDefault(projectId, new ArrayList<>()).stream()
                    .filter(p -> !EnumPRO.EnumInnerReceiveStatus.不能领取.getValue().equals(p.getInnerReceiveStatus()))
                    .collect(Collectors.toList());
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());


            List<DtoAnalyseData> analyseDataList = analyseDataListAll.stream().filter(p -> sampleIds.contains(p.getSampleId())).collect(Collectors.toList());
            // 分析数据加上当天通过的的数据
            analyseDataList.addAll(analyseDataList.stream().filter(p -> workSheetFolderIds.contains(p.getWorkSheetFolderId())).collect(Collectors.toList()));

            Map<String, List<DtoAnalyseData>> analyseMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId));
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());

            List<DtoTest> testsOfProject = testListAll.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList());

            // 处理存在总称的数据，并汇总
            List<DtoTest> testList = getTestTotal(testListAll, testIds, testOfTotal);
            for (DtoTest test : testList) {
                // 根据检测任务状态不同在进行拆分
                List<DtoAnalyseData> analystOfTest = analyseMap.getOrDefault(test.getId(), new ArrayList<>());
                if (analystOfTest.size() == 0) {
                    // 数量为0 判断为总成测试项目，根据总称下的子测试项目筛选出分析数据
                    List<String> testId = testsOfProject.stream().filter(p -> test.getId().equals(p.getParentId())).map(DtoTest::getId).collect(Collectors.toList());
                    analystOfTest = analyseDataList.stream().filter(p -> testId.contains(p.getTestId())).collect(Collectors.toList());
                }
                // 根据测试项目的不同状态进行分组
                Map<Integer, List<DtoAnalyseData>> analyseOfStatusMap = analystOfTest.stream().collect(Collectors.groupingBy(DtoAnalyseData::getDataStatus));
                for (Map.Entry<Integer, List<DtoAnalyseData>> entry : analyseOfStatusMap.entrySet()) {
                    List<DtoAnalyseData> analyseDataOfStatus = entry.getValue();
                    DtoWorkSheetTask workSheetTask = new DtoWorkSheetTask();
                    // 筛选分组后的样品id，并筛选出样品
                    List<String> finalSampleOfTestIds = analyseDataOfStatus.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                    List<String> sampleOfProIds = sampleList.stream()
                            .filter(p -> finalSampleOfTestIds.contains(p.getId())
                                    && projectId.equals(p.getProjectId()))
                            .map(DtoSample::getId).collect(Collectors.toList());
                    int sampleNum = sampleOfProIds.size();
                    // 基础数据赋值
                    workSheetTask.setSampleNum(sampleNum);
                    workSheetTask.setId(projectId);
                    workSheetTask.setTest(test.getRedAnalyzeItemName());
                    workSheetTask.setProjectCode(dtoProject.getProjectCode());
                    workSheetTask.setCountryStandard(test.getRedCountryStandard());
                    // 根据样品获取采样日期最早的一条
                    Date date = sampleList.stream().filter(p -> sampleOfProIds.contains(p.getId())).map(DtoSample::getSamplingTimeBegin)
                            .filter(Objects::nonNull).min(Comparator.comparing(p -> p)).orElse(null);
                    workSheetTask.setSamplingTimeBegin(StringUtil.isNotNull(date) ? DateUtil.dateToString(date, DateUtil.YEAR_SPRIT) : "");
                    // 分析人员
                    String analystName = "";
                    if (allocateByPost) {
                        DtoTestPost dtoTestPost = testId2PostMap.getOrDefault(test.getId(), new DtoTestPost());
                        analystName = StringUtil.isNotNull(dtoTestPost) ? dtoTestPost.getPostName() : "";
                    } else {
                        List<String> analstNames = analyseDataOfStatus.stream().map(DtoAnalyseData::getAnalystName).filter(p -> !"".equals(p)).distinct().collect(Collectors.toList());
                        analystName = StringUtil.isNotEmpty(analstNames) ? String.join(",", analstNames) : "待分配";
                    }
                    workSheetTask.setAnalystName(analystName);
                    // 状态,因为分析数据已经按照分析状态分组，所以获取第一条。
                    DtoAnalyseData analyseData = analyseDataOfStatus.stream().findFirst().orElse(new DtoAnalyseData());
                    String status = setAnalyseNumAndStatus(analyseData.getDataStatus(), new HashMap<>());
                    workSheetTask.setStatus(status);

                    workSheetTasks.add(workSheetTask);
                }
            }
        }
        return workSheetTasks;
    }


    @Override
    public Map<String, Object> statisticsData() {
        // 查询当前年份的所有原样数据
        StringBuilder condition = new StringBuilder();
        condition.append("select t1 from DtoSample t1 where 1 = 1 ");
        condition.append(" and t1.sampleCategory = 0 and t1.isDeleted = 0 ");
        condition.append(" and t1.samplingTimeBegin >= :form");
        condition.append(" and t1.samplingTimeBegin <= :end");
        Map<String, Object> values = new HashMap<>();
        // 设置年初年尾条件
        setYearConditions(values);
        List<DtoSample> sampleList = commonRepository.find(condition.toString(), values);
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
        Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, p -> p));
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        // 多线程获取数据
        futureGetAnalyseData(analyseDataList, sampleIds);

        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> typeList = new ArrayList<>();
        // 按照检测类型分组
        Map<String, List<DtoSample>> sampleOfTypeMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleTypeId));
        for (Map.Entry<String, List<DtoSample>> entry : sampleOfTypeMap.entrySet()) {
            String key = entry.getKey();
            List<DtoSample> sampleOfType = entry.getValue();

            Map<String, Object> typeMap = new HashMap<>();
            // 检测类型
            DtoSampleType sampleType = sampleTypeMap.getOrDefault(key, new DtoSampleType());
            typeMap.put("sampleTypeName", sampleType.getTypeName());
            typeMap.put("typeColor", sampleType.getTypeColor());
            // 样品数据详情
            typeMap.put("sampleDetail", new int[12]);
            typeMap.put("analyseDetail", new int[12]);
            // 当前检测类型下的数据，按照采样日期分组
            getMouthDate(sampleOfType, typeMap, analyseDataList, "sampleDetail", "analyseDetail");
            typeList.add(typeMap);
        }
        map.put("type", typeList);
        // 筛选出数据TOP
        List<DtoSample> sampleTypeTop = StringUtil.isNotEmpty(sampleOfTypeMap.values()) ? Collections.max(sampleOfTypeMap.values(), Comparator.comparingInt(List::size)) : new ArrayList<>();
        map.put("sampleTypeTop", new HashMap<String, Object>() {
            {
                String topName = "";
                if (StringUtil.isNotEmpty(sampleTypeTop)) {
                    DtoSampleType sampleType = sampleTypeMap.getOrDefault(sampleTypeTop.get(0).getSampleTypeId(), new DtoSampleType());
                    topName = sampleType.getTypeName();
                }
                put("topName", topName);
                put("size", sampleTypeTop.size());
            }
        });
        map.put("mouthTop", "");
        map.put("sampleAll", new int[12]);
        map.put("analyseAll", new int[12]);
        getMouthDate(sampleList, map, analyseDataList, "sampleAll", "analyseAll");
        return map;
    }

    @Override
    public List<Map<String, Object>> overdueTask() {
        List<Map<String, Object>> overdueTaskList = new ArrayList<>();
        // 获取没有分析完成的送样单
        StringBuilder condition = new StringBuilder();
        condition.append("select t1 from DtoReceiveSampleRecord t1 where 1 = 1 and t1.isDeleted = 0 ");
        condition.append(" and EXISTS ( SELECT 1 FROM DtoSample t2 WHERE t1.id = t2.receiveId AND t2.sampleCategory = 0 and t2.isDeleted = 0 AND t2.ananlyzeStatus <> 16 ) ");
        condition.append(" and t1.samplingTime >= :form");
        condition.append(" and t1.samplingTime <= :end");
        condition.append(" and t1.receiveSampleDate is not null ");
        Map<String, Object> values = new HashMap<>();
        // 设置年初年尾条件
        setYearConditions(values);
        List<DtoReceiveSampleRecord> receiveSampleRecords = commonRepository.find(condition.toString(), values);

        List<String> receiveIds = receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(receiveIds) ? sampleRepository.findByReceiveIdIn(receiveIds) : new ArrayList<>();
        Map<String, List<DtoSample>> sampleOfMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getReceiveId));
        List<String> sampleIdsAll = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleGroup> sampleGroups = StringUtil.isNotEmpty(receiveIds) ? sampleGroupRepository.findByReceiveIdIn(receiveIds) : new ArrayList<>();
        Map<String, List<DtoSampleGroup>> sampleGroupMap = sampleGroups.stream().collect(Collectors.groupingBy(DtoSampleGroup::getReceiveId));
        List<DtoAnalyseData> analyseDataListAll = new ArrayList<>();
        futureGetAnalyseData(analyseDataListAll, sampleIdsAll);
        analyseDataListAll = analyseDataListAll.stream().filter(p -> !p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).collect(Collectors.toList());
        // 获取所有测试项目
        List<String> testIdsAll = analyseDataListAll.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testListAll = StringUtil.isNotEmpty(testIdsAll) ? testRepository.findAllDeleted(testIdsAll) : new ArrayList<>();
        List<String> parentTestIds = testListAll.stream().map(DtoTest::getParentId).filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toList());
        // 总称测试项目
        List<DtoTest> testOfTotal = StringUtil.isNotEmpty(parentTestIds) ? testRepository.findAllDeleted(parentTestIds) : new ArrayList<>();
        Date now = new Date();
        for (DtoReceiveSampleRecord receiveSampleRecord : receiveSampleRecords) {
            List<DtoSample> samples = sampleOfMap.getOrDefault(receiveSampleRecord.getId(), new ArrayList<>());

            List<String> sampleIds = samples.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = analyseDataListAll.stream().filter(p -> sampleIds.contains(p.getSampleId())).collect(Collectors.toList());
            // 数据中只有现场任务的不显示
            if (analyseDataList.stream().allMatch(DtoAnalyseData::getIsCompleteField)) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("recordCode", receiveSampleRecord.getRecordCode());
            map.put("samplingTime", StringUtil.isNotNull(receiveSampleRecord.getSamplingTime()) ? DateUtil.dateToString(receiveSampleRecord.getSamplingTime(), DateUtil.YEAR_SPRIT) : "");
            // 分析数据过滤掉已通过的数据审核通过的数据
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> testTotal = getTestTotal(testListAll, testIds, testOfTotal);
            List<String> testNames = testTotal.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
            map.put("tests", StringUtil.isNotEmpty(testNames) ? String.join("、", testNames) : "");
            Date sampleReceiveDate = StringUtil.isNotNull(receiveSampleRecord.getReceiveSampleDate()) ? receiveSampleRecord.getReceiveSampleDate() : new Date();
            List<DtoSampleGroup> sampleGroupList = sampleGroupMap.getOrDefault(receiveSampleRecord.getId(), new ArrayList<>());
            List<Date> receiveSampleDateList = sampleGroupList.stream().map(DtoSampleGroup::getReceiveSampleDate)
                    .filter(StringUtil::isNotNull).distinct().collect(Collectors.toList());
            // 接样日期先从样品分组中获取，并且取最早的日期，如果没有则拿送样单中的日期
            sampleReceiveDate = StringUtil.isNotEmpty(receiveSampleDateList) ? receiveSampleDateList.stream().min(Comparator.comparing(p -> p)).get() : sampleReceiveDate;
            map.put("retentionTime", getRetentionTime(now, sampleReceiveDate));

            overdueTaskList.add(map);
        }
        return overdueTaskList;
    }

    @Override
    public List<DtoKeyValue> person(DtoPersonQuery queryDto) {
        return personService.query(queryDto);
    }

    @Override
    public List<DtoTestPost> testPost() {
        return testPostRepository.findAll();
    }

    @Override
    public Boolean isTestPost() {
        //判断是否按岗位分配
        boolean allocateByPost = false;
        DtoCode dtoCode = codeService.findByCode(LimConstants.codeConstants.PRO_AnalyseAllocationRules_Post);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            allocateByPost = true;
        }
        return allocateByPost;
    }

    @Override
    public List<OrgModel> getOrgList() {
        return orgService.selectList(new EntityWrapper<>());
    }


    private List<DtoSample> getLocalAssociateSample(List<String> ids) {
        List<DtoSample> list = new ArrayList<>();
        if (StringUtil.isNotEmpty(ids)) {
            List<DtoSample> samples = sampleRepository.findByAssociateSampleIdIn(ids);
            samples = samples.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.质控样.getValue()) ||
                    p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.串联样.getValue()) ||
                    EnumPRO.EnumSampleCategory.洗涤剂.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());

            List<String> qcIds = samples.stream().map(DtoSample::getQcId).filter(qcId -> !UUIDHelper.GUID_EMPTY.equals(qcId)).collect(Collectors.toList());
            List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
            if (StringUtil.isNotNull(qcList) && qcList.size() > 0) {
                Map<String, DtoQualityControl> qcMap = qcList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())).collect(Collectors.toMap(DtoQualityControl::getId, qc -> qc));

                for (DtoSample sample : samples) {
                    if (sample.getSampleCategory().equals(EnumPRO.EnumSampleCategory.串联样.getValue()) || EnumPRO.EnumSampleCategory.洗涤剂.getValue().equals(sample.getSampleCategory())) {
                        list.add(sample);
                    } else if (qcMap.containsKey(sample.getQcId())) {
                        sample.setQcGrade(EnumLIM.EnumQCGrade.外部质控.getValue());
                        sample.setQcType(qcMap.get(sample.getQcId()).getQcType());
                        list.add(sample);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 多线程获取分析数据
     *
     * @param analyseDataList
     * @param sampleIds
     */
    private void futureGetAnalyseData(List<DtoAnalyseData> analyseDataList, List<String> sampleIds) {
        List<Future<List<DtoAnalyseData>>> analyseDataResultList = new ArrayList<>();
        List<String> list = null;
        final int batchSize = 50;
        for (String sampleId : sampleIds) {
            if (list == null) {
                list = new ArrayList<>();
            }
            if (list.size() < batchSize) {
                list.add(sampleId);
            } else if (list.size() == batchSize) {
                //多线程处理排序
                analyseDataResultList.add(analyseDataFutureService.getListBySampleIdIn(list));
                list = new ArrayList<>();
                list.add(sampleId);
            }
        }
        //如果存在最后一批样，需要单独去排序处理
        if (StringUtil.isNotEmpty(list)) {
            analyseDataResultList.add(analyseDataFutureService.getListBySampleIdIn(list));
        }
        //处理多线程处理的结果
        try {
            for (Future<List<DtoAnalyseData>> analyseDataResult : analyseDataResultList) {
                while (true) {
                    if (analyseDataResult.isDone() && !analyseDataResult.isCancelled()) {
                        analyseDataList.addAll(analyseDataResult.get());
                        break;
                    } else {
                        //防止CPU高速轮询被耗空
                        Thread.sleep(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("......多线程处理分析数据出错......");
        }
    }


    /**
     * 设置年份区间（年初-年尾）
     *
     * @param values 参数Map
     */
    private void setYearConditions(Map<String, Object> values) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        calendar.set(year, Calendar.JANUARY, 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 年初
        Date form = calendar.getTime();
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        // 年尾
        Date end = calendar.getTime();
        values.put("form", form);
        values.put("end", end);
    }


    /**
     * 获取合并的测试项目名称
     *
     * @param testList       测试项目
     * @param testIds        测试项目id
     * @param parentTestList 父测试项目
     * @return 返回数据
     */
    private static List<DtoTest> getTestTotal(List<DtoTest> testList, List<String> testIds, List<DtoTest> parentTestList) {
        //当前遍历的测试项目集合
        List<DtoTest> curTestList = testList.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList());
        //按照父id分组
        Map<String, List<DtoTest>> parentId2TestListMap = curTestList.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
        List<DtoTest> mergedTestList = new ArrayList<>();
        for (Map.Entry<String, List<DtoTest>> entry : parentId2TestListMap.entrySet()) {
            String parentId = entry.getKey();
            List<DtoTest> loopTestList = entry.getValue();
            //获取父测试项目
            DtoTest parentTest = parentTestList.stream().filter(p -> parentId.equals(p.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(parentTest)) {
                if (parentTest.getIsTotalTest() && loopTestList.size() > parentTest.getMergeBase()) {
                    //合并显示
                    mergedTestList.add(parentTest);
                } else {
                    //存在父测试项目，是否总称开关关闭时，多个因子放在一起展示
                    mergedTestList.addAll(loopTestList);
                }
            } else {
                mergedTestList.addAll(loopTestList);
            }
        }
        return mergedTestList;
    }

    /**
     * 统计分析数据状态数量并返回状态
     *
     * @param dataStatus 分析数据状态
     * @param numMap     统计Map
     * @return 状态
     */
    private String setAnalyseNumAndStatus(Integer dataStatus, Map<String, Integer> numMap) {
        String status = "";
        if (StringUtil.isNotNull(dataStatus)) {
            // 待检测
            if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.未测.getValue())) {
                numMap.put("wait", StringUtil.isNotNull(numMap.get("wait")) ? numMap.get("wait") + 1 : 0);
                status = "待测";
            } else {//检测中
                if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.拒绝.getValue()) ||
                        dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.在测.getValue())) {
                    numMap.put("test", StringUtil.isNotNull(numMap.get("test")) ? numMap.get("test") + 1 : 0);
                    status = "在检";
                } else {//复核中
                    if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue())) {
                        numMap.put("checkDeal", StringUtil.isNotNull(numMap.get("checkDeal")) ? numMap.get("checkDeal") + 1 : 0);
                        status = "复核";
                    } else {//审核中
                        if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())) {
                            numMap.put("auditDeal", StringUtil.isNotNull(numMap.get("auditDeal")) ? numMap.get("auditDeal") + 1 : 0);
                            status = "审核";
                        } else {//已确认
                            numMap.put("affirm", StringUtil.isNotNull(numMap.get("affirm")) ? numMap.get("affirm") + 1 : 0);
                            status = "通过";
                        }
                    }
                }
            }
        }
        return status;
    }


    /**
     * 获取月份下详细数据
     *
     * @param sampleList      样品集合
     * @param map             map数据源
     * @param analyseDataList 分析数据
     * @param key1            key1
     * @param key2            key2
     */
    private void getMouthDate(List<DtoSample> sampleList, Map<String, Object> map, List<DtoAnalyseData> analyseDataList, String key1, String key2) {
        Map<String, List<DtoSample>> sampleMap = sampleList.stream().collect(Collectors.groupingBy(p -> DateUtil.dateToString(p.getSamplingTimeBegin(), "yyyy-MM")));
        for (Map.Entry<String, List<DtoSample>> sampleMapEntry : sampleMap.entrySet()) {
            String samplingTime = sampleMapEntry.getKey();
            List<DtoSample> value = sampleMapEntry.getValue();

            LocalDate date = LocalDate.parse(samplingTime + "-01", DateTimeFormatter.ISO_LOCAL_DATE);
            // 获取月份
            int month = date.getMonthValue();
            int[] sampleDetail = (int[]) map.get(key1);
            sampleDetail[month - 1] = value.size();
            map.put(key1, sampleDetail);

            // 获取当前月份下的分析数据
            List<String> sampleIdList = value.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoAnalyseData> analyseDatas = analyseDataList.stream().filter(p -> sampleIdList.contains(p.getSampleId())).collect(Collectors.toList());
            int[] analyseDetail = (int[]) map.get(key2);
            analyseDetail[month - 1] = analyseDatas.size();
            map.put(key2, analyseDetail);
        }
        // 获取按照月份的TOP数据
        if (map.containsKey("mouthTop")) {
            List<DtoSample> sampleTypeTop = StringUtil.isNotEmpty(sampleMap.values()) ? Collections.max(sampleMap.values(), Comparator.comparingInt(List::size)) : new ArrayList<>();
            map.put("mouthTop", new HashMap<String, Object>() {
                {
                    int month = 1;
                    if (StringUtil.isNotEmpty(sampleTypeTop)) {
                        month = Integer.parseInt(DateUtil.dateToString(sampleTypeTop.get(0).getSamplingTimeBegin(), "MM"));
                    }
                    put("topName", month);
                    put("size", sampleTypeTop.size());
                }
            });
        }
    }

    /**
     * 计算滞留天数
     *
     * @param now               当前日期
     * @param sampleReceiveDate 交接日期
     * @return 天数
     */
    private long getRetentionTime(Date now, Date sampleReceiveDate) {
        long nowTime = now.getTime();
        long receiveDateTime = sampleReceiveDate.getTime();
        long diffInMillies = nowTime - receiveDateTime;
        long convert = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
        if (diffInMillies < TimeUnit.DAYS.toMillis(1) && convert == 0) {
            // 如果不足一天，则按照1天计算
            convert = 1;
        }
        return convert;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setStatusForRecordRepository(StatusForRecordRepository statusForRecordRepository) {
        this.statusForRecordRepository = statusForRecordRepository;
    }

    @Autowired
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setProject2WorkSheetFolderRepository(Project2WorkSheetFolderRepository project2WorkSheetFolderRepository) {
        this.project2WorkSheetFolderRepository = project2WorkSheetFolderRepository;
    }

    @Autowired
    public void setTestPostRepository(TestPostRepository testPostRepository) {
        this.testPostRepository = testPostRepository;
    }

    @Autowired
    public void setTestPost2TestRepository(TestPost2TestRepository testPost2TestRepository) {
        this.testPost2TestRepository = testPost2TestRepository;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setOrgService(IOrgService orgService) {
        this.orgService = orgService;
    }

    @Autowired
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }

    @Autowired
    public void setAnalyseDataFutureService(AnalyseDataFutureService analyseDataFutureService) {
        this.analyseDataFutureService = analyseDataFutureService;
    }

    @Autowired
    public void setSampleGroupRepository(SampleGroupRepository sampleGroupRepository) {
        this.sampleGroupRepository = sampleGroupRepository;
    }
}
