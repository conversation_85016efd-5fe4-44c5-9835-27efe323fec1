package com.sinoyd.lims.api.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.entity.SampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.*;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.api.dto.customer.DtoParamsDataApiPhone;
import com.sinoyd.lims.api.dto.customer.DtoSampleCopy;
import com.sinoyd.lims.api.dto.customer.DtoSampleParamsApiPhone;
import com.sinoyd.lims.api.service.FieldMonitoringService;
import com.sinoyd.lims.foreign.service.ISampleCodeService;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.QualityControl;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.ArrayUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FieldMonitoringServiceImpl extends BaseJpaServiceImpl<DtoProject, String, ProjectRepository> implements FieldMonitoringService {

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private CommonRepository comRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    private FolderSignRepository folderSignRepository;

    @Autowired
    @Lazy
    private SchemeService schemeService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    private ReceiveSubSampleRecord2SampleRepository sub2SampleRepository;

    @Autowired
    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    private SampleGroupRepository sampleGroupRepository;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Autowired
    @Lazy
    private ParamsDataService paramsDataService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private SampleGroupService sampleGroupService;

    @Autowired
    @Lazy
    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    @Autowired
    @Lazy
    private SampleTypeGroupRepository sampleTypeGroupRepository;

    @Autowired
    @Lazy
    private SampleTypeGroup2TestService sampleTypeGroup2TestService;

    @Autowired
    @Lazy
    private CodeService codeService;

    @Autowired
    private QualityControlRepository qualityControlRepository;

    private IConfigService configService;

    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    private ParamsFormulaService paramsFormulaService;

    private ParamsTestFormulaService paramsTestFormulaService;

    private AnalyseDataService analyseDataService;

    private SystemConfigService systemConfigService;

    private EnterpriseRepository enterpriseRepository;

    private StatusForRecordRepository statusForRecordRepository;

    private StatusForRecordService statusForRecordService;

    private AuthorizeService authorizeService;

    private DimensionRepository dimensionRepository;

    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    private DocumentService documentService;

    private DocumentRepository documentRepository;
    private ConsumableService consumableService;
    private QualityControlEvaluateRepository qualityControlEvaluateRepository;
    private DimensionService dimensionService;

    private AnalyzeMethodService analyzeMethodService;
    private ISampleCodeService sampleCodeService;
    private SampleTypeService sampleTypeService;

    /**
     * 项目查询
     *
     * @param pb                   pagebean
     * @param projectPhoneCriteria 查询条件
     */
    @Override
    public List<DtoProjectPhone> getProjectList(PageBean<DtoProject> pb, BaseCriteria projectPhoneCriteria) {
        pb.setEntityName("DtoProject p,DtoProjectPlan pl");
        pb.setSelect("select p,pl,json_value(json,'$.contractCode') as contractCode,json_value(json,'$.collectionStatus') as collectionStatus" +
                ",json_value(json,'$.collectionDetail') as collectionDetail,json_value(json,'$.notSampled') as notSampled,json_value(json,'$.analyzeSummary') as analyzeSummary" +
                ",json_value(json,'$.analyzeDetail') as analyzeDetail,json_value(json,'$.reportDetail') as reportDetail,json_value(json,'$.dataChangeStatus') as dataChangeStatus");
        comRepository.findByPage(pb, projectPhoneCriteria);
        List<DtoProject> datas = pb.getData();
        Iterator<DtoProject> projectIte = datas.iterator();
        List<DtoProjectPhone> projectPhoneList = new ArrayList<>();

        List<String> projectIds = new ArrayList<>();
        List<String> typeIds = new ArrayList<>();
        while (projectIte.hasNext()) {
            Object obj = projectIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoProject project = (DtoProject) objs[0];
            DtoProjectPlan plan = (DtoProjectPlan) objs[1];
            project.loadFromPlan(plan);
            DtoProjectPhone projectPhone = new DtoProjectPhone();
            projectPhone.setId(project.getId());
            projectPhone.setProjectCode(project.getProjectCode());
            projectPhone.setProjectName(project.getProjectName());
            projectPhone.setProjectTypeId(project.getProjectTypeId());
            projectPhone.setProjectTypeName(project.getProjectTypeName());
            projectPhone.setInputTime(DateUtil.dateToString(project.getInputTime(), DateUtil.YEAR));
            projectPhone.setInspectedEntId(project.getInspectedEntId());
            projectPhone.setInspectedEntName(project.getInspectedEnt());
            projectPhone.setGrade(project.getGrade());
            projectPhone.setProjectStatus(project.getStatus());
            projectIds.add(project.getId());
            typeIds.add(project.getProjectTypeId());
            projectPhoneList.add(projectPhone);
        }
        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
        List<DtoSample> sampleList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectIds)) {
            receiveSampleRecordList = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
            sampleList = sampleRepository.findByProjectIdIn(projectIds);
        }
        List<DtoProjectType> projectTypeList = new ArrayList<>();
        if (StringUtil.isNotEmpty(typeIds)) {
            projectTypeList = projectTypeService.findAll(typeIds);
        }
        for (DtoProjectPhone projectPhone : projectPhoneList) {
            List<DtoSample> allProSampleList = sampleList.stream().filter(p -> projectPhone.getId().equals(p.getProjectId())).collect(Collectors.toList());
            List<DtoSample> samList = sampleList.stream().filter(p -> projectPhone.getId().equals(p.getProjectId())
                    && UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).collect(Collectors.toList());

            List<DtoReceiveSampleRecord> proRecordList = receiveSampleRecordList.stream().filter(p -> projectPhone.getId().equals(p.getProjectId())).collect(Collectors.toList());

            List<DtoReceiveSampleRecord> recordList = proRecordList.stream().filter(p -> EnumPRO.EnumReceiveUploadStatus.未提交.getValue().equals(p.getUploadStatus())).collect(Collectors.toList());

            if (samList.size() > 0 || recordList.size() > 0 || allProSampleList.size() == 0) {
                projectPhone.setStatus("未完成");
            } else {
                projectPhone.setStatus("已完成");
            }
            if (proRecordList.size() > 0) {
                projectPhone.setReceiveIds(proRecordList.stream().map(DtoReceiveSampleRecord::getId).distinct().collect(Collectors.toList()));
            }
            DtoProjectType projectType = projectTypeList.stream().filter(p -> projectPhone.getProjectTypeId().equals(p.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(projectType)) {
                projectPhone.setProjectTypeName(projectType.getName());
            }
        }
        return projectPhoneList;
    }

    @Override
    public List<DtoProjectPhone> getReceiveProjectList(PageBean<DtoReceiveSampleRecord> pb, BaseCriteria receivePhoneCriteria) {
        pb.setEntityName("DtoReceiveSampleRecord r,DtoProject p,DtoProjectPlan pl");
        pb.setSelect("select r,p,pl");
        comRepository.findByPage(pb, receivePhoneCriteria);
        List<DtoReceiveSampleRecord> datas = pb.getData();
        Iterator<DtoReceiveSampleRecord> projectIte = datas.iterator();

        List<DtoProjectPhone> projectPhoneList = new ArrayList<>();
        List<String> typeIds = new ArrayList<>();
        List<String> receiveIds = new ArrayList<>();
        while (projectIte.hasNext()) {
            Object obj = projectIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoProject project = (DtoProject) objs[1];
            DtoProjectPlan plan = (DtoProjectPlan) objs[2];
            DtoReceiveSampleRecord receiveSampleRecord = (DtoReceiveSampleRecord) objs[0];
            project.loadFromPlan(plan);
            DtoProjectPhone projectPhone = new DtoProjectPhone();
            projectPhone.setId(project.getId());
            projectPhone.setProjectCode(project.getProjectCode());
            projectPhone.setReceiveCode(receiveSampleRecord.getRecordCode());
            projectPhone.setProjectName(project.getProjectName());
            projectPhone.setProjectTypeId(project.getProjectTypeId());
            projectPhone.setProjectTypeName(project.getProjectTypeName());
            projectPhone.setInputTime(DateUtil.dateToString(project.getInputTime(), DateUtil.YEAR));
            projectPhone.setInspectedEntId(project.getInspectedEntId());
            projectPhone.setInspectedEntName(project.getInspectedEnt());
            projectPhone.setGrade(project.getGrade());
            projectPhone.setProjectStatus(project.getStatus());
            projectPhone.setReceiveIds(Arrays.asList(receiveSampleRecord.getId()));
            typeIds.add(project.getProjectTypeId());
            receiveIds.add(receiveSampleRecord.getId());
            projectPhoneList.add(projectPhone);
        }

        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
        List<DtoSample> sampleList = new ArrayList<>();
        if (StringUtil.isNotEmpty(receiveIds)) {
            receiveSampleRecordList = receiveSampleRecordRepository.findAll(receiveIds);
            sampleList = sampleRepository.findByReceiveIdIn(receiveIds);
        }

        List<DtoProjectType> projectTypeList = new ArrayList<>();
        if (StringUtil.isNotEmpty(typeIds)) {
            projectTypeList = projectTypeService.findAll(typeIds);
        }
        for (DtoProjectPhone projectPhone : projectPhoneList) {
            //移动端状态处理: 2023/11/22长城提: 移动端状态独立，不受平台端提交或添加样品的影响，所以此处只判断移动端状态，移除对样品的判断
            List<DtoReceiveSampleRecord> recordList = receiveSampleRecordList
                    .stream().filter(p -> projectPhone.getReceiveIds().contains(p.getId())
                            && EnumPRO.EnumReceiveUploadStatus.未提交.getValue().equals(p.getUploadStatus()))
                    .collect(Collectors.toList());
            projectPhone.setStatus(recordList.size() > 0 ? "未完成" : "已完成");

            DtoProjectType projectType = projectTypeList.stream().filter(p -> projectPhone.getProjectTypeId().equals(p.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(projectType)) {
                projectPhone.setProjectTypeName(projectType.getName());
            }
        }
        return projectPhoneList;
    }

    /**
     * 通过编码查询项目类型
     *
     * @param code   编码
     * @param values 类型
     * @return 项目类型集合
     */
    @Override
    public List<Map<String, Object>> getProjectTypeList(String code, String[] values) {
        return projectTypeService.getProjectTypeByCode(code, values);
    }

    /**
     * 获取样品类型小类
     *
     * @return 样品类型小类
     */
    @Override
    public List<TreeNode> getSampleTypeList() {
        // 获取所有检测类型
        List<DtoSampleType> list = sampleTypeRepository.findAll();
        return handleSampleTypeTree(list);
    }

    /**
     * 地图底图是否使用天地图
     * true 是，false 否
     *
     * @return 返回判断
     */
    @Override
    public boolean isWorldEarth() {
        return receiveSampleRecordService.isWorldEarth();
    }

    /**
     * 处理检测类型树结构
     *
     * @param list 所有检测类型
     * @return 树结构检测类型
     */
    private List<TreeNode> handleSampleTypeTree(List<DtoSampleType> list) {
        //将检测类型转换成树类型
        ArrayList<TreeNode> sampleLists = new ArrayList<>();
        for (SampleType var : list) {
            if (var.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())
                    || var.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue())) {
                TreeNode node = new TreeNode();
                node.setId(var.getId());
                node.setParentId(var.getParentId());
                node.setCategory(var.getCategory());
                node.setLabel(var.getTypeName());
                node.setOrderNum(var.getOrderNum());
                node.setExtent1(var.getIcon());
                String type = "sampleType" + "~" + var.getIndustryTypeId();
                node.setType(type);
                node.setChildren(new ArrayList<>());
                sampleLists.add(node);
            }
        }

        List<TreeNode> sampleTypeTree = new ArrayList<>();//检测类型树的大类
        //遍历检测类型,将list类型转换成tree树
        //排序
        List<TreeNode> lists = sampleLists.stream().sorted(Comparator.comparing(TreeNode::getOrderNum).reversed()).collect(Collectors.toList());
        for (TreeNode var : lists) {
            for (TreeNode li : lists) {
                if (li.getParentId().equals(var.getId())) {
                    var.setIsLeaf(false);
                    var.getChildren().add(li);
                } else {
                    var.setIsLeaf(true);
                }
            }
            //检测类型大类
            if (var.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()) &&
                    var.getParentId().equals(UUIDHelper.GUID_EMPTY)) {
                sampleTypeTree.add(var);
            }
        }
        return sampleTypeTree;
    }

    /**
     * 获取项目详情
     *
     * @param projectId 项目id
     * @return 项目详情
     */
    @Override
    public DtoProjectDetailPhone getProjectDetailById(String projectId) {
        DtoProjectDetailPhone projectDetail = new DtoProjectDetailPhone();
        DtoProject project = projectService.findOne(projectId);
        projectDetail.setProjectCode(project.getProjectCode());
        projectDetail.setProjectName(project.getProjectName());
        projectDetail.setCustomerRequired(project.getCustomerRequired());
        projectDetail.setGrade(project.getGrade());
        projectDetail.setProjectTypeId(project.getProjectTypeId());
        projectDetail.setProjectTypeName(project.getProjectTypeName());
        projectDetail.setInputTime(project.getInputTime());
        projectDetail.setLeaderId(project.getLeaderId());
        if (StringUtil.isNotNull(project.getLeaderId())) {
            DtoPerson person = personRepository.findOne(project.getLeaderId());
            if (StringUtil.isNotNull(person)) {
                projectDetail.setLeaderName(person.getCName());
            }
        }
        projectDetail.setRemark(project.getRemark());
        projectDetail.setStatus(project.getStatus());
        projectDetail.setInspectedEntId(project.getInspectedEntId());
        projectDetail.setInspectedEntName(project.getInspectedEnt());
        projectDetail.setAddress(project.getInspectedAddress());
        projectDetail.setId(project.getId());
        projectDetail.setReportMakerId(project.getReportMakerId());
        if (StringUtil.isNotNull(project.getLeaderId())) {
            DtoPerson person = personRepository.findOne(project.getReportMakerId());
            if (StringUtil.isNotNull(person)) {
                projectDetail.setReportMakerName(person.getCName());
            }
        }
        projectDetail.setDeadLine(project.getDeadLine());
        projectDetail.setInspectedLinkMan(project.getInspectedLinkMan());
        projectDetail.setInspectedLinkPhone(project.getInspectedLinkPhone());
        projectDetail.setCustomerId(project.getCustomerId());
        projectDetail.setCustomerName(project.getCustomerName());
        projectDetail.setMonitorPurp(project.getMonitorPurp());
        projectDetail.setMonitorMethods(project.getMonitorMethods());
        projectDetail.setInceptPersonId(project.getInceptPersonId());
        projectDetail.setInceptTime(project.getInceptTime());


        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findByProjectId(projectId).stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(record)) {
            List<DtoReceiveSubSampleRecord> subSampleRecordList = receiveSubSampleRecordRepository.findByReceiveId(record.getId());
            projectDetail.setReceiveId(record.getId());
            projectDetail.setSenderId(record.getSenderId());
            projectDetail.setSenderName(record.getSenderName());
            projectDetail.setSendTime(record.getSendTime());
            projectDetail.setSamplingTime(record.getSamplingTime());
            projectDetail.setReceiveType(EnumPRO.EnumReceiveType.现场送样.getValue());
            projectDetail.setReceiveRemark(record.getRemark());
            String xcSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("XC")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
            String fxSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("FX")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
            projectDetail.setSubFXId(fxSubId);
            projectDetail.setSubXCId(xcSubId);
            DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordService.findOne(record.getId());
            if (StringUtil.isNotEmpty(receiveSampleRecord.getSamplingPersonNames())) {
                projectDetail.setSamPersonIds(receiveSampleRecord.getSamplingPersonIds());
                projectDetail.setSamplingPersonNames(String.join(",", receiveSampleRecord.getSamplingPersonNames()));
            }
        }

        return projectDetail;
    }

    /**
     * 点位集合
     *
     * @param projectId     项目id
     * @param folderName    点位名称
     * @param sampleTypeIds 样品类型id
     * @param cycleOrder    周期
     * @param timesOrder    次数
     * @return 点位信息集合
     */
    @Override
    public List<DtoSampleFolderPhone> getFolderList(String projectId, String folderName, List<String> sampleTypeIds, Integer cycleOrder, Integer timesOrder) {
        List<DtoSampleFolderPhone> sampleFolderPhoneList = new ArrayList<>();
        Map<String, Object> values = new HashMap<>();
        StringBuilder select = new StringBuilder();
        select.append("select f from ");
        select.append(" DtoSampleFolder f");
        select.append(" where f.isDeleted = 0 and f.projectId =:projectId ");
        values.put("projectId", projectId);
        if (StringUtil.isNotEmpty(folderName)) {
            select.append(" and f.watchSpot like :folderName ");
            values.put("folderName", "%" + folderName + "%");
        }
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            select.append(" and f.sampleTypeId in :sampleTypeIds ");
            values.put("sampleTypeIds", sampleTypeIds);
        }
        if (!EnumPRO.EnumStatus.所有.getValue().equals(cycleOrder) || !EnumPRO.EnumStatus.所有.getValue().equals(timesOrder)) {
            if (!EnumPRO.EnumStatus.所有.getValue().equals(cycleOrder) && !EnumPRO.EnumStatus.所有.getValue().equals(timesOrder)) {
                select.append(" and exists ( select 1 from DtoSample where isDeleted = 0 and sampleFolderId = f.id " +
                        "and cycleOrder =:cycleOrder and timesOrder =:timesOrder ) ");
                values.put("cycleOrder", cycleOrder);
                values.put("timesOrder", timesOrder);
            } else if (!EnumPRO.EnumStatus.所有.getValue().equals(cycleOrder)) {
                select.append(" and exists ( select 1 from DtoSample where isDeleted = 0 and sampleFolderId = f.id " +
                        "and cycleOrder =:cycleOrder ) ");
                values.put("cycleOrder", cycleOrder);
            } else if (!EnumPRO.EnumStatus.所有.getValue().equals(timesOrder)) {
                select.append(" and exists ( select 1 from DtoSample where isDeleted = 0 and sampleFolderId = f.id " +
                        "and timesOrder =:timesOrder ) ");
                values.put("timesOrder", timesOrder);
            }
        }
        List<DtoSampleFolder> dataList = comRepository.find(select.toString(), values);

        if (StringUtil.isNotEmpty(dataList)) {
            List<String> sampleTypeIdList = dataList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypeIdList);
            Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));
            //点位信息
            List<String> folderIds = dataList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
            List<String> typeIds = dataList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(typeIds);
            //原样
            List<DtoSample> sampleList = sampleRepository.findBySampleFolderIdIn(folderIds);
            List<String> receiveIds = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
            List<DtoAnalyseData> analyseDataList = new ArrayList<>();
            if (StringUtil.isNotEmpty(receiveIds)) {
                receiveSampleRecordList = receiveSampleRecordRepository.findAll(receiveIds);
            }
            if (StringUtil.isNotEmpty(sampleIds)) {
                //对应的质控样
                List<DtoSample> qcSampleList = sampleRepository.findByAssociateSampleIdIn(sampleIds);
                if (StringUtil.isNotEmpty(qcSampleList)) {
                    List<String> qcSamIds = qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    sampleIds.addAll(qcSamIds);
                }
                analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            }
            sampleFolderPhoneList = getFolderInfo(dataList, sampleTypeList, sampleList, analyseDataList, receiveSampleRecordList, samTypeMap);
            for (DtoSampleFolderPhone sampleFolder : sampleFolderPhoneList) {
                //点位周期下的样品
                List<DtoSample> allFolderSampleList = sampleList.stream().filter(p -> sampleFolder.getId().equals(p.getSampleFolderId()) &&
                        sampleFolder.getCycValue().equals(p.getCycleOrder())).collect(Collectors.toList());
                //未加入采样单的样品
                List<DtoSample> samList = sampleList.stream().filter(p -> sampleFolder.getId().equals(p.getSampleFolderId()) &&
                        sampleFolder.getCycValue().equals(p.getCycleOrder()) && UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).collect(Collectors.toList());
                //未提交的采样单
                List<String> recIds = allFolderSampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).map(DtoSample::getReceiveId).collect(Collectors.toList());
                List<DtoReceiveSampleRecord> recordList = new ArrayList<>();
                if (StringUtil.isNotEmpty(recIds)) {
                    recordList = receiveSampleRecordList.stream().filter(p -> recIds.contains(p.getId())
                            && EnumPRO.EnumReceiveUploadStatus.未提交.getValue().equals(p.getUploadStatus())).collect(Collectors.toList());
                }
                if (samList.size() > 0 || recordList.size() > 0 || allFolderSampleList.size() == 0) {
                    sampleFolder.setStatus("未完成");
                } else {
                    sampleFolder.setStatus("已完成");
                }
            }
        }
        if (!EnumPRO.EnumStatus.所有.getValue().equals(cycleOrder)) {
            sampleFolderPhoneList = sampleFolderPhoneList.stream().filter(p -> p.getCycValue().equals(cycleOrder)).collect(Collectors.toList());
        }
        return sampleFolderPhoneList;
    }


    /**
     * 定义比较器
     *
     * @param folderTemp 排序的Map
     * @return 比较器
     */
    private Comparator<DtoSampleFolderPhone> getComparator(List<DtoSampleFolderPhone> folderTemp) {
        return (a, b) -> SortUtil.compareString(a.getWatchSpot(), b.getWatchSpot());
    }

    /**
     * 获取最大周期和最大次数
     *
     * @param projectId 项目id
     * @return 最大周期和最大次数
     */
    @Override
    public Map<String, Object> getMaxCount(String projectId) {
        List<DtoSample> sampleList = sampleRepository.findByProjectId(projectId);
        Map<String, Object> retMap = new HashMap<>();
        Optional<Integer> cycValue = sampleList.stream().map(DtoSample::getCycleOrder).max(Comparator.comparing(Integer::intValue));
        Optional<Integer> timeValue = sampleList.stream().map(DtoSample::getTimesOrder).max(Comparator.comparing(Integer::intValue));
        if (cycValue.isPresent()) {
            retMap.put("cycleOrder", cycValue.get());
        } else {
            retMap.put("cycleOrder", -1);
        }
        if (timeValue.isPresent()) {
            retMap.put("timesOrder", timeValue.get());
        } else {
            retMap.put("timesOrder", -1);
        }
        return retMap;
    }

    /**
     * 新增采样单
     *
     * @param projectId          项目id
     * @param folderInfo         点位信息
     * @param samplingPersonIds  采样人员
     * @param samplingLeaderId   采样负责人id
     * @param samplingLeaderName 采样负责人名称
     * @param samplingTime       采样时间
     */
    @Transactional
    @Override
    public void createReceiveSampleRecord(String projectId, List<String> folderInfo, List<String> samplingPersonIds, String samplingLeaderId, String samplingLeaderName, Date samplingTime) {
        DtoProject project = projectService.findOne(projectId);
        List<DtoSample> recSamList = getSampleList(projectId, UUIDHelper.GUID_EMPTY, folderInfo);
        if (StringUtil.isNotEmpty(recSamList)) {
            // 生成采样单前，筛选样品编号为空的样品，先生成编号
            handleSampleEmptyCode(recSamList, projectId, samplingTime);

            DtoReceiveSampleRecord record = receiveSampleRecordService.createReceiveRecordToPhone(project, recSamList, samplingTime, samplingLeaderId, samplingLeaderName, samplingPersonIds);
            for (DtoSample sample : recSamList) {
                sample.setReceiveId(record.getId());
                sample.setSamplingStatus(EnumPRO.EnumSamplingStatus.采样中.getValue());
                sample.setSamplingTimeBegin(samplingTime);
                sample.setSamplingTimeEnd(samplingTime);
                sample.setSamplingPersonId(samplingLeaderId);
                sample.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                sample.setModifyDate(new Date());
            }
            //修改样品状态
            if (recSamList.size() > 0) {
                sampleRepository.save(recSamList);
                sampleRepository.flush();
            }
            //设置样品现场分析数据的分析时间为采样时间
            analyseDataService.setAnalyzeTimeForSample(recSamList, samplingTime);
            proService.checkPrepareSample(project, recSamList);

            List<String> sampleIds = recSamList.stream().map(DtoSample::getId).collect(Collectors.toList());
            flowToReceiveSample(record, sampleIds);
            //todo 判断样品是否存在样品编号，如果没有就自动添加样品编号
//            List<String> sampleIds = recSamList.stream().filter(p -> !StringUtil.isNotEmpty(p.getCode())).map(DtoSample::getId).collect(Collectors.toList());
//            if (StringUtil.isNotEmpty(sampleIds)) {
//                this.createSampleCode(sampleIds, projectId, new Date());
//            }
        }
    }

    /**
     * 如有实验室指标则同时流转到样品交接(需要根据配置开关决定)
     *
     * @param dtoReceiveSampleRecord 送样单
     */
    private void flowToReceiveSample(DtoReceiveSampleRecord dtoReceiveSampleRecord, List<String> sampleIds) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        analyseDataList = analyseDataList.parallelStream().filter(p -> !p.getIsCompleteField() && !p.getIsSamplingOut()
                && !p.getIsOutsourcing()).collect(Collectors.toList());
        if (analyseDataList.size() > 0) {
            DtoCode code = codeService.findByCode(ProCodeHelper.LIM_FLOW_RECEIVE_SAMPLE);
            if (code != null && "1".equals(code.getDictValue())) {
                if (EnumPRO.EnumReceiveType.内部送样.getValue().equals(dtoReceiveSampleRecord.getReceiveType())) {
                    DtoStatusForRecord record = statusForRecordRepository.findByReceiveIdAndModule(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                    if (!StringUtil.isNotNull(record)) {
                        statusForRecordService.createStatus(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                    }
                }
            }
        }
    }

    /**
     * 新增采样单
     *
     * @param receiveSampleRecord 送样单信息
     * @return 送样单信息
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecordPhone createReceiveSampleRecord(DtoReceiveSampleRecord receiveSampleRecord) {
        //人要重新获取
        String senderName = personRepository.findOne(receiveSampleRecord.getSenderId()).getCName();
        receiveSampleRecord.setSenderName(senderName);
        List<String> personNames = personRepository.findAll(receiveSampleRecord.getSamplingPersonIds()).stream().map(DtoPerson::getCName).distinct().collect(Collectors.toList());
        receiveSampleRecord.setSamplingPersonNames(personNames);
        DtoReceiveSampleRecord record = receiveSampleRecordService.save(receiveSampleRecord, true);

        DtoReceiveSampleRecordPhone recordPhone = new DtoReceiveSampleRecordPhone();
        DtoProject project = projectService.findOne(receiveSampleRecord.getProjectId());
        //送样信息
        recordPhone.setId(record.getId());
        recordPhone.setSenderId(record.getSenderId());
        recordPhone.setSenderName(record.getSenderName());
        recordPhone.setSamplingPersonIds(record.getSamplingPersonIds());
        recordPhone.setSamplingPersonNames(String.join(",", record.getSamplingPersonNames()));
        recordPhone.setSamplingTime(DateUtil.dateToString(record.getSamplingTime(), DateUtil.YEAR));
        recordPhone.setRecordRemark(record.getRemark());

        //项目信息
        recordPhone.setProjectId(project.getId());
        recordPhone.setProjectCode(project.getProjectCode());
        recordPhone.setProjectName(project.getProjectName());
        recordPhone.setInspectedEnt(project.getInspectedEnt());
        recordPhone.setInspectedEntId(project.getInspectedEntId());
        recordPhone.setProjectTypeId(project.getProjectTypeId());
        recordPhone.setProjectTypeName(project.getProjectTypeName());
        recordPhone.setLeaderName(project.getLeaderName());
        recordPhone.setLeaderId(project.getLeaderId());
        recordPhone.setGrade(project.getGrade());
        recordPhone.setMonitorPurp(project.getMonitorPurp());
        recordPhone.setCustomerRequired(project.getCustomerRequired());
        recordPhone.setRemark(project.getRemark());

        List<DtoReceiveSubSampleRecord> subSampleRecordList = receiveSubSampleRecordRepository.findByReceiveId(record.getId());
        String xcSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("XC")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        String fxSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("FX")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        recordPhone.setSubXCId(xcSubId);
        recordPhone.setSubFXId(fxSubId);
        return recordPhone;
    }


    @Override
    public DtoReceiveSampleRecordPhone getRecord(String projectId, String receiveId) {
        DtoReceiveSampleRecordPhone recordPhone = new DtoReceiveSampleRecordPhone();
        DtoReceiveSampleRecord record = receiveSampleRecordService.findOne(receiveId);
        DtoProject project = projectService.findOne(projectId);
        DtoEnterprise inspectedEnt = enterpriseRepository.findOne(project.getInspectedEntId());
        String linkMan = StringUtil.isNotNull(inspectedEnt) ? inspectedEnt.getContactMan() : "";
        String linkPhone = StringUtil.isNotNull(inspectedEnt) ? inspectedEnt.getContactTelPhone() : "";
        String address = StringUtil.isNotNull(inspectedEnt) ? inspectedEnt.getAddress() : "";
        //送样信息
        recordPhone.setId(record.getId());
        recordPhone.setSenderId(record.getSenderId());
        recordPhone.setSenderName(record.getSenderName());
        recordPhone.setSamplingPersonIds(record.getSamplingPersonIds());
        recordPhone.setSamplingPersonNames(String.join(",", record.getSamplingPersonNames()));
        recordPhone.setSamplingTime(DateUtil.dateToString(record.getSamplingTime(), DateUtil.YEAR));
        recordPhone.setRecordRemark(record.getRemark());
        recordPhone.setSendTime(record.getSendTime());

        //项目信息
        recordPhone.setProjectId(project.getId());
        recordPhone.setProjectCode(project.getProjectCode());
        recordPhone.setProjectName(project.getProjectName());
        recordPhone.setInspectedEnt(project.getInspectedEnt());
        recordPhone.setInspectedEntId(project.getInspectedEntId());
        recordPhone.setInspectedAddress(project.getInspectedAddress());
        recordPhone.setProjectTypeId(project.getProjectTypeId());
        recordPhone.setProjectTypeName(project.getProjectTypeName());
        recordPhone.setLeaderId(project.getLeaderId());
        if (StringUtil.isNotEmpty(project.getLeaderId())) {
            DtoPerson person = personRepository.findOne(project.getLeaderId());
            if (StringUtil.isNotNull(person)) {
                String leaderName = person.getCName();
                recordPhone.setLeaderName(leaderName);
            }
        }
        recordPhone.setGrade(project.getGrade());
        recordPhone.setMonitorPurp(project.getMonitorPurp());
        recordPhone.setCustomerRequired(project.getCustomerRequired());
        recordPhone.setRemark(project.getRemark());
        recordPhone.setInspectedLinkMan(StringUtil.isNotEmpty(project.getInspectedLinkMan()) ? project.getInspectedLinkMan() : linkMan);
        recordPhone.setInspectedLinkPhone(StringUtil.isNotEmpty(project.getInspectedLinkPhone()) ? project.getInspectedLinkPhone() : linkPhone);
        recordPhone.setInspectedAddress(StringUtil.isNotEmpty(project.getInspectedAddress()) ? project.getInspectedAddress() : address);

        List<DtoReceiveSubSampleRecord> subSampleRecordList = receiveSubSampleRecordRepository.findByReceiveId(receiveId);
        String xcSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("XC")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        String fxSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("FX")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        recordPhone.setSubXCId(xcSubId);
        recordPhone.setSubFXId(fxSubId);
        return recordPhone;
    }

    /**
     * 加入已有采样单
     *
     * @param projectId  项目id
     * @param receiveId  送样单id
     * @param folderInfo 点位信息
     */
    @Transactional
    @Override
    public void joinToReceiveSampleRecord(String projectId, String receiveId, List<String> folderInfo) {
        List<DtoSample> recSamList = getSampleList(projectId, UUIDHelper.GUID_EMPTY, folderInfo);
        if (StringUtil.isNotEmpty(recSamList)) {
            DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordService.findOne(receiveId);
            // 生成采样单前，筛选样品编号为空的样品，先生成编号
            handleSampleEmptyCode(recSamList, projectId, receiveSampleRecord.getSamplingTime());

            receiveSampleRecordService.joinRecord(receiveId, recSamList.stream().map(DtoSample::getId).collect(Collectors.toList()));
            DtoProject project = projectService.findOne(projectId);
            proService.checkPrepareSample(project, recSamList);
            //todo 判断样品是否存在样品编号，如果没有就自动添加样品编号
//            List<String> sampleIds = recSamList.stream().filter(p -> !StringUtil.isNotEmpty(p.getCode())).map(DtoSample::getId).collect(Collectors.toList());
//            if (StringUtil.isNotEmpty(sampleIds)) {
//                this.createSampleCode(sampleIds, projectId, new Date());
//            }
        }
    }

    /**
     * 通过项目id获取送样单集合
     *
     * @param projectId 项目id
     * @return 送样单集合
     */
    @Override
    public List<DtoReceiveSampleRecordPhone> getReceiveSampleRecordByProjectId(String projectId, List<String> sampleTypeIds, Boolean isFilterSamPerson) {
        List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findByProjectId(projectId);
        List<DtoReceiveSampleRecordPhone> recordList = new ArrayList<>();
        List<DtoSample> sampleList = sampleRepository.findByProjectId(projectId);
        List<DtoSampleFolder> sampleFolderList = sampleFolderRepository.findByProjectId(projectId);
        List<String> samTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());

        List<DtoSamplingPersonConfig> personCfgs = samplingPersonConfigRepository.findByObjectIdIn(receiveSampleRecordList.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList()));

        if (StringUtil.isNotEmpty(samTypeIds)) {
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(samTypeIds);
            for (DtoReceiveSampleRecord receiveSampleRecord : receiveSampleRecordList) {
                DtoReceiveSampleRecordPhone record = new DtoReceiveSampleRecordPhone();
                List<DtoSample> recSamList = sampleList.stream().filter(p -> receiveSampleRecord.getId().equals(p.getReceiveId())).collect(Collectors.toList());
                List<String> folderIds = recSamList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
                List<String> folderNames = sampleFolderList.stream().filter(p -> folderIds.contains(p.getId()))
                        .map(DtoSampleFolder::getWatchSpot).collect(Collectors.toList());
                List<String> typeIds = recSamList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toList());
                //加入已有采样单需要过滤下采样单的样品类型，相同项目且相同样品类型的才可以添加
                if (!StringUtil.isNotEmpty(sampleTypeIds) || sampleTypeIds.containsAll(typeIds)) {
                    List<String> typeNames = sampleTypeList.stream().filter(p -> typeIds.contains(p.getId())).map(DtoSampleType::getTypeName)
                            .distinct().collect(Collectors.toList());
                    List<DtoSamplingPersonConfig> recpcfgList = personCfgs.stream().filter(p -> p.getObjectId().equals(receiveSampleRecord.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotNull(recpcfgList) && recpcfgList.size() > 0) {
                        record.setSamplingPersonNames(String.join(",", recpcfgList.stream().map(DtoSamplingPersonConfig::getSamplingPerson).distinct().collect(Collectors.toList())));
                    }
                    record.setId(receiveSampleRecord.getId());
                    record.setReceiveSampleRecordCode(receiveSampleRecord.getRecordCode());
                    record.setSamplingTime(DateUtil.dateToString(receiveSampleRecord.getSamplingTime(), DateUtil.YEAR_ZH_CN));
                    record.setStatus(EnumPRO.EnumReceiveUploadStatus.未提交.getValue().equals(receiveSampleRecord.getUploadStatus()) ? "未完成" : "已完成");
                    record.setStatusNumber(EnumPRO.EnumReceiveUploadStatus.未提交.getValue().equals(receiveSampleRecord.getUploadStatus()) ? EnumPRO.EnumStatus.待处理.getValue() : EnumPRO.EnumStatus.已处理.getValue());
                    record.setFolderCount(folderIds.size());
                    record.setFolderName(String.join(",", folderNames));
                    record.setTypeNames(String.join(",", typeNames));
                    // 按照采样人员进行过滤
                    if (isFilterSamPerson) {
                        String userId = PrincipalContextUser.getPrincipal().getUserId();
                        List<String> samplingPersonIds = recpcfgList.stream().map(DtoSamplingPersonConfig::getSamplingPersonId).distinct().collect(Collectors.toList());
                        if (receiveSampleRecord.getSenderId().equals(userId) || samplingPersonIds.contains(userId)) {
                            recordList.add(record);
                        }
                    } else {
                        recordList.add(record);
                    }
                }
            }
        }
        //加入已有采样单的时候需要过滤已完成的采样单
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            recordList = recordList.stream().filter(p -> "未完成".equals(p.getStatus())).collect(Collectors.toList());
        }
        return recordList.stream().sorted(Comparator.comparing(DtoReceiveSampleRecordPhone::getStatusNumber)).collect(Collectors.toList());
    }

    /**
     * 通过点位获取点位信息
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @param receiveId  送样单id
     * @return 点位信息
     */
    @Override
    public DtoSampleFolderPhone getFolderById(String folderId, Integer cycleOrder, String receiveId) {
        DtoSampleFolderPhone folderPhone = new DtoSampleFolderPhone();
        DtoSampleFolder folder = sampleFolderRepository.findOne(folderId);
        List<DtoSample> sampleList = sampleRepository.findBySampleFolderId(folderId);
        if (StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            sampleList.removeIf(p -> !receiveId.equals(p.getReceiveId()));
        }
        //原样
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        //对应的质控样
        List<DtoSample> qcSampleList = sampleRepository.findByAssociateSampleIdIn(sampleIds);
        List<String> qcSampleIds = qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        sampleIds.addAll(qcSampleIds);
        String testNames = "";
        Integer analyzeItemNum = 0;
        String regex = "^0\\.0*$";
        if (StringUtil.isNotNull(folder)) {
            folderPhone.setSampleFolderName(folder.getWatchSpot());
            folderPhone.setSampleTypeId(folder.getSampleTypeId());
            DtoSampleType sampleType = sampleTypeRepository.findOne(folder.getSampleTypeId());
            if (StringUtil.isNotNull(sampleType)) {
                folderPhone.setSampleTypeName(sampleType.getTypeName());
            }
            // 获取点位上的计划经纬度
            folderPhone.setPlanLon(folder.getLon());
            folderPhone.setPlanLat(folder.getLat());
            if (StringUtil.isNull(folderPhone.getPlanLon()) || folderPhone.getPlanLon().matches(regex)) {
                folderPhone.setPlanLon("");
            }
            if (StringUtil.isNull(folderPhone.getPlanLat()) || folderPhone.getPlanLat().matches(regex)) {
                folderPhone.setPlanLat("");
            }
        }

        //获取所有的数据，对应找所有的测试项目
        if (StringUtil.isNotEmpty(sampleIds)) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testService.findAll(testIds) : new ArrayList<>();
            List<String> parentTestIds = tests.stream().filter(t -> StringUtils.isNotNullAndEmpty(t.getParentId()) && !UUIDHelper.GUID_EMPTY.equals(t.getParentId())).map(DtoTest::getParentId).distinct().collect(Collectors.toList());
            List<DtoTest> parentTests = StringUtil.isNotEmpty(parentTestIds) ? testService.findAll(parentTestIds) : new ArrayList<>();
            List<String> analyzeItemNames = new ArrayList<>();
            analyzeItemNum = tests.size();
            tests.stream().collect(Collectors.groupingBy(DtoTest::getParentId)).forEach((parentId, childTests) -> {
                DtoTest parentTest = parentTests.stream().filter(t -> t.getId().equals(parentId)).findFirst().orElse(null);
                if (UUIDHelper.GUID_EMPTY.equals(parentId) || StringUtil.isNull(parentTest)) {
                    analyzeItemNames.addAll(childTests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
                } else if (parentTest != null) {
                    if (childTests.size() >= parentTest.getMergeBase()) {
                        analyzeItemNames.add(parentTest.getRedAnalyzeItemName() + "(" + childTests.size() + ")");
                    } else {
                        analyzeItemNames.addAll(childTests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
                    }
                }
            });
            Collections.sort(analyzeItemNames);
            if (StringUtil.isNotEmpty(analyzeItemNames)) {
                testNames = String.join("、", analyzeItemNames);
            }
        }
        folderPhone.setAnalyzeItems(testNames);
        folderPhone.setAnalyzeCount(analyzeItemNum);
        //找到对应的签到信息
        List<DtoFolderSign> signList = folderSignRepository.findBySampleFolderIdAndCycleOrder(folderId, cycleOrder);
        if (signList.size() > 0) {
            DtoFolderSign folderSign = signList.stream().max(Comparator.comparing(DtoFolderSign::getSignTime)).orElse(null);
            if (StringUtil.isNotNull(folderSign)) {
                folderPhone.setLon(folderSign.getSignLon());
                folderPhone.setLat(folderSign.getSignLat());
                if (StringUtil.isNull(folderPhone.getLon()) || folderPhone.getLon().matches(regex)) {
                    folderPhone.setLon("");
                }
                if (StringUtil.isNull(folderPhone.getLat()) || folderPhone.getLat().matches(regex)) {
                    folderPhone.setLat("");
                }
                folderPhone.setSignPerson(folderSign.getSignPersonName());
                folderPhone.setSignTime(DateUtil.dateToString(folderSign.getSignTime(), DateUtil.FULL));
                folderPhone.setVoiceTip(folderSign.getVoiceTip());
                folderPhone.setFolderSignId(folderSign.getId());
            }
        }
        return folderPhone;
    }

    /**
     * 保存语音说明
     *
     * @param folderSignId 签到信息id
     * @param voiceTip     语音说明
     */
    @Override
    public void saveVoiceTip(String folderSignId, String voiceTip) {
        DtoFolderSign folderSign = folderSignRepository.findOne(folderSignId);
        if (StringUtil.isNotNull(folderSign)) {
            folderSign.setVoiceTip(voiceTip);
            folderSignRepository.save(folderSign);
        }
    }

    /**
     * 判断开关是否开启
     *
     * @return 是否开启
     */
    @Override
    public DtoJudgmentPhone switchIsOpen() {
        DtoJudgmentPhone judgmentPhone = new DtoJudgmentPhone();
        DtoCode code = codeService.findByCode(ProCodeHelper.FACE_RECOGNITION);
        judgmentPhone.setIsFaceOpen(StringUtil.isNotNull(code) && "1".equals(code.getDictValue()));
        ConfigModel configModel = configService.findConfig("phone.company.many");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            judgmentPhone.setIsManyCompany(configModel.getConfigValue());
        }
        return judgmentPhone;
    }

    /**
     * 保存签到信息
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @param lon        经度
     * @param lat        纬度
     * @param signTime   签到时间
     */
    @Transactional
    @Override
    public void saveSignInfo(String folderId, Integer cycleOrder, String lon,
                             String lat, Date signTime, String signTip,
                             Boolean isVerify, String voiceTip) {
        //点位的签到信息
        //DtoSampleFolder sampleFolder = sampleFolderRepository.findOne(folderId);
        //现在点位签到的点位不存在点位上，而是存到了点位签到表上，所以不要再修改点位上设置的经纬度
        //sampleFolder.setLon(lon);
        //sampleFolder.setLat(lat);
        //sampleFolderRepository.save(sampleFolder);

        //添加点位签到表
        DtoFolderSign folderSign = new DtoFolderSign();
        folderSign.setSampleFolderId(folderId);
        folderSign.setCycleOrder(cycleOrder);
        folderSign.setSignTime(signTime);
        folderSign.setSignLon(lon);
        folderSign.setSignLat(lat);
        folderSign.setSignTip(signTip);
        folderSign.setSignPersonId(StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ?
                PrincipalContextUser.getPrincipal().getUserId() : null);
        String personName = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ?
                PrincipalContextUser.getPrincipal().getUserName() : null;
        if (StringUtil.isNotEmpty(personName) && isVerify) {
            personName = String.format("%s（已认证）", personName);
        }
        folderSign.setSignPersonName(personName);
        folderSign.setVoiceTip(voiceTip);
        folderSignRepository.save(folderSign);
    }

    /**
     * 样品列表
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 样品列表
     */
    @Override
    public List<DtoSample> getSampleList(String folderId, String projectId, String receiveId, Integer cycleOrder) {
        return getAllSampleList(folderId, projectId, receiveId, cycleOrder, false, false);
    }

    @Override
    public List<DtoSample> getSampleWithInvalidList(String folderId, String projectId, String receiveId, Integer cycleOrder) {
        return getAllSampleList(folderId, projectId, receiveId, cycleOrder, true, false);
    }

    @Override
    public List<DtoSample> getSampleWithInvalidAndStandardList(String folderId, String projectId, String receiveId, Integer cycleOrder) {
        return getAllSampleList(folderId, projectId, receiveId, cycleOrder, true, true);
    }

    /**
     * @param folderId      点位
     * @param projectId     项目
     * @param receiveId     送样单
     * @param cycleOrder    周期
     * @param hsaInValid    是否包含作废样品
     * @param hsaInStandard 是否包含标样
     * @return 样品列表
     */
    private List<DtoSample> getAllSampleList(String folderId, String projectId, String receiveId, Integer cycleOrder, boolean hsaInValid, boolean hsaInStandard) {
        //找到原样
        List<DtoSample> sampleList = new ArrayList<>();
        if (StringUtil.isNotEmpty(folderId) && !UUIDHelper.GUID_EMPTY.equals(folderId)) {
            if (hsaInValid) {
                sampleList = sampleRepository.findBySampleFolderIdWithInvaild(folderId).stream()
                        .filter(p -> cycleOrder.equals(p.getCycleOrder())).collect(Collectors.toList());
            } else {
                sampleList = sampleRepository.findBySampleFolderId(folderId).stream()
                        .filter(p -> cycleOrder.equals(p.getCycleOrder())).collect(Collectors.toList());
            }
        } else {
            if (hsaInValid) {
                sampleList = sampleRepository.findByProjectIdWithInvaild(projectId);
            } else {
                sampleList = sampleRepository.findByProjectId(projectId);
            }
        }
        if (!UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            sampleList = sampleList.stream().filter(p -> p.getReceiveId().equals(receiveId)).collect(Collectors.toList());
        } else if (!UUIDHelper.GUID_EMPTY.equals(projectId)) {
            sampleList = sampleList.stream().filter(p -> p.getProjectId().equals(projectId)).collect(Collectors.toList());
        }
        sampleList = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).sorted
                (Comparator.comparing(DtoSample::getSampleTypeId).thenComparing(DtoSample::getRedFolderName)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleList)) {
            //找到对应原样的质控样
            List<DtoSample> qcSamList = findXcQcSamples(sampleList);
            sampleList.addAll(qcSamList);
            //质控样信息--串联
            if (StringUtil.isNotEmpty(qcSamList)) {
                List<DtoSample> clSampleList = findXcQcSamples(qcSamList);
                if (StringUtil.isNotEmpty(clSampleList)) {
                    sampleList.addAll(clSampleList);
                }
            }
            sampleList = sampleService.sortPrepareSample(sampleList, false);
            // 包含标样
            if (hsaInStandard) {
                List<String> receiveIds = new ArrayList<>();
                receiveIds.add(receiveId);
                if (UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                    receiveIds = sampleList.stream().map(DtoSample::getReceiveId).distinct().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
                }
                //根据送样单与样品的关系获取关联样品
                List<String> subIdList = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds).stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(subIdList)) {
                    List<String> subSamIdList = sub2SampleRepository.findByReceiveSubSampleRecordIdIn(subIdList).stream()
                            .map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(subSamIdList)) {
                        List<DtoSample> subSamList = sampleRepository.findByIdInAndIsDeletedFalse(subSamIdList);
                        if (StringUtil.isNotEmpty(subSamList)) {
                            List<String> qcIdList = subSamList.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
                            List<DtoQualityControl> bzQcList = qualityControlRepository.findAll(qcIdList).stream().filter(p -> EnumLIM.EnumQCType.标准.getValue().equals(p.getQcType())).collect(Collectors.toList());
                            List<String> bzQcIdList = bzQcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
                            subSamList = subSamList.stream().filter(p -> bzQcIdList.contains(p.getQcId())).collect(Collectors.toList());
                            for (DtoSample sample : subSamList) {
                                bzQcList.stream().filter(p -> p.getId().equals(sample.getQcId())).findFirst().ifPresent(qc -> {
                                    sample.setQcType(qc.getQcType());
                                });
                            }
                            if (StringUtil.isNotEmpty(subSamList)) {
                                subSamList.sort(Comparator.comparing(DtoSample::getRedFolderName));
                                sampleList.addAll(subSamList);
                            }
                        }
                    }
                }
            }
            List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            sampleList.forEach(p -> {
                p.setBigSampleTypeId(UUIDHelper.GUID_EMPTY);
                Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.stream().filter(s -> p.getSampleTypeId().equals(s.getId())).findFirst();
                if (sampleTypeOptional.isPresent()) {
                    p.setBigSampleTypeId(sampleTypeOptional.get().getParentId());
                }
            });
        }
        sampleList = sampleList.stream().filter(s -> !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(s.getSampleCategory())).collect(Collectors.toList());
        return sampleList;
    }

    /**
     * 样品列表
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 样品列表
     */
    @Override
    public List<DtoSample> getSampleList(String folderId, String projectId, List<String> receiveIdList, Integer cycleOrder) {
        //找到原样
        List<DtoSample> sampleList = new ArrayList<>();
        if (StringUtil.isNotEmpty(folderId) && !UUIDHelper.GUID_EMPTY.equals(folderId)) {
            sampleList = sampleRepository.findBySampleFolderId(folderId).stream().filter(p -> cycleOrder.equals(p.getCycleOrder())).collect(Collectors.toList());
        } else {
            sampleList = sampleRepository.findByProjectId(projectId);
        }
        if (StringUtil.isNotEmpty(receiveIdList)) {
            sampleList = sampleList.stream().filter(p -> receiveIdList.contains(p.getReceiveId())).collect(Collectors.toList());
        } else if (!UUIDHelper.GUID_EMPTY.equals(projectId)) {
            sampleList = sampleList.stream().filter(p -> p.getProjectId().equals(projectId)).collect(Collectors.toList());
        }
        sampleList = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).sorted
                (Comparator.comparing(DtoSample::getSampleTypeId).thenComparing(DtoSample::getRedFolderName)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleList)) {
            //找到对应原样的质控样
            List<DtoSample> qcSamList = sampleRepository.findByAssociateSampleIdIn(sampleList.stream().map(DtoSample::getId).collect(Collectors.toList()));
            sampleList.addAll(qcSamList);
            //质控样信息--串联
            if (StringUtil.isNotEmpty(qcSamList)) {
                List<DtoSample> clSampleList = sampleRepository.findByAssociateSampleIdIn(qcSamList.stream().map(DtoSample::getId).collect(Collectors.toList()));
                if (StringUtil.isNotEmpty(clSampleList)) {
                    sampleList.addAll(clSampleList);
                }
            }
            sampleList = sampleService.sortPrepareSample(sampleList, false);
            List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            sampleList.forEach(p -> {
                p.setBigSampleTypeId(UUIDHelper.GUID_EMPTY);
                Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.stream().filter(s -> p.getSampleTypeId().equals(s.getId())).findFirst();
                if (sampleTypeOptional.isPresent()) {
                    p.setBigSampleTypeId(sampleTypeOptional.get().getParentId());
                }
            });
        }
        return sampleList;
    }


    /**
     * 剔除质控样
     *
     * @param qcSampleIds 质控样ids
     */
    @Transactional
    @Override
    public void removeQcSample(String projectId, List<String> qcSampleIds) {
        List<DtoSample> sampleList = sampleRepository.findByIds(qcSampleIds).stream()
                .filter(p -> EnumPRO.EnumSampleCategory.原样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
        if (StringUtil.isEmpty(sampleList)) {
            schemeService.deleteQCSample(projectId, qcSampleIds);
        } else {
            throw new BaseException("选择的样品中包含原样，请确认！");
        }
    }

    /**
     * 创建样品编号
     *
     * @param sampleIds    样品ids
     * @param projectId    项目id
     * @param samplingTime 采样时间
     */
    @Transactional
    @Override
    public void createSampleCode(List<String> sampleIds, String projectId, Date samplingTime) {
        this.createSampleCode(sampleIds, projectId, samplingTime, null);
    }

    /**
     * 创建样品编号
     *
     * @param sampleIds    样品ids
     * @param projectId    项目id
     * @param samplingTime 采样时间
     */
    @Transactional
    @Override
    public void createSampleCode(List<String> sampleIds, String projectId, Date samplingTime, String receiveId) {
        List<DtoSample> samples = sampleRepository.findAll(sampleIds);
        DtoProject project = projectService.findOne(projectId);
        DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
        Map<String, List<DtoSample>> sampleMap = samples.stream().collect(Collectors.groupingBy(DtoSample::getSampleTypeId));
        //需要新增的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //需要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
        List<DtoSample> sampleList = new ArrayList<>();
        for (String sampleTypeId : sampleMap.keySet()) {
            List<DtoSample> samList = sampleMap.get(sampleTypeId);
            samList = sampleService.sortPrepareSample(samList, true);
            if (samList.size() > 0) {
                Map<String, String> codeMap = new HashMap<>();
                for (DtoSample sample : samList) {
                    DtoGenerateSN targetGenerateSN;
                    if (sample.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())) {
                        targetGenerateSN = sampleCodeService.createSampleCode(project, projectType, sampleTypeId, sample.getSampleFolderId(), samplingTime, UUIDHelper.GUID_EMPTY,
                                sample.getId(), true, PrincipalContextUser.getPrincipal().getUserId(), false, null);
                    } else {
                        targetGenerateSN = sampleCodeService.createSampleCode(project, projectType, sampleTypeId, sample.getSampleFolderId(), samplingTime, UUIDHelper.GUID_EMPTY,
                                sample.getId(), true, StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : null,
                                true, sample.getAssociateSampleId(), sample.getQcId(), sample.getSampleCategory(), false, codeMap.getOrDefault(sample.getAssociateSampleId(), ""), null);
                    }

                    //需要新增的序列号
                    if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigCreate())) {
                        serialNumberConfigCreateList.add(targetGenerateSN.getSerialNumberConfigCreate());
                    }
                    //需要修改的序列号
                    if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigUpdate())) {
                        serialNumberConfigUpdateList.add(targetGenerateSN.getSerialNumberConfigUpdate());
                    }

                    codeMap.put(sample.getId(), targetGenerateSN.getCode());

                    sample.setCode(targetGenerateSN.getCode());
                    sample.setSamplingTimeBegin(samplingTime);
                    sample.setSamplingTimeEnd(samplingTime);
                    sample.setSignTime(samplingTime);
                    sample.setSamplingStatus(EnumPRO.EnumSamplingStatus.采样中.getValue());
                    sample.setLastNewSubmitTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
                    sample.setLoseEfficacyTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
                    sample.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    sample.setModifyDate(new Date());
                    if (StringUtil.isNotEmpty(receiveId)) {
                        sample.setReceiveId(receiveId);
                    }
                    sampleList.add(sample);
                }
            }
        }

        if (sampleList.size() > 0) {
            sampleRepository.save(sampleList);
        }
        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }
        sampleService.doSaveCompareJudgeData(sampleList, project);
    }

    /**
     * 样品信息
     *
     * @param samId 样品id
     * @return 样品信息
     */
    @Override
    public DtoSamplePhone getSampleDetail(String samId) {
        DtoSample sample = sampleRepository.findOne(samId);
        DtoSampleType sampleType = sampleTypeRepository.findOne(sample.getSampleTypeId());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(samId);
        List<DtoAnalyseDataPhone> analyseDataPhoneList = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            DtoAnalyseDataPhone analyseDataPhone = new DtoAnalyseDataPhone();
            analyseDataPhone.setAnaId(analyseData.getId());
            analyseDataPhone.setAnalyseMethod(analyseData.getRedAnalyzeMethodName());
            analyseDataPhone.setAnalyseItemName(analyseData.getRedAnalyzeItemName());
            analyseDataPhone.setSampleId(analyseData.getSampleId());
            analyseDataPhone.setIsCompleteField(analyseData.getIsCompleteField());
            analyseDataPhone.setIsOutsourcing(analyseData.getIsOutsourcing());
            analyseDataPhone.setItemStandard(analyseData.getRedCountryStandard());
            analyseDataPhone.setTestId(analyseData.getTestId());
            analyseDataPhoneList.add(analyseDataPhone);
        }
        DtoSamplePhone samplePhone = new DtoSamplePhone();
        samplePhone.setSampleCode(sample.getCode());
        samplePhone.setSamId(sample.getId());
        samplePhone.setFolderName(sample.getRedFolderName());
        samplePhone.setTimesOrder(sample.getTimesOrder());
        samplePhone.setCycleOrder(sample.getCycleOrder());
        samplePhone.setSampleOrder(sample.getSampleOrder());
        samplePhone.setSamplingTimeBegin(sample.getSamplingTimeBegin());
        samplePhone.setAnalyseDataList(analyseDataPhoneList.stream().sorted(Comparator
                .comparing(DtoAnalyseDataPhone::getAnalyseItemName)).collect(Collectors.toList()));
        samplePhone.setSampleTypeId(sample.getSampleTypeId());
        samplePhone.setBigSampleTypeId(sampleType.getParentId());
        return samplePhone;
    }

    /**
     * 测试项目列表
     *
     * @param pb           分页
     * @param testCriteria 检索条件
     * @return 测试项目列表
     */
    @Override
    public List<DtoAnalyseDataPhone> getTestList(PageBean<DtoTest> pb, BaseCriteria testCriteria) {
        // 设置查询的实体类名及别名
        pb.setEntityName("DtoTest x");
        // 设置查询返回的字段、实体别名表示所有字段
        pb.setSelect("select x");
        comRepository.findByPage(pb, testCriteria);
        List<DtoTest> datas = pb.getData();
        Iterator<DtoTest> testIte = datas.iterator();
        List<DtoAnalyseDataPhone> analyseDataPhoneList = new ArrayList<>();
        while (testIte.hasNext()) {
            Object obj = testIte.next();
            DtoTest test = (DtoTest) obj;
            DtoAnalyseDataPhone analyseDataPhone = new DtoAnalyseDataPhone();
            analyseDataPhone.setTestId(test.getId());
            analyseDataPhone.setAnalyseMethod(test.getRedAnalyzeMethodName());
            analyseDataPhone.setIsCompleteField(test.getIsCompleteField());
            analyseDataPhone.setIsOutsourcing(test.getIsOutsourcing());
            analyseDataPhone.setItemStandard(test.getRedCountryStandard());
            analyseDataPhone.setAnalyseItemName(test.getRedAnalyzeItemName());
            analyseDataPhoneList.add(analyseDataPhone);
        }
        return analyseDataPhoneList;
    }

    /**
     * 提交送样单
     *
     * @param recIds 送样单ids
     */
    @Transactional
    @Override
    public void submitReceive(List<String> recIds) {
        List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findAll(recIds);
        //移动端可能会传项目的id
        if (receiveSampleRecordList.size() == 0) {
            receiveSampleRecordList = receiveSampleRecordRepository.findByProjectIdIn(recIds);
        }
        for (DtoReceiveSampleRecord receiveSampleRecord : receiveSampleRecordList) {
            receiveSampleRecord.setUploadStatus(EnumPRO.EnumReceiveUploadStatus.已数据同步.getValue());
            receiveSampleRecord.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            receiveSampleRecord.setModifyDate(new Date());
        }
        receiveSampleRecordRepository.save(receiveSampleRecordList);
        // 送样单提交后，如有实验室指标则同时流转到样品交接
        List<String> receiveIds = receiveSampleRecordList.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
        List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(receiveIds);
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        this.flowToReceiveSample(sampleIdList);
    }


    /**
     * 送样单详情
     *
     * @param recId 送样单id
     * @return 送样单详情
     */
    @Override
    public DtoReceiveSampleRecordPhone getRecordById(String recId) {
        DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordService.findOne(recId);
        List<DtoReceiveSubSampleRecord> subSampleRecordList = receiveSubSampleRecordRepository.findByReceiveId(recId);
        DtoReceiveSampleRecordPhone dtoReceiveSampleRecordPhone = new DtoReceiveSampleRecordPhone();
        dtoReceiveSampleRecordPhone.setReceiveSampleRecordCode(receiveSampleRecord.getRecordCode());
        dtoReceiveSampleRecordPhone.setRecordRemark(receiveSampleRecord.getRemark());
        dtoReceiveSampleRecordPhone.setSamplingDate(receiveSampleRecord.getSamplingTime());
        DtoRecordJson receiveSampleRecordJson = JsonIterator.deserialize(receiveSampleRecord.getJson(), DtoRecordJson.class);
        dtoReceiveSampleRecordPhone.setTypeNames(receiveSampleRecordJson.getLabSampleTypes());
        if (!StringUtil.isNotEmpty(receiveSampleRecordJson.getLabSampleTypes())) {
            List<String> sampleTypeIds = sampleRepository.findByReceiveId(recId).stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleTypeIds)) {
                List<String> sampleTypeName = sampleTypeRepository.findAll(sampleTypeIds).stream().map(DtoSampleType::getTypeName).collect(Collectors.toList());
                dtoReceiveSampleRecordPhone.setTypeNames(String.join(",", sampleTypeName));
            }
        }
        dtoReceiveSampleRecordPhone.setLeaderId(receiveSampleRecord.getSenderId());
        if (StringUtil.isNotEmpty(receiveSampleRecord.getSenderName())) {
            dtoReceiveSampleRecordPhone.setLeaderName(receiveSampleRecord.getSenderName());
        } else {
            if (StringUtil.isNotEmpty(receiveSampleRecord.getSenderId())) {
                DtoPerson person = personRepository.findOne(receiveSampleRecord.getSenderId());
                if (StringUtil.isNotNull(person)) {
                    dtoReceiveSampleRecordPhone.setLeaderName(person.getCName());
                }
            }
        }

        dtoReceiveSampleRecordPhone.setSamplingPersonIds(receiveSampleRecord.getSamplingPersonIds());
        dtoReceiveSampleRecordPhone.setSamplingPersonNames(String.join("、", receiveSampleRecord.getSamplingPersonNames()));
        dtoReceiveSampleRecordPhone.setId(receiveSampleRecord.getId());
        String xcSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("XC")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        String fxSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("FX")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        dtoReceiveSampleRecordPhone.setSubFXId(fxSubId);
        dtoReceiveSampleRecordPhone.setSubXCId(xcSubId);
        return dtoReceiveSampleRecordPhone;
    }

    /**
     * 保存送样单信息
     *
     * @param receiveSampleRecordPhone 送样单信息
     */
    @Transactional
    @Override
    public void saveRecordInfo(DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord(); //receiveSampleRecordService.findOne(receiveSampleRecordPhone.getId());
        receiveSampleRecord.setId(receiveSampleRecordPhone.getId());
        receiveSampleRecord.setSamplingTime(receiveSampleRecordPhone.getSamplingDate());
        receiveSampleRecord.setSenderId(receiveSampleRecordPhone.getLeaderId());
        receiveSampleRecord.setSenderName(receiveSampleRecordPhone.getLeaderName());
        receiveSampleRecord.setSamplingPersonIds(receiveSampleRecordPhone.getSamplingPersonIds());
        receiveSampleRecord.setRecordCode(receiveSampleRecordPhone.getReceiveSampleRecordCode());
        receiveSampleRecord.setSamplingPersonNames(StringUtil.isNotEmpty(receiveSampleRecordPhone.getSamplingPersonNames()) ? Arrays.asList(receiveSampleRecordPhone.getSamplingPersonNames().split(",")) : new ArrayList<>());
        receiveSampleRecordService.updateRecord(receiveSampleRecord);
    }

    /**
     * 通过送样单id获取点位信息
     *
     * @param recId 送样单id
     * @return 点位信息
     */
    @Override
    public List<DtoSampleFolderPhone> getFolderInfoByReceiveId(String recId) {
        //样品
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(recId);
        List<String> folderIds = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getSampleFolderId())).map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        //点位
        List<DtoSampleFolder> sampleFolderList = new ArrayList<>();
        if (StringUtil.isNotEmpty(folderIds)) {
            sampleFolderList = sampleFolderRepository.findAll(folderIds);
        }
        //样品类型
        List<String> typeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = new ArrayList<>();
        if (StringUtil.isNotEmpty(typeIds)) {
            sampleTypeList = sampleTypeRepository.findAll(typeIds);
        }
        //数据
        List<String> samIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        if (StringUtil.isNotEmpty(samIds)) {
            analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(samIds);
        }
        List<String> sampleTypeIdList = sampleFolderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypeIdList);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));
        //送样单
        DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordRepository.findOne(recId);
        List<DtoReceiveSampleRecord> recordList = new ArrayList<>();
        recordList.add(receiveSampleRecord);
        return getFolderInfo(sampleFolderList, sampleTypeList, sampleList, analyseDataList, recordList, samTypeMap);
    }

    /**
     * 剔除送样单样品
     *
     * @param recId      送样单id
     * @param folderInfo 点位信息
     */
    @Override
    public void removeSample(String recId, List<String> folderInfo) {
        List<DtoSample> sampleList = getSampleList(UUIDHelper.GUID_EMPTY, recId, folderInfo);
        receiveSampleRecordService.removeSample(recId, sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList()));
    }

    /**
     * 剔除送样单样品
     *
     * @param recId      送样单id
     * @param folderInfo 点位信息
     */
    @Override
    public void removeOutSample(String recId, List<String> folderInfo) {
        List<DtoSample> sampleList = getSampleList(UUIDHelper.GUID_EMPTY, recId, folderInfo);
        schemeService.deleteOutsideSample(sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList()), recId);
    }

    /**
     * 获取点位、公共参数
     *
     * @param sampleFolderId 点位id
     * @param sampleTypeId   样品类型id
     * @param cycleOrder     周期
     * @return 获取点位、公共参数
     */
    @Override
    public List<Map<String, Object>> getFolderParams(String sampleFolderId, String projectId, String receiveId, String sampleTypeId, Integer cycleOrder) {
        //找到对应的样品
        List<DtoSample> sampleList = getSampleList(sampleFolderId, projectId, receiveId, cycleOrder);

        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
        List<DtoParamsDataApiPhone> paramsDataPhoneList = new ArrayList<>();
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (sampleIds.size() == 0) {
            return mapList;
        }
        sampleList = sampleService.sortPrepareSample(sampleList, false);

        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        Set<String> analyseItemIds = analyseDataList.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toSet());
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        //paramsConfigs = analyseItemIds.size() > 0 ? paramsConfigService.findBySampleTypeId(sampleTypeId, analyseItemIds) :  paramsConfigService.findBySampleTypeId(sampleTypeId);
        paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
        //只获取点位参数和公共参数
        paramsConfigs = paramsConfigs.stream().filter(p -> EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()) ||
                EnumLIM.EnumParamsType.点位参数.getValue().equals(p.getParamsType())).collect(Collectors.toList());

        //按照排序值进行排序
        paramsConfigs = paramsConfigs.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());

        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(sampleIds, EnumPRO.EnumParamsDataType.样品.getValue());
        Map<String, String> paramsDataMap = paramsDataList.stream().
                collect(Collectors.groupingBy(DtoParamsData::getParamsConfigId, Collectors.collectingAndThen(Collectors.toList(),
                        value -> ArrayUtil.topFrequent(value.stream().map(pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "").collect(Collectors.toList()).toArray(new String[value.size()])))));

        Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));
        List<DtoParamsData> paramsDatas = new ArrayList<>();
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            paramsConfig.setParamsValue(paramsDataMap.getOrDefault(paramsConfig.getId(), paramsConfig.getDefaultValue()));
            for (DtoSample sample : sampleList) {
                String key = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                String paramsValue = StringUtils.isNotNullAndEmpty(paramsConfig.getParamsValue()) ? paramsConfig.getParamsValue() : "";
                if (!allParamsDataMap.containsKey(key)) {
                    //配置中有，数据库中没有的进行预先插入
                    DtoParamsData paramsData = new DtoParamsData();
                    paramsData.setObjectId(sample.getId());
                    paramsData.setObjectType(EnumPRO.EnumParamsDataType.样品.getValue());
                    paramsData.setParamsConfigId(paramsConfig.getId());
                    paramsData.setParamsName(paramsConfig.getAlias());
                    paramsData.setParamsValue(paramsValue);
                    paramsData.setDimension(paramsConfig.getDimension());
                    paramsData.setDimensionId(paramsConfig.getDimensionId());
                    paramsData.setOrderNum(paramsConfig.getOrderNum());
                    paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                    paramsDatas.add(paramsData);
                } else if (!allParamsDataMap.get(key).equals(paramsValue)) {
                    Optional<DtoParamsData> pd = paramsDataList.stream().filter(p -> p.getObjectId().equals(sample.getId()) && p.getParamsConfigId().equals(paramsConfig.getId())).findFirst();
                    if (pd.isPresent()) {//TODO 测试这种情况下的save是否为更新
                        DtoParamsData paramsData = pd.get();
                        paramsData.setParamsValue(paramsValue);
                        paramsDatas.add(paramsData);
                    }
                }
            }
        }

        if (paramsDatas.size() > 0) {
            paramsDataService.saveAsync(paramsDatas);
        }

        // 根据检测类型id查询采样方法
        List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodService.bySampleType(sampleTypeId);
        List<DtoDataSourcePhone> dataSourcePhoneList = analyzeMethodList.stream().map(p -> {
            DtoDataSourcePhone dataSourcePhone = new DtoDataSourcePhone();
            dataSourcePhone.setKey(p.getMethodName() + (StringUtil.isNotEmpty(p.getCountryStandard()) ? p.getCountryStandard() : ""));
            dataSourcePhone.setValue(p.getMethodName() + (StringUtil.isNotEmpty(p.getCountryStandard()) ? p.getCountryStandard() : ""));
            return dataSourcePhone;
        }).collect(Collectors.toList());

        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            DtoParamsDataApiPhone paramsDataPhone = new DtoParamsDataApiPhone();
            paramsDataPhone.setParamsValue(paramsConfig.getParamsValue());
            paramsDataPhone.setParamsName(paramsConfig.getAlias());
            paramsDataPhone.setParamsConfigId(paramsConfig.getId());
            paramsDataPhone.setSampleId(UUIDHelper.GUID_EMPTY);
            paramsDataPhone.setDefaultControl(paramsConfig.getDefaultControl());
            paramsDataPhone.setDimension(paramsConfig.getDimension());
            paramsDataPhone.setGroupId(UUIDHelper.GUID_EMPTY);
            paramsDataPhone.setParamsType(paramsConfig.getParamsType());
            paramsDataPhone.setOrderNum(paramsConfig.getOrderNum());
            paramsDataPhone.setReferenceText(paramsConfig.getReferenceText());
            if (StringUtil.isNotEmpty(paramsConfig.getDataSource())) {
                TypeLiteral<List<DtoDataSourcePhone>> typeLiteral = new TypeLiteral<List<DtoDataSourcePhone>>() {
                };
                List<DtoDataSourcePhone> dataSourcePhone = JsonIterator.deserialize(paramsConfig.getDataSource(), typeLiteral);
                if (paramsConfig.getParamsName().equals("采样方法及依据")) {
                    dataSourcePhone = dataSourcePhoneList;
                }
                paramsDataPhone.setDataSource(dataSourcePhone);
            }
            paramsDataPhoneList.add(paramsDataPhone);
        }
        paramsDataPhoneList.sort(Comparator.comparing(DtoParamsDataApiPhone::getParamsType).reversed().thenComparing(DtoParamsDataApiPhone::getOrderNum).reversed());
        //格式化接口响应对象
        mapList = formatParamsDataPhoneList(paramsDataPhoneList);
        return mapList;
    }

    /**
     * 格式化接口响应对象
     *
     * @param paramsDataPhoneList 参数对象列表
     * @return 格式化后的响应对象
     */
    private List<Map<String, Object>> formatParamsDataPhoneList(List<DtoParamsDataApiPhone> paramsDataPhoneList) {
//        EnumParamsType
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<DtoParamsDataApiPhone> publicDataList = paramsDataPhoneList.stream().filter(p -> EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()))
                .collect(Collectors.toList());
        publicDataList.sort(Comparator.comparing(DtoParamsDataApiPhone::getOrderNum).reversed());
        Map<String, Object> publicMap = new HashMap<>();
        publicMap.put("paramsType", EnumLIM.EnumParamsType.公共参数.getValue());
        publicMap.put("paramsDataPhoneList", publicDataList);
        mapList.add(publicMap);
        List<DtoParamsDataApiPhone> folderDataList = paramsDataPhoneList.stream().filter(p -> EnumLIM.EnumParamsType.点位参数.getValue().equals(p.getParamsType()))
                .collect(Collectors.toList());
        folderDataList.sort(Comparator.comparing(DtoParamsDataApiPhone::getOrderNum).reversed());
        Map<String, Object> folderMap = new HashMap<>();
        folderMap.put("paramsType", EnumLIM.EnumParamsType.点位参数.getValue());
        folderMap.put("paramsDataPhoneList", folderDataList);
        mapList.add(folderMap);
        return mapList;
    }

    /**
     * 样品参数信息
     *
     * @param sampleId     样品id
     * @param sampleTypeId 样品类型id
     * @return 样品参数信息
     */
    @Override
    public DtoSampleParamsApiPhone getSampleParams(String sampleId, String sampleTypeId, Integer isEnterValue) {

        DtoSampleParamsApiPhone sampleInfo = new DtoSampleParamsApiPhone();

        DtoSample sample = sampleRepository.findOne(sampleId);

        if (!StringUtil.isNotNull(sample)) {
            return sampleInfo;
        }

        //样品大类
        DtoSampleType samType = sampleTypeRepository.findOne(sample.getSampleTypeId());
        //样品大类下的现场分组
        List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
        if (samType != null && StringUtil.isNotEmpty(samType.getFieldTaskGroupId()) && !samType.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)) {
            dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(Arrays.asList(samType.getFieldTaskGroupId()), EnumLIM.EnumGroupType.分组.getValue());
            if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                group2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
            }
        }
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId).stream().sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).collect(Collectors.toList());

        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
        //获取测试项目公式信息
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        List<String> testIdList = testList.stream().filter(p -> StringUtil.isNotNull(p.getIsUseFormula()) && p.getIsUseFormula()).map(DtoTest::getId).collect(Collectors.toList());
        List<DtoParamsFormula> formulaList = StringUtil.isNotEmpty(testIdList) ? paramsFormulaService.findByObjectIds(testIdList) : new ArrayList<>();

        if (paramsConfigs.size() > 0) {
            //除开点位参数和公共参数
            paramsConfigs = paramsConfigs.stream().filter(p -> !EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()) &&
                    !EnumLIM.EnumParamsType.点位参数.getValue().equals(p.getParamsType())).collect(Collectors.toList());

            //按照排序值进行排序
            paramsConfigs = paramsConfigs.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum)).collect(Collectors.toList());
        }

        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(Collections.singletonList(sampleId), EnumPRO.EnumParamsDataType.样品.getValue());

        Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));

        List<DtoParamsData> paramsDatas = new ArrayList<>();
        sampleInfo.setSampleCode(sample.getCode());
        sampleInfo.setSamId(sample.getId());
        List<DtoParamsDataApiPhone> paramsDataPhoneList = new ArrayList<>();
        List<DtoSampleDataPhone> analyseDataPhoneList = new ArrayList<>();
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) ||
                    paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                String groupId = UUIDHelper.GUID_EMPTY;
                String groupName = "";
                List<String> paramsKey = new ArrayList<>();
                //找到对应的分组Id
                if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                    for (DtoAnalyseData ana : analyseDataList) {
                        groupId = group2TestList.stream().filter(p -> ana.getTestId().equals(p.getTestId())).map(DtoSampleTypeGroup2Test::getSampleTypeGroupId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
                        if (!UUIDHelper.GUID_EMPTY.equals(groupId)) {
                            if (!paramsKey.contains(groupId)) {
                                String finalGroupId = groupId;
                                Optional<DtoSampleTypeGroup> group = dtoSampleGroups.stream().filter(p -> finalGroupId.equals(p.getId())).findFirst();
                                if (group.isPresent()) {
                                    groupName = group.get().getGroupName();
                                    //如果参数值为体积类型，需要从分组上获取默认值
                                    if (("体积类型".equals(paramsConfig.getParamsName()) || "体积类型".equals(paramsConfig.getAlias()))) {
                                        paramsConfig.setDefaultValue(group.get().getVolumeType());
                                    }
                                }
                                addParamsData(sample, paramsConfig, groupId, groupName, sampleId, allParamsDataMap, paramsDatas, paramsDataPhoneList);
                                paramsKey.add(groupId);
                            }
                        } else {
                            groupName = "";
                            if (!paramsKey.contains(groupId)) {
                                addParamsData(sample, paramsConfig, groupId, groupName, sampleId, allParamsDataMap, paramsDatas, paramsDataPhoneList);
                                paramsKey.add(groupId);
                            }
                        }
                    }
                } else {
                    addParamsData(sample, paramsConfig, groupId, groupName, sampleId, allParamsDataMap, paramsDatas, paramsDataPhoneList);
                }
            }
        }
        boolean isAddData = true;
        //获取分析数据对应的公式参数信息
        List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        List<DtoAnalyseOriginalRecord> originalRecordList = StringUtil.isNotEmpty(analyseDataIdList) ? analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIdList) : new ArrayList<>();
        Map<String, List<DtoAnalyseOriginalRecord>> originalRecordMap = originalRecordList.stream().collect(Collectors.groupingBy(DtoAnalyseOriginalRecord::getAnalyseDataId));
        TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
        };
        for (DtoAnalyseData analyseData : analyseDataList) {
            //只获取现场的测试项目（录入数据的时候，如果是数据查询，则获取所有的数据）
            if (isEnterValue.equals(1)) {
                if (!(analyseData.getIsCompleteField() && !analyseData.getIsOutsourcing())) {
                    isAddData = false;
                } else {
                    isAddData = true;
                }
            }
            if (isAddData) {
                DtoSampleDataPhone analyseDataPhone = new DtoSampleDataPhone();
                analyseDataPhone.setSampleId(sampleId);
                analyseDataPhone.setAnaId(analyseData.getId());
                analyseDataPhone.setItemName(analyseData.getRedAnalyzeItemName());
                analyseDataPhone.setTestId(analyseData.getTestId());
                analyseDataPhone.setAnaValue(analyseData.getTestValue());
                analyseDataPhone.setDimension(analyseData.getDimension());
                analyseDataPhone.setMostDecimal(analyseData.getMostDecimal());
                analyseDataPhone.setMostSignificance(analyseData.getMostSignificance());
                analyseDataPhone.setExamLimitValue(analyseData.getExamLimitValue());
                analyseDataPhone.setUncertainType(EnumBase.EnumUncertainType.浓度.getValue());
                //获取公式信息
                DtoParamsFormula formula = getFormula(formulaList, samType, analyseData.getTestId());
                //设置公式参数信息
                List<DtoAnalyseOriginalRecord> existRecordList = originalRecordMap.getOrDefault(analyseData.getId(), new ArrayList<>());
                if (StringUtil.isEmpty(existRecordList)) {
                    DtoAnalyseOriginalRecord newRecord = initOriginalRecord(analyseData, formula);
                    if (StringUtil.isNotNull(newRecord)) {
                        existRecordList.add(newRecord);
                    }
                }
                analyseDataPhone.setAnalyseOriginalJsonList(getOriginalJsonInfo(existRecordList, typeLiteral));
                analyseDataPhone.setFormula(StringUtil.isNotNull(formula) ? formula.getFormula() : "");
                analyseDataPhone.setFormulaId(StringUtil.isNotNull(formula) ? formula.getId() : UUIDHelper.GUID_EMPTY);
                analyseDataPhoneList.add(analyseDataPhone);
            }
        }
        //保证分组设置排序值的时候遍历的顺序和现场任务那边一致
        paramsDataPhoneList.sort(Comparator.comparing(DtoParamsDataApiPhone::getOrderNum, Comparator.reverseOrder()).thenComparing(DtoParamsDataApiPhone::getParamsName));
        Map<String, Integer> groupMap = new HashMap<>();
        paramsDataPhoneList.forEach(r -> {
            if (!StringUtil.isNotEmpty(r.getGroupId())) {
                r.setGroupId(UUIDHelper.GUID_EMPTY);
            } else {
                if (!UUIDHelper.GUID_EMPTY.equals(r.getGroupId())) {
                    int count = groupMap.size() + 1;
                    if (!groupMap.containsKey(r.getGroupId())) {
                        groupMap.put(r.getGroupId(), count);
                        r.setOrderNum((r.getOrderNum() + 1000 * count));
                    } else {
                        r.setOrderNum((r.getOrderNum() + 1000 * groupMap.get(r.getGroupId())));
                    }
                }
            }
        });
        paramsDataPhoneList.sort(Comparator.comparing(DtoParamsDataApiPhone::getOrderNum).reversed());
        if (isEnterValue.equals(1)) {
            sampleInfo.setParamsDataPhoneList(paramsDataPhoneList);
        } else {
            //清空参数
            paramsDataPhoneList.clear();
            sampleInfo.setParamsDataPhoneList(paramsDataPhoneList);
        }
        sampleInfo.setAnalyseDataPhoneList(analyseDataPhoneList);
        if (paramsDatas.size() > 0) {
            paramsDataService.saveAsync(paramsDatas);
        }
        return sampleInfo;
    }

    /**
     * 样品参数信息
     *
     * @param sampleId     样品id
     * @param sampleTypeId 样品类型id
     * @return 样品参数信息
     */
    @Override
    public List<DtoSampleParamsApiPhone> getSampleParamsList(String sampleId, String sampleTypeId, Integer isEnterValue) {
        List<DtoSampleParamsApiPhone> sampleInfoList = new ArrayList<>();
        DtoSample sample = sampleRepository.findOne(sampleId);
        if (!StringUtil.isNotNull(sample)) {
            return sampleInfoList;
        }
        List<DtoSample> allSampleList = new ArrayList<>();
        allSampleList.add(sample);
        // 判断当前样品是否为标样
        boolean byFlag = false;
        if (StringUtil.isNotEmpty(sample.getQcId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getQcId())) {
            DtoQualityControl qualityControl = qualityControlRepository.findOne(sample.getQcId());
            if (EnumLIM.EnumQCType.标准.getValue().equals(qualityControl.getQcType())) {
                byFlag = true;
            }
        }
        List<String> assSampleIdList = new ArrayList<>();
        if (!byFlag) {
            List<DtoSample> assSampleList = sampleRepository.findByAssociateSampleId(sample.getId());
            // 排除当前原样关联的外部质控样
            List<String> qcIds = assSampleList.stream().map(DtoSample::getQcId).collect(Collectors.toList());
            List<String> nbQcIdList = qualityControlRepository.findAll(qcIds).stream().filter(p -> EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade()))
                    .map(DtoQualityControl::getId).collect(Collectors.toList());
            assSampleList = assSampleList.stream().filter(p -> nbQcIdList.contains(p.getQcId())).collect(Collectors.toList());
            assSampleIdList = assSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            allSampleList.addAll(assSampleList);
        }
        List<String> allQcIdList = allSampleList.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        List<String> allSampleIdList = new ArrayList<>(assSampleIdList);
        allSampleIdList.add(sampleId);
        Map<String, DtoQualityControl> qcMap = StringUtil.isNotEmpty(allQcIdList) ? qualityControlRepository.findByIds(allQcIdList)
                .stream().collect(Collectors.toMap(DtoQualityControl::getId, dto -> dto)) : new HashMap<>();
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(allSampleIdList).stream().sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).collect(Collectors.toList());
        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        List<DtoQualityControlEvaluate> evaluateList = StringUtil.isNotEmpty(analyseDataIdList) ? qualityControlEvaluateRepository.findByObjectIdIn(analyseDataIdList) : new ArrayList<>();
        Map<String, List<DtoQualityControlEvaluate>> evaluateMap = evaluateList.stream().collect(Collectors.groupingBy(DtoQualityControlEvaluate::getObjectId));
        //样品大类
        DtoSampleType samType = sampleTypeRepository.findOne(sample.getSampleTypeId());
        //样品大类下的现场分组
        List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
        if (samType != null && StringUtil.isNotEmpty(samType.getFieldTaskGroupId()) && !samType.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)) {
            dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(Arrays.asList(samType.getFieldTaskGroupId()), EnumLIM.EnumGroupType.分组.getValue());
            if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                group2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
            }
        }
        List<DtoParamsData> paramsDatas = new ArrayList<>();
        for (DtoSample loopSample : allSampleList) {

            List<DtoAnalyseData> loopAnaDataList = analyseDataMap.getOrDefault(loopSample.getId(), new ArrayList<>());
            //sampleInfo对象填充
            // 因移动端排序问题需要拆分因子
            for (DtoAnalyseData analyseData : loopAnaDataList) {
                DtoSampleParamsApiPhone sampleInfo = new DtoSampleParamsApiPhone();
                initSampleInfo(sampleTypeId, isEnterValue, sampleInfo, loopSample, Collections.singletonList(analyseData), samType, dtoSampleGroups, group2TestList, paramsDatas, qcMap, evaluateMap);
                sampleInfoList.add(sampleInfo);
            }
        }
        if (paramsDatas.size() > 0) {
            paramsDataService.saveAsync(paramsDatas);
        }
        return sampleInfoList;
    }

    /**
     * 初始化样品分析数据及参数信息对象
     *
     * @param sampleTypeId 检测类型id
     * @param sampleInfo   样品信息对象
     * @param sample       样品对象
     */
    private void initSampleInfo(String sampleTypeId, Integer isEnterValue, DtoSampleParamsApiPhone sampleInfo, DtoSample sample, List<DtoAnalyseData> analyseDataList,
                                DtoSampleType samType, List<DtoSampleTypeGroup> dtoSampleGroups, List<DtoSampleTypeGroup2Test> group2TestList,
                                List<DtoParamsData> paramsDatas, Map<String, DtoQualityControl> qcMap, Map<String, List<DtoQualityControlEvaluate>> evaluateMap) {
        String sampleId = sample.getId();
        String qcCode = qcMap.containsKey(sample.getQcId()) ? qcMap.get(sample.getQcId()).getQcCode() : "",
                qcValue = qcMap.containsKey(sample.getQcId()) ? qcMap.get(sample.getQcId()).getQcValue() : "";
        int uncertainType = qcMap.containsKey(sample.getQcId()) ? qcMap.get(sample.getQcId()).getUncertainType() : EnumBase.EnumUncertainType.浓度.getValue();
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
        //获取测试项目公式信息
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        List<String> testIdList = testList.stream().filter(p -> StringUtil.isNotNull(p.getIsUseFormula()) && p.getIsUseFormula()).map(DtoTest::getId).collect(Collectors.toList());
        List<DtoParamsFormula> formulaList = StringUtil.isNotEmpty(testIdList) ? paramsFormulaService.findByObjectIds(testIdList) : new ArrayList<>();

        if (paramsConfigs.size() > 0) {
            //除开点位参数和公共参数
            paramsConfigs = paramsConfigs.stream().filter(p -> !EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()) &&
                    !EnumLIM.EnumParamsType.点位参数.getValue().equals(p.getParamsType())).collect(Collectors.toList());

            //按照排序值进行排序
            paramsConfigs = paramsConfigs.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum)).collect(Collectors.toList());
        }

        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(Collections.singletonList(sampleId), EnumPRO.EnumParamsDataType.样品.getValue());

        Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));


        sampleInfo.setSampleCode(sample.getCode());
        sampleInfo.setSamId(sample.getId());
        int qcType = qcMap.containsKey(sample.getQcId()) ? qcMap.get(sample.getQcId()).getQcType() : -1;
        sampleInfo.setQcType(qcType);
        List<DtoParamsDataApiPhone> paramsDataPhoneList = new ArrayList<>();
        List<DtoSampleDataPhone> analyseDataPhoneList = new ArrayList<>();
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) ||
                    paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                String groupId = UUIDHelper.GUID_EMPTY;
                String groupName = "";
                List<String> paramsKey = new ArrayList<>();
                //找到对应的分组Id
                if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                    for (DtoAnalyseData ana : analyseDataList) {
                        groupId = group2TestList.stream().filter(p -> ana.getTestId().equals(p.getTestId())).map(DtoSampleTypeGroup2Test::getSampleTypeGroupId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
                        if (!UUIDHelper.GUID_EMPTY.equals(groupId)) {
                            if (!paramsKey.contains(groupId)) {
                                String finalGroupId = groupId;
                                Optional<DtoSampleTypeGroup> group = dtoSampleGroups.stream().filter(p -> finalGroupId.equals(p.getId())).findFirst();
                                if (group.isPresent()) {
                                    groupName = group.get().getGroupName();
                                    //如果参数值为体积类型，需要从分组上获取默认值
                                    if (("体积类型".equals(paramsConfig.getParamsName()) || "体积类型".equals(paramsConfig.getAlias()))) {
                                        paramsConfig.setDefaultValue(group.get().getVolumeType());
                                    }
                                }
                                addParamsData(sample, paramsConfig, groupId, groupName, sampleId, allParamsDataMap, paramsDatas, paramsDataPhoneList);
                                paramsKey.add(groupId);
                            }
                        } else {
                            if (!paramsKey.contains(groupId)) {
                                addParamsData(sample, paramsConfig, groupId, groupName, sampleId, allParamsDataMap, paramsDatas, paramsDataPhoneList);
                                paramsKey.add(groupId);
                            }
                        }
                    }
                } else {
                    addParamsData(sample, paramsConfig, groupId, groupName, sampleId, allParamsDataMap, paramsDatas, paramsDataPhoneList);
                }
            }
        }
        boolean isAddData = true;
        //获取分析数据对应的公式参数信息
        List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        List<DtoAnalyseOriginalRecord> originalRecordList = StringUtil.isNotEmpty(analyseDataIdList) ? analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIdList) : new ArrayList<>();
        Map<String, List<DtoAnalyseOriginalRecord>> originalRecordMap = originalRecordList.stream().collect(Collectors.groupingBy(DtoAnalyseOriginalRecord::getAnalyseDataId));
        TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
        };
        for (DtoAnalyseData analyseData : analyseDataList) {
            //只获取现场的测试项目（录入数据的时候，如果是数据查询，则获取所有的数据）
            if (isEnterValue.equals(1)) {
                if (!(analyseData.getIsCompleteField() && !analyseData.getIsOutsourcing())) {
                    isAddData = false;
                } else {
                    isAddData = true;
                }
            }
            if (isAddData) {
                DtoSampleDataPhone analyseDataPhone = new DtoSampleDataPhone();
                analyseDataPhone.setSampleId(sampleId);
                analyseDataPhone.setAnaId(analyseData.getId());
                analyseDataPhone.setItemName(analyseData.getRedAnalyzeItemName());
                analyseDataPhone.setTestId(analyseData.getTestId());
                analyseDataPhone.setAnaValue(analyseData.getTestValue());
                analyseDataPhone.setDimension(analyseData.getDimension());
                analyseDataPhone.setDimensionId(analyseData.getDimensionId());
                analyseDataPhone.setMostDecimal(analyseData.getMostDecimal());
                analyseDataPhone.setMostSignificance(analyseData.getMostSignificance());
                analyseDataPhone.setExamLimitValue(analyseData.getExamLimitValue());
                //获取公式信息
                DtoParamsFormula formula = getFormula(formulaList, samType, analyseData.getTestId());
                //设置公式参数信息
                List<DtoAnalyseOriginalRecord> existRecordList = originalRecordMap.getOrDefault(analyseData.getId(), new ArrayList<>());
                if (StringUtil.isEmpty(existRecordList)) {
                    DtoAnalyseOriginalRecord newRecord = initOriginalRecord(analyseData, formula);
                    if (StringUtil.isNotNull(newRecord)) {
                        existRecordList.add(newRecord);
                    }
                }
                analyseDataPhone.setAnalyseOriginalJsonList(getOriginalJsonInfo(existRecordList, typeLiteral));
                analyseDataPhone.setFormula(StringUtil.isNotNull(formula) ? formula.getFormula() : "");
                analyseDataPhone.setFormulaId(StringUtil.isNotNull(formula) ? formula.getId() : UUIDHelper.GUID_EMPTY);
                analyseDataPhone.setSampleTypeId(StringUtil.isNotNull(samType) ? samType.getId() : UUIDHelper.GUID_EMPTY);
                analyseDataPhone.setSampleTypeName(StringUtil.isNotNull(samType) ? samType.getTypeName() : "");
                analyseDataPhone.setQcCode(qcCode);
                analyseDataPhone.setQcValue(qcValue);
                analyseDataPhone.setUncertainType(uncertainType);
                analyseDataPhone.setQcInfo(analyseData.getQcInfo());
                DtoQualityControlEvaluate evaluate = evaluateMap.containsKey(analyseData.getId()) ? evaluateMap.get(analyseData.getId()).get(0) : null;
                analyseDataPhone.setIsPass(StringUtil.isNotNull(evaluate) ? evaluate.getIsPass() : null);
                analyseDataPhone.setJudgingMethod(StringUtil.isNotNull(evaluate) ? EnumBase.EnumJudgingMethod.getName(evaluate.getJudgingMethod()) : "");
                analyseDataPhone.setAllowLimit(StringUtil.isNotNull(evaluate) ? evaluate.getAllowLimit() : "");
                analyseDataPhoneList.add(analyseDataPhone);
            }
        }
        //保证分组设置排序值的时候遍历的顺序和现场任务那边一致
        paramsDataPhoneList.sort(Comparator.comparing(DtoParamsDataApiPhone::getOrderNum, Comparator.reverseOrder()).thenComparing(DtoParamsDataApiPhone::getParamsName));
        Map<String, Integer> groupMap = new HashMap<>();
        paramsDataPhoneList.forEach(r -> {
            if (!StringUtil.isNotEmpty(r.getGroupId())) {
                r.setGroupId(UUIDHelper.GUID_EMPTY);
            } else {
                if (!UUIDHelper.GUID_EMPTY.equals(r.getGroupId())) {
                    int count = groupMap.size() + 1;
                    if (!groupMap.containsKey(r.getGroupId())) {
                        groupMap.put(r.getGroupId(), count);
                        r.setOrderNum((r.getOrderNum() + 1000 * count));
                    } else {
                        r.setOrderNum((r.getOrderNum() + 1000 * groupMap.get(r.getGroupId())));
                    }
                }
            }
        });
        paramsDataPhoneList.sort(Comparator.comparing(DtoParamsDataApiPhone::getOrderNum).reversed());
        if (isEnterValue.equals(1)) {
            sampleInfo.setParamsDataPhoneList(paramsDataPhoneList);
        } else {
            //清空参数
            paramsDataPhoneList.clear();
            sampleInfo.setParamsDataPhoneList(paramsDataPhoneList);
        }
        sampleInfo.setAnalyseDataPhoneList(analyseDataPhoneList);
    }

    /**
     * 获取分析数据对应的公式参数信息
     *
     * @param analyseOriginalRecordList 原始记录对象列表
     * @param typeLiteral               json转换对象
     * @return 公式参数信息
     */
    private List<DtoAnalyseOriginalJson> getOriginalJsonInfo(List<DtoAnalyseOriginalRecord> analyseOriginalRecordList,
                                                             TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral) {
        List<DtoAnalyseOriginalJson> originalJsonList = new ArrayList<>();
        if (StringUtil.isNotEmpty(analyseOriginalRecordList)) {
            DtoAnalyseOriginalRecord record = analyseOriginalRecordList.get(0);
            if (StringUtil.isNotEmpty(record.getJson())) {
                originalJsonList = JsonIterator.deserialize(record.getJson(), typeLiteral);
            }
        }
        return originalJsonList;
    }

    /**
     * 初始化一个原始记录对象
     *
     * @param analyseData 分析数据对象
     * @param formula     测试项目公式对象
     * @return 原始记录对象
     */
    private DtoAnalyseOriginalRecord initOriginalRecord(DtoAnalyseData analyseData, DtoParamsFormula formula) {
        if (StringUtil.isNotNull(formula)) {
            DtoAnalyseOriginalRecord newRecord = new DtoAnalyseOriginalRecord();
            newRecord.setTestFormulaId(formula.getId());
            newRecord.setTestFormula(formula.getFormula());
            newRecord.setAnalyseDataId(analyseData.getId());
            List<DtoParamsTestFormula> paramsTestFormulaList = paramsTestFormulaService.findByObjectId(formula.getId());
            if (StringUtil.isNotEmpty(paramsTestFormulaList)) {
                newRecord.setJson(JsonStream.serialize(paramsTestFormulaList));
            }
            return newRecord;
        }
        return null;
    }

    /**
     * 获取公式对象
     *
     * @param formulaList 公式列表
     * @param sampleType  检测类型
     * @param testId      测试项目id
     * @return 公式对象
     */
    private DtoParamsFormula getFormula(List<DtoParamsFormula> formulaList, DtoSampleType sampleType, String testId) {
        DtoParamsFormula formula = null;
        DtoParamsFormula dtoParamsFormula = formulaList.stream().filter(p -> sampleType.getId().equals(p.getSampleTypeId()) && p.getObjectId().equals(testId))
                .max(Comparator.comparing(DtoParamsFormula::getConfigDate)).orElse(null);
        if (StringUtil.isNotNull(dtoParamsFormula)) { //小类是否有公式
            formula = dtoParamsFormula;
        } else { //大类是否有公式
            DtoParamsFormula bigParamsFormula = formulaList.stream()
                    .filter(p -> p.getSampleTypeId().equals(sampleType.getParentId()) && p.getObjectId().equals(testId))
                    .max(Comparator.comparing(DtoParamsFormula::getConfigDate)).orElse(null);
            if (StringUtil.isNotNull(bigParamsFormula)) {
                formula = bigParamsFormula;
            }
        }
        return formula;
    }

    private void addParamsData(DtoSample sample, DtoParamsConfig paramsConfig, String groupId, String groupName,
                               String sampleId, Map<String, String> allParamsDataMap, List<DtoParamsData> paramsDatas,
                               List<DtoParamsDataApiPhone> paramsDataPhoneList) {
        DtoParamsDataApiPhone paramsDataPhone = new DtoParamsDataApiPhone();
        String key = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), groupId);
        paramsDataPhone.setParamsConfigId(paramsConfig.getId());
        paramsDataPhone.setParamsName(paramsConfig.getAlias());
        if (StringUtil.isNotEmpty(groupName)) {
            paramsDataPhone.setParamsName(String.format("%s-%s", groupName, paramsDataPhone.getParamsName()));
        }
        paramsDataPhone.setSampleId(sampleId);
        paramsDataPhone.setDefaultControl(paramsConfig.getDefaultControl());
        paramsDataPhone.setDimension(paramsConfig.getDimension());
        paramsDataPhone.setParamsType(paramsConfig.getParamsType());
        paramsDataPhone.setOrderNum(paramsConfig.getOrderNum());
        paramsDataPhone.setGroupId(groupId);
        paramsDataPhone.setOrderNum(paramsConfig.getOrderNum());
        paramsDataPhone.setReferenceText(paramsConfig.getReferenceText());
        if (StringUtil.isNotEmpty(paramsConfig.getDataSource())) {
            TypeLiteral<List<DtoDataSourcePhone>> typeLiteral = new TypeLiteral<List<DtoDataSourcePhone>>() {
            };
            List<DtoDataSourcePhone> dataSourcePhone = JsonIterator.deserialize(paramsConfig.getDataSource(), typeLiteral);
            paramsDataPhone.setDataSource(dataSourcePhone);
        }
        String strValue = allParamsDataMap.getOrDefault(key, "");

        //数据为空，找默认值
        if (!StringUtil.isNotEmpty(strValue)) {
            //配置中有，数据库中没有的进行预先插入
            strValue = StringUtils.isNotNullAndEmpty(paramsConfig.getDefaultValue()) ? paramsConfig.getDefaultValue() : "";
        }

        //为什么是判断不是为空的？
        if (!allParamsDataMap.containsKey(key) && StringUtil.isNotEmpty(strValue)) {
            //配置中有，数据库中没有的进行预先插入
            String paramsValue = StringUtils.isNotNullAndEmpty(paramsConfig.getDefaultValue()) ? paramsConfig.getDefaultValue() : "";
            strValue = paramsValue;
            DtoParamsData paramsData = new DtoParamsData();
            paramsData.setObjectId(sample.getId());
            paramsData.setObjectType(EnumPRO.EnumParamsDataType.样品.getValue());
            paramsData.setParamsConfigId(paramsConfig.getId());
            paramsData.setParamsName(paramsConfig.getAlias());
            paramsData.setParamsValue(paramsValue);
            paramsData.setDimension(paramsConfig.getDimension());
            paramsData.setDimensionId(paramsConfig.getDimensionId());
            paramsData.setOrderNum(paramsConfig.getOrderNum());
            paramsData.setGroupId(groupId);
            paramsDatas.add(paramsData);
        }

        paramsDataPhone.setParamsValue(strValue);
        paramsDataPhoneList.add(paramsDataPhone);
    }


    /**
     * 保存点位参数
     *
     * @param sampleFolderId      点位id
     * @param projectId           项目id
     * @param receiveId           送样单id
     * @param cycleOrder          周期
     * @param paramsDataPhoneList 参数数据
     */
    @Transactional
    @Override
    public void saveFolderParamsValue(String sampleFolderId, String projectId, String receiveId, Integer cycleOrder, List<DtoParamsDataApiPhone> paramsDataPhoneList) {
        List<DtoSample> sampleList = getSampleList(sampleFolderId, projectId, receiveId, cycleOrder);

        //找到当前送样单下所有的样品
        String proId = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId())).map(DtoSample::getProjectId).findFirst().orElse(UUIDHelper.GUID_EMPTY);

        List<DtoSample> recSampleList = new ArrayList<>();
        if (StringUtil.isEmpty(receiveId) || UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            //送样单id为空，表示前端直接点了点位后修改点位相关参数，没有先选择相应采样单，因此需要根据点位id查询对应的送样单
            List<String> receiveIdList = sampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                    .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
            recSampleList = getSampleList(UUIDHelper.GUID_EMPTY, proId, receiveIdList, cycleOrder);
        } else {
            recSampleList = getSampleList(UUIDHelper.GUID_EMPTY, proId, receiveId, cycleOrder);
        }

        List<String> folderIds = recSampleList.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toList());
        List<DtoSampleFolder> folderList = sampleFolderRepository.findAll(folderIds);
        //没有参数不需要执行以下操作
        if (!StringUtil.isNotEmpty(paramsDataPhoneList)) {
            return;
        }
        List<String> configIds = paramsDataPhoneList.stream().map(DtoParamsDataApiPhone::getParamsConfigId).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigList = paramsConfigService.findAll(configIds);
        DtoSampleFolder sampleFolder = sampleFolderRepository.findOne(sampleFolderId);
        if (sampleList.size() > 0) {
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(EnumPRO.EnumParamsDataType.样品.getValue(), sampleIds, configIds);
            //样品及配置下的主键映射
            Map<String, DtoParamsData> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(),
                    p.getParamsConfigId(), p.getGroupId()), pd -> pd, (p1, p2) -> p1));
            List<DtoLog> logs = new ArrayList<>();
            //保存非公共参数
            List<DtoParamsConfig> notPulicList = paramsConfigList.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())).collect(Collectors.toList());
            saveParamsValue(sampleList, notPulicList, paramsDataPhoneList, logs, allParamsDataMap, Collections.singletonList(sampleFolder));
            //保存公共参数
            List<DtoParamsConfig> publicList = paramsConfigList.stream().filter(p -> p.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())).collect(Collectors.toList());
            if (recSampleList.size() > 0) {
                List<String> recSampleIds = recSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoParamsData> recParamsDataList = paramsDataRepository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(EnumPRO.EnumParamsDataType.样品.getValue(), recSampleIds, configIds);
                Map<String, DtoParamsData> recAllParamsDataMap = recParamsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(),
                        p.getParamsConfigId(), p.getGroupId()), pd -> pd, (p1, p2) -> p1));
                saveParamsValue(recSampleList, publicList, paramsDataPhoneList, logs, recAllParamsDataMap, folderList);
            }
            if (logs.size() > 0) {
                if (!UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                    DtoLog log = logs.get(0);
                    log.setObjectId(receiveId);
                    newLogService.createLog(log);
                }
            }
        }
    }

    /**
     * 保存样品参数数据
     *
     * @param sampleParamsPhone 样品数据
     */
    @Transactional
    @Override
    public void saveSampleParamsValue(DtoSampleParamsApiPhone sampleParamsPhone) {
        DtoSample sample = sampleRepository.findOne(sampleParamsPhone.getSamId());
        List<String> paramsConfigIdList = StringUtil.isNotEmpty(sampleParamsPhone.getParamsDataPhoneList())
                ? sampleParamsPhone.getParamsDataPhoneList().stream().map(DtoParamsDataApiPhone::getParamsConfigId).collect(Collectors.toList()) : new ArrayList<>();
        List<DtoParamsData> paramsDataList = StringUtil.isNotEmpty(paramsConfigIdList) ? paramsDataRepository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(EnumPRO.EnumParamsDataType.样品.getValue(),
                Collections.singletonList(sampleParamsPhone.getSamId()), paramsConfigIdList) : new ArrayList<>();
        //样品及配置下的主键映射
        Map<String, DtoParamsData> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(),
                p.getParamsConfigId(), p.getGroupId()), pd -> pd, (p1, p2) -> p1));
        List<DtoParamsConfig> paramsConfigs = StringUtil.isNotEmpty(paramsConfigIdList) ? paramsConfigService.findAll(paramsConfigIdList) : new ArrayList<>();
        //样品参数
        List<DtoLog> logs = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleParamsPhone.getParamsDataPhoneList())) {
            for (DtoParamsDataApiPhone paramsDataPhone : sampleParamsPhone.getParamsDataPhoneList()) {
                DtoParamsConfig paramsConfig = paramsConfigs.stream().filter(p -> paramsDataPhone.getParamsConfigId().equals(p.getId())).findFirst().orElse(null);
                DtoParamsData paramsData = new DtoParamsData();
                paramsData.setObjectId(sampleParamsPhone.getSamId());
                paramsData.setObjectType(EnumPRO.EnumParamsDataType.样品.getValue());
                paramsData.setParamsConfigId(paramsDataPhone.getParamsConfigId());
                paramsData.setParamsName(paramsDataPhone.getParamsName());
                paramsData.setParamsValue(paramsDataPhone.getParamsValue());
                paramsData.setDimension(StringUtil.isNotNull(paramsConfig) ? paramsConfig.getDimension() : "");
                paramsData.setDimensionId(StringUtil.isNotNull(paramsConfig) ? paramsConfig.getDimensionId() : UUIDHelper.GUID_EMPTY);
                paramsData.setOrderNum(StringUtil.isNotNull(paramsConfig) ? paramsConfig.getOrderNum() : 0);
                paramsData.setGroupId(paramsDataPhone.getGroupId());
                String key = String.format("%s;%s;%s", sampleParamsPhone.getSamId(), paramsDataPhone.getParamsConfigId(), paramsDataPhone.getGroupId());
                if (allParamsDataMap.containsKey(key)) {
                    if (StringUtil.isNotNull(paramsConfig)) {
                        logs.add(getParamsLog(false, paramsConfig, sample, sample.getRedFolderName(), allParamsDataMap.get(key).getParamsValue(), paramsConfig.getParamsValue()));
                    }
                    paramsData.setId(allParamsDataMap.get(key).getId());
                    comRepository.merge(paramsData);
                } else {
                    if (StringUtil.isNotNull(paramsConfig)) {
                        logs.add(this.getParamsLog(false, paramsConfig, sample, sample.getRedFolderName(), null, paramsConfig.getParamsValue()));
                    }
                    paramsDataRepository.save(paramsData);
                }
            }
        }
        if (logs.size() > 0) {
            if (!UUIDHelper.GUID_EMPTY.equals(sample.getReceiveId())) {
                DtoLog log = logs.get(0);
                log.setObjectId(sample.getReceiveId());
                newLogService.createLog(log);
            }
        }
        //现场数据保存
        List<String> anaIds = StringUtil.isNotEmpty(sampleParamsPhone.getAnalyseDataPhoneList()) ? sampleParamsPhone.getAnalyseDataPhoneList().stream().map(com.sinoyd.lims.pro.dto.customer.DtoSampleDataPhone::getAnaId).collect(Collectors.toList()) : new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        Map<String, List<DtoAnalyseOriginalRecord>> recordMap = new HashMap<>();
        if (StringUtil.isNotEmpty(anaIds)) {
            analyseDataList = analyseDataRepository.findAll(anaIds);
            List<DtoAnalyseOriginalRecord> recordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(anaIds);
            recordMap = recordList.stream().collect(Collectors.groupingBy(DtoAnalyseOriginalRecord::getAnalyseDataId));
        }
        List<DtoLog> anaLog = new ArrayList<>();
        List<DtoAnalyseOriginalRecord> originalRecordList = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            DtoSampleDataPhone sampleDataPhone = sampleParamsPhone.getAnalyseDataPhoneList().stream().filter(p -> analyseData.getId().equals(p.getAnaId())).findFirst().orElse(null);
            analyseData.setTestOrignValue(sampleDataPhone.getAnaValue());
            String strValue = proService.getDecimal(analyseData.getMostSignificance(), analyseData.getMostDecimal(), sampleDataPhone.getAnaValue());
            analyseData.setTestValueDstr(strValue);
            analyseData.setTestValue(strValue);
            analyseData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            analyseData.setModifyDate(new Date());
            //保存公式参数信息
            saveOriginalRecordInfo(analyseData, sampleDataPhone, recordMap, originalRecordList);
            anaLog.add(getDataLog(analyseData.getId(), sampleDataPhone.getAnaValue()));
        }

        for (DtoLog log : anaLog) {
            newLogService.createLog(log);
        }
        if (StringUtil.isNotEmpty(analyseDataList)) {
            analyseDataRepository.save(analyseDataList);
        }
        if (StringUtil.isNotEmpty(originalRecordList)) {
            analyseOriginalRecordRepository.save(originalRecordList);
        }
    }


    @Override
    @Transactional
    public void copySample(DtoSampleCopy sampleCopy) {
        // 目标样品
        List<String> targetSampleIds = sampleCopy.getTargetIds();
        List<DtoSample> sampleList = sampleService.findAll(targetSampleIds);
        if (StringUtil.isNotEmpty(sampleList)) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(targetSampleIds);
            List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(targetSampleIds, EnumPRO.EnumParamsDataType.样品.getValue());
            //样品及配置下的主键映射
            Map<String, DtoParamsData> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(), p.getGroupId()), pd -> pd, (p1, p2) -> p1));

            //样品大类
            DtoSampleType samType = sampleTypeRepository.findOne(sampleList.get(0).getSampleTypeId());
            //样品大类下的现场分组
            List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
            List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
            if (samType != null && StringUtil.isNotEmpty(samType.getFieldTaskGroupId()) && !samType.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)) {
                dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(Arrays.asList(samType.getFieldTaskGroupId()), EnumLIM.EnumGroupType.分组.getValue());
                if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                    group2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
                }
            }
            List<DtoDimension> dimensionList = dimensionRepository.findAll();
            Map<String, String> dimensionMap = dimensionList.stream().collect(Collectors.toMap(DtoDimension::getDimensionName, DtoDimension::getId, (p1, p2) -> p1));
            List<DtoParamsData> updateParamdDates = new ArrayList<>();
            List<DtoParamsData> addParamdDates = new ArrayList<>();
            // 复制内容
            List<DtoParamsDataApiPhone> paramsConfigList = sampleCopy.getParamsConfigList();
            for (DtoParamsDataApiPhone paramsDataApiPhone : paramsConfigList) {
                String groupId = paramsDataApiPhone.getGroupId();
                for (DtoSample sample : sampleList) {
                    if (!UUIDHelper.GUID_EMPTY.equals(groupId)) {
                        List<String> groupTestIds = group2TestList.stream().filter(p -> groupId.equals(p.getSampleTypeGroupId()))
                                .map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
                        if (analyseDataList.stream().noneMatch(p -> sample.getId().equals(p.getSampleId()) && groupTestIds.contains(p.getTestId()))) {
                            continue;
                        }
                    }
                    String key = String.format("%s;%s;%s", sample.getId(), paramsDataApiPhone.getParamsConfigId(), paramsDataApiPhone.getGroupId());
                    DtoParamsData paramsData = new DtoParamsData();
                    paramsData.setObjectId(sample.getId());
                    paramsData.setObjectType(EnumPRO.EnumParamsDataType.样品.getValue());
                    paramsData.setParamsConfigId(paramsDataApiPhone.getParamsConfigId());
                    paramsData.setParamsName(paramsDataApiPhone.getParamsName());
                    paramsData.setParamsValue(paramsDataApiPhone.getParamsValue());
                    paramsData.setDimension(paramsDataApiPhone.getDimension());
                    paramsData.setDimensionId(dimensionMap.getOrDefault(paramsDataApiPhone.getDimension(), UUIDHelper.GUID_EMPTY));
                    paramsData.setOrderNum(paramsDataApiPhone.getOrderNum());
                    paramsData.setGroupId(groupId);
                    if (allParamsDataMap.containsKey(key)) {
                        paramsData.setId(allParamsDataMap.get(key).getId());
                        updateParamdDates.add(paramsData);
                    } else {
                        addParamdDates.add(paramsData);
                    }
                }
            }
            if (updateParamdDates.size() > 0) {
                paramsDataService.update(updateParamdDates);
            }
            if (addParamdDates.size() > 0) {
                paramsDataService.save(addParamdDates);
            }
        }
    }

    /**
     * 保存分析数据对应的公式参数信息
     *
     * @param analyseData     分析数据对象
     * @param sampleDataPhone 前端传递的分析数据对象
     * @param recordMap       原始记录映射
     */
    private void saveOriginalRecordInfo(DtoAnalyseData analyseData, DtoSampleDataPhone sampleDataPhone, Map<String, List<DtoAnalyseOriginalRecord>> recordMap,
                                        List<DtoAnalyseOriginalRecord> originalRecordList) {
        List<DtoAnalyseOriginalJson> jsonList = sampleDataPhone.getAnalyseOriginalJsonList();
        if (StringUtil.isNotEmpty(jsonList)) {
            String json = JsonStream.serialize(jsonList);
            DtoAnalyseOriginalRecord oldRecord = recordMap.containsKey(analyseData.getId()) ? recordMap.get(analyseData.getId()).get(0) : null;
            if (StringUtil.isNotNull(oldRecord)) {
                //存在老的原始记录对象则进行修改
                oldRecord.setJson(json);
                originalRecordList.add(oldRecord);
            } else {
                //不存在则新增
                DtoAnalyseOriginalRecord newRecord = new DtoAnalyseOriginalRecord();
                newRecord.setJson(json);
                newRecord.setAnalyseDataId(analyseData.getId());
                newRecord.setTestFormula(sampleDataPhone.getFormula());
                newRecord.setTestFormulaId(sampleDataPhone.getFormulaId());
                originalRecordList.add(newRecord);
            }
        }
    }

    /**
     * 通过项目id及点位信息获取未加入采样单的样品
     *
     * @param projectId  项目id
     * @param folderInfo 点位信息
     * @return 未加入采样单的样品
     */
    private List<DtoSample> getSampleList(String projectId, String receiveId, List<String> folderInfo) {
        //找到原样
        List<DtoSample> sampleList = new ArrayList<>();
        if (!UUIDHelper.GUID_EMPTY.equals(projectId)) {
            sampleList = sampleRepository.findByProjectId(projectId).stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                    .collect(Collectors.toList());
        } else if (!UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            sampleList = sampleRepository.findByReceiveId(receiveId);
        }
        if (sampleList.size() == 0) {
            return sampleList;
        }
        List<String> samIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        //找到对应的质控样
        List<DtoSample> qcSampleList = sampleRepository.findByAssociateSampleIdIn(samIds).stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                .collect(Collectors.toList());
        List<DtoSample> recSamList = new ArrayList<>();
        for (String folder : folderInfo) {
            String folderId = folder.split(",")[0];
            String cycleOrder = folder.split(",")[1];
            List<DtoSample> samList = sampleList.stream().filter(p -> folderId.equals(p.getSampleFolderId()) && cycleOrder.equals(p.getCycleOrder().toString())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(samList)) {
                List<String> sIds = samList.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoSample> qcSamList = qcSampleList.stream().filter(p -> sIds.contains(p.getAssociateSampleId())).collect(Collectors.toList());
                recSamList.addAll(samList);
                if (StringUtil.isNotEmpty(qcSamList)) {
                    recSamList.addAll(qcSamList);
                }
            }
        }
        return recSamList;
    }

    /**
     * 获取点位信息
     *
     * @param dataList                点位集合
     * @param sampleTypeList          样品类型集合
     * @param sampleList              样品集合
     * @param analyseDataList         数据集合
     * @param receiveSampleRecordList 送样单集合
     * @return 点位列表
     */
    private List<DtoSampleFolderPhone> getFolderInfo(List<DtoSampleFolder> dataList, List<DtoSampleType> sampleTypeList, List<DtoSample> sampleList,
                                                     List<DtoAnalyseData> analyseDataList, List<DtoReceiveSampleRecord> receiveSampleRecordList,
                                                     Map<String, DtoSampleType> samTypeMap) {

        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoParamsData> paramsDataList = StringUtil.isNotEmpty(sampleIds) ? paramsDataRepository.findByObjectIdIn(sampleIds) : new ArrayList<>();
        List<String> paramsConfigIds = paramsDataList.stream().map(DtoParamsData::getParamsConfigId).distinct().collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigList = StringUtil.isNotEmpty(paramsConfigIds) ? paramsConfigService.findAll(paramsConfigIds) : new ArrayList<>();
        List<DtoSampleFolderPhone> sampleFolderPhoneList = new ArrayList<>();
        for (DtoSampleFolder folder : dataList) {
            DtoSampleType sampleType = sampleTypeList.stream().filter(p -> folder.getSampleTypeId().equals(p.getId()))
                    .findFirst().orElse(null);
            List<DtoSample> samList = sampleList.stream().filter(p -> folder.getId().equals(p.getSampleFolderId())).collect(Collectors.toList());
            Optional<Integer> cycValue = samList.stream().map(DtoSample::getCycleOrder).max(Comparator.comparing(Integer::intValue));
            if (cycValue.isPresent()) {
                //最大的周期
                for (Integer i = 1; i <= cycValue.get(); i++) {
                    DtoSampleFolderPhone sampleFolderPhone = new DtoSampleFolderPhone();
                    sampleFolderPhone.setId(folder.getId());
                    sampleFolderPhone.setSampleFolderName(folder.getWatchSpot());
                    sampleFolderPhone.setCycleOrder(String.format("第%d周期", i));
                    sampleFolderPhone.setCycValue(i);
                    sampleFolderPhone.setSampleTypeId(folder.getSampleTypeId());
                    sampleFolderPhone.setIsOver(1);
                    sampleFolderPhone.setWatchSpot(folder.getWatchSpot());
                    if (samTypeMap.containsKey(folder.getSampleTypeId())) {
                        DtoSampleType samType = samTypeMap.get(folder.getSampleTypeId());
                        sampleFolderPhone.setBigSampleTypeId(samType.getParentId());
                        sampleFolderPhone.setSampleTypeName(samType.getTypeName());
                        sampleFolderPhone.setSampleTypeOrderNum(samType.getOrderNum());
                    }
                    if (StringUtil.isNotNull(sampleType)) {
                        sampleFolderPhone.setSampleTypeName(sampleType.getTypeName());
                        sampleFolderPhone.setCheckType(sampleType.getCheckType() != null ? sampleType.getCheckType() : -1);
                    }
                    Integer finalI = i;
                    List<DtoSample> cycSamList = samList.stream().filter(p -> finalI.equals(p.getCycleOrder()) && !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory()))
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(cycSamList)) {
                        //当前周期的样品数
                        sampleFolderPhone.setSampleCount(cycSamList.size());
                        List<DtoSample> collentList = cycSamList.stream().filter(p -> !UUIDHelper.GUID_EMPTY
                                .equals(p.getReceiveId())).collect(Collectors.toList());
                        //当前周期在送样单的样品数
                        sampleFolderPhone.setCollectCount(collentList.size());
                        if (cycSamList.size() == collentList.size()) {
                            sampleFolderPhone.setIsOver(0);
                        }
                        List<String> cycSamIds = cycSamList.stream().map(DtoSample::getId).collect(Collectors.toList());
                        List<DtoAnalyseData> anaList = analyseDataList.stream().filter(p -> cycSamIds.contains(p.getSampleId())).collect(Collectors.toList());
                        List<String> testIds = anaList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
                        //当前周期的测试项目数
                        sampleFolderPhone.setAnalyzeCount(testIds.size());
                        List<String> anaItems = anaList.stream().map(DtoAnalyseData::getRedAnalyzeItemName).sorted().distinct().collect(Collectors.toList());
                        //当前周期的分析项目名称
                        sampleFolderPhone.setAnalyzeItems(String.join("、", anaItems));
                        List<String> recIds = cycSamList.stream().map(DtoSample::getReceiveId)
                                .filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId)).distinct().collect(Collectors.toList());
                        //送样单的信息
                        List<String> receiveStr = new ArrayList<>();
                        for (String rId : recIds) {
                            DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordList.stream().filter(p -> rId.equals(p.getId())).findFirst().orElse(null);
                            if (StringUtil.isNotNull(receiveSampleRecord)) {
                                long samCount = cycSamList.stream().filter(p -> rId.equals(p.getReceiveId())).count();
                                receiveStr.add(String.format("%s;%d", receiveSampleRecord.getRecordCode(), samCount));
                            }
                        }
                        // 设置是否录入
                        boolean isInput = true;
                        // 判定现场指标中的出证结果是否录入
                        if (anaList.stream().anyMatch(p-> p.getIsCompleteField() && StringUtil.isEmpty(p.getTestValue()))){
                            isInput = false;
                        }
                        // 出证结果已录入时，其次判定样品参数中必填项是否有值
                        if (isInput){
                            List<DtoParamsData> paramsDataOfSample = paramsDataList.stream().filter(p -> cycSamIds.contains(p.getObjectId())).collect(Collectors.toList());
                            List<String> paramsConfigIdsOfSample = paramsDataOfSample.stream().map(DtoParamsData::getParamsConfigId).distinct().collect(Collectors.toList());
                            List<String> requiredParamsConfigIds = paramsConfigList.stream().filter(p -> paramsConfigIdsOfSample.contains(p.getId()) && p.getIsRequired())
                                    .map(DtoParamsConfig::getId).collect(Collectors.toList());
                            // 判定样品中现场参数数据必填项是否有值
                            if (StringUtil.isNotEmpty(requiredParamsConfigIds)) {
                                if (paramsDataOfSample.stream().anyMatch(p -> requiredParamsConfigIds.contains(p.getParamsConfigId()) && StringUtil.isEmpty(p.getParamsValue()))) {
                                    isInput = false;
                                }
                            }
                        }
                        sampleFolderPhone.setIsInput(isInput);
                        sampleFolderPhone.setRecordCode(receiveStr);
                        sampleFolderPhoneList.add(sampleFolderPhone);
                    }
                }
            }
        }
//        sampleFolderPhoneList = sampleFolderPhoneList.stream().sorted(Comparator.comparing(DtoSampleFolderPhone::getIsOver)
//                .reversed().thenComparing(DtoSampleFolderPhone::getSampleTypeId).thenComparing(DtoSampleFolderPhone::getSampleFolderName)
//                .thenComparing(DtoSampleFolderPhone::getCycValue)).collect(Collectors.toList());

        Collator collator = Collator.getInstance();
        Comparator<DtoSampleFolderPhone> watchSpot = getComparator(sampleFolderPhoneList);
        sampleFolderPhoneList.sort(Comparator.comparing(DtoSampleFolderPhone::getIsOver)
                .reversed().thenComparing(DtoSampleFolderPhone::getSampleTypeOrderNum, Comparator.reverseOrder())
                .thenComparing(DtoSampleFolderPhone::getBigSampleTypeId)
                .thenComparing(DtoSampleFolderPhone::getSampleTypeName, collator)
                .thenComparing(watchSpot));
        return sampleFolderPhoneList;
    }

    /**
     * 保存参数日志
     *
     * @param isPublic     是否公共
     * @param paramsConfig 参数
     * @param sample       样品
     * @param watchSpot    点位名称
     * @param oldValue     老值
     * @param newValue     新值
     * @return 日志信息
     */
    private DtoLog getParamsLog(Boolean isPublic, DtoParamsConfig paramsConfig, DtoSample sample, String watchSpot, String oldValue, String newValue) {
        String comment = "";
        DtoLog log = new DtoLog();
        log.setId(UUIDHelper.NewID());
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperateTime(new Date());
        log.setOpinion("");
        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        log.setRemark("");

        if (isPublic) {
            if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())) {
                log.setObjectId(sample.getReceiveId());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改送样单.toString());
                log.setLogType(EnumPRO.EnumLogType.送样单样品信息.getValue());
                log.setObjectType(EnumPRO.EnumLogObjectType.送样单.getValue());
                comment = StringUtils.isNotNullAndEmpty(oldValue) ? String.format("修改送样单的公共参数%s为%s,原参数结果为%s", paramsConfig.getAlias(), StringUtil.isNotEmpty(newValue) ? newValue : "' '", oldValue) :
                        String.format("保存送样单的公共参数%s为%s", paramsConfig.getAlias(), newValue);
            } else if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) {
                log.setObjectId(sample.getReceiveId());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改点位.toString());
                log.setLogType(EnumPRO.EnumLogType.送样单样品信息.getValue());
                log.setObjectType(EnumPRO.EnumLogObjectType.送样单.getValue());
                comment = StringUtils.isNotNullAndEmpty(oldValue) ? String.format("修改点位%s的参数%s为%s,原参数结果为%s", watchSpot, paramsConfig.getAlias(), StringUtil.isNotEmpty(newValue) ? newValue : "' '", oldValue) :
                        String.format("保存点位%s的参数%s为%s", watchSpot, paramsConfig.getAlias(), newValue);
            }
        } else {
            log.setObjectId(sample.getId());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.修改样品.toString());
            log.setLogType(EnumPRO.EnumLogType.样品信息.getValue());
            log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
            comment = StringUtils.isNotNullAndEmpty(oldValue) ? String.format("修改样品%s的参数%s为%s,原参数结果为%s", sample.getCode(), paramsConfig.getAlias(), StringUtil.isNotEmpty(newValue) ? newValue : "' '", oldValue) :
                    String.format("保存样品%s的参数%s为%s", sample.getCode(), paramsConfig.getAlias(), newValue);
        }

        log.setComment(comment);
        return log;
    }

    /**
     * 数据日志
     *
     * @param anaId    数据id
     * @param anaValue 数据值
     * @return 数据日志
     */
    private DtoLog getDataLog(String anaId, String anaValue) {
        DtoLog log = new DtoLog();
        log.setId(UUIDHelper.NewID());
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperateTime(new Date());
        log.setOpinion("");
        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        log.setRemark("");
        log.setObjectId(anaId);
        log.setOperateInfo(EnumPRO.EnumLogOperateType.修改样品.toString());
        log.setLogType(EnumPRO.EnumLogType.样品流程.getValue());
        log.setObjectType(EnumPRO.EnumLogObjectType.送样单.getValue());
        log.setComment(PrincipalContextUser.getPrincipal().getUserName() + "修改数据结果：" + anaValue);
        return log;
    }

    //region 打印样品标签
    @Transactional
    @Override
    public Object createSampleLabelData(Map<String, Object> map) {
        //是否生成空白标签
        Boolean isBlank = map.containsKey("isBlank") && (Boolean) map.get("isBlank");
        //生成空白标签
        if (isBlank) {
            return createBlankSampleLabel(map);
        } else {
            //数据是否进行分组
            Boolean isGroup = map.containsKey("isGroup") && (Boolean) map.get("isGroup");
            //分组ids/analyzeMethod/replace
            List<String> groupIds = map.containsKey("groupIds") ? (List<String>) map.get("groupIds") : new ArrayList<>();
            //是否选择的是样品
            Boolean isSample = map.containsKey("isSample") && (Boolean) map.get("isSample");
            List<String> sampleIds;
            if (isSample) {
                //生成指定样品或者送样单下的样品标签
                sampleIds = map.containsKey("sampleIds") ? (List<String>) map.get("sampleIds")
                        : new ArrayList<>();
            } else {
                List<String> receiveIds = map.containsKey("receiveIds") ? (List<String>) map.get("receiveIds") : new ArrayList<>();
                List<DtoSample> samples = sampleRepository.findByReceiveIdIn(receiveIds);
                sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
            }
            return createSampleLabel(sampleIds, isGroup, groupIds);
        }
    }


    @Override
    public Boolean validateAuth(String userId, String authCode) {
        return authorizeService.haveActionPermission(userId, authCode);
    }

    @Override
    @Transactional
    public void saveSampleDataPhone(DtoSampleDataPhone sampleDataPhone) {
        DtoAnalyseData analyseData = analyseDataRepository.findOne(sampleDataPhone.getAnaId());
        if (analyseData != null) {
            //更新 有效位数/小数位数/检出限/量纲
            analyseData.setMostDecimal(sampleDataPhone.getMostDecimal());
            analyseData.setMostSignificance(sampleDataPhone.getMostSignificance());
            analyseData.setExamLimitValue(sampleDataPhone.getExamLimitValue());
            analyseData.setDimension(sampleDataPhone.getDimension());
            analyseData.setDimensionId(sampleDataPhone.getDimensionId());
            analyseDataRepository.save(analyseData);
            //更新标准值/标样编号
            DtoQualityControl qc = qualityControlRepository.findOne(analyseData.getQcId());
            if (StringUtil.isNotNull(qc)) {
                qc.setQcCode(sampleDataPhone.getQcCode());
                qc.setQcValue(sampleDataPhone.getQcValue());
                qc.setUncertainType(sampleDataPhone.getUncertainType());
                qualityControlRepository.save(qc);
            }
            //更新公式
            DtoAnalyseDataChangeFormula analyseDataChangeFormula = new DtoAnalyseDataChangeFormula();
            analyseDataChangeFormula.setFormulaId(sampleDataPhone.getFormulaId());
            analyseDataChangeFormula.setSubId(UUIDHelper.GUID_EMPTY);
            analyseDataChangeFormula.setAnalyzeDataIds(Collections.singletonList(analyseData.getId()));
            receiveSubSampleRecordService.changeAnalyseDataFormula(analyseDataChangeFormula);
        }
    }

    @Override
    public List<DtoDocument> uploadFolderFile(HttpServletRequest request) {
        String folderId = request.getParameter("folderId");
        String docTypeId = request.getParameter("docTypeId");
        List<DtoDocument> oldDocumentList = documentRepository.findByFolderIdAndDocTypeIdOrderByCreateDateDesc(folderId, docTypeId);
        List<DtoDocument> documents = documentService.upload(request, null);
        // 附件已保存的最大流水号，没有流水号则从0开始计算
        int serialNumber = StringUtil.isNotEmpty(oldDocumentList) ?
                oldDocumentList.stream().filter(p -> StringUtil.isNotNull(p.getSerialNumber()))
                        .max(Comparator.comparing(DtoDocument::getSerialNumber)).map(DtoDocument::getSerialNumber).orElse(0) : 0;
        for (DtoDocument document : documents) {
            // 根据附件类型获取备注
            String remark = getRemarkOfDocTypeId(document.getDocTypeId());
            if (StringUtil.isNotEmpty(remark)) {
                serialNumber++;
                document.setSerialNumber(serialNumber);
                document.setRemark(remark + serialNumber);
            }
        }
        return documentService.save(documents);
    }

    @Override
    public Map<String, List<DtoDocument>> getFolderFile(PageBean<DtoDocument> pageBean, DocumentCriteria documentCriteria) {
        documentService.findByPage(pageBean, documentCriteria);
        List<DtoDocument> dtoDocuments = pageBean.getData();
        // 根据采样类型分组
        Map<String, List<DtoDocument>> map = new HashMap<>();
        Map<String, List<DtoDocument>> documentMap = dtoDocuments.stream().collect(Collectors.groupingBy(DtoDocument::getDocTypeId));
        map.put("front", documentMap.containsKey(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_FRONT) ?
                documentMap.get(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_FRONT) : new ArrayList<>());
        map.put("in", documentMap.containsKey(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_IN) ?
                documentMap.get(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_IN) : new ArrayList<>());
        map.put("after", documentMap.containsKey(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_AFTER) ?
                documentMap.get(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_AFTER) : new ArrayList<>());
        return map;
    }


    /**
     * 根据附件类型获取备注
     *
     * @return 备注
     */
    private String getRemarkOfDocTypeId(String docTypeId) {
        String remark = "";
        switch (docTypeId) {
            case BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_FRONT:
                remark = "采样前";
                break;
            case BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_IN:
                remark = "采样中";
                break;
            case BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_AFTER:
                remark = "采样后";
                break;
        }
        return remark;
    }

    //生成空白标签
    private Object createBlankSampleLabel(Map<String, Object> map) {
        Integer spaceLabelCount = map.containsKey("spaceLabelCount") ? (Integer) map.get("spaceLabelCount") : 0;
        Date sampleDate = map.containsKey("sampleDate") ? DateUtil.stringToDate((String) map.get("sampleDate"), DateUtil.YEAR) : DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);//采样日期
        String sampleTypeId = map.containsKey("sampleTypeId") ? (String) map.get("sampleTypeId") : UUIDHelper.GUID_EMPTY;//样品类型
        Boolean isContainData = false;//是否存在数据
        List<Map<String, Object>> list = new ArrayList<>();
        for (Integer i = 1; i <= spaceLabelCount; i++) {
            String sampleCode = sampleService.createSpaceSampleCode(sampleDate, sampleTypeId, true);
            Map<String, Object> sampleMap = new HashMap<>();
            sampleMap.put("sampleCode", sampleCode);
            sampleMap.put("analyzeItem", "");
            sampleMap.put("sampleType", "");
            sampleMap.put("samplingTimeBegin", DateUtil.dateToString(sampleDate, DateUtil.YEAR));
            sampleMap.put("fixer", "");
            sampleMap.put("sampleFolder", "");
            sampleMap.put("fullName", "");
            sampleMap.put("shortName", "");
            sampleMap.put("welcomeWord", "");
            sampleMap.put("companyName", "");
            sampleMap.put("companyEnglishName", "");
            sampleMap.put("companyAddress", "");
            sampleMap.put("companyPostCode", "");
            sampleMap.put("companyPhone", "");
            list.add(sampleMap);
            isContainData = true;
        }
        list = this.setSampleLabelData(list, isContainData);
        return list;
    }

    /**
     * 生成指定的标签
     *
     * @return 返回数据源
     */
    private Object createSampleLabel(List<String> sampleIds, Boolean isGroup, List<String> groupIds) {
        List<DtoSampleLabelPhone> sampleLabelDataList = this.findSampleLabelData(sampleIds, isGroup, groupIds);
        return createSampleLabel(sampleLabelDataList, isGroup);
    }

    private Object createSampleLabel(List<DtoSampleLabelPhone> sampleLabelDataList, Boolean isGroup) {
        Date year1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        //测试项目ids
        List<String> testAllIds = sampleLabelDataList.stream().map(DtoSampleLabelPhone::getTestId).distinct().collect(Collectors.toList());
        List<String> marks = sampleLabelDataList.stream().map(isGroup ? DtoSampleLabelPhone::getGroupMark : DtoSampleLabelPhone::getMark).distinct().collect(Collectors.toList());
        //相关的测试项目数据
        List<DtoTest> testList = testService.findRedisByIds(testAllIds);
        //样品信息
        List<String> sampleIds = sampleLabelDataList.stream().map(DtoSampleLabelPhone::getSampleId).collect(Collectors.toList());
        List<Map<String, Object>> list = new ArrayList<>();
        if (!StringUtil.isNotEmpty(sampleIds)) {
            return list;
        }
        List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
        //需要新增的分组信息列表
        List<DtoSampleGroup> newSampleGroupList = new ArrayList<>();
        //是否需要删除旧的分组记录标记
        Boolean deleteOldSampleGroupInd = Boolean.FALSE;
        //已经存在的分组信息
        List<DtoSampleGroup> oldSampleGroupList = sampleGroupRepository.findBySampleIdIn(sampleIds);
        Integer page = 0;
        Boolean isContainData = false;//是否存在数据
        for (String mark : marks) {
            //分组后的信息
            List<DtoSampleLabelPhone> dataList = sampleLabelDataList.stream().filter(p -> isGroup ? mark.equals(p.getGroupMark()) :
                    mark.equals(p.getMark())).collect(Collectors.toList());
            List<String> testIds = dataList.stream().map(DtoSampleLabelPhone::getTestId).distinct().collect(Collectors.toList());
            if (dataList.size() > 0) {
                DtoSampleLabelPhone sampleLabelData = dataList.stream().findFirst().orElse(null);
                Map<String, Object> map = new HashMap<>();
                assert sampleLabelData != null;
                map.put("sampleCode", sampleLabelData.getSampleCode());
                map.put("analyzeItem", "□" + String.join(" □", getTestTotalName(testList, testIds)));
                map.put("sampleType", sampleLabelData.getSampleTypeName());
                Date samplingTimeBegin = sampleLabelData.getSamplingTimeBegin();
                map.put("samplingTimeBegin", year1753.compareTo(samplingTimeBegin) == 0 ? "" : DateUtil.dateToString(samplingTimeBegin, DateUtil.YEAR));
                map.put("fixer", sampleLabelData.getFixer() == null ? "" : sampleLabelData.getFixer());
                map.put("sampleFolder", sampleLabelData.getRedFolderName());
                map.put("groupMark", sampleLabelData.getGroupMark());
                map.put("fullName", sampleLabelData.getFullName());
                map.put("shortName", sampleLabelData.getShortName());
                map.put("welcomeWord", sampleLabelData.getWelcomeWord());
                map.put("companyName", sampleLabelData.getCompanyName());
                map.put("companyEnglishName", sampleLabelData.getCompanyEnglishName());
                map.put("companyAddress", sampleLabelData.getCompanyAddress());
                map.put("companyPostCode", sampleLabelData.getCompanyPostCode());
                map.put("companyPhone", sampleLabelData.getCompanyPhone());
                map.put("containerName", sampleLabelData.getContainerName());
                map.put("storageConditions", sampleLabelData.getSaveCondition());
                page++;
                list.add(map);
                isContainData = true;
                //获取样品信息
                DtoSample dtoSample = sampleList.stream().filter(p -> p.getCode().equals(sampleLabelData.getSampleCode()))
                        .findFirst().orElse(null);
                String analyseItemNames = String.join("，", getTestTotalName(testList, testIds));
//                //获取送样单信息
//                DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordRepository.findOne(dtoSample.getReceiveId());
//                //样品交接提交后，分组信息不做任何更新
//                if (receiveSampleRecord == null || !EnumLIM.EnumReceiveRecordStatus.新建.name().equals(receiveSampleRecord.getStatus())) {
//                    continue;
//                }
                //样品已检毕，分组信息不做任何更新
                if (dtoSample == null || EnumPRO.EnumSampleStatus.样品检毕.name().equals(dtoSample.getStatus())) {
                    if (dtoSample != null) {
                        oldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
                    }
                    continue;
                }
                //获取已存在的分组信息
                Optional<DtoSampleGroup> oldSampleGroupOptional = oldSampleGroupList.parallelStream()
                        .filter(p -> p.getSampleId().equals(sampleLabelData.getSampleId())
                                && p.getSampleTypeGroupId().equals(sampleLabelData.getGroupId())).findFirst();
                //是否需要更新标记，默认需要
                Boolean needUpdate = Boolean.TRUE;
                //存在旧的分组信息
                if (oldSampleGroupOptional.isPresent()) {
                    DtoSampleGroup oldSampleGroup = oldSampleGroupOptional.get();
                    //比较分组信息有没有更改，无更改则 needUpdate标记成false
                    if (oldSampleGroup.getSampleId().equals(sampleLabelData.getSampleId())
                            && oldSampleGroup.getReceiveId().equals(sampleLabelData.getReceiveId())
                            && oldSampleGroup.getAnalyseItemNames().equals(analyseItemNames)
                            && oldSampleGroup.getSampleTypeGroupId().equals(sampleLabelData.getGroupId())
                            && oldSampleGroup.getSampleTypeGroupName().equals(sampleLabelData.getGroupName())
                            && oldSampleGroup.getContainerName().equals(sampleLabelData.getContainerName())
                            && oldSampleGroup.getFixer().equals(sampleLabelData.getFixer())
                            && oldSampleGroup.getRemark().equals(sampleLabelData.getRemark())
                            && oldSampleGroupList.size() == marks.size()) {
                        needUpdate = Boolean.FALSE;
                    } else {
                        //分组信息有更改，需要将删除旧的分组信息标记变成true
                        deleteOldSampleGroupInd = Boolean.TRUE;
                    }
                }
                if (needUpdate) {
                    DtoSampleGroup dtoSampleGroup = new DtoSampleGroup();
                    dtoSampleGroup.setSampleId(dtoSample.getId());
                    dtoSampleGroup.setReceiveId(dtoSample.getReceiveId());
                    dtoSampleGroup.setSampleTypeGroupId(sampleLabelData.getGroupId());
                    dtoSampleGroup.setSampleTypeGroupName(sampleLabelData.getGroupName());
                    dtoSampleGroup.setRemark(sampleLabelData.getRemark());
                    dtoSampleGroup.setFixer(sampleLabelData.getFixer());
                    dtoSampleGroup.setContainerName(sampleLabelData.getContainerName());
                    dtoSampleGroup.setPretreatmentMethod(sampleLabelData.getPretreatmentMethod());
                    dtoSampleGroup.setSampleVolume(sampleLabelData.getSampleVolume());
                    dtoSampleGroup.setContainerStatus(sampleLabelData.getContainerStatus());
                    dtoSampleGroup.setSaveCondition(sampleLabelData.getSaveCondition());
                    dtoSampleGroup.setHasScanned(Boolean.FALSE);
                    dtoSampleGroup.setAnalyseItemNames(analyseItemNames);
                    newSampleGroupList.add(dtoSampleGroup);
                    deleteOldSampleGroupInd = Boolean.TRUE;
                }
            }
        }
        //删除旧的分组信息
        if (deleteOldSampleGroupInd) {
            sampleGroupService.delete(oldSampleGroupList);
        }
        //存储新的分组信息
        if (StringUtil.isNotEmpty(newSampleGroupList)) {
            sampleGroupService.save(newSampleGroupList);
        }

        // #endregion
        list = this.setSampleLabelData(list, isContainData);
        return list;
    }

    /**
     * 覆盖空白标签数据
     *
     * @param isContainData 是否有数据
     * @return 返回数据
     */
    private List<Map<String, Object>> setSampleLabelData(List<Map<String, Object>> list, Boolean isContainData) {

        //#region 如果没有数据，需要替换模板上的模板数据
        if (!isContainData) {
            list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("sampleCode", "");
            map.put("analyzeItem", "");
            map.put("sampleType", "");
            map.put("samplingTimeBegin", "");
            map.put("fixer", "");
            map.put("sampleFolder", "");
            map.put("groupMark", "");
            map.put("fullName", "");
            map.put("shortName", "");
            map.put("welcomeWord", "");
            map.put("companyName", "");
            map.put("companyEnglishName", "");
            map.put("companyAddress", "");
            map.put("companyPostCode", "");
            map.put("companyPhone", "");
            map.put("containerName", "");
            map.put("storageConditions", "");
            list.add(map);
        }
        return list;
    }

    private List<DtoSampleLabelPhone> findSampleLabelData(List<String> sampleIds, Boolean isGroup, List<String> groupIds) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.api.dto.DtoSampleLabelPhone(");
        stringBuilder.append("s.id, s.code,s.redFolderName,s.samplingTimeBegin,a.testId,a.id,s.sampleTypeId,st.typeName,");
        stringBuilder.append("st.parentId,a.redAnalyzeItemName,a.isOutsourcing,s.receiveId )");
        stringBuilder.append(" from DtoSample s,DtoAnalyseData a,DtoSampleType st");
        stringBuilder.append(" where 1=1");
        stringBuilder.append(" and s.id = a.sampleId");
        stringBuilder.append(" and s.sampleTypeId = st.id");
        stringBuilder.append(" and s.isDeleted=0 and a.isDeleted=0 and a.isCompleteField = 0");
        stringBuilder.append(" and s.isDeleted=0 and a.isDeleted=0 and a.isOutsourcing = 0");
        stringBuilder.append(" and s.id in :sampleIds");
        stringBuilder.append(" order by s.code asc");
        Map<String, Object> values = new HashMap<>();
        values.put("sampleIds", sampleIds);

        List<DtoSampleLabelPhone> sampleLabelDataList = comRepository.find(stringBuilder.toString(), values);

        //分组相关的信息
        List<DtoSampleTypeGroup> sampleTypeGroupList = this.findSampleTypeGroup(isGroup, groupIds);
        List<DtoSystemConfig> systemConfigList = systemConfigService.findAll();
        DtoSystemConfig systemConfig = StringUtil.isNotEmpty(systemConfigList) ? systemConfigList.get(0) : null;

        for (DtoSampleLabelPhone sampleLabelData : sampleLabelDataList) {
            //是否进行分包
            Boolean isOutsourcing = sampleLabelData.getIsOutsourcing();
            if (isOutsourcing) {
                sampleLabelData.setMark(sampleLabelData.getSampleCode() + sampleLabelData.getRedAnalyzeItemName());
                sampleLabelData.setGroupMark(sampleLabelData.getSampleCode() + sampleLabelData.getRedAnalyzeItemName());
            } else {
                sampleLabelData.setMark(sampleLabelData.getSampleCode());
                Optional<DtoSampleTypeGroup> sampleTypeGroupOptional = sampleTypeGroupList.stream().filter(p -> p.getTestIds().contains(sampleLabelData.getTestId())
                        && p.getSampleTypeId().equals(sampleLabelData.getBigSampleTypeId())).findFirst();
                sampleLabelData.setGroupId(UUIDHelper.GUID_EMPTY);
                //分组信息全置空，为后续判断避免空指针
                sampleLabelData.setContainerName("");
                sampleLabelData.setPretreatmentMethod("");
                sampleLabelData.setSampleVolume("");
                sampleLabelData.setContainerStatus(0);
                sampleLabelData.setRemark("");
                sampleLabelData.setFixer("");
                sampleLabelData.setGroupName("");
                sampleLabelData.setSaveCondition("");
                sampleTypeGroupOptional.ifPresent(dto -> {
                    sampleLabelData.setFixer(dto.getFixer());
                    sampleLabelData.setGroupId(dto.getId());
                    sampleLabelData.setFixer(dto.getFixer());
                    sampleLabelData.setGroupName(dto.getGroupName());
                    sampleLabelData.setContainerName(dto.getContainerName());
                    sampleLabelData.setPretreatmentMethod(dto.getPretreatmentMethod());
                    sampleLabelData.setSampleVolume(dto.getSampleVolume());
                    sampleLabelData.setContainerStatus(dto.getContainerStatus());
                    sampleLabelData.setRemark(dto.getRemark());
                    sampleLabelData.setSaveCondition(dto.getSaveCondition());
                });
                sampleLabelData.setGroupMark(sampleLabelData.getSampleCode() + "," + sampleLabelData.getGroupId());
            }
            setSystemConfigInfo(sampleLabelData, systemConfig);
        }
        return sampleLabelDataList;
    }

    /**
     * 设置系统基础信息
     *
     * @param sampleLabelData 样品标签数据
     * @param systemConfig    系统信息对象
     */
    private void setSystemConfigInfo(DtoSampleLabelPhone sampleLabelData, DtoSystemConfig systemConfig) {
        sampleLabelData.setFullName((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getFullName())) ? systemConfig.getFullName() : "");
        sampleLabelData.setShortName((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getShortName())) ? systemConfig.getShortName() : "");
        sampleLabelData.setWelcomeWord((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getWelcomeWord())) ? systemConfig.getWelcomeWord() : "");
        sampleLabelData.setCompanyName((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getCompanyName())) ? systemConfig.getCompanyName() : "");
        sampleLabelData.setCompanyAddress((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getCompanyAddress())) ? systemConfig.getCompanyAddress() : "");
        sampleLabelData.setCompanyPostCode((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getCompanyPostCode())) ? systemConfig.getCompanyPostCode() : "");
        sampleLabelData.setCompanyPhone((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getCompanyPhone())) ? systemConfig.getCompanyPhone() : "");
        sampleLabelData.setCompanyEnglishName((StringUtil.isNotNull(systemConfig) && StringUtil.isNotEmpty(systemConfig.getCompanyEnglishName())) ? systemConfig.getCompanyEnglishName() : "");
    }

    /**
     * 取出分组信息
     *
     * @param isGroup  是否分组显示
     * @param groupIds 选择的分组id
     * @return 返回分组信息
     */
    private List<DtoSampleTypeGroup> findSampleTypeGroup(Boolean isGroup, List<String> groupIds) {
        List<DtoSampleTypeGroup> sampleTypeGroups = new ArrayList<>();
        if (isGroup && groupIds.size() > 0) {
            StringBuilder select = new StringBuilder("select a from DtoSampleTypeGroup as a");
            select.append(" where 1=1 ");
            select.append(" and a.parentId in :groupIds");
            Map<String, Object> values = new HashMap<>();
            values.put("groupIds", groupIds);
            sampleTypeGroups = comRepository.find(select.toString(), values);
            List<String> gIds = sampleTypeGroups.stream().map(DtoSampleTypeGroup::getId).distinct().collect(Collectors.toList());
            //获取相关的测试项目分组信息
            List<DtoSampleTypeGroup2Test> group2Tests =
                    sampleTypeGroup2TestRepository.findBySampleTypeGroupIds(gIds);
            for (DtoSampleTypeGroup sampleTypeGroup : sampleTypeGroups) {
                List<String> testIds =
                        group2Tests.stream().filter(p -> p.getSampleTypeGroupId().equals(sampleTypeGroup.getId())).map(DtoSampleTypeGroup2Test::getTestId).distinct().collect(Collectors.toList());
                sampleTypeGroup.setTestIds(testIds);
            }
        }
        return sampleTypeGroups;
    }

    /**
     * 获取合并的测试项目名称
     *
     * @param testList 测试项目
     * @param testIds  测试项目id
     * @return 返回数据
     */
    private List<String> getTestTotalName(List<DtoTest> testList, List<String> testIds) {
        //显示总称的测试项目
        List<String> testNames = testList.stream().filter(p -> testIds.contains(p.getId())
                && p.getIsShowTotalTest()).map(DtoTest::getTotalTestName).distinct().collect(Collectors.toList());
        //不显示总称的测试项目
        List<String> testNoShows = testList.stream().filter(p -> testIds.contains(p.getId())
                && !p.getIsShowTotalTest()).map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
        testNames.addAll(testNoShows);
        Collator comparator = Collator.getInstance(Locale.CHINA);
        return testNames.stream().sorted(Comparator.comparing(t -> t, comparator)).collect(Collectors.toList());
    }

    private void saveParamsValue(List<DtoSample> sampleList, List<DtoParamsConfig> paramsConfigList,
                                 List<DtoParamsDataApiPhone> paramsDataPhoneList, List<DtoLog> logs,
                                 Map<String, DtoParamsData> allParamsDataMap, List<DtoSampleFolder> folderList) {
        for (DtoSample sample : sampleList) {
            Optional<DtoSampleFolder> sampleFolder = folderList.stream().filter(p -> p.getId().equals(sample.getSampleFolderId())).findFirst();
            String watchSpot = "";
            if (sampleFolder.isPresent()) {
                watchSpot = sampleFolder.get().getWatchSpot();
            }
            for (DtoParamsConfig paramsConfig : paramsConfigList) {
                DtoParamsDataApiPhone paramsDataPhone = paramsDataPhoneList.stream().filter(p -> paramsConfig.getId().equals(p.getParamsConfigId())).findFirst().orElse(null);
                DtoParamsData paramsData = new DtoParamsData();
                paramsData.setObjectId(sample.getId());
                paramsData.setObjectType(EnumPRO.EnumParamsDataType.样品.getValue());
                paramsData.setParamsConfigId(paramsConfig.getId());
                paramsData.setParamsName(paramsConfig.getAlias());
                if (StringUtil.isNotNull(paramsDataPhone)) {
                    paramsData.setParamsValue(paramsDataPhone.getParamsValue());
                }
                paramsData.setDimension(paramsConfig.getDimension());
                paramsData.setDimensionId(paramsConfig.getDimensionId());
                paramsData.setOrderNum(paramsConfig.getOrderNum());
                paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                String key = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                if (allParamsDataMap.containsKey(key)) {
                    logs.add(getParamsLog(false, paramsConfig, sample, watchSpot, allParamsDataMap.get(key).getParamsValue(), paramsConfig.getParamsValue()));
                    paramsData.setId(allParamsDataMap.get(key).getId());
                    comRepository.merge(paramsData);
                } else {
                    logs.add(this.getParamsLog(false, paramsConfig, sample, watchSpot, null, paramsConfig.getParamsValue()));
                    paramsDataRepository.save(paramsData);
                }
            }
        }
    }
    //endregion

    /**
     * 查找样品关联外部质控样
     *
     * @param sampleList 样品列表
     * @return List<DtoSample>
     */
    private List<DtoSample> findXcQcSamples(List<DtoSample> sampleList) {
        List<DtoSample> qcSamList = sampleRepository.findByAssociateSampleIdIn(sampleList.stream().map(DtoSample::getId).collect(Collectors.toList()));
        // 筛选现场串联样
        List<String> clSampleIds = qcSamList.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.串联样.getValue())).map(DtoSample::getId).collect(Collectors.toList());
        List<String> qcIds = qcSamList.stream().map(DtoSample::getQcId).collect(Collectors.toList());
        List<DtoQualityControl> qualityControls = StringUtil.isNotEmpty(qcIds) ? qualityControlRepository.findAll(qcIds).stream().filter(qc -> EnumLIM.EnumQCGrade.外部质控.getValue().equals(qc.getQcGrade())).collect(Collectors.toList()) : new ArrayList<>();
        List<String> xcQcIds = qualityControls.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
        qcSamList.removeIf(qc -> !xcQcIds.contains(qc.getQcId()) && !clSampleIds.contains(qc.getId()));
        return qcSamList;
    }


    /**
     * 送样单提交后，如有实验室指标则同时流转到样品交接(需要根据配置开关决定)
     *
     * @param sampleIds 样品id列表
     */
    private void flowToReceiveSample(List<String> sampleIds) {
        DtoCode code = codeService.findByCode(ProCodeHelper.LIM_FLOW_RECEIVE_SAMPLE);
        if (code != null && "1".equals(code.getDictValue())) {
            //获取样品相关的数据
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
            //找出非现场和非分包的数据
            analyseDataList = analyseDataList.parallelStream().filter(p -> !p.getIsCompleteField()
                    && !p.getIsOutsourcing() && !p.getIsSamplingOut()).collect(Collectors.toList());
            //对实验室的数据进行处理，让其相关的送样单流转到样品交接
            if (StringUtil.isNotEmpty(analyseDataList)) {
                sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
                //获取样品相关的送样单id
                List<String> receiveIds = new ArrayList<>();
                for (DtoSample sample : sampleList) {
                    String loopReceiveId = sample.getReceiveId();
                    if (!receiveIds.contains(loopReceiveId)) {
                        receiveIds.add(loopReceiveId);
                    }
                }

                //获取相关送样单
                List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findAll(receiveIds);
                //流转到样品交接
                for (DtoReceiveSampleRecord dtoReceiveSampleRecord : receiveSampleRecordList) {
                    DtoStatusForRecord record = statusForRecordRepository.findByReceiveIdAndModule(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                    if (!StringUtil.isNotNull(record)) {
                        statusForRecordService.createStatus(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                    }
                }

            }
        }
    }

    /**
     * 移动端添加现场质控样
     *
     * @param maps 质控样参数
     * @return 返回质控样数据
     */
    @Transactional
    @Override
    public void addXCSamplePhone(List<Map<String, Object>> maps) {
        List<DtoQualityControlTemp> tempList = new ArrayList<>();
        List<String> sampleIds = StringUtil.isNotEmpty(maps) ? (List<String>) maps.get(0).get("sampleIds") : new ArrayList<>();
        String sampleTypeId = StringUtil.isNotEmpty(maps) ? (String) maps.get(0).get("sampleTypeId") : UUIDHelper.GUID_EMPTY;
        List<DtoSampleParamsApiPhone> sampleParamsApiPhones = new ArrayList<>();
        for (String sampleId : sampleIds) {
            // 因为移动端选中的样品可能为页面没加载的样品，导致无法传输因子数据，需要根据选择的样品id先查询样品参数和因子信息
            sampleParamsApiPhones.add(this.getSampleParams(sampleId, sampleTypeId, 1));
        }
        for (Map<String, Object> map : maps) {
            for (String sampleId : sampleIds) {
                DtoQualityControlTemp temp = new DtoQualityControlTemp();
                temp.setQcGrade((Integer) map.getOrDefault("qcGrade", -1));
                temp.setQcType((Integer) map.getOrDefault("qcType", -1));
                temp.setQcValue((String) map.getOrDefault("qcValue", ""));
                temp.setQcCode((String) map.getOrDefault("qcCode", ""));
                temp.setSampleId(sampleId);
                temp.setCopyTimes(1);
                temp.setSampleCategory(EnumPRO.EnumSampleCategory.质控样.getValue());
                temp.setQcStandardId((String) map.getOrDefault("qcStandardId", UUIDHelper.GUID_EMPTY));
                temp.setTestId((String) map.getOrDefault("testId", UUIDHelper.GUID_EMPTY));
                temp.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
                temp.setDimension((String) map.getOrDefault("dimension", ""));
                temp.setDimensionId((String) map.getOrDefault("dimensionId", ""));
                temp.setUncertainType((Integer) map.getOrDefault("uncertainType", 10));
                Optional<DtoSampleParamsApiPhone> sampleParamsApiPhoneOptional = sampleParamsApiPhones.stream().filter(p -> p.getSamId().equals(sampleId)).findFirst();
                if (sampleParamsApiPhoneOptional.isPresent()) {
                    DtoSampleParamsApiPhone sampleParamsApiPhone = sampleParamsApiPhoneOptional.get();
                    List<DtoSampleDataPhone> analyseDataPhoneList = sampleParamsApiPhone.getAnalyseDataPhoneList().stream().filter(p -> p.getTestId().equals(temp.getTestId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(analyseDataPhoneList)) {
                        List<Map<String, Object>> analyseDataMaps = new ArrayList<>();
                        for (DtoSampleDataPhone dataPhone : analyseDataPhoneList) {
                            Map<String, Object> dataMap = new HashMap<>();
                            dataMap.put("id", dataPhone.getAnaId());
                            dataMap.put("formula", dataPhone.getFormula());
                            dataMap.put("formulaId", dataPhone.getFormulaId());
                            dataMap.put("sampleTypeName", dataPhone.getSampleTypeName());
                            analyseDataMaps.add(dataMap);
                        }
                        temp.setAnalyseData(analyseDataMaps);
                        tempList.add(temp);
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(tempList)) {
            sampleService.addInnerSample(tempList, true);
        }
    }

    /**
     * 分页查询消耗品列表
     *
     * @param pageBean     分页对象
     * @param baseCriteria 查询条件
     */
    @Override
    public void findStandardByPage(PageBean<DtoConsumable> pageBean, BaseCriteria baseCriteria) {
        consumableService.findByPage(pageBean, baseCriteria);
    }

    /**
     * 分页查询量纲列表
     *
     * @param pageBean     分页对象
     * @param baseCriteria 查询条件
     */
    @Override
    public void findDimensionByPage(PageBean<DtoDimension> pageBean, BaseCriteria baseCriteria) {
        dimensionService.findByPage(pageBean, baseCriteria);
    }

    @Override
    @Transactional
    public void removeQCDataById(String anaId) {
        DtoAnalyseData analyseData = analyseDataRepository.findOne(anaId);
        if (StringUtil.isNotNull(analyseData)) {
            DtoSample sample = sampleRepository.findOne(analyseData.getSampleId());
            DtoReceiveSubSampleRecord receiveSubSampleRecord = receiveSubSampleRecordService.findByReceiveIdAndType(sample.getReceiveId(), EnumPRO.EnumSubRecordType.现场.getValue());
            analyseDataService.removeDataFromSubRecord(Collections.singletonList(anaId), null == receiveSubSampleRecord ? UUIDHelper.GUID_EMPTY : receiveSubSampleRecord.getId());
        }
    }


    @Override
    public String getSignRange() {
        ConfigModel configModel = configService.findConfig("app.sign.range");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            return configModel.getConfigValue();
        }
        return null;
    }

    /**
     * 创建采样单或者假如采样单时，样品编号为空的样品自动生成编号
     *
     * @param recSamList   样品集合
     * @param projectId    项目id
     * @param samplingTime 采样日期
     */
    private void handleSampleEmptyCode(List<DtoSample> recSamList, String projectId, Date samplingTime) {
        // 生成采样单前，筛选样品编号为空的样品，先生成编号
        List<String> createCodeSampleIds = recSamList.stream().filter(p -> StringUtil.isEmpty(p.getCode())).map(DtoSample::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(createCodeSampleIds)) {
            recSamList.removeIf(p -> createCodeSampleIds.contains(p.getId()));
            this.createSampleCode(createCodeSampleIds, projectId, samplingTime);
            List<DtoSample> samples = sampleRepository.findAll(createCodeSampleIds);
            recSamList.addAll(samples);
        }
    }


    @Autowired
    @Lazy
    public void setIConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    public void setAnalyseOriginalRecordRepository(AnalyseOriginalRecordRepository analyseOriginalRecordRepository) {
        this.analyseOriginalRecordRepository = analyseOriginalRecordRepository;
    }

    @Autowired
    @Lazy
    public void setParamsFormulaService(ParamsFormulaService paramsFormulaService) {
        this.paramsFormulaService = paramsFormulaService;
    }

    @Autowired
    @Lazy
    public void setParamsTestFormulaService(ParamsTestFormulaService paramsTestFormulaService) {
        this.paramsTestFormulaService = paramsTestFormulaService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    public void setSystemConfigService(SystemConfigService systemConfigService) {
        this.systemConfigService = systemConfigService;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setStatusForRecordRepository(StatusForRecordRepository statusForRecordRepository) {
        this.statusForRecordRepository = statusForRecordRepository;
    }

    @Autowired
    public void setStatusForRecordService(StatusForRecordService statusForRecordService) {
        this.statusForRecordService = statusForRecordService;
    }

    @Autowired
    public void setAuthorizeService(AuthorizeService authorizeService) {
        this.authorizeService = authorizeService;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setReceiveSubSampleRecordService(ReceiveSubSampleRecordService receiveSubSampleRecordService) {
        this.receiveSubSampleRecordService = receiveSubSampleRecordService;
    }

    @Autowired
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    @Lazy
    public void setConsumableService(ConsumableService consumableService) {
        this.consumableService = consumableService;
    }

    @Autowired
    @Lazy
    public void setDimensionService(DimensionService dimensionService) {
        this.dimensionService = dimensionService;
    }

    @Autowired
    public void setSub2SampleRepository(ReceiveSubSampleRecord2SampleRepository sub2SampleRepository) {
        this.sub2SampleRepository = sub2SampleRepository;
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    public void setAnalyzeMethodService(AnalyzeMethodService analyzeMethodService) {
        this.analyzeMethodService = analyzeMethodService;
    }

    @Autowired
    public void setSampleCodeService(ISampleCodeService sampleCodeService) {
        this.sampleCodeService = sampleCodeService;
    }

    @Autowired
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }
}
