package com.sinoyd.lims.api.controller;

import com.sinoyd.base.criteria.ConsumableCriteria;
import com.sinoyd.base.criteria.DimensionCriteria;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.api.criteria.ProjectPhoneCriteria;
import com.sinoyd.lims.api.criteria.ReceivePhoneCriteria;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.api.dto.customer.DtoParamsApiPhoneInfo;
import com.sinoyd.lims.api.dto.customer.DtoSampleCopy;
import com.sinoyd.lims.api.dto.customer.DtoSampleParamsApiPhone;
import com.sinoyd.lims.api.service.FieldMonitoringService;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoOutSampleDelete;
import com.sinoyd.lims.pro.dto.customer.DtoSampleDataPhone;
import com.sinoyd.lims.pro.dto.customer.DtoSampleItemParams;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import com.sinoyd.lims.pro.service.SchemeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Api(tags = "示例: fieldMonitoring服务")
@RestController
@RequestMapping("api/field/monitor")
public class FieldMonitoringController extends BaseJpaController<DtoProject, String, FieldMonitoringService> {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private SchemeService schemeService;

    /**
     * 查询
     *
     * @param projectPhoneCriteria 查询条件
     * @return RestResponse<List < DtoProjectPhone>>
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping
    public RestResponse<List<DtoProjectPhone>> getProjectList(ProjectPhoneCriteria projectPhoneCriteria) {
        PageBean<DtoProject> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectPhone>> restResponse = new RestResponse<>();
        List<DtoProjectPhone> projectPhoneList = service.getProjectList(pageBean, projectPhoneCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(projectPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectPhoneList);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询
     *
     * @param receivePhoneCriteria 查询条件
     * @return RestResponse<List < DtoProjectPhone>>
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping("/receiveList")
    public RestResponse<List<DtoProjectPhone>> getReceiveProjectList(ReceivePhoneCriteria receivePhoneCriteria) {
        PageBean<DtoReceiveSampleRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectPhone>> restResponse = new RestResponse<>();
        List<DtoProjectPhone> projectPhoneList = service.getReceiveProjectList(pageBean, receivePhoneCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(projectPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectPhoneList);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据编码获取项目类型
     *
     * @return RestResponse<List < Map < String, Object>>>
     */
    @ApiOperation(value = "根据编码获取项目类型", notes = "根据编码获取项目类型")
    @GetMapping("/code")
    public RestResponse<List<Map<String, Object>>> findByCode(@RequestParam(name = "code") String code, @RequestParam(name = "values") String[] values) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(service.getProjectTypeList(code, values));
        return restResponse;
    }

    /**
     * 按主键查询项目
     *
     * @param id 主键id
     * @return RestResponse<DtoProjectDetailPhone>
     */
    @ApiOperation(value = "按主键查询项目", notes = "按主键查询项目")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoProjectDetailPhone> findProject(@PathVariable(name = "id") String id) {
        RestResponse<DtoProjectDetailPhone> restResponse = new RestResponse<>();
        DtoProjectDetailPhone project = service.getProjectDetailById(id);
        restResponse.setData(project);
        restResponse.setRestStatus(StringUtil.isNull(project) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 获取点位集合
     *
     * @param folder 参数
     * @return 点位列表
     */
    @ApiOperation(value = "获取点位集合", notes = "获取点位集合")
    @PostMapping("/folderList")
    public RestResponse<List<DtoSampleFolderPhone>> getFolderList(@RequestBody DtoFolderInfo folder) {
        RestResponse<List<DtoSampleFolderPhone>> restResp = new RestResponse<>();
        List<DtoSampleFolderPhone> folderPhoneList = service.getFolderList(folder.getProjectId(), folder.getFolderName(), folder.getSampleTypeIds(), folder.getCycleOrder(), folder.getTimesOrder());
        restResp.setData(folderPhoneList);
        restResp.setRestStatus(StringUtil.isEmpty(folderPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 根据id批量删除外部送样样品
     *
     * @param dto 删除结构
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "根据id批量删除外部送样样品", notes = "根据id批量删除外部送样样品")
    @PostMapping(path = "/outside")
    public RestResponse<Boolean> delete(@RequestBody DtoOutSampleDelete dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        schemeService.deleteOutsideSample(dto.getSampleIds(), dto.getReceiveId());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 创建送样单
     *
     * @param recordInfo 送样单信息
     * @return true
     */
    @ApiOperation(value = "创建送样单", notes = "创建送样单")
    @PostMapping("/createRecord")
    public RestResponse<Boolean> createReceiveSampleRecord(@RequestBody DtoRecordInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.createReceiveSampleRecord(recordInfo.getProjectId(), recordInfo.getFolderInfo(), recordInfo.getSamplingPersonIds(), recordInfo.getSamplingLeaderId(), recordInfo.getSamplingLeaderName(), recordInfo.getSamplingTime());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 创建送样单
     *
     * @param record 送样单信息
     * @return true
     */
    @ApiOperation(value = "创建送样单", notes = "创建送样单")
    @PostMapping("/createSendReocr")
    public RestResponse<DtoReceiveSampleRecordPhone> createSampleRecord(@RequestBody DtoReceiveSampleRecord record) {
        RestResponse<DtoReceiveSampleRecordPhone> restResp = new RestResponse<>();
        DtoReceiveSampleRecordPhone recordPhone = service.createReceiveSampleRecord(record);
        restResp.setData(recordPhone);
        restResp.setRestStatus(StringUtil.isNull(recordPhone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 获取详情
     *
     * @param folder 项目id，送样单id
     * @return true
     */
    @ApiOperation(value = "获取详情", notes = "获取详情")
    @PostMapping("/getRecord")
    public RestResponse<DtoReceiveSampleRecordPhone> getRecord(@RequestBody DtoFolderInfo folder) {
        RestResponse<DtoReceiveSampleRecordPhone> restResp = new RestResponse<>();
        DtoReceiveSampleRecordPhone recordPhone = service.getRecord(folder.getProjectId(), folder.getReceiveId());
        restResp.setData(recordPhone);
        restResp.setRestStatus(StringUtil.isNull(recordPhone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 加入已有送样单
     *
     * @param recordInfo 送样单信息
     * @return true
     */
    @ApiOperation(value = "加入已有送样单", notes = "加入已有送样单")
    @PostMapping("/joinRecord")
    public RestResponse<Boolean> joinToReceiveSampleRecord(@RequestBody DtoRecordInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.joinToReceiveSampleRecord(recordInfo.getProjectId(), recordInfo.getReceiveId(), recordInfo.getFolderInfo());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 通过项目id获取送样单集合
     *
     * @param projectId 项目id
     * @return RestResponse<List < DtoReceiveSampleRecordPhone>>
     */
    @ApiOperation(value = "通过项目id获取送样单集合", notes = "通过项目id获取送样单集合")
    @GetMapping(path = "/recordByProjectId")
    public RestResponse<List<DtoReceiveSampleRecordPhone>> getReceiveSampleRecordByProjectId(@RequestParam(name = "projectId") String projectId,
                                                                                             @RequestParam(name = "sampleTypeIds") List<String> sampleTypeIds,
                                                                                             @RequestParam(name = "isFilterSamPerson") Boolean isFilterSamPerson) {
        RestResponse<List<DtoReceiveSampleRecordPhone>> restResponse = new RestResponse<>();
        List<DtoReceiveSampleRecordPhone> recordList = service.getReceiveSampleRecordByProjectId(projectId, sampleTypeIds, isFilterSamPerson);
        restResponse.setRestStatus(StringUtil.isEmpty(recordList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(recordList);
        return restResponse;
    }

    /**
     * 获取点位信息
     *
     * @param folder 参数
     * @return 点位信息
     */
    @ApiOperation(value = "获取点位集合", notes = "获取点位集合")
    @PostMapping("/folder")
    public RestResponse<DtoSampleFolderPhone> getFolderById(@RequestBody DtoFolderInfo folder) {
        RestResponse<DtoSampleFolderPhone> restResp = new RestResponse<>();
        DtoSampleFolderPhone folderPhone = service.getFolderById(folder.getFolderId(), folder.getCycleOrder(),folder.getReceiveId());
        restResp.setData(folderPhone);
        restResp.setRestStatus(StringUtil.isNotNull(folderPhone) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 查询测试项目
     *
     * @param testCriteria 查询条件
     * @return RestResponse<List < DtoAnalyseDataPhone>>
     */
    @ApiOperation(value = "分页动态条件查询测试项目", notes = "分页动态条件查询测试项目")
    @GetMapping("/testList")
    public RestResponse<List<DtoAnalyseDataPhone>> getTestList(TestCriteria testCriteria) {
        PageBean<DtoTest> pageBean = super.getPageBean();
        RestResponse<List<DtoAnalyseDataPhone>> restResponse = new RestResponse<>();
        List<DtoAnalyseDataPhone> testPhoneList = service.getTestList(pageBean, testCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(testPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(testPhoneList);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 保存点位信息
     *
     * @param folder 参数
     * @return 保存点位信息
     */
    @ApiOperation(value = "保存点位信息", notes = "保存点位信息")
    @PostMapping("/saveFolder")
    public RestResponse<Boolean> saveFolderSign(@RequestBody @Validated DtoFolderInfo folder) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.saveSignInfo(folder.getFolderId(), folder.getCycleOrder(), folder.getLon(),
                folder.getLat(), folder.getSignTime(), folder.getSignTip(),
                folder.getIsVerify(), folder.getVoiceTip());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 保存语音说明
     *
     * @param folder 参数
     * @return 保存语音说明
     */
    @ApiOperation(value = "保存语音说明", notes = "保存语音说明")
    @PostMapping("/saveVoiceTip")
    public RestResponse<Boolean> saveVoiceTip(@RequestBody @Validated DtoFolderInfo folder) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.saveVoiceTip(folder.getFolderSignId(), folder.getVoiceTip());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 样品信息列表
     *
     * @param folder 参数
     * @return 样品信息列表
     */
    @ApiOperation(value = "样品信息列表", notes = "样品信息列表")
    @PostMapping("/sampleList")
    public RestResponse<List<DtoSample>> getSampleList(@RequestBody DtoFolderInfo folder) {
        RestResponse<List<DtoSample>> restResp = new RestResponse<>();
        List<DtoSample> samList = service.getSampleList(folder.getFolderId(), folder.getProjectId(), folder.getReceiveId(), folder.getCycleOrder());
        restResp.setData(samList);
        restResp.setRestStatus(StringUtil.isNotNull(samList) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 样品信息列表
     *
     * @param folder 参数
     * @return 样品信息列表
     */
    @ApiOperation(value = "样品信息列表", notes = "样品信息列表")
    @PostMapping("/sampleListWithInvalid")
    public RestResponse<List<DtoSample>> getSampleListWithInvalid(@RequestBody DtoFolderInfo folder) {
        RestResponse<List<DtoSample>> restResp = new RestResponse<>();
        List<DtoSample> samList = service.getSampleWithInvalidList(folder.getFolderId(), folder.getProjectId(), folder.getReceiveId(), folder.getCycleOrder());
        restResp.setData(samList);
        restResp.setRestStatus(StringUtil.isNotNull(samList) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 样品信息列表(包含作废数据和标样)
     *
     * @param folder 参数
     * @return 样品信息列表
     */
    @ApiOperation(value = "样品信息列表(包含作废数据和标样)", notes = "样品信息列表(包含作废数据和标样)")
    @PostMapping("/sampleListInvalidAndStandard")
    public RestResponse<List<DtoSample>> getSampleWithInvalidAndStandardList(@RequestBody DtoFolderInfo folder) {
        RestResponse<List<DtoSample>> restResp = new RestResponse<>();
        List<DtoSample> samList = service.getSampleWithInvalidAndStandardList(folder.getFolderId(), folder.getProjectId(), folder.getReceiveId(), folder.getCycleOrder());
        restResp.setData(samList);
        restResp.setRestStatus(StringUtil.isNotNull(samList) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 剔除样品
     *
     * @param folder 参数
     * @return 剔除样品
     */
    @ApiOperation(value = "剔除样品", notes = "剔除样品")
    @PostMapping("/removeQcSample")
    public RestResponse<Boolean> removeQcSample(@RequestBody DtoFolderInfo folder) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.removeQcSample(folder.getProjectId(), folder.getSampleIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 创建样品编号
     *
     * @param folder 参数
     * @return 创建样品编号
     */
    @ApiOperation(value = "创建样品编号", notes = "创建样品编号")
    @PostMapping("/createSampleCode")
    public RestResponse<Boolean> createSampleCode(@RequestBody DtoFolderInfo folder) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.createSampleCode(folder.getSampleIds(), folder.getProjectId(), folder.getSamplingTime());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 获取样品信息
     *
     * @param folder 参数
     * @return 获取样品信息
     */
    @ApiOperation(value = "获取样品信息", notes = "获取样品信息")
    @PostMapping("/getSampleDetail")
    public RestResponse<DtoSamplePhone> getSampleDetail(@RequestBody DtoFolderInfo folder) {
        RestResponse<DtoSamplePhone> restResp = new RestResponse<>();
        DtoSamplePhone sample = service.getSampleDetail(folder.getSampleId());
        restResp.setData(sample);
        restResp.setRestStatus(StringUtil.isNotNull(sample) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 获取送样单详情
     *
     * @param receiveId 送样单id
     * @return RestResponse<DtoReceiveSampleRecordPhone>
     */
    @ApiOperation(value = "获取送样单详情", notes = "获取送样单详情")
    @GetMapping(path = "/record")
    public RestResponse<DtoReceiveSampleRecordPhone> getRecordById(@RequestParam(name = "receiveId") String receiveId) {
        RestResponse<DtoReceiveSampleRecordPhone> restResponse = new RestResponse<>();
        DtoReceiveSampleRecordPhone record = service.getRecordById(receiveId);
        restResponse.setRestStatus(StringUtil.isNotNull(record) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        restResponse.setData(record);
        return restResponse;
    }

    /**
     * 提交送样单
     *
     * @param recordInfo 送样单信息
     * @return true
     */
    @ApiOperation(value = "提交送样单", notes = "提交送样单")
    @PostMapping("/submitRecord")
    public RestResponse<Boolean> submitReceive(@RequestBody DtoRecordInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.submitReceive(recordInfo.getReceiveIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 保存送样单信息
     *
     * @param receiveSampleRecordPhone 送样单信息
     * @return true
     */
    @ApiOperation(value = "保存送样单信息", notes = "保存送样单信息")
    @PostMapping("/saveRecord")
    public RestResponse<Boolean> saveRecordInfo(@RequestBody DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.saveRecordInfo(receiveSampleRecordPhone);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 获取点位集合
     *
     * @param folder 参数
     * @return 点位列表
     */
    @ApiOperation(value = "获取点位集合", notes = "获取点位集合")
    @PostMapping("/folderByRecId")
    public RestResponse<List<DtoSampleFolderPhone>> getFolderInfoByReceiveId(@RequestBody DtoFolderInfo folder) {
        RestResponse<List<DtoSampleFolderPhone>> restResp = new RestResponse<>();
        List<DtoSampleFolderPhone> folderPhoneList = service.getFolderInfoByReceiveId(folder.getReceiveId());
        restResp.setData(folderPhoneList);
        restResp.setRestStatus(StringUtil.isEmpty(folderPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 剔除送样单样品
     *
     * @param recordInfo 送样单信息
     * @return true
     */
    @ApiOperation(value = "提交送样单", notes = "提交送样单")
    @PostMapping("/removeRecord")
    public RestResponse<Boolean> removeSample(@RequestBody DtoRecordInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.removeSample(recordInfo.getReceiveId(), recordInfo.getFolderInfo());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 剔除送样单样品
     *
     * @param recordInfo 送样单信息
     * @return true
     */
    @ApiOperation(value = "提交送样单", notes = "提交送样单")
    @PostMapping("/removeOutRecord")
    public RestResponse<Boolean> removeOutSample(@RequestBody DtoRecordInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.removeOutSample(recordInfo.getReceiveId(), recordInfo.getFolderInfo());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 获取点位、公共参数
     *
     * @param paramsInfo 参数
     * @return true
     */
    @ApiOperation(value = "获取点位、公共参数", notes = "获取点位、公共参数")
    @PostMapping("/folderParams")
    public RestResponse<List<Map<String, Object>>> getFolderParams(@RequestBody DtoParamsApiPhoneInfo paramsInfo) {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        List<Map<String, Object>> paramsConfigList = service.getFolderParams(paramsInfo.getSampleFolderId(), paramsInfo.getProjectId(), paramsInfo.getReceiveId(), paramsInfo.getSampleTypeId(), paramsInfo.getCycleOrder());
        restResp.setRestStatus(StringUtil.isEmpty(paramsConfigList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(paramsConfigList);
        return restResp;
    }

    /**
     * 送样单剔除样品
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "送样单剔除样品", notes = "送样单剔除样品")
    @PostMapping(path = "/remove")
    public RestResponse<Boolean> remove(@RequestBody DtoSampleItemParams dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        Boolean isDelRecord = receiveSampleRecordService.removeSample(dto.getReceiveId(), dto.getSampleIds());
        restResponse.setData(isDelRecord);
        return restResponse;
    }

    /**
     * 获取样品参数
     *
     * @param paramsInfo 参数
     * @return true
     */
    @ApiOperation(value = "获取样品参数", notes = "获取样品参数")
    @PostMapping("/sampleParams")
    public RestResponse<DtoSampleParamsApiPhone> getSampleParams(@RequestBody DtoParamsApiPhoneInfo paramsInfo) {
        RestResponse<DtoSampleParamsApiPhone> restResp = new RestResponse<>();
        DtoSampleParamsApiPhone sampleParamsPhone = service.getSampleParams(paramsInfo.getSampleId(), paramsInfo.getSampleTypeId(), paramsInfo.getIsEnterValue());
        restResp.setRestStatus(!StringUtil.isNotNull(sampleParamsPhone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(sampleParamsPhone);
        return restResp;
    }

    /**
     * 获取样品参数（带质控样）
     *
     * @param paramsInfo 参数
     * @return true
     */
    @ApiOperation(value = "获取样品参数（带质控样）", notes = "获取样品参数（带质控样）")
    @PostMapping("/sampleParamsList")
    public RestResponse<List<DtoSampleParamsApiPhone>> getSampleParamsList(@RequestBody DtoParamsApiPhoneInfo paramsInfo) {
        RestResponse<List<DtoSampleParamsApiPhone>> restResp = new RestResponse<>();
        List<DtoSampleParamsApiPhone> sampleParamsPhoneList = service.getSampleParamsList(paramsInfo.getSampleId(), paramsInfo.getSampleTypeId(), paramsInfo.getIsEnterValue());
        restResp.setRestStatus(!StringUtil.isNotEmpty(sampleParamsPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(sampleParamsPhoneList);
        return restResp;
    }

    /**
     * 保存点位参数
     *
     * @param recordInfo 送样单信息
     * @return truesampleList
     */
    @ApiOperation(value = "保存点位参数", notes = "保存点位参数")
    @PostMapping("/saveFolderParamsValue")
    public RestResponse<Boolean> saveFolderParamsValue(@RequestBody DtoParamsApiPhoneInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.saveFolderParamsValue(recordInfo.getSampleFolderId(), recordInfo.getProjectId(), recordInfo.getReceiveId(), recordInfo.getCycleOrder(), recordInfo.getParamsConfigList());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 保存样品参数
     *
     * @param recordInfo 送样单信息
     * @return true
     */
    @ApiOperation(value = "保存样品参数", notes = "保存样品参数")
    @PostMapping("/saveSampleParamsValue")
    public RestResponse<Boolean> saveSampleParamsValue(@RequestBody DtoParamsApiPhoneInfo recordInfo) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.saveSampleParamsValue(recordInfo.getSampleParamsPhone());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 删除采样单签名
     *
     * @param sampleParamsPhone 送样单信息
     * @return true
     */
    @ApiOperation(value = "删除采样单签名", notes = "删除采样单签名")
    @PostMapping("/deleteSigneraturePhone")
    public RestResponse<Boolean> deleteSigneraturePhone(@RequestBody DtoSampleParamsApiPhone sampleParamsPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        documentService.logicDeleteById(sampleParamsPhone.getFileIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 获取最大周期和最大次数
     *
     * @param folder 参数
     * @return 获取最大周期和最大次数
     */
    @ApiOperation(value = "获取最大周期和最大次数", notes = "获取最大周期和最大次数")
    @PostMapping("/maxCount")
    public RestResponse<Map<String, Object>> getMaxCount(@RequestBody DtoFolderInfo folder) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        Map<String, Object> map = service.getMaxCount(folder.getProjectId());
        restResp.setData(map);
        restResp.setRestStatus(StringUtil.isNotNull(map) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 获取样品类型树结构
     *
     * @return 样品类型小类
     */
    @ApiOperation(value = "获取样品类型树结构", notes = "获取样品类型树结构")
    @PostMapping("/sampleTypeList")
    public RestResponse<List<TreeNode>> getSampleTypeList() {
        RestResponse<List<TreeNode>> restResp = new RestResponse<>();
        List<TreeNode> map = service.getSampleTypeList();
        restResp.setData(map);
        restResp.setRestStatus(StringUtil.isNotNull(map) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 创建样品编号
     *
     * @return 创建样品编号
     */
    @ApiOperation(value = "创建样品编号", notes = "创建样品编号")
    @PostMapping("/sampleLabel")
    public RestResponse<Object> generate(@RequestBody Map<String, Object> map) {
        RestResponse<Object> restResponse = new RestResponse<>();
        restResponse.setData(service.createSampleLabelData(map));
        return restResponse;
    }

    /**
     * 判断开关是否开启
     *
     * @return 是否开启
     */
    @ApiOperation(value = "判断开关是否开启", notes = "判断开关是否开启")
    @PostMapping("/switchIsOpen")
    public RestResponse<DtoJudgmentPhone> switchIsOpen() {
        RestResponse<DtoJudgmentPhone> restResponse = new RestResponse<>();
        DtoJudgmentPhone data = service.switchIsOpen();
        restResponse.setData(data);
        restResponse.setRestStatus(StringUtil.isNull(data) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 权限验证
     *
     * @return 返回是否具有权限
     */
    @ApiOperation(value = "权限验证", notes = "权限验证")
    @GetMapping("/validateAuth")
    public RestResponse<Boolean> validateAuth(@RequestParam("userId") String userId,
                                              @RequestParam("authCode") String authCode) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.validateAuth(userId, authCode));
        return restResponse;
    }

    /**
     * 样品复制
     *
     * @param sampleCopy 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "样品复制", notes = "样品复制")
    @PostMapping(path = "/copySample")
    public RestResponse<Boolean> copySample(@RequestBody DtoSampleCopy sampleCopy) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.copySample(sampleCopy);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 监测数据详情保存
     *
     * @param sampleDataPhone 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "监测数据详情保存", notes = "监测数据详情保存")
    @PostMapping(path = "/saveSampleDataPhone")
    public RestResponse<Void> saveSampleDataPhone(@RequestBody DtoSampleDataPhone sampleDataPhone) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.saveSampleDataPhone(sampleDataPhone);
        return restResponse;
    }

    /**
     * 地图底图是否使用天地图
     * true 是，false 否
     *
     * @return 返回判断
     */
    @ApiOperation(value = "地图底图是否使用天地图", notes = "地图底图是否使用天地图")
    @GetMapping("/worldEarth")
    public RestResponse<Boolean> isWorldEarth() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.isWorldEarth());
        return restResponse;
    }


    /**
     * 采样点位附件上传
     *
     * @param request 请求体
     * @return
     */
    @ApiOperation(value = "采样点位附件上传", notes = "采样点位附件上传")
    @PostMapping("/uploadFolderFile")
    public RestResponse<List<DtoDocument>> fileUpload(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.uploadFolderFile(request));
        return restResponse;
    }


    /**
     * 移动端采样点位附件查询(根据点位类型分组)
     *
     * @param documentCriteria 请求体
     * @return
     */
    @ApiOperation(value = "移动端采样点位附件查询(根据点位类型分组)", notes = "移动端采样点位附件查询(根据点位类型分组)")
    @GetMapping("/folderFile")
    public RestResponse<Map<String, List<DtoDocument>>> getFolderFile(DocumentCriteria documentCriteria) {
        RestResponse<Map<String, List<DtoDocument>>> restResponse = new RestResponse<>();
        PageBean<DtoDocument> page = super.getPageBean();
        restResponse.setData(service.getFolderFile(page, documentCriteria));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 移动端添加现场质控样
     *
     * @param maps 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "移动端添加现场质控样", notes = "移动端添加现场质控样")
    @PostMapping(path = "/addXCSamplePhone")
    public RestResponse<List<Map<String, Object>>> addXcQcSample(@RequestBody List<Map<String, Object>> maps) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        service.addXCSamplePhone(maps);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 移动端获取标样信息
     *
     * @param criteria 查询条件
     * @return RestResponse<List < DtoConsumable>>
     */
    @ApiOperation(value = "移动端获取标样信息", notes = "移动端获取标样信息")
    @GetMapping("/standardInfo")
    public RestResponse<List<DtoConsumable>> findByPage(ConsumableCriteria criteria) {
        RestResponse<List<DtoConsumable>> restResp = new RestResponse<>();
        PageBean<DtoConsumable> page = super.getPageBean();
        criteria.setIsStandard(true);
        criteria.setExpireStatus("0");
        criteria.setIsLabEncryption(false);
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        service.findStandardByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 移动端获取量纲信息
     *
     * @param criteria 查询条件
     * @return RestResponse<List < DtoDimension>>
     */
    @ApiOperation(value = "移动端获取量纲信息", notes = "移动端获取量纲信息")
    @GetMapping("/dimensionInfo")
    public RestResponse<List<DtoDimension>> findByPage(DimensionCriteria criteria) {
        RestResponse<List<DtoDimension>> restResp = new RestResponse<>();
        PageBean<DtoDimension> page = super.getPageBean();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        service.findDimensionByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }


    /**
     * 移动删除现场质控样
     *
     * @param anaId 现场质控数据id
     * @return
     */
    @ApiOperation(value = "移动删除质控样", notes = "移动删除质控样")
    @DeleteMapping("/removeQCData/{anaId}")
    public RestResponse<String> deleteBySubRecord(@PathVariable String anaId) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.removeQCDataById(anaId);
        return restResponse;
    }

    /**
     * 移动端签到范围
     *
     * @return 返回判断
     */
    @ApiOperation(value = "移动端签到范围", notes = "移动端签到范围")
    @GetMapping("/signRange")
    public RestResponse<String> getSignRange() {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.getSignRange());
        return restResponse;
    }
}
