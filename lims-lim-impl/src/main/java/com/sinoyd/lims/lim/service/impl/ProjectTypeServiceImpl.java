package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.repository.rcc.ProjectTypeRepository;
import com.sinoyd.lims.lim.repository.rcc.SerialIdentifierConfigRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import io.swagger.annotations.Authorization;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 项目类型操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/23
 * @since V100R001
 */
@Service
@Slf4j
public class ProjectTypeServiceImpl extends BaseJpaServiceImpl<DtoProjectType, String, ProjectTypeRepository> implements ProjectTypeService {

    private SerialIdentifierConfigRepository serialIdentifierConfigRepository;

    /**
     * 根据项目类型id获取项目类型
     *
     * @param ids 项目类型ids
     * @return 返回项目类型
     */
    @Override
    public List<DtoProjectType> findRedisByIds(List<String> ids) {
        return findAll(ids);
    }

    /**
     * 新增项目类型
     */
    @Transactional
    @Override
    public DtoProjectType save(DtoProjectType entity) {
        Integer count = repository.countByName(entity.getName());
        if (count > 0) {
            throw new BaseException("已存在相同名称的项目类型！");
        }
        return super.save(entity);
    }

    /**
     * 更新项目类型
     */
    @Transactional
    @Override
    public DtoProjectType update(DtoProjectType entity) {
        Integer count = repository.countByNameAndIdNot(entity.getName(), entity.getId());
        if (count > 0) {
            throw new BaseException("已存在相同名称的项目类型！");
        }
        if (StringUtil.isNotNull(entity.getConfigObject())) {
            entity.setConfig(JsonStream.serialize(entity.getConfigObject()));
        }

        return super.update(entity);
    }

    /**
     * 删除项目类型，需级联删除
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);

        //待删除的项目类型id数组
        List<String> delIds = new ArrayList<>();

        //父类项目类型id数组
        List<String> parentIds = new ArrayList<>();

        delIds.add(idStr);
        parentIds.add(idStr);

        //当父类项目类型id数组存在元素时进行查询对应子类的项目类型，并将查询后的子类项目类型id插入待删除的数组，以及将子类的项目类型id数组转为下一次的父类数组
        while (parentIds.size() > 0) {
            List<DtoProjectType> childProjectTypes = repository.findByParentIdIn(parentIds);
            parentIds.clear();
            for (DtoProjectType projectType : childProjectTypes) {
                delIds.add(projectType.getId());
                parentIds.add(projectType.getId());
            }
        }
        return super.logicDeleteById(delIds);
    }

    @Override
    public void findByPage(PageBean<DtoProjectType> pb, BaseCriteria projectTypeCriteria) {
        pb.setEntityName("DtoProjectType a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, projectTypeCriteria);
    }

    /**
     * 根据编码获取项目类型
     *
     * @param code   编码
     * @param values 值
     */
    @Override
    public List<Map<String, Object>> getProjectTypeByCode(String code, String[] values) {
        // 按照排序值倒序排序
        List<String> valueList = Arrays.asList(values);
        List<DtoProjectType> projectTypeList = repository.findOrderNumDesc();
        projectTypeList = projectTypeList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getConfig()) &&
                valueList.contains(JsonIterator.deserialize(p.getConfig(), Map.class).get(code)))
                .collect(Collectors.toList());

        List<Map<String, Object>> projectTypeMapList = new ArrayList<Map<String, Object>>();
        for (DtoProjectType projectType : projectTypeList) {
            Map<String, Object> projectTypeMap = new HashMap<String, Object>();
            projectTypeMap.put("id", projectType.getId());
            projectTypeMap.put("name", projectType.getName());

            Map configMap = JsonIterator.deserialize(projectType.getConfig(), Map.class);

            for (Object key : configMap.keySet()) {
                projectTypeMap.put(String.valueOf(key), configMap.get(key));
            }

            projectTypeMapList.add(projectTypeMap);
        }
        return projectTypeMapList;
    }

    /**
     * 项目编号配置根据编码获取项目类型
     *
     * @param code   编码
     * @param values 值
     */
    @Override
    public List<Map<String, Object>> getProjectTypeForSerialIdentifierConfig(String code, String[] values, String[] projectTypeIds, Integer configType, Integer qcType, Integer qcGrade) {
        List<Map<String, Object>> projectTypeMapList = getProjectTypeByCode(code, values);
        //过滤掉已经配置过项目编号的项目类型
        List<String> projectTypeIdList = projectTypeMapList.stream().map(p -> p.get("id").toString()).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(projectTypeIdList)) {
            List<DtoSerialIdentifierConfig> identifierConfigList = serialIdentifierConfigRepository.findByConfigTypeAndQcTypeAndQcGrade(configType, qcType, qcGrade);
            if (StringUtil.isNotEmpty(identifierConfigList)) {
                Set<String> existTypeIdSet = new HashSet<>();
                for (DtoSerialIdentifierConfig config : identifierConfigList) {
                    if (StringUtil.isNotEmpty(config.getProjectTypeId()) && !UUIDHelper.GUID_EMPTY.equals(config.getProjectTypeId())) {
                        String[] ids = config.getProjectTypeId().split(",");
                        Collections.addAll(existTypeIdSet, ids);
                    }
                }
                List<String> fltProjectTypeIdList = projectTypeIdList.stream().filter(p -> !existTypeIdSet.contains(p)).collect(Collectors.toList());
                //加上已选择的项目类型id
                List<String> selectIdList = new ArrayList<>(Arrays.asList(projectTypeIds));
                selectIdList = selectIdList.stream().filter(p -> StringUtil.isNotEmpty(p)).collect(Collectors.toList());
                fltProjectTypeIdList.addAll(selectIdList);
                projectTypeMapList = projectTypeMapList.stream().filter(p -> fltProjectTypeIdList.contains(p.get("id").toString())).collect(Collectors.toList());
            }
        }
        return projectTypeMapList;
    }

    /**
     * 获取项目类型对应编码值
     *
     * @param projectType 项目类型
     * @param code        编码
     */
    @Override
    public String getConfigValue(String projectType, String code) {
        DtoProjectType projType = this.findOne(projectType);
        if (StringUtils.isNotNullAndEmpty(projType.getConfig())) {
            Object value = JsonIterator.deserialize(projType.getConfig(), Map.class).get(code);
            return StringUtil.isNotNull(value) ? String.valueOf(value) : null;
        }
        return null;
    }

    /**
     * 获取项目类型对应编码值
     *
     * @param typeIds 项目类型
     * @param code    编码
     */
    @Override
    public Map<String, String> getConfigValueByIds(Collection<String> typeIds, String code) {
        List<DtoProjectType> typeList = this.findAll(typeIds);
        Map<String, String> typeConfigList = new HashMap<>();
        typeList.forEach(p -> {
            if (StringUtils.isNotNullAndEmpty(p.getConfig())) {
                Object value = JsonIterator.deserialize(p.getConfig(), Map.class).get(code);
                String config = StringUtil.isNotNull(value) ? String.valueOf(value) : null;
                typeConfigList.put(p.getId(), config);
            }
        });
        return typeConfigList;
    }

    /**
     * 获取项目类型树
     */
    @Override
    public List<TreeNode> getProjectTree() {
        List<DtoProjectType> projectTypeList = repository.findOrderNumDesc();
        // 转化为treeNode集合
        return convertProjectTypeToTreeNodes(projectTypeList);
    }

    /**
     * 转化项目类型为树结构
     *
     * @param projectTypeList 项目类型集合
     * @return 树结构集合
     */
    private List<TreeNode> convertProjectTypeToTreeNodes(List<DtoProjectType> projectTypeList) {

        //将项目类型list转为TreeNode list
        List<TreeNode> nodeList = new ArrayList<>();
        for (DtoProjectType projectType : projectTypeList) {
            TreeNode node = new TreeNode();
            node.setId(projectType.getId());
            node.setParentId(projectType.getParentId());
            node.setLabel(projectType.getName());
            node.setType(projectType.getWorkflowId());
            node.setOrderNum(projectType.getOrderNum());
            node.setExtent1(projectType.getMark());
            node.setIsLeaf(false);
            node.setChildren(new ArrayList<>());
            nodeList.add(node);
        }

        //将list转为map
        Map<String, TreeNode> projectTypeMap = nodeList.stream().collect(Collectors.toMap(TreeNode::getId, node -> node));

        //清洗projectTypeMap，若parentId不在map中，则说明该数据为脏数据，需移除
        List<String> delIds = new ArrayList<>();
        Boolean flag = true;
        while (flag) {//因为出现脏数据的可能性很小，所以这里仅做代码健壮考虑添加清洗的flag，不考虑性能
            for (String id : projectTypeMap.keySet()) {
                String parentId = projectTypeMap.get(id).getParentId();
                if (!projectTypeMap.containsKey(parentId) &&
                        !parentId.equals(UUIDHelper.GUID_EMPTY)) {
                    delIds.add(id);
                }
            }
            for (String delId : delIds) {
                projectTypeMap.remove(delId);
            }
            flag = delIds.size() > 0;
            delIds.clear();
        }

        //父类id与子类list的Map
        Map<String, List<String>> parentMap = new HashMap<>();
        nodeList.stream().collect(Collectors.groupingBy(TreeNode::getParentId, Collectors.toList())).forEach((parentId, childList) -> {
            List<String> ids = childList.stream().map(TreeNode::getId).collect(Collectors.toList());
            parentMap.put(parentId, ids);
        });

        //叶子项目类型id数组
        List<String> leafIds = new ArrayList<>();
        //父类项目类型id数组
        HashSet<String> parentIds = new HashSet<>();
        for (String id : projectTypeMap.keySet()) {//对所有项目类型遍历，不在父类Map中的便是子类项目类型，并将这些子类对应的父类项目类型id添加到对应数组中
            if (!parentMap.containsKey(id)) {
                String parentId = projectTypeMap.get(id).getParentId();
                projectTypeMap.get(id).setIsLeaf(true);
                if (!parentId.equals(UUIDHelper.GUID_EMPTY)) {//空id不添加
                    leafIds.add(id);
                    parentIds.add(parentId);
                }
            }
        }

        while (parentIds.size() > 0) {//每次的父类项目id需将children进行赋值，每次的子类项目id需移除map。把这次的父类当成下次的子类，把这次的父类的父类当成下次的父类
            for (String id : parentIds) {
                List<String> childIds = new ArrayList<>(parentMap.get(id));
                childIds.retainAll(leafIds);
                for (String childId : childIds) {
                    TreeNode childNode = projectTypeMap.get(childId);
                    projectTypeMap.get(id).getChildren().add(childNode);
                }
            }
            leafIds.removeAll(parentIds);
            for (String id : leafIds) {
                projectTypeMap.remove(id);
            }

            List<String> ids = new ArrayList<>(parentIds);
            leafIds.clear();
            parentIds.clear();
            for (String id : ids) {
                String parentId = projectTypeMap.get(id).getParentId();
                if (!parentId.equals(UUIDHelper.GUID_EMPTY)) {
                    leafIds.add(id);
                    parentIds.add(parentId);
                }
            }
        }

        List<TreeNode> treeNodes = new ArrayList<>();
        if (parentMap.containsKey(UUIDHelper.GUID_EMPTY)) {
            for (String id : parentMap.get(UUIDHelper.GUID_EMPTY)) {
                treeNodes.add(projectTypeMap.get(id));
            }
        }
        return treeNodes;
    }

    /**
     * 根据工作流id获取项目类型
     *
     * @param workflowId 工作流id
     * @return 对应工作流id的项目类型
     */
    @Override
    public List<DtoProjectType> findByWorkflowId(String workflowId) {
        return repository.findByWorkflowId(workflowId);
    }

    @Override
    public List<DtoProjectType> findByTypeCode(String code) {
        List<DtoProjectType> dtoProjectTypes = repository.findAll();
        List<DtoProjectType> projectTypes = dtoProjectTypes.parallelStream()
                .filter(item -> StringUtil.isNotEmpty(item.getConfig())
                        && code.equals(JsonIterator.deserialize(item.getConfig(), Map.class).get("LIM_ProjectTypeCode_IND")))
                .collect(Collectors.toList());
        return projectTypes;
    }

    /**
     * 根据项目类型id获取项目类型列表
     *
     * @param typeId 项目类型id
     * @return 实体列表
     */
    @Override
    public List<DtoProjectType> findByTypeId(String typeId) {
        DtoProjectType projectType = repository.findOne(typeId);
        List<DtoProjectType> dtoProjectTypes = repository.findByParentIdIn(Collections.singletonList(projectType.getParentId()));
        return dtoProjectTypes;
    }

    @Override
    public List<TreeNode> getProjectTreeByCode(String code, String[] values) {
        // 按照排序值倒序排序
        List<String> valueList = Arrays.asList(values);
        List<DtoProjectType> projectTypeList = repository.findOrderNumDesc();
        List<DtoProjectType> childrenList = projectTypeList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getConfig()) &&
                valueList.contains(JsonIterator.deserialize(p.getConfig(), Map.class).get(code)))
                .collect(Collectors.toList());
        List<String> parentIds = childrenList.stream().map(DtoProjectType::getParentId).distinct().collect(Collectors.toList());
        childrenList.addAll(projectTypeList.stream().filter(p -> parentIds.contains(p.getId())).collect(Collectors.toList()));
        childrenList = childrenList.stream().distinct().sorted(Comparator.comparing(DtoProjectType::getOrderNum).reversed()).collect(Collectors.toList());
        return convertProjectTypeToTreeNodes(childrenList);
    }

    @Autowired
    public void setSerialIdentifierConfigRepository(SerialIdentifierConfigRepository serialIdentifierConfigRepository) {
        this.serialIdentifierConfigRepository = serialIdentifierConfigRepository;
    }
}