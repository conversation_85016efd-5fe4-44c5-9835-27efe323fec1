package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 测试项目管理查询条件
 *
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestCriteria extends BaseCriteria {

    /**
     * 关键字（分析项目名称、分析项目方法、标准编号、测试项目编码，支持分析项目和分析方法拼音检索）
     */
    private String key;


    /**
     * 关键字（分析项目名称、分析项目拼音检索）
     */
    private String analyzeItemKey;

    /**
     * 关键字（分析方法相关关键字检索）
     */
    private String analyzeMethodKey;

    private String industryTypeId;

    private String sampleTypeId;

    /**
     * 小类样品类型id
     */
    private String smallSampleTypeId;

    /**
     * 分析项目id
     */
    private String analyzeItemId;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    private Boolean isCompleteField;

    private Boolean isOutsourcing;

    private Boolean isSamplingOut;

    private List<String> sampleTypeIds;

    public void setSampleTypeIds(List<String> sampleTypeIds) {
        this.sampleTypeIds = sampleTypeIds;
    }

    /**
     * 质控限制配置状态（-1 所有 1 已配置 2 未配置）
     */
    private Integer qcRangeStatus;

    /**
     * 是否显示默认的配置人员
     */
    private Boolean isShowDefaultPerson;

    /**
     * A/B岗默认人员标识
     */
    private String defaultPersonId;

    /**
     * 是否显示总称
     */
    private Boolean isShowParent = true;

    /**
     * 是否只显示总称
     */
    private Boolean isTotal = false;

    /**
     * 总称id
     */
    private String totalTestId;

    /**
     * 是否显示假删数据
     */
    private Boolean isShowDeleted = false;

    /**
     * 检测模板id （LIMS5.1新增）
     */
    private String testTemplate;

    /**
     * 需要排除的测试项目id (LIMS5.1新增)
     */
    private List<String> excludeIds;

    /**
     * 是否过滤已选择的测试项目
     */
    private Boolean isFilterSelected = false;

    /**
     * 是否过滤掉已配置测试岗位的测试项目
     */
    private Boolean filterTestPost = false;

    /**
     * 测试资质（0:非认可认证，1:认证，2:认可，4:认证认可）
     */
    private Integer cert;

    /**
     * 包含的测试项目id
     */
    private List<String> includeIds;

    /**
     * 检测类型范围集合，通过行业类型查询时，该范围是该行业类型下的全部检测类型id
     */
    private List<String> sampleTypeIdsUnderIndustry;

    /**
     * 验证状态 0未验证  1 已验证
     */
    private Integer validate;

    /**
     * 测试项目ids
     */
    private List<String> ids;

    /**
     * 排序值，post请求时使用
     */
    private String sort;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 点位ids
     */
    private List<String> sampleFolderIds;

    @Override
    public String getCondition() {
        // 清除条件数据
        values.clear();
        StringBuilder condition = new StringBuilder();
        //仅能选择方法状态为“启用”的测试项目
        condition.append(" and isAbolish = 0 ");
        //要进行假删过滤
        if (!(StringUtil.isNotNull(isShowDeleted) && isShowDeleted)) {
            condition.append(" and isDeleted = 0 ");
        }

        //监测类型
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            condition.append(" and sampleTypeId = :sampleTypeId ");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        //监测类型
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            condition.append(" and sampleTypeId in :sampleTypeIds ");
            values.put("sampleTypeIds", this.sampleTypeIds);
        }
        //行业类型
        if (StringUtils.isNotNullAndEmpty(this.industryTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.industryTypeId)) {
            if (StringUtils.isNotNullAndEmpty(this.sampleTypeIdsUnderIndustry)) {
                condition.append(" and sampleTypeId in :sampleTypeIdsUnderIndustry ");
                values.put("sampleTypeIdsUnderIndustry", this.sampleTypeIdsUnderIndustry);
            } else {
                condition.append(" and  1 = 2 ");
            }
        }
        //分析项目id
        if (StringUtils.isNotNullAndEmpty(this.analyzeItemId) && !UUIDHelper.GUID_EMPTY.equals(this.analyzeItemId)) {
            condition.append(" and analyzeItemId = :analyzeItemId ");
            values.put("analyzeItemId", this.analyzeItemId);
        }
        //分析方法id
        if (StringUtils.isNotNullAndEmpty(this.analyzeMethodId) && !UUIDHelper.GUID_EMPTY.equals(this.analyzeMethodId)) {
            condition.append(" and analyzeMethodId = :analyzeMethodId ");
            values.put("analyzeMethodId", this.analyzeMethodId);
        }
        //是否现场
        if (StringUtil.isNotNull(this.isCompleteField)) {
            condition.append(" and isCompleteField = :isCompleteField ");
            values.put("isCompleteField", this.isCompleteField);
        }
        //是否采测分包
        if (StringUtil.isNotNull(this.isOutsourcing)) {
            condition.append(" and isOutsourcing = :isOutsourcing ");
            values.put("isOutsourcing", this.isOutsourcing);
        }
        //是否分析分包
        if (StringUtil.isNotNull(this.isSamplingOut)) {
            condition.append(" and isSamplingOut = :isSamplingOut ");
            values.put("isSamplingOut", this.isSamplingOut);
        }
        //不显示总称
        if (!isShowParent) {
            condition.append(" and isTotalTest = :isTotalTest ");
            values.put("isTotalTest", false);
        }
        if (this.isTotal) {
            condition.append(" and parentId = :isTotal ");
            values.put("isTotal", UUIDHelper.GUID_EMPTY);
        }
        //总称测试项目id
        if (StringUtils.isNotNullAndEmpty(this.totalTestId) && !UUIDHelper.GUID_EMPTY.equals(this.totalTestId)) {
            condition.append(" and parentId = :totalTestId ");
            values.put("totalTestId", this.totalTestId);
        }

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and ( redAnalyzeMethodName like :key or redCountryStandard like :key or redAnalyzeItemName like :key or fullPinYin like :key or pinYin like :key or testCode like :key)");
            values.put("key", "%" + this.key + "%");
        }

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.analyzeMethodKey)) {
            condition.append(" and ( redAnalyzeMethodName like :analyzeMethodKey or redCountryStandard like :analyzeMethodKey)");
            values.put("analyzeMethodKey", "%" + this.analyzeMethodKey + "%");
        }

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.analyzeItemKey)) {

            if (isTotal) {
                condition.append(" and ((redAnalyzeItemName like :analyzeItemKey or fullPinYin like :analyzeItemKey or pinYin like :analyzeItemKey) ");
                condition.append(" or id in (select parentId from DtoTest t1 where t1.parentId = x.id and (t1.redAnalyzeItemName like :analyzeItemKey  or t1.fullPinYin like :analyzeItemKey or t1.pinYin like :analyzeItemKey)))");
            } else {
                condition.append(" and (redAnalyzeItemName like :analyzeItemKey or fullPinYin like :analyzeItemKey or pinYin like :analyzeItemKey)");
            }

            values.put("analyzeItemKey", "%" + this.analyzeItemKey + "%");
        }

        if (StringUtil.isNotEmpty(this.includeIds)) {
            condition.append(" and id in :includeIds ");
            values.put("includeIds", this.includeIds);
        }

        //检测模板查询 (LIMS5.1新增)
        if (StringUtil.isNotEmpty(testTemplate) && !UUIDHelper.GUID_EMPTY.equals(this.testTemplate)) {
            condition.append(" and id in (select testId from DtoSampleType2Test where sampleTypeId = :testTemplate)");
            values.put("testTemplate", this.testTemplate);
        }

        //排除一些测试项目 ((LIMS5.1新增))
        if (StringUtil.isNotEmpty(excludeIds)) {
            condition.append(" and id not in :excludeIds ");
            values.put("excludeIds", excludeIds);
        }

        //过滤掉已配置测试岗位的测试项目
        if (StringUtil.isNotNull(filterTestPost) && filterTestPost) {
            condition.append(" and id not in (select distinct testId from DtoTestPost2Test)");
        }

        //测试资质
        if (StringUtils.isNotNullAndEmpty(this.cert)) {
            condition.append(" and cert = :cert");
            values.put("cert", this.cert);
        }

        if (StringUtils.isNotNullAndEmpty(validate)) {
            if (EnumLIM.EnumCodeValidate.已验证.getValue().equals(validate)) {
                condition.append(" and (validate =  :validate or usageNum > 0)");
            } else {
                condition.append(" and validate =  :validate and (usageNum = null or usageNum = 0)");
            }
            values.put("validate", this.validate);
        }

        if (StringUtil.isNotEmpty(ids)) {
            condition.append(" and id in :ids ");
            values.put("ids", this.ids);
        }

        if (StringUtil.isNotEmpty(defaultPersonId)) {
            condition.append(" and exists(select 1 from DtoPerson2Test p2t where x.sampleTypeId = p2t.sampleTypeId and p2t.personId = :defaultPersonId " +
                    " and x.id = p2t.testId and (p2t.isDefaultPerson = 1 or p2t.isDefaultAuditPerson =1 )) ");
            values.put("defaultPersonId", this.defaultPersonId);
        }

        return condition.toString();
    }
}