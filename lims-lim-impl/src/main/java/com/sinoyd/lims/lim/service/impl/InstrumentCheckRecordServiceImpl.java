package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.InstrumentCheckRecordRepository;
import com.sinoyd.lims.lim.service.InstrumentCheckRecordService;
import com.sinoyd.lims.lim.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器检定校准接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Service
public class InstrumentCheckRecordServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentCheckRecord, String, InstrumentCheckRecordRepository>
        implements InstrumentCheckRecordService {

    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    private InstrumentRepository instrumentRepository;

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean pageBean, BaseCriteria criteria) {
        // 多表关联查询返回自定义字段
        pageBean.setEntityName("DtoInstrumentCheckRecord a,DtoInstrument b");
        pageBean.setSelect("select a,b.instrumentName,b.model,b.instrumentsCode,b.serialNo");

        super.findByPage(pageBean, criteria);

        List<DtoInstrumentCheckRecord> list = pageBean.getData();
        List<DtoInstrumentCheckRecord> insList = new ArrayList<>();

        Iterator<DtoInstrumentCheckRecord> dataList = list.iterator();
        // 循环迭代获取JPQL中查询返回的属性
        while (dataList.hasNext()) {
            Object obj = dataList.next();
            Object[] array = (Object[]) obj;
            DtoInstrumentCheckRecord ins = (DtoInstrumentCheckRecord) array[0]; // 如果是实体这么写
            ins.setInstrumentName(String.valueOf(array[1])); // 仪器名称
            ins.setModel(String.valueOf(array[2])); // 规格类型
            ins.setInstrumentsCode(String.valueOf(array[3])); // 仪器编号
            ins.setSerialNo(String.valueOf(array[4]));// 出厂编号
            insList.add(ins);
        }
        List<String> checkPersonIdList = insList.stream().map(DtoInstrumentCheckRecord::getCheckPersonId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList =  StringUtil.isNotEmpty(checkPersonIdList) ? personService.findAll(checkPersonIdList) : new ArrayList<>();
        for (DtoInstrumentCheckRecord instrumentCheckRecord : insList){
            Optional<DtoPerson> person = personList.stream().filter(p->p.getId().equals(instrumentCheckRecord.getCheckPersonId())).findFirst();
            person.ifPresent(dtoPerson -> instrumentCheckRecord.setCheckPerson(dtoPerson.getCName()));
        }
        pageBean.setData((insList));
    }

    /**
     * 保存数据
     */
    @Transactional
    @Override
    public DtoInstrumentCheckRecord save(DtoInstrumentCheckRecord entity) {

        // 仪器对象
        DtoInstrument insModel = instrumentService.findOne(entity.getInstrumentId());
        updateInstrument(insModel, entity);
        return super.save(entity);
    }

    /**
     * 更新数据
     */
    @Transactional
    @Override
    public DtoInstrumentCheckRecord update(DtoInstrumentCheckRecord entity) {

        // 仪器对象
        DtoInstrument insModel = instrumentService.findOne(entity.getInstrumentId());
        updateInstrument(insModel, entity);
        return super.update(entity);
    }

    /**
     * 批量保存
     */
    @Transactional
    @Override
    public List<DtoInstrumentCheckRecord> save(Collection<DtoInstrumentCheckRecord> entities) {

        List<String> instrumentIds = entities.stream().map(DtoInstrumentCheckRecord::getInstrumentId).distinct().collect(Collectors.toList());

        List<DtoInstrument> instruments = instrumentService.findAll(instrumentIds);

        for (DtoInstrumentCheckRecord entity : entities) {
            String instrumentId = entity.getInstrumentId();
            Optional<DtoInstrument> optionalDtoInstrument = instruments.stream().filter(p -> p.getId().equals(instrumentId)).findFirst();
            if (StringUtil.isNotNull(optionalDtoInstrument) && optionalDtoInstrument.isPresent()) {
                DtoInstrument insModel = optionalDtoInstrument.get();
                updateInstrument(insModel, entity);
            }
        }

        return super.save(entities);
    }


    /**
     * 更新仪器信息
     *
     * @param insModel 仪器信息
     * @param entity   实体信息
     */
    @Transactional
    protected void updateInstrument(DtoInstrument insModel, DtoInstrumentCheckRecord entity) {
        if (StringUtil.isNotNull(entity.getCheckTime()) && entity.getCheckEndDate().compareTo(insModel.getOriginEndDate()) > 0){
            insModel.setOriginEndDate(entity.getCheckEndDate());//有效期
            if (entity.getCheckEndDate().compareTo(new Date()) >= 0) {
                insModel.setState(EnumBase.EnumInstrumentStatus.正常.getValue());
            }
        }
        insModel.setOriginType(entity.getOriginType()); // 溯源方式
        insModel.setOriginUnit(entity.getCheckDeptName()); // 溯源单位
        insModel.setOriginCyc(entity.getOriginCyc()); // 溯源周期
        insModel.setOriginResult(entity.getCheckResult()); // 溯源结果
        insModel.setOriginDate(entity.getCheckTime());//溯源日期
        insModel.setOriginRemark(entity.getRemark());//备注
        instrumentRepository.save(insModel); // 更新基础信息
    }
}