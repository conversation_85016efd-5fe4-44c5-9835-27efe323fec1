package com.sinoyd.lims.lim.service.sync;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.impl.SyncDataServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 测试项目拓展同步业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/3/14
 */
@Service
public class SampleTypeSyncServiceImpl extends SyncDataServiceImpl<DtoSampleType, String, SampleTypeRepository> {
    @Override
    public boolean needPrintMessage() {
        return true;
    }

    @Override
    public List<String> getPrintFieldNames() {
        return Collections.singletonList("typeName");
    }

    @Override
    public List<String> getUnSyncFields() {
        List<String> fields = super.getUnSyncFields();
        //默认标签分组
        fields.add("defaultLabelGroupId");
        //现场任务分组
        fields.add("fieldTaskGroupId");
        //是否开启分组标签
        fields.add("isOpenGroupTag");
        //备注
        fields.add("remark");
        return fields;
    }
}
