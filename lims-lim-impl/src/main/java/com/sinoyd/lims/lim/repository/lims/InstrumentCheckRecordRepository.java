package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;

import java.util.Collection;
import java.util.List;

/**
 * 仪器检定校准操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
public interface InstrumentCheckRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentCheckRecord, String> {

    /**
     * 根据仪器id获取检定校准记录
     *
     * @param instrumentIds 仪器id
     */
    List<DtoInstrumentCheckRecord> findByInstrumentIdIn(Collection<String> instrumentIds);

}