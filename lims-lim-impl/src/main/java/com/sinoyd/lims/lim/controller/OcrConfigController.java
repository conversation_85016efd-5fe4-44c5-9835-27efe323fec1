package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.OcrConfigCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoOcrDataContainer;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;
import com.sinoyd.lims.lim.service.OcrConfigService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;

/**
 * ocr对象
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@RestController
@RequestMapping("api/lim/ocrConfig")
@Validated
@Slf4j
public class OcrConfigController extends BaseJpaController<DtoOcrConfig, String, OcrConfigService> {

    /**
     * 分页动态条件查询DtoOcrConfig
     *
     * @param ocrConfigCriteria 条件参数
     * @return RestResponse<List < DtoOcrConfig>>
     */
    @ApiOperation(value = "分页动态条件查询DtoOcrConfig", notes = "分页动态条件查询DtoOcrConfig")
    @GetMapping
    public RestResponse<List<DtoOcrConfig>> findByPage(OcrConfigCriteria ocrConfigCriteria) {
        PageBean<DtoOcrConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoOcrConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, ocrConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增ocr对象
     *
     * @param ocrConfig 实体列表
     * @return RestResponse<DtoOcrConfig>
     */
    @ApiOperation(value = "新增ocr对象", notes = "新增ocr对象")
    @PostMapping
    public RestResponse<DtoOcrConfig> create(@Validated @RequestBody DtoOcrConfig ocrConfig) {
        RestResponse<DtoOcrConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(ocrConfig));
        return restResponse;
    }

    /**
     * 查询ocr对象
     *
     * @param id 标识
     * @return 查询结果
     */
    @ApiOperation(value = "查询ocr对象", notes = "查询ocr对象")
    @GetMapping("/{id}")
    public RestResponse<DtoOcrConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOcrConfig> restResp = new RestResponse<>();
        restResp.setData(service.findOne(id));
        return restResp;
    }

    /**
     * 修改ocr对象
     *
     * @param entity ocr对象
     * @return ocr对象
     */
    @ApiOperation(value = "修改ocr对象", notes = "修改ocr对象")
    @PutMapping
    public RestResponse<DtoOcrConfig> update(@Validated @RequestBody DtoOcrConfig entity) {
        RestResponse<DtoOcrConfig> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.update(entity));
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 批量删除ocr对象
     *
     * @param ids 标识列表
     * @return 删除数量
     */
    @ApiOperation(value = "批量删除ocr对象", notes = "批量删除ocr对象")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * ocr识别接口
     *
     * @param ocrConfigRecord 识别参数
     * @return RestResponse<DtoOcrConfig>
     */
    @ApiOperation(value = "ocr识别接口", notes = "ocr识别接口")
    @PostMapping("/ocrRecognize")
    public RestResponse<DtoOcrDataContainer> ocrRecognize(@RequestBody DtoOcrConfigRecord ocrConfigRecord) {
        RestResponse<DtoOcrDataContainer> restResponse = new RestResponse<>();
        restResponse.setData(service.ocrRecognize(ocrConfigRecord));
        return restResponse;
    }

    /**
     * ocr流式接口
     *
     * @param ocrConfigRecord 识别参数
     * @return RestResponse<DtoOcrConfig>
     */
    @ApiOperation(value = "ocr接口测试", notes = "ocr接口测试")
    @PostMapping("/ocrStream")
    public SseEmitter callStreamApi(@RequestBody DtoOcrConfigRecord ocrConfigRecord) {
        SseEmitter emitter = new SseEmitter(0L); // 不超时
        SecurityContext context = SecurityContextHolder.getContext();
        // 在新线程中执行，避免阻塞主线程
        new Thread(() -> {
            try {
                SecurityContextHolder.setContext(context);
                service.callStreamApi(ocrConfigRecord, data -> {
                    try {
                        // 发送数据到客户端
                        emitter.send(data);
                        log.info("Sent SSE data: {}", data);
                    } catch (IOException e) {
                        log.error("Failed to send SSE data", e);
                        emitter.completeWithError(e);
                    }
                });
                // 完成流式传输
                emitter.complete();
                log.info("SSE stream completed");
            } catch (Exception e) {
                log.error("OCR stream processing failed", e);
                emitter.completeWithError(e);
            } finally {
                // 无论成功或失败，都必须清除上下文
                SecurityContextHolder.clearContext();
                log.debug("子线程安全上下文已清理。");
            }
        }).start();
        return emitter;
    }
}
