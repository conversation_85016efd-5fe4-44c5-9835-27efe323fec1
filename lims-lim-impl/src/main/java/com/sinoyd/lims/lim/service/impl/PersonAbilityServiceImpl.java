package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.PersonAbilityCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.entity.Test;
import com.sinoyd.lims.lim.repository.lims.PersonAbilityRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.PersonAbilityService;
import com.sinoyd.lims.lim.service.PersonCertService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员检测能力
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class PersonAbilityServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPersonAbility, String, PersonAbilityRepository> implements PersonAbilityService {

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private PersonCertService personCertService;

    @Autowired
    @Lazy
    private CodeService codeService;

    private AnalyzeMethodRepository analyzeMethodRepository;

    private AnalyzeItemRepository analyzeItemRepository;

    /**
     * 分页查询人员检测能力列表
     */
    @Override
    public void findByPage(PageBean<DtoPersonAbility> page, BaseCriteria criteria) {
        int pageNum = page.getPageNo();
        int rowsPerPage = page.getRowsPerPage();
        page.setEntityName("DtoPersonAbility p, DtoPersonCert pt");
        page.setSelect("select p");
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        PersonAbilityCriteria personCertCriteria = (PersonAbilityCriteria) criteria;
        DtoCode code = codeService.findByCode("BASE_ExpirationAlertTime_PersonCert");
        if (StringUtil.isNull(code)) {
            throw new BaseException("未配置有效期限，请添加正确的有效期限");
        }
        // 状态不为空时，获取所有数据，并根据人员上岗证过期预警时间进行判断过滤
        String state = personCertCriteria.getState();
        int alertTime = Integer.parseInt(code.getDictValue());

        Calendar expiryDateCalendar = new GregorianCalendar();
        Date date = new Date();
        expiryDateCalendar.setTime(date);
        //当前日期加上预警天数
        expiryDateCalendar.add(Calendar.DATE, alertTime);
        Date alertDate = expiryDateCalendar.getTime();

        if (StringUtil.isNotEmpty(state) && !"-1".equals(state)) {
            Date compareDate = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);
            if (!"2".equals(state)) {
                compareDate = alertDate;
            }
            personCertCriteria.setCompareDate(compareDate);
        }
        super.findByPage(page, personCertCriteria);

        List<DtoPersonAbility> list = page.getData();

        List<String> testIds = list.stream().map(DtoPersonAbility::getTestId).distinct().collect(Collectors.toList());
        List<String> samplingMethodIds = list.stream().map(DtoPersonAbility::getSamplingMethodId).distinct().collect(Collectors.toList());

        // 采样方法集合
        List<DtoAnalyzeMethod> samplingMethodList = StringUtil.isNotEmpty(samplingMethodIds) ?
                analyzeMethodRepository.findAll(samplingMethodIds) : new ArrayList<>();

        //相关的测试项目数据
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findRedisByIds(testIds);
        }
        List<DtoCode> personCertCode = codeService.findCodes(BaseCodeHelper.PersonCertType);

        //相关的检测类型数据
        List<String> sampleTypeIds = testList.stream().map(Test::getSampleTypeId).distinct().collect(Collectors.toList());
        // 采样和其他能力中的检测类型
        sampleTypeIds.addAll(list.stream().map(DtoPersonAbility::getSampleTypeId).distinct().collect(Collectors.toList()));
        List<DtoSampleType> typeList = new ArrayList<>();
        if (sampleTypeIds.size() > 0) {
            typeList = sampleTypeService.findRedisByIds(sampleTypeIds);
        }

        //相关证书信息
        List<String> certIds = list.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getPersonCertId())
                && !p.getPersonCertId().equals(UUIDHelper.GUID_EMPTY)).map(DtoPersonAbility::getPersonCertId).distinct().collect(Collectors.toList());
        List<DtoPersonCert> certList = new ArrayList<>();
        if (certIds.size() > 0) {
            certList = personCertService.findAll();
        }
        List<DtoAnalyzeItem> analyzeItemList = analyzeItemRepository.findAll();
        for (DtoPersonAbility item : list) {
            // 分析能力
            Optional<DtoTest> ap = testList.stream().filter(p -> item.getTestId().contains(p.getId())).findFirst();
            if (ap.isPresent()) {
                DtoTest test = ap.get();
                item.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                item.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                item.setRedCountryStandard(test.getRedCountryStandard());
                item.setIsCompleteField(test.getIsCompleteField());
                item.setCert(test.getCert());
                item.setSampleTypeId(test.getSampleTypeId());
                analyzeItemList.stream().filter(v -> v.getId().equals(test.getAnalyzeItemId()))
                        .findFirst().ifPresent(v -> item.setCasNum(v.getCasNum()));
            }
            // 采样能力
            Optional<DtoAnalyzeMethod> methodOptional = samplingMethodList.stream().filter(p -> item.getSamplingMethodId().equals(p.getId())).findFirst();
            if (methodOptional.isPresent()) {
                DtoAnalyzeMethod analyzeMethod = methodOptional.get();
                item.setRedAnalyzeMethodName(analyzeMethod.getMethodName());
                item.setRedCountryStandard(analyzeMethod.getCountryStandard());
            }
            // 检测类型
            if (StringUtil.isNotEmpty(item.getSampleTypeId()) && !UUIDHelper.GUID_EMPTY.equals(item.getSampleTypeId())) {
                Optional<DtoSampleType> apst = typeList.stream().filter(p -> item.getSampleTypeId().equals(p.getId())).findFirst();
                if (apst.isPresent()) {
                    DtoSampleType type = apst.get();
                    item.setSampleTypeName(type.getTypeName());
                    item.setIcon(type.getIcon());
                }
            }

            // 处理数据状态
            handleDataState(item, alertDate);

            Optional<DtoPersonCert> apct = certList.stream().filter(p -> item.getPersonCertId().contains(p.getId())).findFirst();
            if (apct.isPresent()) {
                DtoPersonCert cert = apct.get();
                item.setPersonCertCode(cert.getCertCode());
                item.setPersonCertName(cert.getCertName());
            }

            personCertCode.stream().filter(p -> p.getDictValue().equals(item.getAbilityType())).findFirst().ifPresent(p -> {
                item.setAbilityTypeName(p.getDictName());
            });
        }
        //检测类型过滤
        if (StringUtil.isNotEmpty(personCertCriteria.getSampleTypeId())) {
            list = list.stream().filter(v -> personCertCriteria.getSampleTypeId().equals(v.getSampleTypeId())).collect(Collectors.toList());
        }
        //分析项目,化学符号
        if (StringUtil.isNotEmpty(personCertCriteria.getAnalyzeItem())) {
            list = list.stream().filter(v -> (StringUtil.isNotEmpty(v.getRedAnalyzeItemName()) && v.getRedAnalyzeItemName().contains(personCertCriteria.getAnalyzeItem()))
                    || (StringUtil.isNotEmpty(v.getCasNum()) && v.getCasNum().contains(personCertCriteria.getAnalyzeItem())))
                    .collect(Collectors.toList());
        }
        //分析方法,标准类型
        if (StringUtil.isNotEmpty(personCertCriteria.getAnalyzeMethod())) {
            list = list.stream().filter(v -> (StringUtil.isNotEmpty(v.getRedAnalyzeMethodName()) && v.getRedAnalyzeMethodName().contains(personCertCriteria.getAnalyzeMethod()))
                    || (StringUtil.isNotEmpty(v.getRedCountryStandard()) && v.getRedCountryStandard().contains(personCertCriteria.getAnalyzeMethod())))
                    .collect(Collectors.toList());
        }
        // 排序先按状态：未过期的、过期的，剩下的按有效期、标准编号分析方法、分析项目顺序
        list.sort(Comparator.comparing(DtoPersonAbility::getOrderNum, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(DtoPersonAbility::getCertEffectiveTime, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoPersonAbility::getRedCountryStandard, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoPersonAbility::getRedAnalyzeMethodName, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoPersonAbility::getRedAnalyzeItemName, Comparator.nullsLast(Comparator.naturalOrder())));
        page.setRowsCount(list.size());
        list = list.stream().skip((long) (pageNum - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
        page.setData(list);// 重新封装到pageBean返回到上层
    }

    /**
     * 新增人员检测能力
     */
    @Override
    @Transactional
    public DtoPersonAbility save(DtoPersonAbility entity) {
        if (entity.getCertEffectiveTime() != null && entity.getCertEffectiveTime().before(entity.getAchieveDate())) {
            throw new BaseException("有效期至不能小于发证日期！");
        }
        repository.deleteByTestIdAndPersonId(entity.getTestId(), entity.getPersonId());

        return super.save(entity);

    }

    @Override
    public List<DtoPersonAbility> save(Collection<DtoPersonAbility> entities) {
        for (DtoPersonAbility entity : entities) {
            if (entity.getCertEffectiveTime() != null && entity.getCertEffectiveTime().before(entity.getAchieveDate())) {
                throw new BaseException("有效期至不能小于发证日期！");
            }
        }
        return super.save(entities);
    }


    /**
     * 批量新增人员检测能力(证书下面已经存在的检测能力不在进行新增,不存在的进行新增)
     *
     * @param personAbilities 人员检测能力实体集合
     * @return
     */
    @Transactional
    @Override
    public String saveList(List<DtoPersonAbility> personAbilities) {
        // 人员检测能力集合非空判断
        if (StringUtil.isEmpty(personAbilities)) {
            return null;
        }
        // 定义要进行保存的人员检测能力集合
        List<DtoPersonAbility> saveDtoPersonAbilities = new ArrayList<>();
        // 定义进行更新的人员检测能力集合
        List<DtoPersonAbility> updatePersonAblities = new ArrayList<>();
        // 查询某个人员下的所有检测能力
        List<DtoPersonAbility> exitPersonAbilities = repository.findByPersonId(personAbilities.get(0).getPersonId());
        // 根据证书进行分组
        Map<String, List<DtoPersonAbility>> personAblitiesMap = personAbilities.stream().collect(Collectors.groupingBy(DtoPersonAbility::getPersonCertId));
        // 已经存在的检测能力分组
        Map<String, List<DtoPersonAbility>> exitPersonAbilitiesMap = exitPersonAbilities.stream().collect(Collectors.groupingBy(DtoPersonAbility::getPersonCertId));
        for (String key : personAblitiesMap.keySet()) {
            // 获取证书下的检测能力集合
            List<DtoPersonAbility> dtoPersonAbilityList = personAblitiesMap.get(key);
            List<DtoPersonAbility> exitPsersonAbilityList = exitPersonAbilitiesMap.get(key);
            // 若此证书数据库没有相关检测能力则直接进行保存
            if (StringUtil.isEmpty(exitPsersonAbilityList)) {
                saveDtoPersonAbilities.addAll(dtoPersonAbilityList);
            } else {
                for (DtoPersonAbility personAbility : dtoPersonAbilityList) {
                    Boolean isSave = true;
                    DtoPersonAbility ability = new DtoPersonAbility();
                    if (personAbility.getAbilityType().equals("5")) {
                        //其他
                        if (StringUtil.isNotEmpty(personAbility.getRedAnalyzeMethodName()) && StringUtil.isNotEmpty(personAbility.getRedAnalyzeItemName())) {
                            List<DtoPersonAbility> abilityList = exitPsersonAbilityList.stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getTestId())
                                    && personAbility.getRedAnalyzeMethodName().equals(p.getRedAnalyzeMethodName())
                                    && personAbility.getRedAnalyzeItemName().equals(p.getRedAnalyzeItemName())).collect(Collectors.toList());
                            if (abilityList.size() > 0) {
                                isSave = false;
                                ability = abilityList.get(0);
                            }
                        }
                    } else if (personAbility.getAbilityType().equals("6")) {
                        //分析
                        List<DtoPersonAbility> abilityList = exitPsersonAbilityList.stream()
                                .filter(p -> p.getTestId().equals(personAbility.getTestId())).collect(Collectors.toList());
                        if (abilityList.size() > 0) {
                            isSave = false;
                            ability = abilityList.get(0);
                        }
                    } else if (personAbility.getAbilityType().equals("7")) {
                        //采样
                        if (StringUtil.isNotEmpty(personAbility.getSamplingMethodId()) && StringUtil.isNotEmpty(personAbility.getRedAnalyzeItemName())) {
                            List<DtoPersonAbility> abilityList = exitPsersonAbilityList.stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getTestId())
                                    && personAbility.getSamplingMethodId().equals(p.getSamplingMethodId())
                                    && personAbility.getRedAnalyzeItemName().equals(p.getRedAnalyzeItemName())).collect(Collectors.toList());
                            if (abilityList.size() > 0) {
                                isSave = false;
                                ability = abilityList.get(0);
                            }
                        }
                    }
                    if (isSave) {
                        saveDtoPersonAbilities.add(personAbility);
                    } else {
                        personAbility.setId(ability.getId());
                        updatePersonAblities.add(personAbility);
                    }
                }
            }
        }
        // 保存新增的人员检测能力
        if (StringUtil.isNotEmpty(saveDtoPersonAbilities)) {
            this.save(saveDtoPersonAbilities);
        }
        // 进行人员检测能力更新
        if (StringUtil.isNotEmpty(updatePersonAblities)) {
            this.save(updatePersonAblities);
            if (StringUtil.isEmpty(saveDtoPersonAbilities)) {
                return "更新成功";
            }
        }
        return "保存成功";
    }

    /**
     * 修改人员检测能力
     */
    @Transactional
    @Override
    public DtoPersonAbility update(DtoPersonAbility entity) {
        // 根据personId查询是否已存在
//        DtoPersonAbility exist = repository.findByPersonIdAndTestId(entity.getPersonId(),entity.getTestId());
//
//        if (StringUtil.isNotNull(exist) && !exist.getId().equals(entity.getId())) {// 不能修改为系统中已存在的人员
//            throw new BaseException("系统已存在相同的人员id:" + exist.getPersonId());
//        }
        if (entity.getCertEffectiveTime().before(entity.getAchieveDate())) {
            throw new BaseException("有效期至不能小于发证日期！");
        }

        return super.update(entity);
    }


    @Override
    @Transactional
    public List<DtoPersonAbility> update(Collection<DtoPersonAbility> entities) {
        for (DtoPersonAbility entity : entities) {
            if (entity.getCertEffectiveTime().before(entity.getAchieveDate())) {
                throw new BaseException("有效期至不能小于发证日期！");
            }
        }
        return super.update(entities);
    }

    /**
     * 查询对应测试项目的人员检测能力
     *
     * @param testId 测试项目id
     * @return 检测能力
     */
    @Override
    public List<DtoPersonAbility> findPersonCertByTestId(String testId) {
        StringBuilder select = new StringBuilder("select pa from DtoPersonAbility pa ,DtoPersonCert pc");
        select.append(" where 1=1 ");
        select.append(" and pa.personCertId = pc.id");
        select.append(" and pa.testId = :testId");
        Map<String, Object> values = new HashMap<>();
        values.put("testId", testId);
        List<DtoPersonAbility> personAbilities = comRepository.find(select.toString(), values);
        return StringUtil.isNotNull(personAbilities) ? personAbilities : new ArrayList<>();
    }

    @Override
    public List<DtoPersonAbility> findByTestIds(Collection<String> testIds) {
        if (StringUtil.isNotNull(testIds) && testIds.size() > 0) {
            return repository.findByTestIdIn(testIds);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void bachUpdateDate(Map<String, Object> map) {
        List<DtoPersonAbility> personAbilities = super.findAll((List<String>) map.get("ids"));
        if (StringUtil.isEmpty(personAbilities)) {
            return;
        }
        String achieveDate = (String) map.get("achieveDate");
        Date achieve = StringUtil.isEmpty(achieveDate) ? DateUtil.stringToDate("1753-01-01", DateUtil.YEAR) : DateUtil.stringToDate(achieveDate, DateUtil.YEAR);
        String certEffectiveTime = (String) map.get("certEffectiveTime");
        Date certEffective = StringUtil.isEmpty(certEffectiveTime) ? DateUtil.stringToDate("1753-01-01", DateUtil.YEAR) : DateUtil.stringToDate(certEffectiveTime, DateUtil.YEAR);
        if (certEffective.before(achieve)) {
            throw new BaseException("有效期至不能小于发证日期！");
        }
        for (DtoPersonAbility personAbility : personAbilities) {
            if (StringUtil.isNotEmpty(achieveDate)) {
                personAbility.setAchieveDate(achieve);
            }
            if (StringUtil.isNotEmpty(certEffectiveTime)) {
                personAbility.setCertEffectiveTime(certEffective);
            }
            if (personAbility.getCertEffectiveTime().before(personAbility.getAchieveDate())) {
                throw new BaseException("有效期至不能小于发证日期！");
            }
        }
        super.save(personAbilities);
    }

    /**
     * 根据预警日期设置状态
     *
     * @param ability   能力实体
     * @param alertDate 预警日期
     * @return
     */
    private void handleDataState(DtoPersonAbility ability, Date alertDate) {
        Date certEffectiveTime = ability.getCertEffectiveTime();
        String status = "即将过期";
        int orderNum = 99;
        // 当前日期
        Date currentDate = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);
        // 大于等于预警日期
        if (alertDate.before(certEffectiveTime) || alertDate.equals(certEffectiveTime)) {
            status = "正常";
            orderNum = 999;
        }
        if (certEffectiveTime.before(currentDate)) {
            status = "已过期";
            orderNum = 9;
        }
        ability.setCertState(status);
        ability.setOrderNum(orderNum);
    }


    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }
}