package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentCheckRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;
import com.sinoyd.lims.lim.service.InstrumentCheckRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器检定校准接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Api(tags = "仪器检定校准")
@Validated
@RestController
@RequestMapping("/api/lim/instrumentCheckRecord")
public class InstrumentCheckRecordController
        extends BaseJpaController<DtoInstrumentCheckRecord, String, InstrumentCheckRecordService> {

    /**
     * 根据id获取仪器检定校准记录
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取检定校准信息", notes = "根据id获取检定校准信息")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentCheckRecord> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoInstrumentCheckRecord> restResp = new RestResponse<>();
        DtoInstrumentCheckRecord checkRecord = service.findOne(id);
        restResp.setData(checkRecord);

        restResp.setRestStatus(StringUtil.isNull(checkRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询检定校准信息
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询检定校准信息", notes = "分页动态条件查询检定校准信息")
    @GetMapping
    public RestResponse<List<DtoInstrumentCheckRecord>> findByPage(InstrumentCheckRecordCriteria criteria) {

        RestResponse<List<DtoInstrumentCheckRecord>> restResp = new RestResponse<>();

        PageBean<DtoInstrumentCheckRecord> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增单个检定校准记录
     * @param entity 集合
     * @return 返回新增记录
     */
    @ApiOperation(value = "新增单个检定校准记录", notes = "新增单个检定校准记录")
    @PostMapping
    public RestResponse<DtoInstrumentCheckRecord> save(@Validated @RequestBody DtoInstrumentCheckRecord entity) {

        RestResponse<DtoInstrumentCheckRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentCheckRecord data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 批量新增检定校准记录
     * @param entities 集合
     * @return 返回新增记录
     */
    @ApiOperation(value = "批量新增检定校准记录", notes = "批量新增检定校准记录")
    @PostMapping("/addRecords")
    public RestResponse<List<DtoInstrumentCheckRecord>> save(@Validated @RequestBody List<DtoInstrumentCheckRecord> entities) {

        RestResponse<List<DtoInstrumentCheckRecord>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        List<DtoInstrumentCheckRecord> data = service.save(entities);
        restResp.setData(data);
        restResp.setCount(data.size());

        return restResp;
    }

    /**
     * 修改单个检定校准记录
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改单个检定校准记录", notes = "修改单个检定校准记录")
    @PutMapping
    public RestResponse<DtoInstrumentCheckRecord> update(@Validated @RequestBody DtoInstrumentCheckRecord entity) {

        RestResponse<DtoInstrumentCheckRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentCheckRecord data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id批量删除检定校准信息
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除检定校准信息", notes = "根据id批量删除检定校准信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));

        return restResp;
    }

}