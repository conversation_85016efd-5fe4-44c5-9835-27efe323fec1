package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoParams2ParamsFormulaTemp;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalDataParams;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalHeaderParams;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.Params2ParamsFormulaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Params2ParamsFormula操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Service
public class Params2ParamsFormulaServiceImpl extends BaseJpaServiceImpl<DtoParams2ParamsFormula, String, Params2ParamsFormulaRepository> implements Params2ParamsFormulaService {

    @Autowired
    private RecordConfig2TestRepository recordConfig2TestRepository;

    @Autowired
    private ParamsConfigRepository paramsConfigRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    private ParamsFormulaRepository paramsFormulaRepository;

    @Autowired
    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    @Override
    public void findByPage(PageBean<DtoParams2ParamsFormula> pb, BaseCriteria params2ParamsFormulaCriteria) {
        pb.setEntityName("DtoParams2ParamsFormula a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, params2ParamsFormulaCriteria);
    }

    /**
     * 查询个性化配置
     *
     * @param recordConfigId   记录单id
     * @param objectId         对象id
     * @param paramsConfigId   参数id
     * @param paramsConfigType 类型
     * @return
     */
    @Override
    public DtoParams2ParamsFormulaTemp findPersonalParams(String recordConfigId,
                                                          String objectId,
                                                          String paramsConfigId,
                                                          Integer paramsConfigType) {
        DtoParams2ParamsFormulaTemp params2ParamsFormulaTemp = new DtoParams2ParamsFormulaTemp();
        List<DtoParams2ParamsFormula> params2ParamsFormulas = repository.findByRecordIdAndObjectIdAndParamsConfigId(recordConfigId, objectId, paramsConfigId);
        DtoParams2ParamsFormula params2ParamsFormula = params2ParamsFormulas.stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(params2ParamsFormula)) {
            assert params2ParamsFormula != null;
            //部分修约公式
            List<DtoParamsPartFormula> paramsPartFormulas = paramsPartFormulaRepository.findByFormulaId(params2ParamsFormula.getId());
            List<DtoParamsConfig> paramsConfigList = paramsConfigRepository.findByObjIdAndType(params2ParamsFormula.getId(), paramsConfigType);
            params2ParamsFormulaTemp.setParams2ParamsFormula(params2ParamsFormula);
            params2ParamsFormulaTemp.setParamsConfigs(paramsConfigList);
            params2ParamsFormulaTemp.setParamsPartFormulas(paramsPartFormulas);
            return params2ParamsFormulaTemp;
        }
        return null;
    }

    /**
     * 保存记录单参数个性化数据配置
     *
     * @param personalDataParams 个性数据参数
     */
    @Transactional
    @Override
    public void savePersonalDataParams(DtoPersonalDataParams personalDataParams) {
        List<DtoParams2ParamsFormula> params2ParamsFormulas = repository.findByRecordIdAndObjectIdAndParamsConfigId(
                personalDataParams.getRecordId(),
                personalDataParams.getObjectId(), personalDataParams.getParamsConfigId());

        DtoParams2ParamsFormula dtoParams2ParamsFormula = params2ParamsFormulas.stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(dtoParams2ParamsFormula)) {
            assert dtoParams2ParamsFormula != null;
            personalDataParams.setDtoParams2ParamsFormula(dtoParams2ParamsFormula, personalDataParams);
            dtoParams2ParamsFormula.setIsEnabled(true);
            super.update(dtoParams2ParamsFormula);
            //如果是修改，需要将原先的部分公式删除，新增不需要做
            paramsPartFormulaRepository.deleteByObjId(dtoParams2ParamsFormula.getId());
        } else {
            dtoParams2ParamsFormula = new DtoParams2ParamsFormula();
            personalDataParams.setDtoParams2ParamsFormula(dtoParams2ParamsFormula, personalDataParams);
            dtoParams2ParamsFormula.setIsEnabled(true);
            super.save(dtoParams2ParamsFormula);
        }
        //需要保存部分公式的修约
        saveParamsPartFormula(personalDataParams.getParamsPartFormulas(), dtoParams2ParamsFormula.getId());

        //纠正参数状态
        checkDataParams(personalDataParams.getRecordId(), personalDataParams.getParamsConfigId());

        //个性化配置是否存在，如果启用，需要存储相应的数据，否则要将原先的数据进行删除
        List<DtoParamsConfig> paramsConfigList = paramsConfigRepository.findByObjIdAndType(dtoParams2ParamsFormula.getId(),
                EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
        DtoParamsConfig dtoParamsConfig = paramsConfigList.stream().findFirst().orElse(null);
        if (personalDataParams.getIsUse()) {
            if (StringUtil.isNotNull(dtoParamsConfig)) {
                personalDataParams.setDtoParamsConfig(dtoParamsConfig, personalDataParams);
                assert dtoParamsConfig != null;
                dtoParamsConfig.setObjId(dtoParams2ParamsFormula.getId());
                dtoParamsConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
                comRepository.merge(dtoParamsConfig);
            } else {
                dtoParamsConfig = new DtoParamsConfig();
                personalDataParams.setDtoParamsConfig(dtoParamsConfig, personalDataParams);
                dtoParamsConfig.setObjId(dtoParams2ParamsFormula.getId());
                dtoParamsConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
                paramsConfigRepository.save(dtoParamsConfig);
            }
        } else if (StringUtil.isNotNull(dtoParamsConfig)) {//要将数据进行删除
            assert dtoParamsConfig != null;
            paramsConfigRepository.delete(dtoParamsConfig.getId());
        }
    }

    /**
     * 保存表头参数个性化配置
     *
     * @param dtoPersonalHeaderParams 表头参数
     */
    @Transactional
    @Override
    public void savePersonalHeaderParams(DtoPersonalHeaderParams dtoPersonalHeaderParams) {
        List<DtoParams2ParamsFormula> params2ParamsFormulas = repository.findByRecordIdAndObjectIdAndParamsConfigId(
                dtoPersonalHeaderParams.getRecordId(),
                dtoPersonalHeaderParams.getObjectId(), dtoPersonalHeaderParams.getParamsConfigId());

        DtoParams2ParamsFormula dtoParams2ParamsFormula = params2ParamsFormulas.stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(dtoParams2ParamsFormula)) {
            assert dtoParams2ParamsFormula != null;
            dtoPersonalHeaderParams.setDtoParams2ParamsFormula(dtoParams2ParamsFormula, dtoPersonalHeaderParams);
            //公式是否启用
            dtoParams2ParamsFormula.setIsEnabled(dtoPersonalHeaderParams.getIsUseDefault());
            super.update(dtoParams2ParamsFormula);
            //如果是修改，需要将原先的部分公式删除，新增不需要做
            paramsPartFormulaRepository.deleteByObjId(dtoParams2ParamsFormula.getId());
        } else {
            dtoParams2ParamsFormula = new DtoParams2ParamsFormula();
            dtoPersonalHeaderParams.setDtoParams2ParamsFormula(dtoParams2ParamsFormula, dtoPersonalHeaderParams);
            //公式是否启用
            dtoParams2ParamsFormula.setIsEnabled(dtoPersonalHeaderParams.getIsUseDefault());
            super.save(dtoParams2ParamsFormula);
        }
        //需要保存部分公式的修约
        saveParamsPartFormula(dtoPersonalHeaderParams.getParamsPartFormulas(), dtoParams2ParamsFormula.getId());
        //核对状态
        checkHeaderParams(dtoPersonalHeaderParams.getRecordId(), dtoPersonalHeaderParams.getParamsConfigId());
        //个性化配置是否存在，如果启用，需要存储相应的数据，否则要将原先的数据进行删除
        List<DtoParamsConfig> paramsConfigList = paramsConfigRepository.findByObjIdAndType(dtoParams2ParamsFormula.getId(),
                EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());
        DtoParamsConfig dtoParamsConfig = paramsConfigList.stream().findFirst().orElse(null);
        if (dtoPersonalHeaderParams.getIsShow() || !dtoPersonalHeaderParams.getIsUseDefault()) {
            if (StringUtil.isNull(dtoParamsConfig)) {
                dtoParamsConfig = new DtoParamsConfig();
                initParamsConfig(dtoParamsConfig);
                if (dtoPersonalHeaderParams.getIsShow()) {
//                    dtoPersonalHeaderParams.setDefaultValue("");
                    dtoParamsConfig.setIsShow(true);
                    dtoPersonalHeaderParams.setDtoParamsConfig(dtoPersonalHeaderParams, dtoParamsConfig);
                }
                //是否启用公式，如果不启用公式这个也要存储默认值
                if (!dtoPersonalHeaderParams.getIsUseDefault()) {
                    dtoPersonalHeaderParams.setDefaultValue(dtoPersonalHeaderParams.getDefaultValue());
                }
                dtoPersonalHeaderParams.setDtoParamsConfig(dtoPersonalHeaderParams, dtoParamsConfig);
                dtoParamsConfig.setParamsId(dtoPersonalHeaderParams.getParamsId());
                dtoParamsConfig.setAlias(dtoPersonalHeaderParams.getAlias());
                dtoParamsConfig.setObjId(dtoParams2ParamsFormula.getId());
                dtoParamsConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());
                if (dtoPersonalHeaderParams.getIsShow() || dtoPersonalHeaderParams.getIsUseDefault() ||
                        (!dtoPersonalHeaderParams.getIsUseDefault() &&
                                StringUtils.isNotNullAndEmpty(dtoPersonalHeaderParams.getDefaultValue()))) {
                    paramsConfigRepository.save(dtoParamsConfig);
                }
            } else {
                assert dtoParamsConfig != null;
                initParamsConfig(dtoParamsConfig);
                dtoParamsConfig.setIsShow(dtoPersonalHeaderParams.getIsShow());
                if (dtoPersonalHeaderParams.getIsShow()) {
                    dtoPersonalHeaderParams.setDtoParamsConfig(dtoPersonalHeaderParams, dtoParamsConfig);
                }
                if (!dtoPersonalHeaderParams.getIsUseDefault()) {
//                    dtoPersonalHeaderParams.setDefaultValue(dtoPersonalHeaderParams.getDefaultValue());
                    dtoParamsConfig.setDefaultValue(dtoPersonalHeaderParams.getDefaultValue());
                }
                dtoParamsConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());
                if (dtoPersonalHeaderParams.getIsShow() || dtoPersonalHeaderParams.getIsUseDefault() ||
                        (!dtoPersonalHeaderParams.getIsUseDefault() &&
                                StringUtils.isNotNullAndEmpty(dtoPersonalHeaderParams.getDefaultValue()))) {
                    comRepository.merge(dtoParamsConfig);
                } else { //否则将数据删除
                    paramsConfigRepository.delete(dtoParamsConfig.getId());
                }
            }
        } else if (StringUtil.isNotNull(dtoParamsConfig)) {//如果默认值不启用，或者小数位数不启用要将数据进行删除
            assert dtoParamsConfig != null;
            paramsConfigRepository.delete(dtoParamsConfig.getId());
        }
    }

    /**
     * 复制原始记录单个性化配置
     *
     * @param dtoPersonalDataParams 复制数据参数
     * @param formulaIds            公式ids
     */
    @Transactional
    @Override
    public void copyPersonalDataParams(DtoPersonalDataParams dtoPersonalDataParams, List<String> formulaIds) {
        copyPersonalParams(dtoPersonalDataParams.getRecordId(),
                dtoPersonalDataParams.getParamsConfigId(),
                dtoPersonalDataParams.getFormula(),
                dtoPersonalDataParams.getAlias(),
                dtoPersonalDataParams.getParamsId(),
                dtoPersonalDataParams.getParamsPartFormulas(),
                dtoPersonalDataParams.getIsUse(), false,
                dtoPersonalDataParams.getMostDecimal(),
                dtoPersonalDataParams.getMostSignificance(),
                dtoPersonalDataParams.getDimension(),
                dtoPersonalDataParams.getDimensionId(), "",
                dtoPersonalDataParams.getObjectId(), formulaIds,
                EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());//需要保存部分公式的修约
        //纠正参数状态
        checkDataParams(dtoPersonalDataParams.getRecordId(), dtoPersonalDataParams.getParamsConfigId());
    }

    /**
     * 复制表头参数个性化配置
     *
     * @param dtoPersonalHeaderParams 复制表头参数
     * @param formulaIds              公式ids
     */
    @Transactional
    @Override
    public void copyPersonalHeaderParams(DtoPersonalHeaderParams dtoPersonalHeaderParams, List<String> formulaIds) {
        copyPersonalParams(dtoPersonalHeaderParams.getRecordId(),
                dtoPersonalHeaderParams.getParamsConfigId(),
                dtoPersonalHeaderParams.getFormula(),
                dtoPersonalHeaderParams.getAlias(),
                dtoPersonalHeaderParams.getParamsId(),
                dtoPersonalHeaderParams.getParamsPartFormulas(),
                dtoPersonalHeaderParams.getIsUse(),
                dtoPersonalHeaderParams.getIsUseDefault(),
                dtoPersonalHeaderParams.getMostDecimal(),
                dtoPersonalHeaderParams.getMostSignificance(),
                dtoPersonalHeaderParams.getDimension(),
                dtoPersonalHeaderParams.getDimensionId(),
                dtoPersonalHeaderParams.getDefaultValue(),
                dtoPersonalHeaderParams.getObjectId(),
                formulaIds,
                EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());//需要保存部分公式的修约
        //核对状态
        checkHeaderParams(dtoPersonalHeaderParams.getRecordId(), dtoPersonalHeaderParams.getParamsConfigId());
    }


    /**
     * 复制个性化数据
     *
     * @param recordConfigId     记录单id
     * @param paramsConfigId     参数配置id
     * @param formula            公式
     * @param paramsPartFormulas 部分公式
     * @param isUse              是否使用
     * @param isUseDefault       是否使用默认值
     * @param mostDecimal        小数位数
     * @param mostSignificance   有效位数
     * @param dimension          量纲
     * @param dimensionId        量纲id
     * @param defaultValue       默认值
     * @param objectIds          对象ids
     * @param paramsConfigType   参数配置类型
     */
    private void copyPersonalParams(String recordConfigId,
                                    String paramsConfigId,
                                    String formula,
                                    String alias,
                                    String paramsId,
                                    List<DtoParamsPartFormula> paramsPartFormulas,
                                    Boolean isUse,
                                    Boolean isUseDefault,
                                    Integer mostDecimal,
                                    Integer mostSignificance,
                                    String dimension,
                                    String dimensionId,
                                    String defaultValue,
                                    String sourceObjectId,
                                    List<String> objectIds,
                                    Integer paramsConfigType) {
        List<DtoParams2ParamsFormula> params2ParamsFormulas = new ArrayList<>();
        //排除源id
        objectIds.remove(sourceObjectId);
        if (StringUtil.isEmpty(objectIds)) {
            return;
        }
        //数据源的参数数据
        List<DtoParams2ParamsFormula> sourceParamsFormulas = repository.findByRecordIdAndObjectIdAndParamsConfigId(recordConfigId, sourceObjectId, paramsConfigId);
        if (StringUtil.isNotEmpty(sourceParamsFormulas)) {
            DtoParams2ParamsFormula sourceParamsFormula = sourceParamsFormulas.get(0);
            //要复制的测试项目公式的所有公式参数
            List<DtoParamsTestFormula> sourceParamsTestFormulaList = paramsTestFormulaRepository.findByObjId(sourceObjectId);
            //要复制的公式配置
            String sourceFormula = sourceParamsFormula.getFormula();
            //要复制的公式配置中包含的公式参数id
            List<String> srcFormulaParamIdList = new ArrayList<>();
            if (StringUtil.isNotEmpty(sourceFormula)) {
                for (DtoParamsTestFormula testFormula : sourceParamsTestFormulaList) {
                    if (sourceFormula.contains(testFormula.getAlias())) {
                        srcFormulaParamIdList.add(testFormula.getParamsId());
                    }
                }
            }
            if (StringUtil.isNotEmpty(srcFormulaParamIdList)) {
                //被复制的测试项目公式的所有公式参数id，按照objectId分组
                List<DtoParamsTestFormula> copyParamsTestFormulaList = paramsTestFormulaRepository.findByObjIdIn(objectIds);
                Map<String, List<String>> copyObjId2ParamsIdListMap = copyParamsTestFormulaList.stream()
                        .collect(Collectors.groupingBy(DtoParamsTestFormula::getObjId, Collectors.mapping(DtoParamsTestFormula::getParamsId, Collectors.toList())));
                //遍历要复制的测试项目公式，过滤掉不包含源数据公式配置中的参数的测试项目公式
                List<String> rmvObjIdList = new ArrayList<>();
                for (String objId : objectIds) {
                    if (!copyObjId2ParamsIdListMap.containsKey(objId)) {
                        //被复制的测试项目公式中没有公式参数则不能复制
                        rmvObjIdList.add(objId);
                    } else {
                        List<String> loopParamsIdList = copyObjId2ParamsIdListMap.get(objId);
                        //遍历所有源公式配置参数，判断是否都存在
                        for (String srcParmId : srcFormulaParamIdList) {
                            if (!loopParamsIdList.contains(srcParmId)) {
                                rmvObjIdList.add(objId);
                                break;
                            }
                        }
                    }
                }
                if (StringUtil.isNotEmpty(rmvObjIdList)) {
                    objectIds.removeAll(rmvObjIdList);
                }
            }
        }

        if (objectIds.size() > 0) {
            //删除相关的数据
            List<DtoParams2ParamsFormula> existList = repository.findByRecordIdAndObjectIdInAndParamsConfigId(recordConfigId,
                    objectIds,
                    paramsConfigId);
            //对象ids
            List<String> existIds = existList.stream().map(DtoParams2ParamsFormula::getId).distinct().collect(Collectors.toList());
            if (existIds.size() > 0) {//这个由于数据会覆盖，考虑进行真删
                //本身的数据删除
                repository.deleteByIds(existIds);
                //部分公式数据删除
                paramsPartFormulaRepository.deleteByObjIdIn(existIds);
                //个性化的参数数据删除
                paramsConfigRepository.deleteTrueByObjIdIn(existIds);
            }
            //数据源的参数数据
//            List<DtoParams2ParamsFormula> sourceParamsFormulas=repository.findByRecordIdAndObjectIdAndParamsConfigId(recordConfigId,sourceObjectId,paramsConfigId);
            List<DtoParamsConfig> sourceParamsConfigList = new ArrayList<>();
            if (sourceParamsFormulas.size() > 0) {
                sourceParamsConfigList = paramsConfigRepository.findByObjIdAndType(sourceParamsFormulas.get(0).getId(), paramsConfigType);
            }
            List<DtoParamsPartFormula> paramsPartFormulaAll = new ArrayList<>();
            List<DtoParamsConfig> paramsConfigList = new ArrayList<>();
            for (String objectId : objectIds) {
                //主表数据
                DtoParams2ParamsFormula dtoParams2ParamsFormula = new DtoParams2ParamsFormula();
                dtoParams2ParamsFormula.setRecordId(recordConfigId);
                dtoParams2ParamsFormula.setParamsConfigId(paramsConfigId);
                dtoParams2ParamsFormula.setFormula(formula);
                dtoParams2ParamsFormula.setObjectId(objectId);
                dtoParams2ParamsFormula.setIsEnabled(EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue().equals(paramsConfigType)
                        ? isUseDefault : true);
                params2ParamsFormulas.add(dtoParams2ParamsFormula);
                //部分公式数据
                for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulas) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                    dtoParamsPartFormula.setFormulaId(dtoParams2ParamsFormula.getId());
                    dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.修约公式.getValue());
                    paramsPartFormulaAll.add(dtoParamsPartFormula);
                }
                if (isUse || !isUseDefault) {
                    DtoParamsConfig dtoParamsConfig = new DtoParamsConfig();
                    dtoParamsConfig.setObjId(dtoParams2ParamsFormula.getId());
                    initParamsConfig(dtoParamsConfig);
                    if (!isUseDefault) {
                        dtoParamsConfig.setDefaultValue(defaultValue);
                    }
                    if (isUse) {
                        dtoParamsConfig.setMostDecimal(mostDecimal);
                        dtoParamsConfig.setMostSignificance(mostSignificance);
                        dtoParamsConfig.setDimension(dimension);
                        dtoParamsConfig.setDimensionId(dimensionId);
                    }
                    dtoParamsConfig.setAlias(alias);
                    dtoParamsConfig.setParamsId(paramsId);
                    dtoParamsConfig.setType(paramsConfigType);
                    //源数据有参数配置才进行赋值，否则不进行复制了
                    if (sourceParamsConfigList.size() > 0 && (isUse ||
                            StringUtils.isNotNullAndEmpty(dtoParamsConfig.getDefaultValue()))) {
                        paramsConfigList.add(dtoParamsConfig);
                    }
                }
            }
            if (params2ParamsFormulas.size() > 0) {
                repository.save(params2ParamsFormulas);
            }
            if (paramsPartFormulaAll.size() > 0) {
                paramsPartFormulaRepository.save(paramsPartFormulaAll);
            }
            if (paramsConfigList.size() > 0) {
                paramsConfigRepository.save(paramsConfigList);
            }
        }
    }


    /**
     * 保存部分公式
     *
     * @param paramsPartFormulas 部分公式
     * @param id                 主键id
     */
    private void saveParamsPartFormula(List<DtoParamsPartFormula> paramsPartFormulas, String id) {
        //需要保存部分公式的修约
        if (StringUtil.isNotNull(paramsPartFormulas) && paramsPartFormulas.size() > 0) {
            List<DtoParamsPartFormula> paramsPartFormulaAll = new ArrayList<>();
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulas) {
                dtoParamsPartFormula.setId(UUIDHelper.NewID());
                dtoParamsPartFormula.setFormulaId(id);
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.修约公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
            paramsPartFormulaRepository.save(paramsPartFormulaAll);
        }
    }


    /**
     * 初始化数据
     *
     * @param paramsConfig 参数配置
     */
    private void initParamsConfig(DtoParamsConfig paramsConfig) {
        paramsConfig.setMostDecimal(-1);
        paramsConfig.setMostSignificance(-1);
        paramsConfig.setDimension("");
        paramsConfig.setDimensionId(UUIDHelper.GUID_EMPTY);
        paramsConfig.setDefaultValue("");
    }


    /**
     * 纠正数据参数是否配置全
     *
     * @param recordConfigId 记录单id
     * @param paramsConfigId 参数id
     */
    private void checkDataParams(String recordConfigId, String paramsConfigId) {

        //判断这个参数对应的公式是否都配置全了
        Boolean isAllConfig = false;
        List<DtoParams2ParamsFormula> params2ParamsFormulaAll = repository.findByRecordIdAndParamsConfigId(recordConfigId,
                paramsConfigId);
        //已配置的ids
        List<String> testFormulaExistIds = params2ParamsFormulaAll.stream().map(DtoParams2ParamsFormula::getObjectId).distinct().collect(Collectors.toList());
        //记录单相关的检测能力
        List<DtoRecordConfig2Test> tests = recordConfig2TestRepository.findByRecordConfigId(recordConfigId);
        //所有相关的测试项目ids
        List<String> testIds = tests.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
        if (testIds.size() > 0) {
            //测试项目下的公式
            List<DtoParamsFormula> testFormulaList = paramsFormulaRepository.findByObjectIds(testIds);
            List<String> testFormulaAllIds = testFormulaList.stream().map(DtoParamsFormula::getId).distinct().collect(Collectors.toList());
            isAllConfig = testFormulaAllIds.size() == testFormulaExistIds.size();
        }
        paramsConfigRepository.updateParamsConfigIsAllConfig(paramsConfigId, isAllConfig);
    }


    /**
     * 检查头部参数
     *
     * @param recordConfigId 记录单id
     * @param paramsConfigId 参数id
     */
    private void checkHeaderParams(String recordConfigId, String paramsConfigId) {
        //判断这个参数对应的公式是否都配置全了
        Boolean isAllConfig = false;
        List<DtoParams2ParamsFormula> params2ParamsFormulaAll = repository.findByRecordIdAndParamsConfigId(recordConfigId,
                paramsConfigId);
        //已配置的测试项目ids
        List<String> testExistIds = params2ParamsFormulaAll.stream().map(DtoParams2ParamsFormula::getObjectId).distinct().collect(Collectors.toList());
        //记录单相关的检测能力
        List<DtoRecordConfig2Test> tests = recordConfig2TestRepository.findByRecordConfigId(recordConfigId);
        //所有相关的测试项目ids
        List<String> testIds = tests.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
        if (testIds.size() > 0) {
            isAllConfig = testIds.size() == testExistIds.size();
        }
        paramsConfigRepository.updateParamsConfigIsAllConfig(paramsConfigId, isAllConfig);
    }


    @Override
    @Transactional
    public void updateParams2ParamsFormulaAndParamsConfig(Map<String, Object> map) {
        //传参异常判断
        if (map.get("recordConfigId") == null || map.get("paramsConfigId") == null || map.get("testFormulaId") == null || (map.get("formula") == null && map.get("paramsConfig") == null)) {
            throw new BaseException("传参异常");
        }
        //参数解析
        String recordId = (String) map.get("recordConfigId");
        String paramsConfigId = (String) map.get("paramsConfigId");
        String testFormulaId = (String) map.get("testFormulaId");
        //个性化公式
        DtoParams2ParamsFormula dtoParams2ParamsFormula = null;
        List<DtoParams2ParamsFormula> allDtoParams2ParamsFormulaList = repository.findByRecordIdAndObjectIdAndParamsConfigId(recordId, testFormulaId, paramsConfigId);
        if (!allDtoParams2ParamsFormulaList.isEmpty()) {
            dtoParams2ParamsFormula = allDtoParams2ParamsFormulaList.get(0);
        }
        if (dtoParams2ParamsFormula == null) {
            dtoParams2ParamsFormula = new DtoParams2ParamsFormula();
            dtoParams2ParamsFormula.setRecordId(recordId);
            dtoParams2ParamsFormula.setParamsConfigId(paramsConfigId);
            dtoParams2ParamsFormula.setObjectId(testFormulaId);
        }
        //保存个性化公式
        if (map.get("formula") != null) {
            String formula = (String) map.get("formula");
            dtoParams2ParamsFormula.setFormula(formula);
        }
        repository.save(dtoParams2ParamsFormula);
        // 保存个性化量纲,有效位数,小数位数
        if (map.get("paramsConfig") != null) {
            Map<String, Object> dataMap = (Map<String, Object>) map.get("paramsConfig");
            DtoParamsConfig paramsConfig = new DtoParamsConfig(dataMap);
            DtoParamsConfig dtoParamsConfig = paramsConfigRepository.findOne(paramsConfigId);
            List<DtoParamsConfig> existPersonalConfigList = paramsConfigRepository.findByObjIdAndType(dtoParams2ParamsFormula.getId(), EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
            DtoParamsConfig personalConfig = updatePersonalParamsConfig(dtoParamsConfig, dtoParams2ParamsFormula.getId(), existPersonalConfigList, paramsConfig);
            paramsConfigRepository.save(personalConfig);
        }
    }

    @Override
    @Transactional
    public void batchUpdateParams2ParamsFormula(Map<String, Object> map) {
        List<String> testFormulaIds = (List<String>) map.get("testFormulaIds");
        String formula = StringUtil.isNotNull(map.get("formula"))?map.get("formula").toString().replaceAll("[\\[\\]]", "") : "";
        String recordId = (String) map.get("recordConfigId");
        String paramsConfigId = (String) map.get("paramsConfigId");
        Map<String, Object> paramsConfigMap = (Map<String, Object>) map.get("paramsConfig");
        // 测试项目公式
        List<DtoParamsFormula> paramsFormulaList = paramsFormulaRepository.findAll(testFormulaIds);
        // 测试项目公式参数
        List<DtoParamsTestFormula> paramsTestFormulaList = StringUtil.isNotEmpty(testFormulaIds) ? paramsTestFormulaRepository.findByObjIdIn(testFormulaIds) : new ArrayList<>();
        // 已配置的关联公式
        List<DtoParams2ParamsFormula> allDtoParams2ParamsFormulaList = repository.findByRecordIdAndObjectIdInAndParamsConfigId(recordId, testFormulaIds, paramsConfigId);
        // 已配置的关联公式中的参数
        List<String> params2ParamsFormulaIds = allDtoParams2ParamsFormulaList.stream().map(DtoParams2ParamsFormula::getId).collect(Collectors.toList());
        List<DtoParamsConfig> paramsConfigList = StringUtil.isNotEmpty(params2ParamsFormulaIds) ?
                paramsConfigRepository.findByObjIdInAndType(params2ParamsFormulaIds, EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue()) : new ArrayList<>();
        // 当前原始记录单参数
        DtoParamsConfig paramsConfig = StringUtil.isNotEmpty(paramsConfigId) ? paramsConfigRepository.findOne(paramsConfigId) : new DtoParamsConfig();
        List<DtoParams2ParamsFormula> updateParams2ParamsFormulas = new ArrayList<>();
        List<DtoParamsConfig> updateParamsConfigs = new ArrayList<>();
        boolean isFormula = StringUtil.isNotEmpty(formula);
        boolean isParamsConfig = StringUtil.isNotEmpty(paramsConfigMap);
        DtoParamsConfig params = isParamsConfig ? new DtoParamsConfig(paramsConfigMap) : null;
        paramsFormulaList.forEach(paramsFormula -> {
            // 根据公式id 参数配置id 记录单id获取
            DtoParams2ParamsFormula params2ParamsFormula = allDtoParams2ParamsFormulaList.stream()
                    .filter(p -> paramsFormula.getId().equals(p.getObjectId())
                            && recordId.equals(p.getRecordId())
                            && paramsConfigId.equals(p.getParamsConfigId())).findFirst().orElse(null);
            DtoParams2ParamsFormula update;
            if (params2ParamsFormula != null) {
                update = params2ParamsFormula;
            } else {
                update = new DtoParams2ParamsFormula();
                update.setRecordId(recordId);
                update.setParamsConfigId(paramsConfigId);
                update.setObjectId(paramsFormula.getId());
            }
            if (isFormula) {
                // 根据输入公式参数，过滤出名称与公式参数一致的数据，不存在就跳过
                Optional<DtoParamsTestFormula> testFormulaOptional = paramsTestFormulaList.stream().filter(p -> p.getObjId().equals(paramsFormula.getId())
                        && p.getParamsName().equals(formula)).findFirst();
                testFormulaOptional.ifPresent(testFormula -> {
                    update.setFormula("[" + formula + "]");
                });
            }
            updateParams2ParamsFormulas.add(update);
            // 参数不为空时处理
            if (isParamsConfig) {
                List<DtoParamsConfig> paramsConfigs = paramsConfigList.stream().filter(p -> p.getObjId().equals(update.getId())).collect(Collectors.toList());
                updateParamsConfigs.add(updatePersonalParamsConfig(paramsConfig, update.getId(), paramsConfigs, params));
            }
        });
        // 数据更新
        if (StringUtil.isNotEmpty(updateParams2ParamsFormulas)) {
            repository.save(updateParams2ParamsFormulas);
        }
        if (StringUtil.isNotEmpty(updateParamsConfigs)) {
            paramsConfigRepository.save(updateParamsConfigs);
        }
    }

    /**
     * 保存个性化量纲,有效位数,小数位数
     *
     * @param dtoParamsConfig           原始记录单参数
     * @param dtoParams2ParamsFormulaId 个性化实体id
     * @param dtoParams2ParamsFormulaId 个性化实体参数集合
     * @param paramsConfig              更新数据容器
     */
    private DtoParamsConfig updatePersonalParamsConfig(DtoParamsConfig dtoParamsConfig, String dtoParams2ParamsFormulaId, List<DtoParamsConfig> existPersonalConfigList, DtoParamsConfig paramsConfig) {

        DtoParamsConfig personalConfig = null;

        if (!existPersonalConfigList.isEmpty()) {
            personalConfig = existPersonalConfigList.get(0);
        }
        if (personalConfig == null) {
            personalConfig = new DtoParamsConfig();
            personalConfig.setObjId(dtoParams2ParamsFormulaId);
            personalConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
            personalConfig.setParamsId(dtoParamsConfig.getParamsId());
            personalConfig.setAlias(dtoParamsConfig.getAlias());
        }
        if (paramsConfig.getDimensionId() != null) {
            personalConfig.setDimensionId(paramsConfig.getDimensionId());
            personalConfig.setDimension(paramsConfig.getDimension());
        } else {
            personalConfig.setDimensionId("");
            personalConfig.setDimension("");
        }
        if (paramsConfig.getMostDecimal() != null) {
            personalConfig.setMostDecimal(paramsConfig.getMostDecimal());
        } else {
            personalConfig.setMostDecimal(-1);
        }
        if (paramsConfig.getMostSignificance() != null) {
            personalConfig.setMostSignificance(paramsConfig.getMostSignificance());
        } else {
            personalConfig.setMostSignificance(-1);
        }
        return personalConfig;
    }
}