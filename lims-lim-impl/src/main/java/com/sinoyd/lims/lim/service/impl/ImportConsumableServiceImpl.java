package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.customer.DtoImportConsumable;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.ConsumableRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableExtend;
import com.sinoyd.lims.lim.dto.lims.DtoConsumableStorage;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.ConsumableStorageRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ImportConsumableService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.ConsumableVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消耗品/标准样品导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/24
 * @since V100R001
 */
@Service
@Slf4j
public class ImportConsumableServiceImpl implements ImportConsumableService {

    //region 注入
    protected ConsumableRepository consumableRepository;

    private ConsumableService consumableService;

    protected ConsumableDetailService detailService;

    protected CodeService codeService;

    protected ImportUtils importUtils;

    protected PersonRepository personRepository;

    protected DimensionRepository dimensionRepository;

    protected EnterpriseRepository enterpriseRepository;

    protected EnterpriseService enterpriseService;

    protected ConsumableStorageRepository consumableStorageRepository;

    //endregion

    /**
     * 导入消耗品/标准样品
     *
     * @param file      传入的文件
     * @param objectMap 业务数据
     * @return List
     * @throws Exception 错误信息
     */
    @Override
    @Transactional
    public List<DtoConsumable> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 参数
        //获取是否导入消耗品类型
        Boolean isImportConsumableType = (Boolean) objectMap.get(0);
        //获取导入的关联信息
        ExcelImportResult<DtoImportConsumableExtend> extendResult = importUtils.getExcelData(file, DtoImportConsumableExtend.class, 0, 1, 1, true, null);
        //判断消耗品类型是否为标样
        String codeType;
        Workbook workbook = extendResult.getWorkbook();
        String sheetName = workbook.getSheetName(1);
        if ("消耗品类型".equals(sheetName)) {
            codeType = "LIM_ConsumableCategory";
        } else {
            codeType = "LIM_StandardCategory";
        }
        //获取数据库中所有的消耗品类型
        List<DtoCode> dbConsType = codeService.findCodes(codeType);
        //所有的量纲数据
        List<DtoDimension> dbDimension = dimensionRepository.findAll();
        //获取企业信息
        List<DtoEnterprise> enterprise = querySupplierEnt();
        //获取人员信息
        List<DtoPerson> personList = personRepository.findAll();
        ConsumableVerifyHandler verify = getVerify(codeType, isImportConsumableType, dbConsType, extendResult.getList());
        //获取消耗品导入结果
        ExcelImportResult<DtoImportConsumable> result = getExcelData(verify, file, response);
        //获取消耗品导入List
        List<DtoImportConsumable> importConsumables = result.getList();
        importConsumables.removeIf(p -> StringUtil.isEmpty(p.getConsumableName()));
        //判断导入中是否存在数据
        if (StringUtil.isEmpty(importConsumables)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        //endregion

        //region 标样处理
        //标样赋值
        if ("LIM_StandardCategory".equals(codeType)) {
            importConsumables.forEach(p -> p.setIsStandard(true));
        }
        //endregion

        //region 判断消耗品类型是否导入
        if (isImportConsumableType) {
            //导入消耗品类型
            importExtend(file, dbConsType, codeType);
        }
        //endregion

        //region 转换实体
        //转换实体（消耗品基本信息）
        List<DtoConsumable> consumables = getConsumableEntity(importConsumables, personList);
        //处理量纲
        handleDimension(consumables, dbDimension);
        //处理供应商
        handleSupplier(importConsumables, enterprise);
        List<DtoConsumableStorage> consumableStorages = new ArrayList<>();
        //转换实体（消耗品详细信息）
        List<DtoConsumableDetail> details = getDetailEntity(importConsumables, enterprise, personList, consumableStorages);
        //endregion

        //region 储存数据库
        //保存消耗品基本信息
        addData(consumables);
        //保存消耗品详细信息
        detailService.insertBatch(details);
        // 保存消耗品入库记录
        consumableStorageRepository.save(consumableStorages);
        //endregion

        return consumableRepository.findAll();
    }

    /**
     * 处理供应商
     *
     * @param importConsumables 消耗品
     */
    private void handleSupplier(List<DtoImportConsumable> importConsumables, List<DtoEnterprise> enterprise) {
        List<String> providerEntNames = importConsumables.stream().filter(v -> StringUtil.isNotNull(v.getDetail()) && StringUtil.isNotEmpty(v.getDetail().getSupplierName()))
                .map(v -> v.getDetail().getSupplierName()).collect(Collectors.toList());
        List<String> entNames = enterprise.stream().map(DtoEnterprise::getName).collect(Collectors.toList());
        providerEntNames.removeAll(entNames);
        List<DtoEnterprise> newProvideEntList = new ArrayList<>();
        providerEntNames.stream().distinct().forEach(v -> {
            DtoEnterprise ent = new DtoEnterprise();
            ent.setType(EnumBase.EnumEnterpriseType.供应商.getValue());
            ent.setName(v);
            newProvideEntList.add(ent);
        });
        enterprise.addAll(newProvideEntList);
        enterpriseService.save(newProvideEntList);
    }

    /**
     * 处理量纲数据
     *
     * @param consumables 需要导入的消耗品
     * @param dbDimension 所有的量纲数据
     */
    protected void handleDimension(List<DtoConsumable> consumables, List<DtoDimension> dbDimension) {
        List<String> dimensionNames = dbDimension.stream().map(DtoDimension::getDimensionName).collect(Collectors.toList());
        List<String> importDimensionNames = consumables.stream().map(DtoConsumable::getUnit).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> selectDimensionNames = consumables.stream().map(DtoConsumable::getDimensionName).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        importDimensionNames.addAll(selectDimensionNames);
        importDimensionNames = importDimensionNames.stream().distinct().collect(Collectors.toList());
        List<String> notExistNames = importDimensionNames.stream().filter(p -> !dimensionNames.contains(p)).distinct().collect(Collectors.toList());
        List<DtoDimension> addDimension = new ArrayList<>();
        for (String notExistName : notExistNames) {
            DtoDimension dtoDimension = new DtoDimension();
            dtoDimension.setDimensionName(notExistName);
            dtoDimension.setDimensionTypeId("/");
            dtoDimension.setBaseValue(BigDecimal.ZERO);
            addDimension.add(dtoDimension);
        }
        if (StringUtil.isNotEmpty(addDimension)) {
            List<DtoDimension> save = dimensionRepository.save(addDimension);
            dbDimension.addAll(save);
        }
        //处理实体中的量纲Id
        dbDimension.removeIf(p -> StringUtil.isEmpty(p.getDimensionName()));
        Map<String, List<DtoDimension>> dimensionMap = dbDimension.stream().collect(Collectors.groupingBy(DtoDimension::getDimensionName));
        for (DtoConsumable consumable : consumables) {
            List<DtoDimension> dimensionOfName = dimensionMap.get(consumable.getUnit());
            if (StringUtil.isNotNull(dimensionOfName)) {
                Optional<DtoDimension> dimension = dimensionOfName.stream().findFirst();
                dimension.ifPresent(p -> consumable.setUnitId(p.getId()));
            }
            List<DtoDimension> dimensionOfSelect = dimensionMap.get(consumable.getDimensionName());
            if (StringUtil.isNotNull(dimensionOfSelect)) {
                Optional<DtoDimension> selectDimension = dimensionOfSelect.stream().findFirst();
                selectDimension.ifPresent(p -> consumable.setDimensionId(p.getId()));
            }
        }
    }


    /**
     * 添加数据库数据
     *
     * @param data 需要导入的数据
     */
    @Override
    @Transactional
    public void addData(List<DtoConsumable> data) {
        consumableService.insertBatch(data);
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoImportConsumable> getExcelData(IExcelVerifyHandler<DtoImportConsumable> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验        params.setNeedVerify(true);
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoImportConsumable> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportConsumable.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "消耗品导入错误信息");
            failWorkbook.removeSheetAt(1);
            PoiExcelUtils.downLoadExcel("消耗品导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    /**
     * 获取消耗品导入校验器
     *
     * @param codeType               消耗品类型
     * @param isImportConsumableType 是否导入消耗品类型
     * @param dbConsType             消耗品等级
     * @param extendList             导入关联数据
     * @return 校验器
     */
    protected ConsumableVerifyHandler getVerify(String codeType, Boolean isImportConsumableType,
                                                List<DtoCode> dbConsType, List<DtoImportConsumableExtend> extendList) {
        Map<String, Boolean> relationMap = new HashMap<>();
        relationMap.put("isImportConsumableType", isImportConsumableType);
        return new ConsumableVerifyHandler(relationMap, codeType,
                codeService.findCodes("LIM_ConsumableGrade"),
                dbConsType, personRepository.findAll(),
                querySupplierEnt(), extendList);
    }

    /**
     * 查询现有供应商
     *
     * @return 现有供应商
     */
    private List<DtoEnterprise> querySupplierEnt() {
        EnterpriseCriteria criteria = new EnterpriseCriteria();
        PageBean<DtoEnterprise> pb = new PageBean<>();
        criteria.setType(4);
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        enterpriseService.findByPage(pb, criteria);
        return pb.getData();
    }

    /**
     * 获取导入模板的数据
     *
     * @param file 传入的文件
     * @return 导入结果
     * @throws Exception 错误信息
     */
    @Override
    public ExcelImportResult<DtoImportConsumable> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取消耗品基本信息实体
     *
     * @param importConsumables 导入实体
     * @param personList        人员信息
     * @return 消耗品实体
     */
    private List<DtoConsumable> getConsumableEntity(List<DtoImportConsumable> importConsumables, List<DtoPerson> personList) {
        List<DtoConsumable> consumables = new ArrayList<>();
        for (DtoImportConsumable importConsumable : importConsumables) {
            String sendWarnUser = "";
            if (StringUtil.isNotEmpty(importConsumable.getSendWarnUserName())) {
                sendWarnUser = importConsumable.getSendWarnUserName();
            }
            DtoConsumable consumable = new DtoConsumable();
            consumable.importToEntity(importConsumable);
            String finalSendWarnUser = sendWarnUser;
            String sendWarnUserId = personList.stream().filter(p -> p.getCName().equals(finalSendWarnUser))
                    .map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
            consumable.setSendWarnUserId(sendWarnUserId);
            if (importConsumable.getIsStandard()) {
                consumable.setIsLabEncryption(importConsumable.getIsLabEncryption().equals("是"));
            }
            if (StringUtil.isNotEmpty(importConsumable.getUncertainTypeName())) {
                consumable.setUncertainType(EnumBase.EnumUncertainType.getValueByName(importConsumable.getUncertainTypeName()));
            }
            consumables.add(consumable);
        }
        return consumables;
    }

    /**
     * 获取消耗品基本信息实体
     *
     * @param importConsumables  导入实体
     * @param enterprise         企业信息
     * @param personList         人员信息
     * @param consumableStorages 消耗品入库记录集合
     * @return 消耗品实体
     */
    private List<DtoConsumableDetail> getDetailEntity(List<DtoImportConsumable> importConsumables,
                                                      List<DtoEnterprise> enterprise,
                                                      List<DtoPerson> personList,
                                                      List<DtoConsumableStorage> consumableStorages) {
        List<DtoConsumableDetail> details = new ArrayList<>();
        for (DtoImportConsumable importConsumable : importConsumables) {
            String sendWarnUser = "";
            if (StringUtil.isNotEmpty(importConsumable.getSendWarnUserName())) {
                sendWarnUser = importConsumable.getSendWarnUserName();
            }
            DtoConsumableDetail consumableDetail = new DtoConsumableDetail();
            consumableDetail.importToEntity(importConsumable);
            consumableDetail.setStorageDate(importUtils.stringToDateAllFormat(importConsumable.getDetail().getStorageDate()));
            consumableDetail.setExpiryDate(importUtils.stringToDateAllFormat(importConsumable.getDetail().getExpiryDate()));
            String finalSendWarnUser = sendWarnUser;
            String checkerId = personList.stream().filter(p -> p.getCName().equals(finalSendWarnUser))
                    .map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
            consumableDetail.setCheckerId(checkerId);
            consumableDetail.setOrderTime(importUtils.stringToDateAllFormat(importConsumable.getDetail().getOrderTime()));
            consumableDetail.setSupplierName(importConsumable.getDetail().getSupplierName());
            String supplierId = enterprise.stream().filter(p -> p.getName().equals(importConsumable.getDetail().getSupplierName()))
                    .map(DtoEnterprise::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
            consumableDetail.setSupplierId(supplierId);
            consumableDetail.setSendWarnUser(sendWarnUser);
            details.add(consumableDetail);
            // 消耗品入库记录
            DtoConsumableStorage consumableStorage = new DtoConsumableStorage();
            consumableStorage.importToEntity(consumableDetail);
            consumableStorages.add(consumableStorage);
        }
        return details;
    }

    /**
     * 导入消耗品类型
     *
     * @param file       文件流
     * @param dbConsType 消耗品类型数据
     */
    protected void importExtend(MultipartFile file, List<DtoCode> dbConsType, String codeType) {
        //获取导入的关联信息
        List<DtoImportConsumableExtend> extendResult = importUtils.getImportNames(file, DtoImportConsumableExtend.class);
        //数据库消耗品类型
        List<String> dbExtendData = dbConsType.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //关联表中消耗品类型
        List<String> resultNames = extendResult.stream().map(DtoImportConsumableExtend::getConsumableCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        //标样类型
        if ("LIM_StandardCategory".equals(codeType)) {
            resultNames = extendResult.stream().map(DtoImportConsumableExtend::getConsumableStandardCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        }
        importUtils.createCodes(resultNames, codeType, dbExtendData);
    }

    @Autowired
    public void setConsumableRepository(ConsumableRepository consumableRepository) {
        this.consumableRepository = consumableRepository;
    }

    @Autowired
    public void setConsumableService(ConsumableService consumableService) {
        this.consumableService = consumableService;
    }

    @Autowired
    public void setDetailService(ConsumableDetailService detailService) {
        this.detailService = detailService;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setConsumableStorageRepository(ConsumableStorageRepository consumableStorageRepository) {
        this.consumableStorageRepository = consumableStorageRepository;
    }
}
