package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;

import java.util.List;

/**
 * ocr参数数据历史
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigParamDataRepository extends IBaseJpaRepository<DtoOcrConfigParamData, String> {
    /**
     * 根据识别记录标识查询
     * @param recordId 根据识别记录标识
     * @return 参数数据
     */
    List<DtoOcrConfigParamData> findAllByRecordId(String recordId);

    /**
     * 根据识别记录标识查询
     * @param recordIds 根据识别记录标识
     * @return 参数数据
     */
    List<DtoOcrConfigParamData> findAllByRecordIdIn(List<String> recordIds);
}