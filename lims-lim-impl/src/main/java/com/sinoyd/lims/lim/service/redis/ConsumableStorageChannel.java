package com.sinoyd.lims.lim.service.redis;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.lims.lim.dto.lims.DtoConsumableStorage;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.ConsumableStorageRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 入库记录接收通道
 * <AUTHOR>
 * @version V1.0.0 2019/12/9
 * @since V100R001
 */
@Component
@Service
public class ConsumableStorageChannel {

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private ConsumableStorageRepository consumableStorageRepository;
    @Async
    @Transactional
    public void createConsumableStorage(String consumableDetailJson) {
        if (StringUtils.isNotNullAndEmpty(consumableDetailJson)) {
            TypeLiteral<DtoConsumableDetail> typeLiteral = new TypeLiteral<DtoConsumableDetail>() {
            };
            DtoConsumableDetail consumableDetail = JsonIterator.deserialize(JsonIterator.deserialize(consumableDetailJson).toString(), typeLiteral);

            String checkId = consumableDetail.getCheckerId();
            String checkName = "";
            DtoPerson person = personRepository.findOne(checkId);

            if (StringUtil.isNotNull(person)) {
                checkName = person.getCName();
            } else {
                DtoUser dtoUser = userService.findByUserId(checkId);
                if (StringUtil.isNotNull(dtoUser)) {
                    checkName = dtoUser.getUserName();
                }
            }
            DtoConsumableStorage consumableStorage = consumableStorageRepository.findOne(consumableDetail.getNewId());
            if (StringUtil.isNull(consumableStorage)) {
                consumableStorage = new DtoConsumableStorage();
            }
            consumableStorage.setId(consumableDetail.getNewId());
            consumableStorage.setConsumableId(consumableDetail.getParentId());
            consumableStorage.setPurchaseDetailId(consumableDetail.getId());
            consumableStorage.setDimensionId(consumableDetail.getUnitId());
            consumableStorage.setDimensionName(consumableDetail.getUnitName());
            consumableStorage.setProductionCode(consumableDetail.getProductionCode());
            consumableStorage.setCheckerId(checkId);
            consumableStorage.setCheckerName(checkName);
            consumableStorage.setCheckerDate(consumableDetail.getPurchasingDate());
            consumableStorage.setCheckerResult(consumableDetail.getCheckerResult());
            consumableStorage.setSupplyCompanyName(consumableDetail.getSupplierName());
            consumableStorage.setExpiryDate(consumableDetail.getExpiryDate());
            consumableStorage.setStorageNum(consumableDetail.getInventory());
            consumableStorage.setStorageTime(consumableDetail.getStorageDate());
            consumableStorage.setBalance(consumableDetail.getStorage());
            consumableStorage.setUnitPrice(consumableDetail.getUnitPrice());
            consumableStorage.setTotalPrice(consumableDetail.getUnitPrice().multiply(consumableDetail.getInventory()));
            consumableStorage.setSupplyCompanyId(consumableDetail.getSupplierId());
            consumableStorage.setSupplyCompanyName(consumableDetail.getSupplierName());
            consumableStorage.setOperatorId(consumableDetail.getCreator());
            consumableStorage.setOperatorName(consumableDetail.getCreatorName());
            consumableStorage.setAppearance(consumableDetail.getAppearance());
            consumableStorage.setCheckItem(consumableDetail.getCheckItem());
            consumableStorage.setBuyReason(consumableDetail.getBuyReason());
            consumableStorage.setKeepPlace(consumableDetail.getKeepPlace());
            consumableStorage.setRemark(consumableDetail.getRemark());
            consumableStorage.setOrgId(consumableDetail.getOrgId());
            consumableStorageRepository.save(consumableStorage);
        }
    }
}
