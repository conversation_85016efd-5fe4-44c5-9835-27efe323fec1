package com.sinoyd.lims.lim.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.UserDepartmentModel;
import com.sinoyd.boot.frame.sys.service.IUserDepartmentService;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityConfigTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.customer.DtoDocUserConfigTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocUserTemp;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityConfig;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityConfigRepository;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityListRepository;
import com.sinoyd.lims.lim.repository.lims.FolderRepository;
import com.sinoyd.lims.lim.service.DocAuthorityConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件夹权限接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/3
 * @since V100R001
 */
@Service
public class DocAuthorityConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoDocAuthorityConfig, String, DocAuthorityConfigRepository> implements DocAuthorityConfigService {

    private DepartmentService departmentService;

    private UserService userService;

    @Autowired
    private FolderRepository folderRepository;

    private CodeService codeService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    private IUserDepartmentService userDepartmentService;
    
    private DocAuthorityListRepository docAuthorityListRepository;

    @Autowired
    private AuthorizeService authorizeService;

    @Transactional
    @Override
    public List<DtoDocAuthorityList> findAuthorityList(String objectId, String orgId) {

        List<DtoDocAuthorityList> authorityLists = docAuthorityListRepository.findByObjectId(objectId);
        List<DtoDocAuthorityConfig> authorityConfigs = repository.findByObjectId(objectId);

        // 查询文件权限字典配置
        List<DtoCode> authList = codeService.findCodes(LimCodeHelper.LIM_DocumentAuthorityType);
        // 查询所有人员
        List<DtoUser> allUser = userService.findAll();

        DtoFolder folder = folderRepository.findOne(objectId);

        authorityLists = authorityLists.stream().peek(authorityList -> {
            if (authorityList.getDefaultOpenInd()) {
                authorityList.setUserNums(allUser.size() - 1);
            } else {
                authorityList.setUserNums(authorityConfigs.stream().filter(c -> c.getAuthorityListId().equals(authorityList.getId()) && !c.getUserId().equals(folder.getCreator())).collect(Collectors.toList()).size());
            }
        }).sorted(Comparator.comparing(DtoDocAuthorityList::getSortNum, Comparator.reverseOrder())).collect(Collectors.toList());
        return authorityLists;
    }

    @Override
    public List<DtoDocUserConfigTemp> findUserList(String objectId, String authCode) {

        // 初始化返回值
        List<DtoDocUserConfigTemp> tempList = new ArrayList<>();
        // 查询当前文件夹的创建人
        DtoFolder dtoFolder = folderRepository.findOne(objectId);
        // 查询已配置的权限信息，为标记做准备
        List<DtoDocAuthorityConfig> docAuthorityConfigList = repository.findByObjectIdAndAuthCode(objectId, authCode);
        List<String> collect = docAuthorityConfigList.stream().map(DtoDocAuthorityConfig::getUserId).collect(Collectors.toList());
        // 查询所有科室
        List<DtoDepartment> deptList = departmentService.findAll();
        // 查询所有人员
        List<DtoUser> allUser = userService.findAll();
        // 过滤文件夹创建人
        allUser = allUser.stream().filter(p -> !p.getId().equals(dtoFolder.getCreator())).collect(Collectors.toList());
        // 查询科室和用户关联信息
        Wrapper<UserDepartmentModel> wrapper = new EntityWrapper<>();
        List<UserDepartmentModel> userDepartmentModels = userDepartmentService.selectList(wrapper);
        for (DtoDepartment dtoDepartment : deptList) {
            DtoDocUserConfigTemp configTemp = new DtoDocUserConfigTemp();
            // 筛选当前科室下的所有用户
            List<String> deptUserIdList = userDepartmentModels.stream().filter(p -> dtoDepartment.getId().equals(p.getDeptGuid())).map(UserDepartmentModel::getUserGuid).collect(Collectors.toList());
            List<DtoUser> userList = allUser.stream().filter(user -> deptUserIdList.contains(user.getId())).collect(Collectors.toList());

            // 过滤文件夹的创建人
            List<DtoDocUserTemp> userTempList = new ArrayList<>();
            for (DtoUser user : userList) {
                DtoDocUserTemp userTemp = new DtoDocUserTemp();
                if (collect.contains(user.getId())) {
                    // 标记选中
                    userTemp.setChoose(true);
                }
                userTemp.setUserName(user.getUserName());
                userTemp.setId(user.getId());
                userTempList.add(userTemp);
            }
            configTemp.setUserTempList(userTempList);
            configTemp.setDepartmentName(dtoDepartment.getDeptName());
            tempList.add(configTemp);
        }
        return tempList;
    }

    @Override
    public List<DtoDocUserConfigTemp> findAllUserList() {
        List<DtoDocUserConfigTemp> tempList = new ArrayList<>();
        List<DtoUser> allUser = userService.findAll();
        allUser.removeIf(u -> PrincipalContextUser.getPrincipal().getUserId().equals(u.getId()));
        List<DtoDepartment> deptList = departmentService.findAll();
        Wrapper<UserDepartmentModel> wrapper = new EntityWrapper<>();
        List<UserDepartmentModel> userDepartmentModels = userDepartmentService.selectList(wrapper);
        for (DtoDepartment dtoDepartment : deptList) {
            DtoDocUserConfigTemp configTemp = new DtoDocUserConfigTemp();
            List<String> deptUserIdList = userDepartmentModels.stream().filter(p -> dtoDepartment.getId().equals(p.getDeptGuid())).map(UserDepartmentModel::getUserGuid).collect(Collectors.toList());
            List<DtoUser> userList = allUser.stream().filter(user -> deptUserIdList.contains(user.getId())).collect(Collectors.toList());
            List<DtoDocUserTemp> userTempList = new ArrayList<>();
            for (DtoUser user : userList) {
                DtoDocUserTemp userTemp = new DtoDocUserTemp();
                userTemp.setChoose(false);
                userTemp.setUserName(user.getUserName());
                userTemp.setId(user.getId());
                userTempList.add(userTemp);
            }
            configTemp.setUserTempList(userTempList);
            configTemp.setDepartmentName(dtoDepartment.getDeptName());
            tempList.add(configTemp);
        }
        return tempList;
    }

    @Transactional
    @Override
    public void saveAll(DtoDocAuthorityConfigTemp temp) {
        // 权限信息
        DtoDocAuthorityConfig docAuthorityConfig = temp.getDocAuthorityConfigList().get(0);
        // 人员信息
        List<String> userIdList = temp.getUserIdList();
        List<DtoDocAuthorityConfig> configList = new ArrayList<>();
        if (StringUtil.isNotEmpty(userIdList)) {
            // 数据处理
            processAuthorityConfig(userIdList, docAuthorityConfig, configList);
        }
        // 先删除已配置的权限，
        deletedByObjectIdAndAuthCode(docAuthorityConfig.getObjectId(), Stream.of(docAuthorityConfig.getAuthCode()).collect(Collectors.toList()));
        super.save(configList);
    }

    @Transactional
    @Override
    public void batchSaveAll(DtoDocAuthorityConfigTemp temp) {
        if (StringUtil.isNotNull(temp)) {
            // 获取权限集合
            List<DtoDocAuthorityConfig> docAuthorityConfigList = temp.getDocAuthorityConfigList();
            DtoDocAuthorityConfig docAuthorityConfig = docAuthorityConfigList.stream().findFirst().orElse(null);
            // 筛选出所有的权限编码集合
            List<String> authCodes = docAuthorityConfigList.stream().map(DtoDocAuthorityConfig::getAuthCode).collect(Collectors.toList());
            // 先删除已配置的权限，
            deletedByObjectIdAndAuthCode(docAuthorityConfig.getObjectId(), authCodes);

            List<String> userIdList = temp.getUserIdList();
            List<DtoDocAuthorityConfig> configList = new ArrayList<>();
            if (StringUtil.isNotNull(docAuthorityConfigList)) {
                for (DtoDocAuthorityConfig config : docAuthorityConfigList) {
                    // 数据处理
                    processAuthorityConfig(userIdList, config, configList);
                }
            }
            super.save(configList);
        }
    }

    /**
     * 权限验证
     *
     * @param dtoDocAuthorityValidate 文档权限验证实体
     * @return 是否拥有权限
     */
    @Override
    public Boolean validateAuth(DtoDocAuthorityValidate dtoDocAuthorityValidate) {
        // 验证信息
        String userId = PrincipalContextUser.getPrincipal().getUserId();

        if (StringUtil.isNotNull(dtoDocAuthorityValidate)) {
            // 文件夹信息
            DtoFolder dtoFolder = dtoDocAuthorityValidate.getFolder();
            if (StringUtil.isNotNull(dtoFolder)) {
                //文件夹ID
                String folderId = dtoFolder.getId();
                //创建人员
                String creator = dtoFolder.getCreator();
                if (creator.equals(userId)) { //如果当前登录人为文件夹的创建人，默认拥有所有权限
                    return true;
                }
                //权限编码
                String docAuthorityCode = dtoDocAuthorityValidate.getDocAuthorityCode();
                if (docAuthorityCode.equals(LimCodeHelper.LIM_DocAuthority_Show)) { //如果是文件管理员的权限编码，用框架的权限验证
                    return authorizeService.haveActionPermission(userId, docAuthorityCode);
                }
                DtoDocAuthorityList authorityList = docAuthorityListRepository.findByObjectIdAndAuthCode(folderId, docAuthorityCode);
                if (StringUtil.isNotNull(authorityList) && authorityList.getDefaultOpenInd()) {
                    return true;
                }
                Integer count = repository.countByObjectIdAndAuthCodeAndUserId(folderId, docAuthorityCode, userId);
                if (count > 0) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }


    @Transactional
    @Override
    public List<DtoDocAuthorityConfig> initAuthorityConfig(String objectId, String userId) {
        List<DtoCode> authList = codeService.findCodes(LimCodeHelper.LIM_DocumentAuthorityType);
        List<DtoDocAuthorityConfig> configList = new ArrayList<>();
        for (DtoCode dtoCode : authList) {
            DtoDocAuthorityConfig config = new DtoDocAuthorityConfig();
            config.setAuthCode(dtoCode.getDictCode());
            config.setObjectId(objectId);
            config.setAuthName(dtoCode.getDictName());
            config.setSortNum(dtoCode.getSortNum());
            if (dtoCode.getExtendI1() == 1) {
                config.setDefaultOpenInd(true);
            } else {
                config.setDefaultOpenInd(false);
            }
            config.setUserId(userId);
            configList.add(config);
        }
        return super.save(configList);
    }

    @Override
    @Transactional
    public void batchSet(List<String> ids, Boolean defaultOpenInd) {
        docAuthorityListRepository.batchSet(ids, defaultOpenInd);
        if (defaultOpenInd) {
            DtoDocAuthorityList authorityList = docAuthorityListRepository.findOne(ids.get(0));
            DtoFolder folder = folderRepository.findOne(authorityList.getObjectId());
            repository.deleteByAuthorityListIdInAndUserIdNot(ids, folder.getCreator());
        }
    }

    @Override
    @Transactional
    public void batchSet(List<String> ids, List<String> authCodes, Boolean defaultOpenInd) {
        List<DtoDocAuthorityList> authorityLists = docAuthorityListRepository.findByObjectIdInAndAuthCodeIn(ids, authCodes);
        authorityLists.forEach(a -> a.setDefaultOpenInd(defaultOpenInd));
        docAuthorityListRepository.save(authorityLists);
        if (defaultOpenInd) {
            authorityLists.stream().collect(Collectors.groupingBy(DtoDocAuthorityList::getObjectId)).forEach((objectId, authLists) -> {
                DtoFolder folder = folderRepository.findOne(objectId);
                repository.deleteByAuthorityListIdInAndUserIdNot(authLists.stream().map(DtoDocAuthorityList::getId).collect(Collectors.toList()), folder.getCreator());
            });
        }
    }

    @Override
    @Transactional
    public void batchSet(List<String> ids, List<String> authCodes, List<String> userIds) {
        List<DtoFolder> folderList = folderRepository.findAll(ids);
        List<DtoDocAuthorityList> authorityLists = docAuthorityListRepository.findByObjectIdIn(ids);
        List<DtoDocAuthorityConfig> configList = new ArrayList<>();
        for (DtoFolder folder : folderList) {
            repository.deleteByObjectIdAndAuthCodeInAndUserIdNot(folder.getId(), authCodes, folder.getCreator());
            List<DtoDocAuthorityList> authorityListsOfFolder = authorityLists.stream().filter(a -> a.getObjectId().equals(folder.getId()) && authCodes.contains(a.getAuthCode())).collect(Collectors.toList());
            for (DtoDocAuthorityList authorityList : authorityListsOfFolder) {
                if (authorityList.getDefaultOpenInd()) {
                    continue;
                }
                for (String userId : userIds) {
                    DtoDocAuthorityConfig config = new DtoDocAuthorityConfig();
                    config.setObjectId(folder.getId());
                    config.setAuthCode(authorityList.getAuthCode());
                    config.setAuthName(authorityList.getAuthName());
                    config.setDefaultOpenInd(authorityList.getDefaultOpenInd());
                    config.setSortNum(authorityList.getSortNum());
                    config.setAuthorityListId(authorityList.getId());
                    config.setUserId(userId);
                    configList.add(config);
                }
            }
        }
        if (StringUtil.isNotEmpty(configList)) {
            repository.save(configList);
        }
    }

    /**
     * 数据处理
     *
     * @param userIdList         用户id集合
     * @param docAuthorityConfig 权权限
     * @param configList         数据汇总集合
     */
    private void processAuthorityConfig(List<String> userIdList, DtoDocAuthorityConfig docAuthorityConfig, List<DtoDocAuthorityConfig> configList) {

        if (StringUtil.isNotNull(userIdList)) {
            for (String userId : userIdList) {
                DtoDocAuthorityConfig config = new DtoDocAuthorityConfig();
                config.setObjectId(docAuthorityConfig.getObjectId());
                config.setAuthCode(docAuthorityConfig.getAuthCode());
                config.setAuthName(docAuthorityConfig.getAuthName());
                config.setDefaultOpenInd(docAuthorityConfig.getDefaultOpenInd());
                config.setSortNum(docAuthorityConfig.getSortNum());
                config.setAuthorityListId(docAuthorityConfig.getId());
                config.setUserId(userId);
                configList.add(config);
            }
        }
    }


    /**
     * 根据文件夹id 和权限编码删除
     *
     * @param objectId  文件夹id
     * @param authCodes 权限编码集合
     */
    private void deletedByObjectIdAndAuthCode(String objectId, List<String> authCodes) {
        // 查询当前文件夹的创建人
        DtoFolder dtoFolder = folderRepository.findOne(objectId);
        List<DtoDocAuthorityConfig> authorityConfigList = repository.findByObjectIdAndAuthCodeIn(objectId, authCodes);
        // 筛选出id，过滤掉文件夹的创建人
        List<String> ids = authorityConfigList.stream()
                .filter(p -> !p.getUserId().equals(dtoFolder.getCreator()))
                .map(DtoDocAuthorityConfig::getId)
                .collect(Collectors.toList());
        // 先删除配置信息
        repository.deleteByIdIn(ids);
    }


    /**
     * 根据objectId，按照文件权限分组查询
     *
     * @param objectId 文件夹id
     * @return 分组后的结果
     */
    private List<DtoDocAuthorityConfig> groupConfigByObjectId(String objectId, String orgId) {
        StringBuilder sql = new StringBuilder("select p.objectId, count(p.authCode) as userNums,")
                .append(" p.authCode, p.authName ,p.sortNum,p.defaultOpenInd")
                .append(" from TB_lim_docauthorityconfig p")
                .append(" where p.objectId = :objectId ")
                .append(" and p.orgId = :orgId ")
                .append(" group by p.authCode,")
                .append("  p.objectId,")
                .append("  p.authName,")
                .append("  p.sortNum,")
                .append("  p.defaultOpenInd");
        Map<String, Object> values = new HashMap<>();
        values.put("objectId", objectId);
        values.put("orgId", orgId);
        return namedParameterJdbcTemplate.query(sql.toString(), values, (rs, rowNum) -> {
            DtoDocAuthorityConfig docAuthorityConfig = new DtoDocAuthorityConfig();
            docAuthorityConfig.setAuthCode(rs.getString("authCode"));
            docAuthorityConfig.setObjectId(rs.getString("objectId"));
            docAuthorityConfig.setAuthName(rs.getString("authName"));
            docAuthorityConfig.setUserNums(rs.getInt("userNums"));
            docAuthorityConfig.setSortNum(rs.getInt("sortNum"));
            docAuthorityConfig.setDefaultOpenInd(rs.getBoolean("defaultOpenInd"));
            return docAuthorityConfig;
        });
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setUserDepartmentService(IUserDepartmentService userDepartmentService) {
        this.userDepartmentService = userDepartmentService;
    }

    @Autowired
    public void setDocAuthorityListRepository(DocAuthorityListRepository docAuthorityListRepository) {
        this.docAuthorityListRepository = docAuthorityListRepository;
    }
}
