package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import com.sinoyd.lims.lim.service.InstrumentUseRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器使用记录接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-08
 * @since V100R001
 */
@Api(tags = "仪器使用记录")
@RestController
@RequestMapping("/api/lim/instrumentUseRecord")
@Validated
public class InstrumentUseRecordController extends BaseJpaController<DtoInstrumentUseRecord, String, InstrumentUseRecordService>{

    /**
     * 根据id获取仪器使用记录
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取仪器使用记录", notes = "根据id获取仪器使用记录")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentUseRecord> find(@PathVariable(name = "id") String id){

        RestResponse<DtoInstrumentUseRecord> restResp = new RestResponse<>();
        DtoInstrumentUseRecord record = service.findOne(id);
        restResp.setData(record);

        restResp.setRestStatus(StringUtil.isNull(record) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }
    
     /**
      * 分页动态条件查询仪器使用记录
      * @param criteria
      * @return
      */
    @ApiOperation(value = "分页动态条件查询仪器使用记录", notes = "分页动态条件查询仪器使用记录")
    @GetMapping
    public RestResponse<List<DtoInstrumentUseRecord>> findByPage(InstrumentUseRecordCriteria criteria){

        RestResponse<List<DtoInstrumentUseRecord>> restResp = new RestResponse<>();

        PageBean<DtoInstrumentUseRecord> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    @ApiOperation(value = "新增仪器使用记录", notes = "新增仪器使用记录")
    @PostMapping
    public RestResponse<DtoInstrumentUseRecord> save(@Validated @RequestBody DtoInstrumentUseRecord entity) {

        RestResponse<DtoInstrumentUseRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentUseRecord data = service.saveInstrumentUseRecord(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    @ApiOperation(value = "新增仪器使用记录", notes = "新增仪器使用记录")
    @PostMapping("/newSave/{environmentalRecordId}")
    public RestResponse<List<DtoInstrumentUseRecord>> newSave(@PathVariable String environmentalRecordId, @Validated @RequestBody List<DtoInstrumentUseRecord> entityList) {
        RestResponse<List<DtoInstrumentUseRecord>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.newSave(environmentalRecordId,entityList));
        return restResp;
    }

    @ApiOperation(value = "修改仪器使用记录", notes = "修改仪器使用记录")
    @PutMapping
    public RestResponse<DtoInstrumentUseRecord> update(@Validated @RequestBody DtoInstrumentUseRecord entity) {

        RestResponse<DtoInstrumentUseRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentUseRecord data = service.updateInstrumentUseRecord(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    @ApiOperation(value = "修改仪器使用记录", notes = "修改仪器使用记录")
    @PutMapping("/newUpdate")
    public RestResponse<List<DtoInstrumentUseRecord>> newUpdate(@Validated @RequestBody DtoInstrumentUseRecord entity) {
        RestResponse<List<DtoInstrumentUseRecord>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.newUpdate(entity));
        return restResp;
    }

    /**
     * 根据id批量删除仪器维护记录
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除仪器维修记录", notes = "根据id批量删除仪器维修记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 根据id批量删除仪器维护记录
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除仪器维修记录", notes = "根据id批量删除仪器维修记录")
    @DeleteMapping("/newDelete/{id}")
    public RestResponse<String> newDelete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.newDelete(id));
        return restResp;
    }
}