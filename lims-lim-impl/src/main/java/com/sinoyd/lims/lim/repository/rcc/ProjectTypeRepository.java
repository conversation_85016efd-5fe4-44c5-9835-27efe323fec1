package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;


/**
 * 项目类型数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/23
 * @since V100R001
 */
public interface ProjectTypeRepository extends IBaseJpaRepository<DtoProjectType, String> {

    /**
     * 新增时判断是否存在同项目类型名称
     *
     * @param name 项目类型名称
     * @return 相同项目类型名称的项目类型数量
     */
    Integer countByName(String name);

    /**
     * 更新时判断是否存在同项目类型名称
     *
     * @param name 项目类型名称
     * @param id   要更新的项目类型的id
     * @return 相同项目类型名称的项目类型数量
     */
    Integer countByNameAndIdNot(String name, String id);

    /**
     * 获取父类下的子类项目类型
     *
     * @param parentIds 父类项目类型id集合
     * @return 父类下的所有子类项目类型
     */
    List<DtoProjectType> findByParentIdIn(Collection parentIds);

    /**
     * 根据工作流id获取项目类型
     *
     * @param workflowId 工作流id
     * @return 对应工作流id的项目类型
     */
    List<DtoProjectType> findByWorkflowId(String workflowId);

    /**
     * 倒序排序获取所有的项目类型的方法
     * @return 返回项目类型
     */
    @Query("SELECT  a FROM DtoProjectType  as  a where  a.isDeleted=0 order by  a.orderNum desc")
    List<DtoProjectType> findOrderNumDesc();
}