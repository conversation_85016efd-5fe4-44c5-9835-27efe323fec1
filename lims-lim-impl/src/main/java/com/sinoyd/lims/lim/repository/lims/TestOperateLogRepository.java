package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTestOperateLog;

import java.util.List;


/**
 * TestOperateLog数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/02/20
 * @since V100R001
 */
public interface TestOperateLogRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestOperateLog, String> {

    /**
     * 根据修改字段查询
     *
     * @param tableName    表名
     * @param operateField 字段名称
     * @return TestOperateLog集合
     */
    List<DtoTestOperateLog> findByTableNameAndOperateField(String tableName, String operateField);
}