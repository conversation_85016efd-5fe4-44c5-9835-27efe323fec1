package com.sinoyd.lims.lim.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.lims.lim.repository.rcc.VersionInfoRepository;
import com.sinoyd.lims.lim.service.LoginQRCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

/**
 * 登陆页二维码操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/19
 * @since V100R001
 */
@Service
@Slf4j
public class LoginQRCodeServiceImpl implements LoginQRCodeService {

    @Autowired
    @Lazy
    private VersionInfoRepository versionInfoRepository;

    @Autowired
    @Lazy
    private DocumentService documentService;


    private IConfigService configService;

    @Override
    public String findNewVersionInfo() {
        List<DtoVersionInfo> versionInfoList = versionInfoRepository.findAll();
        // 查询登录页是否显示二维码开关
        Wrapper<ConfigModel> wrapper = new EntityWrapper<>();
        wrapper.eq("configKey", LimCodeHelper.LOGIN_QRCODE);
        ConfigModel configModel = configService.selectOne(wrapper);
        if (StringUtil.isNotNull(configModel) && "true".equals(configModel.getConfigValue())) {
            DtoVersionInfo versionInfo = versionInfoList.stream().max(Comparator.comparing(DtoVersionInfo::getVersion)).orElse(null);
            if (StringUtil.isNotNull(versionInfo)) {
                processUrl(versionInfo);
                return versionInfo.getRealUrl();
            }
        }
        return null;
    }

    /**
     * 处理移动端下载路径
     */
    @Override
    public void processUrl(DtoVersionInfo versionInfo) {
        DtoDocument document = documentService.findByObjectId(versionInfo.getId()).stream().max(Comparator.comparing(DtoDocument::getCreateDate)).orElse(null);
        if (StringUtil.isNotNull(document)) {
            versionInfo.setRealSize(document.getDocSize());
            String url = loadMobileGatePath() + "/files" + document.getPath();
            versionInfo.setRealUrl(url);
        }
    }


    @Override
    public String getCaptchaConfig() {
        ConfigModel configModel = configService.findConfig("sys.login.captcha");
        if (configModel == null || StringUtil.isEmpty(configModel.getConfigValue())) {
            return "false";
        }
        log.info(".........登录验证码配置: " + configModel.getConfigValue() + ".....................");
        return configModel.getConfigValue();
    }


    /**
     * 获取移动端网关地址
     *
     * @return RCC访问地址
     */
    private String loadMobileGatePath() {
        ConfigModel configModel = configService.findConfig("app.gate.path");
        if (configModel == null || StringUtil.isEmpty(configModel.getConfigValue())) {
            throw new BaseException("请在参数设置中配置移动端网关地址, 如http://ip:prot");
        }
        log.info(".........移动端网关地址: " + configModel.getConfigValue() + ".....................");
        return configModel.getConfigValue();
    }


    @Autowired
    @Lazy
    public void setConfigService(IConfigService configService) {
        this.configService = configService;
    }
}
