package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTest2ShSamplingMethod;
import com.sinoyd.lims.lim.repository.lims.Test2ShSamplingMethodRepository;
import com.sinoyd.lims.lim.service.Test2ShSamplingMethodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 测试项目与监管平台采样方法关联服务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/17
 **/
@Slf4j
@Service
public class Test2ShSamplingMethodServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTest2ShSamplingMethod, String, Test2ShSamplingMethodRepository> implements Test2ShSamplingMethodService {


}
