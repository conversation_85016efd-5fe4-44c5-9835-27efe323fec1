package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 参数配置管理查询条件
 *
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParamsConfigCriteria extends BaseCriteria {

    /**
     * 关键字：参数配置名称，参数配置编号,变量名称
     */
    private String key;

    /**
     * 采样单配置id
     */
    private String recordId;

    /**
     * 采样单配置中的查询条件
     */
    private String condition;
    /**
     * 对象id
     */
    private String objId;

    /**
     * 仅仅父参数
     */
    private Boolean onlyParent = false;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 参数id
     */
    private String paramsId;

    /**
     * 组织架构id
     */
    private String orgId;

    /**
     * 对象类型（枚举EnumParamsConfigType：1.检测（样品）类型，2.测试项目，3.企业（预留）,4.方法（预留）,5.采样单（预留）6.原始记录单7.报告（预留）8.样品-采样单参数也是检测（样品）类型上的参数，只是进行设置用到采样单分组（检测（样品）类型公共参数）9.原始记录单-检测单参数（原始记录单上面部分的计算，如化学需氧量、BOD5）
     */
    private Integer type;

    /**
     * 参数类型
     */
    private Integer paramsType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (alias like :key)");
            values.put("key", "%" + this.key + "%");
        }
        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.condition)) {
            condition.append(" and (alias like :condition)");
            values.put("condition", "%" + this.condition + "%");
        }
        //对象id
        if (StringUtils.isNotNullAndEmpty(this.objId) && !UUIDHelper.GUID_EMPTY.equals(this.objId)) {
            condition.append(" and objId = :objId ");
            values.put("objId", this.objId);
        }
        //对象id
        if (StringUtils.isNotNullAndEmpty(this.recordId) && !UUIDHelper.GUID_EMPTY.equals(this.recordId)) {
            condition.append(" and objId in (select sampleTypeId from DtoRecordConfig where id = :recordId) and id not in(select paramsConfigId from DtoRecordConfig2ParamsConfig where recordConfigId = :recordId)");
            values.put("recordId", this.recordId);
        }
        //参数id
        if (StringUtils.isNotNullAndEmpty(this.paramsId) && !UUIDHelper.GUID_EMPTY.equals(this.paramsId)) {
            condition.append(" and paramsId = :paramsId ");
            values.put("paramsId", this.paramsId);
        }
        //对象类型
        if (StringUtil.isNotNull(type) && type.intValue() != -1) {
            condition.append(" and type = :type ");
            values.put("type", this.type);
        }

        if (StringUtil.isNotNull(paramsType)) {
            condition.append(" and paramsType = :paramsType ");
            values.put("paramsType", this.paramsType);
        }

        if (StringUtils.isNotNullAndEmpty(this.parentId) && !UUIDHelper.GUID_EMPTY.equals(this.parentId)) {
            condition.append(" and parentId = :parentId ");
            values.put("parentId", this.parentId);
        }

        if (onlyParent) {
            condition.append(" and parentId = :parentId ");
            values.put("parentId", UUIDHelper.GUID_EMPTY);
        }


        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }
}