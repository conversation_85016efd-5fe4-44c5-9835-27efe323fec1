package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchTask;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.entity.Person;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.NewSearchResultRepository;
import com.sinoyd.lims.lim.repository.lims.NewSearchTaskRepository;
import com.sinoyd.lims.lim.service.NewSearchResultService;
import com.sinoyd.lims.lim.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查新结果接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Service
public class NewSearchResultServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoNewSearchResult, String, NewSearchResultRepository> implements NewSearchResultService {

    private NewSearchTaskRepository newSearchTaskRepository;

    private PersonService personService;

    @Override
    public void findByPage(PageBean<DtoNewSearchResult> page, BaseCriteria criteria) {
        page.setEntityName("DtoNewSearchResult t");
        page.setSelect("select t");
        super.findByPage(page, criteria);
        List<DtoNewSearchResult> resultList = page.getData();
        List<String> taskIds = resultList.stream().map(DtoNewSearchResult::getTaskId).distinct().collect(Collectors.toList());
        List<DtoNewSearchTask> taskList = StringUtil.isNotEmpty(taskIds) ? newSearchTaskRepository.findAll(taskIds) : new ArrayList<>();
        List<String> executorIds = taskList.stream().map(DtoNewSearchTask::getExecutor).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(executorIds) ? personService.findAll(executorIds) : new ArrayList<>();
        for (DtoNewSearchResult newSearchResult : resultList) {
            // 执行人
            taskList.stream().filter(p -> p.getId().equals(newSearchResult.getTaskId())).findFirst().ifPresent(task -> {
                personList.stream().filter(p -> task.getExecutor().equals(p.getId())).findFirst().ifPresent(person -> {
                    newSearchResult.setExecutor(person.getCName());
                });

            });
            // 确认人
            personList.stream().filter(p -> newSearchResult.getConfirmId().equals(p.getId())).findFirst().ifPresent(person -> {
                newSearchResult.setConfirmName(person.getCName());
            });
        }
    }


    @Override
    public DtoNewSearchResult save(DtoNewSearchResult entity) {
        entity.setStatus(EnumLIM.EnumNewSearchStatus.新建.getValue());
        // 是否新标准为否时，全部默认斜杠
        if (!entity.getIsNewStandard()) {
            entity.fillField(entity);
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public List<DtoNewSearchResult> submit(List<String> ids) {
        return handleResult(ids, EnumLIM.EnumNewSearchStatus.已处理.getValue());
    }

    @Override
    @Transactional
    public List<DtoNewSearchResult> backResult(List<String> ids) {
        return handleResult(ids, EnumLIM.EnumNewSearchStatus.待处理.getValue());
    }

    /**
     * 处理结果数据
     *
     * @param ids    主键ids
     * @param status 状态
     * @return 处理后的结果
     */
    private List<DtoNewSearchResult> handleResult(List<String> ids, int status) {
        List<DtoNewSearchResult> newSearchResults = repository.findAll(ids);
        // 更新结果状态
        newSearchResults.forEach(p -> p.setStatus(status));
        return super.save(newSearchResults);
    }

    @Autowired
    public void setNewSearchTaskRepository(NewSearchTaskRepository newSearchTaskRepository) {
        this.newSearchTaskRepository = newSearchTaskRepository;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}
