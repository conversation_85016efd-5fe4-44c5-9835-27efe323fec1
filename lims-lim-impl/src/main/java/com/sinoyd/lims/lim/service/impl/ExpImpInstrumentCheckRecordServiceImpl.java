package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.InstrumentCheckRecordCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpInstrumentCheckRecord;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.InstrumentCheckRecordRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ExpImpInstrumentCheckRecordService;
import com.sinoyd.lims.lim.service.InstrumentCheckRecordService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyInstrumentCheckRecordVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器鉴定校准导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExpImpInstrumentCheckRecordServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentCheckRecord, String, InstrumentCheckRecordRepository> implements ExpImpInstrumentCheckRecordService {

    private InstrumentCheckRecordService instrumentCheckRecordService;
    private InstrumentService instrumentService;
    private ImportUtils importUtils;
    private PersonRepository personRepository;
    private ImpModifyInstrumentCheckRecordVerify impModifyInstrumentCheckRecordVerify;

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {

        InstrumentCheckRecordCriteria checkRecordCriteria = (InstrumentCheckRecordCriteria) baseCriteria;
        PageBean<DtoInstrumentCheckRecord> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        instrumentCheckRecordService.findByPage(pb, checkRecordCriteria);
        List<DtoInstrumentCheckRecord> checkRecords = pb.getData();
        Set<String> instrumentIds = checkRecords.stream().map(DtoInstrumentCheckRecord::getInstrumentId).collect(Collectors.toSet());
        List<DtoInstrument> instrumentList = StringUtil.isNotEmpty(instrumentIds) ? instrumentService.findAll(instrumentIds) : new ArrayList<>();
        Map<String, DtoInstrument> instrumentMap = instrumentList.stream().collect(Collectors.toMap(DtoInstrument::getId, p -> p));
        List<DtoPerson> personList = personRepository.findAll();
        Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        List<DtoExpImpInstrumentCheckRecord> expImpInstrumentCheckRecords = new ArrayList<>();
        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        for (DtoInstrumentCheckRecord checkRecord : checkRecords) {
            DtoExpImpInstrumentCheckRecord expImpInstrumentCheckRecord = new DtoExpImpInstrumentCheckRecord();
            BeanUtils.copyProperties(checkRecord, expImpInstrumentCheckRecord);
            // 仪器本站编号
            DtoInstrument instrument = instrumentMap.getOrDefault(checkRecord.getInstrumentId(), new DtoInstrument());
            expImpInstrumentCheckRecord.setInstrumentCode(StringUtil.isNotEmpty(instrument.getInstrumentsCode()) ? instrument.getInstrumentsCode() : "");
            // 溯源方式
            expImpInstrumentCheckRecord.setOriginTypeName(EnumBase.EnumOriginType.getEnumOriginType(checkRecord.getOriginType()).name());
            //检定/校准日期
            String checkTime = "";
            if (StringUtil.isNotNull(checkRecord.getCheckTime()) && checkRecord.getCheckTime().compareTo(date1753) != 0) {
                checkTime = DateUtil.dateToString(checkRecord.getCheckTime(), DateUtil.YEAR);
            }
            expImpInstrumentCheckRecord.setCheckTime(checkTime);
            //检定/校准有效期
            String checkEndTime = "";
            if (StringUtil.isNotNull(checkRecord.getCheckEndDate()) && checkRecord.getCheckEndDate().compareTo(date1753) != 0) {
                checkEndTime = DateUtil.dateToString(checkRecord.getCheckEndDate(), DateUtil.YEAR);
            }
//            // 适用性
            expImpInstrumentCheckRecord.setUsabilityStr(checkRecord.getUsability() ? "是" : "否");
            expImpInstrumentCheckRecord.setCheckResultStr(checkRecord.getCheckResult() == 1 ? "合格" : "不合格");
            expImpInstrumentCheckRecord.setCheckEndDate(checkEndTime);
            expImpInstrumentCheckRecords.add(expImpInstrumentCheckRecord);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpInstrumentCheckRecord.class, expImpInstrumentCheckRecords);
        // 设置下拉框、
        String[] originType = Arrays.stream(EnumBase.EnumOriginType.values()).map(Enum::name).toArray(String[]::new);
        importUtils.selectList(workBook, 2, 2, originType);
        importUtils.selectList(workBook, 9, 9, new String[]{"是", "否"});
        importUtils.selectList(workBook, 10, 10, new String[]{"合格", "不合格"});
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    @Transactional
    public List<DtoInstrumentCheckRecord> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 数据参数

        //获取所有的仪器信息
        List<DtoInstrument> instrumentList = instrumentService.findAll();
        //获取所有的人员信息
        List<DtoPerson> personList = personRepository.findAll();
        impModifyInstrumentCheckRecordVerify.getDbInstrumentTl().set(instrumentList);
        impModifyInstrumentCheckRecordVerify.getDbPersonTl().set(personList);

        ExcelImportResult<DtoExpImpInstrumentCheckRecord> importResult = getExcelData(file, response);

        //获取校验成功导入数据
        List<DtoExpImpInstrumentCheckRecord> importList = importResult.getList();
        impModifyInstrumentCheckRecordVerify.getDbInstrumentTl().remove();
        impModifyInstrumentCheckRecordVerify.getDbPersonTl().remove();
        impModifyInstrumentCheckRecordVerify.getDbInstrumentTl().remove();


        //移除空行
        importList.removeIf(p -> StringUtil.isNull(p.getOriginType()));

        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //endregion

        List<String> checkIds = importList.stream().map(DtoExpImpInstrumentCheckRecord::getId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<DtoInstrumentCheckRecord> instrumentCheckRecordList = StringUtil.isNotEmpty(checkIds) ? repository.findAll(checkIds) : new ArrayList<>();

        //region 添加数据
        List<DtoInstrumentCheckRecord> checkRecordList = importToEntity(instrumentList, importList, personList,instrumentCheckRecordList);
        addData(checkRecordList);
        //endregion

        return repository.findAll();
    }

    @Override
    @Transactional
    public void addData(List<DtoInstrumentCheckRecord> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpInstrumentCheckRecord> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(impModifyInstrumentCheckRecordVerify);
        ExcelImportResult<DtoExpImpInstrumentCheckRecord> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoExpImpInstrumentCheckRecord.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "仪器检定导入错误信息");
            PoiExcelUtils.downLoadExcel("仪器检定导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 导入实体转换为仪器校准信息实体
     *
     * @param importCheckRecords 导入数据
     * @return 仪器校准信息实体
     */
    private List<DtoInstrumentCheckRecord> importToEntity(List<DtoInstrument> instrumentList, List<DtoExpImpInstrumentCheckRecord> importCheckRecords,
                                                          List<DtoPerson> personList,List<DtoInstrumentCheckRecord> instrumentCheckRecordList) {
        //region 参数
        List<DtoInstrumentCheckRecord> checkRecordList = new ArrayList<>();
        //endregion
        // 根据仪器本站编号分组
        Map<String, DtoInstrument> instrumentMap = instrumentList.stream().collect(Collectors.toMap(DtoInstrument::getInstrumentsCode, p -> p));
        Map<String, DtoInstrumentCheckRecord> checkRecordMap = instrumentCheckRecordList.stream().collect(Collectors.toMap(DtoInstrumentCheckRecord::getId, p -> p));

        for (DtoExpImpInstrumentCheckRecord importCheckRecord : importCheckRecords) {
            //获取校准人
            if (StringUtil.isNotEmpty(importCheckRecord.getCheckPerson())) {
                importCheckRecord.setCheckPersonId(UUIDHelper.GUID_EMPTY);
                importCheckRecord.setCheckPerson(importCheckRecord.getCheckPerson());
            }else {
                importCheckRecord.setCheckPersonId(UUIDHelper.GUID_EMPTY);
            }
            //仪器信息
            if (StringUtil.isNotEmpty(importCheckRecord.getInstrumentCode())) {
                DtoInstrumentCheckRecord checkRecord = new DtoInstrumentCheckRecord();
                //获取到关联仪器信息
                if (StringUtil.isNotEmpty(importCheckRecord.getId()) && checkRecordMap.containsKey(importCheckRecord.getId())) {
                    checkRecord = checkRecordMap.get(importCheckRecord.getId());
                }else {
                    importCheckRecord.setId(UUIDHelper.NewID());
                }
                DtoInstrument instrumentOrDefault = instrumentMap.getOrDefault(importCheckRecord.getInstrumentCode(), new DtoInstrument());
                importCheckRecord.setInstrumentId(instrumentOrDefault.getId());
                //赋值
                BeanUtils.copyProperties(importCheckRecord, checkRecord);
                checkRecord.setCheckTime(importUtils.stringToDateAllFormat(importCheckRecord.getCheckTime()));
                checkRecord.setCheckEndDate(importUtils.stringToDateAllFormat(importCheckRecord.getCheckEndDate()));
                checkRecord.setCost(BigDecimal.ZERO);
                checkRecordList.add(checkRecord);
            }
        }
        return checkRecordList;
    }

    @Autowired
    public void setInstrumentCheckRecordService(InstrumentCheckRecordService instrumentCheckRecordService) {
        this.instrumentCheckRecordService = instrumentCheckRecordService;
    }

    @Autowired
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setImpModifyInstrumentCheckRecordVerify(ImpModifyInstrumentCheckRecordVerify impModifyInstrumentCheckRecordVerify) {
        this.impModifyInstrumentCheckRecordVerify = impModifyInstrumentCheckRecordVerify;
    }
}
