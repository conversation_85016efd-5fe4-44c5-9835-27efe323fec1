package com.sinoyd.lims.lim.service.rcc;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.service.ISyncDataService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumRequestTarget;
import com.sinoyd.commons.vo.QueryRequestParamVO;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源中心测试项目服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@Service
public class RccTestServiceImpl extends RemoteRequestBaseServiceImpl implements IRccTestService {

    private ISyncDataService<DtoTest> testSyncService;

    private IRccTestExpandService rccTestExpandService;

    private TestService testService;

    private TestRepository testRepository;

    @Override
    public RestResponse<List<DtoTest>> findByPageFromRcc(PageBean<DtoTest> pb, BaseCriteria baseCriteria) {
        TestCriteria testCriteria = (TestCriteria) baseCriteria;
        //处理过滤测试项目数据
        if (testCriteria.getIsFilterSelected()){
            List<String> excloudIds = testRepository.findAll().stream().map(DtoTest::getId).collect(Collectors.toList());
            testCriteria.setExcludeIds(excloudIds);
        }
        Map<String, Object> criteriaMap = super.objectToMap(testCriteria, "values", "sort");
        QueryRequestParamVO param = createQueryParam(pb, criteriaMap, "/api/lim/test/exclude?page=" + pb.getPageNo() + "&rows=" + pb.getRowsPerPage(), HttpMethod.POST.name(), EnumRequestTarget.RCC.getValue(), "RCC");
        param.setSort(testCriteria.getSort());
        param.setIsRePage(false);
        RestResponse<List<JSONObject>> restResponse = rccQueryService.findByPage(param);
        List<DtoTest> testList = getResponseList(restResponse, DtoTest.class);
        RestResponse<List<DtoTest>> response = new RestResponse<>();
        response.setCount(restResponse.getCount());
        response.setMsg(restResponse.getMsg());
        response.setData(testList);
        return response;
    }

    @Override
    public List<DtoTest> findRccAllByIds(Collection<String> testIds) {
        if (StringUtil.isNotEmpty(testIds)) {
            Map<String, Object> criteriaMap = new HashMap<>();
            criteriaMap.put("", testIds);
            QueryRequestParamVO param = createQueryParam("/api/lim/test/multiple", HttpMethod.POST.name(),
                    EnumRequestTarget.RCC.getValue(), "RCC", criteriaMap);
            RestResponse<List<JSONObject>> response = rccQueryService.findList(param);
            return getResponseList(response, DtoTest.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DtoTest> findRccAll() {
        QueryRequestParamVO param = createQueryParam("/api/lim/test/all", HttpMethod.POST.name(), EnumRequestTarget.RCC.getValue(), "RCC", new HashMap<>());
        RestResponse<List<JSONObject>> response = rccQueryService.findList(param);
        return getResponseList(response, DtoTest.class);
    }

    @Override
    @Transactional
    public void sync() {
        List<DtoTest> sourceDataList = this.findRccAll();
        if (StringUtil.isNotEmpty(sourceDataList)) {
            testSyncService.sync(sourceDataList, null);
            List<String> sourceIds = sourceDataList.stream().map(DtoTest::getId).collect(Collectors.toList());
            rccTestExpandService.sync(sourceIds);
        }
    }

    @Override
    @Transactional
    public void syncStation() {
        List<DtoTest> stationTests = testService.findAll();
        List<String> testIds = stationTests.stream().map(DtoTest::getId).collect(Collectors.toList());
        sync(testIds);
    }

    @Override
    @Transactional
    public void sync(Collection<String> rccTestIds) {
        List<DtoTest> sourceDataList = this.findRccAllByIds(rccTestIds);
        if (StringUtil.isNotEmpty(sourceDataList)) {
            testSyncService.sync(sourceDataList, null);
            rccTestExpandService.sync(rccTestIds);
        }
    }

    @Override
    @Transactional
    public void abolish(Collection<String> rccTestIds) {
        if (StringUtil.isEmpty(rccTestIds)) {
            return;
        }
        List<DtoTest> testList = testService.findAll(rccTestIds);
        if (StringUtil.isNotEmpty(testList)) {
            //作废测试项目
            testList.forEach(p -> p.setIsRccAbolish(true));
            testRepository.save(testList);
            testService.initRedis();
        }
    }

    @Override
    @Transactional
    public void enable(Collection<String> rccTestIds) {
        if (StringUtil.isEmpty(rccTestIds)) {
            return;
        }
        List<DtoTest> testList = testService.findAll(rccTestIds);
        if (StringUtil.isNotEmpty(testList)) {
            //作废测试项目
            testList.forEach(p -> p.setIsRccAbolish(false));
            testRepository.save(testList);
            testService.initRedis();
        }
    }


    @Autowired
    @Lazy
    @Qualifier("testSyncServiceImpl")
    public void setTestSyncService(ISyncDataService<DtoTest> testSyncService) {
        this.testSyncService = testSyncService;
    }

    @Autowired
    @Lazy
    public void setRccTestExpandService(IRccTestExpandService rccTestExpandService) {
        this.rccTestExpandService = rccTestExpandService;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }
}
