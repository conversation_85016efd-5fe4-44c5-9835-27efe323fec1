package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.rcc.IRccTestService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 资源中心测试项目 Controller
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@RestController
@RequestMapping("/api/lim/rccTest")
public class RccTestController extends ExceptionHandlerController<IRccTestService> {

    /**
     * 资源配置中心测试项目分页查询
     *
     * @param criteria 资源配置中心测试项目查询条件
     * @return 资源配置中心测试项目集合
     */
    @GetMapping
    public RestResponse<List<DtoTest>> findByPage(TestCriteria criteria) {
        PageBean<DtoTest> pb = super.getPageBean();
        return service.findByPageFromRcc(pb, criteria);
    }


    /**
     * 从RCC中同步测试项目
     *
     * @param testIds 待同步的测试项目id集合
     * @return 结果
     */
    @PostMapping("/sync")
    public RestResponse<String> sync(@RequestBody Collection<String> testIds) {
        service.sync(testIds);
        return new RestResponse<>();
    }

    /**
     * 从RCC中同步全部测试项目
     *
     * @return 结果
     */
    @PostMapping("/sync/all")
    public RestResponse<String> sync() {
        service.sync();
        return new RestResponse<>();
    }

    /**
     * 从RCC中同步本机构所有测试项目
     *
     * @return 结果
     */
    @PostMapping("/sync/org")
    public RestResponse<String> syncStation() {
        service.syncStation();
        return new RestResponse<>();
    }

    /**
     * 作废RCC删除的测试项目数据
     *
     * @return 结果
     */
    @PostMapping("/abolish")
    public RestResponse<String> abolish(@RequestBody Collection<String> testIds) {
        service.abolish(testIds);
        return new RestResponse<>();
    }

    /**
     * 启用作废的RCC测试项目数据
     *
     * @return 结果
     */
    @PostMapping("/enable")
    public RestResponse<String> enable(@RequestBody Collection<String> testIds) {
        service.enable(testIds);
        return new RestResponse<>();
    }

}
