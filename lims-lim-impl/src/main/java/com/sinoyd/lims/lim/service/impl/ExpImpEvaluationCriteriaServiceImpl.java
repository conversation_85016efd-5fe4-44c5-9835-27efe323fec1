package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.*;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEvaluationCriteria;
import com.sinoyd.lims.lim.service.ExpImpEvaluationCriteriaService;
import com.sinoyd.lims.lim.service.ImportEvaluationCriteriaService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyEvaluationCriteriaVerify;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 评价标准导出接口实现实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
@Slf4j
public class ExpImpEvaluationCriteriaServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEvaluationCriteria, String, EvaluationCriteriaRepository> implements ExpImpEvaluationCriteriaService {
    private ImportUtils importUtils;
    private SampleTypeRepository sampleTypeRepository;
    private AnalyzeItemRepository analyzeItemRepository;
    private EvaluationValueRepository evaluationValueRepository;
    private EvaluationLevelRepository evaluationLevelRepository;
    private ImportEvaluationCriteriaService importEvaluationCriteriaService;
    private EvaluationCriteriaService evaluationCriteriaService;
    private EvaluationAnalyzeItemRepository evaluationAnalyzeItemRepository;
    private DimensionRepository dimensionRepository;

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoEvaluationCriteria> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        EvaluationCriteriaCriteria criteria = (EvaluationCriteriaCriteria) baseCriteria;
        evaluationCriteriaService.findByPage(page, criteria);
        List<DtoEvaluationCriteria> evaluationCriteriaList = page.getData();

        List<String> evaluationIds = evaluationCriteriaList.stream().map(DtoEvaluationCriteria::getId).collect(Collectors.toList());
        // 检测类型
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        Map<String, String> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));

        // 评价等级
        List<DtoEvaluationLevel> evaluationLevelList = evaluationLevelRepository.findByEvaluationIdIn(evaluationIds);
        // 处理评价等级的评价等级全称
        importEvaluationCriteriaService.getLevelListFullName(evaluationLevelList);
        Map<String, List<DtoEvaluationLevel>> levelGroup = sortParent(evaluationLevelList);

        // 评价数据
        List<String> levelIds = evaluationLevelList.stream().map(DtoEvaluationLevel::getId).collect(Collectors.toList());
        List<DtoEvaluationValue> evaluationList = evaluationValueRepository.findByLevelIdIn(levelIds);
        List<String> analyzeItemIds = evaluationList.stream().map(DtoEvaluationValue::getAnalyzeItemId).distinct().collect(Collectors.toList());
        // 分析因子
        List<DtoAnalyzeItem> analyseItemList = StringUtil.isNotEmpty(analyzeItemIds) ? analyzeItemRepository.findAll(analyzeItemIds) : new ArrayList<>();
        Map<String, String> analyzeItemMap = analyseItemList.stream().collect(Collectors.toMap(DtoAnalyzeItem::getId, DtoAnalyzeItem::getAnalyzeItemName));
        Map<String, List<DtoEvaluationValue>> evaluationGroup = sortEvaluationItemByName(evaluationList, analyzeItemMap);
        // 量纲
        List<String> dimensionIds = evaluationList.stream().map(DtoEvaluationValue::getDimensionId).distinct().collect(Collectors.toList());
        List<DtoDimension> dimensionList = dimensionRepository.findAll(dimensionIds);
        Map<String, DtoDimension> dimensionMap = dimensionList.stream().collect(Collectors.toMap(DtoDimension::getId, p -> p));

        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        List<DtoExpImpEvaluationCriteria> expImpEvaluationCriteria = new ArrayList<>();
        for (DtoEvaluationCriteria dtoEvaluationCriteria : evaluationCriteriaList) {
            // 实施日期
            String startTime = "";
            if (StringUtil.isNotNull(dtoEvaluationCriteria.getStartTime()) && dtoEvaluationCriteria.getStartTime().compareTo(date1753) != 0) {
                startTime = DateUtil.dateToString(dtoEvaluationCriteria.getStartTime(), DateUtil.YEAR);
            }
            // 检测类型
            String sampleTypeName = sampleTypeMap.getOrDefault(dtoEvaluationCriteria.getSampleTypeId(), "");
            // 根据评价等级循环
            List<DtoEvaluationLevel> evaluationLevels = levelGroup.getOrDefault(dtoEvaluationCriteria.getId(), new ArrayList<>());
            if (StringUtil.isNotEmpty(evaluationLevels)) {
                for (DtoEvaluationLevel evaluationLevel : evaluationLevels) {
                    List<DtoEvaluationValue> evaluationValues = evaluationGroup.getOrDefault(evaluationLevel.getId(), new ArrayList<>());
                    if (StringUtil.isNotEmpty(evaluationValues)) {
                        // 根据评价等级对应的因子循环
                        for (DtoEvaluationValue evaluationValue : evaluationValues) {
                            DtoDimension dimension = dimensionMap.getOrDefault(evaluationValue.getDimensionId(), null);
                            evaluationValue.setDimensionName(StringUtil.isNull(dimension) ? "" : dimension.getDimensionName());
                            // Map赋值
                            setValue(expImpEvaluationCriteria, analyzeItemMap, dtoEvaluationCriteria, startTime, sampleTypeName, evaluationLevel, evaluationValue);
                        }
                    } else {
                        // Map赋值
                        setValue(expImpEvaluationCriteria, analyzeItemMap, dtoEvaluationCriteria, startTime, sampleTypeName, evaluationLevel, null);
                    }
                }
            } else {
                // Map赋值
                setValue(expImpEvaluationCriteria, analyzeItemMap, dtoEvaluationCriteria, startTime, sampleTypeName, null, null);
            }
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpEvaluationCriteria.class, expImpEvaluationCriteria);
        List<DtoSampleType> finalTypeList = sampleTypeList.stream()
                .filter(p -> p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()))
                .sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed())
                .collect(Collectors.toList());
        String[] sampleTypeArray = finalTypeList.stream().map(DtoSampleType::getTypeName).toArray(String[]::new);
        importUtils.selectList(workBook, 4, 4, sampleTypeArray);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    public List<DtoEvaluationCriteria> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 获取参数
        //获取所有的检测类型数据
        List<DtoSampleType> dbSampleType = sampleTypeRepository.findAll();
        //获取所有的分析项目数据
        List<DtoAnalyzeItem> dbAnalyzeItem = analyzeItemRepository.findAll();
        //获取所有的评价标准
        List<DtoEvaluationCriteria> dbEvaluationCriteria = repository.findAll();
        //获取所有的评价等级数据
        List<DtoEvaluationLevel> dbEvaLevels = evaluationLevelRepository.findAll();
        //获取所有的评价等级数据
        List<DtoEvaluationValue> dbEvaValues = evaluationValueRepository.findAll();
        //获取所有的评价因子数据
        List<DtoEvaluationAnalyzeItem> dbEvaAnalyzeItem = evaluationAnalyzeItemRepository.findAll();
        List<DtoDimension> dimensionList = dimensionRepository.findAll();
        Map<String, List<DtoEvaluationAnalyzeItem>> evaAnaItemMapGroup = dbEvaAnalyzeItem.stream().collect(Collectors.groupingBy(DtoEvaluationAnalyzeItem::getEvaluationId));
        //endregion

        //region 线程变量处理
        // 处理评价等级全称
        importEvaluationCriteriaService.getLevelListFullName(dbEvaLevels);
        ImpModifyEvaluationCriteriaVerify verify = new ImpModifyEvaluationCriteriaVerify(dbAnalyzeItem, dbSampleType, dbEvaluationCriteria, dbEvaLevels, dbEvaValues,dimensionList);
        Date time1 = new Date();
        //获取excel导入数据
        ExcelImportResult<DtoExpImpEvaluationCriteria> importResult = getExcelData(verify, file, response);
        Date time2 = new Date();
        log.info("========================================获取校验后导入数据时间为：" + (time2.getTime() - time1.getTime()) + "ms============================");
        //endregion

        //region 存放需要添加的评价标准信息
        List<DtoEvaluationLevel> evaluationLevels = new ArrayList<>();
        List<DtoEvaluationValue> evaluationValues = new ArrayList<>();
        List<DtoEvaluationCriteria> evaluationCriteriaList = new ArrayList<>();
        List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems = new ArrayList<>();
        //endregion

        //获取excel导入数据
        List<DtoExpImpEvaluationCriteria> importList = importResult.getList();
        //移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getName()));

        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据或模板不正确，请检查后导入");
        }
        //处理数据
        this.importToEntity(importList, dbEvaluationCriteria, dbAnalyzeItem, dbEvaLevels, evaluationLevels, evaluationValues, evaluationCriteriaList, evaluationAnalyzeItems, evaAnaItemMapGroup, dbEvaValues);
        //添加数据
        Date time3 = new Date();
        importEvaluationCriteriaService.addData(evaluationLevels, evaluationAnalyzeItems, evaluationValues);
        Date time4 = new Date();
        log.info("===================================保存评价关联数据用时：" + (time4.getTime() - time3.getTime()) + "ms ===============================");
        this.addData(evaluationCriteriaList);
        Date time5 = new Date();
        log.info("===================================保存评价数据用时：" + (time5.getTime() - time4.getTime()) + "ms ===============================");
        return evaluationCriteriaList;
    }

    @Override
    @Transactional
    public void addData(List<DtoEvaluationCriteria> data) {
        if (StringUtil.isNotEmpty(data)) {
            //处理缓存的数据
            List<DtoEvaluationCriteria> save = repository.save(data);
            List<String> evaluationIds = data.stream().map(DtoEvaluationCriteria::getId).collect(Collectors.toList());
            //获取评价因子并按照评价标准id分组
            List<DtoEvaluationAnalyzeItem> evaAnaItems = evaluationAnalyzeItemRepository.findByEvaluationIdIn(evaluationIds);
            Map<String, List<DtoEvaluationAnalyzeItem>> evaAnaItemGroup = evaAnaItems.stream().collect(Collectors.groupingBy(DtoEvaluationAnalyzeItem::getEvaluationId));
            //获取评价等级按照标准id分组
            List<DtoEvaluationLevel> evaLevels = evaluationLevelRepository.findByEvaluationIdIn(evaluationIds);
            List<String> levelIds = evaLevels.stream().map(DtoEvaluationLevel::getId).collect(Collectors.toList());
            //获取评价限值数据并分组
            List<DtoEvaluationValue> values = evaluationValueRepository.findByLevelIdIn(levelIds);
            Map<String, List<DtoEvaluationValue>> valuesGroup = values.stream().collect(Collectors.groupingBy(DtoEvaluationValue::getLevelId));
            for (DtoEvaluationLevel evaLevel : evaLevels) {
                evaLevel.setEvaluationValue(valuesGroup.get(evaLevel.getId()));
            }
            Map<String, List<DtoEvaluationLevel>> evaLevelsGroup = evaLevels.stream().collect(Collectors.groupingBy(DtoEvaluationLevel::getEvaluationId));
            for (DtoEvaluationCriteria criteria : save) {
                criteria.setEvaluationAnalyzeItem(evaAnaItemGroup.get(criteria.getId()));
                criteria.setEvaluationLevel(evaLevelsGroup.get(criteria.getId()));
                evaluationCriteriaService.saveEvaluationCriteriaRedis(criteria);
            }
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpEvaluationCriteria> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public ExcelImportResult<DtoExpImpEvaluationCriteria> getExcelData(IExcelVerifyHandler<DtoExpImpEvaluationCriteria> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(verifyHandler);
        // 放大阈值
        ZipSecureFile.setMinInflateRatio(0.001);
        ExcelImportResult<DtoExpImpEvaluationCriteria> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoExpImpEvaluationCriteria.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "评价标准信息");
            PoiExcelUtils.downLoadExcel("评价标准导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * map 赋值
     *
     * @param evaluationCriteriaList 导出数据
     * @param analyzeItemMap         分析项目Map
     * @param dtoEvaluationCriteria  评价标准
     * @param startTime              实施时间
     * @param sampleTypeName         检测类型
     * @param evaluationLevel        评价等级
     * @param evaluationValue        评价数据
     */
    private void setValue(List<DtoExpImpEvaluationCriteria> evaluationCriteriaList,
                          Map<String, String> analyzeItemMap,
                          DtoEvaluationCriteria dtoEvaluationCriteria,
                          String startTime,
                          String sampleTypeName,
                          DtoEvaluationLevel evaluationLevel,
                          DtoEvaluationValue evaluationValue) {
        DtoExpImpEvaluationCriteria expImpEvaluationCriteria = new DtoExpImpEvaluationCriteria();
        expImpEvaluationCriteria.setId(dtoEvaluationCriteria.getId());
        expImpEvaluationCriteria.setName(dtoEvaluationCriteria.getName());
        expImpEvaluationCriteria.setCode(dtoEvaluationCriteria.getCode());
        expImpEvaluationCriteria.setStartTime(startTime);
        expImpEvaluationCriteria.setSampleTypeName(sampleTypeName);
        expImpEvaluationCriteria.setEvaluationLevel(StringUtil.isNotNull(evaluationLevel) ? evaluationLevel.getLevelFullName() : "");
        expImpEvaluationCriteria.setAnalyzeItemNames(StringUtil.isNotNull(evaluationValue) ? analyzeItemMap.getOrDefault(evaluationValue.getAnalyzeItemId(), "") : "");
        expImpEvaluationCriteria.setLowerLimitSymble(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getLowerLimitSymble() : "");
        expImpEvaluationCriteria.setLowerLimit(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getLowerLimit() : "");
        expImpEvaluationCriteria.setUpperLimitSymble(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getUpperLimitSymble() : "");
        expImpEvaluationCriteria.setUpperLimit(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getUpperLimit() : "");
        expImpEvaluationCriteria.setEvaluationValueRemark(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getRemark() : "");
        expImpEvaluationCriteria.setDimension(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getDimensionName() : "");
        expImpEvaluationCriteria.setEmissionRate(StringUtil.isNotNull(evaluationValue) ? evaluationValue.getEmissionRate() : "");
        evaluationCriteriaList.add(expImpEvaluationCriteria);
    }


    /**
     * 评价等级父子级排序
     *
     * @param evaluationLevelList 评价等级数据
     * @return 根据评价标准id分组后的Map数据
     */
    private Map<String, List<DtoEvaluationLevel>> sortParent(List<DtoEvaluationLevel> evaluationLevelList) {
        Map<String, List<DtoEvaluationLevel>> levelGroup = evaluationLevelList.stream().collect(Collectors.groupingBy(DtoEvaluationLevel::getEvaluationId));
        for (String evaluationId : levelGroup.keySet()) {
            List<DtoEvaluationLevel> evaluationLevels = levelGroup.get(evaluationId);
            // 筛选所有父级数据,并排序
            List<DtoEvaluationLevel> parentList = evaluationLevels.stream().filter(p -> p.getParentId().equals(UUIDHelper.GUID_EMPTY)
                    || StringUtil.isEmpty(p.getParentId()))
                    .sorted(Comparator.comparing(DtoEvaluationLevel::getOrderNum).reversed())
                    .collect(Collectors.toList());
            List<DtoEvaluationLevel> finalList = new ArrayList<>();
            for (DtoEvaluationLevel parent : parentList) {
                finalList.add(parent);
                // 递归调用评价等级子集排序方法
                finalList.addAll(sortChild(parent.getId(), evaluationLevels));
            }
            levelGroup.put(evaluationId, finalList);
        }
        return levelGroup;
    }

    /**
     * （递归）评价等级子集排序
     *
     * @param parentId         父级id
     * @param evaluationLevels 所有数据
     * @return 排序后数据
     */
    private List<DtoEvaluationLevel> sortChild(String parentId, List<DtoEvaluationLevel> evaluationLevels) {
        List<DtoEvaluationLevel> sortedList = new ArrayList<>();
        // 获取当前父级下的子集
        List<DtoEvaluationLevel> sonList = evaluationLevels.stream().filter(p -> parentId.equals(p.getParentId()))
                .sorted(Comparator.comparing(DtoEvaluationLevel::getOrderNum).reversed()).collect(Collectors.toList());
        for (DtoEvaluationLevel son : sonList) {
            sortedList.add(son);
            sortedList.addAll(sortChild(son.getId(), evaluationLevels));
        }
        return sortedList;
    }

    /**
     * 评价因子按照分析因子名称排序
     *
     * @param evaluationList 评价因子数据
     * @param analyzeItemMap 分析因子Map
     * @return 评价因子根据条件分组
     */
    public Map<String, List<DtoEvaluationValue>> sortEvaluationItemByName(List<DtoEvaluationValue> evaluationList,
                                                                          Map<String, String> analyzeItemMap) {

        evaluationList = evaluationList.stream().peek(p -> p.setAnalyzeItemName(analyzeItemMap.getOrDefault(p.getAnalyzeItemId(), ""))).collect(Collectors.toList());

        Map<String, List<DtoEvaluationValue>> evaluationGroup = evaluationList.stream().collect(Collectors.groupingBy(DtoEvaluationValue::getLevelId));
        for (String key : evaluationGroup.keySet()) {
            List<DtoEvaluationValue> evaluationValueList = evaluationGroup.get(key);
            evaluationValueList = evaluationValueList.stream().sorted(Comparator.comparing(DtoEvaluationValue::getAnalyzeItemName)).collect(Collectors.toList());
            evaluationGroup.put(key, evaluationValueList);
        }
        return evaluationGroup;
    }

    /**
     * 处理导入的数据
     *
     * @param importList             导入的数据
     * @param dbEvaluationCriteria   数据库中的所有评价标准
     * @param dbAnalyzeItem          数据库中的所有分析因子
     * @param dbEvaLevels            数据库中所有的评价等级
     * @param evaluationLevels       处理后需要添加的评价等级
     * @param evaluationValues       处理后需要添加的评价限值
     * @param evaluationCriteriaList 处理后需要添加的评价标准
     * @param evaluationAnalyzeItems 处理后需要添加的评价因子
     * @param evaAnaItemMapGroup     评价分组
     */
    private void importToEntity(List<DtoExpImpEvaluationCriteria> importList, List<DtoEvaluationCriteria> dbEvaluationCriteria,
                                List<DtoAnalyzeItem> dbAnalyzeItem, List<DtoEvaluationLevel> dbEvaLevels, List<DtoEvaluationLevel> evaluationLevels,
                                List<DtoEvaluationValue> evaluationValues, List<DtoEvaluationCriteria> evaluationCriteriaList,
                                List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems, Map<String, List<DtoEvaluationAnalyzeItem>> evaAnaItemMapGroup, List<DtoEvaluationValue> dbEvaValues) {
        //将导入的数据分类（已经有评价标准和评价等级的，没有评价等级的）
        List<DtoExpImpEvaluationCriteria> hasEvaLevelList = importList.stream().filter(p -> StringUtil.isNotEmpty(p.getEvaluationLevelId())).collect(Collectors.toList());
        List<DtoExpImpEvaluationCriteria> noEvaLevelList = importList.stream().filter(p -> !StringUtil.isNotEmpty(p.getEvaluationLevelId())).collect(Collectors.toList());

        // 评价等级根据评价标准id进行分组
        Map<String, List<DtoEvaluationLevel>> levelGroup = dbEvaLevels.stream().collect(Collectors.groupingBy(DtoEvaluationLevel::getEvaluationId));
        //将导入的数据先按照标准编码进行分组
        Set<String> codes = importList.stream().map(DtoExpImpEvaluationCriteria::getCode).collect(Collectors.toSet());
        Map<String, List<DtoExpImpEvaluationCriteria>> listOfCode = noEvaLevelList.stream().collect(Collectors.groupingBy(DtoExpImpEvaluationCriteria::getCode));
        //获取评价标准名称
        for (String code : codes) {
            //创建评价标准
            DtoEvaluationCriteria criteria = new DtoEvaluationCriteria();
            //根据评价编码判断数据库中是否存在此评价标准，如果存在，则获取到此评价标准
            Optional<DtoEvaluationCriteria> dbEvaCriteriaOp = dbEvaluationCriteria.stream().filter(p -> code.equals(p.getCode())).findFirst();
            if (dbEvaCriteriaOp.isPresent()) {
                criteria = dbEvaCriteriaOp.get();
            }
            //获取标准编码下的数据
            String evaCriteriaId = criteria.getId();
            List<DtoEvaluationLevel> levelsOfEva = levelGroup.get(evaCriteriaId);
            // 没有评价等级的数据
            List<DtoExpImpEvaluationCriteria> criteriaList = listOfCode.get(code);
            if (StringUtil.isEmpty(criteriaList)) {
                continue;
            }
            criteriaList.forEach(p -> {
                p.setEvaluationCriteriaId(evaCriteriaId);
                p.setId(evaCriteriaId);
            });
            //设置评价标准基本信息
            if (StringUtil.isEmpty(criteria.getName())) {
                BeanUtils.copyProperties(criteriaList.get(0), criteria);
                criteria.setStartTime(importUtils.stringToDateAllFormat(criteriaList.get(0).getStartTime()));
                criteria.setStatus(EnumBase.EnumEvaluateCriteriaStatus.有效.getValue());
                criteria.setCategoryId(LimConstants.ImportConstants.EVALUATION_TYPE_GB);
            }
            //获取到此评价标准下所有的分析因子，筛选出需要新增的数据，并添加数据
            List<String> analyzeItemNames = criteriaList.stream().map(DtoExpImpEvaluationCriteria::getAnalyzeItemNames).collect(Collectors.toList());
            List<String> anaItemIds = dbAnalyzeItem.stream().filter(p -> analyzeItemNames.contains(p.getAnalyzeItemName())).map(DtoAnalyzeItem::getId).collect(Collectors.toList());
            List<DtoEvaluationAnalyzeItem> anaItemOfEva = evaAnaItemMapGroup.get(evaCriteriaId);
            if (StringUtil.isNotEmpty(anaItemOfEva)) {
                List<String> dbAnaItemOfEva = anaItemOfEva.stream().map(DtoEvaluationAnalyzeItem::getAnalyzeItemId).collect(Collectors.toList());
                anaItemIds = anaItemIds.stream().filter(p -> !dbAnaItemOfEva.contains(p)).collect(Collectors.toList());
            }
            for (String anaItemId : anaItemIds) {
                //创建评价标准与评价因子关系记录数据
                DtoEvaluationAnalyzeItem evaluationAnalyzeItem = new DtoEvaluationAnalyzeItem();
                evaluationAnalyzeItem.setEvaluationId(criteria.getId());
                evaluationAnalyzeItem.setAnalyzeItemId(anaItemId);
                evaluationAnalyzeItems.add(evaluationAnalyzeItem);
            }
            //处理评价等级数据
            //获取此评价标准下的所有导入的评价等级,保证导入的顺序
            List<String> LevelsOfCriteria = criteriaList.stream().map(DtoExpImpEvaluationCriteria::getEvaluationLevel).distinct().collect(Collectors.toList());
            Collections.reverse(LevelsOfCriteria);
            LevelsOfCriteria.removeIf(StringUtil::isNull);
            //创建评价等级数据
            Set<DtoEvaluationLevel> levels = new HashSet<>();
            Integer levelCode = 0;
            for (String levelName : LevelsOfCriteria) {
                LinkedList<DtoEvaluationLevel> sonList = new LinkedList<>();
                DtoEvaluationLevel fuLevel = new DtoEvaluationLevel();
                //如果有“-”,则存在多个条件项
                fuLevel.setEvaluationId(criteria.getId());
                levelCode = setLevelCode(levelCode, levels, levelsOfEva);
                fuLevel.setOrderNum(levelCode);
                if (levelName.contains(LimConstants.ImportConstants.ENGLISH_SPLIT_CHAR)) {
//                    String replaceLevelName = levelName.replace(LimConstants.ImportConstants.CHINESE_SPLIT_CHAR, LimConstants.ImportConstants.ENGLISH_SPLIT_CHAR);
                    //处理条件项
                    List<String> allLevel = Stream.of(levelName.split(LimConstants.ImportConstants.ENGLISH_SPLIT_CHAR)).collect(Collectors.toList());
                    fuLevel.setName(allLevel.get(0));
                    fuLevel.setLevelFullName(allLevel.get(0));
                    Optional<DtoEvaluationLevel> levelOp = levels.stream().filter(p -> allLevel.get(0).equals(p.getLevelFullName())).findFirst();
                    if (levelOp.isPresent()) {
                        fuLevel = levelOp.get();
                    }
                    Optional<DtoEvaluationLevel> levelOpOfData = dbEvaLevels.stream().filter(p -> allLevel.get(0).equals(p.getLevelFullName()) && evaCriteriaId.equals(p.getEvaluationId())).findFirst();
                    if (levelOpOfData.isPresent()) {
                        fuLevel = levelOpOfData.get();
                    }
                    LinkedList<String> sonLevels = new LinkedList<>(allLevel);
                    //处理评价等级数据
                    this.handleLevels(sonList, sonLevels, dbEvaLevels, fuLevel, criteria, levels, levelCode);
                } else {
                    fuLevel.setName(levelName);
                    fuLevel.setLevelFullName(levelName);
                    Optional<DtoEvaluationLevel> levelOp = levels.stream().filter(p -> levelName.equals(p.getName())).findFirst();
                    if (levelOp.isPresent()) {
                        fuLevel = levelOp.get();
                    }
                    Optional<DtoEvaluationLevel> levelOpOfData = dbEvaLevels.stream().filter(p -> levelName.equals(p.getName()) && evaCriteriaId.equals(p.getEvaluationId())).findFirst();
                    if (levelOpOfData.isPresent()) {
                        fuLevel = levelOpOfData.get();
                    }
                }
                sonList.addFirst(fuLevel);
                levels.addAll(sonList);
            }
            evaluationLevels.addAll(levels);
            evaluationCriteriaList.add(criteria);
        }
        //处理评价限值
        this.handleValues(noEvaLevelList, evaluationCriteriaList, evaluationLevels, evaluationValues, dbAnalyzeItem);
        //处理以及存在评级标准以及评价等级的数据
        this.handleValuesWithLevel(hasEvaLevelList, evaAnaItemMapGroup, evaluationAnalyzeItems, evaluationValues, dbEvaValues);
    }

    /**
     * 处理系统中不存在的评价标准的评价限值
     *
     * @param noEvaLevelList         不存在评价等级的导入数据
     * @param evaluationCriteriaList 需要保存的评价标准
     * @param evaluationLevels       需要保存的评价等级
     * @param evaluationValues       需要保存的评价限值
     * @param dbAnalyzeItem          所有的评价因子数据
     */
    private void handleValues(List<DtoExpImpEvaluationCriteria> noEvaLevelList, List<DtoEvaluationCriteria> evaluationCriteriaList,
                              List<DtoEvaluationLevel> evaluationLevels, List<DtoEvaluationValue> evaluationValues, List<DtoAnalyzeItem> dbAnalyzeItem) {
        //评价标准按id分组
        Map<String, List<DtoEvaluationCriteria>> evaluationGroup = evaluationCriteriaList.stream().collect(Collectors.groupingBy(DtoEvaluationCriteria::getId));
        //评价等级按评价标准id分组
        Map<String, List<DtoEvaluationLevel>> levelGroup = evaluationLevels.stream().collect(Collectors.groupingBy(DtoEvaluationLevel::getEvaluationId));
        //分析因子按id分组
        Map<String, List<DtoAnalyzeItem>> anaItemGroup = dbAnalyzeItem.stream().collect(Collectors.groupingBy(DtoAnalyzeItem::getId));
        //处理评价限值
        for (DtoExpImpEvaluationCriteria evaluationValue : noEvaLevelList) {
            DtoEvaluationValue value = new DtoEvaluationValue();
            String evaluationId = evaluationValue.getEvaluationCriteriaId();
            String levelName = evaluationValue.getEvaluationLevel();
            String analyzeItemId = evaluationValue.getAnalyzeItemId();
            //根据标准名称找到评价标准，并赋值评价标准Id
            List<DtoEvaluationCriteria> criteriaList = evaluationGroup.get(evaluationId);
            if (StringUtil.isNotEmpty(criteriaList)) {
                DtoEvaluationCriteria criteria = criteriaList.get(0);
                value.setEvaluationId(criteria.getId());
            }
            //赋值评价等级Id
            List<DtoEvaluationLevel> levelsOfEvaId = levelGroup.get(evaluationId);
            if (StringUtil.isNotEmpty(levelsOfEvaId) && StringUtil.isNotEmpty(levelName)) {
                String replace = levelName.replace(LimConstants.ImportConstants.CHINESE_SPLIT_CHAR, LimConstants.ImportConstants.ENGLISH_SPLIT_CHAR);
                Optional<DtoEvaluationLevel> levelOptional = levelsOfEvaId.stream().filter(p -> replace.equals(p.getLevelFullName())).findFirst();
                value.setLevelId(levelOptional.isPresent() ? levelOptional.get().getId() : UUIDHelper.GUID_EMPTY);
            }
            //赋值评价因子Id
            List<DtoAnalyzeItem> analyzeItems = anaItemGroup.get(analyzeItemId);
            if (StringUtil.isNotEmpty(analyzeItems)) {
                DtoAnalyzeItem anaItem = dbAnalyzeItem.get(0);
                value.setAnalyzeItemId(anaItem.getId());
            }
            //赋值评价限值
            BeanUtils.copyProperties(evaluationValue, value);
            value.setId(UUIDHelper.NewID());
            value.setRemark(evaluationValue.getEvaluationValueRemark());
            value.setEmissionRate(evaluationValue.getEmissionRate());
            evaluationValues.add(value);
        }
    }

    /**
     * 处理以及存在评级标准以及评价等级的数据
     *
     * @param hasEvaLevelList        评价等级已存在的数据
     * @param evaAnaItemMapGroup     按照评价标准id分组的评价因子数据
     * @param evaluationAnalyzeItems 需要保存的评价因子数据
     * @param evaluationValues       需要保存的评价限值数据
     */
    private void handleValuesWithLevel(List<DtoExpImpEvaluationCriteria> hasEvaLevelList, Map<String, List<DtoEvaluationAnalyzeItem>> evaAnaItemMapGroup,
                                       List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems, List<DtoEvaluationValue> evaluationValues, List<DtoEvaluationValue> dbEvaValues) {
        for (DtoExpImpEvaluationCriteria importDto : hasEvaLevelList) {

            //处理已经存在评价标准和评价条件项的数据
            List<DtoEvaluationAnalyzeItem> evaAnaItems = evaAnaItemMapGroup.get(importDto.getEvaluationCriteriaId());
            if (StringUtil.isNotEmpty(evaAnaItems)) {
                List<DtoEvaluationAnalyzeItem> evaAnaItemsIsExist = evaAnaItems.stream()
                        .filter(p -> p.getAnalyzeItemId().equals(importDto.getAnalyzeItemId()))
                        .collect(Collectors.toList());
                if (StringUtil.isEmpty(evaAnaItemsIsExist)) {
                    evaAnaItemsIsExist = evaluationAnalyzeItems.stream()
                            .filter(p -> p.getAnalyzeItemId().equals(importDto.getAnalyzeItemId()))
                            .collect(Collectors.toList());
                    if (StringUtil.isEmpty(evaAnaItemsIsExist) && StringUtil.isNotEmpty(importDto.getAnalyzeItemId())) {
                        DtoEvaluationAnalyzeItem evaluationAnalyzeItem = new DtoEvaluationAnalyzeItem();
                        evaluationAnalyzeItem.setEvaluationId(importDto.getEvaluationCriteriaId());
                        evaluationAnalyzeItem.setAnalyzeItemId(importDto.getAnalyzeItemId());
                        evaluationAnalyzeItems.add(evaluationAnalyzeItem);
                    }
                }
            } else {
                List<DtoEvaluationAnalyzeItem> evaAnaItemsIsExist = evaluationAnalyzeItems.stream()
                        .filter(p -> importDto.getAnalyzeItemId().equals(p.getAnalyzeItemId()))
                        .collect(Collectors.toList());
                if (StringUtil.isEmpty(evaAnaItemsIsExist)) {
                    DtoEvaluationAnalyzeItem evaluationAnalyzeItem = new DtoEvaluationAnalyzeItem();
                    evaluationAnalyzeItem.setEvaluationId(importDto.getEvaluationCriteriaId());
                    evaluationAnalyzeItem.setAnalyzeItemId(importDto.getAnalyzeItemId());
                    evaluationAnalyzeItems.add(evaluationAnalyzeItem);
                }
            }
            //赋值评价限值
            DtoEvaluationValue value = new DtoEvaluationValue();
            Optional<DtoEvaluationValue> evaluationValueOptional = dbEvaValues.stream()
                    .filter(p -> importDto.getId().equals(p.getEvaluationId())
                            && importDto.getEvaluationLevelId().equals(p.getLevelId())
                            && p.getAnalyzeItemId().equals(importDto.getAnalyzeItemId())).findFirst();
            if (evaluationValueOptional.isPresent()) {
                String id = evaluationValueOptional.get().getId();
                value = evaluationValueOptional.get();
                BeanUtils.copyProperties(importDto, value);
                value.setId(id);
            } else {
                BeanUtils.copyProperties(importDto, value);
                value.setId(UUIDHelper.NewID());
            }
            value.setRemark(importDto.getEvaluationValueRemark());
            value.setEvaluationId(importDto.getEvaluationCriteriaId());
            value.setLevelId(importDto.getEvaluationLevelId());
            evaluationValues.add(value);
        }
    }


    /**
     * 处理评价等级
     *
     * @param sonList   存放评价标准下的评价等级
     * @param sonLevels 导入的评价等级
     * @param fuLevel   第一层评价等级
     * @param criteria  评价标准
     */
    private void handleLevels(LinkedList<DtoEvaluationLevel> sonList, LinkedList<String> sonLevels, List<DtoEvaluationLevel> dbLevels,
                              DtoEvaluationLevel fuLevel, DtoEvaluationCriteria criteria, Set<DtoEvaluationLevel> levelAll, Integer levelCode) {
        sonLevels.removeFirst();
        StringBuilder levelFullName = new StringBuilder(fuLevel.getName());
        for (String sonLevel : sonLevels) {
            levelCode++;
            if (StringUtil.isNotEmpty(sonList.stream().filter(p -> sonLevel.equals(p.getName()) && criteria.getId().equals(p.getEvaluationId())).collect(Collectors.toList()))) {
                continue;
            }
            if (StringUtil.isNotEmpty(sonList)) {
                DtoEvaluationLevel levelSon = getLevelSon(sonList.getLast(), sonLevel, criteria.getId(), dbLevels, levelAll, levelFullName, levelCode);
                sonList.addLast(levelSon);

            } else {
                DtoEvaluationLevel levelSon = getLevelSon(fuLevel, sonLevel, criteria.getId(), dbLevels, levelAll, levelFullName, levelCode);
                sonList.addLast(levelSon);
            }
        }
    }

    /**
     * 获取评价登记实体
     *
     * @param level        上一层评价等级
     * @param levelName    需要添加的评价等级名称
     * @param evaluationId 添加的评价等级对于的评价标准
     * @param dbLevels     数据库中的评价等级
     * @return 需要保存的评价等级
     */
    private DtoEvaluationLevel getLevelSon(DtoEvaluationLevel level, String levelName, String evaluationId,
                                           List<DtoEvaluationLevel> dbLevels, Set<DtoEvaluationLevel> allLevels,
                                           StringBuilder levelFullName, Integer levelCode) {
        List<DtoEvaluationLevel> dvLevelsOfEva = dbLevels.stream().filter(p -> evaluationId.equals(p.getEvaluationId())).collect(Collectors.toList());
        DtoEvaluationLevel levelSon = new DtoEvaluationLevel();
        levelFullName.append(LimConstants.ImportConstants.ENGLISH_SPLIT_CHAR).append(levelName);
        levelSon.setEvaluationId(evaluationId);
        levelSon.setParentId(level.getId());
        levelSon.setName(levelName);
        levelSon.setLevelFullName(levelFullName.toString());
        levelCode = this.setLevelCode(levelCode, dvLevelsOfEva, allLevels);
        levelSon.setOrderNum(levelCode);
        levelSon = this.getExistLevel(levelSon, level, levelName, evaluationId, allLevels);
        levelSon = this.getExistLevel(levelSon, level, levelName, evaluationId, dbLevels);
        return levelSon;
    }

    /**
     * 设置条件编码跳过重复
     *
     * @param levelCode   条件编码
     * @param levelList   需要判断的集合
     * @param levelsOfEva 临时集合
     * @return 条件编码
     */
    private Integer setLevelCode(Integer levelCode, Collection<DtoEvaluationLevel> levelList, Collection<DtoEvaluationLevel> levelsOfEva) {
        Integer levelCodeTemp = levelCode;
        if (StringUtil.isNotEmpty(levelList)) {
            for (DtoEvaluationLevel dbLevel : levelList) {
                if (dbLevel.getOrderNum().equals(levelCode)) {
                    levelCodeTemp++;
                }
            }
        }
        if (StringUtil.isNotEmpty(levelsOfEva)) {
            for (DtoEvaluationLevel dbLevel : levelsOfEva) {
                if (dbLevel.getOrderNum().equals(levelCode)) {
                    levelCodeTemp++;
                }
            }
        }
        if (levelCodeTemp.equals(levelCode)) {
            return levelCodeTemp;
        }
        levelCodeTemp = setLevelCode(levelCodeTemp, levelList, levelsOfEva);
        return levelCodeTemp;
    }

    /**
     * 处理中层数据，如果已经存在，则赋值为已创建的数据
     *
     * @param levelSon     新增的评价等级
     * @param fatherLevel  上层评价等级
     * @param levelName    新建的评价等级名称
     * @param evaluationId 关联的评价标准id
     * @param levelList    需要判断集合
     * @return 处理完成的数据
     */
    private DtoEvaluationLevel getExistLevel(DtoEvaluationLevel levelSon, DtoEvaluationLevel fatherLevel, String levelName,
                                             String evaluationId, Collection<DtoEvaluationLevel> levelList) {
        //处理层级中已存在的评价等级
        Optional<DtoEvaluationLevel> levelOp = levelList.stream().filter(p -> evaluationId.equals(p.getEvaluationId())
                && levelName.equals(p.getName()) && fatherLevel.getId().equals(p.getParentId())).findFirst();
        if (levelOp.isPresent()) {
            levelSon = levelOp.get();
        }
        return levelSon;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setEvaluationValueRepository(EvaluationValueRepository evaluationValueRepository) {
        this.evaluationValueRepository = evaluationValueRepository;
    }

    @Autowired
    public void setEvaluationLevelRepository(EvaluationLevelRepository evaluationLevelRepository) {
        this.evaluationLevelRepository = evaluationLevelRepository;
    }

    @Autowired
    public void setImportEvaluationCriteriaService(ImportEvaluationCriteriaService importEvaluationCriteriaService) {
        this.importEvaluationCriteriaService = importEvaluationCriteriaService;
    }

    @Autowired
    public void setEvaluationCriteriaService(EvaluationCriteriaService evaluationCriteriaService) {
        this.evaluationCriteriaService = evaluationCriteriaService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setEvaluationAnalyzeItemRepository(EvaluationAnalyzeItemRepository evaluationAnalyzeItemRepository) {
        this.evaluationAnalyzeItemRepository = evaluationAnalyzeItemRepository;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }
}
