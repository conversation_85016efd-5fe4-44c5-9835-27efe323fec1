package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.lim.repository.rcc.ItemRelationRepository;
import com.sinoyd.lims.lim.service.ItemRelationParamsService;
import com.sinoyd.lims.lim.service.ItemRelationService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * ItemRelation操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @Service
public class ItemRelationServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoItemRelation,String, ItemRelationRepository> implements ItemRelationService {

    private ItemRelationParamsService itemRelationParamsService;

    @Override
    public void findByPage(PageBean<DtoItemRelation> pb, BaseCriteria itemRelationCriteria) {
        pb.setEntityName("DtoItemRelation a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, itemRelationCriteria);
    }

    @Transactional
    @Override
    public DtoItemRelation save(DtoItemRelation itemRelation) {
        DtoItemRelation finalItemRelation = super.save(itemRelation);
        List<DtoItemRelationParams> itemRelationParamsList = itemRelation.getItemRelationParamsList();
        itemRelationParamsList.forEach(p -> {
            p.setRelationId(finalItemRelation.getId());
        });
        itemRelationParamsService.save(itemRelationParamsList);
        return finalItemRelation;
    }

    @Override
    public DtoItemRelation findOne(String id){
        DtoItemRelation itemRelation = super.findOne(id);
        List<DtoItemRelationParams> itemRelationParamsList = itemRelationParamsService.findByRelationIds(Collections.singleton(id));
        itemRelation.setItemRelationParamsList(itemRelationParamsList);
        return itemRelation;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> itemRelationParamsIds = itemRelationParamsService.findByRelationIds(ids)
                .stream().map(DtoItemRelationParams::getId).collect(Collectors.toList());
        if (itemRelationParamsIds.size() > 0) {
            itemRelationParamsService.logicDeleteById(itemRelationParamsIds);
        }
        return super.logicDeleteById(ids);
    }

    @Autowired
    @Lazy
    public void setItemRelationParamsService(ItemRelationParamsService itemRelationParamsService) {
        this.itemRelationParamsService = itemRelationParamsService;
    }
}