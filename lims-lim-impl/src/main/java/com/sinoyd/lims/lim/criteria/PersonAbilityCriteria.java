package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 人员检测能力查询条件
 * <AUTHOR>
 * @version v1.0.0 2019/5/6
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonAbilityCriteria extends BaseCriteria {

    /**
     * 样品类型id
     */
    private String sampleTypeId;
    /**
     * 默认获取当前人员的id
     */
    private String personId;
    /**
     * 关键字（分析项目、分析方法、测试项目拼音/全拼）
     */
    private String key;

    /**
     * 上岗证id
     */
    private String personCertId;

    /**
     * 关分析项目，化学符号
     */
    private String analyzeItem;

    /**
     * 分析项目，拼音
     */
    private String analyzeItemKey;

    /**
     * 分析方法，标准编号
     */
    private String analyzeMethod;

    /**
     * 状态 0：正常 1：即将过期 2：已过期
     */
    private String state;

    /**
     * 对比时间，用于时间状态查询使用
     */
    private Date compareDate;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.personCertId = pt.id");
        if (StringUtils.isNotNullAndEmpty(personId) && !UUIDHelper.GUID_EMPTY.equals(this.personId)) {
            condition.append(" and (p.personId = :personId)");
            values.put("personId", this.personId);
        }
        // 上岗证
        if (StringUtils.isNotNullAndEmpty(personCertId) && !UUIDHelper.GUID_EMPTY.equals(this.personCertId)) {
            condition.append(" and (p.personCertId = :personCertId)");
            values.put("personCertId", this.personCertId);
        }
        // 状态查询
        if (StringUtil.isNotEmpty(state) && !"-1".equals(state)) {
            // 正常状态
            if ("0".equals(state)) {
                condition.append(" and p.certEffectiveTime >= :compareDate ");
            } else {
                condition.append(" and p.certEffectiveTime < :compareDate");
                // 即将过期
                if ("1".equals(state)) {
                    condition.append(" and p.certEffectiveTime >= :nowDate ");
                    values.put("nowDate", DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
                }
            }
            values.put("compareDate", this.compareDate);
        }

        return condition.toString();
    }
}