package com.sinoyd.lims.lim.service.sync;

import com.jsoniter.output.JsonStream;
import com.sinoyd.base.service.impl.SyncDataServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import org.springframework.stereotype.Service;

/**
 * 测试项目拓展同步业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/3/14
 */
@Service
public class TestExpandSyncServiceImpl extends SyncDataServiceImpl<DtoTestExpand, String, TestExpandRepository> {

    @Override
    public void saveRedis(DtoTestExpand dtoTestExpand) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.getValue());
        redisTemplate.opsForHash().put(key, dtoTestExpand.getTestId(), JsonStream.serialize(dtoTestExpand));
    }

    @Override
    public boolean needPrintMessage() {
        return false;
    }
}
