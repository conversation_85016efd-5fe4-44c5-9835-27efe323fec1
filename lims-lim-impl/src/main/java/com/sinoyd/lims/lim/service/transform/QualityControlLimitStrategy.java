package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.QualityControlLimitMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.verify.TransformQualityControlLimitVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  测试项目数据迁移质控限值导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(80)
public class QualityControlLimitStrategy implements TransformImportStrategy{
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 4;

    private QualityControlLimitRepository qualityControlLimitRepository;

    private TransformQualityControlLimitVerifyHandler transformQualityControlLimitVerifyHandler;

    private QualityControlLimitMapper qualityControlLimitMapper;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception{
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformQualityControlLimitVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportQualityControlLimit> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportQualityControlLimit.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoQualityControlLimit> waitSaveList = new ArrayList<>();
        buildRightData(result,waitSaveList);
        //数据保存
        qualityControlLimitRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        List<DtoExportQualityControlLimit> qualityControlLimitList = testDependentData.getQualityControlLimitList();
        if (StringUtil.isNotEmpty(qualityControlLimitList)) {
            List<DtoExportTest> testSet = exportData.getTestList();
            List<String> exportTestIds = StringUtil.isNotEmpty(testSet) ? testSet.stream().map(DtoExportTest::getId).collect(Collectors.toList()) : new ArrayList<>();
            // 导入数据处理，类型转换
            List<DtoQualityControlLimit> dtoQualityControlLimits = qualityControlLimitList.stream().filter(p -> !exportTestIds.contains(p.getTestId()))
                    .map(p -> {
                        DtoQualityControlLimit dtoQualityControlLimit = qualityControlLimitMapper.toDtoQualityControlLimit(p);
                        // 获取替代物绑定数据
                        DtoBaseData substitute = substituteMap.getOrDefault(p.getSubstituteName(), null);
                        if (StringUtil.isNotNull(substitute)) {
                            dtoQualityControlLimit.setSubstituteId(substitute.getTargetId());
                            dtoQualityControlLimit.setSubstituteName(substitute.getTargetName());
                        }
                        return dtoQualityControlLimit;
                    }).collect(Collectors.toList());
            importTestTemp.setQualityControlLimitTemps(dtoQualityControlLimits);

            // 导出数据处理
            if (StringUtil.isNotEmpty(exportTestIds)){
                List<DtoExportQualityControlLimit> qualityControlLimits = qualityControlLimitList.stream().filter(p -> exportTestIds.contains(p.getTestId())).collect(Collectors.toList());
                exportData.setQualityControlLimitList(qualityControlLimits);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoQualityControlLimit> qualityControlLimitTemps = importTestTemp.getQualityControlLimitTemps();
        if (StringUtil.isNotEmpty(qualityControlLimitTemps)) {
            deletedOldData(qualityControlLimitTemps);
            //已同步记录数
            int i = 0;
            for (DtoQualityControlLimit qualityControlLimit : qualityControlLimitTemps) {
                qualityControlLimitRepository.save(qualityControlLimit);
                webSocketServer.sendMessage(getMessage(qualityControlLimitTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(qualityControlLimitTemps.size(),0));
        }
    }

    /**
     * 删除相同的老的质控限值数据
     *
     * @param qualityControlLimitTemps 导入数据集合
     */
    private void deletedOldData(List<DtoQualityControlLimit> qualityControlLimitTemps) {
        List<String> testIds = qualityControlLimitTemps.stream().map(DtoQualityControlLimit::getTestId).distinct().collect(Collectors.toList());
        List<DtoQualityControlLimit> qualityControlLimits = qualityControlLimitRepository.findByTestIdIn(testIds);
        if (StringUtil.isNotEmpty(qualityControlLimits)){
            List<DtoQualityControlLimit> deletes = new ArrayList<>();
            for (DtoQualityControlLimit qualityControlLimit : qualityControlLimits) {
                if (qualityControlLimitTemps.stream().noneMatch(p-> p.getId().equals(qualityControlLimit.getId())
                && p.getTestId().equals(qualityControlLimit.getTestId()))){
                    deletes.add(qualityControlLimit);
                }
            }
            qualityControlLimitRepository.delete(deletes);
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.质控限值表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.质控限值表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.质控限值表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportQualityControlLimit> exportQualityControlLimits = testDependentData.getQualityControlLimitList();
        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> qualityControlLimitCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(exportQualityControlLimits)){
            List<String> testIds = exportQualityControlLimits.stream().map(DtoExportQualityControlLimit::getTestId).distinct().collect(Collectors.toList());
            List<DtoQualityControlLimit> qualityControlLimits = qualityControlLimitRepository.findByTestIdIn(testIds);
            List<DtoExportTest> testList = testDependentData.getTestList();
            for (String testId : testIds) {
                List<DtoQualityControlLimit> limits = qualityControlLimits.stream()
                        .filter(p -> p.getTestId().equals(testId)).collect(Collectors.toList());

                DtoExportTest exportTest = testList.stream().filter(p -> p.getId().equals(testId)).findFirst().orElse(new DtoExportTest());
                DtoDataCheck dtoDataCheck = new DtoDataCheck();
                Map<String, Object> otherField = new HashMap<>();
                otherField.put("redAnalyzeItemName", exportTest.getRedAnalyzeItemName());
                otherField.put("redAnalyzeMethodName", exportTest.getRedAnalyzeMethodName());
                otherField.put("redCountryStandard", exportTest.getRedCountryStandard());
                otherField.put("sampleType", exportTest.getSampleTypeId());
                dtoDataCheck.setOtherField(otherField);
                if (StringUtil.isNotEmpty(limits)){
                    dtoDataCheck.setType(BASE_DATA_TYPE[1]);
                }else {
                    dtoDataCheck.setType(BASE_DATA_TYPE[0]);
                }
                qualityControlLimitCheckList.add(dtoDataCheck);
            }
        }
        List<DtoDataCheck> existsList = qualityControlLimitCheckList.stream().filter(p -> BASE_DATA_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExistsList = qualityControlLimitCheckList.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.质控限值表.getCheckItem(),
                BASE_DATA_TYPE[1], existsList.size(), "先删除原有配置，再执行新增操作，导入时将进行插入。",
                existsList));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.质控限值表.getCheckItem(),
                BASE_DATA_TYPE[0], noExistsList.size(), "新增质控限制配置，导入时将进行插入。",
                noExistsList));

        return importChecks;
    }

    /**
     *  校验容器初始化
     */
    private void handleInit(){
        transformQualityControlLimitVerifyHandler = new TransformQualityControlLimitVerifyHandler();
        transformQualityControlLimitVerifyHandler.setRepoDataList(qualityControlLimitRepository.findAll());
        transformQualityControlLimitVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     * @param result         导入结果集
     * @param waitSaveList   待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportQualityControlLimit> result, List<DtoQualityControlLimit> waitSaveList) {
        List<DtoExportQualityControlLimit> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportQualityControlLimit exportQualityControlLimit: importList) {
            DtoQualityControlLimit dtoQualityControlLimit = new DtoQualityControlLimit();
            BeanUtils.copyProperties(exportQualityControlLimit,dtoQualityControlLimit);
            waitSaveList.add(dtoQualityControlLimit);
        }
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }
    @Autowired
    public void setQualityControlLimitMapper(QualityControlLimitMapper qualityControlLimitMapper) {
        this.qualityControlLimitMapper = qualityControlLimitMapper;
    }
}
