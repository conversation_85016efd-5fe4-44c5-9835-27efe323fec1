package com.sinoyd.lims.lim.service.rcc;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.ISyncDataService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumRequestTarget;
import com.sinoyd.commons.vo.QueryRequestParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 检测类型资源中心服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/19
 */
@Service
public class RccSampleTypeServiceImpl extends RemoteRequestBaseServiceImpl implements IRccSampleTypeService {

    private ISyncDataService<DtoSampleType> sampleTypeSyncService;

    private SampleTypeRepository sampleTypeRepository;

    @Override
    public List<DtoSampleType> findRccAll() {
        QueryRequestParamVO params = createQueryParam("/api/base/sampleType/all", HttpMethod.POST.name(), EnumRequestTarget.RCC.getValue(),
                "RCC", new HashMap<>());
        return getResponseList(rccQueryService.findList(params), DtoSampleType.class);
    }

    @Override
    @Transactional
    public void sync() {
        List<DtoSampleType> sourceDataList = findRccAll();
        List<String> localIdList = findRccAll().stream().map(DtoSampleType::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sourceDataList)) {
            localIdList.removeAll(sourceDataList.stream().map(DtoSampleType::getId).collect(Collectors.toList()));
            sampleTypeSyncService.sync(sourceDataList, null);
            if (StringUtil.isNotEmpty(localIdList)) {
                sampleTypeRepository.delete(sampleTypeRepository.findAll(localIdList));
            }
        }
    }

    @Override
    @Transactional
    public void sync(Collection<String> rccSampleTypeIds) {
        List<DtoSampleType> sourceDataList = findRccAll().stream()
                .filter(p -> rccSampleTypeIds.contains(p.getId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sourceDataList)) {
            sampleTypeSyncService.sync(sourceDataList, null);
        }
    }

    @Autowired
    @Lazy
    @Qualifier("sampleTypeSyncServiceImpl")
    public void setSampleTypeSyncService(ISyncDataService<DtoSampleType> sampleTypeSyncService) {
        this.sampleTypeSyncService = sampleTypeSyncService;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }
}
