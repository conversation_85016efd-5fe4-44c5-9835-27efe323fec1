package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.service.rcc.IRccSampleTypeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

/**
 * 资源中心测试项目 Controller
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@RestController
@RequestMapping("/api/lim/rccSampleType")
public class RccSampleTypeController extends ExceptionHandlerController<IRccSampleTypeService> {

    /**
     * 选择同步
     *
     * @param sampleTypeIds 检测类型ids
     * @return 结果
     */
    @PostMapping("/sync")
    public RestResponse<String> sync(@RequestBody Collection<String> sampleTypeIds) {
        service.sync(sampleTypeIds);
        return new RestResponse<>();
    }

    /**
     * 从RCC中同步全部检测类型
     *
     * @return 结果
     */
    @PostMapping("/sync/all")
    public RestResponse<String> sync() {
        service.sync();
        return new RestResponse<>();
    }
}
