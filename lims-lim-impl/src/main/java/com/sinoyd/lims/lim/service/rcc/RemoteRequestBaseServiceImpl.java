package com.sinoyd.lims.lim.service.rcc;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumRequestTarget;
import com.sinoyd.commons.service.IRCCQueryService;
import com.sinoyd.commons.service.IRCCRequestService;
import com.sinoyd.commons.vo.QueryRequestParamVO;
import com.sinoyd.frame.base.util.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源中心请求服务基础实现类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@Service
@Slf4j
public abstract class RemoteRequestBaseServiceImpl {

    protected IRCCQueryService rccQueryService;

    protected IRCCRequestService rccRequestService;

    /**
     * 根据业务id集合查询rcc 文档信息
     *
     * @param folderIds 业务id集合
     * @return 文档实体集合
     */
    protected List<DtoDocument> findRccDocument(Collection<String> folderIds) {
        Map<String, Object> map = new HashMap<>();
        map.put("", folderIds);
        QueryRequestParamVO param = createQueryParam("/api/base/document/objects",
                HttpMethod.POST.name(), EnumRequestTarget.RCC.getValue(), "RCC", map);
        return getResponseList(rccQueryService.findList(param), DtoDocument.class);
    }

    /**
     * 构建请求参数
     *
     * @param pb          分页对象
     * @param criteriaMap 业务过滤条件
     * @param uri         请求uri
     * @param httpMethod  请求方法
     * @param target      请求目标
     * @param orgCode     请求机构
     * @return 请求参数
     */
    protected <T> QueryRequestParamVO createQueryParam(PageBean<T> pb, Map<String, Object> criteriaMap,
                                                       String uri, String httpMethod, Integer target, String orgCode) {
        QueryRequestParamVO param = createQueryParam(uri, httpMethod, target, orgCode, criteriaMap);
        param.setPage(pb.getPageNo());
        param.setRows(pb.getRowsPerPage());
        param.setSort(pb.getSort());
        return param;
    }

    /**
     * 构建请求参数
     *
     * @param uri        请求uri
     * @param httpMethod 请求方法
     * @param target     请求目标
     * @param orgCode    请求机构
     * @param bizFilter  业务过滤条件
     * @return 请求参数
     */
    protected QueryRequestParamVO createQueryParam(String uri, String httpMethod, Integer target,
                                                   String orgCode, Map<String, Object> bizFilter) {
        QueryRequestParamVO paramVO = new QueryRequestParamVO();
        paramVO.setOrgCodes(Collections.singletonList(orgCode));
        paramVO.setTarget(target);
        paramVO.setUri(uri);
        paramVO.setHttpMethod(httpMethod);
        paramVO.setBizFilter(bizFilter);
        return paramVO;
    }

    /**
     * 获取接口请求响应数据
     *
     * @param response 接口响应
     * @param c        实体类
     * @param <T>      泛型，需要获取的数据类型
     * @return 获取的数据
     */
    protected <T> List<T> getResponseList(RestResponse<List<JSONObject>> response, Class<T> c) {
        if (response.isSuccess()) {
            return response.getData().stream()
                    .map(p -> p.toJavaObject(c))
                    .collect(Collectors.toList());
        } else {
            log.error("请求失败，msg:{}", response.getMsg());
            return Collections.emptyList();
        }
    }

    /**
     * 对象转map
     *
     * @param obj          对象
     * @param ignoreFields 需要过滤的字段
     * @return map
     */
    protected Map<String, Object> objectToMap(Object obj, String... ignoreFields) {
        Map<String, Object> result = new HashMap<>();
        List<Field> fields = objectFields(obj.getClass());
        try {
            for (Field field : fields) {
                if (StringUtil.isNotEmpty(Arrays.stream(ignoreFields).filter(p -> field.getName().equals(p)).collect(Collectors.toList()))) {
                    continue;
                }
                field.setAccessible(true);
                result.put(field.getName(), field.get(obj));
                if (List.class.isAssignableFrom(field.getType())) {
                    if (field.get(obj) != null && ((List) field.get(obj)).isEmpty()) {
                        result.put(field.getName(), null);
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }finally {
            for (Field field : fields) {
                field.setAccessible(false);
            }
        }
        return result;
    }

    /**
     * 获取对象所有属性
     *
     * @param c 对象类
     * @return 属性列表
     */
    private static List<Field> objectFields(Class<?> c) {
        List<Field> fieldList = Arrays.stream(c.getDeclaredFields()).collect(Collectors.toList());
        if (c.getSuperclass() != null && !c.getSuperclass().equals(Object.class)) {
            fieldList.addAll(objectFields(c.getSuperclass()));
        }
        return fieldList;
    }

    @Autowired
    public void setRccQueryService(IRCCQueryService rccQueryService) {
        this.rccQueryService = rccQueryService;
    }

    @Autowired
    public void setRccRequestService(IRCCRequestService rccRequestService) {
        this.rccRequestService = rccRequestService;
    }

}
