package com.sinoyd.lims.lim.repository.lims;


import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;

import java.util.List;

/**
 * 分析项目排序详情仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigParamRepository extends IBaseJpaPhysicalDeleteRepository<DtoOcrConfigParam, String>{
    /**
     * 通过对象标识获取所有参数
     * @param configIdList 对象数组
     * @return List<DtoOcrConfigParam>
     */
    List<DtoOcrConfigParam> findAllByConfigIdIn(List<String> configIdList);

    /**
     * 通过对象标识获取所有参数
     * @param configId 对象标识
     * @return List<DtoOcrConfigParam>
     */
    List<DtoOcrConfigParam> findAllByConfigId(String configId);
}