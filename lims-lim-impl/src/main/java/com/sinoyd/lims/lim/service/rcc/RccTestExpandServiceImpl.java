package com.sinoyd.lims.lim.service.rcc;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.service.ISyncDataService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumRequestTarget;
import com.sinoyd.commons.vo.QueryRequestParamVO;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import io.swagger.models.HttpMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试项目拓展资源中心服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@Service
public class RccTestExpandServiceImpl extends RemoteRequestBaseServiceImpl implements IRccTestExpandService{

    private TestExpandRepository testExpandRepository;

    private ISyncDataService<DtoTestExpand> testExpandSyncService;

    @Override
    @Transactional
    public void sync(Collection<String> rccTestIds) {
        //先删除测试项目拓展数据
        testExpandRepository.deleteByTestIdIn(rccTestIds);
        List<DtoTestExpand> sourceDataList = findRccByTestIds(rccTestIds);
        if (StringUtil.isNotEmpty(sourceDataList)) {
            testExpandSyncService.sync(sourceDataList, null);
        }
    }

    @Override
    public List<DtoTestExpand> findRccByTestIds(Collection<String> testIds) {
        Map<String, Object> bizFilter = new HashMap<>();
        bizFilter.put("", testIds);
        QueryRequestParamVO paramVO = createQueryParam("/api/lim/testExpand/test", HttpMethod.POST.name(),
                EnumRequestTarget.RCC.getValue(), "RCC", bizFilter);
        RestResponse<List<JSONObject>> response = rccQueryService.findList(paramVO);
        return getResponseList(response, DtoTestExpand.class);
    }

    @Autowired
    public void setTestExpandRepository(TestExpandRepository testExpandRepository) {
        this.testExpandRepository = testExpandRepository;
    }

    @Autowired
    @Lazy
    @Qualifier("testExpandSyncServiceImpl")
    public void setTestExpandSyncService(ISyncDataService<DtoTestExpand> testExpandSyncService) {
        this.testExpandSyncService = testExpandSyncService;
    }
}
