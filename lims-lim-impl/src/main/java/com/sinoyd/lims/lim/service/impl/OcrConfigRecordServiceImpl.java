package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.service.OcrConfigParamDataService;
import com.sinoyd.lims.lim.service.OcrConfigRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ocr对象参数数据接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Service
public class OcrConfigRecordServiceImpl extends BaseJpaServiceImpl<DtoOcrConfigRecord, String, OcrConfigRecordRepository>
        implements OcrConfigRecordService {

    private OcrConfigParamDataRepository ocrConfigParamDataRepository;

    private PersonRepository personRepository;

    private OcrConfigParamRepository ocrConfigParamRepository;

    private OcrConfigRepository ocrConfigRepository;

    private OcrConfigParamDataService ocrConfigParamDataService;

    @Override
    public void findByPage(PageBean<DtoOcrConfigRecord> page, BaseCriteria criteria) {
        page.setEntityName("DtoOcrConfigRecord a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        loadTransientData(page.getData());
    }

    @Override
    @Transactional
    public DtoOcrConfigRecord save(DtoOcrConfigRecord entity) {
        if (StringUtil.isNotEmpty(entity.getParamDataList())) {
            List<DtoOcrConfigParamData> paramDataList = entity.getParamDataList();
//            paramDataList.forEach(p -> p.setRecordId(entity.getId()));
            ocrConfigParamDataService.updateBatch(paramDataList);
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public void saveConfigParamData(DtoOcrConfigRecord entity) {
        if (StringUtil.isNotEmpty(entity.getParamDataList())) {
            List<DtoOcrConfigParamData> paramDataList = entity.getParamDataList();
            ocrConfigParamDataService.updateBatch(paramDataList);
        }
    }

    @Override
    public Map<String, DtoOcrConfigRecord> getLatestDataMapByCode(List<String> sampleCodes) {
        List<DtoOcrConfigRecord> recordList = repository.findAllBySampleCodeInOrderByCreateDateDesc(sampleCodes);
        List<DtoOcrConfigRecord> tempList = new ArrayList<>();
        Map<String, DtoOcrConfigRecord> map = new HashMap<>();
        for (String sampleCode : sampleCodes) {
            recordList.stream().filter(r -> sampleCode.equals(r.getSampleCode())).findFirst()
                    .ifPresent(tempList::add);
        }
        loadTransientData(tempList);
        for (String sampleCode : sampleCodes) {
            DtoOcrConfigRecord record = tempList.stream().filter(r -> sampleCode.equals(r.getSampleCode())).findFirst().orElse(null);
            map.put(sampleCode, record);
        }
        return map;
    }


    @Override
    public DtoOcrConfigRecord findOne(String key) {
        DtoOcrConfigRecord ocrConfigRecord = super.findOne(key);
        loadTransientData(Collections.singletonList(ocrConfigRecord));
        return ocrConfigRecord;
    }

    @Override
    public Map<String, DtoOcrConfigRecord> getDataMapByRecordIds(List<String> ocrConfigRecordIds, List<String> sampleCodes) {
        List<DtoOcrConfigRecord> recordList = repository.findAll(ocrConfigRecordIds);
        List<DtoOcrConfigRecord> tempList = new ArrayList<>();
        Map<String, DtoOcrConfigRecord> map = new HashMap<>();
        for (String sampleCode : sampleCodes) {
            recordList.stream().filter(r -> sampleCode.equals(r.getSampleCode())).findFirst()
                    .ifPresent(tempList::add);
        }
        loadTransientData(tempList);
        for (String sampleCode : sampleCodes) {
            DtoOcrConfigRecord record = tempList.stream().filter(r -> sampleCode.equals(r.getSampleCode())).findFirst().orElse(null);
            map.put(sampleCode, record);
        }
        return map;
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        List<DtoOcrConfigParamData> ocrConfigParamDataList = ocrConfigParamDataRepository.findAllByRecordId((String) id);
        // 删除识别记录参数
        if (StringUtil.isNotEmpty(ocrConfigParamDataList)) {
            ocrConfigParamDataRepository.delete(ocrConfigParamDataList);
        }
        return super.logicDeleteById(id);
    }



    /**
     * 填充附加字段
     *
     * @param list 原始结果集
     */
    private void loadTransientData(List<DtoOcrConfigRecord> list) {
        if (!list.isEmpty()) {
            List<String> recordIds = list.stream().map(DtoOcrConfigRecord::getId).collect(Collectors.toList());
            List<String> configIds = list.stream().map(DtoOcrConfigRecord::getConfigId).collect(Collectors.toList());
            List<DtoOcrConfigParamData> dataList = ocrConfigParamDataRepository.findAllByRecordIdIn(recordIds);
            List<DtoOcrConfig> configList = ocrConfigRepository.findAll(configIds);
            if (!dataList.isEmpty()) {
                List<DtoPerson> personList = personRepository.findAll();
                List<DtoOcrConfigParam> params = ocrConfigParamRepository.findAllByConfigIdIn(configIds);
                for (DtoOcrConfigRecord record : list) {
                    personList.stream().filter(p -> p.getId().equals(record.getCreator())).findFirst().ifPresent(p -> record.setCreatorName(p.getCName()));
                    configList.stream().filter(c -> c.getId().equals(record.getConfigId())).findFirst().ifPresent(c -> record.setConfigName(c.getConfigName()));
                    List<DtoOcrConfigParamData> recordDataList = dataList.stream().filter(d -> record.getId().equals(d.getRecordId())).collect(Collectors.toList());
                    for (DtoOcrConfigParamData data : recordDataList) {
                        params.stream().filter(p -> p.getId().equals(data.getConfigParamId())).findFirst()
                                .ifPresent(param -> {
                                    data.setParamName(param.getParamName());
                                    data.setParamNameAlias(param.getParamNameAlias());
                                    data.setParamType(param.getParamType());
                                    data.setAnalyzeItemId(param.getAnalyzeItemId());
                                    data.setOrderNum(param.getOrderNum());
                                    data.setDimension(param.getDimension());
                                });
                    }
                    recordDataList.sort(Comparator.comparing(DtoOcrConfigParamData::getOrderNum, Comparator.reverseOrder()));
                    record.setParamDataList(recordDataList);
                }
            }
        }
    }

    @Autowired
    public void setOcrConfigParamDataRepository(OcrConfigParamDataRepository ocrConfigParamDataRepository) {
        this.ocrConfigParamDataRepository = ocrConfigParamDataRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setOcrConfigParamRepository(OcrConfigParamRepository ocrConfigParamRepository) {
        this.ocrConfigParamRepository = ocrConfigParamRepository;
    }

    @Autowired
    public void setOcrConfigRepository(OcrConfigRepository ocrConfigRepository) {
        this.ocrConfigRepository = ocrConfigRepository;
    }

    @Autowired
    @Lazy
    public void setOcrConfigParamDataService(OcrConfigParamDataService ocrConfigParamDataService) {
        this.ocrConfigParamDataService = ocrConfigParamDataService;
    }
}
