package com.sinoyd.lims.lim.service.impl;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.customer.DtoCompareExportAnalyzeMethod;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.AnalyzeMethodService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;


/**
 * 分析方法接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/19
 * @since V100R001
 */
@Service
@Slf4j
public class AnalyzeMethodServiceImpl extends BaseJpaServiceImpl<DtoAnalyzeMethod, String, AnalyzeMethodRepository> implements AnalyzeMethodService {

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private RedisTemplate redisTemplate;

    private ImportUtils importUtils = new ImportUtils();

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private Person2TestRepository person2TestRepository;

    @Autowired
    private TestExpandRepository testExpandRepository;

    @Autowired
    private ParamsFormulaRepository paramsFormulaRepository;

    @Autowired
    private TestPost2TestRepository testPost2TestRepository;

    @Autowired
    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    @Autowired
    private LogForAnalyzeMethodRepository logForAnalyzeMethodRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    /**
     * 新增分析方法
     */
    @Override
    @Transactional
    public DtoAnalyzeMethod save(DtoAnalyzeMethod entity) {
        if (StringUtil.isNotNull(entity.getIsSamplingMethod()) && entity.getIsSamplingMethod()) {
            if (repository.countByMethodName(entity.getMethodName(), entity.getId()) > 0) {
                throw new BaseException("新增采样方法已存在，请确认后再操作！");
            }
        } else {
            DtoAnalyzeMethod exitDtoAnalyzeMethod = repository.getByNameAndCountryStandard(entity.getMethodName(), entity.getCountryStandard());
            // 判断分析方法名称是否重复
            if (StringUtil.isNotNull(exitDtoAnalyzeMethod)) {
                throw new BaseException("已存在相同名称和标准编号的分析方法！");
            }
        }

        return super.save(entity);
    }

    /**
     * 修改分析方法
     */
    @Transactional
    @Override
    public DtoAnalyzeMethod update(DtoAnalyzeMethod entity) {
        if (StringUtil.isNotNull(entity.getIsSamplingMethod()) && entity.getIsSamplingMethod()) {
            if (repository.countByMethodName(entity.getMethodName(), entity.getId()) > 0) {
                throw new BaseException("新增采样方法已存在，请确认后再操作！");
            }
        } else {
            DtoAnalyzeMethod exitDtoAnalyzeMethod = repository.getByNameAndCountryStandard(entity.getMethodName(), entity.getCountryStandard());
            // 判断分析方法名称是否重复
            if (StringUtil.isNotNull(exitDtoAnalyzeMethod) && !entity.getId().equals(exitDtoAnalyzeMethod.getId())) {
                throw new BaseException("已存在相同名称和标准编号的分析方法！");
            }
        }

        try{
            // 获取相关测试项目
            List<DtoTest> testList = testService.getListByAnalyzeMethodId(entity.getId());

            Map<String, Object> map = new HashMap<>();
            String methodName = entity.getMethodName();
            String countryStandard = entity.getCountryStandard();
            //需要修改的测试项目集合
            List<DtoTest> updateTestList = new ArrayList<>();
            for (DtoTest test : testList) {
                boolean isUpload = false;
                //不等的时候才做更新
                if (!methodName.equals(test.getRedAnalyzeMethodName())
                        || !StringUtil.isNotEmpty(countryStandard)
                        || !countryStandard.equals(test.getRedCountryStandard())) {
                    isUpload = true;
                }
                if (isUpload) {
                    test.setRedAnalyzeMethodName(entity.getMethodName());
                    test.setRedCountryStandard(entity.getCountryStandard());
                    test.setTestName(test.getRedAnalyzeItemName() + '-' + methodName + ' ' + (StringUtil.isNull(countryStandard) ? "" : countryStandard));
                    map.put(test.getId(), JsonStream.serialize(test));
                    updateTestList.add(test);
                }
            }
            if (updateTestList.size() > 0) {
                testRepository.save(updateTestList);
                String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
                redisTemplate.opsForHash().putAll(key, map);
            }
            return super.update(entity);
        }catch (Exception e){
            log.error("修改分析方法失败", e);
            throw new BaseException("修改分析方法失败");
        }
    }

    /**
     * 分页获取分析方法
     *
     * @param page     分页条件
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoAnalyzeMethod> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoAnalyzeMethod p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);
        List<DtoAnalyzeMethod> analyzeMethodList = page.getData();
        List<String> sampleTypeIds = analyzeMethodList.stream().map(DtoAnalyzeMethod::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
        for (DtoAnalyzeMethod method : analyzeMethodList) {
            sampleTypeList.stream().filter(p -> p.getId().equals(method.getSampleTypeId())).findFirst().ifPresent(sampleType -> {
                method.setSampleTypeName(sampleType.getTypeName());
            });
        }
    }

    /**
     * 单个删除（假删）
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        //级联根据分析方法id删除测试项目
        Collection<String> ids = new ArrayList<>();
        ids.add((String) id);
        return logicDeleteById(ids);
    }

    /**
     * 批量删除（假删）
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {

        //级联根据分析方法id删除测试项目
        Collection<String> idList = (List<String>) ids;
        testService.deleteByAnalyzeMethodIds(idList);
        return super.logicDeleteById(idList);
    }

    /**
     * 替换分析方法
     *
     * @param entity 待替换的分析方法
     */
    @Transactional
    @Override
    public void replace(DtoAnalyzeMethod entity) {
        // 保存新分析方法
        DtoAnalyzeMethod anaMethod = save(entity);
        //能够自动复制老方法的测试项目信息、测试人员配置、测试扩展、测试项目公式、测试项目公式参数、岗位配置信息
        copyAndSaveRelatedData(anaMethod, entity.getOldMethodId());
    }

    /**
     * 根据方法名称和标准编号获取分析方法
     *
     * @param analyzeMethodName
     * @param countryStandard
     * @return 分析方法
     */
    @Override
    public DtoAnalyzeMethod getByAnalyzeMethodNameAndCountryStandard(String analyzeMethodName, String countryStandard) {
        return repository.getByNameAndCountryStandard(analyzeMethodName, countryStandard);
    }


    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回方法对象
     */
    @Override
    public DtoAnalyzeMethod findOne(String id) {
        return super.findOne(id);
    }

    @Override
    public List<DtoAnalyzeMethod> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoAnalyzeMethod> findAllDeleted() {
        return repository.findAllDeleted();
    }

    @Override
    public void compareExport(Integer similarity, HttpServletResponse response) {
        List<DtoAnalyzeMethod> analyzeMethods = repository.findAll();
        // 遍历
        List<DtoCompareExportAnalyzeMethod> result = new ArrayList<>();
        for (int i = 0; i < analyzeMethods.size(); i++) {
            for (int j = 0; j < analyzeMethods.size(); j++) {
                if (analyzeMethods.get(i).getId().equals(analyzeMethods.get(j).getId())) {
                    continue;
                }
                // 比较方法名称
                double compareCharacter = DivationUtils.compareCharacter(analyzeMethods.get(i).getMethodName(), analyzeMethods.get(j).getMethodName());
                if (compareCharacter * 100 >= similarity) {
                    DtoCompareExportAnalyzeMethod compareExportAnalyzeMethod = new DtoCompareExportAnalyzeMethod();
                    DtoCompareExportAnalyzeMethod compareExportAnalyzeMethod2 = new DtoCompareExportAnalyzeMethod();
                    BeanUtils.copyProperties(analyzeMethods.get(i), compareExportAnalyzeMethod);
                    BeanUtils.copyProperties(analyzeMethods.get(j), compareExportAnalyzeMethod2);
                    result.add(compareExportAnalyzeMethod);
                    result.add(compareExportAnalyzeMethod2);
                }
            }
        }
        result = result.stream().distinct().peek(p -> p.setMethodName(p.getMethodName().trim()))
                .sorted(Comparator.comparing(DtoCompareExportAnalyzeMethod::getMethodName)).collect(Collectors.toList());
        if (StringUtil.isEmpty(result)) {
            throw new BaseException("分析方法中不存在相似度大于等于" + similarity + "的数据");
        } else {
            Map<String, String> sheetNames = new HashMap<>();
            sheetNames.put("firstName", "分析方法");
            Workbook workBook = importUtils.getWorkBook(sheetNames, DtoCompareExportAnalyzeMethod.class, result);
            PoiExcelUtils.downLoadExcel("分析方法", response, workBook);
        }
    }

    @Override
    @Transactional
    public void updateMethodStatus(Integer status, List<String> ids) {
        if (EnumLIM.EnumAnalyzeMethodStatus.启用.getValue().equals(status)) {
            enableMethods(ids);
        } else if (EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(status)) {
            deactivateMethods(ids);
        } else if (EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(status)) {
            abolishMethods(ids);
        }
    }

    /**
     * 启用方法
     *
     * @param ids 方法标识
     */
    private void abolishMethods(List<String> ids) {
        //状态变更
        updateEntityStatus(EnumLIM.EnumAnalyzeMethodStatus.废止, ids);
        //关联测试项目设置为废止并删除
        List<DtoTest> testList = testRepository.findByAnalyzeMethodIdIn(ids);
        testList.forEach(t -> {
            t.setIsAbolish(true);
            t.setIsDeleted(true);
        });
        testRepository.save(testList);
        testService.initRedis();
    }

    /**
     * 停用方法
     *
     * @param ids 方法标识
     */
    private void deactivateMethods(List<String> ids) {
        //状态变更
        updateEntityStatus(EnumLIM.EnumAnalyzeMethodStatus.停用, ids);
    }

    /**
     * 废止方法
     *
     * @param ids 方法标识
     */
    private void enableMethods(List<String> ids) {
        //状态变更
        updateEntityStatus(EnumLIM.EnumAnalyzeMethodStatus.启用, ids);
        //还原测试项目
        List<DtoTest> testList = testRepository.findByAnalyzeMethodIdInAndIsAbolishTrue(ids);
        testList.forEach(t -> {
            t.setIsAbolish(false);
            t.setIsDeleted(false);
        });
        testRepository.save(testList);
        testService.initRedis();
    }

    /**
     * 实体状态变更
     *
     * @param status 状态
     * @param ids    方法标识列表
     */
    private void updateEntityStatus(EnumLIM.EnumAnalyzeMethodStatus status, List<String> ids) {
        List<DtoAnalyzeMethod> list = findAll(ids);
        List<DtoLogForAnalyzeMethod> logList = new ArrayList<>();
        list.forEach(a -> {
            a.setStatus(status.getValue());
            //插入日志
            DtoLogForAnalyzeMethod logForAnalyzeMethod = new DtoLogForAnalyzeMethod();
            logForAnalyzeMethod.setObjectId(a.getId());
            logForAnalyzeMethod.setOperateTime(new Date());
            logForAnalyzeMethod.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            logForAnalyzeMethod.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            logForAnalyzeMethod.setOperateInfo(String.format("%s了该方法", status.name()));
            logList.add(logForAnalyzeMethod);
        });
        update(list);
        logForAnalyzeMethodRepository.save(logList);
    }

    /**
     * 复制老方法的测试项目信息、测试人员配置、测试扩展、测试项目公式、测试项目公式参数、岗位配置信息
     *
     * @param anaMethod   新方法
     * @param oldMethodId 老方法标识
     */
    protected List<DtoTest> copyAndSaveRelatedData(DtoAnalyzeMethod anaMethod, String oldMethodId) {
        //数据查询
        List<DtoTest> testList = testRepository.findByAnalyzeMethodIdIn(Collections.singletonList(oldMethodId));
        List<String> testIdList = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
        List<DtoPerson2Test> person2TestList = testIdList.isEmpty() ? new ArrayList<>() : person2TestRepository.findByTestIdIn(testIdList);
        List<DtoTestExpand> testExpandList = testIdList.isEmpty() ? new ArrayList<>() : testExpandRepository.findByTestIdIn(testIdList);
        List<DtoParamsFormula> paramsFormulaList = testIdList.isEmpty() ? new ArrayList<>()
                : paramsFormulaRepository.findAllByObjectIdInAndObjectTypeAndIsDeletedFalse(testIdList, EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue());
        List<String> testFormulaIds = paramsFormulaList.stream().map(DtoParamsFormula::getId).collect(Collectors.toList());
        List<DtoParamsTestFormula> paramsTestFormulaList = testFormulaIds.isEmpty() ? new ArrayList<>() : paramsTestFormulaRepository.findByObjIdIn(testFormulaIds);
        List<DtoTestPost2Test> testPost2TestList = testIdList.isEmpty() ? new ArrayList<>() : testPost2TestRepository.findByTestIdIn(testIdList);
        //数据容器
        List<DtoTest> saveTestList = new ArrayList<>();
        List<DtoPerson2Test> savePerson2TestList = new ArrayList<>();
        List<DtoTestExpand> saveTestExpandList = new ArrayList<>();
        List<DtoParamsFormula> saveParamsFormulaList = new ArrayList<>();
        List<DtoParamsTestFormula> saveParamsTestFormulaList = new ArrayList<>();
        List<DtoTestPost2Test> saveTestPost2TestList = new ArrayList<>();
        //数据复制
        for (DtoTest dtoTest : testList) {
            //测试项目复制
            DtoTest test = new DtoTest();
            BeanUtils.copyProperties(dtoTest, test, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
            test.setAnalyzeMethodId(anaMethod.getId());
            test.setRedAnalyzeMethodName(anaMethod.getMethodName());
            test.setRedCountryStandard(anaMethod.getCountryStandard());
            test.setTestName(test.getRedAnalyzeItemName() + '-' + anaMethod.getMethodName() + ' ' + (StringUtil.isNull(anaMethod.getCountryStandard()) ? "" : anaMethod.getCountryStandard()));
            saveTestList.add(test);
            //测试人员配置复制
            copyOriginPerson2Test(savePerson2TestList, person2TestList, dtoTest.getId(), test.getId());
            //测试拓展复制
            copyOriginTestExpand(saveTestExpandList, testExpandList, dtoTest.getId(), test.getId());
            //岗位配置信息复制
            copyOriginTestPost2Test(saveTestPost2TestList, testPost2TestList, dtoTest.getId(), test.getId());
            //公式复制
            copyOriginParamsFormula(saveParamsFormulaList, saveParamsTestFormulaList, paramsFormulaList, paramsTestFormulaList, dtoTest.getId(), test.getId());
        }
        testRepository.save(saveTestList);
        person2TestRepository.save(savePerson2TestList);
        testExpandRepository.save(saveTestExpandList);
        paramsFormulaRepository.save(saveParamsFormulaList);
        paramsTestFormulaRepository.save(saveParamsTestFormulaList);
        testPost2TestRepository.save(saveTestPost2TestList);
        return saveTestList;
    }

    /**
     * 测试人员配置复制
     *
     * @param savePerson2TestList 保存容器
     * @param person2TestList     原始数据集
     * @param oldTestId           旧测试项目标识
     * @param newTestId           新测试项目标识
     */
    private void copyOriginPerson2Test(List<DtoPerson2Test> savePerson2TestList, List<DtoPerson2Test> person2TestList, String oldTestId, String newTestId) {
        List<DtoPerson2Test> originPerson2TestList = person2TestList.stream().filter(p -> oldTestId.equals(p.getTestId())).collect(Collectors.toList());
        for (DtoPerson2Test originPerson2Test : originPerson2TestList) {
            DtoPerson2Test newPerson2Test = new DtoPerson2Test();
            BeanUtils.copyProperties(originPerson2Test, newPerson2Test, "id", "domainId");
            newPerson2Test.setTestId(newTestId);
            savePerson2TestList.add(newPerson2Test);
        }
    }

    /**
     * 测试拓展复制
     *
     * @param saveTestExpandList 保存容器
     * @param testExpandList     原始数据集
     * @param oldTestId          旧测试项目标识
     * @param newTestId          新测试项目标识
     */
    private void copyOriginTestExpand(List<DtoTestExpand> saveTestExpandList, List<DtoTestExpand> testExpandList, String oldTestId, String newTestId) {
        List<DtoTestExpand> originTestExpandList = testExpandList.stream().filter(p -> oldTestId.equals(p.getTestId())).collect(Collectors.toList());
        for (DtoTestExpand originTestExpand : originTestExpandList) {
            DtoTestExpand newTestExpand = new DtoTestExpand();
            BeanUtils.copyProperties(originTestExpand, newTestExpand, "id", "orgId");
            newTestExpand.setTestId(newTestId);
            saveTestExpandList.add(newTestExpand);
        }
    }

    /**
     * 岗位信息复制
     *
     * @param saveTestPost2TestList 保存容器
     * @param testPost2TestList     原始数据集
     * @param oldTestId             旧测试项目标识
     * @param newTestId             新测试项目标识
     */
    private void copyOriginTestPost2Test(List<DtoTestPost2Test> saveTestPost2TestList, List<DtoTestPost2Test> testPost2TestList, String oldTestId, String newTestId) {
        List<DtoTestPost2Test> originTestPost2TestList = testPost2TestList.stream().filter(p -> oldTestId.equals(p.getTestId())).collect(Collectors.toList());
        for (DtoTestPost2Test originTestPost2Test : originTestPost2TestList) {
            DtoTestPost2Test newTestPost2Test = new DtoTestPost2Test();
            BeanUtils.copyProperties(originTestPost2Test, newTestPost2Test, "id");
            newTestPost2Test.setTestId(newTestId);
            saveTestPost2TestList.add(newTestPost2Test);
        }
    }


    /**
     * 公式及参数复制
     *
     * @param saveParamsFormulaList     公式保存容器
     * @param saveParamsTestFormulaList 公式参数保存容器
     * @param paramsFormulaList         公式原始数据集
     * @param paramsTestFormulaList     公式参数原始数据集
     * @param oldTestId                 旧测试项目标识
     * @param newTestId                 新测试项目标识
     */
    private void copyOriginParamsFormula(List<DtoParamsFormula> saveParamsFormulaList, List<DtoParamsTestFormula> saveParamsTestFormulaList,
                                         List<DtoParamsFormula> paramsFormulaList, List<DtoParamsTestFormula> paramsTestFormulaList,
                                         String oldTestId, String newTestId) {
        List<DtoParamsFormula> originParamsFormulaList = paramsFormulaList.stream().filter(p -> oldTestId.equals(p.getObjectId())).collect(Collectors.toList());
        for (DtoParamsFormula originParamsFormula : originParamsFormulaList) {
            DtoParamsFormula newParamsFormula = new DtoParamsFormula();
            BeanUtils.copyProperties(originParamsFormula, newParamsFormula, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
            newParamsFormula.setObjectId(newTestId);
            saveParamsFormulaList.add(newParamsFormula);
            //公式参数复制
            List<DtoParamsTestFormula> originParamsTestFormulaList = paramsTestFormulaList.stream().filter(t -> originParamsFormula.getId().equals(t.getObjId())).collect(Collectors.toList());
            for (DtoParamsTestFormula originParamsTestFormula : originParamsTestFormulaList) {
                DtoParamsTestFormula newParamsTestFormula = new DtoParamsTestFormula();
                BeanUtils.copyProperties(originParamsTestFormula, newParamsTestFormula, "id", "orgId");
                newParamsTestFormula.setObjId(newParamsFormula.getId());
                saveParamsTestFormulaList.add(newParamsTestFormula);
            }
        }
    }

    @Override
    public List<DtoLogForAnalyzeMethod> getStatusLog(String id) {
        return logForAnalyzeMethodRepository.findByObjectIdOrderByOperateTimeDesc(id);
    }

    @Override
    public List<DtoAnalyzeMethod> bySampleType(String sampleTypeId) {
        if (StringUtil.isNotEmpty(sampleTypeId)) {
            List<String> sampleTypeIds = new ArrayList<>(Arrays.asList(sampleTypeId.split(",")));
            // 根据小类获取大类id
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            List<String> bigSampleTypeIds = sampleTypeList.stream().map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
            return StringUtil.isNotEmpty(bigSampleTypeIds) ? repository.findBySampleTypeIdInAndIsDeletedFalse(bigSampleTypeIds) : new ArrayList<>();
        }
        return new ArrayList<>();
    }
}