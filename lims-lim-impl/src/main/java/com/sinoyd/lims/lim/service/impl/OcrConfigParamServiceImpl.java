package com.sinoyd.lims.lim.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamRepository;
import com.sinoyd.lims.lim.service.OcrConfigParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ocr对象参数接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Service
public class OcrConfigParamServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOcrConfigParam, String, OcrConfigParamRepository> implements OcrConfigParamService {


    private AnalyzeItemService analyzeItemService;

    @Override
    public void findByPage(PageBean<DtoOcrConfigParam> page, BaseCriteria criteria) {
        page.setEntityName("DtoOcrConfigParam c");
        page.setSelect("select c");
        super.findByPage(page, criteria);
        List<DtoOcrConfigParam> list = page.getData();
        List<DtoAnalyzeItem> analyzeItemList = analyzeItemService.findAllDeleted();
        for (DtoOcrConfigParam dtoOcrConfigParam : list) {
            //参数类型文本s
            dtoOcrConfigParam.setParamTypeName(EnumLIM.EnumOcrConfigParamType.getByValue(dtoOcrConfigParam.getParamType()));
            String analyzeItemId = dtoOcrConfigParam.getAnalyzeItemId();
            if (StringUtil.isNotEmpty(analyzeItemId)) {
                List<String> analyzeItemIds = Arrays.asList(analyzeItemId.split(","));
                dtoOcrConfigParam.setAnalyzeItemName(analyzeItemList.stream().filter(p -> analyzeItemIds.contains(p.getId())).map(DtoAnalyzeItem::getAnalyzeItemName).collect(Collectors.joining(",")));
            }
        }
    }

    @Override
    public List<Map<String, Object>> getParamTypeSelectList() {
        List<Map<String, Object>> res = new ArrayList<>();
        for (EnumLIM.EnumOcrConfigParamType c : EnumLIM.EnumOcrConfigParamType.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("text", c.name());
            map.put("value", c.getValue());
            res.add(map);
        }
        return res;
    }

    @Autowired
    public void setAnalyzeItemService(AnalyzeItemService analyzeItemService) {
        this.analyzeItemService = analyzeItemService;
    }
}
