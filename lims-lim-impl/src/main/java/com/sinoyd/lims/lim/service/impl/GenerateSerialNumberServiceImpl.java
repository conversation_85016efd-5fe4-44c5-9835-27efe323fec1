package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.repository.rcc.SerialNumberConfigRepository;
import com.sinoyd.lims.lim.service.GenerateSerialNumberService;
import com.sinoyd.lims.lim.service.SerialNumberConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 实验室资源流水号
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
@Slf4j
public class GenerateSerialNumberServiceImpl implements GenerateSerialNumberService {

    @Autowired
    @Lazy
    private SerialNumberConfigService serialNumberConfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SerialNumberConfigRepository serialNumberConfigRepository;

    //通用正则
    protected static final Pattern PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    //日期正则
    private static final Pattern PATTERN_DATE = Pattern.compile("(?<=\\().*?(?=\\))");

    @Transactional
    @Override
    public String generateNextSN(String format, Map<String, Object> map) {
        DtoGenerateSN generateSN = generateSN(format, map, true, true);
        return generateSN.getCode();
    }

    @Transactional
    @Override
    public DtoGenerateSN generateNextSN(String format, Map<String, Object> map, Boolean isAutoCommitSN) {
        return generateSN(format, map, true, isAutoCommitSN);
    }

    @Override
    public DtoGenerateSN generateCurrentSN(String format, Map<String, Object> map) {
        return generateSN(format, map, false, false);
    }


    /**
     * 返回相应的编号
     *
     * @param format         格式
     * @param map            数据
     * @param isSaveSn       是否要保存流水号（Redis）
     * @param isAutoCommitSN 是否要将保存的流水号自动提交
     * @return 返回编号
     */
    @Transactional
    public DtoGenerateSN generateSN(String format, Map<String, Object> map, Boolean isSaveSn, Boolean isAutoCommitSN) {
        DtoGenerateSN generateSN = new DtoGenerateSN();
        String serialType;
        //编号去除存储的空格
        String code = format.trim();
        //登记人id
        String inputPersonId = PrincipalContextUser.getPrincipal().getUserId();
        Matcher matcher = PATTERN.matcher(format);
        //是否有流水号
        Boolean isSn = false;
        //流水号位数
        String num = "0";
        //sn标识
        String sn = "";
        //流水号的值
        String snValue = "";
        Integer serialNumber = (Integer) map.get("serialNumber");
        Integer manualSn =  (Integer) map.getOrDefault("manualSn", -1);
        while (matcher.find()) {
            //如何得到key
            String groupValue = matcher.group();
            //说明包含流水号
            if (groupValue.contains(LimCodeHelper.SN)) {
                //计算出他需要几位流水号
                Integer endIndex = groupValue.length();
                if (groupValue.contains("-")) {
                    endIndex = groupValue.indexOf("-");
                }
                //需要修约的位数
                num = groupValue.substring(LimCodeHelper.SN.length(), endIndex);
                //有流水号，需要进行相应的处理
                isSn = true;
                sn = LimCodeHelper.SN + num;

                try {
                    groupValue = groupValue.substring(groupValue.indexOf("[") + 1, groupValue.length());
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
            Map<String, Object> dateMap = getDateValue(groupValue, map);

            //如果有日期数据匹配
            if (dateMap.containsKey(groupValue)) {
                String dateValue = StringUtil.isNotNull(dateMap.get(groupValue)) ?
                        String.valueOf(dateMap.get(groupValue)) : "";
                code = code.replace("[" + groupValue + "]", dateValue);
                continue;
            }

            //带有实体的数据源（不带日期的），日期的上面统一考虑处理
            Map<String, Object> entityMap = getEntityValue(groupValue, map);
            if (entityMap.containsKey(groupValue)) {
                String entityValue = StringUtil.isNotNull(entityMap.get(groupValue)) ? String.valueOf(entityMap.get(groupValue)) : "";
                code = code.replace("[" + groupValue + "]", entityValue);
                continue;
            }

            //两种情况都不包含
            String value = "";
            if (StringUtil.isNotNull(map.get(groupValue))) {
                value = String.valueOf(map.get(groupValue));
            }
            code = code.replace("[" + groupValue + "]", value);
        }
        //有流水号
        if (isSn) {
            serialType = getSerialType(code);
            generateSN.setCurrentSerialNumberType(serialType);
            //要去处理流水号
            if (StringUtils.isNotNullAndEmpty(serialType)) {
                sn = sn + "-" + serialType;
            }
            Integer currentNum;
            if (StringUtil.isNull(serialNumber)) {
                DtoSerialNumberConfig dtoSerialNumberConfig = serialNumberConfigService.findBySerialNumberType(serialType);
                if (StringUtil.isNull(dtoSerialNumberConfig)) {
                    //数据
                    currentNum = manualSn == -1 ? 1 : manualSn;
                    if (isSaveSn) {
                        //是否要保存流水号
                        dtoSerialNumberConfig = new DtoSerialNumberConfig();
                        dtoSerialNumberConfig.setSerialNumberType(serialType);
                        dtoSerialNumberConfig.setPara1(String.valueOf(currentNum));
                        dtoSerialNumberConfig.setPara2(PrincipalContextUser.getPrincipal().getUserId());
                        dtoSerialNumberConfig.setLastUpdateTime(new Date());
                        dtoSerialNumberConfig.setCreateDate(new Date());
                        dtoSerialNumberConfig.setCreator(PrincipalContextUser.getPrincipal().getUserId());
                        dtoSerialNumberConfig.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                        dtoSerialNumberConfig.setModifyDate(new Date());
                        if (isAutoCommitSN) {
                            serialNumberConfigService.save(dtoSerialNumberConfig);
                        } else {
                            //将需要保存的数据放到键值对里面
                            generateSN.setSerialNumberConfigCreate(dtoSerialNumberConfig);
                        }
                        saveSerialNumberConfig(dtoSerialNumberConfig, serialType);
                    }
                } else {
                    currentNum = manualSn == -1 ? Integer.parseInt(dtoSerialNumberConfig.getPara1()) : manualSn;
                    //当前的数据
                    if (isSaveSn) {
                        currentNum ++;
                        //下一个数据
                        DtoSerialNumberConfig newSerialNumberConfig = new DtoSerialNumberConfig();
                        newSerialNumberConfig.setId(dtoSerialNumberConfig.getId());
                        newSerialNumberConfig.setSerialNumberType(serialType);
                        newSerialNumberConfig.setPara1(String.valueOf(currentNum));
                        newSerialNumberConfig.setPara2(PrincipalContextUser.getPrincipal().getUserId());
                        newSerialNumberConfig.setCreateDate(dtoSerialNumberConfig.getCreateDate());
                        newSerialNumberConfig.setCreator(dtoSerialNumberConfig.getCreator());
                        newSerialNumberConfig.setLastUpdateTime(new Date());
                        newSerialNumberConfig.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                        newSerialNumberConfig.setModifyDate(new Date());
                        if (isAutoCommitSN) {
                            serialNumberConfigRepository.update(dtoSerialNumberConfig.getId(), newSerialNumberConfig.getPara1(),
                                    newSerialNumberConfig.getPara2(), newSerialNumberConfig.getLastUpdateTime());
                        } else {
                            //将需要保存的数据放到键值对里面
                            generateSN.setSerialNumberConfigUpdate(newSerialNumberConfig);
                        }
                        saveSerialNumberConfig(newSerialNumberConfig, serialType);
                    } else {
                        inputPersonId = dtoSerialNumberConfig.getPara2();
                    }
                }
            } else {
                currentNum = serialNumber;
            }
            StringBuilder strFormat = new StringBuilder("0");
            //要判断是否有配置数据
            if (StringUtils.isNotNullAndEmpty(num.trim())) {
                try {
                    for (Integer i = 1; i < Integer.parseInt(num.trim()); i++) {
                        strFormat.append("0");
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
            DecimalFormat df = new DecimalFormat(strFormat.toString());
            snValue = df.format(currentNum);
        }
        code = code.replace("[" + sn + "]", snValue);
        generateSN.setCode(code);
        generateSN.setCurrentPara1(snValue);
        generateSN.setCurrentPara2(PrincipalContextUser.getPrincipal().getUserId());
        generateSN.setInputPersonId(inputPersonId);
        return generateSN;
    }

    /**
     * 获取流水号的序列化的类型
     *
     * @param format 数据格式
     * @return 返回相应的格式化编号
     */
    protected String getSerialType(String format) {
        StringBuilder value = new StringBuilder();
        Matcher matcher = PATTERN.matcher(format);
        while (matcher.find()) {
            //如何得到key
            String groupValue = matcher.group();
            if (groupValue.contains(LimCodeHelper.SN)) {
                String[] groupValues = groupValue.split("-");
                if (!(groupValues.length > 1)) {
                    for (String group : groupValues) {
                        if (!group.contains(LimCodeHelper.SN)) {
                            value.append(group);
                        }
                    }
                } else {
                    int idx = groupValue.indexOf("-");
                    value.append(groupValue.substring(idx + 1));
                }
            }
        }
        return value.toString();
    }

    /**
     * 主要是用来匹配日期的数据 如time(yy) 或者 sample.sampleTime(yy)
     *
     * @param groupValue 匹配的正则
     * @param map        数据源
     * @return 返回匹配的数据
     */
    private Map<String, Object> getDateValue(String groupValue, Map<String, Object> map) {
        Matcher matcher = PATTERN_DATE.matcher(groupValue);
        Map<String, Object> matchMap = new HashMap<>();
        while (matcher.find()) {
            //如何得到value值
            String keyValue = matcher.group();
            String value = "";
            String key = groupValue.replace("(" + keyValue + ")", "");
            if (key.contains(".")) {
                map = getEntityValue(key, map);
            }
            if (Arrays.asList(LimCodeHelper.DATE_FORMAT).contains(keyValue)) {
                Object objectValue = map.get(key);
                if (StringUtil.isNotNull(objectValue)) {
                    if (objectValue instanceof Date) {
                        value = getDate((Date) objectValue, keyValue);
                    } else {
                        value = getDate(DateUtil.stringToDate(String.valueOf(objectValue), DateUtil.FULL), keyValue);
                    }
                }
            }
            matchMap.put(groupValue, value);
        }
        return matchMap;
    }

    /**
     * 获取带实体的数据源 如sample.code
     *
     * @param groupValue 匹配的正则
     * @param map        数据源
     * @return 返回匹配的数据
     */
    private Map<String, Object> getEntityValue(String groupValue, Map<String, Object> map) {
        Map<String, Object> matchMap = new HashMap<>();
        if (groupValue.contains(".")) {
            String[] groups = groupValue.split("\\.");
            Object value = null;
            for (String group : groups) {
                Object object = map.get(group);
                if (object instanceof Map) {
                    map = (Map) object;
                } else {
                    value = object;
                }
            }
            matchMap.put(groupValue, value);
        }
        return matchMap;
    }

    /**
     * 获取相应的指定日期格式数据
     *
     * @param date   日期
     * @param format 日期格式
     * @return 返回想要的数据源
     */
    private String getDate(Date date, String format) {
        return DateUtil.dateToString(date, format);
    }

    private void saveSerialNumberConfig(DtoSerialNumberConfig dtoSerialNumberConfig, String serialType) {
        redisTemplate.opsForValue().set(serialType + PrincipalContextUser.getPrincipal().getOrgId(), JsonStream.serialize(dtoSerialNumberConfig), 1800, TimeUnit.SECONDS);
    }
}
