package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.DtoConsumableStandard;
import com.sinoyd.base.dto.customer.DtoConsumableTemplate;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableExtend;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableOfMixed;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableStandardExtend;
import com.sinoyd.lims.lim.service.DownLoadConsumableTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DownLoadConsumableTemplateServiceImpl implements DownLoadConsumableTemplateService {

    private static List<String> consCategoryList = new ArrayList<>();

    private static List<String> consGradeList = new ArrayList<>();

    @Autowired
    private CodeService codeService;

    @Autowired
    private ImportUtils importUtils;

    @Autowired
    private DimensionRepository dimensionRepository;

    /**
     * 下载消耗品导入模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadConsumable(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        consCategoryList = codeService.findCodes("LIM_ConsumableCategory").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        consGradeList = codeService.findCodes("LIM_ConsumableGrade").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        List<DtoImportConsumableExtend> extendList = getExtendData(consCategoryList, consGradeList);
        List<DtoConsumableTemplate> dataList = new ArrayList<>();
        dataList.add(new DtoConsumableTemplate());
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoConsumableTemplate.class, DtoImportConsumableExtend.class, dataList, extendList);
        workBook.setSheetHidden(1, true);
        String[] consCategoryArr = new String[consCategoryList.size()];
        String[] consGradeArr = new String[consGradeList.size()];
        for (int i = 0; i < consCategoryList.size(); i++) {
            consCategoryArr[i] = consCategoryList.get(i);
        }
        for (int i = 0; i < consGradeList.size(); i++) {
            consGradeArr[i] = consGradeList.get(i);
        }
        //消耗品分类下拉
        importUtils.selectList(workBook, 2, 2, consCategoryArr);
        //消耗品分类下拉
        importUtils.selectList(workBook, 4, 4, consGradeArr);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    /**
     * 标样模板下载
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadStandard(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        consCategoryList = codeService.findCodes("LIM_StandardCategory").stream().map(DtoCode::getDictName).collect(Collectors.toList());

        consGradeList = codeService.findCodes("LIM_ConsumableGrade").stream().map(DtoCode::getDictName).collect(Collectors.toList());

        List<DtoDimension> dtoDimensionList = dimensionRepository.findAll();
        List<String> dimensionNameList = dtoDimensionList.stream().filter(d-> StringUtil.isNotEmpty(d.getDimensionName())).map(DtoDimension::getDimensionName).collect(Collectors.toList());

        List<DtoImportConsumableStandardExtend> extendList = getStandardExtendData(consCategoryList, consGradeList);

        List<DtoConsumableStandard> consumables = new ArrayList<>();
        DtoConsumableStandard consumable = new DtoConsumableStandard();
        consumables.add(consumable);

        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoConsumableStandard.class, DtoImportConsumableStandardExtend.class, consumables, extendList);
        String[] consCategoryArr = new String[consCategoryList.size()];
        String[] consGradeArr = new String[consGradeList.size()];
        for (int i = 0; i < consCategoryList.size(); i++) {
            consCategoryArr[i] = consCategoryList.get(i);
        }
        for (int i = 0; i < consGradeList.size(); i++) {
            consGradeArr[i] = consGradeList.get(i);
        }
        importUtils.selectList(workBook, 3, 3, consCategoryArr);
        importUtils.selectList(workBook, 5, 5, consGradeArr);
        importUtils.selectList(workBook, 15, 15, EnumBase.EnumUncertainType.getNames());
        importUtils.selectListMoreThan255(workBook,"量纲下拉",2,0,14,14, dimensionNameList.toArray(new String[0]));
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
        //endregion
    }

    /**
     * 下载标样混标导入模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadConsumableOfMixed(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        List<DtoImportConsumableOfMixed> emptyList = new ArrayList<>();
        DtoImportConsumableOfMixed dto = new DtoImportConsumableOfMixed();
        emptyList.add(dto);
        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportConsumableOfMixed.class, emptyList);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
        //endregion
    }


    /**
     * 获取标样导出模板的拓展数据
     *
     * @param consCategoryList 标样类型
     * @param consGradeList    等级
     * @return 标样导出关联数据
     */
    private List<DtoImportConsumableStandardExtend> getStandardExtendData(List<String> consCategoryList, List<String> consGradeList) {
        //返回的数据集合
        List<DtoImportConsumableStandardExtend> extendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(consCategoryList.size());
        size.add(consGradeList.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportConsumableStandardExtend extendData = new DtoImportConsumableStandardExtend();
            extendData.setConsumableCategory(consCategoryList.size() < i + 1 ? null : consCategoryList.get(i));
            extendData.setGrade(consGradeList.size() < i + 1 ? null : consGradeList.get(i));
            extendList.add(extendData);
        }
        return extendList;
    }


    /**
     * 获取消耗品导出模板的拓展数据
     *
     * @param consCategoryList 消耗品类型
     * @param consGradeList    等级
     * @return 消耗品关联数据
     */
    private List<DtoImportConsumableExtend> getExtendData(List<String> consCategoryList, List<String> consGradeList) {
        //返回的数据集合
        List<DtoImportConsumableExtend> extendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(consCategoryList.size());
        size.add(consGradeList.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportConsumableExtend extendData = new DtoImportConsumableExtend();
            extendData.setConsumableCategory(consCategoryList.size() < i + 1 ? null : consCategoryList.get(i));
            extendData.setGrade(consGradeList.size() < i + 1 ? null : consGradeList.get(i));
            extendList.add(extendData);
        }
        return extendList;
    }

}
