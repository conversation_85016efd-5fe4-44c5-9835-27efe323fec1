package com.sinoyd.lims.lim.service.sync;

import com.sinoyd.base.service.impl.SyncDataServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.repository.lims.ReportConfigRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 报表配置同步业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/14
 */
@Service
public class ReportConfigSyncServiceImpl extends SyncDataServiceImpl<DtoReportConfig, String, ReportConfigRepository> {

    @Override
    public boolean needPrintMessage() {
        return true;
    }

    @Override
    public List<String> getPrintFieldNames() {
        return Stream.of("reportCode", "templateName").collect(Collectors.toList());
    }

    @Override
    public List<String> getUnSyncFields() {
        List<String> fields = super.getUnSyncFields();
        fields.add("controlNum");
        fields.add("versionNum");
        fields.add("reportName");
        fields.add("isDefineFileName");
        fields.add("defineFileName");
        fields.add("beanName");
        return fields;
    }
}
