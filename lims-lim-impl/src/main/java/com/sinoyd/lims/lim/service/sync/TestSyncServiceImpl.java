package com.sinoyd.lims.lim.service.sync;

import com.jsoniter.output.JsonStream;
import com.sinoyd.base.service.impl.SyncDataServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 测试项目同步业务实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/3/14
 */
@Service
public class TestSyncServiceImpl extends SyncDataServiceImpl<DtoTest, String, TestRepository> {
    @Override
    public void saveRedis(DtoTest dtoTest) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
        redisTemplate.opsForHash().put(key, dtoTest.getId(), JsonStream.serialize(dtoTest));
    }

    @Override
    public boolean needPrintMessage() {
        return true;
    }

    @Override
    public List<String> getPrintFieldNames() {
        return Collections.singletonList("testName");
    }

    @Override
    public List<String> getUnSyncFields() {
        List<String> fields = super.getUnSyncFields();
        //检测资质
        fields.add("cert");
        //是否现场数据
        fields.add("isCompleteField");
        // 验证状态
        fields.add("validate");
        // 使用次数
        fields.add("usageNum");
        //上海监管平台分析方法id
        fields.add("shMethodId");
        //上海监管平台采样方法名称
        fields.add("shMethodName");
        //上海监管平台采样方法id
        fields.add("shSamplingMethodId");
        //上海监管平台采样方法名称
        fields.add("shSamplingMethodName");
        //是否与上海监管平台分析方法完美匹配
        fields.add("shMatchStatus");
        //上海监管平台分析方法匹配信息
        fields.add("shMatchMessage");
        return fields;
    }
}
