package com.sinoyd.lims.lim.repository.lims;

import java.util.List;


import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * FolderRepository数据接口
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
public interface FolderRepository extends IBaseJpaPhysicalDeleteRepository<DtoFolder,String>
{

    /**
     * 通过文件夹的parentId获取文件夹
     * @param patentId 文件夹的parentId
     * @return 返回查询到文件夹个数
     */
    @Query("select count(p.id) from DtoFolder p where p.id = :patentId")
    Integer getCount(@Param("patentId")String patentId);

    /**
     * 根据人员id来判断文件权限
     * @param id 文件夹id
     * @param creator 人员id
     * @return 返回查询到文件夹个数
     */
    @Query("select count(p) from DtoFolder p where p.id = :id and p.creator = :creator")
    Integer validateAuth(@Param("id")String id,@Param("creator")String creator);

    /**
     * 通过父id查找所有子节点
     * @param parentId 文件夹parentId集合
     * @return 返回id集合
     */
    @Query("select p.id from DtoFolder p where p.parentId in :parentId")
    List<String> getRowIdListByParentId(@Param("parentId") List<String> parentId);

    /**
     * 查找名称重复的文件夹(同级目录没有重复即可)
     * @param folderName 文件夹名称
     * @param parentId 文件夹parentId
     * @return 返回查找到文件夹数量
     */
    @Query("select count(p) from DtoFolder p where p.folderName = :folderName and p.parentId = :parentId and p.id != :id")
    Integer countByNameAndParentId(@Param("folderName")String folderName,@Param("id")String id,@Param("parentId")String parentId);

    /**
     * 查找名称重复的文件夹
     * @param folderName 文件夹名称
     * @param id 文件夹id
     * @return 返回查找到文件夹数量
     */
    @Query("select count(p) from DtoFolder p where p.folderName = :folderName and p.id != :id")
    Integer countByNameAndId(@Param("folderName")String folderName,@Param("id")String id);

    /**
     * 判断该节点有没有子节点
     * @param parentId 文件夹parentId
     * @return 返回查找到文件夹数量
     */
    @Query("select count(p.id) from DtoFolder p where p.parentId = :parentId")
    Integer countByParentId(@Param("parentId")String parentId);
}