package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableExtend;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpStandard;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 标准物质导入更新数据校验器
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/12
 * @since V100R001
 */
@Component
@Data
public class ImpModifyStandardVerify implements IExcelVerifyHandler<DtoExpImpStandard> {

    /**
     * 临时数据
     */
    private ThreadLocal<List<DtoExpImpStandard>> standardTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoEnterprise>> enterpriseTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoCode>> gradesTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoCode>> standardCategoryTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoPerson>> personTl = new ThreadLocal<>();
    private ThreadLocal<Boolean> isImportStandardTypeTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoImportConsumableExtend>> importConsumableExtend = new ThreadLocal<>();
    private ThreadLocal<List<DtoDimension>> dimensionList = new ThreadLocal<>();
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();


    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpStandard dto) {
        //region 导入数据处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //endregion

        // 默认值
        //region 参数
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误总数据
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验有误");
        // 校验数据
        List<DtoEnterprise> dtoEnterprises = enterpriseTl.get();
        List<DtoCode> grades = gradesTl.get();
        List<DtoCode> standardCategoryCode = standardCategoryTl.get();
        List<DtoPerson> personList = personTl.get();
        Boolean isImportStandardType = isImportStandardTypeTl.get();
        List<DtoImportConsumableExtend> importConsumableExtends = importConsumableExtend.get();
        List<DtoDimension> dtoDimensions = dimensionList.get();
        //获取临时仪器数据集
        List<DtoExpImpStandard> impStandards = standardTl.get();
        if (StringUtil.isEmpty(impStandards)) {
            impStandards = new ArrayList<>();
        }
        // 必填校验
        importUtils.checkIsNull(result, dto.getConsumableName(), "标准样品名称", failStr);
        importUtils.checkIsNull(result, dto.getCategoryId(), "标准样品分类", failStr);
        importUtils.checkIsNull(result, dto.getInventory(), "库存数量", failStr);
        importUtils.checkIsNull(result, dto.getStorageDate(), "入库日期", failStr);
        importUtils.checkIsNull(result, dto.getExpiryDate(), "有效日期", failStr);
        importUtils.checkIsNull(result, dto.getIsLabEncryption(), "是否实验室加密", failStr);
        importUtils.checkIsNull(result, dto.getSendWarnUserName(), "管理员", failStr);


        //校验部门是否存在
        isExistEnterprises(result, dtoEnterprises, dto, failStr);
        isExistGrades(result, grades, dto, failStr);
        isExistStandardCategory(result, standardCategoryCode, dto, failStr, isImportStandardType, importConsumableExtends);
        isExistPerson(result, personList, dto, failStr);
        //判断有效日期前后性
        checkDateIsBefore(dto, result, failStr);
        isExistDimension(result, dtoDimensions, dto, failStr);
        //校验区间类型
        checkRangeType(dto, result, failStr);

        // 采购时间格式校验
        importUtils.checkDateTwo(result, dto.getStorageDate(), ",入库日期", failStr);
        importUtils.checkDateTwo(result, dto.getExpiryDate(), ",有效日期", failStr);
        importUtils.checkDateTwo(result, dto.getOrderTime(), ",定值日期", failStr);
        importUtils.checkNumTwo(result, dto.getInventory(), "库存数量", failStr);
        importUtils.checkNumTwo(result, dto.getWarningNum(), "库存警告数量", failStr);
        importUtils.checkNumTwo(result, dto.getUnitPrice(), "单价", failStr);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        impStandards.add(dto);
        standardTl.set(impStandards);

        return result;
    }

    /**
     * 不确定度类型为区间时，高低点必填
     *
     * @param result  返回结果
     * @param dto     导入数据
     * @param failStr 校验错误信息
     */
    private void checkRangeType(DtoExpImpStandard dto, ExcelVerifyHandlerResult result, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getUncertainTypeName())
                && EnumBase.EnumUncertainType.区间.getValue().equals(EnumBase.EnumUncertainType.getValueByName(dto.getUncertainTypeName()))
                && (StringUtil.isEmpty(dto.getRangeLow()) || StringUtil.isEmpty(dto.getRangeHigh()))) {
            result.setSuccess(false);
            failStr.append("；不确定度类型为区间时，区间范围高低点必填");
        }
    }

    /**
     * 校验量纲和单位
     *
     * @param result        返回结果
     * @param dtoDimensions 量纲集合
     * @param dto           导入数据
     * @param failStr       校验错误信息
     */
    private void isExistDimension(ExcelVerifyHandlerResult result, List<DtoDimension> dtoDimensions, DtoExpImpStandard dto, StringBuilder failStr) {

        if (StringUtil.isNotEmpty(dto.getUnit())) {
            List<DtoDimension> dimensions = dtoDimensions.stream().filter(p -> dto.getUnit().equals(p.getDimensionName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(dimensions)) {
                result.setSuccess(false);
                failStr.append("；单位不存在");
            } else {
                dto.setUnitId(dimensions.get(0).getId());
            }
        } else {
            dto.setUnitId(UUIDHelper.GUID_EMPTY);
        }

        if (StringUtil.isNotEmpty(dto.getDimensionName())) {
            List<DtoDimension> dimensions = dtoDimensions.stream().filter(p -> dto.getDimensionName().equals(p.getDimensionName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(dimensions)) {
                result.setSuccess(false);
                failStr.append("；量纲不存在");
            } else {
                dto.setDimensionId(dimensions.get(0).getId());
            }
        } else {
            dto.setDimensionId(UUIDHelper.GUID_EMPTY);
        }

    }

    /**
     * 比较有效日期与入库日期
     *
     * @param dto     导入数据
     * @param result  返回结果
     * @param failStr 校验错误信息
     */
    private void checkDateIsBefore(DtoExpImpStandard dto, ExcelVerifyHandlerResult result, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getExpiryDate()) && StringUtil.isNotEmpty(dto.getStorageDate())) {
            Date expiryDate = importUtils.stringToDateAllFormat(dto.getExpiryDate());
            Date storageDate = importUtils.stringToDateAllFormat(dto.getStorageDate());
            if (storageDate.compareTo(expiryDate) > 0) {
                result.setSuccess(false);
                failStr.append("；有效日期不能小于入库日期");
            }
        }
    }

    private void isExistPerson(ExcelVerifyHandlerResult result, List<DtoPerson> personList, DtoExpImpStandard dto, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getSendWarnUserName())) {
            List<DtoPerson> isExistPerson = personList.stream().filter(p -> dto.getSendWarnUserName().equals(p.getCName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistPerson)) {
                result.setSuccess(false);
                failStr.append("；管理人员不存在");
            }
        }
    }

    private void isExistStandardCategory(ExcelVerifyHandlerResult result,
                                         List<DtoCode> standardCategoryCode,
                                         DtoExpImpStandard dto,
                                         StringBuilder failStr,
                                         Boolean isImportStandardType,
                                         List<DtoImportConsumableExtend> importConsumableExtends) {
        if (StringUtil.isNotEmpty(dto.getCategoryId())) {
            if (!isImportStandardType) {
                List<DtoCode> list = standardCategoryCode.stream().filter(p -> dto.getCategoryId().equals(p.getDictName())).collect(Collectors.toList());
                if (StringUtil.isEmpty(list)) {
                    result.setSuccess(false);
                    failStr.append("；标准样品分类不存在");
                }
            } else {
                List<String> categoryNames = importConsumableExtends.stream().map(DtoImportConsumableExtend::getConsumableStandardCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
                List<String> isExistExtend = categoryNames.stream().filter(p -> dto.getCategoryId().equals(p)).collect(Collectors.toList());
                if (StringUtil.isEmpty(isExistExtend)) {
                    result.setSuccess(false);
                    failStr.append("；标准样品类型在关联表中不存在");
                }
            }

        }
    }

    private void isExistGrades(ExcelVerifyHandlerResult result, List<DtoCode> grades, DtoExpImpStandard dto, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getGrade())) {
            List<DtoCode> list = grades.stream().filter(p -> dto.getGrade().equals(p.getDictName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(list)) {
                result.setSuccess(false);
                failStr.append("；等级不存在");
            }
        }
    }

    private void isExistEnterprises(ExcelVerifyHandlerResult result, List<DtoEnterprise> dtoEnterprises, DtoExpImpStandard dto, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getSupplierName())) {
            List<DtoEnterprise> isExistEnt = dtoEnterprises.stream().filter(p -> dto.getSupplierName().equals(p.getName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistEnt)) {
                result.setSuccess(false);
                failStr.append("；供应厂商不存在");
            }
        }
    }


}
