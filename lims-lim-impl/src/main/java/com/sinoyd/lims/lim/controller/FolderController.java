package com.sinoyd.lims.lim.controller;

import java.util.List;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.lims.lim.criteria.FolderCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import com.sinoyd.lims.lim.service.FolderService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 文件夹管理
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Api(tags = "文件夹管理服务: 文件夹管理服务")
@RestController
@RequestMapping("/api/base/folder")
@Validated
public class FolderController extends BaseJpaController<DtoFolder, String, FolderService>{

    /**
     * 新增
     * @param folder
     */
    @ApiOperation(value = "新增文件夹", notes = "新增文件夹")
    @PostMapping("")
    public RestResponse<DtoFolder> create(@Validated @RequestBody DtoFolder folder)
    {
        RestResponse<DtoFolder> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoFolder data = service.save(folder);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新
     * @param folder
     */
    @ApiOperation(value = "更新文件夹", notes = "更新文件夹")
    @PutMapping("")
    public RestResponse<DtoFolder> update(@Validated @RequestBody DtoFolder folder)
    {
        RestResponse<DtoFolder> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoFolder data = service.update(folder);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 批量删
     * @param ids
     */
    @ApiOperation(value = "批量删除文件夹", notes = "批量删除文件夹")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids)
    {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

     /**
     * 单个删
     * @param id
     */
    @ApiOperation(value = "根据id删除文件夹", notes = "根据id删除文件夹")
    @DeleteMapping("/{id}")
    public RestResponse<String> deleteOne(@PathVariable("id")String id)
    {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.delete(id);
     
        return restResponse;
    }

     /**
     * 单个获取
     * @param id
     */
    @ApiOperation(value = "通过id获取单个文件夹", notes = "通过id获取单个文件夹")
    @GetMapping("/{id}")
    public RestResponse<DtoFolder> get(@PathVariable("id")String id)
    {
        RestResponse<DtoFolder> restResponse = new RestResponse<>();
        DtoFolder folder = service.findOne(id);
        restResponse.setData(folder);
        restResponse.setRestStatus(StringUtil.isNull(folder) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
       
        return restResponse;
    }


    /**
     * 分页获取
     * @param folderCriteria 封装的参数有当前文件夹的id
     * @return
     */
    @ApiOperation(value = "分页动态条件查询文件夹", notes = "分页动态条件查询文件夹")
    @GetMapping("")
    public RestResponse<List<DtoFolder>> findByPage(FolderCriteria folderCriteria)
    {
        RestResponse<List<DtoFolder>> restResponse = new RestResponse<>();
        PageBean<DtoFolder> page = super.getPageBean();
        service.findByPage(page, folderCriteria);

        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());
     
        return restResponse;

    }

    /**
     * 文件树结构
     * @return RestResponse<List<TreeNode>>
     */
    @ApiOperation(value = "文件树结构", notes = "文件树结构")
    @GetMapping("/folderTree")
    public RestResponse<List<TreeNode>> bigTree() {
        RestResponse<List<TreeNode>> response = new RestResponse<>();
        response.setData(service.folderTree());
        return response;
    }

}