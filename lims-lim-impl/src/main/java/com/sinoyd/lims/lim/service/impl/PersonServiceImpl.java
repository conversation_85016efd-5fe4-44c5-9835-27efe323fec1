package com.sinoyd.lims.lim.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.*;
import com.sinoyd.frame.service.*;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.customer.DtoPersonCertQuery;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.entity.Person;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.service.PersonAbilityService;
import com.sinoyd.lims.lim.service.PersonCertService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.vo.PersonVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 人员管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class PersonServiceImpl extends BaseJpaServiceImpl<DtoPerson, String, PersonRepository>
        implements PersonService {

    @Autowired
    private PersonCertRepository personCertRepository;

    @Autowired
    @Lazy
    private PersonCertService personCertService;

    @Autowired
    private UserService userService;

    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    @Lazy
    private PersonAbilityService personAbilityService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private PersonAbilityRepository personAbilityRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private Person2TestRepository person2TestRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Lazy
    @Autowired
    private RoleService roleService;

    @Autowired
    private TestPost2PersonRepository testPost2PersonRepository;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private SampleTypeService sampleTypeService;

    private TestRepository testRepository;

    // 分页查询
    @Override
    public void findByPage(PageBean<DtoPerson> pageBean, BaseCriteria baseCriteria) {
        pageBean.setEntityName("DtoPerson p");
        pageBean.setSelect("select p");

        super.findByPage(pageBean, baseCriteria);

        List<DtoPerson> list = pageBean.getData();

        if (StringUtils.isNotNullAndEmpty(list)) {
            List<DtoDepartment> departments = departmentService.findAll();
            List<String> personIds = list.stream().map(Person::getId).distinct().collect(Collectors.toList());
            List<DtoPersonCert> personCertList = new ArrayList<>();
            List<DtoDocument> photoList = new ArrayList<>();
            if (personIds.size() > 0) {
                personCertList = personCertRepository.findByPersonIds(personIds);
                //BASE_DocumentExtendType_Signature
                photoList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(personIds, "BASE_DocumentExtendType_PersonPhotos");
            }
            for (DtoPerson person : list) {
                departments.stream().filter(d -> d.getId().equals(person.getDeptId())).findFirst().ifPresent(d -> person.setDeptName(d.getDeptName()));
                person.setWorkYear(calculationWorkYear(person.getJoinCompanyTime()));
                person.setBase64Content(documentService.convertBase64Content(person.getPhotoUrl(), 40, 40));
                if (StringUtil.isNotEmpty(personCertList)) {
                    List<DtoPersonCert> personCertByPersonList = personCertList.stream().filter(p -> p.getPersonId().equals(person.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotNull(personCertByPersonList) && personCertByPersonList.size() > 0) {
                        person.setCerts(personCertByPersonList);
                    }
                }
                if (StringUtil.isNotEmpty(photoList)) {
                    List<DtoDocument> personPhotoList = photoList.parallelStream().filter(p -> person.getId().equals(p.getFolderId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(personPhotoList)) {
                        personPhotoList.sort(Comparator.comparing(DtoDocument::getCreateDate, Comparator.reverseOrder()));
                        person.setPhotoPath(personPhotoList.get(0).getPath());
                    }
                }
            }
        }
        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    // 新增人员
    @Transactional
    @Override
    public DtoPerson save(DtoPerson person) {

        if (repository.getCountByUserNoAndId(person.getUserNo(), person.getId()) > 0) {
            throw new BaseException("系统已存在相同的人员编号！");
        }
        if (StringUtils.isNotNullAndEmpty(person.getCName())) {
            person.setFullPinYin(PinYinUtil.getFullSpell(person.getCName()));
            person.setPinYin(PinYinUtil.getFirstSpell(person.getCName()));
        }
        DtoPerson dto = super.save(person);
        List<DtoPersonCert> newCerts = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(person.getCerts())) {

            List<DtoPersonCert> personCerts = new ArrayList<>();
            for (DtoPersonCert cert : person.getCerts()) {
                if (StringUtils.isNotNullAndEmpty(cert.getCertCode())) {
                    cert.setPersonId(dto.getId());
                    personCerts.add(cert);
                }
            }
            newCerts = personCertService.save(personCerts);
        }
        dto.setCerts(newCerts);
        return dto;
    }

    /**
     * 修改人员信息,若有删除的证书则级联删除证书相关检测能力
     *
     * @param person 人员对象
     * @return 人员信息
     */
    @Transactional
    @Override
    public DtoPerson update(DtoPerson person) {

        if (repository.getCountByUserNoAndId(person.getUserNo(), person.getId()) > 0) {
            throw new BaseException("系统已存在相同的人员编号！");
        }
        List<DtoPersonCert> newCerts = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(person.getCerts())) {
            personCertRepository.deleteByPersonId(person.getId());

            List<DtoPersonCert> personCerts = new ArrayList<>();
            for (DtoPersonCert cert : person.getCerts()) {
//                if (StringUtils.isNotNullAndEmpty(cert.getId()) && !BaseCodeHelper.GUID_EMPTY.toString().equals(cert.getId())) {
//                    personCertService.update(cert);
//                } else {
                cert.setPersonId(person.getId());
//                }
                personCerts.add(cert);
            }

            newCerts = personCertService.save(personCerts);
        }
        if (StringUtils.isNotNullAndEmpty(person.getCName())) {
            person.setFullPinYin(PinYinUtil.getFullSpell(person.getCName()));
            person.setPinYin(PinYinUtil.getFirstSpell(person.getCName()));
        }
        Boolean isEnabled = true;
        if (StringUtil.isNotNull(person.getStatus()) && person.getStatus().equals(EnumLIM.EnumPersonStatus.离职.getValue())) {
            isEnabled = false;
        }
        userService.updateAccount(person.getId(), isEnabled);
        DtoPerson dtoPerson = super.update(person);
        dtoPerson.setCerts(newCerts);
        // 若有删除的证书,级联删除相关检测能力
        // 查询人员相关的证书
        List<DtoPersonCert> exitCerts = personCertRepository.findByPersonId(person.getId());
        // 查询人员相关的检测能力
        List<DtoPersonAbility> personAbilityList = personAbilityRepository.findByPersonId(person.getId());
        Map<String, List<DtoPersonAbility>> personAblitiesMap = personAbilityList.stream().collect(Collectors.groupingBy(DtoPersonAbility::getPersonCertId));
        // 获取证书id集合
        List<String> certIds = exitCerts.stream().map(c -> c.getId()).collect(Collectors.toList());
        // 进行删除的证书id定义
        List<String> deleteCertIds = new ArrayList<>();
        // 人员相关检测能力非空判断
        if (StringUtil.isEmpty(personAblitiesMap)) {
            return dtoPerson;
        }
        for (String key : personAblitiesMap.keySet()) {
            if (!certIds.contains(key)) {
                deleteCertIds.add(key);
            }
        }
        // 如果有删除的证书,则级联删除相关检测能力
        if (StringUtil.isNotEmpty(deleteCertIds)) {
            personAbilityRepository.deleteByCertIds(deleteCertIds);
        }
        return dtoPerson;
    }

    // 开通账户
    @Override
    @Transactional
    public Boolean openAccount(String id, String account, String password, List<String> roleIds, List<DtoLoginExpand> loginExpands) {
        DtoUser dtoUser = userService.findByUserId(id);
        if (StringUtil.isNotNull(dtoUser)) {
            throw new BaseException(("当前人员已经开通了账户"));
        }
        DtoPerson dtoPerson = findOne(id);
        if (StringUtil.isNull(dtoPerson)) {
            throw new BaseException("请先创建人员信息！");
        }
        if (dtoPerson.getStatus().equals(EnumLIM.EnumPersonStatus.离职.getValue())) {
            throw new BaseException(("当前人员已离职无法开通账户"));
        }
        dtoUser = new DtoUser();
        dtoUser.setId(id);
        dtoUser.setDeptId(dtoPerson.getDeptId());
        dtoUser.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        dtoUser.setTelephone(account);
        dtoUser.setLoginId(account);
        dtoUser.setUserName(dtoPerson.getCName());
        dtoUser.setBirthDay(dtoPerson.getBirthDay());
        dtoUser.setEmail(dtoPerson.getEmail());
        dtoUser.setSex(String.valueOf(dtoPerson.getSex()));

        //如果开通账号没有选择角色，则需要给予默认角色：所有人员
        if (StringUtil.isEmpty(roleIds)) {
            List<DtoRole> roleList = roleService.findAll();
            Optional<DtoRole> roleOptional = roleList.parallelStream()
                    .filter(p -> "所有人员".equals(p.getRoleName())).findFirst();
            if (roleOptional.isPresent()) {
                roleIds = Stream.of(roleOptional.get().getRoleId()).collect(Collectors.toList());
            } else {
                throw new BaseException("请先在角色管理中配置角色: 所有人员");
            }
        }

        return userService.openAccount(dtoUser, password, roleIds, loginExpands);
    }

    /**
     * @param id    人员id
     * @param cName
     * @return
     */
    @Override
    public DtoPerson findPersonAttachment(String id, String cName) {
        DtoPerson dtoPerson = repository.findOne(id);
        if (StringUtil.isNull(dtoPerson)) {
            dtoPerson = new DtoPerson();
            dtoPerson.setId(id);
            dtoPerson.setCName(cName);
        }
        return dtoPerson;
    }


    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回人员对象
     */
    @Override
    public DtoPerson findOne(String id) {
        return findOne(id, false);
    }

    @Override
    public DtoPerson findOne(String id, Boolean isOnlyReturnPerson) {
        DtoPerson dtoPerson = super.findOne(id);
        if (!isOnlyReturnPerson && StringUtil.isNotNull(dtoPerson)) {
            //返回人员证书信息
            List<String> personIds = new ArrayList<>();
            personIds.add(id);
            List<DtoPersonCert> personCertList = personCertRepository.findByPersonIds(personIds);
            dtoPerson.setWorkYear(calculationWorkYear(dtoPerson.getJoinCompanyTime()));
            dtoPerson.setBase64Content(documentService.convertBase64Content(dtoPerson.getPhotoUrl()));
            if (StringUtils.isNotNullAndEmpty(personCertList)) {
                for (DtoPersonCert dtoPersonCert : personCertList) {
                    dtoPersonCert.setBase64Content(documentService.convertBase64Content(dtoPersonCert.getPhoneUrl()));
                }
                dtoPerson.setCerts(personCertList);
            }
        }
        return dtoPerson;
    }

    @Override
    public String findPersonNameById(String id) {
        return repository.findPersonNameById(id);
    }

    /**
     * 为拼音字段为空的人员,添加拼音
     *
     * @return
     */
    @Transactional
    @Override
    public List<DtoPerson> changePinYinFull() {
        List<DtoPerson> personList = repository.findByPinYinIsNotExit();
        for (DtoPerson person : personList) {
            person.setFullPinYin(PinYinUtil.getFullSpell(person.getCName()));
        }
        return super.update(personList);
    }

    @Override
    public List<DtoPerson> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoPerson> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 返回人员列表
     *
     * @param queryDto 查询dto
     * @return 返回人员对象
     */
    @Override
    public List<DtoKeyValue> query(DtoPersonQuery queryDto) {
        List<OrgModel> orgList = orgService.selectList(new EntityWrapper<>());
        // 处理orgId
        String orgId;
        if (StringUtil.isNotEmpty(queryDto.getOrgCode())) {
            OrgModel orgModel = orgList.stream().filter(p -> queryDto.getOrgCode().equals(p.getOrgCode())).findFirst()
                    .orElseThrow(() -> new BaseException("机构编码不正确"));
            orgId = orgModel.getId();
        } else {
            orgId = PrincipalContextUser.getPrincipal().getOrgId();
        }
        //排除重复数据
        List<String> userIds = new ArrayList<>();
        //获取到权限的编码
        Boolean isPermission = false;//是否要根据权限过滤
        List<String> permissionList = queryDto.getPermission();
        if (StringUtil.isNotEmpty(permissionList)) {
            List<DtoUser> userList = authorizeService.findUserByAuthorize(permissionList, Boolean.FALSE);
            userIds.addAll(userList.stream().map(DtoUser::getId).distinct().collect(Collectors.toList()));
            isPermission = true;
        }
        //如果前端未传状态或者传的状态未赋值，后端自动补上全部的值的状态
        if (StringUtil.isEmpty(queryDto.getStatus())) {
            Integer[] status = new Integer[]{EnumLIM.EnumPersonStatus.休假.getValue(), EnumLIM.EnumPersonStatus.在职.getValue(), EnumLIM.EnumPersonStatus.离职.getValue()};
            queryDto.setStatus(Arrays.asList(status));
        }
        List<DtoKeyValue> keyValues = new ArrayList<>();
        if (StringUtil.isNotNull(queryDto.getIsUser()) && queryDto.getIsUser()) {
            //用户不根据状态过滤了，如果显示人员，按状态过滤
            List<DtoUser> userList = new ArrayList<>();
            if (isPermission && StringUtils.isNotNullAndEmpty(userIds)) { //要按权限过滤，并且权限查询出数据的
                userList = userService.findByIds(userIds);
            } else if (!isPermission) {//不按权限过滤
                userList = userService.findAll();
            }
            if (StringUtil.isNotEmpty(userList)) {
                if (StringUtil.isNotEmpty(queryDto.getPostId())) {
                    //根据岗位id获取人员列表
                    List<DtoTestPost2Person> postToPersonList = testPost2PersonRepository.findByTestPostId(queryDto.getPostId());
                    //获取人员id集合
                    List<String> personIds = postToPersonList.stream().map(DtoTestPost2Person::getPersonId).collect(Collectors.toList());
                    //获取人员集合数据
                    if (StringUtil.isNotEmpty(personIds)) {
                        userList = userList.stream().filter(p -> personIds.contains(p.getId())).collect(Collectors.toList());
                    }
                }
                userList.sort(Comparator.comparing(PersonServiceImpl::sortUser, Comparator.reverseOrder()));
            }
            List<DtoPerson> personList = new ArrayList<>();
            if (userList.size() > 0) {
                personList = repository.findByIdIn(userList.stream().map(DtoUser::getId).collect(Collectors.toList()));
                personList = personList.stream().filter(p -> orgId.equals(p.getOrgId())
                        && queryDto.getStatus().contains(p.getStatus())).collect(Collectors.toList());
            }
            Comparator<DtoPerson> sortPinYing = getComparator(personList);
            personList.sort(sortPinYing);
            keyValues = personList.stream().map(p -> new DtoKeyValue() {
                @Override
                public Object getValue() {
                    return p.getId();
                }

                @Override
                public Object getKey() {
                    return p.getCName();
                }

                @Override
                public String getOrgName() {
                    return loadPersonOrgName(p, orgList);
                }
            }).collect(Collectors.toList());
        } else {
            List<DtoPerson> personList = new ArrayList<>();
            if (isPermission && userIds.size() > 0) {
                personList = repository.findPersonByStatusAndId(userIds, queryDto.getStatus());
            } else if (!isPermission) {
                personList = repository.findPersonByStatus(queryDto.getStatus());
            }
            personList = personList.stream().filter(p -> orgId.equals(p.getOrgId()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(personList)) {
                if (StringUtil.isNotEmpty(queryDto.getPostId())) {
                    //根据岗位id获取人员列表
                    List<DtoTestPost2Person> postToPersonList = testPost2PersonRepository.findByTestPostId(queryDto.getPostId());
                    //获取人员id集合
                    List<String> personIds = postToPersonList.stream().map(DtoTestPost2Person::getPersonId).collect(Collectors.toList());
                    //获取人员集合数据
                    if (StringUtil.isNotEmpty(personIds)) {
                        personList = personList.stream().filter(p -> personIds.contains(p.getId())).collect(Collectors.toList());
                    }
                }
                personList.sort(Comparator.comparing(DtoPerson::getOrderNum, Comparator.reverseOrder()));
            }
            Comparator<DtoPerson> sortPinYing = getComparator(personList);
            personList.sort(sortPinYing);
            keyValues = personList.stream().map(p -> new DtoKeyValue() {
                @Override
                public Object getValue() {
                    return p.getId();
                }

                @Override
                public Object getKey() {
                    return p.getCName();
                }

                @Override
                public String getOrgName() {
                    return loadPersonOrgName(p, orgList);
                }
            }).collect(Collectors.toList());
        }
        // 校验当前登录人是否为远大管理员
        List<DtoUser> userByRule = userService.findByRoleCode(LimCodeHelper.SINOYD_ADMINISTRATORS);
        DtoUser user = userByRule.stream().filter(p -> p.getId().equals(PrincipalContextUser.getPrincipal().getUserId())).findFirst().orElse(null);
        if (null != user && keyValues.stream().noneMatch(p -> p.getValue().equals(PrincipalContextUser.getPrincipal().getUserId()))) {
            DtoKeyValue dtoKeyValue = new DtoKeyValue();
            dtoKeyValue.setValue(user.getId());
            dtoKeyValue.setKey(user.getUserName());
            dtoKeyValue.setOrgName(loadPersonOrgName(user.getOrgId(), orgList));
            keyValues.add(dtoKeyValue);
        }
//        return keyValues.stream().sorted(Comparator.comparing(t -> t.getKey().toString(), Comparator.reverseOrder())).collect(Collectors.toList());
        return keyValues;
    }

    /**
     * 定义比较器
     *
     * @param personList 排序的Map
     * @return 比较器
     */
    private Comparator<DtoPerson> getComparator(List<DtoPerson> personList) {
        return (a, b) -> SortUtil.compareString(a.getPinYin(), b.getPinYin());
    }

    /**
     * 查询人员（带实体字段名）
     *
     * @param queryDto 查询条件
     * @return 人员数据
     */
    @Override
    public List<Map<String, Object>> queryOnlyName(DtoPersonQuery queryDto) {
        List<Map<String, Object>> personResult = new ArrayList<>();
        List<DtoKeyValue> query = query(queryDto);
        if (StringUtil.isNotEmpty(query)) {
            for (DtoKeyValue dtoKeyValue : query) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", dtoKeyValue.getValue());
                map.put("userName", dtoKeyValue.getKey());
                map.put("orgName ", dtoKeyValue.getOrgName());
                personResult.add(map);
            }
        }
        return personResult;
    }

    /**
     * 根据测试项目查询人员及有证信息
     *
     * @param testId     测试项目id
     * @param permission 权限
     * @return 人员及有证信息
     */
    @Override
    public List<Map<String, Object>> queryWithCert(String testId, String permission) {
        DtoPersonCertQuery personCertQuery = new DtoPersonCertQuery();
        personCertQuery.setTestId(testId);
        personCertQuery.setPermissions(Collections.singletonList(permission));
        return queryWithCert(personCertQuery);
    }

    @Override
    public List<Map<String, Object>> queryWithCert(DtoPersonCertQuery personCertQuery) {
        DtoPersonQuery queryDto = new DtoPersonQuery();
        String testId = personCertQuery.getTestId();
        List<String> permission = personCertQuery.getPermissions();
        queryDto.setPermission(permission);
        queryDto.setStatus(Arrays.asList(EnumLIM.EnumPersonStatus.在职.getValue(), EnumLIM.EnumPersonStatus.休假.getValue()));
        List<DtoKeyValue> resultList = this.query(queryDto);

        List<DtoPersonAbility> abilityList = new ArrayList<>();
        if (StringUtil.isNotEmpty(testId)) {
            abilityList = personAbilityService.findPersonCertByTestId(testId);
        } else if (StringUtil.isNotEmpty(personCertQuery.getSampleTypeIds())) {
            List<DtoSampleType> sampleTypeList = sampleTypeService.findRedisByIds(personCertQuery.getSampleTypeIds());
            List<String> bigSampleTypeIds = sampleTypeList.stream().map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
            abilityList = personAbilityRepository.findBySampleTypeIdIn(bigSampleTypeIds);
        }

        Map<Object, List<DtoPersonAbility>> abilityMap = abilityList.stream().collect(
                Collectors.groupingBy(DtoPersonAbility::getPersonId));

        //获取测试项目的默认分析人员
        List<DtoPerson2Test> person2TestList = person2TestRepository.findByIsDefaultPersonTrueAndTestId(testId);
        List<String> defaultPersonIds = person2TestList.parallelStream().map(DtoPerson2Test::getPersonId).distinct().collect(Collectors.toList());

        List<Map<String, Object>> objList = new ArrayList<>();
        for (DtoKeyValue result : resultList) {
            Map<String, Object> obj = new HashMap<>();
            String personId = (String) result.getValue();
            if (defaultPersonIds.contains(personId)) {
                obj.put("isDefault", Boolean.TRUE);
            } else {
                obj.put("isDefault", Boolean.FALSE);
            }
            obj.put("cname", result.getKey());
            obj.put("id", personId);
            if (abilityMap.containsKey(result.getValue())) {
                Date currentDate = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);
                obj.put("cert", abilityMap.get(result.getValue()).stream().anyMatch(
                        p -> p.getAchieveDate().compareTo(new Date()) <= 0 && p.getCertEffectiveTime().compareTo(currentDate) >= 0) ? "有证" : "过期");
            } else {
                obj.put("cert", "无证");
            }
            objList.add(obj);
        }
        return objList;
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        Integer count = super.logicDeleteById(id);
        userService.deleteById((String) id);
        return count;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        Integer count = super.logicDeleteById(ids);
        userService.deleteByIds((List<String>) ids);
        return count;
    }

    /**
     * 上传人员签名
     *
     * @param request 请求体
     * @return 上传后的人员数据
     */
    @Transactional
    @Override
    public DtoPerson uploadSignature(HttpServletRequest request) {
        String personId = request.getParameter("folderId");
        //查询到人员数据
        DtoPerson person = repository.findOne(personId);
        //上传人员签名文件并保存路径到人员信息中
        List<DtoDocument> upload = documentService.upload(request, null);
        //获取上传的文件
        DtoDocument dtoDocument = upload.stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(dtoDocument)) {
            person.setSignature(dtoDocument.getPath());
        }
        return repository.save(person);
    }

    /**
     * 删除人员签名路径
     *
     * @param documentIds 人员签名文档id集合
     * @return 删除后的人员数据
     */
    @Override
    @Transactional
    public DtoPerson deleteSignature(List<String> documentIds) {
        String documentId = documentIds.stream().findFirst().orElse("");
        DtoDocument dtoDocument = documentRepository.findOne(documentId);
        //查询到人员数据
        DtoPerson person = repository.findOne(dtoDocument.getFolderId());
        person.setSignature("");
        documentService.logicDeleteById(documentIds);
        return repository.save(person);
    }


    /**
     * 计算工作年限
     *
     * @param beginWorkTime 工作时间
     * @return 返回工作年限
     */
    private Integer calculationWorkYear(Date beginWorkTime) {
        //当前时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        if (StringUtil.isNotNull(beginWorkTime)) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(beginWorkTime);
            if ((!DateUtil.dateToString(beginWorkTime, DateUtil.YEAR).equals("1753-01-01"))) {

                return c.get(Calendar.YEAR) - cal.get(Calendar.YEAR);
            }
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> findAllPhotoUrl(HttpServletRequest request) {
        //获取所有人员
        List<DtoPerson> persons = repository.findAll();
        List<Map<String, Object>> maps = new ArrayList<>();
        for (DtoPerson person : persons) {
            Map<String, Object> map = new HashMap<>();
            String url = "";
            if (StringUtils.isNotNullAndEmpty(person.getPhotoUrl())) {
                url = person.getPhotoUrl();
            }
            map.put("id", person.getId());
            map.put("url", url);
            maps.add(map);
        }
        return maps;
    }

    @Override
    public Boolean haveSignature() {
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        List<DtoDocument> documents = documentRepository.findByFolderIdInAndDocTypeIdAndIsDeletedFalseOrderByCreateDateDesc(Collections.singletonList(userId), BaseCodeHelper.DOCUMENT_EXTEND_TYPE_SIGNATURE);
        if (StringUtil.isNotEmpty(documents)) {
            return true;
        }
        return false;
    }

    @Override
    public void exportPersonDetails(BaseCriteria personCriteria, HttpServletResponse response) {
        PageBean<DtoPerson> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        page.setSort("orderNum-");
        super.findByPage(page, personCriteria);
        List<PersonVO> personVOList = new ArrayList<>();
        List<DtoDepartment> departments = departmentService.findAll();
        page.getData().forEach(d -> {
            PersonVO personVO = new PersonVO(d);
            Optional<DtoDepartment> department = departments.stream().filter(p -> p.getId().equals(d.getDeptId())).findFirst();
            department.ifPresent(de -> personVO.setDeptId(de.getDeptName()));
            personVOList.add(personVO);
        });
        PoiExcelUtils.exportExcel(personVOList, null, "人员表格", PersonVO.class, "人员导出表格" + "_" + DateUtil.nowTime("yyyyMMddHHmmss"), response);
    }

    @Override
    public Map<String, Object> certPreview(PageBean<DtoPerson> page, BaseCriteria personCriteria) {
        Map<String, Object> rtMap = new HashMap<>();
        page.setEntityName("DtoPerson p");
        page.setSelect("select p");
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        super.findByPage(page, personCriteria);
        List<DtoPerson> list = page.getData();
        Date now = new Date();
        if (StringUtil.isNotEmpty(list)) {
            List<String> personIds = list.stream().map(DtoPerson::getId).collect(Collectors.toList());
            List<DtoPersonCert> personCertList = personCertRepository.findByPersonIds(personIds);
            List<String> personCertIds = personCertList.stream().map(DtoPersonCert::getId).collect(Collectors.toList());
            List<DtoPersonAbility> personAbilityList = StringUtil.isNotEmpty(personCertIds) ?
                    personAbilityRepository.findByPersonCertIdIn(personCertIds) : new ArrayList<>();
            //BUG2024121301883 【优化】【2024-12-18】【马川江】【安徽】【上岗证统计】仅统计在“上岗证管理”中维护的，在有效期内的上岗证信息。即左侧人员列表不是显示所有人，需显示配置了上岗证且在有效期内的人员
            List<String> validPersonIds = personAbilityList.stream().filter(v -> personCertIds.contains(v.getPersonCertId())
                    && (DateUtil.dateToString(now, DateUtil.YEAR).equals(DateUtil.dateToString(v.getCertEffectiveTime(), DateUtil.YEAR))
                    || v.getCertEffectiveTime().after(now))).map(DtoPersonAbility::getPersonId).collect(Collectors.toList());
            list = list.stream().filter(v -> validPersonIds.contains(v.getId())).collect(Collectors.toList());
            List<DtoDepartment> departments = departmentService.findAll();
            List<DtoSampleType> sampleTypeList = sampleTypeService.findAllBigSampleType();
            Map<String, List<DtoSampleType>> tempMap = sampleTypeList.stream().filter(v -> StringUtil.isNotEmpty(v.getShortName()))
                    .collect(Collectors.groupingBy(DtoSampleType::getShortName));
            Map<String, List<String>> typeMap = collectBigTypeMap(tempMap);
            List<DtoTest> testList = testRepository.findAll();
            List<Map<String, Object>> dataList = new ArrayList<>();
            List<DtoPersonAbility> validAbilityList = new ArrayList<>();
            for (DtoPerson person : list) {
                Map<String, Object> map = new HashMap<>();
                map.put("cName", person.getCName());
                departments.stream().filter(v -> v.getId().equals(person.getDeptId())).findFirst().ifPresent(v -> map.put("deptName", v.getDeptName()));
                List<DtoPersonCert> certList = personCertList.stream().filter(v -> person.getId().equals(v.getPersonId())).collect(Collectors.toList());
                List<String> certIds = certList.stream().map(DtoPersonCert::getId).collect(Collectors.toList());
                List<DtoPersonAbility> abilityList = personAbilityList.stream().filter(v -> certIds.contains(v.getPersonCertId())
                        && (DateUtil.dateToString(now, DateUtil.YEAR).equals(DateUtil.dateToString(v.getCertEffectiveTime(), DateUtil.YEAR))
                        || v.getCertEffectiveTime().after(now))).collect(Collectors.toList());
                map.put("certNum", abilityList.stream().map(DtoPersonAbility::getPersonCertId).distinct().count());
                List<DtoPersonAbility> samplingAbilityList = abilityList.stream().filter(a -> EnumLIM.EnumPersonCertType.采样.getValue().equals(a.getAbilityType()))
                        .collect(Collectors.toList());
                map.put("samplingAbilityCount", samplingAbilityList.size());
                validAbilityList.addAll(samplingAbilityList);
                List<DtoPersonAbility> analyzeAbilityList = abilityList.stream().filter(a -> EnumLIM.EnumPersonCertType.分析.getValue().equals(a.getAbilityType()))
                        .collect(Collectors.toList());
                map.put("analyzeAbilityCount", analyzeAbilityList.size());
                validAbilityList.addAll(analyzeAbilityList);
                Arrays.asList(EnumLIM.EnumPersonCertType.values()).forEach(v -> {
                    if (EnumLIM.EnumPersonCertType.其他.getValue().equals(v.getValue())) {
                        map.put(v.name(), abilityList.stream().anyMatch(a -> a.getAbilityType().equals(v.getValue())));
                    }
                    if (EnumLIM.EnumPersonCertType.分析.getValue().equals(v.getValue())) {
                        typeMap.forEach((shortName, sampleTypeIds) -> {
                            map.put(v.name() + "_" + shortName, abilityList.stream().anyMatch(a -> {
                                DtoTest test = testList.stream().filter(t -> t.getId().equals(a.getTestId())).findFirst().orElse(null);
                                return EnumLIM.EnumPersonCertType.分析.getValue().equals(a.getAbilityType())
                                        && test != null && test.getSampleTypeId() != null && sampleTypeIds.contains(test.getSampleTypeId());
                            }));
                        });
                    }
                    if (EnumLIM.EnumPersonCertType.采样.getValue().equals(v.getValue())) {
                        typeMap.forEach((shortName, sampleTypeIds) -> {
                            map.put(v.name() + "_" + shortName, abilityList.stream().anyMatch(a -> EnumLIM.EnumPersonCertType.采样.getValue().equals(a.getAbilityType())
                                    && sampleTypeIds.contains(a.getSampleTypeId())));
                        });
                    }
                });
                dataList.add(map);
            }
            rtMap.put("dataList", dataList);
            rtMap.put("sortArr", typeShortNameSort(tempMap));
        }

        //BUG2024121301891 “持证数、采样持证数、分析持证数”同检索条件脱钩，统计所有
        //采样项目总数：上岗证右侧明细中，类型为“采样”且“项目名称”去重的数量；采样项次总数：上岗证右侧明细中类型为“采样”的所有明细总数；分析同上，统计类型为分析的即可
        List<String> allPeopleIds = repository.findAll().stream().map(DtoPerson::getId).collect(Collectors.toList());
        List<DtoPersonCert> allCerts = personCertRepository.findAll().stream().filter(v -> allPeopleIds.contains(v.getPersonId())).collect(Collectors.toList());
        List<String> allCertIds = allCerts.stream().map(DtoPersonCert::getId).collect(Collectors.toList());
        List<DtoPersonAbility> allValidAbilitys = personAbilityRepository.findAll().stream()
                .filter(v -> allPeopleIds.contains(v.getPersonId()) && allCertIds.contains(v.getPersonCertId())
                        && (DateUtil.dateToString(now, DateUtil.YEAR).equals(DateUtil.dateToString(v.getCertEffectiveTime(), DateUtil.YEAR))
                        || v.getCertEffectiveTime().after(now))).collect(Collectors.toList());
        rtMap.put("certPeopleSum", (int) allValidAbilitys.stream().map(DtoPersonAbility::getPersonId).count());
        rtMap.put("samplingPeopleSum", (int) allValidAbilitys.stream().filter(v -> EnumLIM.EnumPersonCertType.采样.getValue().equals(v.getAbilityType()))
                .map(DtoPersonAbility::getPersonId).count());
        rtMap.put("samplingItemSum", (int) allValidAbilitys.stream().filter(v -> EnumLIM.EnumPersonCertType.采样.getValue().equals(v.getAbilityType()))
                .map(DtoPersonAbility::getRedAnalyzeItemName).distinct().count());
        rtMap.put("samplingAbilitySum", (int) allValidAbilitys.stream().filter(v -> EnumLIM.EnumPersonCertType.采样.getValue().equals(v.getAbilityType())).count());
        rtMap.put("analyzePeopleSum", (int) allValidAbilitys.stream().filter(v -> EnumLIM.EnumPersonCertType.分析.getValue().equals(v.getAbilityType()))
                .map(DtoPersonAbility::getPersonId).count());
        rtMap.put("analyzeItemSum", (int) allValidAbilitys.stream().filter(v -> EnumLIM.EnumPersonCertType.分析.getValue().equals(v.getAbilityType()))
                .map(DtoPersonAbility::getTestId).distinct().count());
        rtMap.put("analyzeAbilitySum", (int) allValidAbilitys.stream().filter(v -> EnumLIM.EnumPersonCertType.分析.getValue().equals(v.getAbilityType())).count());
        return rtMap;
    }

    /**
     * 将检测类型按照简称分组
     *
     * @return 结果
     */
    private Map<String, List<String>> collectBigTypeMap(Map<String, List<DtoSampleType>> tempMap) {
        Map<String, List<String>> map = new HashMap<>();
        tempMap.forEach((k, v) -> {
            map.put(k, v.stream().map(DtoSampleType::getId).collect(Collectors.toList()));
        });
        return map;
    }

    /**
     * 简称排序
     *
     * @param map 数据map
     * @return 排序
     */
    private List<String> typeShortNameSort(Map<String, List<DtoSampleType>> map) {
        List<NameValuePair> list = new ArrayList<>();
        map.forEach((k, v) -> {
            NameValuePair nameValuePair = new NameValuePair();
            nameValuePair.setName(k);
            List<DtoSampleType> tempList = v.stream().sorted(Comparator.comparing(DtoSampleType::getOrderNum, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            nameValuePair.setValue(tempList.get(0).getOrderNum() + "");
            list.add(nameValuePair);
        });
        return list.stream().sorted((o1, o2) -> Integer.parseInt((String) o2.getValue()) - Integer.parseInt((String) o1.getValue()))
                .map(NameValuePair::getName).collect(Collectors.toList());
    }


    /**
     * 加载人员所在机构名称
     *
     * @param person  人员实例
     * @param orgList 所有机构列表
     * @return 机构名称
     */
    private String loadPersonOrgName(DtoPerson person, List<OrgModel> orgList) {
        return loadPersonOrgName(person.getOrgId(), orgList);
    }

    /**
     * 加载人员所在机构名称
     *
     * @param orgId   机构id
     * @param orgList 所有机构列表
     * @return 机构名称
     */
    private String loadPersonOrgName(String orgId, List<OrgModel> orgList) {
        String orgName = "";
        Optional<OrgModel> orgModelOptional = orgList.stream().filter(g -> g.getId().equals(orgId)).findFirst();
        if (orgModelOptional.isPresent()) {
            orgName = orgModelOptional.get().getOrgName();
        }
        return orgName;
    }


    private static Integer sortUser(DtoUser dtoUser) {
        if (dtoUser.getOrderNum() == null) {
            return 0;
        }
        return dtoUser.getOrderNum();
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }
}