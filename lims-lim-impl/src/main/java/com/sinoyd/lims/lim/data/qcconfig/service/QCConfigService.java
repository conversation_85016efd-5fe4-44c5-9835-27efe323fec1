package com.sinoyd.lims.lim.data.qcconfig.service;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.dto.vo.DeviationFormulaSrcVO;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.criteria.QualityControlLimitCriteria;
import com.sinoyd.lims.lim.data.qcconfig.dto.DtoQCTemp;
import com.sinoyd.lims.lim.dto.customer.DtoQualityControlDeviation;
import com.sinoyd.lims.lim.dto.lims.DtoTest;


import java.util.List;
import java.util.Map;

/**
 * 质控限值接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
public interface QCConfigService extends IBaseJpaService<DtoQualityControlLimit, String> {

    /**
     * 分页查询测试项目
     *
     * @param pb       分页
     * @param criteria 条件
     */
    void findTestByPage(PageBean<DtoTest> pb, TestCriteria criteria);

    /**
     * 分页查询测试项目关联质控限值
     *
     * @param pageBean 分页
     * @param criteria 查询条件
     */
    void findByPage(PageBean<DtoQualityControlLimit> pageBean, QualityControlLimitCriteria criteria);

    /**
     * 保存数据
     *
     * @param qcRangeCopy 需要保存的数据
     * @param qcType      质控类型
     */
    DtoQualityControlLimit save(DtoQualityControlLimit qcRangeCopy, Integer qcType);

    /**
     * 保存偏差公式
     *
     * @param qualityControlDeviation 需要保存的数据
     * @return 偏差公式
     */
    DtoQualityControlDeviation saveDeviation(DtoQualityControlDeviation qualityControlDeviation);

    /**
     * 修改偏差公式
     *
     * @param qualityControlDeviation 需要修改的数据
     * @return 偏差公式
     */
    DtoQualityControlDeviation updateDeviation(DtoQualityControlDeviation qualityControlDeviation);

    /**
     * 批量删除偏差公式
     *
     * @param ids 偏差公式记录id值
     * @return 删除的数量
     */
    Integer deleteDeviation(List<String> ids);

    /**
     * 查询质控类型的偏差公式设置
     *
     * @return 质控类型的偏差公式设置
     */
    List<DtoQualityControlDeviation> queryDeviation();

    /**
     * 获取所有的评判方式
     *
     * @return 评判方式
     */
    Map<Integer, String> findAllJudgingMethod();

    /**
     * 查询所有替代物
     *
     * @return 替代物
     */
    List<DtoSubstitute> findSubstitute();

    /**
     * 获取单独测试项目下的质控限值数据
     *
     * @param testId 测试项目Id
     * @return 测试项目质控数据
     */
    DtoQCTemp findQCTemp(String testId);

    /**
     * 复制质控限值配置
     *
     * @param tempTestId 被复制的测试项目id
     * @param testIds    需要复制的测试项目id数组
     * @param qcList     需要复制的质控类型
     */
    void copy(String tempTestId, List<String> testIds, List<DtoQualityControlLimit> qcList);

    /**
     * 删除
     *
     * @param ids 需要删除的编号
     * @return 删除的数量
     */
    Integer delete(List<String> ids);

    /**
     * 获取偏差公式配置质控类型下拉框数据源
     *
     * @return 下拉框数据源信息
     */
    List<DeviationFormulaSrcVO> getDeviationFormulaQcTypeDataSource();

    /**
     * 批量清除测试项目对应的质控限值列表
     *
     * @param testIdList 测试项目id列表
     */
    void clearQcLimit(List<String> testIdList);

    void updateDefaultValue(String id);

    /**
     * 更新配置公式id
     */
    void uploadOldLimit();

    /**
     * 公式验证
     * @param s 文本
     * @return 结果
     */
    boolean validateFormat(String s);
}
