package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.RecordConfig2TestCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormula;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.RecordConfig2TestService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 原始记录单相关测试项目操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Service
public class RecordConfig2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoRecordConfig2Test, String, RecordConfig2TestRepository> implements RecordConfig2TestService {

    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;

    private ParamsConfigRepository paramsConfigRepository;

    private ParamsFormulaRepository paramsFormulaRepository;

    private TestRepository testRepository;

    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    private TestService testService;

    private ParamsRepository paramsRepository;

    @Override
    public void findByPage(PageBean<DtoRecordConfig2Test> pb, BaseCriteria baseCriteria) {
        RecordConfig2TestCriteria criteria = (RecordConfig2TestCriteria) baseCriteria;
        Set<String> testIds = new HashSet<>();
        if (StringUtil.isNotEmpty(criteria.getAnalyzeItemKey())) {
            List<String> ids = testRepository.findWithAnalyzeItemKey("%" + criteria.getAnalyzeItemKey() + "%");
            if (StringUtil.isNotEmpty(ids)) {
                testIds.addAll(ids);
            }else{
                testIds.add(UUIDHelper.GUID_EMPTY);
            }
        }
        if (StringUtil.isNotEmpty(criteria.getAnalyzeMethodKey())) {
            List<String> ids = testRepository.findWithAnalyzeMethodKey("%" + criteria.getAnalyzeMethodKey() + "%");
            if (StringUtil.isNotEmpty(ids)) {
                testIds.addAll(ids);
            }else{
                testIds.add(UUIDHelper.GUID_EMPTY);
            }
        }
        criteria.setTestIds(testIds);
        pb.setEntityName("DtoRecordConfig2Test ct ");
        pb.setSelect("select ct ");
        comRepository.findByPage(pb, criteria);
        List<DtoRecordConfig2Test> dataList = pb.getData();
        if (StringUtil.isNotEmpty(dataList)) {
            List<String> tIds = dataList.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> tList = testService.findAll(tIds);
            for (DtoRecordConfig2Test dto : dataList) {
                Optional<DtoTest> testOptional = tList.stream().filter(t -> t.getId().equals(dto.getTestId())).findFirst();
                testOptional.ifPresent(t -> {
                    dto.setRedAnalyzeMethodName(t.getRedAnalyzeMethodName());
                    dto.setRedAnalyzeItemName(t.getRedAnalyzeItemName());
                    dto.setRedCountryStandard(t.getRedCountryStandard());
                    dto.setSampleTypeName(t.getSampleTypeName());
                    dto.setOrderNum(t.getOrderNum());
                });
            }

            Map<String, List<DtoRecordConfig2Test>> anaMth2Cfg2TestListMap = dataList.stream()
                    .collect(Collectors.groupingBy(DtoRecordConfig2Test::getRedAnalyzeMethodName));
            List<DtoRecordConfig2Test> sortedRecordConfig2TestList = new ArrayList<>();
            for (Map.Entry<String, List<DtoRecordConfig2Test>> entry : anaMth2Cfg2TestListMap.entrySet()) {
                List<DtoRecordConfig2Test> loopList = entry.getValue();
                loopList.sort(Comparator.comparing(DtoRecordConfig2Test::getOrderNum).reversed());
                sortedRecordConfig2TestList.addAll(loopList);
            }
            Collator collator = Collator.getInstance(Locale.CHINA);
            sortedRecordConfig2TestList.sort(Comparator.comparing(DtoRecordConfig2Test::getSampleTypeName, collator)
                    .thenComparing(DtoRecordConfig2Test::getRedAnalyzeMethodName, collator).thenComparing(DtoRecordConfig2Test::getRedAnalyzeItemName, collator));
            pb.setData(sortedRecordConfig2TestList);
        }
    }

    /**
     * 新增原始记录单相关测试项目
     *
     * @param testIds        测试项目ids
     * @param recordConfigId 记录配置id
     * @return 返回保存的数据
     */
    @Override
    public List<DtoRecordConfig2Test> save(List<String> testIds, String recordConfigId) {
        //查询该原始记录单下有没有配置相应的检测项目
        List<DtoRecordConfig2Test> recordConfig2Tests = repository.findByRecordConfigId(recordConfigId);
        List<DtoRecordConfig2Test> entities = new ArrayList<>();
        for (String testId : testIds) {
            DtoRecordConfig2Test dtoRecordConfig2Test = recordConfig2Tests.stream().filter(p -> p.getTestId().equals(testId)).findFirst().orElse(null);
            if (StringUtil.isNull(dtoRecordConfig2Test)) {
                DtoRecordConfig2Test item = new DtoRecordConfig2Test();
                item.setTestId(testId);
                item.setRecordConfigId(recordConfigId);
                entities.add(item);
            }
        }
        if (entities.size() > 0) {
            //新增测试项目之后，所有的原始记录单参数都要改成未配置
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByRecordId(recordConfigId);
            List<String> paramsConfigIds = params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getParamsConfigId).distinct().collect(Collectors.toList());
            if (paramsConfigIds.size() > 0) {
                paramsConfigRepository.updateParamsConfigIsAllConfig(paramsConfigIds, false);
            }
            return super.save(entities);
        }
        return null;
    }


    @Override
    public List<Map<String, Object>> findTestFormula(String recordConfigId,
                                                     String paramsConfigId,
                                                     Integer isAll, String analyzeItem, String analyzeMethod, String formulaText) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        //记录单相关的检测能力
        List<DtoRecordConfig2Test> tests = repository.findByRecordConfigId(recordConfigId);
        //所有相关的测试项目ids
        List<String> testIds = tests.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
        if ((testIds.size() > 0 && isAll.equals(EnumLIM.EnumAllConfig.全部.getValue())) || (testIds.size() > 0 && StringUtil.isNotEmpty(paramsConfigId))) {
            //拼装sql
            StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoTestFormula(");
            stringBuilder.append("a.id,a.objectId,b.redAnalyzeItemName,b.redAnalyzeMethodName,b.redCountryStandard,a.formula,a.sampleTypeId,");
            stringBuilder.append("c.typeName,a.configDate)");
            stringBuilder.append(" from DtoParamsFormula a,DtoTest b,DtoSampleType c");
            stringBuilder.append(" where a.objectId=b.id and b.sampleTypeId=c.id");
            stringBuilder.append(" and a.objectId in :testIds");
            stringBuilder.append(" and a.objectType = :objectType");
            stringBuilder.append(" and a.isDeleted = 0");
            stringBuilder.append(" and b.isDeleted = 0");
            Map<String, Object> values = new HashMap<>();
            values.put("testIds", testIds);
            values.put("objectType", EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue());
            //分析项目模糊查询
            if (StringUtil.isNotEmpty(analyzeItem)) {
                stringBuilder.append(" and  b.redAnalyzeItemName like :redAnalyzeItemName  ");
                values.put("redAnalyzeItemName", "%" + analyzeItem + "%");
            }
            //分析方法模糊查询
            if (StringUtil.isNotEmpty(analyzeMethod)) {
                stringBuilder.append(" and (b.redAnalyzeMethodName like :redAnalyzeMethodName or b.redCountryStandard like :redAnalyzeMethodName) ");
                values.put("redAnalyzeMethodName", "%" + analyzeMethod + "%");
            }
            //公式模糊查询
            if (StringUtil.isNotEmpty(formulaText)) {
                stringBuilder.append(" and  a.formula like :formula) ");
                values.put("formula", "%" + formulaText + "%");
            }
            List<DtoTestFormula> formulas = comRepository.find(stringBuilder.toString(), values);
            if (StringUtil.isEmpty(paramsConfigId)) {
                paramsConfigId = UUIDHelper.GUID_EMPTY;
            }
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.
                    findByRecordIdAndParamsConfigId(recordConfigId, paramsConfigId);
            List<String> formulaIds = params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getObjectId).distinct().collect(Collectors.toList());
            //所有测试项目公式参数
            List<String> objectIds = formulas.stream().map(DtoTestFormula::getId).collect(Collectors.toList());
            List<DtoParamsTestFormula> paramsTestFormulaList = (!objectIds.isEmpty()) ? paramsTestFormulaRepository.findByObjIdIn(objectIds) : new ArrayList<>();
            //记录单参数所有的配置
            List<DtoParams2ParamsFormula> params2ParamsFormulaList = (!objectIds.isEmpty()) ? params2ParamsFormulaRepository.findByRecordIdAndObjectIdInAndParamsConfigId(recordConfigId, objectIds, paramsConfigId)
                    : new ArrayList<>();
            //所有的配置的个性化量纲，小数位数，有效位数
            List<String> params2ParamsFormulaIdList = params2ParamsFormulaList.stream().map(DtoParams2ParamsFormula::getId).collect(Collectors.toList());
            List<DtoParamsConfig> paramsConfigList = (!params2ParamsFormulaIdList.isEmpty()) ? paramsConfigRepository.findByObjIdInAndType(params2ParamsFormulaIdList, EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue())
                    : new ArrayList<>();

            //用于校验公式是否重复
            List<String> checkdupFormulaIdList = new ArrayList<>();
            for (DtoTestFormula formula : formulas) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", formula.getId());
                map.put("testId", formula.getTestId());
                map.put("redAnalyzeItemName", formula.getRedAnalyzeItemName());
                map.put("redAnalyzeMethodName", formula.getRedAnalyzeMethodName());
                map.put("redCountryStandard", formula.getRedCountryStandard());
                map.put("formula", formula.getFormula());
                map.put("sampleTypeId", formula.getSampleTypeId());
                map.put("sampleTypeName", formula.getSampleTypeName());
                //判断是否配置的逻辑调整
                Boolean isAllConfig = personalJudgeIsConfig(formula, params2ParamsFormulaList, paramsConfigList);
                map.put("isAllConfig", isAllConfig);
                //关联公式参数
                map.put("relratedParam", getrelratedParam(formula, params2ParamsFormulaList));
                //列参数
                map.put("columnParams", buildColumnParams(formula, paramsTestFormulaList));
                //个性化的量纲 有效位数 小数位数
                DtoParamsConfig paramsConfig = getReleatedParamsConfig(formula, params2ParamsFormulaList, paramsConfigList);
                map.put("dimension", paramsConfig != null ? paramsConfig.getDimension() : "");
                map.put("dimensionId", paramsConfig != null ? paramsConfig.getDimensionId() : "");
                map.put("mostSignificance", paramsConfig != null && !paramsConfig.getMostSignificance().equals(-1)
                        ? paramsConfig.getMostSignificance() : "");
                map.put("mostDecimal", paramsConfig != null && !paramsConfig.getMostDecimal().equals(-1)
                        ? paramsConfig.getMostDecimal() : "");
                //关联公式参数下拉数据源
                List<DtoParamsTestFormula> relratedParamSourceList = paramsTestFormulaList.stream().filter(t -> formula.getId().equals(t.getObjId())).collect(Collectors.toList());
                relratedParamSourceList.sort(Comparator.comparing(DtoParamsTestFormula::getOrderNum, Comparator.reverseOrder())
                        .thenComparing(DtoParamsTestFormula::getParamsName));
                map.put("relratedParamSourceList", relratedParamSourceList);
                String dupKey = formula.getId() + "_" + formula.getTestId() + "_" + formula.getSampleTypeId();
                if (!checkdupFormulaIdList.contains(dupKey)) {
                    if ((isAll.equals(EnumLIM.EnumAllConfig.已配置.getValue()) && isAllConfig)
                            || (isAll.equals(EnumLIM.EnumAllConfig.未配置.getValue()) && !isAllConfig)
                            || (!isAll.equals(EnumLIM.EnumAllConfig.已配置.getValue()) && !isAll.equals(EnumLIM.EnumAllConfig.未配置.getValue()))) {
                        checkdupFormulaIdList.add(dupKey);
                    }
                    addMap(isAll, mapList, map, isAllConfig);
                }
            }
        }
        //排序规则为： 未配置 已配置 分析方法 分析项目 公式
        Comparator<Map> configComparator = Comparator.comparing(a -> a.get("isAllConfig") + "");
        Comparator<Map> methodComparator = Comparator.comparing(a -> a.get("redAnalyzeMethodName") + "");
        Comparator<Map> itemComparator = Comparator.comparing(a -> a.get("redAnalyzeItemName") + "");
        Comparator<Map> formulaComparator = Comparator.comparing(a -> a.get("formula") + "");
        mapList.sort(configComparator.thenComparing(methodComparator).thenComparing(itemComparator).thenComparing(formulaComparator));
        return mapList;
    }

    /**
     * 公式内容匹配关联公式参数
     *
     * @param formula                  测试项目公式
     * @param params2ParamsFormulaList 个性化公式
     * @return 关联名称
     */
    private String getrelratedParam(DtoTestFormula formula, List<DtoParams2ParamsFormula> params2ParamsFormulaList) {
        DtoParams2ParamsFormula params2ParamsFormula = params2ParamsFormulaList.stream().filter(p -> formula.getId().equals(p.getObjectId())).findFirst().orElse(null);
        if (params2ParamsFormula != null) {
            return StringUtil.isEmpty(params2ParamsFormula.getFormula()) ? "" : params2ParamsFormula.getFormula();
        }
        return "";
    }

    /**
     * 查找个性化配置的量纲，小数位数，有效位数
     *
     * @param formula                  测试项目公式
     * @param params2ParamsFormulaList 所有配置
     * @param paramsConfigList         所有个性化
     * @return
     */
    private DtoParamsConfig getReleatedParamsConfig(DtoTestFormula formula, List<DtoParams2ParamsFormula> params2ParamsFormulaList, List<DtoParamsConfig> paramsConfigList) {
        DtoParamsConfig result = null;
        DtoParams2ParamsFormula params2ParamsFormula = params2ParamsFormulaList.stream().filter(p -> formula.getId().equals(p.getObjectId())).findFirst().orElse(null);
        if (params2ParamsFormula != null) {
            result = paramsConfigList.stream().filter(p -> params2ParamsFormula.getId().equals(p.getObjId())).findFirst().orElse(null);
        }
        return result;
    }


    /**
     * 新版原始记录单测试项目公式判断是否已配置方法
     *
     * @param formula                  测试项目公式
     * @param params2ParamsFormulaList 所有配置
     * @param paramsConfigList         所有个性化
     * @return
     */
    private boolean personalJudgeIsConfig(DtoTestFormula formula, List<DtoParams2ParamsFormula> params2ParamsFormulaList, List<DtoParamsConfig> paramsConfigList) {
        DtoParams2ParamsFormula params2ParamsFormula = params2ParamsFormulaList.stream().filter(p -> formula.getId().equals(p.getObjectId())).findFirst().orElse(null);
        if (params2ParamsFormula != null) {
            if (StringUtil.isNotEmpty(params2ParamsFormula.getFormula())) {
                return true;
            }
            DtoParamsConfig dtoParamsConfig = paramsConfigList.stream().filter(p -> params2ParamsFormula.getId().equals(p.getObjId())).findFirst().orElse(null);
            if (dtoParamsConfig != null && (StringUtil.isNotEmpty(dtoParamsConfig.getDimension()) || dtoParamsConfig.getMostDecimal() != -1 || dtoParamsConfig.getMostSignificance() != -1)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 破解列参数字段
     *
     * @param formula               测试项目公式
     * @param paramsTestFormulaList 测试项目公式参数列表
     * @return 列参数
     */
    private String buildColumnParams(DtoTestFormula formula, List<DtoParamsTestFormula> paramsTestFormulaList) {
        List<DtoParamsTestFormula> paramsList = paramsTestFormulaList.stream().filter(p -> formula.getId().equals(p.getObjId())).collect(Collectors.toList());
        paramsList.sort(Comparator.comparing(DtoParamsTestFormula::getOrderNum, Comparator.reverseOrder())
                .thenComparing(DtoParamsTestFormula::getParamsName));
        for (DtoParamsTestFormula dtoParamsTestFormula : paramsList) {
            dtoParamsTestFormula.setParamsName("[" + dtoParamsTestFormula.getParamsName() + "]");
        }
        List<String> paramsNameList = paramsList.stream().map(DtoParamsTestFormula::getParamsName).collect(Collectors.toList());
        return String.join("、", paramsNameList);
    }


    @Override
    public List<Map<String, Object>> findTests(String recordConfigId, String paramsConfigId, Integer isAll, String key) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        //记录单相关的检测能力
        List<DtoRecordConfig2Test> tests = repository.findByRecordConfigId(recordConfigId);
        //所有相关的测试项目ids
        List<String> testAllIds = tests.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
        if (testAllIds.size() > 0) {
            StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.lims.DtoTest(");
            stringBuilder.append("a.id,a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.sampleTypeId,");
            stringBuilder.append("b.typeName as sampleTypeName)");
            stringBuilder.append(" from DtoTest a,DtoSampleType b");
            stringBuilder.append(" where a.sampleTypeId=b.id");
            stringBuilder.append(" and a.id in :testIds");
            Map<String, Object> values = new HashMap<>();
            values.put("testIds", testAllIds);
            //可以使用关键字对分析项目名称和分析方法名称进行模糊查询
            if (StringUtil.isNotEmpty(key)) {
                stringBuilder.append(" and (a.redAnalyzeMethodName like :key or a.redAnalyzeItemName like :key or a.redCountryStandard like :key) ");
                values.put("key", "%" + key + "%");
            }
            //排序规则为分析方法>分析项目名称>配置时间
            stringBuilder.append(" order by a.redAnalyzeMethodName,a.redAnalyzeItemName");
            List<DtoTest> testList = comRepository.find(stringBuilder.toString(), values);
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.
                    findByRecordIdAndParamsConfigId(recordConfigId, paramsConfigId);
            List<String> testIds = params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getObjectId).distinct().collect(Collectors.toList());
            for (DtoTest test : testList) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", test.getId());
                map.put("redAnalyzeItemName", test.getRedAnalyzeItemName());
                map.put("redAnalyzeMethodName", test.getRedAnalyzeMethodName());
                map.put("redCountryStandard", test.getRedCountryStandard());
                map.put("sampleTypeId", test.getSampleTypeId());
                map.put("sampleTypeName", test.getSampleTypeName());
                Boolean isAllConfig = testIds.contains(test.getId());
                map.put("isAllConfig", isAllConfig);
                addMap(isAll, mapList, map, isAllConfig);
            }
        }
        return mapList;
    }

    @Override
    public void batchAddParams(List<String> paramsIdList, List<String> testFormulaIdList) {
        //更新容器
        List<DtoParamsTestFormula> waitSaveList = new ArrayList<>();
        //基础数据
        List<DtoParams> paramsList = paramsRepository.findAll(paramsIdList);
        List<DtoParamsTestFormula> paramsTestFormulaList = paramsTestFormulaRepository.findByObjIdIn(testFormulaIdList);
        List<String> allWaitAddFormulaParamIdList = paramsList.stream().map(DtoParams::getId).collect(Collectors.toList());
        for (String testFormulaId : testFormulaIdList) {
            //过滤待插入参数
            List<String> existFormulaParamsIdList = paramsTestFormulaList.stream().filter(t -> testFormulaId.equals(t.getObjId()))
                    .map(DtoParamsTestFormula::getParamsId).collect(Collectors.toList());
            List<String> tempIdList = new ArrayList<>(allWaitAddFormulaParamIdList);
            tempIdList.removeAll(existFormulaParamsIdList);
            for (String paramsId : tempIdList) {
                DtoParams dtoParams = paramsList.stream().filter(p -> paramsId.equals(p.getId())).findFirst().orElse(null);
                if (dtoParams != null) {
                    DtoParamsTestFormula paramsTestFormula = new DtoParamsTestFormula(dtoParams);
                    paramsTestFormula.setObjId(testFormulaId);
                    waitSaveList.add(paramsTestFormula);
                }
            }
        }
        paramsTestFormulaRepository.save(waitSaveList);
    }


    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        //如果删除测试项目，那相应的表头参数或者数据参数状态需要进行更新处理

        List<DtoRecordConfig2Test> recordConfigTests = repository.findAll((List<String>) ids);

        List<String> testIds = recordConfigTests.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());

        //记录单的ids
        List<String> recordIds = recordConfigTests.stream().map(DtoRecordConfig2Test::getRecordConfigId).distinct().collect(Collectors.toList());

        Integer deleteRows = super.logicDeleteById(ids);

        List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByRecordIdIn(recordIds);

        List<DtoRecordConfig2Test> recordConfig2TestsAll = repository.findByRecordConfigIdIn(recordIds);

        //核对头部数据
        checkHeaderParams(recordIds, params2ParamsFormulas, recordConfig2TestsAll, testIds);

        //核对数据数据
        checkDataParams(recordIds, params2ParamsFormulas, recordConfig2TestsAll, testIds);

        return deleteRows;
    }

    /**
     * 增加已配或者未配置数据
     *
     * @param isAll       是否所有
     * @param mapList     map集合
     * @param map         map数据
     * @param isAllConfig 是否要配置显示
     */
    private void addMap(Integer isAll, List<Map<String, Object>> mapList, Map<String, Object> map, Boolean isAllConfig) {
        if (isAll.equals(EnumLIM.EnumAllConfig.已配置.getValue())) {
            if (isAllConfig) {
                mapList.add(map);
            }
        } else if (isAll.equals(EnumLIM.EnumAllConfig.未配置.getValue())) {
            if (!isAllConfig) {
                mapList.add(map);
            }
        } else {
            mapList.add(map);
        }
    }


    /**
     * 核对头部参数是否配置全
     *
     * @param recordIds             记录单ids
     * @param params2ParamsFormulas 配置的公式
     * @param recordConfigTests     相关的测试项目
     */
    private void checkHeaderParams(List<String> recordIds,
                                   List<DtoParams2ParamsFormula> params2ParamsFormulas,
                                   List<DtoRecordConfig2Test> recordConfigTests,
                                   List<String> deleteTestIds
    ) {
        //表头参数
        List<DtoParamsConfig> headerParams = paramsConfigRepository.findByObjIdInAndType(recordIds, EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());

        //当前这个单子的参数数据
        for (String recordId : recordIds) {
            List<String> paramsConfigIds = headerParams.stream().filter(p -> p.getObjId().equals(recordId)).map(DtoParamsConfig::getId).distinct().collect(Collectors.toList());
            //记录单相关的检测能力
            List<DtoRecordConfig2Test> tests = recordConfigTests.stream().filter(p -> p.getRecordConfigId().equals(recordId)
                    && !deleteTestIds.contains(p.getTestId())).collect(Collectors.toList());
            //所有相关的测试项目ids
            List<String> testIds = tests.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
            for (String paramsConfigId : paramsConfigIds) {
                //判断这个参数对应的公式是否都配置全了
                boolean isAllConfig = false;
                //已配置的测试项目ids
                List<String> testExistIds = params2ParamsFormulas
                        .stream().filter(p -> p.getRecordId().equals(recordId)
                                && p.getParamsConfigId().equals(paramsConfigId)
                                && !deleteTestIds.contains(p.getObjectId())).map(DtoParams2ParamsFormula::getObjectId).distinct().collect(Collectors.toList());
                if (testIds.size() > 0) {
                    isAllConfig = testIds.size() == testExistIds.size();
                }
                paramsConfigRepository.updateParamsConfigIsAllConfig(paramsConfigId, isAllConfig);
            }
            //删除对应数据
            if (deleteTestIds.size() > 0) {
                params2ParamsFormulaRepository.deleteByRecordIdAndObjectIdIn(recordId, deleteTestIds);
            }
        }
    }

    /**
     * 核对数据参数
     *
     * @param recordIds             记录单ids
     * @param params2ParamsFormulas 配置的公式
     * @param recordConfigTests     相关的测试项目
     */
    private void checkDataParams(List<String> recordIds,
                                 List<DtoParams2ParamsFormula> params2ParamsFormulas,
                                 List<DtoRecordConfig2Test> recordConfigTests,
                                 List<String> deleteTestIds) {

        //记录单相关的检测能力（数据已经删除）
        List<String> testIds = recordConfigTests.stream().map(DtoRecordConfig2Test::getTestId).collect(Collectors.toList());
        //需要加上被删除的数据
        testIds.addAll(deleteTestIds);
        //测试项目下的公式
        List<DtoParamsFormula> testFormulaAll = new ArrayList<>();
        if (testIds.size() > 0) {
            testFormulaAll = paramsFormulaRepository.findByObjectIds(testIds);
        }

        //要删除的公式id
        List<String> deleteFormulaIds = testFormulaAll.stream().filter(p -> deleteTestIds.contains(p.getObjectId())).map(DtoParamsFormula::getId).distinct().collect(Collectors.toList());

        //需要处理的公式数据
        List<DtoParamsFormula> testFormulaList = testFormulaAll.stream().filter(p -> !deleteTestIds.contains(p.getObjectId())).distinct().collect(Collectors.toList());

        List<String> testFormulaIds = testFormulaList.stream().map(DtoParamsFormula::getId).distinct().collect(Collectors.toList());

        //数据参数
        List<DtoParamsConfig> dataParams = paramsConfigRepository.findByObjIdInAndType(recordIds, EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());

        //当前这个单子的参数数据
        for (String recordId : recordIds) {
            List<String> paramsConfigIds = dataParams.stream().filter(p -> p.getObjId().equals(recordId)).map(DtoParamsConfig::getId).distinct().collect(Collectors.toList());
            for (String paramsConfigId : paramsConfigIds) {
                //已配置的ids
                List<String> testFormulaExistIds = params2ParamsFormulas.stream().filter(p -> p.getRecordId().equals(recordId)
                        && p.getParamsConfigId().equals(paramsConfigId)
                        && !deleteFormulaIds.contains(p.getObjectId())).map(DtoParams2ParamsFormula::getObjectId).distinct().collect(Collectors.toList());
                //判断这个参数对应的公式是否都配置全了
                Boolean isAllConfig = testFormulaIds.size() == testFormulaExistIds.size();
                paramsConfigRepository.updateParamsConfigIsAllConfig(paramsConfigId, isAllConfig);
            }
            //删除对应数据
            if (deleteFormulaIds.size() > 0) {
                params2ParamsFormulaRepository.deleteByRecordIdAndObjectIdIn(recordId, deleteFormulaIds);
            }
        }
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setParams2ParamsFormulaRepository(Params2ParamsFormulaRepository params2ParamsFormulaRepository) {
        this.params2ParamsFormulaRepository = params2ParamsFormulaRepository;
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }

    @Autowired
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }
}