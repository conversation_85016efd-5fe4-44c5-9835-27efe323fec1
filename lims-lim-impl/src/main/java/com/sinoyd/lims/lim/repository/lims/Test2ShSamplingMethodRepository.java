package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTest2ShSamplingMethod;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目与监管平台采样方法关联仓储接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/17
 **/
public interface Test2ShSamplingMethodRepository extends IBaseJpaPhysicalDeleteRepository<DtoTest2ShSamplingMethod, String>, LimsRepository<DtoTest2ShSamplingMethod, String> {

    /**
     * 根据测试项目id集合查询
     *
     * @param testIds 测试项目id集合
     * @return 测试项目与监管平台采样方法关联列表
     */
    List<DtoTest2ShSamplingMethod> findByTestIdIn(Collection<String> testIds);


    /**
     * 根据测试项目id集合和检测类型id查询
     *
     * @param testIds      测试项目id集合
     * @param sampleTypeId 检测类型id
     * @return 测试项目与监管平台采样方法关联列表
     */
    List<DtoTest2ShSamplingMethod> findByTestIdInAndSampleTypeId(Collection<String> testIds, String sampleTypeId);
}
