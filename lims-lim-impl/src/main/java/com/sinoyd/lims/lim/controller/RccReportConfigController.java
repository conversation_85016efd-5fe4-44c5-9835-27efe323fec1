package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.service.rcc.IRccReportConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 资源中心报表配置 Controller
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@RestController
@RequestMapping("/api/lim/rccReportConfig")
public class RccReportConfigController extends ExceptionHandlerController<IRccReportConfigService> {


    /**
     * 资源配置中心报表配置分页查询
     *
     * @param criteria 查询条件
     * @return 资源配置中心测试项目集合
     */
    @ApiOperation(value = "资源配置中心报表配置分页查询", notes = "资源配置中心报表配置分页查询")
    @GetMapping
    public RestResponse<List<DtoReportConfig>> findByPage(ReportConfigCriteria criteria) {
        PageBean<DtoReportConfig> pb = super.getPageBean();
        return service.findByPageFromRcc(pb, criteria);
    }

    /**
     * 选择同步
     *
     * @param reportConfigIds 资源配置中心报表配置id集合
     * @return 无返回值
     */
    @PostMapping("/sync")
    public RestResponse<Void> sync(@RequestBody Collection<String> reportConfigIds) {
        service.sync(reportConfigIds);
        return new RestResponse<>();
    }

    @PostMapping("/sync/all")
    public RestResponse<Void> sync() {
        service.sync();
        return new RestResponse<>();
    }
}
