package com.sinoyd.lims.lim.service.rcc;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.ISyncDataService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumRequestTarget;
import com.sinoyd.commons.vo.QueryRequestParamVO;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.repository.lims.ReportConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 资源中心报表配置服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/03/14
 */
@Service
public class RccReportConfigServiceImpl extends RemoteRequestBaseServiceImpl implements IRccReportConfigService {

    private ReportConfigRepository reportConfigRepository;

    private ISyncDataService<DtoReportConfig> reportConfigSyncService;

    private DocumentService documentService;

    @Override
    public RestResponse<List<DtoReportConfig>> findByPageFromRcc(PageBean<DtoReportConfig> pb, BaseCriteria baseCriteria) {
        ReportConfigCriteria reportConfigCriteria = (ReportConfigCriteria) baseCriteria;
        Map<String, Object> criteriaMap = super.objectToMap(reportConfigCriteria, "values");
        QueryRequestParamVO param = createQueryParam(pb, criteriaMap, "/api/lim/reportConfig", HttpMethod.GET.name(), EnumRequestTarget.RCC.getValue(), "RCC");
        param.setSort(pb.getSort());
        RestResponse<List<JSONObject>> restResponse = rccQueryService.findByPage(param);
        List<DtoReportConfig> reportConfigList = getResponseList(restResponse, DtoReportConfig.class);
        RestResponse<List<DtoReportConfig>> response = new RestResponse<>();
        response.setCount(restResponse.getCount());
        response.setMsg(restResponse.getMsg());
        response.setData(reportConfigList);
        return response;
    }

    @Override
    public List<DtoReportConfig> findRccAllByIds(Collection<String> ids) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("", ids);
        QueryRequestParamVO param = createQueryParam("/api/lim/reportConfig/multiple",
                HttpMethod.POST.name(), EnumRequestTarget.RCC.getValue(), "RCC", requestMap);
        return getResponseList(rccQueryService.findList(param), DtoReportConfig.class);
    }

    @Override
    public List<DtoReportConfig> findRccAll() {
        QueryRequestParamVO param = createQueryParam("/api/lim/reportConfig/all",
                HttpMethod.POST.name(), EnumRequestTarget.RCC.getValue(), "RCC", new HashMap<>());
        return getResponseList(rccQueryService.findList(param), DtoReportConfig.class);
    }

    @Override
    public List<DtoDocument> findRccDocument(Collection<String> folderIds) {
        return super.findRccDocument(folderIds);
    }


    @Override
    @Transactional
    public void sync() {
        List<DtoReportConfig> sourceDataList = this.findRccAll();
        if (StringUtil.isNotEmpty(sourceDataList)) {
            List<DtoReportConfig> limsDeleteDataList = findDeleteData(null);
            reportConfigSyncService.sync(sourceDataList, limsDeleteDataList);
            // 同步报表模版配置附件
            this.syncReportConfigDoc(sourceDataList.stream().map(DtoReportConfig::getId).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional
    public void sync(Collection<String> reportConfigIds) {
        List<DtoReportConfig> sourceDataList = this.findRccAllByIds(reportConfigIds);
        if (StringUtil.isNotEmpty(sourceDataList)) {
            List<DtoReportConfig> limsDeleteDataList = findDeleteData(reportConfigIds);
            reportConfigSyncService.sync(sourceDataList, limsDeleteDataList);
            // 同步报表模版配置附件
            this.syncReportConfigDoc(reportConfigIds);
        }
    }


    /**
     * 同步报表模版配置附件
     *
     * @param reportConfigIds 报表模版配置主键id
     */
    private void syncReportConfigDoc(Collection<String> reportConfigIds) {
        if (StringUtil.isNotEmpty(reportConfigIds)) {
            // 先删除模版配置附件
            List<DtoDocument> documentList = documentService.findByObjectIds(reportConfigIds);
            if (StringUtil.isNotEmpty(documentList)){
                documentService.logicDeleteById(documentList.stream().map(DtoDocument::getId).collect(Collectors.toList()));
            }
            // 查询rcc附件
            List<DtoDocument> dtoDocuments = findRccDocument(reportConfigIds);
            if (StringUtil.isNotEmpty(dtoDocuments)){
                documentService.save(dtoDocuments);
            }
        }
    }

    /**
     * 删除假删数据
     *
     * @param reportConfigIds 报表配置id
     * @return 假删的数据
     */
    private List<DtoReportConfig> findDeleteData(Collection<String> reportConfigIds) {
        List<DtoReportConfig> deletedDataList;
        if (StringUtil.isEmpty(reportConfigIds)) {
            deletedDataList = reportConfigRepository.findAllWithDeleted();
        } else {
            deletedDataList = reportConfigRepository.findAllWithDeleted(reportConfigIds);
        }
        return deletedDataList;
    }

    @Autowired
    public void setReportConfigRepository(ReportConfigRepository reportConfigRepository) {
        this.reportConfigRepository = reportConfigRepository;
    }

    @Autowired
    @Lazy
    @Qualifier("reportConfigSyncServiceImpl")
    public void setReportConfigSyncService(ISyncDataService<DtoReportConfig> reportConfigSyncService) {
        this.reportConfigSyncService = reportConfigSyncService;
    }


    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }
}
