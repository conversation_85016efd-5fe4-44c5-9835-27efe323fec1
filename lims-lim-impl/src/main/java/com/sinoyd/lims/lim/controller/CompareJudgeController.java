package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.CompareJudgeCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.lim.service.CompareJudgeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CompareJudge服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/13
 * @since V100R001
 */
@Api(tags = "示例: CompareJudge服务")
@RestController
@RequestMapping("api/lim/compareJudge")
public class CompareJudgeController extends BaseJpaController<DtoCompareJudge, String, CompareJudgeService> {

    /**
     * 分页动态条件查询DtoCompareJudge
     *
     * @param criteria 条件参数
     * @return RestResponse<List <DtoCompareJudge>>
     */
    @ApiOperation(value = "分页动态条件查询DtoCompareJudge", notes = "分页动态条件查询DtoCompareJudge")
    @GetMapping
    public RestResponse<List<DtoCompareJudge>> findByPage(CompareJudgeCriteria criteria) {
        PageBean<DtoCompareJudge> pageBean = super.getPageBean();
        RestResponse<List<DtoCompareJudge>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 保存
     *
     * @param compareJudge 实体
     * @return RestResponse<DtoCompareJudge>
     */
    @ApiOperation(value = "保存DtoCompareJudge", notes = "保存DtoCompareJudge")
    @PostMapping
    public RestResponse<DtoCompareJudge> save(@RequestBody DtoCompareJudge compareJudge) {
        RestResponse<DtoCompareJudge> response = new RestResponse<>();
        response.setData(service.save(compareJudge));
        return response;
    }

    /**
     * 更新
     *
     * @param compareJudge 实体
     * @return RestResponse<DtoCompareJudge>
     */
    @ApiOperation(value = "更新DtoCompareJudge", notes = "更新DtoCompareJudge")
    @PutMapping
    public RestResponse<DtoCompareJudge> update(@RequestBody DtoCompareJudge compareJudge) {
        RestResponse<DtoCompareJudge> response = new RestResponse<>();
        response.setData(service.update(compareJudge));
        return response;
    }

    /**
     * 根据id批量删除
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

}
