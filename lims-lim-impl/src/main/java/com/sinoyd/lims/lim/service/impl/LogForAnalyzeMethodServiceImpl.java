package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoLogForAnalyzeMethod;
import com.sinoyd.lims.lim.repository.lims.LogForAnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.LogForAnalyzeMethodService;
import org.springframework.stereotype.Service;

/**
 * 方法日志服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/01
 */
@Service
public class LogForAnalyzeMethodServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoLogForAnalyzeMethod, String, LogForAnalyzeMethodRepository> implements LogForAnalyzeMethodService {
}
