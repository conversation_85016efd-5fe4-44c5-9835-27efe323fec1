package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.OcrConfigRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;
import com.sinoyd.lims.lim.service.OcrConfigRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ocr对象参数数据
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@RestController
@RequestMapping("api/lim/ocrConfigRecord")
@Validated
public class OcrConfigRecordController extends BaseJpaController<DtoOcrConfigRecord, String, OcrConfigRecordService> {
    /**
     * 分页动态条件查询DtoOcrConfigRecord
     *
     * @param ocrConfigRecordCriteria 条件参数
     * @return RestResponse<List   <   DtoOcrConfigRecord>>
     */
    @ApiOperation(value = "分页动态条件查询DtoOcrConfigRecordc", notes = "分页动态条件查询DtoOcrConfigRecord")
    @GetMapping
    public RestResponse<List<DtoOcrConfigRecord>> findByPage(OcrConfigRecordCriteria ocrConfigRecordCriteria) {
        PageBean<DtoOcrConfigRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoOcrConfigRecord>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, ocrConfigRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 保存修改数据
     *
     * @param record 识别记录
     * @return RestResponse<List   <   DtoOcrConfigRecord>>
     */
    @ApiOperation(value = "新增识别记录", notes = "新增识别记录")
    @PostMapping
    public RestResponse<Void> save(@Validated @RequestBody  DtoOcrConfigRecord record) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.save(record);
        return restResponse;
    }

    /**
     * 保存修改数据
     *
     * @param record 识别记录
     * @return RestRes se<List   <   DtoOcrConfigRecord>>
     */
    @ApiOperation(value = "新增识别记录", notes = "新增识别记录")
    @PostMapping("/paramData")
    public RestResponse<Void> saveConfigParamData(@Validated @RequestBody  DtoOcrConfigRecord record) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.saveConfigParamData(record);
        return restResponse;
    }


    /**
     * 根据识别id删除
     *
     * @param id 识别记录id
     * @return RestRes se<List   <   DtoOcrConfigRecord>>
     */
    @ApiOperation(value = "根据识别id删除", notes = "根据识别id删除")
    @DeleteMapping("/{id}")
    public RestResponse<Void> delete(@PathVariable("id") String id) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.logicDeleteById(id);
        return restResponse;
    }



    /**
     * 根据id查询识别记录
     *
     * @param id 标识
     * @return 查询结果
     */
    @ApiOperation(value = "根据id查询识别记录", notes = "根据id查询识别记录")
    @GetMapping("/{id}")
    public RestResponse<DtoOcrConfigRecord> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOcrConfigRecord> restResp = new RestResponse<>();
        restResp.setData(service.findOne(id));
        return restResp;
    }

}
