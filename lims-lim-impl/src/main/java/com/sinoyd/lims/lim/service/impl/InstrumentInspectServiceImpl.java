package com.sinoyd.lims.lim.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentInspect;
import com.sinoyd.lims.lim.repository.lims.InstrumentInspectRepository;
import com.sinoyd.lims.lim.service.InstrumentInspectService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器期间勘察接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Service
public class InstrumentInspectServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentInspect, String, InstrumentInspectRepository>
        implements InstrumentInspectService {

    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoInstrumentInspect> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentInspect x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");

        super.findByPage(page, criteria);
    }

    /**
     * 更新同步基础信息
     */
    @Transactional
    @Override
    public DtoInstrumentInspect update(DtoInstrumentInspect entity) {
        // 仪器对象
        DtoInstrument insModel = instrumentService.findOne(entity.getInstrumentId());
        insModel.setInspectDate(entity.getInspectTime()); // 勘查时间
        insModel.setInspectResult(entity.getInspectResult()); // 勘查结果
        insModel.setInspectMethod(entity.getInspectContent()); // 核查方法
        instrumentService.update(insModel); // 更新基础信息
        
        return super.update(entity);
    }

    /**
     * 新增期间勘查
     */
    @Transactional
    @Override
    public DtoInstrumentInspect save(DtoInstrumentInspect entity) {

        // 仪器对象
        DtoInstrument insModel = instrumentService.findOne(entity.getInstrumentId());
        insModel.setInspectDate(entity.getInspectTime()); // 勘查时间
        insModel.setInspectResult(entity.getInspectResult()); // 勘查结果
        insModel.setInspectMethod(entity.getInspectContent()); // 核查方法
        instrumentService.update(insModel); // 更新基础信息

        return super.save(entity);
    }

    /**
     * 批量新增
     */
    @Transactional
    @Override
    public List<DtoInstrumentInspect> save(Collection<DtoInstrumentInspect> entities) {

        List<DtoInstrumentInspect> saves = new ArrayList<>();

        entities.forEach(x -> saves.add(this.save(x)));

        return saves;
    }
}