package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoConsumableStorage;

import java.util.List;

/**
 * 消耗品入库仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface ConsumableStorageRepository extends IBaseJpaPhysicalDeleteRepository<DtoConsumableStorage, String>
{
    /**
     * 根据消耗品标识查询
     * @param consumableIds 消耗品标识集合
     * @return 入库列表
     */
    List<DtoConsumableStorage> findByConsumableIdIn(List<String> consumableIds);
}