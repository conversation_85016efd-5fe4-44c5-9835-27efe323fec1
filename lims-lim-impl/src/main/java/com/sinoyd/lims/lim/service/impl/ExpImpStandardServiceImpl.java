package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.criteria.ConsumableCriteria;
import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.ConsumableDetailRepository;
import com.sinoyd.base.repository.lims.ConsumableRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableExtend;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableStandardExtend;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpStandard;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ExpImpStandardService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyStandardVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.base.core.BaseCodeHelper.ConsumableGrade;


/**
 * 标准物质导入导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExpImpStandardServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoConsumable, String, ConsumableRepository> implements ExpImpStandardService {

    protected ConsumableService consumableService;
    protected ImportUtils importUtils;
    protected PersonRepository personRepository;
    protected CodeService codeService;
    protected DimensionRepository dimensionRepository;
    protected ConsumableDetailRepository consumableDetailRepository;
    protected EnterpriseService enterpriseService;
    private ImpModifyStandardVerify impModifyStandardVerify;


    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoConsumable> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        ConsumableCriteria criteria = (ConsumableCriteria) baseCriteria;
        consumableService.findByPage(page, criteria);
        List<DtoConsumable> dtoConsumables = page.getData();

        List<DtoPerson> personList = personRepository.findAll();
        Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        List<DtoExpImpStandard> dtoExpImpStandards = new ArrayList<>();
        List<String> consumableIds = dtoConsumables.stream().map(DtoConsumable::getId).collect(Collectors.toList());
        List<DtoConsumableDetail> consumableDetails = consumableDetailRepository.findByParentIdIn(consumableIds);
        Map<String, DtoConsumableDetail> consumableDetailMap = consumableDetails.stream().collect(Collectors.toMap(DtoConsumableDetail::getParentId, p -> p));
        Date year1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        for (DtoConsumable dtoConsumable : dtoConsumables) {
            DtoExpImpStandard expImpStandard = new DtoExpImpStandard();
            BeanUtils.copyProperties(dtoConsumable, expImpStandard);

            expImpStandard.setStandard(dtoConsumable.getConcentration());
            expImpStandard.setUncertainTypeName( EnumBase.EnumUncertainType.getNameByValue(dtoConsumable.getUncertainType()));

            DtoConsumableDetail detailMapOrDefault = consumableDetailMap.getOrDefault(dtoConsumable.getId(), null);
            if (StringUtil.isNotNull(detailMapOrDefault)) {
                String expiryDate = detailMapOrDefault.getExpiryDate()==null||detailMapOrDefault.getExpiryDate().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(detailMapOrDefault.getExpiryDate(), DateUtil.YEAR);
                expImpStandard.setExpiryDate(expiryDate);
                String storageDate = detailMapOrDefault.getStorageDate()==null||detailMapOrDefault.getStorageDate().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(detailMapOrDefault.getStorageDate(), DateUtil.YEAR);
                expImpStandard.setStorageDate(storageDate);
                String orderTime = detailMapOrDefault.getOrderTime()==null||detailMapOrDefault.getOrderTime().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(detailMapOrDefault.getOrderTime(), DateUtil.YEAR);
                expImpStandard.setOrderTime(orderTime);
                expImpStandard.setProductionCode(detailMapOrDefault.getProductionCode());
                expImpStandard.setManufacturerName(detailMapOrDefault.getManufacturerName());
                expImpStandard.setSupplierName(detailMapOrDefault.getSupplierName());
                expImpStandard.setKeepPlace(detailMapOrDefault.getKeepPlace());
                expImpStandard.setReMark(detailMapOrDefault.getRemark());
                expImpStandard.setUnitPrice(detailMapOrDefault.getUnitPrice().stripTrailingZeros().toPlainString());
                expImpStandard.setIsLabEncryption(dtoConsumable.getIsLabEncryption() ? "是" : "否");
            }
            expImpStandard.setInventory(dtoConsumable.getInventory().stripTrailingZeros().toPlainString());
            expImpStandard.setWarningNum(dtoConsumable.getWarningNum().stripTrailingZeros().toPlainString());
            // 提醒人员
            String warnUser = personMap.getOrDefault(dtoConsumable.getSendWarnUserId(), "");
            expImpStandard.setSendWarnUserName(warnUser);
            dtoExpImpStandards.add(expImpStandard);
        }

        List<String> consCategoryList = codeService.findCodes("LIM_StandardCategory").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        List<String> consGradeList = codeService.findCodes("LIM_ConsumableGrade").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        List<DtoImportConsumableStandardExtend> extendList = getStandardExtendData(consCategoryList, consGradeList);
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpStandard.class, DtoImportConsumableStandardExtend.class, dtoExpImpStandards, extendList);
        // 设置下拉框、
        // 消耗品等级、种类
        String[] grade = consGradeList.toArray(new String[0]);
        String[] category = consCategoryList.toArray(new String[0]);
        importUtils.selectList(workBook, 4, 4, category);
        importUtils.selectList(workBook, 6, 6, grade);
        importUtils.selectList(workBook, 16, 16, EnumBase.EnumUncertainType.getNames());

        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    @Transactional
    public List<DtoConsumable> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {

        PoiExcelUtils.verifyFileType(file);
        //获取是否导入消耗品类型
        Boolean isImportStandardType = (Boolean) objectMap.get(0);
        ExcelImportResult<DtoImportConsumableExtend> extendResult = importUtils.getExcelData(file, DtoImportConsumableExtend.class, 0, 1, 1, true, null);
        List<DtoCode> grades = codeService.findCodes(ConsumableGrade);
        List<DtoCode> standardCategory = codeService.findCodes("LIM_StandardCategory");
        List<DtoPerson> personList = personRepository.findAll();
        List<DtoDimension> dtoDimensions = dimensionRepository.findAll();
        List<DtoEnterprise> enterprise = getEnterprise();
        List<DtoConsumable> consumableAllList = repository.findAll();
        // 临时数据
        List<DtoExpImpStandard> impStandards = new ArrayList<>();
        impModifyStandardVerify.getStandardCategoryTl().set(standardCategory);
        impModifyStandardVerify.getGradesTl().set(grades);
        impModifyStandardVerify.getPersonTl().set(personList);
        impModifyStandardVerify.getEnterpriseTl().set(enterprise);
        impModifyStandardVerify.getStandardTl().set(impStandards);
        impModifyStandardVerify.getIsImportStandardTypeTl().set(isImportStandardType);
        impModifyStandardVerify.getImportConsumableExtend().set(extendResult.getList());
        impModifyStandardVerify.getDimensionList().set(dtoDimensions);

        //获取导入结果
        ExcelImportResult<DtoExpImpStandard> result = getExcelData(file, response);
        List<DtoExpImpStandard> impStandardList = result.getList();
        impModifyStandardVerify.getStandardCategoryTl().remove();
        impModifyStandardVerify.getGradesTl().remove();
        impModifyStandardVerify.getPersonTl().remove();
        impModifyStandardVerify.getEnterpriseTl().remove();
        impModifyStandardVerify.getStandardTl().remove();
        impModifyStandardVerify.getIsImportStandardTypeTl().remove();
        impModifyStandardVerify.getImportConsumableExtend().remove();
        impModifyStandardVerify.getDimensionList().remove();

        impStandardList.removeIf(p -> StringUtil.isEmpty(p.getConsumableName()));
        //判断导入中是否存在数据
        if (StringUtil.isEmpty(impStandardList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        if (isImportStandardType) {
            //导入消耗品类型
            importExtend(file, standardCategory);
        }
        List<String> consumableIds = impStandardList.stream().map(DtoExpImpStandard::getId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoConsumableDetail> consumableDetails = StringUtil.isNotEmpty(consumableIds) ? consumableDetailRepository.findByParentIdIn(consumableIds) : new ArrayList<>();
        Map<String, DtoConsumableDetail> consumableDetailMap = consumableDetails.stream().collect(Collectors.toMap(DtoConsumableDetail::getParentId, p -> p));
        //转换实体（消耗品基本信息）
        List<DtoConsumable> consumables = getConsumableEntity(impStandardList, personList, consumableAllList);
        // 处理量纲
        handleDimension(consumables, dtoDimensions);

        List<DtoConsumableDetail> details = getDetailEntity(impStandardList, enterprise, personList, consumableDetailMap);
        addData(consumables);
        consumableDetailRepository.save(details);
        return repository.findAll();
    }

    @Override
    @Transactional
    public void addData(List<DtoConsumable> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpStandard> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);

        params.setVerifyHandler(impModifyStandardVerify);
        ExcelImportResult<DtoExpImpStandard> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoExpImpStandard.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "标准物质导入错误信息");
            PoiExcelUtils.downLoadExcel("标准物质导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    protected List<DtoEnterprise> getEnterprise() {
        EnterpriseCriteria criteria = new EnterpriseCriteria();
        PageBean<DtoEnterprise> pb = new PageBean<>();
        criteria.setType(4);
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        enterpriseService.findByPage(pb, criteria);
        return pb.getData();
    }

    /**
     * 获取标样导出模板的拓展数据
     *
     * @param consCategoryList 标样类型
     * @param consGradeList    等级
     * @return 标样导出关联数据
     */
    protected List<DtoImportConsumableStandardExtend> getStandardExtendData(List<String> consCategoryList, List<String> consGradeList) {
        //返回的数据集合
        List<DtoImportConsumableStandardExtend> extendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(consCategoryList.size());
        size.add(consGradeList.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportConsumableStandardExtend extendData = new DtoImportConsumableStandardExtend();
            extendData.setConsumableCategory(consCategoryList.size() < i + 1 ? null : consCategoryList.get(i));
            extendData.setGrade(consGradeList.size() < i + 1 ? null : consGradeList.get(i));
            extendList.add(extendData);
        }
        return extendList;
    }

    /**
     * 获取消耗品基本信息实体
     *
     * @param impStandardList 导入实体
     * @param personList      导入实体
     * @return 消耗品实体
     */
    private List<DtoConsumable> getConsumableEntity(List<DtoExpImpStandard> impStandardList, List<DtoPerson> personList, List<DtoConsumable> consumableAllList) {

        Map<String, DtoConsumable> consumableMap = consumableAllList.stream().collect(Collectors.toMap(DtoConsumable::getId, p -> p));
        List<DtoConsumable> consumables = new ArrayList<>();
        for (DtoExpImpStandard importConsumable : impStandardList) {
            DtoConsumable consumable = new DtoConsumable();
            if (StringUtil.isNotEmpty(importConsumable.getId())) {
                consumable = consumableMap.getOrDefault(importConsumable.getId(), new DtoConsumable());
            } else {
                importConsumable.setId(UUIDHelper.NewID());
            }
            BeanUtils.copyProperties(importConsumable, consumable);
            consumable.setInventory(new BigDecimal(importConsumable.getInventory()));
            consumable.setGrade(StringUtil.isNotNull(importConsumable.getGrade()) ? importConsumable.getGrade() : "");
            consumable.setWarningNum(StringUtil.isEmpty(importConsumable.getWarningNum()) ? BigDecimal.ZERO : new BigDecimal(importConsumable.getWarningNum()));
            consumable.setConcentration(importConsumable.getStandard());
            consumable.setSendWarnUserId(personList.stream().filter(p -> p.getCName().equals(importConsumable.getSendWarnUserName())).map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY));
            if (importConsumable.getIsStandard()) {
                consumable.setIsLabEncryption(importConsumable.getIsLabEncryption().equals("是"));
            }
            consumable.setUncertainType(EnumBase.EnumUncertainType.getValueByName(importConsumable.getUncertainTypeName()));
            consumables.add(consumable);
        }
        return consumables;
    }

    /**
     * 导入消耗品类型
     *
     * @param file       文件流
     * @param dbConsType 消耗品类型数据
     */
    protected void importExtend(MultipartFile file, List<DtoCode> dbConsType) {
        //获取导入的关联信息
        List<DtoImportConsumableExtend> extendResult = importUtils.getImportNames(file, DtoImportConsumableExtend.class);
        //数据库中类型
        List<String> dbExtendData = dbConsType.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //关联表中类型
        List<String> resultNames = extendResult.stream().map(DtoImportConsumableExtend::getConsumableStandardCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        importUtils.createCodes(resultNames, "LIM_ConsumableCategory", dbExtendData);
    }

    /**
     * 处理量纲数据
     *
     * @param consumables 需要导入的消耗品
     * @param dbDimension 所有的量纲数据
     */
    protected void handleDimension(List<DtoConsumable> consumables, List<DtoDimension> dbDimension) {
        List<String> dimensionNames = dbDimension.stream().map(DtoDimension::getDimensionName).collect(Collectors.toList());
        List<String> importDimensionNames = consumables.stream().map(DtoConsumable::getUnit).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> selectDimensionNames = consumables.stream().map(DtoConsumable::getDimensionName).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        importDimensionNames.addAll(selectDimensionNames);
        importDimensionNames = importDimensionNames.stream().distinct().collect(Collectors.toList());
        List<String> notExistNames = importDimensionNames.stream().filter(p -> !dimensionNames.contains(p)).collect(Collectors.toList());
        List<DtoDimension> addDimension = new ArrayList<>();
        for (String notExistName : notExistNames) {
            DtoDimension dtoDimension = new DtoDimension();
            dtoDimension.setDimensionName(notExistName);
            dtoDimension.setDimensionTypeId("/");
            dtoDimension.setBaseValue(BigDecimal.ZERO);
            addDimension.add(dtoDimension);
        }
        if (StringUtil.isNotEmpty(addDimension)) {
            List<DtoDimension> save = dimensionRepository.save(addDimension);
            dbDimension.addAll(save);
        }
        //处理实体中的量纲Id
        dbDimension.removeIf(p -> StringUtil.isEmpty(p.getDimensionName()));
        Map<String, List<DtoDimension>> dimensionMap = dbDimension.stream().collect(Collectors.groupingBy(DtoDimension::getDimensionName));
        for (DtoConsumable consumable : consumables) {
            List<DtoDimension> dimensionOfName = dimensionMap.get(consumable.getUnit());
            if (StringUtil.isNotNull(dimensionOfName)) {
                Optional<DtoDimension> dimension = dimensionOfName.stream().findFirst();
                dimension.ifPresent(p -> consumable.setUnitId(p.getId()));
            }
            List<DtoDimension> dimensionOfSelect = dimensionMap.get(consumable.getDimensionName());
            if (StringUtil.isNotNull(dimensionOfSelect)) {
                Optional<DtoDimension> selectDimension = dimensionOfSelect.stream().findFirst();
                selectDimension.ifPresent(p -> consumable.setDimensionId(p.getId()));
            }
        }
    }

    /**
     * 获取消耗品基本信息实体
     *
     * @param importConsumables 导入实体
     * @return 消耗品实体
     */
    private List<DtoConsumableDetail> getDetailEntity(List<DtoExpImpStandard> importConsumables, List<DtoEnterprise> enterprise,
                                                      List<DtoPerson> personList,
                                                      Map<String, DtoConsumableDetail> consumableDetailMap) {
        List<DtoConsumableDetail> details = new ArrayList<>();
        for (DtoExpImpStandard importConsumable : importConsumables) {
            DtoConsumableDetail consumable = new DtoConsumableDetail();
            if (consumableDetailMap.containsKey(importConsumable.getId())) {
                consumable = consumableDetailMap.get(importConsumable.getId());
            } else {
                importConsumable.setId(UUIDHelper.NewID());
            }
            consumable.setId(importConsumable.getId());
            consumable.setParentId(importConsumable.getId());
            consumable.setStorage(new BigDecimal(importConsumable.getInventory()));
            consumable.setInventory(new BigDecimal(importConsumable.getInventory()));
            consumable.setUnitPrice(StringUtil.isEmpty(importConsumable.getUnitPrice()) ? BigDecimal.ZERO : new BigDecimal(importConsumable.getUnitPrice()));
            consumable.setKeepPlace(importConsumable.getKeepPlace());
            consumable.setCheckerId(personList.stream().filter(p -> p.getCName().equals(importConsumable.getSendWarnUserName())).map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY));
            consumable.setRemark(importConsumable.getReMark());
            consumable.setProductionCode(importConsumable.getProductionCode());
            consumable.setManufacturerName(importConsumable.getManufacturerName());
            consumable.setStorageDate(importUtils.stringToDateAllFormat(importConsumable.getStorageDate()));
            consumable.setExpiryDate(importUtils.stringToDateAllFormat(importConsumable.getExpiryDate()));
            consumable.setOrderTime(importUtils.stringToDateAllFormat(importConsumable.getOrderTime()));
            consumable.setSupplierName(importConsumable.getSupplierName());
            consumable.setSupplierId(enterprise.stream().filter(p -> p.getName().equals(importConsumable.getSupplierName())).map(DtoEnterprise::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY));
            details.add(consumable);
        }
        return details;
    }

    @Autowired
    public void setConsumableService(ConsumableService consumableService) {
        this.consumableService = consumableService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    public void setConsumableDetailRepository(ConsumableDetailRepository consumableDetailRepository) {
        this.consumableDetailRepository = consumableDetailRepository;
    }

    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setImpModifyStandardVerify(ImpModifyStandardVerify impModifyStandardVerify) {
        this.impModifyStandardVerify = impModifyStandardVerify;
    }
}
