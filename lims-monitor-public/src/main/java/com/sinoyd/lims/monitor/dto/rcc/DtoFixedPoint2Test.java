package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.entity.FixedPoint2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoFixedPoint2Test实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_FixedPoint2Test")
@Data
@DynamicInsert
public class DtoFixedPoint2Test extends FixedPoint2Test {
    private static final long serialVersionUID = 1L;

    /**
     * 测试项目表集合
     */
    @Transient
    private List<DtoTest> dtoTests;

    /**
     * 测试项目id列表
     */
    @Transient
    private List<String> testIdList;

    /**
     * 周期（从点位上获取）
     */
    @Transient
    private Integer cycleOrder;

    /**
     * 检测类型id（点位上的检测类型id）
     */
    @Transient
    private String sampleTypeId;


}