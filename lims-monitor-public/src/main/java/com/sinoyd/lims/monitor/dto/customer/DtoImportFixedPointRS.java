package com.sinoyd.lims.monitor.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 污染源点位导入/导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/08
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportFixedPointRS implements IExcelModel, IExcelDataModel {

    /**
     * 企业名称
     */
    @Excel(name = "企业名称(必填)", orderNum = "200", width = 24)
    private String enterpriseName;

    /**
     * 所属企业
     */
    private String enterpriseId;


    /**
     * 点位类型
     */
    private String folderType;


    /**
     * 点位类型名称
     */
    @Excel(name = "点位类型(必填)", orderNum = "300", width = 24)
    private String folderTypeName;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型(必填)", orderNum = "400", width = 24)
    private String sampleTypeName;

    /**
     * 点位编号
     */
    @Excel(name = "点位编号", orderNum = "500", width = 24)
    private String pointCode;

    /**
     * 点位名称
     */
    @Excel(name = "点位名称(必填)", orderNum = "600", width = 22)
    private String pointName;


    /**
     * 经度
     */
    @Excel(name = "经度", orderNum = "700", width = 18)
    private String lon;

    /**
     * 纬度
     */
    @Excel(name = "纬度", orderNum = "800", width = 18)
    private String lat;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "910", width = 22)
    private String remark;


    /**
     * 类型（枚举：环境质量 1 污染源 2）
     */
    private Integer pointType = 2;

    /**
     * 是否启用
     */
    private Boolean isEnabled = true;


    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;
}
