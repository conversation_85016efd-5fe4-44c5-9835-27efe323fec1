package com.sinoyd.lims.qa.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>质量管理体系枚举类</p>
 *
 * <AUTHOR>
 * @version V0.0.1 2021/03/23
 * @since V0.0.1
 */
public class EnumQA {

    /**
     * 年度计划状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAnnualPlanStatus {
        编制中(1),

        审核中(2),

        审核通过(3),

        审核不通过(4);

        private Integer value;
    }

    /**
     * 管理评审状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumManagementReviewPlanStatus {
        计划编制中(1),

        计划审核中(2),

        报告编制中(3),

        报告审核中(4),

        报告审核通过(5),

        审核通过(6),

        审核不通过(7),

        报告审核不通过(8);

        private Integer value;
    }

    /**
     * 内审状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumInternalAuditPlanStatus {
        计划编制中(1),

        计划审核中(2),

        计划执行中(3),

        报告编制中(4),

        报告审核中(5),

        报告审核通过(6),

        审核通过(7),

        审核不通过(8),

        内审检查中(9),

        内审完毕(10),

        报告审核不通过(11);

        private Integer value;
    }

    /**
     * 质量监督状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumMonitoringPlanStatus {
        计划编制中(1),

        计划评审中(2),

        计划执行中(3),

        质量监督中(4),

        质量监督完毕(5),

        执行中(6),

        执行完毕(7),

        审核通过(8),

        审核不通过(9),

        计划明细编制中(10),

        质量监督审核不通过(11);

        private Integer value;
    }

    /**
     * 质量监督页面
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumMonitoringPlanPageFrom {

        质量监督编制(1),
        质量监督审核(2),
        质量监督执行(3),
        质量监督记录(4);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanStatus c : EnumAnnualPlanStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 客户投诉运行状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCustomerComplaintRegistStatus {

        投诉登记中(1),
        投诉审核中(2),
        投诉确认中(3),
        投诉办结中(4),
        投诉已办结(5),
        办结退回(6);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanStatus c : EnumAnnualPlanStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 客户投诉显示页面标示
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCustomerComplaintRegistListType {
        客户投诉登记(1),

        客户投诉审核(2),

        客户投诉确认(3),

        客户投诉办结(4),

        客户投诉进度查询(5);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 不符合项运行状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumNotConformItemStatus {

        不符合项登记中(1),
        不符合项措施拟定中(2),
        不符合项措施拟定不通过(3),
        不符合项措施批准中(4),
        不符合项措施批准不通过(5),
        不符合项措施纠正中(6),
        不符合项措施纠正不通过(7),
        不符合项验证中(8),
        不符合项验证不通过(9),
        不符合项已办结(10);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanStatus c : EnumAnnualPlanStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 不符合项列表类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumNotConformItemListType {
        不符合项登记(1),

        不符合项拟定措施(2),

        不符合项批准措施(3),

        不符合项完成情况(4),

        不符合项验证评价(5),

        不符合项进度查询(6);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 风险机遇运行状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumRiskAndAccidentStatus {
        // 1.登记中、2.措施拟定中、3.措施批准中、4.措施实施中、5.确认中、6.确认完毕
        登记中(1),
        措施拟定中(2),
        拟定不通过(3),
        措施批准中(4),
        措施批准不通过(5),
        措施实施中(6),
        措施实施不通过(7),
        完成情况确认中(8),
        完成情况不通过(9),
        确认完毕(10),
        确认不通过(11);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanStatus c : EnumAnnualPlanStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 风险机遇列表类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumRiskAndAccidentListType {
        登记(1),

        措施拟定(2),

        方案审批(3),

        完成情况(4),

        确认列表(5),

        进度查询(6);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 年度计划类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAnnualPlanType {
        内部评审(1, "内部评审"),

        管理评审(2, "管理评审"),

        质控监督(3, "质控监督"),

        质量控制(4, "质量控制"),

        人员培训(5, "人员培训");

        private Integer value;
        private String name;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }

        public static Integer getByName(String name) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getName().equals(name)) {
                    return c.getValue();
                }
            }
            return null;
        }

    }

    /**
     * 通用状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumStatus {
        所有(-1),

        待处理(1),

        已处理(2);

        private Integer value;
    }

    /**
     * 用于填充submitRecord对象提交类型字段值（int类型） -- 2021-04-08 xyl
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumQASubmitType {
        年度计划登记(38, "年度计划登记"),
        年度计划提交(1, "年度计划提交"),
        年度计划审核(2, "年度计划审核"),
        年度计划审核不通过(56, "年度计划审核不通过"),

        管理评审计划登记(39, "管理评审计划登记"),
        管理评审计划提交(3, "管理评审计划编制提交"),
        管理评审计划审核(4, "管理评审计划审核"),
        管理评审报告编制提交(5, "管理评审报告编制提交"),
        管理评审报告审核(10, "管理评审报告审核"),

        内审管理计划登记(40,"内审管理计划登记"),
        内审管理计划提交(6, "内审管理计划提交"),
        内审管理计划审核(7, "内审管理计划审核"),
        内审实施计划提交(8, "内审实施计划提交"),
        内审管理报告提交(9, "内审管理报告提交"),
        内审实施计划退回(11, "内审管理报告提交"),
        内审管理报告审核(12, "内审管理报告审核"),
        内审实施计划登记(41,"内审实施计划登记"),
        内审实施计划审核(42,"内审实施计划审核"),



        质量监督计划登记(43, "质量监督计划登记"),
        质量监督计划提交(13, "质量监督计划提交"),
        质量监督计划审核(14, "质量监督计划审核"),
        质量监督计划明细登记(43, "质量监督计划明细登记"),
        质量监督计划明细提交(15, "质量监督计划明细提交"),
        质量监督记录审核(16, "质量监督记录审核"),
        质量监督计划明细退回(17, "质量监督计划明细退回"),

        客户投诉登记(36, "客户投诉登记"), //后加
        客户投诉提交(18, "客户投诉提交"),
        客户投诉审核(19, "客户投诉审核"),
        客户投诉确认(20, "客户投诉确认"),
        客户投诉办结(21, "客户投诉办结"),
        客户投诉办结退回(22, "客户投诉办结退回"),

        不符合项登记(37, "不符合项登记"),//后加
        不符合项提交(23, "不符合项提交"),
        不符合项拟定措施提交(24, "不符合项拟定措施提交"),
        不符合项措施审核(25, "不符合项措施审核"),
        不符合项完成情况审核(26, "不符合项完成情况审核"),
        不符合项完成情况退回(27, "不符合项完成情况退回"),
        不符合项验证评价提交(28, "不符合项验证评价提交"),

        风险机遇登记(29, "风险机遇登记"),
        风险机遇提交(30, "风险机遇提交"),
        风险机遇拟定措施提交(31, "风险机遇拟定措施提交"),
        风险机遇拟定措施审核(32, "风险机遇拟定措施审核"),
        风险机遇完成情况审核(33, "风险机遇完成情况审核"),
        风险机遇完成情况退回(34, "风险机遇完成情况退回"),
        风险机遇方案确认(35, "风险机遇方案确认");


        private Integer value;
        private String name;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }

        public static Integer getByName(String name) {
            for (EnumAnnualPlanType c : EnumAnnualPlanType.values()) {
                if (c.getName().equals(name)) {
                    return c.getValue();
                }
            }
            return null;
        }

    }

    /**
     * 用于填充submitRecord对象关联对象类型字段值（objectType int类型） -- 2021-04-08 xyl
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumQAObjType {
        年度计划(1, "年度计划"),

        管理评审(2, "管理评审"),

        内审管理(3, "内审管理"),

        质量监督(4, "质量监督"),

        客户投诉管理(5, "客户投诉管理"),

        不符合项管理(6, "不符合项管理"),

        风险机遇管理(7, "风险记录管理");

        private Integer value;
        private String name;
    }

    /**
     * 操作类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLogOperateType {
        新增年度计划,
        编辑年度计划,
        删除年度计划,
        提交年度计划,
        审核年度计划,

        新增管理评审计划,
        删除管理评审计划,
        提交管理评审计划,
        审核管理评审计划,
        提交管理评审报告,
        审核管理评审报告,

        新增内审管理计划,
        提交内审管理计划,
        审核内审管理计划,
        删除内审管理计划,

        新增内审实施计划,
        删除内审实施计划,
        提交内审实施计划,
        新增检查项,
        删除检查项,
        新增报告信息,
        退回内审实施计划,
        审核内审管理报告,

        新增质量监督计划,
        新增计划明细,
        删除质量监督计划,
        删除计划明细,
        提交质量监督计划,
        审核质量监督计划,
        提交质量监督计划明细,
        审核质量监督记录,
        退回质量监督计划明细,

        新增客户投诉,
        编辑客户投诉,
        删除客户投诉,
        提交客户投诉,
        审核客户投诉,
        确认客户投诉,
        办结客户投诉,

        新增不符合项,
        编辑不符合项,
        删除不符合项,
        提交不符合项,
        提交不符合项措施,
        审核不符合项措施,
        审核不符合项措施完成情况,
        退回不符合项措施完成情况,
        提交不符合项验证评价,

        新增风险机遇,
        编辑风险机遇,
        删除风险机遇,
        提交风险机遇,
        风险机遇措施拟定,
        风险机遇措施措施方案审批,
        风险机遇完成情况,
        退回风险机遇完成情况,
        风险机遇确认
    }

    /**
     * 日志类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLogType {
        /**
         * 新增、提交、审核
         */
        年度计划流程,
        管理评审流程,
        内审管理流程,
        质量监督流程,
        客户投诉流程,
        不符合项登记流程,
        风险机遇流程
    }

    /**
     * 日志对象类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLogObjectType {
        年度计划(1,"年度计划"),
        管理评审(2,"管理评审"),
        内审管理(3,"内审管理"),
        质量监督(4,"质量监督"),
        客户投诉(5,"客户投诉"),
        不符合项(6,"不符合项"),
        风险机遇(7,"风险机遇");

        private Integer value;
        private String name;
    }

    /**
     * 检查结果类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCheckResultType {
        符合(1, "符合"),
        基本符合(2, "基本符合"),
        不符合(3, "不符合"),
        不适用(4, "不适用");

        private Integer value;
        private String name;

        public static String getByValue(Integer value) {
            for (EnumCheckResultType c : EnumCheckResultType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }
}
