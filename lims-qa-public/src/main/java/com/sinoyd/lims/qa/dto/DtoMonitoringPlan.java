package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.MonitoringPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;


/**
 * DtoMonitoringPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_MonitoringPlan")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoMonitoringPlan extends MonitoringPlan {
    private static final long serialVersionUID = 1L;

    /**
     * 操作意见
     */
    @Transient // 此注解标识为非数据库表字段
    private String submitRemark;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 下一步操作人中文名
     */
    @Transient
    private String nextOperatorName;


    /**
     * 制定人名称
     */
    @Transient
    private String markerName;

    /**
     * 计划明细list
     */
    @Transient
    private List<DtoMonitoringPlanDetail> dtoMonitoringPlanDetailList;

    public DtoMonitoringPlan(String id, String marker, Date markDate, String planName, String status,  String creator, Date createDate) {
        this.setId(id);
        this.setMarker(marker);
        this.setMarkDate(markDate);
        this.setPlanName(planName);
        this.setStatus(status);
        this.setCreator(creator);
        this.setCreateDate(createDate);
    }
    public DtoMonitoringPlan() {

    }
}