package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.ReviewPlanReport;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoReviewPlanReport实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_QA_ReviewPlanReport") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoReviewPlanReport extends ReviewPlanReport {
   private static final long serialVersionUID = 1L;
 }