package com.sinoyd.lims.qa.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ManagementReviewPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ManagementReviewPlan")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ManagementReviewPlan implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ManagementReviewPlan() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 年度计划id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("年度计划id")
    private String annualPlanId;

    /**
     * 评审目的
     */
    @Column(length = 2000, nullable = false)
    @ApiModelProperty("评审目的")
    @Length(message = "评审目的{validation.message.length}", max = 2000)
    private String reviewPurp;

    /**
     * 参加人员
     */
    @Column(length = 2000, nullable = false)
    @ApiModelProperty("参加人员")
    @Length(message = "参加人员{validation.message.length}", max = 2000)
    private String attendee;

    /**
     * 评审内容
     */
    @Column(length = 2000, nullable = false)
    @ApiModelProperty("评审内容")
    @Length(message = "评审内容{validation.message.length}", max = 2000)
    private String reviewContent;

    /**
     * 评审准备工作要求(评审主要议程)
     */
    @Column(length = 2000)
    @ApiModelProperty("评审准备工作要求(评审主要议程)")
    @Length(message = "评审准备工作要求{validation.message.length}", max = 2000)
    private String reviewPrepareRequired;

    /**
     * 评审时间
     */
    @Column(nullable = false)
    @ApiModelProperty("评审时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reviewTime;

    /**
     * 评审地点
     */
    @Column(nullable = false)
    @ApiModelProperty("评审地点")
    @Length(message = "评审地点{validation.message.length}", max = 255)
    private String reviewAddr;

    /**
     * 主持人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("主持人")
    @Length(message = "主持人{validation.message.length}", max = 50)
    private String host;

    /**
     * 记录人
     */
    @Column(length = 50)
    @ApiModelProperty("记录人")
    @Length(message = "记录人{validation.message.length}", max = 50)
    private String recorder;

    /**
     * 评审状态
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("评审状态")
    @Length(message = "评审状态{validation.message.length}", max = 50)
    private String status;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}