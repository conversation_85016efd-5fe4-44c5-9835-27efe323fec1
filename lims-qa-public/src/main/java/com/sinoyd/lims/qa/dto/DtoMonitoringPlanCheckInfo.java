package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.MonitoringPlanCheckInfo;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoMonitoringPlanCheckInfo实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_QA_MonitoringPlanCheckInfo")
 @Data
 @DynamicInsert
 public  class DtoMonitoringPlanCheckInfo extends MonitoringPlanCheckInfo {
   private static final long serialVersionUID = 1L;

   @Transient
   private String deptName;
 }