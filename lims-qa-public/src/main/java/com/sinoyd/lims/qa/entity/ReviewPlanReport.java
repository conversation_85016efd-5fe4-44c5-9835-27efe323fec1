package com.sinoyd.lims.qa.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ReviewPlanReport实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ReviewPlanReport")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ReviewPlanReport implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ReviewPlanReport() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 管理评审id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("管理评审id")
	private String reviewPlanId;
    
    /**
    * 编制报告人id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("编制报告人id")
    private String makerId;
    
    /**
    * 编制报告人名字
    */
    @Column(length=50)
    @ApiModelProperty("编制报告人名字")
	private String makerName;
    
    /**
    * 报告编制日期
    */
    @Column(nullable=false)
    @ApiModelProperty("报告编制日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date makeDate;
    
    /**
    * 评审目的
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("评审目的")
    @Length(message = "评审目的{validation.message.length}", max = 2000)
	private String reviewPurp;
    
    /**
    * 评审内容
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("评审内容")
    @Length(message = "评审内容{validation.message.length}", max = 2000)
	private String reviewContent;
    
    /**
    * 评审依据
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("评审依据")
    @Length(message = "评审依据{validation.message.length}", max = 2000)
	private String reviewGist;
    
    /**
    * 评审阐述
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("评审阐述")
    @Length(message = "评审阐述{validation.message.length}", max = 2000)
	private String reviewExpound;
    
    /**
    * 评审决议
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("评审决议")
    @Length(message = "评审决议{validation.message.length}", max = 2000)
	private String reviewDecision;
    
    /**
    * 是否删除
    */
    @Column(nullable=false)
    @ApiModelProperty("是否删除")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }