package com.sinoyd.lims.qa.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * MonitoringPlanCheckInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "MonitoringPlanCheckInfo")
@Data
public class MonitoringPlanCheckInfo implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public  MonitoringPlanCheckInfo() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 质量监督明细
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("质量监督明细")
    private String planDetailId;

    /**
     * 检查项的内容
     */
    @Column(length = 2000)
    @ApiModelProperty("检查项的内容")
    @Length(message = "检查项的内容{validation.message.length}", max = 2000)
    private String checkContent;

    /**
     * 检查结果
     */
    @Column(length = 2000, nullable = false)
    @ApiModelProperty("检查结果")
    @Length(message = "检查结果{validation.message.length}", max = 2000)
    private String checkResult;

    /**
     * 检查时间
     */
    @Column(nullable = false)
    @ApiModelProperty("检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkDate;

    /**
     * 检查人员Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("检查人员Id")
    private String checker;

    /**
     * 检查人员Name
     */
    @ApiModelProperty("检查人员Name")
    private String checkName;

    /**
     * 责任人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("责任人")
    private String dutyPersonId;

    /**
     * 责任人名称
     */
    @Column(length = 50)
    @ApiModelProperty("责任人名称")
    private String dutyPersonName;

    /**
     * 责任部门
     */
    @ApiModelProperty("责任部门")
    @Length(message = "责任部门{validation.message.length}", max = 255)
    private String dutyDept;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}