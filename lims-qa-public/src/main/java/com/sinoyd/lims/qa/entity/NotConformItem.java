package com.sinoyd.lims.qa.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * NotConformItem实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="NotConformItem")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class NotConformItem implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  NotConformItem() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 来源id（常量（GUID）：QA_Source）
    */
    @Column(nullable=false)
    @ApiModelProperty("S来源id（常量（GUID）：QA_ource）")
	private String sourceId;
    
    /**
    * 不符合项责任科室
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("不符合项责任科室")
	private String ncProduceDept;
    
    /**
    * 不符合项科室主任
    */
    @Column(length=50)
    private String ncDeptPerson;
    
    /**
    * 不符合项科室主任Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("不符合项科室主任Id")
    private String ncDeptPersonId;
    
    /**
    * 不符合项描述
    */
    @Column(length=2000)
    @ApiModelProperty("不符合项描述")
    @Length(message = "不符合项描述{validation.message.length}", max = 2000)
    private String ncDescribe;
    
    /**
    * 来源类型（常量（GUID）：QA_SourceType）
    */
    @Column(nullable=false)
    @ApiModelProperty("来源类型（常量（GUID）：QA_SourceType）")
    @Length(message = "来源类型{validation.message.length}", max = 50)
    private String ncSourceType;
    
    /**
    * 不符合项主要负责人Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("不符合项主要负责人Id")
    private String ncMainPersonId;
    
    /**
    * 不符合项主要负责人
    */
    @Column(length=50)
    @ApiModelProperty("不符合项主要负责人")
	private String ncMainPerson;
    
    /**
    * 不符合项发现人员
    */
    @Column(length=50)
    @ApiModelProperty("不符合项发现人员")
	private String ncFindPerson;
    
    /**
    * 不符合项发现人员Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("不符合项发现人员Id")
    private String ncFindPersonId;
    
    /**
    * 不符合项发现日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable=false)
    @ApiModelProperty("不符合项发现日期")
    private Date ncFindDate;
    
    /**
    * 不符合项发现依据
    */
    @Column(length=500)
    @ApiModelProperty("不符合项发现依据")
    @Length(message = "不符合项发现依据{validation.message.length}", max = 500)
    private String ncBasis;
    
    /**
    * 不符合项类型
    */
    @Column(nullable=false)
    @ApiModelProperty("不符合项类型")
    @Length(message = "不符合项类型{validation.message.length}", max = 50)
    private String ncType;
    
    /**
    * 不符合项原因分析
    */
    @Column(length=500)
    @ApiModelProperty("不符合项原因分析")
    @Length(message = "不符合项原因分析{validation.message.length}", max = 500)
    private String ncCauseAnalysis;
    
    /**
    * 不符合要素
    */
    @Column(length=500)
    @ApiModelProperty("不符合要素")
    @Length(message = "不符合要素{validation.message.length}", max = 500)
    private String ncElement;
    
    /**
    * 不符合项性质
    */
    @Column(length=500)
    @ApiModelProperty("不符合项性质")
    @Length(message = "不符合项性质{validation.message.length}", max = 500)
    private String ncNature;
    
    /**
    * 不符合项涉及条款号
    */
    @Column(length=1000)
    @ApiModelProperty("不符合项涉及条款号")
    @Length(message = "不符合项涉及条款号{validation.message.length}", max = 1000)
    private String ncItermNum;
    
    /**
    * 不符合项涉及条款内容
    */
    @Column(length=2000)
    @ApiModelProperty("不符合项涉及条款内容")
    @Length(message = "不符合项涉及条款内容{validation.message.length}", max = 2000)
    private String ncIterm;
    
    /**
    * 拟采取的纠正措施
    */
    @Column(length=2000)
    @ApiModelProperty("拟采取的纠正措施")
    @Length(message = "拟采取的纠正措施{validation.message.length}", max = 2000)
    private String correctMeasures;
    
    /**
    * 预计完成日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable=false)
    @ColumnDefault("1753-01-01")
    @ApiModelProperty("预计完成日期")
    private Date expectFinishDate;
    
    /**
    * 纠正措施完成日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable=false)
    @ColumnDefault("1753-01-01")
    @ApiModelProperty("纠正措施完成日期")
    private Date finishDate;
    
    /**
    * 确认纠正措施完成情况
    */
    @Column(length=2000)
    @ApiModelProperty("确认纠正措施完成情况")
    @Length(message = "确认纠正措施完成情况{validation.message.length}", max = 2000)
    private String complete;
    
    /**
    * 验证纠正措施是否有效  0：无效，1：有效 ，2：未验证
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("验证纠正措施是否有效  0：无效，1：有效 ，2：未验证")
    private Integer effective;
    
    /**
    * 验证及评价
    */
    @Column(length=2000)
    @ApiModelProperty("验证及评价")
    @Length(message = "验证及评价{validation.message.length}", max = 2000)
    private String verifierEvaluate;

    @Column
    @ApiModelProperty("验证时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date verifierDate;
    
    /**
    * 是否是潜在问题（0：不符合项，1：潜在不符合项及预防措施）
    */
    @Column(nullable=false)
    @ApiModelProperty("是否是潜在问题（0：不符合项，1：潜在不符合项及预防措施）")
    private Integer potential;
    
    /**
    * 可否恢复工作
    */
    @Column(nullable=false)
    @ApiModelProperty("可否恢复工作")
    private Integer restoreWork;
    
    /**
    * 是否通知客户
    */
    @Column(nullable=false)
    @ApiModelProperty("是否通知客户")
    private Integer notifyCustomer;
    
    /**
    * 是否需要采取纠正措施
    */
    @Column(nullable=false)
    @ApiModelProperty("是否需要采取纠正措施")
    private Integer correcteAction;
    
    /**
    * 状态
    */
    @Column(length=50)
    @ApiModelProperty("状态")
    @Length(message = "状态{validation.message.length}", max = 50)
    private String status;
    
    /**
    * 是否删除
    */
    @ApiModelProperty("是否删除")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }