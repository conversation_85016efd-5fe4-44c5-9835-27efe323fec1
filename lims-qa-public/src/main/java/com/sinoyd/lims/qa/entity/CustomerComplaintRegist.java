package com.sinoyd.lims.qa.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * CustomerComplaintRegist实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CustomerComplaintRegist")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class CustomerComplaintRegist implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CustomerComplaintRegist() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 投诉方名称
    */
    @Column(length=250,nullable=false)
    @ApiModelProperty("投诉方名称")
    @Length(message = "投诉方名称{validation.message.length}", max = 250)
	private String complaintName;
    
    /**
    * 投诉企业
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("投诉企业")
    private String entId;
    
    /**
    * 投诉人员
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("投诉人员")
    @Length(message = "投诉人员{validation.message.length}", max = 50)
    private String complaintPerson;
    
    /**
    * 投诉人员Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("投诉人员Id")
    private String complaintPersonId;
    
    /**
    * 投诉问题描述
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("投诉问题描述")
    @Length(message = "投诉问题描述{validation.message.length}", max = 2000)
    private String problemDescription;
    
    /**
    * 电话
    */
    @Column(length=20)
    @ApiModelProperty("电话")
    @Length(message = "电话{validation.message.length}", max = 20)
    private String phone;
    
    /**
    * 邮箱地址
    */
    @Column(length=100)
    @ApiModelProperty("邮箱地址")
    @Length(message = "邮箱地址{validation.message.length}", max = 100)
    private String email;
    
    /**
    * 投诉方式（常量）
    */
    @Column(length=50)
    @ApiModelProperty("投诉方式（常量）")
    @Length(message = "投诉方式{validation.message.length}", max = 50)
    private String type;
    
    /**
    * 投诉日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable=false)
    @ApiModelProperty("投诉日期")
    private Date complaintDate;
    
    /**
    * 登记人员
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("登记人员")
	private String registPerson;
    
    /**
    * 登记人员Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("登记人员Id")
    private String registPersonId;
    
    /**
    * 登记日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable=false)
    @ApiModelProperty("登记日期")
    private Date registDate;
    
    /**
    * 投诉级别
    */
    @ApiModelProperty("投诉级别")
    @Length(message = "投诉级别{validation.message.length}", max = 50)
    private String level;
    
    /**
    * 要求完成日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("要求完成日期")
	private Date finshDate;
    
    /**
    * 成立意见
    */
    @Column(length=1000)
    @ApiModelProperty("成立意见")
    @Length(message = "成立意见{validation.message.length}", max = 1000)
    private String opinion;
    
    /**
    * 状态
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("状态")
    @Length(message = "状态{validation.message.length}", max = 50)
    private String status;
    
    /**
    * 是否删除
    */
    @Column(nullable=false)
    @ApiModelProperty("是否删除")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }