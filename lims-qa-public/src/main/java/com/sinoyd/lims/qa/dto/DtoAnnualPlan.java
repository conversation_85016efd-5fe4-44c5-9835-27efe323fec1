package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.AnnualPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * DtoAnnualPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_AnnualPlan")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoAnnualPlan extends AnnualPlan {
    private static final long serialVersionUID = 1L;

    public DtoAnnualPlan() {
    }

    /**
     * 有参构造方法，只查询原型图上指定显示的数据 --xuyonglong 2021-05-07
     *
     * @param id             主键id
     * @param year           年度
     * @param planType       年度计划类别
     * @param makePersonName 制定人
     * @param makeTime       指定日期
     * @param auditContent   评审内容
     * @param auditPurpose   评审目的
     * @param creator        创建人
     * @param createDate     创建时间
     * @param status         状态
     * @param remark         备注/意见
     */
    public DtoAnnualPlan(String id, Integer year, String planType, String makePersonName, Date makeTime, String auditContent,
                         String auditPurpose, String creator, Date createDate, String status, String remark,String makePersonId ) {
        this.setId(id);
        this.setYear(year);
        this.setPlanType(planType);
        this.setMakePersonName(makePersonName);
        this.setMakeTime(makeTime);
        this.setAuditContent(auditContent);
        this.setAuditPurpose(auditPurpose);
        this.setCreator(creator);
        this.setCreateDate(createDate);
        this.setStatus(status);
        this.setRemark(remark);
        this.setMakePersonId(makePersonId);
    }

    /**
     * 操作意见
     */
    @Transient // 此注解标识为非数据库表字段
    @Length(message = "操作意见{validation.message.length}", max = 200)
    private String submitRemark;

    /**
     * 审核时间
     */
    @Transient // 此注解标识为非数据库表字段
    private Date auditTime;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 下一步操作人中文名
     */
    @Transient
    private String nextOperatorName;

    /**
     * 创建人名
     */
    @Transient
    private String creatorName;


}