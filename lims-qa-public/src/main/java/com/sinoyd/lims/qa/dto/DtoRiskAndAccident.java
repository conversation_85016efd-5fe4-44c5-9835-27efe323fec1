package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.RiskAndAccident;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;


/**
 * DtoRiskAndAccident实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/15
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_RiskAndAccident")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoRiskAndAccident extends RiskAndAccident {
    private static final long serialVersionUID = 1L;

    public DtoRiskAndAccident() {
    }

    /**
     * 定义构造方法用于接收数据库查询值
     *
     * @param dutyDomainId 责任科室 id
     * @param dutyPersonId 责任人 id
     * @param finderId     发现人id
     * @param discoverDate 发现日期
     * @param description  风险机遇描述
     * @param sourceType   来源
     * @param creator      创建人
     * @param createDate   创建时间
     * @param status       状态
     * @param nextPerson   下一步操作人
     * @param id           风险机遇id
     */
    public DtoRiskAndAccident(String dutyDomainId, String dutyPersonId, String finderId, Date discoverDate, String description,
                              String sourceType, String creator, Date createDate, String status, String nextPerson, String id, String directorId) {
        this.setDutyDomainId(dutyDomainId);
        this.setDutyPersonId(dutyPersonId);
        this.setFinderId(finderId);
        this.setDiscoverDate(discoverDate);
        this.setDescription(description);
        this.setSourceType(sourceType);
        this.setCreator(creator);
        this.setCreateDate(createDate);
        this.setStatus(status);
        this.setNextOperator(nextPerson);
        this.setId(id);
        this.setDirectorId(directorId);
    }

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 原因
     */
    @Transient
    private String submitRemark;

    /**
     * 是否通过完成确认，true为通过，false为未通过
     */
    @Transient
    private Boolean isPassCompleteFlag;

    /**
     * 责任科室名称
     */
    @Transient
    private String dutyDomainName;

    /**
     * 责任人名称
     */
    @Transient
    private String dutyPersonName;
    /**
     * 科室主任姓名
     */
    @Transient
    private String directorName;

    /**
     * 发现人姓名
     */
    @Transient
    private String finderName;

    /**
     * 前台显示来源类型
     */
    @Transient
    private String sourceTypeName;
}