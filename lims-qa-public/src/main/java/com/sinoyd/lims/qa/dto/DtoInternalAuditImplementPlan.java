package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.InternalAuditImplementPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;


/**
 * DtoInternalAuditImplementPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_InternalAuditImplementPlan")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoInternalAuditImplementPlan extends InternalAuditImplementPlan {
    private static final long serialVersionUID = 1L;

    /**
     * 操作意见
     */
    @Transient // 此注解标识为非数据库表字段
    private String submitRemark;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 责任人名字
     */
    @Transient
    private String personInChargeName;

    /**
     * 审核人员名字
     */
    @Transient
    private String auditorName;

    /**
     * 部门的中文名
     */
    @Transient
    private String auditedDeptName;

    /**
     * 检查项集合
     */
    @Transient
    private List<DtoInternalCheckInfo> dtoInternalCheckInfoList;




    /**
     * @param id             主键id
     * @param auditPlanId    内审计划
     * @param auditTime      审核时间
     * @param auditedDept    审核部门
     * @param personInCharge 责任人
     * @param auditor        审核人员
     * @param auditElement   审核要素
     * @param creator        创建人
     * @param createDate     创建时间
     */
    public DtoInternalAuditImplementPlan(String id, String auditPlanId, Date auditTime, String auditedDept,
                                         String personInCharge, String auditor, String auditElement, String
                                                 creator, Date createDate) {
        this.setId(id);
        this.setAuditPlanId(auditPlanId);
        this.setAuditTime(auditTime);
        this.setAuditedDept(auditedDept);
        this.setPersonInCharge(personInCharge);
        this.setAuditor(auditor);
        this.setAuditElement(auditElement);
        this.setCreator(creator);
        this.setCreateDate(createDate);
    }

    public DtoInternalAuditImplementPlan() {

    }
}