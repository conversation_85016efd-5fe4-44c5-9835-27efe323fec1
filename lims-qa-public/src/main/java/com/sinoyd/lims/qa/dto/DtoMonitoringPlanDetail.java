package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.MonitoringPlanDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoMonitoringPlanDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_MonitoringPlanDetail")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoMonitoringPlanDetail extends MonitoringPlanDetail {
    private static final long serialVersionUID = 1L;

    /**
     * 操作意见
     */
    @Transient // 此注解标识为非数据库表字段
    private String submitRemark;

    /**
     * 责任人
     */
    @Transient // 此注解标识为非数据库表字段
    private String dutyPersonIdName;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 部门中文名
     */
    @Transient
    private String deptName;
}