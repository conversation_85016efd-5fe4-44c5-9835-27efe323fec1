package com.sinoyd.lims.qa.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * Log实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/29
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Log")
 @Data
 public  class Log implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Log() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 操作者Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作者Id")
    private String operatorId;
    
    /**
    * 操作者名字
    */
    @Column(length=50)
    @ApiModelProperty("操作者名字")
	private String operatorName;
    
    /**
    * 操作时间
    */
    @Column(nullable=false)
    @ApiModelProperty("操作时间")
    private Date operateTime;
    
    /**
    * 操作类型(EnumOperationInfo, 1: 新增， 2: 审核)
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("操作类型(见枚举EnumLogOperateType)")
    @Length(message = "操作类型{validation.message.length}", max = 50)
    private String operateInfo;
    
    /**
    * 下一步操作人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("下一步操作人Id")
    private String nextOperatorId;
    
    /**
    * 下一步操作人名字
    */
    @Column(length=50)
    @ApiModelProperty("下一步操作人名字")
	private String nextOperatorName;
    
    /**
    * 日志类型
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("日志类型")
    @Length(message = "日志类型{validation.message.length}", max = 50)
	private String logType;
    
    /**
    * 对象id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("对象id")
    private String objectId;
    
    /**
    * 对象类型
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("对象类型")
    @Length(message = "对象类型{validation.message.length}", max = 50)
    private String objectType;
    
    /**
    * 说明
    */
    @Column(length=4000)
    @ApiModelProperty("说明")
	private String comment;
    
    /**
    * 意见
    */
    @Column(length=1000)
    @ApiModelProperty("意见")
    @Length(message = "意见{validation.message.length}", max = 1000)
    private String opinion;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }