package com.sinoyd.lims.qa.dto;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.qa.entity.CustomerComplaintRegist;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;


/**
 * DtoCustomerComplaintRegist实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_CustomerComplaintRegist")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoCustomerComplaintRegist extends CustomerComplaintRegist {
    private static final long serialVersionUID = 1L;

    public DtoCustomerComplaintRegist() {

    }

    //投诉方名称，投诉人员，投诉方式，投诉时间，登记人员，登记日期，投诉级别，要求完成日期，状态
    public DtoCustomerComplaintRegist(String id, String complaintName, String complaintPerson, String type, Date complaintDate,
                                      String registPerson, String registPersonId, Date registDate, String level, Date finshDate, String status) {
        this.setOrgId(PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "");
        this.setDomainId(PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "");
        this.setComplaintName(complaintName);
        this.setComplaintPerson(complaintPerson);
        this.setType(type);
        this.setComplaintDate(complaintDate);
        this.setRegistPerson(registPerson);
        this.setRegistPersonId(registPersonId);
        this.setRegistDate(registDate);
        this.setLevel(level);
        this.setFinshDate(finshDate);
        this.setStatus(status);
        this.setId(id);
    }

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 未通过原因
     */
    @Transient
    private String submitRemark;
    /**
     * 是否办结通过标示，true为通过，false为不通过
     */
    @Transient
    private Boolean isPassCompleteFlag;

    @Transient
    private String levelName;

    @Transient
    private String typeName;
}