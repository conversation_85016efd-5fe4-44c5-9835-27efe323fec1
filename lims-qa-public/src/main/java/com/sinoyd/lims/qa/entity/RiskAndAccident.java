package com.sinoyd.lims.qa.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * RiskAndAccident实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/15
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "RiskAndAccident")
@Data
@EntityListeners(AuditingEntityListener.class)
public class RiskAndAccident implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public RiskAndAccident() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 发现日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable = false)
    @ApiModelProperty("发现日期")
    private Date discoverDate;

    /**
     * 责任科室
     */
    @Column(length = 50)
    @ApiModelProperty("责任科室")
    @Length(message = "责任科室{validation.message.length}", max = 50)
    private String dutyDomainId;

    /**
     * 来源类型（常量：实验室内审、管理评审、质量监督、客户投诉、其他）
     */
    @Column(length = 50)
    @ApiModelProperty("来源类型（常量：实验室内审、管理评审、质量监督、客户投诉、其他）")
    @Length(message = "来源类型{validation.message.length}", max = 50)
    private String sourceType;

    /**
     * 风险机遇可能性（枚举）
     */
    @Column(nullable = false)
    @ApiModelProperty("风险机遇可能性（枚举）")
    private Integer possibility;

    /**
     * 风险机遇严重性（枚举）
     */
    @Column(nullable = false)
    @ApiModelProperty("风险机遇严重性（枚举）")
    private Integer seriousness;

    /**
     * 科室主任
     */
    @Column(length = 50)
    @ApiModelProperty("科室主任")
    @Length(message = "科室主任{validation.message.length}", max = 50)
    private String directorId;

    /**
     * 责任人
     */
    @Column(length = 50)
    @ApiModelProperty("责任人")
    @Length(message = "责任人{validation.message.length}", max = 50)
    private String dutyPersonId;

    /**
     * 发现人员
     */
    @Column(length = 50)
    @ApiModelProperty("发现人员")
    @Length(message = "发现人员{validation.message.length}", max = 50)
    private String finderId;

    /**
     * 措施拟定
     */
    @Column(length = 500)
    @ApiModelProperty("措施拟定")
    @Length(message = "措施拟定{validation.message.length}", max = 500)
    private String measure;

    /**
     * 拟定人
     */
    @Column(length = 50)
    @ApiModelProperty("拟定人")
    @Length(message = "拟定人{validation.message.length}", max = 50)
    private String studyOutPersonId;

    /**
     * 审核人
     */
    @Column(length = 50)
    @ApiModelProperty("审核人")
    @Length(message = "审核人{validation.message.length}", max = 50)
    private String auditor;

    /**
     * 确认人
     */
    @Column(length = 50)
    @ApiModelProperty("确认人")
    @Length(message = "确认人{validation.message.length}", max = 50)
    private String affirmPersonId;

    /**
     * 完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable = false)
    @ColumnDefault("1753-01-01")
    @ApiModelProperty("完成日期")
    private Date achieveDate;

    /**
     * 完成情况
     */
    @Column(length = 500)
    @ApiModelProperty("完成情况")
    @Length(message = "完成情况{validation.message.length}", max = 500)
    private String performance;

    /**
     * 确认日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable = false)
    @ColumnDefault("1753-01-01")
    @ApiModelProperty("确认日期")
    private Date affirmDate;

    /**
     * 确认及评价
     */
    @Column(length = 500)
    @ApiModelProperty("确认及评价")
    @Length(message = "确认及评价{validation.message.length}", max = 500)
    private String affirmEvaluate;

    /**
     * 发现日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(nullable = false)
    @ColumnDefault("1753-01-01")
    @ApiModelProperty("发现日期")
    private Date findDate;

    /**
     * 风险机遇系数
     */
    @Column(length = 50)
    @ApiModelProperty("风险机遇系数")
    @Length(message = "风险机遇系数{validation.message.length}", max = 50)
    private String coefficient;

    /**
     * 风险机遇描述
     */
    @Column(length = 500)
    @ApiModelProperty("风险机遇描述")
    @Length(message = "风险机遇描述{validation.message.length}", max = 500)
    private String description;

    /**
     * 原因分析
     */
    @Column(length = 500)
    @ApiModelProperty("原因分析")
    @Length(message = "原因分析{validation.message.length}", max = 500)
    private String reason;

    /**
     * 评审状态
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("评审状态")
    @Length(message = "评审状态{validation.message.length}", max = 50)
    private String status;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}