package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.InternalAuditPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;


/**
 * DtoInternalAuditPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_InternalAuditPlan")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoInternalAuditPlan extends InternalAuditPlan {
    private static final long serialVersionUID = 1L;

    /**
     * 人员id数组
     */
    @Transient // 此注解标识为非数据库表字段
    private List<String> attendeeId;

    /**
     * 人员字符串
     */
    @Transient // 此注解标识为非数据库表字段
    private String AttendeeName;

    /**
     * 操作意见
     */
    @Transient // 此注解标识为非数据库表字段
    private String submitRemark;

    /**
     * 年度计划审核目的
     */
    @Transient // 此注解标识为非数据库表字段
    private String annualPlanName;

    /**
     * 报告对象
     */
    @Transient // 此注解标识为非数据库表字段
    private DtoInternalAuditPlanReport dtoInternalAuditPlanReport;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 下一步操作人中文名
     */
    @Transient
    private String nextOperatorName;

    /**
     * 创建人名称
     */
    @Transient
    private String founderName;


    /**
     * @param id           主键id
     * @param auditPurp    审核目的
     * @param auditScope   审核范围
     * @param auditTime    审核时间
     * @param auditContent 审核内容
     * @param attendee     审核参与人员
     * @param auditGist    审核依据
     * @param status       状态
     * @param creator      创建人
     * @param createDate   创建时间
     * @param annualPlanId 年度计划id
     */
    public DtoInternalAuditPlan(String id, String auditPurp, String auditScope, Date auditTime, String auditContent,
                                String attendee, String auditGist, String status, String creator, Date createDate, String annualPlanId) {
        this.setId(id);
        this.setAuditPurp(auditPurp);
        this.setAuditScope(auditScope);
        this.setAuditTime(auditTime);
        this.setAuditContent(auditContent);
        this.setAttendee(attendee);
        this.setAuditGist(auditGist);
        this.setStatus(status);
        this.setCreator(creator);
        this.setCreateDate(createDate);
        this.setAnnualPlanId(annualPlanId);
    }
    public DtoInternalAuditPlan() {

    }
}