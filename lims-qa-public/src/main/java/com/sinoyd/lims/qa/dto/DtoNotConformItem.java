package com.sinoyd.lims.qa.dto;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.qa.entity.NotConformItem;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;


/**
 * DtoNotConformItem实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_NotConformItem")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoNotConformItem extends NotConformItem {
    private static final long serialVersionUID = 1L;

    /**
     * 意见，用于列表上意见的动态查询
     */
    @Transient
    private String submitRemark;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;
    /**
     * 是否通过不符合项验证
     */
    @Transient
    private Boolean isPassCompleteFlag;
    /**
     * 字典来源中文
     */
    @Transient
    private String sourceName;
    /**
     * 字典类型中文
     */
    @Transient
    private String typeName;
    /**
     * 部门名称
     */
    @Transient
    private String departmentName;
    /**
     * 来源类型
     */
    @Transient
    private String sourceTypeName;

    /**
     * 创建构造方法用于接收查询结果集
     *
     * @param id             主键
     * @param ncProduceDept  责任科室
     * @param ncMainPerson   责任人
     * @param ncFindPersonId 发现人id
     * @param ncFindPerson   发现人
     * @param ncMainPersonId 发现人id
     * @param ncFindDate     发现时间
     * @param ncType         类型
     * @param sourceId       来源id
     * @param ncDescribe     不符合项描述
     * @param ncSourceType   源头类型
     * @param creator        创建人
     * @param createDate     创建时间
     * @param status         状态
     * @param nextOperator   下一步操作人
     */
    public DtoNotConformItem(String id, String ncProduceDept, String ncMainPerson,
                             String ncMainPersonId, String ncFindPerson, String ncFindPersonId,
                             Date ncFindDate, String ncType, String sourceId,
                             String ncDescribe, String ncSourceType,
                             String creator, Date createDate,
                             String status, String nextOperator) {

        this.setOrgId(PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "");
        this.setDomainId(PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "");
        this.setId(id);
        this.setNcProduceDept(ncProduceDept);
        this.setNcMainPerson(ncMainPerson);
        this.setNcMainPersonId(ncMainPersonId);
        this.setNcFindDate(ncFindDate);
        this.setNcFindPerson(ncFindPerson);
        this.setNcType(ncType);
        this.setSourceId(sourceId);
        this.setNcSourceType(ncSourceType);
        this.setNcDescribe(ncDescribe);
        this.setCreator(creator);
        this.setCreateDate(createDate);
        this.setStatus(status);
        this.setNextOperator(nextOperator);
        this.setNcFindPersonId(ncFindPersonId);
    }

    public DtoNotConformItem() {

    }
}