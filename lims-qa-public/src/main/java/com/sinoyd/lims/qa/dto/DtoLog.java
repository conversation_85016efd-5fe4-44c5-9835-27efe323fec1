package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.Log;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoLog实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/29
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_QA_Log")
 @Data
 @DynamicInsert
 public  class DtoLog extends Log {
   private static final long serialVersionUID = 1L;
 }