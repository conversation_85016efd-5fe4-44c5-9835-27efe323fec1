package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.SubmitRecordQa;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoSubmitRecord实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/24
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_SubmitRecord")
@Data
@DynamicInsert
public class DtoSubmitRecordQa extends SubmitRecordQa {
    private static final long serialVersionUID = 1L;

}