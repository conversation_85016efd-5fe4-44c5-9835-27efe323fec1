package com.sinoyd.lims.qa.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * InternalCheckInfo实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="InternalCheckInfo")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class InternalCheckInfo implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  InternalCheckInfo() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 内审的实施计划id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("内审的实施计划id")
	private String implementPlanId;
    
    /**
    * 检查项的内容
    */
    @Column(length=2000,nullable=false)
    @ApiModelProperty("检查项的内容")
    @Length(message = "检查项的内容{validation.message.length}", max = 2000)
	private String checkContent;
    
    /**
    * 审核人员
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("审核人员")
    @Length(message = "审核人员{validation.message.length}", max = 50)
	private String auditor;
    
    /**
    * 检查结果（1：符合、2：基本符合、3：不符合、4：不适用）
    */
    @Column(nullable=false)
    @ApiModelProperty("检查结果（1：符合、2：基本符合、3：不符合、4：不适用）")
    private Integer checkResult;
    
    /**
    * 备注信息
    */
    @Column(length=2000)
    @ApiModelProperty("备注信息")
    @Length(message = "备注信息{validation.message.length}", max = 2000)
    private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 是否删除
    */
    @Column(nullable=false)
    @ApiModelProperty("是否删除")
	private Boolean isDeleted=false;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }