package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.InternalCheckInfo;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoInternalCheckInfo实体
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_QA_InternalCheckInfo") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoInternalCheckInfo extends InternalCheckInfo {
   private static final long serialVersionUID = 1L;

   @Transient
 private String checkResultLabel;
 }