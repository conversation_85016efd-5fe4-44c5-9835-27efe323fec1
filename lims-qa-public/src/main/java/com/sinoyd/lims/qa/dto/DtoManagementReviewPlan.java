package com.sinoyd.lims.qa.dto;

import com.sinoyd.lims.qa.entity.ManagementReviewPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;


/**
 * DtoManagementReviewPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_QA_ManagementReviewPlan")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoManagementReviewPlan extends ManagementReviewPlan {
    private static final long serialVersionUID = 1L;

    /**
     * 操作意见
     */
    @Transient // 此注解标识为非数据库表字段
    private String submitRemark;

    /**
     * DtoReviewPlanReport表
     */
    @Transient // 此注解标识为非数据库表字段
    private DtoReviewPlanReport dtoReviewPlanReport;

    /**
     * 人员id数组
     */
    @Transient // 此注解标识为非数据库表字段
    private List<String> attendeeId;

    /**
     * 年度计划审核目的
     */
    @Transient // 此注解标识为非数据库表字段
    private String annualPlanName;

    /**
     * 下一步操作人
     */
    @Transient
    private String nextOperator;

    /**
     * 下一步操作人中文名
     */
    @Transient
    private String nextOperatorName;

    /**
     * 参加人员名称
     */
    @Transient
    private String attendeeName;

    /**
     * 主持人名
     */
    @Transient
    private String hostName;

    /**
     * 记录人名
     */
    @Transient
    private String recorderName;

    /**
     * 创建人名
     */
    @Transient
    private String creatorName;


    /**
     * @param id                    主鍵id
     * @param reviewTime            评审时间
     * @param reviewPurp            评审目的
     * @param reviewContent         评审内容
     * @param reviewAddr            评审地点
     * @param reviewPrepareRequired 评审准备工作要求(评审主要议程)
     * @param host                  主持人
     * @param attendee              参加人员
     * @param recorder              记录人
     * @param createDate            创建时间
     * @param status                评审状态
     */
    public DtoManagementReviewPlan(String id, Date reviewTime, String reviewPurp, String reviewContent, String reviewAddr, String reviewPrepareRequired
            , String host, String attendee, String recorder, Date createDate, String status,String annualPlanId,String creator) {
        this.setId(id);
        this.setReviewTime(reviewTime);
        this.setReviewPurp(reviewPurp);
        this.setReviewContent(reviewContent);
        this.setReviewAddr(reviewAddr);
        this.setReviewPrepareRequired(reviewPrepareRequired);
        this.setHost(host);
        this.setAttendee(attendee);
        this.setRecorder(recorder);
        this.setCreateDate(createDate);
        this.setStatus(status);
        this.setAnnualPlanId(annualPlanId);
        this.setCreator(creator);

    }

    public DtoManagementReviewPlan() {

    }
}