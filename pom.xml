<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.lims.webbackend</groupId>
    <artifactId>lims-webbackend</artifactId>
    <version>${lims.version}-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>lims-webbackend</name>
    <description>lims启动项</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <tomcat.version>8.5.65</tomcat.version>

        <monitor.version>ent-5.4.82</monitor.version>
        <qa.version>ent-5.4.82</qa.version>
        <query.version>ent-5.4.82</query.version>
        <api.version>ent-5.4.82</api.version>
        <od.version>ent-5.4.82</od.version>
        <lims.version>ent-5.4.82</lims.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.sinoyd.lims.monitor</groupId>
            <artifactId>lims-monitor-impl</artifactId>
            <version>${monitor.version}-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sinoyd.lims.qa</groupId>
            <artifactId>lims-qa-impl</artifactId>
            <version>${qa.version}-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sinoyd.lims.query</groupId>
            <artifactId>lims-query-impl</artifactId>
            <version>${query.version}-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sinoyd.lims.api</groupId>
            <artifactId>lims-api-impl</artifactId>
            <version>${api.version}-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sinoyd.lims.od</groupId>
            <artifactId>lims-od-impl</artifactId>
            <version>${od.version}-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>3.2.2</version>
                <scope>compile</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

</project>
