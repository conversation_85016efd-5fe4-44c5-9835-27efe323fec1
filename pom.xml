<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.sinoyd.lims.api</groupId>
	<artifactId>lims-api</artifactId>
	<version>${api.version}-SNAPSHOT</version>
	<name>lims-api</name>
    <description>LIMS APP模块</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <frame.arch>0.0.1Cloud-SNAPSHOT</frame.arch>
        <workflow.version>1.0.2-SNAPSHOT</workflow.version>

        <api.version>ent-5.4.82</api.version>
        <pro.version>ent-5.4.82</pro.version>
    </properties>

    <modules>
        <module>lims-api-public</module>
        <module>lims-api-arch</module>
        <module>lims-api-impl</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims.api</groupId>
                <artifactId>lims-api-public</artifactId>
                <version>${api.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims.api</groupId>
                <artifactId>lims-api-arch</artifactId>
                <version>${api.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims.api</groupId>
                <artifactId>lims-api-impl</artifactId>
                <version>${api.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims.pro</groupId>
                <artifactId>lims-pro-sync</artifactId>
                <version>${pro.version}-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
