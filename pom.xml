<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.base</groupId>
	<artifactId>sinoyd-base</artifactId>
	<version>${base.version}-SNAPSHOT</version>
	<name>sinoyd-base</name>
    <description>LIMS Base模块</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <frame.arch>0.0.2Cloud-SNAPSHOT</frame.arch>
        <workflow.version>1.0.5-SNAPSHOT</workflow.version>
        <mapstrut.version>1.5.3.Final</mapstrut.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <fastjson.version>1.2.83</fastjson.version>
        <cells.version>9.0.0</cells.version>
        <words.version>19.5.0</words.version>
        <pdf.version>18.9.0</pdf.version>
        <easypoi.version>4.0.0</easypoi.version>
        <cglib.version>2.2.2</cglib.version>
        <beanutils.version>1.9.4</beanutils.version>
        <itextpdf.version>5.5.13.2</itextpdf.version>
        <owasp.esapi.version>2.5.2.0</owasp.esapi.version>
        <flyway.version>3.2.2</flyway.version>
        <framework.version>1.5.15.RELEASE</framework.version>
        <jsoup.version>1.17.2</jsoup.version>
        <commons-text.version>1.3</commons-text.version>
        <lims.commons.version>6.0.63</lims.commons.version>
        <base.version>ent-5.4.82</base.version>
        <rcc.version>ent-5.4.52</rcc.version>
    </properties>

    <modules>
        <module>sinoyd-base-public</module>
        <module>sinoyd-base-arch</module>
        <module>sinoyd-base-impl</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.base</groupId>
                <artifactId>sinoyd-base-public</artifactId>
                <version>${base.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.base</groupId>
                <artifactId>sinoyd-base-arch</artifactId>
                <version>${base.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.base</groupId>
                <artifactId>sinoyd-base-impl</artifactId>
                <version>${base.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-commons</artifactId>
                <version>${lims.commons.version}-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.frame</groupId>
                <artifactId>frame-arch</artifactId>
                <version>${frame.arch}</version>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-cells</artifactId>
                <version>${cells.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-words</artifactId>
                <version>${words.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-pdf</artifactId>
                <version>${pdf.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.sinoydcloud.boot</groupId>
                <artifactId>sinoyd-boot-starter-workflow-activiti</artifactId>
                <version>${workflow.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>${easypoi.version}</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstrut.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>${framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstrut.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok.mapstruct.binding.version}</version>
            </dependency>

            <dependency>
                <groupId>org.owasp.esapi</groupId>
                <artifactId>esapi</artifactId>
                <version>${owasp.esapi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims.rcc</groupId>
                <artifactId>rcc-commons</artifactId>
                <version>${rcc.version}-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
