package com.sinoyd.lims.pro.component;

import com.rabbitmq.client.Channel;
import com.sinoyd.lims.lim.dto.lims.DtoLogForAnalyzeMethod;
import com.sinoyd.lims.lim.repository.lims.LogForAnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.LogForAnalyzeMethodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 方法日志同步组件
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/01
 */
@Component
@Slf4j
public class LogForAnalyzeMethodSyncComponent extends AbsSyncComponent<DtoLogForAnalyzeMethod, String, LogForAnalyzeMethodService, LogForAnalyzeMethodRepository> {

    /**
     * 使用RabbitMQ监听器注解来监听特定队列的消息。
     * 当队列接收到消息时，调用sync方法进行处理。
     *
     * @param msg         接收到的消息对象
     * @param deliveryTag 消息的投递标签
     * @param channel     RabbitMQ的通道对象
     * @throws IOException 如果在处理消息时发生IO异常
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "#{@exchangeName + '-' + T(com.sinoyd.lims.pro.component.LogForAnalyzeMethodSyncComponent).newInstance().getTClassName()}", durable = "true"),
            exchange = @Exchange(value = "#{@exchangeName}", durable = "true"),
            key = "#{@exchangeName + '.' + T(com.sinoyd.lims.pro.component.LogForAnalyzeMethodSyncComponent).newInstance().getTClassName() + '.key'}"
    ))
    public void sync(Message msg, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        processMsg(msg, deliveryTag, channel);
    }
}
