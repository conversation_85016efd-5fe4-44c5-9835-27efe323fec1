package com.sinoyd.lims.pro.config;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.AESUtil;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.model.UserModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.boot.frame.sys.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * MQ 客户端
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/24
 **/
@Configuration
@Slf4j
public class MqClientConfiguration {

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IUserService userService;

    @Value("${lims.admin.uid:i40XtquqCMru6oC8lDI0dw==}")
    private String uid;


    /**
     * 获取交换机名称
     *
     * @return 交换机名称
     * @throws BaseException 获取交换机名称失败时抛出异常
     */
    @Bean
    @Order(1)
    public String exchangeName() {
        String loginName = AESUtil.decrypt(uid);
        log.info(".... loginName:{}", loginName);
        RestResponse<UserModel> restResponse = this.userService.login(loginName, "", true);
        if (restResponse.isSuccess() && restResponse.getData() != null) {
            UserModel userModel = restResponse.getData();
            OrgModel orgModel = orgService.selectById(userModel.getOrgGuid());
            String orgCode = orgModel.getOrgCode();
            log.info(".... orgCode:{}", orgCode);
            return orgCode;
        } else {
            throw new BaseException("获取交换机名称失败，可能是尚未配置机构编码");
        }
    }

}
