package com.sinoyd.lims.pro.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.retry.MessageRecoverer;
import org.springframework.amqp.rabbit.retry.RepublishMessageRecoverer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/24
 **/
@Configuration
@Slf4j
public class RabbitMqConfiguration {

    /**
     * 自定义RabbitMQ监听容器工厂
     *
     * @param configurer        容器配置器
     * @param connectionFactory 连接工厂
     * @param recoverer         消息恢复器
     * @return 自定义的RabbitMQ监听容器工厂
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            ConnectionFactory connectionFactory,
            MessageRecoverer recoverer) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);

        // 设置手动确认模式
        factory.setAcknowledgeMode(org.springframework.amqp.core.AcknowledgeMode.MANUAL);

        log.info("自定义RabbitMQ监听容器工厂配置完成");
        return factory;
    }

    /**
     * 自定义RabbitTemplate，用于发送消息
     *
     * @param connectionFactory 连接工厂
     * @return 自定义的RabbitTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);

        // 设置消息确认回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                log.error("消息发送失败，原因：{}", cause);
            }
        });

        // 设置消息返回回调
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            log.error("消息发送失败，exchange: {}, routingKey: {}, replyCode: {}, replyText: {}",
                    exchange, routingKey, replyCode, replyText);
        });

        return rabbitTemplate;
    }

    /**
     * 消息重试恢复器，当重试次数达到上限时，将消息转发到"失败"队列
     * 注意：我们在AbsSyncComponent中自定义了重试逻辑，这里只是作为备用方案
     *
     * @param rabbitTemplate RabbitTemplate
     * @return 消息恢复器
     */
    @Bean
    public MessageRecoverer messageRecoverer(RabbitTemplate rabbitTemplate) {
        return new RepublishMessageRecoverer(rabbitTemplate, "message.failed.exchange", "message.failed") {
            @Override
            public void recover(Message message, Throwable cause) {
                log.warn("消息重试失败，将消息转发到失败队列");
//                super.recover(message, cause);
            }
        };
    }
} 