package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.MessageReceiveMonitor;
import com.sinoyd.commons.enums.EnumMsgReSendStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 消费端消息监控表传输实体
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/16
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_MessageReceiveMonitor")
@Data
@DynamicInsert
public class DtoMessageReceiveMonitor extends MessageReceiveMonitor {


    /**
     * 无参构造方法
     */
    public DtoMessageReceiveMonitor() {
        super();
    }

    /**
     * 构造方法
     *
     * @param message    消息内容
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param queue      队列
     * @param isSuccess  是否成功
     * @param reason     原因
     */
    public DtoMessageReceiveMonitor(String message, String exchange, String routingKey, String queue, Boolean isSuccess, String reason) {
        this();
        setMessage(message);
        setExchange(exchange);
        setRoutingKey(routingKey);
        setQueue(queue);
        setIsSuccess(isSuccess);
        setSendStatus(EnumMsgReSendStatus.发送失败.getValue());
        setReason(EnumMsgReSendStatus.发送失败.name() + ":" + reason);
    }
}
