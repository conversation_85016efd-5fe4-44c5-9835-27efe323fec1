package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.Document;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.List;


/**
 * DtoDocument实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_Document")
@Data
@DynamicInsert
public class DtoDocument extends Document {

    /**
     * 一项一档原始记录单检测单号显示
     */
    @Transient
    private String workSheetCode;


    /**
     * 一项一档原始记录单检分析项目名称
     */
    @Transient
    private String analyseNames;

    /**
     * 一项一档报告文档报告编号显示
     */
    @Transient
    private String reportCode;

    /**
     * 一项一档报告文档报告类型显示
     */
    @Transient
    private String reportType;

    /**
     * 一项一档报告文档报告年份显示
     */
    @Transient
    private String reportYear;

    /**
     * 一项一档采样送样单号显示
     */
    @Transient
    private String recordCode;

    /**
     * 一项一档采样送样送样人显示
     */
    @Transient
    private String senderName;

    /**
     * 报告附件 显示项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 受检方
     */
    @Transient
    private String inspectedEnt;

    /**
     * 登记时间
     */
    @Transient
    private Date inputTime;

    /**
     * 是否锁定 1是  0否
     */
    @Transient
    private Integer isLock;

    /**
     * 工作单合并后的路径集合
     */
    @Transient
    private List<String> workSheetPathList;

    /**
     * 上传编码
     */
    @Transient
    private String code;

    /**
     * 采样日期
     */
    @Transient
    private String samplingDate;

    /**
     * 检测类型 多个用顿号隔开显示
     */
    @Transient
    private String sampleTypes;

    /**
     * 文件标识
     */
    @Transient
    private String tag;

    public DtoDocument(){}

    public DtoDocument(String physicalName, String path, String id, String filename, String folderId) {
        setPhysicalName(physicalName);
        setPath(path);
        setId(id);
        setFilename(filename);
        setFolderId(folderId);
    }
}