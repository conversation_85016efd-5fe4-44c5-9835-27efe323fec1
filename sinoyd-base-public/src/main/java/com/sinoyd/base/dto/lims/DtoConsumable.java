package com.sinoyd.base.dto.lims;

import com.sinoyd.base.dto.customer.DtoImportConsumable;
import com.sinoyd.base.entity.Consumable;
import com.sinoyd.boot.common.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;


/**
 * DtoConsumable实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_Consumable")
@Data
@DynamicInsert
public class DtoConsumable extends Consumable {

    @Transient
    @Valid
    private DtoConsumableDetail detail;

    /**
     * 等级名称
     */
    @Transient
    private String gradeName;

    /**
     * 类型名称
     */
    @Transient
    private String categoryName;

    /**
     * 有效日期(String格式)
     */
    @Transient
    private String expiryDate;

    /**
     * 存放地点
     */
    @Transient
    private String keepPlace;

    /**
     * 标样库存数量
     */
    @Transient
    private BigDecimal standardStorage;

    /**
     * 标样不确定度范围
     */
    @Transient
    private String concentrationRange;

    /**
     * 过期预警时间（天数）
     */
    @Transient
    private String expireAlertDays;

    /**
     * 采购详细id
     */
    @Transient
    private String purchaseDetailId;

    /**
     * 复制次数
     */
    @Transient
    private Integer times;

    /**
     * 提醒人
     */
    @Transient
    private String sendWarnUser;


    /**
     * 混标信息
     */
    @Transient
    private List<DtoConsumableOfMixed> consumableOfMixedList;

    /**
     * 过期状态
     */
    @Transient
    private String expireStatus;

    /**
     * 可领用库存（用于领料审批流程中的消耗品选择）
     */
    @Transient
    private BigDecimal pickUpInventory;

    /**
     * 是否库存警告
     */
    @Transient
    private Boolean isWarning = false;


    /**
     * 导入实体值
     *
     * @param importEntity 导入数据
     */
    public void importToEntity(DtoImportConsumable importEntity) {
        setId(importEntity.getId());
        setConsumableCode(importEntity.getConsumableCode());
        setCodeInStation(importEntity.getCodeInStation());
        setConsumableName(importEntity.getConsumableName());
        setInventory(new BigDecimal(importEntity.getInventory()));
        setCategoryId(importEntity.getCategoryId());
        setSpecification(importEntity.getSpecification());
        setGrade(StringUtil.isNotNull(importEntity.getGrade()) ? importEntity.getGrade() : "");
        setUnit(importEntity.getUnit());
        setSendWarnUserId(importEntity.getSendWarnUserId());
        setWarningNum(StringUtil.isEmpty(importEntity.getWarningNum()) ? BigDecimal.ZERO : new BigDecimal(importEntity.getWarningNum()));
        setConcentration(importEntity.getStandard());
        setUncertainty(importEntity.getUncertainty());
        setDilutedSolution(importEntity.getDilutedSolution());
        setDilutionMethod(importEntity.getDilutionMethod());
        setKeepCondition(importEntity.getKeepCondition());
        setSafetyInstruction(importEntity.getSafetyInstruction());
        setIsStandard(importEntity.getIsStandard());
        setDimensionName(importEntity.getDimension());
        setRangeLow(importEntity.getRangeLow());
        setRangeHigh(importEntity.getRangeHigh());
    }
}