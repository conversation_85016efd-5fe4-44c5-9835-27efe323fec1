package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.Enterprise;

import javax.persistence.*;
import javax.validation.Valid;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import java.util.List;


/**
 * DtoEnterprise实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_Enterprise")
@Data
@DynamicInsert
public class DtoEnterprise extends Enterprise {

    /**
     * 是否使用（账号）
     */
    @Transient
    private Boolean isUsed = false;

    /**
     * 是否违约
     */
    @Transient
    private Boolean isBreak = false;

    /**
     * 违约信息
     */
    @Transient
    private String breakInfo;

    /**
     * 关注程度
     */
    @Transient
    private Integer attentionDegree = -1;

    /**
     * 是否污染源
     */
    @Transient
    private Boolean isPollution = false;

    /**
     * 污染源类型：（常量 LIM_PollutionSourceType：1工业污染源、2污水处理厂、3固废处理厂）
     */
    @Transient
    private List<Integer> pollutionSourceTypeList;

    /**
     * 分包费率
     */
    @Transient
    private String subRate;

    /**
     * 污染源编号
     */
    @Transient
    @Length(message = "污染源编号{validation.message.length}", max = 50)
    private String pollutionCode;

    /**
     * 项目关联企业id
     */
    @Transient
    private String proCustomerId;

    /**
     * 企业拓展数据
     */
    @Transient
    @Valid
    private DtoEnterpriseExtend enterpriseExtend;
}