package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DtoConsumableTemplate {
    /**
     * 编号
     */
    @Excel(name = "编号",orderNum = "10",width = 24)
    private String codeInStation;

    /**
     * 消耗品名称
     */
    @Excel(name = "消耗品名称(必填)",orderNum = "20",width = 17)
    private String consumableName;

    /**
     * 消耗品类型
     */
    @Excel(name = "消耗品分类(必填)",orderNum = "30",width = 22)
    private String categoryId;

    /**
     * 规格
     */
    @Excel(name = "规格",orderNum = "40",width = 13)
    private String specification;

    /**
     * 等级
     */
    @Excel(name = "等级",orderNum = "50",width = 13)
    private String grade;

    /**
     * 库存数量
     */
    @Excel(name = "库存数量(必填)",orderNum = "60",width = 15)
    private String inventory;

    /**
     * 单位
     */
    @Excel(name = "单位",orderNum = "70",width = 12)
    private String unit;


    /**
     * 提醒人名称
     */
    @Excel(name = "管理员(必填)",orderNum = "80",width = 18)
    private String sendWarnUserName;


    /**
     * 单价
     */
    @Excel(name = "单价",orderNum = "90",width = 15)
    private String unitPrice;


    /**
     * 库存警告数量
     */
    @Excel(name = "库存警告数量",orderNum = "100",width = 14)
    private String warningNum;

    /**
     * 入库时间
     */
    @Excel(name = "入库日期(必填)",format = "yyyy-MM-dd",orderNum = "110",width = 23)
    private String storageDate;

    /**
     * 有效日期
     */
    @Excel(name = "有效日期",format = "yyyy-MM-dd",orderNum = "120",width = 22)
    private String expiryDate;


    /**
     * 存放位置
     */
    @Excel(name = "存放位置",orderNum = "130",width = 22)
    private String keepPlace;

    /**
     * 生产批号
     */
    @Excel(name = "生产批号",orderNum = "140",width = 22)
    private String productionCode;

    /**
     * 生产厂商
     */
    @Excel(name = "生产厂商",orderNum = "150",width = 22)
    private String manufacturerName;

    /**
     * 供应厂商
     */
    @Excel(name = "供应厂商",orderNum = "160",width = 22)
    private String supplierName;


    /**
     * 保存条件
     */
    @Excel(name = "保存条件",orderNum = "170",width = 25)
    private String keepCondition;

    /**
     * 安全须知
     */
    @Excel(name = "安全须知",orderNum = "180",width = 22)
    private String safetyInstruction;

    /**
     * 备注
     */
    @Excel(name = "备注",orderNum = "190",width = 22)
    private String remark;

    /**
     * 是否为标样
     */
    private Boolean isStandard = true;
}
