package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Instrument实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Instrument")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Instrument extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Instrument() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 设备名称
     */
    @Column(length = 50)
    @ApiModelProperty("设备名称")
    @Length(message = "设备名称{validation.message.length}", max = 50)
    private String instrumentName;

    /**
     * 拼音缩写
     */
    @Column(length = 50)
    @ApiModelProperty("拼音缩写")
    private String pinYin;

    /**
     * 全拼
     */
    @Column(length = 100)
    @ApiModelProperty("全拼")
    private String fullPinYin;

    /**
     * 规格型号
     */
    @Column(length = 50)
    @ApiModelProperty("规格型号")
    @Length(message = "规格型号{validation.message.length}", max = 200)
    private String model;

    /**
     * 仪器类型（常量：BASE_InstrumentType）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器类型（常量：BASE_InstrumentType）")
    private String instrumentTypeId;

    /**
     * 量程
     */
    @Column(length = 50)
    @ApiModelProperty("量程")
    @Length(message = "量程{validation.message.length}", max = 200)
    private String insRange;

    /**
     * 准确度等级
     */
    @Column(length = 100)
    @ApiModelProperty("准确度等级")
    @Length(message = "准确度等级{validation.message.length}", max = 100)
    private String nicetyRate;

    /**
     * 仪器价格
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("仪器价格")
    private BigDecimal price;

    /**
     * 所在地
     */
    @Column(length = 100)
    @ApiModelProperty("所在地")
    @Length(message = "所在地{validation.message.length}", max = 100)
    private String place;

    /**
     * 管理人员id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("管理人员id（Guid）")
    private String manager;

    /**
     * 管理员名称（冗余）
     */
    @Column(length = 50)
    @ApiModelProperty("管理员名称（冗余）")
    private String managerName;

    /**
     * 是否出证(是、否)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否出证(是、否)")
    private Boolean isShowOnReport;

    /**
     * 使用条件
     */
    @Column(length = 1000)
    @ApiModelProperty("使用条件")
    @Length(message = "使用条件{validation.message.length}", max = 1000)
    private String useConditions;

    /**
     * 状态(枚举：EnumInstrumentStatus：0报废、1正常、2停用、3过期)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("状态(枚举：EnumInstrumentStatus：0报废、1正常、2停用、3过期)")
    private Integer state;

    /**
     * 本站编号
     */
    @Column(length = 20)
    @ApiModelProperty("本站编号")
    @Length(message = "本站编号{validation.message.length}", max = 50)
    private String instrumentsCode;

    /**
     * 出厂编号
     */
    @Column(length = 100)
    @ApiModelProperty("出厂编号")
    @Length(message = "出厂编号{validation.message.length}", max = 100)
    private String serialNo;

    /**
     * 制造厂商名称
     */
    @Column(length = 100)
    @ApiModelProperty("制造厂商名称")
    @Length(message = "制造厂商名称{validation.message.length}", max = 100)
    private String factoryName;

    /**
     * 供应商
     */
    @Column(length = 100)
    @ApiModelProperty("供应商")
    @Length(message = "供应商{validation.message.length}", max = 100)
    private String saleName;

    /**
     * 购置日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("购置日期")
    private Date purchaseDate;

    /**
     * 所属科室id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属科室id（Guid）")
    private String belongDeptId;

    /**
     * 所属科室名称（冗余）
     */
    @Column(length = 50)
    @ApiModelProperty("所属科室名称（冗余）")
    private String deptName;

    /**
     * 固定资产号
     */
    @Column(length = 20)
    @ApiModelProperty("固定资产号")
    @Length(message = "固定资产号{validation.message.length}", max = 20)
    private String fixedAssetsCode;

    /**
     * 控制措施
     */
    @Column(length = 1000)
    @ApiModelProperty("控制措施")
    @Length(message = "控制措施{validation.message.length}", max = 1000)
    private String controlMeasures;

    /**
     * 使用措施
     */
    @Column(length = 1000)
    @ApiModelProperty("使用措施")
    @Length(message = "使用措施{validation.message.length}", max = 1000)
    private String useMethod;

    /**
     * 维护周期(周)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("维护周期(周)")
    private BigDecimal maintenanceCyc;

    /**
     * 维护内容
     */
    @Column(length = 1000)
    @ApiModelProperty("维护内容")
    @Length(message = "维护内容{validation.message.length}", max = 50)
    private String maintenanceContent;

    /**
     * 最近日期（维护）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最近日期（维护）")
    private Date maintenanceDate;

    /**
     * 核查周期(月)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("核查周期(月)")
    private BigDecimal inspectPeriod;

    /**
     * 核查方法
     */
    @Column(length = 1000)
    @ApiModelProperty("核查方法")
    private String inspectMethod;

    /**
     * 核查结果(枚举：EnumInspectResult：1合格、0不合格)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("核查结果(枚举：EnumInspectResult：1合格、0不合格)")
    private Integer inspectResult;

    /**
     * 最近日期（核查）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最近日期（核查）")
    private Date inspectDate;

    /**
     * 溯源周期(月)
     */
    @Column(nullable = false)
    @ColumnDefault("12")
    @ApiModelProperty("溯源周期(月)")
    private BigDecimal originCyc;

    /**
     * 溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)")
    private Integer originType;

    /**
     * 溯源单位
     */
    @Column(length = 100)
    @ApiModelProperty("溯源单位")
    @Length(message = "溯源单位{validation.message.length}", max = 100)
    private String originUnit;

    /**
     * 最近日期（溯源）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最近日期（溯源）")
    private Date originDate;

    /**
     * 过期日期（溯源）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("过期日期（溯源）")
    private Date originEndDate;

    /**
     * 溯源结果(枚举：EnumOriginResult：1合格、0不合格)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("溯源结果(枚举：EnumOriginResult：1合格、0不合格)")
    private Integer originResult;

    /**
     * 溯源备注
     */
    @Column(length = 1000)
    @ApiModelProperty("溯源备注")
    @Length(message = "溯源备注{validation.message.length}", max = 1000)
    private String originRemark;

    /**
     * 颜色(玻璃仪器特有)
     */
    @Column(length = 100)
    @ApiModelProperty("颜色(玻璃仪器特有)")
    @Length(message = "颜色(玻璃仪器特有){validation.message.length}", max = 100)
    private String instrColor;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 最近开启日期，与最近使用日期、最近检定日期不同
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最近开启日期，与最近使用日期、最近检定日期不同")
    private Date recentOpenDate;

    /**
     * 设备开启阀值，即设备在该时长内没有启动则不允许使用，单位是天
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("设备开启阀值，即设备在该时长内没有启动则不允许使用，单位是天")
    private double alarmThreshold;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 图片路径
     */
    @Column(length = 100)
    @ApiModelProperty("图片路径")
    private String photoUrl;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 出厂日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("出厂日期")
    private Date productDate;


    /**
     * 国别
     */
    @Column(length = 50)
    @ApiModelProperty("国别")
    @Length(message = "国别{validation.message.length}", max = 50)
    private String productCountry;

    /**
     * 合同号
     */
    @Column(length = 50)
    @ApiModelProperty("合同号")
    @Length(message = "合同号{validation.message.length}", max = 50)
    private String constractNo;

    /**
     * 报废时间
     */
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("报废时间")
    private Date scrapTime;

    /**
     * 是否核查
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否核查")
    private Boolean isInspected;

    /**
     * 核查方式
     */
    @Column(length = 50)
    @ApiModelProperty("核查方式")
    @Length(message = "核查方式{validation.message.length}", max = 50)
    private String inspectMode;

    /**
     * 核查原因
     */
    @Column(length = 200)
    @ApiModelProperty("核查原因")
    @Length(message = "核查原因{validation.message.length}", max = 200)
    private String inspectReason;

    /**
     * 责任科室
     */
    @Column(length = 50)
    @ApiModelProperty("责任科室")
    @Length(message = "责任科室{validation.message.length}", max = 50)
    private String responsibleOffice;

    /**
     * 是否共享仪器
     */
    @ApiModelProperty("是否共享仪器")
    private Boolean shareFlag;

    /**
     * 是否辅助仪器
     */
    @ApiModelProperty("是否辅助仪器")
    private Boolean isAssistInstrument;

    /**
     * 监管平台仪器名称
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台仪器名称")
    private String regulateName;

    /**
     * 监管平台仪器id
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台仪器id")
    private String regulateId;

    /**
     * 仪器排序值
     */
    @Column
    @ApiModelProperty("仪器排序值")
    private Integer orderNum;


    /**
     * 授权使用人，多个用逗号隔开
     */
    @ApiModelProperty("授权使用人，多个用逗号隔开")
    @Length(message = "授权使用人{validation.message.length}", max = 1000)
    private String authorizedPersons;

    /**
     * 检定技术指标
     */
    @ApiModelProperty("检定技术指标")
    @Length(message = "检定技术指标{validation.message.length}", max = 255)
    private String technicalSpecification;

    /**
     * 确认依据
     */
    @ApiModelProperty("确认依据")
    @Length(message = "确认依据{validation.message.length}", max = 255)
    private String confirmationBasis;
}