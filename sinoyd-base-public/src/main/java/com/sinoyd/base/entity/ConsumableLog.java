package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * ConsumableLog实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ConsumableLog")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ConsumableLog extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  ConsumableLog() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 领料单Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("领料单Id")
	private String consumablePickId;
    
    /**
    * 领用单号
    */
    @Column(length=50)
    @ApiModelProperty("领用单号")
    @Length(message = "领用单号{validation.message.length}", max = 50)
	private String pickNum;
    
    /**
    * 领用人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("领用人")
	private String userId;
    
    /**
    * 领用数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("领用数量")
	private BigDecimal amount;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 领用结存
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("领用结存")
	private BigDecimal balance;
    
    /**
    * 领用时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("领用时间")
	private Date occurrenceTime;
    
    /**
    * 领用批次Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("领用批次Id")
	private String consumableDetailId;
    
    /**
    * 消耗品Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("消耗品Id")
    private String consumableId;
    
    /**
    * 领用人名字
    */
    @Column(length=50)
    @ApiModelProperty("领用人名字")
	private String consumingPersonsName;
    
    /**
    * （3.2预留）领用类型(枚举EnumPickType：0.领用1.盘库)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("（3.2预留）领用类型(枚举EnumPickType：0.领用1.盘库)")
	private Integer pickTypeId;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }