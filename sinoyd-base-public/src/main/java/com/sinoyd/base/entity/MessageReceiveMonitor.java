package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * 消费端消息监控表实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/4/16
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "MessageReceiveMonitor")
@Data
@EntityListeners(AuditingEntityListener.class)
public class MessageReceiveMonitor {

    private static final long serialVersionUID = 1L;

    public MessageReceiveMonitor() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    @Column(nullable=false)
    private String id = UUIDHelper.NewID();

    /**
     * 消息内容
     */
    @Column(nullable=false)
    @ApiModelProperty("消息内容")
    private String message;

    /**
     * 交换机
     */
    @ApiModelProperty("交换机")
    private String exchange;

    /**
     * 路由键
     */
    @ApiModelProperty("路由键")
    private String routingKey;

    /**
     * 队列
     */
    @ApiModelProperty("队列")
    private String queue;

    /**
     * 是否发送成功
     */
    @Column(nullable=false)
    @ColumnDefault("b'0'")
    @ApiModelProperty("是否发送成功")
    private Boolean isSuccess;

    /**
     * 是否发送成功
     */
    @Column(nullable=false)
    @ColumnDefault("2")
    @ApiModelProperty("是否发送成功")
    private Integer sendStatus;

    /**
     * 原因
     */
    @Column(nullable=false)
    @ApiModelProperty("原因")
    private String reason;

    /**
     * 所属机构ID
     */
    @Column(nullable=false)
    @ApiModelProperty("所属机构ID")
    @ColumnDefault("'********-0000-0000-0000-************'")
    private String orgId;

    /**
     * 所属实验室ID
     */
    @Column(nullable=false)
    @ApiModelProperty("所属实验室ID")
    @ColumnDefault("'********-0000-0000-0000-************'")
    private String domainId;

    /**
     * 创建人
     */
    @Column(nullable=false)
    @ApiModelProperty("创建人")
    @ColumnDefault("'********-0000-0000-0000-************'")
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ApiModelProperty("创建时间")
    @ColumnDefault("getdate")
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @Column(nullable=false)
    @ApiModelProperty("更新人")
    @ColumnDefault("'********-0000-0000-0000-************'")
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @Column(nullable=false)
    @ApiModelProperty("更新时间")
    @ColumnDefault("getdate")
    @LastModifiedDate
    private Date modifyDate;


    @PrePersist
    public void prePersist() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "system";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "system";
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

}
