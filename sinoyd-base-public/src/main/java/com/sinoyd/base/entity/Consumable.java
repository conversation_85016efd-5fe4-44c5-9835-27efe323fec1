package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Consumable实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Consumable")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Consumable extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Consumable() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 名称
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("名称")
    @Length(message = "消耗品名称{validation.message.length}", max = 50)
    private String consumableName;

    /**
     * 拼音
     */
    @Column(length = 50)
    @ApiModelProperty("拼音")
    private String pinYin;

    /**
     * 全拼
     */
    @Column(length = 100)
    @ApiModelProperty("全拼")
    private String fullPinYin;

    /**
     * 编号（本站编号）
     */
    @Column(length = 50)
    @ApiModelProperty("编号（本站编号）")
    @Length(message = "本站编号{validation.message.length}", max = 50)
    private String codeInStation;

    /**
     * 标样编号
     */
    @Column(length = 50)
    @ApiModelProperty("标样编号")
    @Length(message = "标样编号{validation.message.length}", max = 40)
    private String consumableCode;

    /**
     * 规格
     */
    @Column(length = 50)
    @ApiModelProperty("规格")
    @Length(message = "规格{validation.message.length}", max = 50)
    private String specification;

    /**
     * 单位名称
     */
    @Column(length = 50)
    @ApiModelProperty("单位名称")
    @Length(message = "单位名称{validation.message.length}", max = 50)
    private String unit;

    /**
     * 单位Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("单位Id")
    @Length(message = "单位Id{validation.message.length}", max = 50)
    private String unitId;

    /**
     * 等级常量（Guid）（LIM_ConsumableGrade:进口、分析纯、FMP、高纯等）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("等级常量（Guid）（LIM_ConsumableGrade:进口、分析纯、FMP、高纯等）")
    @Length(message = "等级{validation.message.length}", max = 50)
    private String grade;

    /**
     * 类别常量（Guid）（LIM_ConsumableCategory:高压气体、易制毒品、化学试剂等）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("类别常量（Guid）（LIM_ConsumableCategory:高压气体、易制毒品、化学试剂等）")
    @Length(message = "类别{validation.message.length}", max = 50)
    private String categoryId;

    /**
     * 警告数量
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("警告数量")
    private BigDecimal warningNum;

    /**
     * 提醒人id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("提醒人id（Guid）")
    @Length(message = "提醒人id{validation.message.length}", max = 50)
    private String sendWarnUserId;

    /**
     * 别名
     */
    @ApiModelProperty("别名")
    @Length(message = "别名{validation.message.length}", max = 255)
    private String alias;

    /**
     * 是否易制毒
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否易制毒")
    private Boolean isPoison;

    /**
     * 保存条件
     */
    @Column(length = 1000)
    @ApiModelProperty("保存条件")
    @Length(message = "保存条件{validation.message.length}", max = 1000)
    private String keepCondition;

    /**
     * 安全须知
     */
    @Column(length = 1000)
    @ApiModelProperty("安全须知")
    @Length(message = "安全须知{validation.message.length}", max = 1000)
    private String safetyInstruction;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 是否标样（0代表消耗品，1代表标样）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否标样（0代表消耗品，1代表标样）")
    private Boolean isStandard;

    /**
     * 稀释液
     */
    @ApiModelProperty("稀释液")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String dilutedSolution;

    /**
     * 稀释方法
     */
    @ApiModelProperty("稀释方法")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String dilutionMethod;

    /**
     * 浓度
     */
    @ApiModelProperty("浓度")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String concentration;

    /**
     * 不确定度
     */
    @ApiModelProperty("不确定度")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String uncertainty;

    /**
     * 不确定度类型
     */
    @ApiModelProperty("不确定度类型")
    private Integer uncertainType;

    /**
     * 是否混标（0代表否  1代表是）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否混标（0代表否  1代表是）")
    private Boolean isMixedStandard;

    /**
     * 是否实验室加密
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否实验室加密")
    private Boolean isLabEncryption;

    /**
     * 库存数量
     */
    @ApiModelProperty("库存数量")
    private BigDecimal inventory;

    /**
     * （预留3.2）提醒时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("（预留3.2）提醒时间")
    private Date sendWarnTime;

    /**
     * （预留3.2）标值
     */
    @Column(length = 50)
    @ApiModelProperty("（预留3.2）标值")
    private String standard;

    /**
     * （预留3.2）国家标准
     */
    @Column(length = 100)
    @ApiModelProperty("（预留3.2）国家标准")
    private String nationalStandard;

    /**
     * （预留3.2）定容介质
     */
    @Column(length = 100)
    @ApiModelProperty("（预留3.2）定容介质")
    private String constantVolumeM;

    /**
     * （预留3.2）分子量
     */
    @Column(length = 100)
    @ApiModelProperty("（预留3.2）分子量")
    private String molecularWeight;

    /**
     * （预留3.2）分子式
     */
    @Column(length = 100)
    @ApiModelProperty("（预留3.2）分子式")
    private String molecularFormula;

    /**
     * （预留3.2）使用方法
     */
    @ApiModelProperty("（预留3.2）使用方法")
    private String useWay;

    /**
     * 量纲id
     */
    @Column(length = 50)
    @ApiModelProperty("量纲id")
    @Length(message = "量纲Id{validation.message.length}", max = 50)
    private String dimensionId;

    /**
     * 量纲名称
     */
    @Column(length = 50)
    @ApiModelProperty("量纲名称")
    private String dimensionName;

    /**
     * （预留3.2）密度
     */
    @Column(length = 100)
    @ApiModelProperty("（预留3.2）密度")
    private String density;

    /**
     * 范围低点
     */
    @Column(length = 50)
    @ApiModelProperty("范围低点")
    private String rangeLow;

    /**
     * 范围高点
     */
    @Column(length = 50)
    @ApiModelProperty("范围高点")
    private String rangeHigh;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}