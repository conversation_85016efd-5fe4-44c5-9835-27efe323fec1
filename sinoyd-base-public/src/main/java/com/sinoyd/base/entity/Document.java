package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * Document实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Document")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Document extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Document() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 文件夹Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("文件夹Id（Guid）")
    private String folderId;

    /**
     * 文件夹名称
     */
    @ApiModelProperty("文件夹名称")
    private String folderName;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    @Length(message = "文件名称{validation.message.length}", max = 255)
    private String filename;

    /**
     * 物理文件名
     */
    @ApiModelProperty("物理文件名")
    @Length(message = "物理文件名{validation.message.length}", max = 255)
    private String physicalName;

    /**
     * 文件路径
     */
    @Column(length = 500)
    @ApiModelProperty("文件路径")
    private String path;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 是否副本
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否副本")
    private Boolean isTranscript;

    /**
     * 文件类型（常量BASE_DocumentExtendType 用于pro项目附件）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("文件类型（常量BASE_DocumentExtendType 用于pro项目附件）")
    private String docTypeId;

    /**
     * 文件类型名称
     */
    @ApiModelProperty("文件类型名称")
    private String docTypeName;

    /**
     * 文件大小
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("文件大小")
    private Integer docSize;

    /**
     * 文件后缀
     */
    @Column(length = 10)
    @ApiModelProperty("文件后缀")
    private String docSuffix;

    /**
     * 下载次数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("下载次数")
    private Integer downloadTimes;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 上传人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("上传人Id")
    private String uploadPersonId;

    /**
     * 上传人姓名
     */
    @Column(length = 50)
    @ApiModelProperty("上传人姓名")
    private String uploadPerson;

    /**
     *
     */
    @Column(nullable = false)
    @ColumnDefault("user_name")
    @ApiModelProperty("")
    private Boolean isStick;

    /**
     * 是否系统绘制
     */
    @Column(nullable = false)
    @ApiModelProperty("是否系统绘制")
    private Boolean isSystemDraw;

    /**
     * 附件流水号
     */
    @Column(nullable = false)
    @ApiModelProperty("附件流水号")
    private Integer serialNumber;

    /**
     * 电子签名状态 （0：未推送 1：未签  2：已签）
     */
    @Column(nullable = false)
    @ApiModelProperty("电子签名状态 （0：未推送 1：未签  2：已签）")
    private Integer signStatus;

    /**
     * 上海监管平台附件ID
     */
    @Column(length = 50)
    @ApiModelProperty("上海监管平台附件ID")
    private String shDocumentId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}