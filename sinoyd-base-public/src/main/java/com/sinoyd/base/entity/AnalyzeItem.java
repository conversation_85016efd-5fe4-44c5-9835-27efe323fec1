package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * AnalyzeItem实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "AnalyzeItem")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AnalyzeItem implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AnalyzeItem() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 名称
     */
    @Column(length = 50)
    @ApiModelProperty("名称")
    @NotBlank(message = "分析项目名称{validation.message.blank}")
    @Length(message = "分析项目名称{validation.message.length}", max = 50)
    private String analyzeItemName;

    /**
     * 分析因子编号
     */
    @Column(length = 50)
    @ApiModelProperty("分析因子编号")
    @Length(message = "分析项目编号{validation.message.length}", max = 50)
    private String analyzeItemCode;

    /**
     * 变量名称（预留，前台改为别名）
     */
    @Column(length = 50)
    @ApiModelProperty("变量名称（预留，前台改为别名）")
    @Length(message = "分析项目别名{validation.message.length}", max = 50)
    private String variableName;

    /**
     * 拼音缩写
     */
    @Column(length = 50)
    @ApiModelProperty("拼音缩写")
    private String pinYin;

    /**
     * 全拼
     */
    @Column(length = 100)
    @ApiModelProperty("全拼")
    private String fullPinYin;

    /**
     * CAS号
     */
    @Column(length = 50)
    @ApiModelProperty("CAS号")
    @Length(message = "CAS号{validation.message.length}", max = 50)
    private String casNum;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    @PrePersist
    public void prePersist() {
        this.creator = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }

    @PreUpdate
    public void preUpdate() {
        this.modifier = PrincipalContextUser.getPrincipal() == null ? "system" : PrincipalContextUser.getPrincipal().getUserId();
    }
}