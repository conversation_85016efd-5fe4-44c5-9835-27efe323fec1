FROM registry.dev.yd/sinoyd/registry/maven:3.8.4-openjdk-8

WORKDIR /application
ADD application.jar /application/

ENV JAVA_OPTS=""
ENTRYPOINT ["java", "-server", "-Duser.timezone=GMT+08", "-Xms4096m","-Xmx4096m","-XX:CompressedClassSpaceSize=2048m","-XX:+UseCompressedClassPointers","-XX:MetaspaceSize=128m","-XX:MaxMetaspaceSize=512m", "-XX:MaxNewSize=512m","-XX:MaxMetaspaceSize=1024m", "-XX:+UseG1GC","-Djava.security.egd=file:/dev/./urandom", "-Dfile.encoding=utf-8","-jar","/application/application.jar"]

# Service listens on port 80
EXPOSE 80
