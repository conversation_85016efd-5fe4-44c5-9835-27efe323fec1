package com.sinoyd.lims.strategy.context;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.vo.RegulatoryPlatformPushVO;

import java.util.List;
import java.util.Map;

/**
 * 上海监管平台远程策略上下文接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/07
 */
@SuppressWarnings("rawtypes")
public interface IRegulatoryPlatformContextService {

    /**
     * 通用请求接口
     *
     * @param methodName    请求方法名
     * @param requestParams 请求参数
     * @return 请求结果
     */
    JSONObject doRequest(String methodName, Map<String, Object> requestParams);

    /**
     * 查询分页数据
     *
     * @param criteria   查询条件
     * @param pageBean   分页数据
     * @param methodName 方法名称
     * @return 分页数据
     */
    List findByPage(BaseCriteria criteria, PageBean pageBean, String methodName);

    /**
     * 监管平台推送
     *
     * @param pushVo     推送参数VO
     */
    void push(RegulatoryPlatformPushVO pushVo);
}
