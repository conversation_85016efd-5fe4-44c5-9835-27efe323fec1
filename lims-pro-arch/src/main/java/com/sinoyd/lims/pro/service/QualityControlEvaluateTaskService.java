package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoQualityControl;
import com.sinoyd.lims.pro.dto.DtoQualityControlEvaluate;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseQualityControlData;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 质控评价相关业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/6
 */
public interface QualityControlEvaluateTaskService {

    Future<String> dealEvaluateData(List<DtoQualityControlEvaluate> evaluateList, List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                    List<DtoQualityControlLimit> qualityControlLimitList, String workSheetFolderId, Map<String, String> anaId2QcInfoMap,
                                    List<DtoTest> testList, Map<String, DtoQualityControl> qcMap, Map<String, DtoAnalyseData> anaDataMap, Boolean configValue,
                                    UsernamePasswordAuthenticationToken token);

}
