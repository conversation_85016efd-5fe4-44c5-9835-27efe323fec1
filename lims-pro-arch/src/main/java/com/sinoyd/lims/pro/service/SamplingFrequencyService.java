package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * SamplingFrequency操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SamplingFrequencyService extends IBaseJpaService<DtoSamplingFrequency, String> {
    /**
     * 获取采样准备点位列表
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param key          关键字
     * @param status       采样状态
     */
    List<Object> findPrepareSamplingFrequency(String projectId, String sampleTypeId, String key, Integer status);

    /**
     * 删除对应点位下的频次
     *
     * @param sampleFolderId 点位id
     * @return 删除的条数
     */
    Integer deleteBySampleFolderId(String sampleFolderId);

    /**
     * 删除对应点位下的频次
     *
     * @param sampleFolderIds 点位id列表
     * @return 删除的条数
     */
    Integer deleteBySampleFolderIdIn(List<String> sampleFolderIds);

    /**
     * @param id             频次id
     * @param sampleFolderId 点位id
     * @return 返回更新信息
     */
    Integer updateSampleFolderId(String id,String sampleFolderId);

    /**
     * 同步点位周期数据
     * @param entityList   待处理数据
     * @param isDelete     是否为删除
     */
    void syncFolderPeriod(List<DtoSamplingFrequency> entityList,boolean isDelete);
}