package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoReportBaseInfo;
import com.sinoyd.lims.pro.dto.customer.DtoReportInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 电子报告相关操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
public interface ElectronicReportService extends IBaseJpaService<DtoReportBaseInfo, String> {

    /**
     * 保存电子报告信息
     *
     * @param reportInfo 对象id
     */
    void saveReportInfo(DtoReportInfo reportInfo);

    /**
     * 自动获取电子报告信息
     *
     * @param reportId 报告id
     * @return 电子报告信息
     */
    DtoReportInfo autoGenerate(String reportId);

    /**
     * 查询电子报告信息
     *
     * @param reportId 报告id
     * @return 电子报告信息
     */
    DtoReportInfo queryReportInfo(String reportId);

    /**
     * 获取报告所有点位并按照给定的点位排序规则进行排序
     *
     * @param reportId           报告id
     * @param sampleFolderSortId 点位排序id
     * @return 点位信息
     */
    List<Map<String, String>> getSortFolderForReport(String reportId, String sampleFolderSortId);

    /**
     * 清空报告信息
     *
     * @param reportId           报告id
     */
    void clearReportInfo(String reportId);

    /**
     * 上传报告点位示意图
     *
     * @param request         请求体
     * @param allowSuffixList 允许上传的文件类型
     * @return 点位示意图文档对象
     */
    List<DtoDocument> uploadFolderSketch(HttpServletRequest request, List<String> allowSuffixList);

    /**
     * 上传报告
     *
     * @param request 请求体
     * @return 报告文档对象
     */
    DtoDocument uploadReport(HttpServletRequest request);

    /**
     * 下载报告
     *
     * @param reportId 请求体
     * @param response 响应对象
     * @return 报告文档对象
     */
    String downloadReport(String reportId, Boolean isCopy,HttpServletResponse response) throws IOException;

    /**
     * 查询报告技术备注说明
     *
     * @return ConfigModel
     */
    ConfigModel findTechnicalRemark();

    /**
     * 查询报告总称测试项目
     *
     * @param reportId 报告id
     * @return 总称测试项目列表
     */
    List<DtoTest> findReportMergeTest(String reportId);

    /**
     * 获取下载的报告附件名称
     *
     * @param reportId 报告id
     * @param isCopy   是否副本
     * @return 返回数据
     */
    String getDocName(String reportId, Boolean isCopy);

}