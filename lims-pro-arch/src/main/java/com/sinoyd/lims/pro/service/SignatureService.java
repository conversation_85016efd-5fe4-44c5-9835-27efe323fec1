package com.sinoyd.lims.pro.service;

import com.aspose.words.DocumentBuilder;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.customer.DtoSigDocument;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 表单签名
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface SignatureService {
    /**
     * 报表签名
     *
     * @param repId      报表id
     * @param personIds  签名人员id
     * @param personType 签名类型
     */
    void sig(String repId, List<String> personIds, Integer personType, String docTypeId, String timeStr) throws Exception;

    /**
     * 报表签名
     *
     * @param repId      报表id
     * @param personIds  签名人员id
     * @param personType 签名类型
     * @param docTypeId  文件类型
     * @param timeStr    日期
     * @param isXC       是否包含现场
     * @throws Exception
     */
    void sig(String repId, List<String> personIds, Integer personType, String docTypeId, String timeStr, Boolean isXC) throws Exception;

    /**
     * 报表签名
     *
     * @param repId      报表id
     * @param docIds     附件ids
     * @param personIds  签名人员id
     * @param personType 签名类型
     * @param docTypeId  文件类型
     * @param timeStr    日期
     * @throws Exception
     */
    void sig(String repId, List<String> docIds, List<String> personIds, Integer personType, String docTypeId, String timeStr);

    /**
     * 删除副本（退回时需要删除副本）
     *
     * @param folderId 关联id
     */
    void deleteDoc(String folderId);

    /**
     * @param repId
     * @param mapType
     */
    void removeWorkSheetSig(String repId, Map<String, Integer> mapType);

    /**
     * 下载签名
     *
     * @param docPath    文件路径
     * @param sigDocList 匹配内容
     * @throws Exception 报错信息
     */
    void sigByDocument(String docPath, List<DtoSigDocument> sigDocList) throws Exception;

    /**
     * 报表签名
     *
     * @param dtoReportConfig 报表类型
     * @param params          参数
     * @param oldPath         路径
     * @throws Exception 报错信息
     */
    void sigDocument(DtoReportConfig dtoReportConfig, List<Object> params, String oldPath) throws Exception;

    /**
     * 报告签章并转换为pdf格式
     *
     * @param reportId 报告id
     */
    void sealChangePdf(String reportId);

    /**
     * 设置签发时需要展示的相关信息（监管平台系统编号，电子报告副本备注）
     *
     * @param builder       aspose文档对象
     * @param reportId     报告id
     */
    void setSignInfo(DocumentBuilder builder, String reportId);

    /**
     * 报告签章
     *
     * @param pathList  需要签章的文档路径数据
     * @param report    报告数据
     * @param isSicCma  是否签CMA章
     * @param isSicCnas 是否签CNAS章
     * @param isSicTest 是否签监测专用章
     * @param isOutput  是否为生成时签章
     */
    void seal(List<String> pathList, DtoReport report, boolean isSicCma, boolean isSicCnas, boolean isSicTest, boolean isOutput);

    /**
     * 报表盖章
     *
     * @param documentList 报告文档列表
     * @param report       报告对象
     * @param isSicCma     是否签CMA章
     */
    void seal(List<DtoDocument> documentList, DtoReport report, boolean isSicCma);

    /**
     * 报告添加点位示意图
     *
     * @param params   报表参数
     * @param filePath 报表文件路径
     */
    void addPointPicForReport(List<Object> params, String filePath);

    /**
     * 报告添加系统logo
     *
     * @param params   报表参数
     * @param filePath 报表文件路径
     */
    void addSystemLogoForReport(List<Object> params, String filePath);

    /**
     * 根据文件和签名地址Excel签名
     *
     * @param document      附件集合
     * @param personType    签名类型
     * @param isDown        是否向下签名
     * @param multipartFile 签名图片
     * @throws Exception 异常
     */
    void sigExcel(DtoDocument document, String personType, Boolean isDown, MultipartFile multipartFile) throws Exception;

    /**
     * 根据送样单id 签名采样单
     *
     * @param docPath   附件路径
     * @param receiveId 送样单id
     */
    void sigExcelByReceiveId(String docPath, String receiveId);

    /**
     * 根据附件清空签名
     *
     * @param document 附件
     * @param mapType  签名类型Map
     */
    void removeSignatureByDoc(DtoDocument document, Map<String, Integer> mapType);
}
