package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.customer.DtoLoadScheme;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 方案操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/06/11
 * @since V100R001
 */
public interface SchemeService {
    /**
     * 复制外部送样单下的样品数据
     *
     * @param sourceProjectId 源项目id
     * @param sourceReceiveId 源送样单id
     * @param targetRecord    目标送样单
     * @param targetProject   目标项目
     */
    void copyOutsideProjectSample(String sourceProjectId, String sourceReceiveId, DtoReceiveSampleRecord targetRecord, DtoProject targetProject);

    /**
     * 复制方案
     *
     * @param sourceProjectId 源项目id
     * @param targetProjectId 目标项目id
     */
    void copyScheme(String sourceProjectId, String targetProjectId);

    /**
     * 复制外部送样样品
     *
     * @param ids               样品id集合
     * @param receiveId         送样单id
     * @param copyTimes         复制次数
     * @param samplingTimeBegin 采样日期
     */
    void copyOutsideSample(List<String> ids, String receiveId, Integer copyTimes, Date samplingTimeBegin);

    /**
     * 删除外部送样单下的样品
     *
     * @param ids       样品id
     * @param receiveId 送样单id
     */
    void deleteOutsideSample(List<String> ids, String receiveId);

    /**
     * 删除外部送样单下的样品(会清空样品编号)
     *
     * @param ids       样品id
     * @param receiveId 送样单id
     */
    void deleteOutsideSampleClearSampleCode(List<String> ids, String receiveId);

    /**
     * 新增点位
     *
     * @param sampleFolder 点位
     * @param testList     点位测试项目
     * @return 点位
     */
    DtoLoadScheme addSchemeFolder(DtoSampleFolder sampleFolder, List<DtoTest> testList);

    /**
     * 复制点位
     *
     * @param ids       点位id集合
     * @param copyTimes 复制次数
     */
    DtoLoadScheme copySchemeSampleFolder(List<String> ids, Integer copyTimes, Boolean isApi);

    /**
     * 添加点位周期频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 点位周期频次
     */
    DtoLoadScheme addSchemePeriodTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    /**
     * 添加方案次数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 点位周期频次
     */
    DtoLoadScheme addSchemeTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    /**
     * 添加方案次数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 点位周期频次
     */
    DtoLoadScheme addSchemeSamplePeriods(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    /**
     * 复制周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param copyTimes      复制次数
     */
    DtoLoadScheme copySchemePeriod(String sampleFolderId, Integer periodCount, Integer copyTimes);

    /**
     * 复制周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     * @param copyTimes      复制次数
     * @return 数据
     */
    DtoLoadScheme copySchemeTimePeriod(String sampleFolderId, Integer periodCount,Integer timePeriod, Integer copyTimes);

    /**
     * 复制次数
     *
     * @param sampleFolderId      点位id
     * @param samplingFrequencyId 频次id
     * @param copyTimes           复制次数
     */
    DtoLoadScheme copySchemeSamplingFrequency(String sampleFolderId, String samplingFrequencyId, Integer copyTimes);

    /**
     * 根据点位id删除样品信息
     *
     * @param projectId      项目id
     * @param sampleFolderId 点位id
     */
    void deleteSampleFolder(String projectId, String sampleFolderId);

    /**
     * 根据频次id删除样品信息
     *
     * @param projectId            项目id
     * @param samplingFrequencyIds 频次id集合
     */
    void deleteSamplingFrequency(String projectId, List<String> samplingFrequencyIds);

    /**
     * 删除项目下的质控样
     *
     * @param projectId 项目id
     * @param sampleIds 质控样id
     */
    void deleteQCSample(String projectId, List<String> sampleIds);

    /**
     * 删除样品
     *
     * @param projectId 项目id
     * @param recMap    数据集合
     */
    void deleteSample(String projectId, Map<String, List<String>> recMap);

    /**
     * 提交方案
     *
     * @param dtoWorkflowSign 工作流实体
     */
    void submitScheme(DtoWorkflowSign dtoWorkflowSign);

    /**
     * 获取样品点位名称
     *
     * @param sampleFolder  点位
     * @param periodCount   周期
     * @param timePerPeriod 次数
     * @param samplePeriod  样品数
     * @return 返回样品点位名称
     */
    String getFolderName(DtoSampleFolder sampleFolder, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    void persistLoadScheme(DtoLoadScheme load);

    /**
     * 获取数据的拷贝
     *
     * @param sourceAnalyseData 源数据
     * @return 拷贝数据
     */
    DtoAnalyseData getSchemeCloneAnalyseData(DtoAnalyseData sourceAnalyseData);

    /**
     * 剔除质控样时，判断数据是否加入加测但并提交
     * @param qcSampleIds
     */
    void checkQcAnalyseDataStatus(List<String> qcSampleIds);
}
