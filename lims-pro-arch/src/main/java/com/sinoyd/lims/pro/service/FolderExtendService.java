package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.vo.TestShMethodUpdateBatchVO;

import java.util.List;
import java.util.Map;

/**
 * 测试项目关联监管平台信息service
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/28
 */
public interface FolderExtendService {

    /**
     * 批量修改分析方法
     *
     * @param batchVO 参数
     */
    void batchUpdateAnalyzeMethod(TestShMethodUpdateBatchVO batchVO);

    /**
     * 批量修改采样方法
     *
     * @param batchVO 参数
     */
    void batchUpdateSamplingMethod(TestShMethodUpdateBatchVO batchVO);

    /**
     * 根据项目id查询点位
     *
     * @param projectId 项目id
     * @param sampleTypeId 检测类型id
     * @return List<DtoSampleFolder>
     */
    List<DtoSampleFolder> findFoldersByProjectId(String projectId, String sampleTypeId);

    /**
     * 根据点位id查询改点位下所有测试项目配置
     *
     * @param criteria 条件
     * @return List<DtoFolderExtend>
     */
    List<DtoTest> findBySampleFolderId(BaseCriteria criteria);

    /**
     * 查询点位测试项目分组数据
     *
     * @param criteria 测试项目查询条件
     * @return 点位测试项目分组 k: 点位id v: 点位下测试项目集合
     */
    Map<String, List<DtoTest>> findSampleFolderGroupTest(BaseCriteria criteria);

    /**
     * 根据项目id查询该项目下所有测试项目
     *
     * @param projectId 项目id
     * @return List<DtoTest>
     */
    List<DtoTest> findByProjectId(String projectId);

    /**
     * 匹配测试项目，处理测试项目匹配状态
     *
     * @param testList 测试项目数据
     */
    void matchRpTestStatus(List<DtoTest> testList);

}
