package com.sinoyd.lims.pro.service;

import java.util.List;
import java.util.Map;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoDetailAnalyseData;
import com.sinoyd.lims.pro.dto.DtoDetailData;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.lims.pro.dto.customer.DtoDetailDataColumn;
import com.sinoyd.lims.pro.dto.customer.DtoMonitorData;

/**
 * 详细数据操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
public interface DetailDataService  extends IBaseJpaService<DtoDetailData, String> {
    /**
     * 根据样品id集合返回详细数据
     *
     * @param sampleIds 样品id集合
     */
    DtoMonitorData getBySampleIds(List<String> sampleIds);

    /**
     * 根据样品id集合返回详细数据
     * 并根据分析项目排序
     *
     * @param baseCriteria 查询参数
     */
    DtoMonitorData getBySampleIdSort(BaseCriteria baseCriteria);

    /**
     * 分页查询详细数据
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     * @return 返回详细数据
     */
    Map<String, Object> findDetailDataByPage(PageBean<Object[]> pageBean, BaseCriteria baseCriteria);


    /**
     * 获取项目上的详细数据
     *
     * @param baseCriteria 查询条件
     * @return 返回详细数据
     */
    Map<String, Object> findProjectDetailData(BaseCriteria baseCriteria);


    /**
     * 获取详细数据的列
     *
     * @param testIds       测试项目ids
     * @param sort          排序方式
     * @param paramsConfigs 参数对象
     * @param paramsColumns 参数列
     * @param testColumns   测试项目列
     * @return 返回详细数据的列
     */
    List<DtoDetailDataColumn> getDetailDataColumns(List<String> testIds, String sort,
                                                   List<DtoParamsConfig> paramsConfigs, List<DtoDetailDataColumn> paramsColumns,
                                                   List<DtoDetailDataColumn> testColumns, List<DtoParamsData> paramsDataList, List<DtoAnalyseData> analyseDataList
    );


    /**
     * 根据detailDataId集合查询所有的测试项目数据
     *
     * @param detailDataIds 样品id集合
     * @return 测试项目数据
     */
    List<DtoDetailAnalyseData> getDetailAnalyseDataByIds(List<String> detailDataIds);
}