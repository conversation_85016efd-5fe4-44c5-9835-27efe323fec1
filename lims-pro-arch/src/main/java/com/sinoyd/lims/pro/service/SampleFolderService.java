package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.dto.customer.DtoMethodStandardSample;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 点位操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface SampleFolderService extends IBaseJpaService<DtoSampleFolder, String> {

    //#region 方案

    /**
     * 根据测试项目返回样品列表
     *
     * @param testId       选中的测试项目编号
     * @param projectId    项目编号
     * @param sampleTypeId 样品类型
     * @param isLoad       是否缓存
     * @return 关联样品
     */
    List<DtoMethodStandardSample> findSample(String testId, String projectId, String sampleTypeId, Boolean isLoad);


    /**
     * 根据样品修改测试项目方法
     *
     * @param sampleIds
     * @return
     */
    List<DtoProjectTest> changeAnalyzeMethodBySample(List<String> sampleIds, String testId, String newAnalyzeMethodId, Boolean isChangeAll);

    /**
     * 获取方法标准页面数据
     *
     * @param projectId            项目编号
     * @param sampleTypeId         样品类型编号
     * @param redAnalyzeItemName   分析项目名称
     * @param redAnalyzeMethodName 分析方法
     * @param isLoad               是否缓存
     * @return 测试项目数据
     */
    List<DtoProjectTest> findMethodStandard(String projectId, String sampleTypeId, String redAnalyzeItemName, String redAnalyzeMethodName, Boolean isLoad);

    /**
     * 点位明细
     *
     * @param id 点位id
     * @return 点位明细
     */
    DtoSampleFolderTemp findDetail(String id);


    /**
     * 查询项目对应检测类型下的方案
     * @param projectId 项目id
     * @param sampleTypeId 检测类型id
     * @param isLoad 是否加载到redis
     * @param redAnalyzeItemName 分析项目名称
     * @param redAnalyzeMethodName 分析方法名称
     * @param isShowTest 是否测试项目
     * @return 方案
     */
    DtoProjectScheme findScheme(String projectId, String sampleTypeId, Boolean isLoad, String redAnalyzeItemName, String redAnalyzeMethodName, Boolean isShowTest);

    /**
     * 查询项目下的指标
     *
     * @param projectId 项目id
     * @return 方案
     */
    List<DtoProjectTest> findProjectTest(String projectId);

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @return 返回该点位的方案
     */
    DtoSampleFolderTemp addFolder(DtoSampleFolder dtoSampleFolder);

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @return 返回该点位的方案
     */
    DtoSampleFolderTemp addFolderTemp(DtoSampleFolder dtoSampleFolder);

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @param testList        测试项目
     * @return 返回该点位的方案
     */
    DtoSampleFolderTemp addFolder(DtoSampleFolder dtoSampleFolder, List<DtoTest> testList);

    /**
     * 复制点位
     *
     * @param ids       被复制的点位id集合
     * @param copyTimes 复制次数
     * @return 复制出来的点位方案
     */
    List<DtoSampleFolderTemp> copyFolders(List<String> ids, Integer copyTimes, Boolean isApi);

    /**
     * 添加点位周期频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 点位周期频次
     */
    List<DtoSampleFolderTemp> addPeriodTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    /**
     * 添加周期次数样品数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 次数样品数
     */
    List<DtoSampleFolderTemp> addTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    /**
     * 添加频次样品数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 样品数
     */
    List<DtoSampleFolderTemp> addSamplePeriods(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod);

    /**
     * 复制周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param copyTimes      复制次数
     * @return 周期
     */
    List<DtoSampleFolderTemp> copyPeriod(String sampleFolderId, Integer periodCount, Integer copyTimes);

    /**
     * 复制批次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     * @param copyTimes      复制次数
     * @return 周期
     */
    List<DtoSampleFolderTemp> copyTimePeriod(String sampleFolderId, Integer periodCount, Integer timePeriod, Integer copyTimes);

    /**
     * 删除周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     */
    void deletePeriod(String sampleFolderId, Integer periodCount);

    /**
     * 删除批次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     */
    void deleteTimePeriod(String sampleFolderId, Integer periodCount, Integer timePeriod);

    /**
     * 复制次数
     *
     * @param sampleFolderId      点位id
     * @param samplingFrequencyId 频次id
     * @param copyTimes           复制次数
     * @return 次数
     */
    List<DtoSampleFolderTemp> copyTimes(String sampleFolderId, String samplingFrequencyId, Integer copyTimes);

    /**
     * 删除次数
     *
     * @param samplingFrequencyId 频次id
     */
    void deleteTimes(String samplingFrequencyId);

    /**
     * 删除点位周期次数
     *
     * @param samplingFrequencyIds 频次id集合
     */
    void deleteTimes(List<String> samplingFrequencyIds);

    /**
     * 删除频次
     *
     * @param frequencyList        频次
     * @param folderList           点位
     * @param projectId            项目id
     * @param samplingFrequencyIds 频次
     */
    void deleteFrequency(List<DtoSamplingFrequency> frequencyList, List<DtoSampleFolder> folderList, String projectId, List<String> samplingFrequencyIds);

    /**
     * 根据周期频次id获取工作单id
     *
     * @param samplingFrequencyIds 周期频次id
     * @return 工作单id
     */
    List<String> getWorkSheetIdsBySamplingFrequencyId(List<String> samplingFrequencyIds);

    /**
     * 修改频次指标
     *
     * @param samplingFrequencyId 频次id
     * @param analyseItemIds      分析项目id集合
     * @return 修改后的指标
     */
    DtoSampleFolderTemp modifySamplingFrequencyTest(String samplingFrequencyId, List<String> analyseItemIds, List<String> testIds);

    /**
     * 批量添加测试项目
     *
     * @param samplingFrequencyIds 频次id集合
     * @param analyseItemIds       分析项目id集合
     * @return 添加的频次指标
     */
    List<DtoProjectTest> addFrequencyAnalyseItems(List<String> samplingFrequencyIds, List<String> analyseItemIds, List<String> testIds);

    /**
     * 批量删除测试项目
     *
     * @param frequencyIds   频次id集合
     * @param analyseItemIds 分析项目id集合
     */
    void deleteFrequencyAnalyseItems(List<String> frequencyIds, List<String> analyseItemIds);

    /**
     * 批量删除测试项目
     *
     * @param sampleIds   样品ids
     * @param testIds 测试项目标识
     */
    void deleteSampleTests(List<String> sampleIds, List<String> testIds);

    /**
     * 批量设置分包
     *
     * @param samplingFrequencyIds 频次id集合
     * @param projectTests         项目指标集合
     */
    void sub(List<String> samplingFrequencyIds, List<DtoProjectTest> projectTests);

    /**
     * 更换指标方法
     *
     * @param dto 方法修改传输对象
     */
    List<DtoProjectTest> changeAnalyzeMethod(DtoSchemeMethodChange dto);

    /**
     * 核对方案
     *
     * @param projectId 项目id
     */
    void checkScheme(String projectId);

    /**
     * 纠正样品点位名称
     */
    void checkSampleCode();

    //#endregion

    /**
     * 添加外部送样点位
     *
     * @param dto 样品实体
     * @return 返回对应点位频次
     */
    DtoSamplingFrequency addOutsideFolder(String fixedPointId, DtoSample dto, List<DtoTest> testList);

    /**
     * 添加点位(质控)
     *
     * @param dto 样品实体
     * @return 返回对应点位频次
     */
    DtoSamplingFrequency addQMFolder(DtoSample dto);

    /**
     * 根据项目id返回点位树
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     */
    List<TreeNode> getSampleFolderTree(String projectId, String sampleTypeId);

    /**
     * 点位方案
     *
     * @param projectId     任务id
     * @param folderList    点位列表
     * @param frequencyList 频次列表
     * @param sftList       方案列表
     * @param tests         测试项目
     * @param samTypeMap    样品类型
     * @return 点位方案
     */
    List<DtoSampleFolderTemp> getFolderSchemes(String projectId,
                                               List<DtoSampleFolder> folderList,
                                               List<DtoSamplingFrequency> frequencyList,
                                               List<DtoSamplingFrequencyTest> sftList,
                                               List<DtoTest> tests,
                                               Map<String, DtoSampleType> samTypeMap);

    /**
     * 根据送样单id获取点位列表
     *
     * @param receiveId 送样单id
     * @return
     */
    List<DtoSampleFolder> getByReceiveId(String receiveId);

    /**
     * 按点位录入(新）
     * BUG2024030199945  【重要】【2024-03-02】【潘长城】【现场任务】现场任务-样品信息-按点位录入改造，原本左侧排列点位，现在将点位完全平铺，将一个点位的样品放在一个卡片区域（注意质控样），公共参数还是在最上面，点位参数跟随每个选项卡，下面样品参数、分析项目参数及其他固定列参考‘按样品录入’表格形式，参数直接放在列上，注：排序等机制都参考‘按样品录入’表格。注意每个区块增加收缩功能，并且支持全部收缩和全部展开，参考方案编制。
     *
     * @param pageBean 分页容器
     * @param sampleFolderCriteria 条件参数
     * @return RestResponse<List<DtoSampleFolder>>
     */
    DtoSampleFolderLoadDataContainer loadDataBySampleFolder(PageBean<DtoSampleFolder> pageBean, BaseCriteria sampleFolderCriteria);

    /**
     * 查询点位下各个因子的样品数据
     * @param sampleFolderIds 点位标识集合
     * @return 结果
     */
    List<DtoSampleFolderTestCount> querySampleFolderTestCount(Collection<String> sampleFolderIds);


}