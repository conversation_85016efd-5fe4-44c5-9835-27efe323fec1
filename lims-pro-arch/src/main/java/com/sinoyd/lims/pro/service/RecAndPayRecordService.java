package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoRecAndPayRecord;

import java.util.Map;

/**
 * 收/付款记录管理
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface RecAndPayRecordService extends IBaseJpaService<DtoRecAndPayRecord, String> {
    /**
     * 获取分析数据
     * @param page          分页参数
     * @param criteria      检索条件
     * @return              Map<String, Object>
     */
    Map<String, Object> analyzeProfileData(PageBean<DtoRecAndPayRecord> page, BaseCriteria criteria);

    /**
     * 附件上传
     * @param id    标识
     * @return      DtoRecAndPayRecord
     */
    DtoRecAndPayRecord findAttachPath(String id);
}