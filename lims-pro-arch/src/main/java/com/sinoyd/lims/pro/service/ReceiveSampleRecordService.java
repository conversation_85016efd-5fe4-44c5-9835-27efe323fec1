package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoUpdateAnalyst;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoInnerRecordTemp;
import com.sinoyd.lims.pro.dto.customer.DtoParamsCheckTemp;
import com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordQuery;
import com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordTemp;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * ReceiveSampleRecord操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface ReceiveSampleRecordService extends IBaseJpaService<DtoReceiveSampleRecord, String> {

    void queryByPage(PageBean<DtoReceiveSampleRecordQuery> pb, BaseCriteria receiveSampleRecordCriteria);

    /**
     * 创建状态
     *
     * @param projectId 项目id
     */
    void createOutsideStatus(String projectId);

    /**
     * 保存送样单数据
     *
     * @param dto             送样单数据
     * @param isUpdateProject 是否保存项目相关信息
     * @return 保存后的送样单数据
     */
    DtoReceiveSampleRecord save(DtoReceiveSampleRecord dto, Boolean isUpdateProject);

    /**
     * 生成送样单的时候，当送样单中存在现场测试项目时，需要在“仪器使用记录-现场仪器”中，针对每个现场测试项目，自动生成一条仪器使用记录
     *
     * @param receiveId 送样单编号
     */
    void autoGnerateXcInstrumentUseRecord(String receiveId);


    /**
     * 修改送样单
     *
     * @param dto 送样单
     */
    DtoReceiveSampleRecord updateRecord(DtoReceiveSampleRecord dto);

    /**
     * 修改送样单
     *
     * @param dto             送样单
     * @param isUpdateProject 是否更新项目信息
     */
    DtoReceiveSampleRecord updateRecord(DtoReceiveSampleRecord dto, Boolean isUpdateProject);

    /**
     * 创建送样单编号
     *
     * @return 返回送样单编号
     */
    String createReceiveSampleRecordCode();

    /**
     * 复制送样单
     *
     * @param oldProject   被复制的项目
     * @param samplingTime 采样时间
     * @param newProject   新的项目信息
     * @param isCopySample 是否复制样品
     */
    void copyRecord(DtoProject oldProject, Date samplingTime, DtoProject newProject, Boolean isCopySample);

    /**
     * 采样准备创建送样单
     *
     * @param project           项目
     * @param samples           样品
     * @param samplingTime      采样时间
     * @param samplingPersonId  采样负责人id
     * @param samplingPerson    采样负责人
     * @param samplingPersonIds 采样人员
     */
    DtoReceiveSampleRecord createReceiveRecord(DtoProject project,
                                               List<DtoSample> samples,
                                               Date samplingTime,
                                               String samplingPersonId,
                                               String samplingPerson,
                                               List<String> samplingPersonIds);

    /**
     * 采样准备创建送样单
     *
     * @param project           项目
     * @param samples           样品
     * @param samplingTime      采样时间
     * @param samplingPersonId  采样负责人id
     * @param samplingPerson    采样负责人
     * @param samplingPersonIds 采样人员
     */
    DtoReceiveSampleRecord createReceiveRecordToPhone(DtoProject project,
                                                      List<DtoSample> samples,
                                                      Date samplingTime,
                                                      String samplingPersonId,
                                                      String samplingPerson,
                                                      List<String> samplingPersonIds);


    /**
     * 创建内部送样单
     *
     * @param temp 内部送样单
     */
    DtoReceiveSampleRecord createInnerReceiveRecord(DtoInnerRecordTemp temp);

    /**
     * 加入送样单
     *
     * @param receiveId 送样单id
     * @param sampleIds 样品id集合
     */
    void joinRecord(String receiveId, List<String> sampleIds);

    /**
     * 剔除送样单
     *
     * @param receiveId 送样单id
     * @param sampleIds 样品id集合
     */
    Boolean removeSample(String receiveId, List<String> sampleIds);

    /**
     * 根据送样项目的项目id查询对应送样单
     *
     * @param projectId 项目id
     * @return 送样单
     */
    DtoReceiveSampleRecord findOutsideSendSampleById(String projectId);


    /**
     * 根据送样单id获取相关的送样单信息
     *
     * @param receiveIds 送样单ids
     * @return 返回想要的送样单数据
     */
    List<DtoReceiveSampleRecordTemp> findReceiveSampleRecordList(List<String> receiveIds);

    /**
     * 核查能否提交送样单
     *
     * @param ids  送样单id集合
     * @param type 提交类型
     */
    void canSubmitReceiveRecord(List<String> ids, String type);

    /**
     * 默认样品有效期
     *
     * @return 返回有效期
     */
    ConfigModel getDefaultDateConfig();

    /**
     * 核查能否提交送样单
     *
     * @param id   送样单id
     * @param type 提交类型
     */
    void canSubmitReceiveRecord(String id, String type);

    /**
     * 参数
     *
     * @param paramsConfigList 参数集合
     * @return 返回参数
     */
    DtoParamsCheckTemp findParamsCheckTemp(List<DtoParamsConfig> paramsConfigList);

    /**
     * 提交送样单
     *
     * @param ids  送样单id集合
     * @param type 提交类型
     */
    List<String> submitReceiveRecord(List<String> ids, String type, String nextPersonId, String nextPerson, String opinion, Date receiveSampleDate, String recipientId)
            throws Exception;

    /**
     * 复核送样单
     *
     * @param receiveIds 送样单id集合
     * @param isPass     是否通过
     * @param opinion    意见
     */
    List<String> checkRecords(List<String> receiveIds, Boolean isPass, String opinion);

    /**
     * 审核送样单
     *
     * @param receiveIds 送样单id集合
     * @param isPass     是否通过
     * @param opinion    意见
     * @param isReport   是否编制报告
     */
    List<String> auditRecords(List<String> receiveIds, Boolean isPass, String opinion, String type, Boolean isReport);

    /**
     * 附件路径
     *
     * @param id 项目id
     * @return 返回相应的路径信息
     */
    DtoReceiveSampleRecord findAttachPath(String id);

    /**
     * 获取送样单id下的实验室指标
     *
     * @param receiveId 送样单id
     * @return 返回对应送样单id下的实验室指标
     */
    List<DtoTest> findAnalyseTest(String receiveId);

    /**
     * 确认扫码
     *
     * @param groupIds 分组ids
     */
    void scanAffirmByGroupIds(List<String> groupIds);

    /**
     * 扫码显示样品
     *
     * @param codeStr 扫码信息
     * @return 返回分组信息
     */
    DtoSampleGroup scanCode(String codeStr);

    /**
     * 获取分组信息
     *
     * @param receiveId 送样单id
     * @return 返回分组信息
     */
    Map<String, Object> getSampleGroupByReceiveId(String receiveId);

    /**
     * 获取分组信息
     *
     * @param receiveId 送样单id
     * @return 返回分组信息
     */
    List<DtoSampleTypeGroup> getAllSampleGroupByReceiveId(String receiveId);

    /**
     * 更新样品分配中所有分析人员为默认人员
     *
     * @param updateAnalyst 接收实体
     */
    void updateDefaultAnalyst(DtoUpdateAnalyst updateAnalyst);

    /**
     * 查找送样单中的所有检测类型
     *
     * @param receiveId 送样单id
     * @return 返回值
     */
    List<DtoSampleType> findSampleTypeForRecord(String receiveId);

    /**
     * 判断送样单中是否存在样品编号为空的样品
     *
     * @param receiveId 送样单id
     * @return 判断结果
     */
    Boolean checkSampleCodeByReceiveId(String receiveId);

    /**
     * 新增送样单参数模板
     *
     * @param template 参数模板
     * @return 参数模板
     */
    DtoReceiveSampleRecordParamTemplate saveParamTemplate(DtoReceiveSampleRecordParamTemplate template);

    /**
     * 修改送样单参数模板
     *
     * @param template 参数模板
     * @return 参数模板
     */
    DtoReceiveSampleRecordParamTemplate updateParamTemplate(DtoReceiveSampleRecordParamTemplate template);

    /**
     * 动态条件查询送验单参数模板
     *
     * @param criteria 参数模板
     * @return 参数模板
     */
    List<DtoReceiveSampleRecordParamTemplate> queryParamTemplate(BaseCriteria criteria);

    /**
     * 获取送样单下对应检测类型的参数及现场测试项目
     *
     * @param criteria 条件参数
     * @return 参数模板
     */
    List<DtoReceiveSampleRecordParamInfo> getTemplateParam(BaseCriteria criteria);

    /**
     * 根据id批量删除送样单参数模板
     *
     * @param ids 模板id列表
     * @return 删除的数量
     */
    int deleteParamTemplate(List<String> ids);

    /**
     * 根据id查询送样单参数模板
     *
     * @param id 模板id
     * @return 送样单参数模板
     */
    DtoReceiveSampleRecordParamTemplate findParamTemplate(String id);

    /**
     * 参数模板excel导出
     *
     * @param templateId 模板id
     * @param response   响应对象
     */
    void paramTemplateExport(String templateId, String receiveId, HttpServletResponse response);

    /**
     * 参数模板excel导入
     *
     * @param file      文件对象
     * @param objectMap 参数
     * @param response  响应对象
     */
    void paramTemplateImport(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response);

    /**
     * 刷新送样单检测类型
     *
     * @param receiveIds 送样单ids
     */
    void reloadRecordJsonByIds(List<String> receiveIds);

    /**
     * 配置现场任务审核
     * true：一审 false：二审
     *
     * @return 返回判断
     */
    boolean isRecordAudit();

    /**
     * 地图底图是否使用天地图
     * true 是，false 否
     *
     * @return 返回判断
     */
    boolean isWorldEarth();

    /**
     * 查询送样单下流量校准可选的点位周期
     *
     * @param flowCalibrationId 流量校准标识
     * @param receiveId         送样单标识
     * @return 点位周期集合
     */
    List<Map<String, Object>> queryInsFolderPeriodSelectList(String flowCalibrationId, String receiveId);

    /**
     * 记录交接单修改日志
     *
     * @param id      送样单id
     * @param opinion 意见
     */
    void deliveryReceiptModify(String id, String opinion);

    /**
     * 刷新现场样品
     *
     * @param receiveIds 送样单ids
     */
    void refreshLocalSample(List<String> receiveIds);

    /**
     * 刷新分析项目参数
     *
     */
    void refreshParams();
}