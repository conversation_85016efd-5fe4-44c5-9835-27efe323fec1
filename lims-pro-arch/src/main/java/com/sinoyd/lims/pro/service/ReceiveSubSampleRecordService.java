package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.*;

import java.util.List;
import java.util.Map;


/**
 * ReceiveSubSampleRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface ReceiveSubSampleRecordService extends IBaseJpaService<DtoReceiveSubSampleRecord, String> {

    /**
     * 创建领样单
     *
     * @param record  送样单
     * @param samples 样品
     * @param type    类型 XC,FX
     */
    void createSubRecord(DtoReceiveSampleRecord record, List<DtoSample> samples, String type);

    /**
     * 根据送样单id和类型查询领样单
     *
     * @param receiveId 送样单id
     * @param type      类型 XC,FX
     * @return 返回对应送样单下的对应类型的领样单
     */
    DtoReceiveSubSampleRecord findByReceiveIdAndType(String receiveId, String type);

    /**
     * 获取领样单id下的现场指标
     *
     * @param subId 领样单id
     * @return 返回对应领样单下的现场指标
     */
    List<DtoTest> findLocalTest(String subId);

    /**
     * 更换现场公式
     *
     * @param dtoAnalyseDataChangeFormula 更换dto
     * @return 返回对应更换数据
     */
    DtoLocalDataProperty changeAnalyseDataFormula(DtoAnalyseDataChangeFormula dtoAnalyseDataChangeFormula);

    /**
     * 获取领样单id下的现场数据
     *
     * @param subId  领样单id
     * @param module 模块
     * @param type   状态
     * @return 返回对应领样单下的现场数据
     */
    List<DtoLocalDataProperty> findLocalData(String subId, String module, Integer type, String sortId, String key);

    /**
     * 获取领样单id下的现场数据
     *
     * @param subId 领样单id
     * @return 返回对应领样单下的现场数据
     */
    List<DtoAnalyseData> findLocalDataBySubId(String subId);

    /**
     * 获取样品分配列表
     */
    void findAssignByPage(PageBean<DtoReceiveSubSampleRecord> pb, BaseCriteria receiveSubSampleRecordCriteria);

    /**
     * 核查能否分配领样单
     *
     * @param ids 领样单id集合
     */
    void canAssignReceiveSubRecord(List<String> ids);

    /**
     * 核查能否分配领样单
     *
     * @param ids 领样单id集合
     * @return 返回结果
     */
    Map<String, List<String>> canSubRecord(List<String> ids);

    /**
     * 样品分配
     *
     * @param subIds 领样单id集合
     */
    void assign(List<String> subIds);

    /**
     * 查询领样单下的样品分析人员\方法分配
     *
     * @param id       领样单id
     * @param isPerson 是否查询人员
     * @return 分析人员\方法分配
     */
    DtoSampleAssignTemp findSampleAssignInfoById(String id, Boolean isPerson);

    /**
     * 修改人员
     *
     * @param paramList 传参实体
     */
    void changePerson(List<DtoSampleAssignParam> paramList);


    /**
     * 检查样品状态，筛选出在检测单内或已确认的数据，这些数据无法进行修改
     *
     * @param paramList 传参实体
     */
    List<String> checkSampleStatus(List<DtoSampleAssignParam> paramList);


    /**
     * 修改方法
     *
     * @param param 传参实体
     */
    void changeMethod(DtoSampleAssignParam param);

    /**
     * 根据领样单查询所选样品的指标类型
     *
     * @param sampleIds 样品id
     * @return 样品指标类型
     */
    List<DtoSampleTestType> findSampleTestType(List<String> sampleIds);

}