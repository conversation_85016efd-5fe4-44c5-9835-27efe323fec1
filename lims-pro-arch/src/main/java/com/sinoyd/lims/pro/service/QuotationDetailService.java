package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail2Test;
import com.sinoyd.lims.pro.dto.customer.DtoQuotationData;
import com.sinoyd.lims.pro.dto.customer.DtoQuotationFolderTestVO;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.vo.AutoProjectInfoVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * QuotationDetail操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
public interface QuotationDetailService extends IBaseJpaService<DtoQuotationDetail, String> {

    /**
     * 保存费用明细
     *
     * @param testList    测试项目集合
     * @param orderId     订单id
     * @param quotationId 费用id
     */
    void save(List<DtoTest> testList, String orderId, String quotationId, String sampleTypeId, String testTemplateId);

    /**
     * 处理订单详情中的总称测试项目价格
     *
     * @param quotationDetail2TestList 订单详情测试项目集合（总称时添加关联子测试项目数据）
     * @param quotationDetail          订单详情
     * @param test                     测试项目数据
     * @param costList                 费用集合
     * @param sampleTypeId             检测类型id
     */
    void handleTotalTestPrice(List<DtoQuotationDetail2Test> quotationDetail2TestList,
                              DtoQuotationDetail quotationDetail,
                              DtoTest test, List<DtoCost> costList,
                              String sampleTypeId);

    /**
     * 费用明细获取子项测试项目
     *
     * @param detailId 明细id
     * @return 子项测试项目集合
     */
    List<DtoTest> findByTestList(String detailId);

    /**
     * 新增明细子项
     *
     * @param detailId 明细id
     * @param testIds  测试项目ids
     */
    void addTestList(String detailId, List<String> testIds);

    /**
     * 删除明细子项
     *
     * @param detailId 明细id
     * @param testIds  测试项目ids
     */
    void deleteTestList(String detailId, List<String> testIds);

    /**
     * 批量修改费用详情
     *
     * @param detailIds        明细ids
     * @param dtoQuotationData 修改内容
     */
    void updateBatch(List<String> detailIds, DtoQuotationData dtoQuotationData);

    /**
     * 批量添加点位信息
     *
     * @param detailIds        明细ids
     * @param dtoQuotationData 修改内容
     */
    void insertFolderBatch(List<String> detailIds, DtoQuotationData dtoQuotationData);

    /**
     * 更新检测费
     *
     * @param detailIds 明细ids
     */
    void updatePrice(List<String> detailIds);

    /**
     * 新增企业点位
     *
     * @param quotationData 实体数据
     * @return String
     */
    void addFixedPoint(DtoQuotationData quotationData);

    /**
     * 获取检测费用明细下检测类型
     *
     * @param orderId 订单id
     * @return 检测类型
     */
    List<DtoSampleType> findSampleTypes(String orderId);

    /**
     * 根据订单id获取所有点位
     *
     * @param detailIds 明细id
     * @return List<String>
     */
    Collection<String> findAllFolder(List<String> detailIds);

    /**
     * 批量删除点位
     *
     * @param detailIds     明细id
     * @param quotationData 点位集合
     */
    void batchDeleteFolder(List<String> detailIds, DtoQuotationData quotationData);

    /**
     * 根据订单id获取详情信息
     *
     * @param orderId 订单id
     * @return 返回详细信息
     */
    List<DtoQuotationDetail> findByOrderId(String orderId);


    /**
     * 点位导入模版下载
     *
     * @param response 响应头
     */
    void downLoadTemplate(HttpServletResponse response);

    /**
     * 点位导入
     *
     * @param file      导入文件
     * @param response  响应头
     */
    List<String> importFolder(MultipartFile file,  HttpServletResponse response);

    /**
     * 检测费明细转点位方案展示
     *
     * @param orderId 订单标识
     * @return 点位方案
     */
    List<DtoQuotationFolderTestVO> schemaFolderShow(String orderId);


    /**
     * 订单自动拆单
     *
     * @param year    年份
     * @param month   月份
     * @param count   拆单数
     * @param orderId 订单id
     * @return 自动拆单集合
     */
    List<AutoProjectInfoVO> autoDisassembleOrderProject(Integer year, Integer month, Integer count, String orderId);

    /**
     * 方案预览
     *
     * @param details 订单详情
     * @return 预览信息
     */
    List<DtoSampleFolderTemp> generateProjectPlans(List<DtoQuotationDetail> details);

    /**
     * 自定拆单，批量创建项目
     *
     * @param orderId    订单id
     * @param infoVOList 拆单明细
     * @param isSampling 是否采样
     */
    void autoProjectByDetails(String orderId, List<AutoProjectInfoVO> infoVOList, Boolean isSampling);
}