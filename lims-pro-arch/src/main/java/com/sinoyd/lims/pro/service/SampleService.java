package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Sample操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SampleService extends IBaseJpaService<DtoSample, String> {

    //#region 生成样品编号

    /**
     * 根据样品编号查询相应的信息
     *
     * @param code 样品编号
     * @return 返回编号信息
     */
    DtoSample findByCode(String code);


    /**
     * 创建空白标签
     *
     * @param sampleDate   采样日期
     * @param sampleTypeId 检测类型
     * @param isCreate     是否创建
     * @return 返回空白标签
     */
    String createSpaceSampleCode(Date sampleDate, String sampleTypeId, Boolean isCreate);


    /**
     * 返回室内的质控编号
     *
     * @param sampleCategory     样品类别
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param associateSampleId  原样id
     * @param workSheetFolderId  检测单id
     * @param standardSampleCode 标样编号
     * @return 返回室内的质控编号
     */
    String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType, String associateSampleId,
                                 String workSheetFolderId, String standardSampleCode);


    /**
     * 返回室内的质控编号
     *
     * @param sampleCategory     样品类别
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param associateSample    原样信息
     * @param workSheetFolderId  检测单id
     * @param standardSampleCode 标样编号
     * @return 返回室内的质控编号
     */
    String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType, DtoSample associateSample, String workSheetFolderId,
                                 String standardSampleCode);


    /**
     * 返回室内的质控编号
     *
     * @param sampleCategory     样品类别
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param associateSampleId  原样id
     * @param dtoWorkSheetFolder 检测单信息
     * @param standardSampleCode 标样编号
     * @return 返回室内的质控编号
     */
    String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType,
                                 String associateSampleId, DtoWorkSheetFolder dtoWorkSheetFolder, String standardSampleCode);


    /**
     * 返回室内的质控编号
     *
     * @param sampleCategory     样品类别
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param associateSample    原样信息
     * @param dtoWorkSheetFolder 检测单信息
     * @param standardSampleCode 标样编号
     * @return 返回室内的质控编号
     */
    String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType, DtoSample associateSample,
                                 DtoWorkSheetFolder dtoWorkSheetFolder, String standardSampleCode);

    //#endregion

    void findSampleTestByPage(PageBean<DtoTest> pb, BaseCriteria criteria);

    /**
     * @param sampleId       样品id
     * @param paramsDataList 参数数据
     */
    void saveSampleParams(String sampleId, List<DtoParamsConfig> paramsDataList);

    //#region 采样准备

    /**
     * 采样准备获取点位信息
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param key          关键字
     * @param status       采样状态
     * @return 点位信息
     */
    DtoPrepareFolder findPrepareFolder(String projectId, String sampleTypeId, String key, Integer status);

    /**
     * 采样准备创建样品编号
     *
     * @param projectId        项目id
     * @param ids              样品ids
     * @param samplingTime     采样时间
     * @param samplingPersonId 采样负责人id
     * @param samplingPerson   采样负责人名称
     * @param samplingPersons  采样人员
     */
    List<DtoSample> createSampleCode(String projectId, List<String> ids, Date samplingTime, String samplingPersonId, String samplingPerson, List<DtoSamplingPersonConfig> samplingPersons);

    /**
     * 采样准备清除样品编号
     *
     * @param ids 样品id集合
     */
    void clearSampleCode(List<String> ids);

    List<String> clearCodeBackRecordIds(List<String> ids);

    //#endregion

    /**
     * 核对样品
     *
     * @param ids 样品id集合
     */
    void check(List<String> ids);


    /**
     * 初始化样品
     *
     * @param dtoOutSample 外部样品
     * @return 初始化的样品信息
     */
    DtoOutSample initOutSample(DtoOutSample dtoOutSample);

    /**
     * 获取样品详情
     *
     * @param id 样品id
     * @return 样品详情
     */
    DtoOutSample findDetail(String id);

    /**
     * 获取样品信息
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @return 样品信息
     */
    DtoSampleInfo findDetails(String receiveId, String sampleTypeId, String sampleFolderId);

    /**
     * 获取样品信息
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @param isProject      是否项目登记
     * @return 样品信息
     */
    DtoSampleInfo findDetails(String receiveId, String sampleTypeId, String sampleFolderId, Boolean isProject);

    DtoSampleInfo findFolderDetails(String receiveId, String sampleTypeId, String sampleFolderId, String samKey, String paramsKey, Integer sortType);

    /**
     * 根据送样单、检测类型、点位查询样品
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @param isSameRecord   是否同张送样单下
     * @return 样品
     */
    List<DtoSample> findSampleByReceiveIdAndSampleTypeIdAndSampleFolderId(String receiveId, String sampleTypeId, String sampleFolderId, Boolean isSameRecord);

    /**
     * 新增外部样品
     *
     * @param dto 样品
     * @return 返回外部样品
     */
    DtoOutSample saveOutsideSample(DtoOutSample dto);

    /**
     * 修改外部样品
     *
     * @param sampleSave 样品修改信息
     */
    void updateOutsideSample(DtoOutSampleSave sampleSave);

    /**
     * 修改样品
     *
     * @param sample 样品
     */
    DtoSample updateSample(DtoSample sample);

    /**
     * 新增关联样
     *
     * @param ids            样品id集合
     * @param sampleCategory 样品类别
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     */
    List<DtoSample> addAssociateSample(Collection<String> ids, Integer sampleCategory, Integer qcGrade, Integer qcType);

    /**
     * 通过关联样id获取样品
     *
     * @param allSampleList 所有样品列表
     * @param assSampleList 关联样品列表
     */
    void addSampleByAssId(List<DtoSample> allSampleList, List<DtoSample> assSampleList);

    /**
     * 新增关联样
     *
     * @param ids            样品id集合
     * @param sampleCategory 样品类别
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     * @param isApi          是否移动端
     */
    List<DtoSample> addAssociateSample(Collection<String> ids, Integer sampleCategory, Integer qcGrade, Integer qcType, Boolean isApi);

    /**
     * 获取原样以及相关的现场关联样
     *
     * @param ids 样品id集合
     * @return 原样以及相关的现场关联样
     */
    List<DtoSample> findAllLocalSample(Collection<String> ids);

    /**
     * 获取原样的现场关联样
     *
     * @param ids 原样id集合
     * @return 原样的现场关联样
     */
    List<DtoSample> getLocalAssociateSample(Collection<String> ids);

    /**
     * 获取待选的样品
     *
     * @param projectId      项目id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @param key            关键字
     * @return 原样的现场关联样
     */
    List<DtoSample> findRemovedSamples(String projectId, String sampleTypeId, String sampleFolderId, String key);

    /**
     * 获取样品名称
     *
     * @param sample    样品
     * @param watchSpot 点位名称
     * @return 样品名称
     */
    String getSampleName(DtoSample sample, String watchSpot);

    /**
     * 修改点位名称
     *
     * @param sampleFolderId 点位id
     * @param watchSpot      点位名称
     * @param folderCode     点位编号
     */
    void updateWatchSpot(String sampleFolderId, String watchSpot, String folderCode);

    /**
     * 根据使用记录id获取相关的样品信息
     *
     * @param useRecordId 使用记录id
     * @return 返回相关的样品信息
     */
    List<DtoSample> findSamplesByUseRecord(String useRecordId);

    /**
     * 增加室内质控样数据
     *
     * @param qualityControl 参数
     * @return 返回质控样数据
     */
    List<Map<String, Object>> addInnerSample(DtoQualityControlTemp qualityControl, Boolean isXc);

    /**
     * 批量增加室内质控样数据
     *
     * @param qualityControlTempList 质控样集合
     * @return 返回质控样数据
     */
    List<Map<String, Object>> addInnerSample(List<DtoQualityControlTemp> qualityControlTempList);

    /**
     * 批量增加室内质控样数据
     *
     * @param qualityControlTempList 质控样集合
     * @param isXc                   是否现场
     * @return 返回质控样数据
     */
    List<Map<String, Object>> addInnerSample(List<DtoQualityControlTemp> qualityControlTempList, Boolean isXc);

    /**
     * 获取样品下的模板明细
     *
     * @param para 传参
     * @return 模板明细
     */
    List<Map<String, Object>> findSampleTemplateDetail(DtoSampleItemParams para);

    /**
     * 获取质控样样品详情
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param testId       测试项目id
     * @return 质控样样品详情
     */
    List<DtoQCSample> findQCSample(String projectId, String sampleTypeId, String testId);

    /**
     * 根据样品ID查询样品日志
     *
     * @param sampleId 样品id
     * @return 样品相关日志信息
     */
    List<DtoLog> findSampleLog(String sampleId);

    /**
     * 样品作废
     *
     * @param ids    作废的样品ids
     * @param remark 作废的原因
     */
    void invalidSamples(List<String> ids, String remark);

    /**
     * 取消作废
     *
     * @param ids    取消作废的样品ids
     * @param remark 取消作废的原因
     */
    void invalidCancelSamples(List<String> ids, String remark);

    /**
     * 采样准备样品排序
     *
     * @param samples 样品集合
     */
    List<DtoSample> sortPrepareSample(List<DtoSample> samples, Boolean isFilter);

    /**
     * 根据点位修改样品经纬度
     *
     * @param sampleFolder 点位
     */
    void updateSampleLonAndLat(DtoSampleFolder sampleFolder);

    /**
     * 采样准备样品排序
     *
     * @param samplingFrequencies 样品周期频次
     */
    void sortPrepareFolder(List<DtoSamplingFrequency> samplingFrequencies);

    void findSampleAndProjectByPage(PageBean<DtoSample> pageBean, BaseCriteria criteria);

    Map<String, Object> getAnalyseDataMap(DtoAnalyseData dtoAnalyseData,
                                          DtoSample dtoSample,
                                          String formula,
                                          String formulaId,
                                          String qcValue,
                                          String qcVolume,
                                          String qcCode,
                                          String sampleRemark
    );

    /**
     * 创建质控米高扬
     *
     * @param sampleIds         原样id集合
     * @param qcType            质控样类型
     * @param qcGrade           质控类型
     * @param workSheetFolderId 工作单id
     * @param qualityControls   质控信息集合
     * @return
     */
    List<DtoSample> createQcSamples(Collection<String> sampleIds, int qcType, int qcGrade, String workSheetFolderId, List<DtoQualityControl> qualityControls);

    /**
     * 批量添加质控样时获取当前测试项目上一次配置的量纲
     *
     * @param qcType     质控样类型
     * @param testIdList 质控类型
     * @return 测试项目上一次配置的量纲
     */
    Map<String, Map<String, String>> getQcDimensionInfoForTests(int qcType, List<String> testIdList);

    /**
     * 获取所有关联样品,加入原有样品列表中
     *
     * @param allSampleList 样品列表
     */
    void addAssSample(List<DtoSample> allSampleList);

    /**
     * 通过领样单获取样品
     *
     * @param subId 领样单id
     * @return 样品集合
     */
    List<DtoSample> findSampleBySubId(String subId);

    /**
     * 根据样品ids获取对应qcType和qcGrade信息的样品集合
     *
     * @param samIds 样品ids
     * @return 样品集合
     */
    List<DtoSample> findBySampleIds(Collection<String> samIds);

    Map<String, Object> getAssociateSample(Integer sampleCategory,
                                           Integer qcGrade,
                                           Integer qcType,
                                           DtoSample oldSample,
                                           String qcValue,
                                           String qcAliasName,
                                           String qcCode,
                                           String qcVolume,
                                           Date qcValidDate,
                                           String currentUserId,
                                           DtoWorkSheetFolder dtoWorkSheetFolder,
                                           DtoProject project,
                                           Date qcStandardDate,
                                           String qcStandardId, String qcConcentration);

    /**
     * 保存比对数据
     *
     * @param sampleList 样品列表
     * @param project    项目
     */
    void doSaveCompareJudgeData(List<DtoSample> sampleList, DtoProject project);

    /**
     * 获取样品排序的key值映射
     *
     * @param samples 样品列表
     * @param qcList  质控信息列表
     *                样品排序的key值映射
     */
    Map<String, String> getSampleValueMapForSort(List<DtoSample> samples, List<DtoQualityControl> qcList);

    /**
     * 获取样品排序key值
     *
     * @param sampleValMap 样品排序的key值映射
     * @param sample       样品对象
     */
    String getKeyForSort(Map<String, String> sampleValMap, DtoSample sample);

    /**
     * 根据key值排序样品
     *
     * @param samples 样品列表
     * @param qcList  质控信息列表
     */
    void SortSampleByKey(List<DtoSample> samples, List<DtoQualityControl> qcList);

    /**
     * 现场/送样任务样品排序
     *
     * @param sampleList 样品列表
     */
    void sortSampleForXC(List<DtoSample> sampleList);

    /**
     * 选定日期样品登记明细
     *
     * @param date 选定日期
     * @return 样品列表
     */
    List<DtoSample> findDateRegisteredSampleList(String date);

    /**
     * 样品参数同步ocr识别结果
     *
     * @param ocrDataSyncParams 同步参数
     */
    void syncOcrParamsData(DtoOcrDataSyncParams ocrDataSyncParams);

    /**
     * 批量公式计算现场数据出证结果并保存
     *
     * @param sampleDataPhoneList 参数容器
     * @return 异常信息
     */
    Map<String, Object> batchCalculateFormula(List<DtoSampleDataPhone> sampleDataPhoneList);

    /**
     * 根据项目id获取数据集合
     *
     * @param projectId 项目id
     * @return 返回数据集合
     */
    List<DtoAnalyseData> findByProjectId(String projectId);

    /**
     * 根据送样单获取样品集合
     *
     * @param receiveId 送样单id
     * @return 返回样品集合
     */
    List<DtoSample> findByReceiveId(String receiveId);

    /**
     * 根据项目id获取数据集合
     *
     * @param receiveId 送样单id
     * @return 返回数据集合
     */
    List<DtoAnalyseData> findDataByReceiveId(String receiveId);

    /**
     * 获取ocr样品选择
     *
     * @return 样品列表
     */
    List<Map<String, Object>> getOcrSelectList();

    /**
     * 获取样品分组信息
     *
     * @param sampleId 样品编号
     * @return 分组信息
     */
    List<Map<String, Object>> getSampleGroupSelectList(String sampleId);

    /**
     * 获取排序值
     *
     * @param sample            样品
     * @param orderReviseVOList 配置文件数据
     * @return 样品排序值
     */
    String getSortOrder(DtoSample sample, List<OrderReviseVO> orderReviseVOList);

    /**
     * 样品参数同步例行点位信息
     *
     * @param receiveId 送样单Id
     */
    void syncFixedPoint(String receiveId);

    /**
     * 样品同步移动端签到点位的经纬度数据
     *
     * @param receiveId 送样单Id
     */
    void syncFolderLocation(String receiveId);

    /**
     * 送样类批量添加测试项目
     *
     * @param samplingFrequencyTest 传输实体
     */
    void batchAddTest(DtoSamplingFrequencyTest samplingFrequencyTest);

    /**
     * 现场任务样品排序（先按照采样日期正序，然后按照样品编号正序）
     *
     * @param samples 样品集合
     */
    List<DtoSample> sortSampleByReceiveDataAndCode(List<DtoSample> samples);

    /**
     * 批量填写样品参数
     *
     * @param sampleParamsVO 杨平参数传输实体
     */
    void batchSampleParams(DtoSampleParamsVO sampleParamsVO);

    /**
     * 加载手动修改的样品编号，用于前端修改了序列号后页面及时回显
     *
     * @param sample 样品实体
     * @return 编号
     */
    String loadManualSampleCode(DtoSample sample);

    /**
     * 保存体积类型
     *
     * @param sample       样品
     * @param paramsConfig 参数
     * @param paramsValue  参数值
     * @param groupId      分组id
     */
    void saveParamsDataVolume(DtoSample sample, DtoParamsConfig paramsConfig, String paramsValue, String groupId);
}