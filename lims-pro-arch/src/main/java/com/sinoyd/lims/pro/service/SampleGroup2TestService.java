package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.dto.DtoSampleGroup2Test;

import java.util.Collection;
import java.util.List;

/**
* SampleGroup2Test操作接口
* <AUTHOR>
* @version V1.0.0 2023/7/28
* @since V100R001
*/
public interface SampleGroup2TestService extends IBaseJpaService<DtoSampleGroup2Test, String> {

    /**
     * 根据分组ids获取关系数据集合
     *
     * @param groupIds 分组ids
     * @return 数据集合
     */
    List<DtoSampleGroup2Test> findByGroupIds(Collection<String> groupIds);
}
