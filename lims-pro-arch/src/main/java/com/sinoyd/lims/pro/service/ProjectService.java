package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.customer.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 项目操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
public interface ProjectService extends IBaseJpaService<DtoProject, String> {

    /**
     * 获取合同下的项目列表
     *
     * @param contractId 合同id
     * @return 合同下的项目列表
     */
    List<DtoProject> findByContractId(String contractId);

    /**
     * 获取送样项目详情
     *
     * @param id 项目id
     * @return 送样项目
     */
    DtoReceiveSampleRecordTemp findOutsideSendSample(String id);

    /**
     * 新增送样项目
     *
     * @param dto 实体
     * @return 送样项目
     */
    DtoReceiveSampleRecordTemp saveOutsideSendSample(DtoReceiveSampleRecordTemp dto);

    /**
     * 修改送样项目
     *
     * @param dto 实体
     * @return 送样项目
     */
    DtoReceiveSampleRecordTemp updateOutsideSendSample(DtoReceiveSampleRecordTemp dto);

    /**
     * 复制项目
     *
     * @param receiveSampleRecord 前端传回的采样单对象
     * @return 所复制的项目
     */
    DtoProject copyProject(DtoReceiveSampleRecord receiveSampleRecord);

    /**
     * 复制项目（实际项目不使用，测试组造数据使用）
     *
     * @param projectCode  项目编号
     * @param samplingTime 采样时间
     * @param copyTimes    复制次数
     */
    List<DtoProject> copyProject(String projectCode, Date samplingTime, int copyTimes);

    /**
     * 复制方案
     *
     * @param oldProjectId 源项目id
     * @param projectId    目标项目id
     */
    void copyScheme(String oldProjectId, String projectId);

    /**
     * 创建项目编号
     *
     * @param projectTypeId 项目类型id
     * @param inputTime     登记时间
     * @return 返回项目编号
     */
    String createProjectCode(String projectTypeId, Date inputTime);

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    void projectSignal(DtoWorkflowSign dtoWorkflowSign);

    /**
     * 获取项目下未采样品的个数
     *
     * @param projectId 样品id
     * @return 项目下未采样品的个数
     */
    Integer countNotSampleByProjectId(String projectId);

    /**
     * 新增项目流程状态退回日志
     *
     * @param projectId 对象ids
     * @param opinion   意见
     */
    void projectSchemeModify(String projectId, String opinion);

    /**
     * 获取项目下的检测类型
     *
     * @param projectId 项目id
     * @return 项目下的检测类型
     */
    List<DtoSampleType> findProjectSampleTypes(String projectId);

    /**
     * 获取项目下检测大类
     *
     * @param projectId 项目id
     */
    List<DtoSampleType> findProjectBigSampleTypes(String projectId);

    /**
     * 核对项目操作条件
     *
     * @param projectIds 项目id集合
     * @param type       场景类型
     */
    void checkCondition(List<String> projectIds, String type);

    /**
     * 校验必填参数是否有值
     *
     * @param projectIds 项目id集合
     * @return 是否有必填参数没有填写参数值
     */
    boolean checkSampleParamRequired(List<String> projectIds);

    /**
     * 验证报告完成
     *
     * @param projectId 项目id
     * @param errorInfo 返回信息
     */
    void checkReportCondition(String projectId, DtoErrorInfo errorInfo);

    /**
     * 修改项目报告状态
     *
     * @param projectIds   项目idlist
     * @param reportStatus 报告状态
     */
    void changeReportStatus(List<String> projectIds, Integer reportStatus, String comment);

    /**
     * 修改项目采样状态
     *
     * @param projectIds     项目idlist
     * @param samplingStatus 报告状态
     */
    void changeSamplingStatus(List<String> projectIds, Integer samplingStatus);

    /**
     * 更换编制报告人
     *
     * @param projectId     项目id
     * @param reportMakerId 编制报告人id
     * @param opinion       意见
     */
    void changeReportMaker(String projectId, String reportMakerId, String opinion);

    /**
     * 附件路径
     *
     * @param id 项目id
     * @return 返回相应的路径信息
     */
    DtoProject findAttachPath(String id);

    /***
     * 保存环境质量任务
     * @param project 项目信息
     * @param pIds 监测子计划
     * @param pollution 是否污染源
     * @param scheme 是否更新方案
     */
    DtoProject saveProject(DtoProject project, List<String> pIds, Boolean pollution, Boolean scheme);

    /**
     * 更新污染源方案
     *
     * @param projectId 任务id
     * @param pIds      点位集合
     */
    void pollutionMonitorScheme(String projectId, List<String> pIds);

    /**
     * 更新例行方案
     *
     * @param projectId 任务id
     * @param pIds      监测计划集合
     */
    void routineMonitorScheme(String projectId, List<String> pIds);

    /**
     * 获取污染源配置点位信息
     *
     * @param projectId    任务id
     * @param entId        企业id
     * @param enableStatus 是否启用
     * @param showConfig   显示配置查询
     */
    List<DtoFixedpoint> getPollutionFolder(String projectId, String entId, Integer enableStatus, Boolean showConfig);

    /**
     * 获取例行监测计划信息(树)
     *
     * @param key  关键字检索（计划名称、子计划名称）
     * @param year 年份
     */
    List<TreeNode> getPointProperty(String key, Integer year, Integer month);

    /**
     * 已选监测计划列表
     *
     * @param projectId 任务id
     * @return 监测计划集合
     */
    List<DtoFixedPointProperty> getProjectProperty(String projectId);

    /**
     * 项目进度，点位地图数据
     *
     * @param projectId 项目id
     * @param keyWord   查询关键字
     * @return 查询返回结果
     */
    List<DtoSampleFolderMap> getSampleFolderMapOfProject(String projectId, String keyWord);

    /**
     * 保存分包项目
     *
     * @param project 项目
     * @return 保存后的项目
     */
    DtoProject createSubcontractedProject(DtoProject project);

    /**
     * 更新分包项目
     *
     * @param project 项目
     * @return 保存后的项目
     */
    DtoProject updateSubcontractedProject(DtoProject project);

    /**
     * 导入订单方案
     *
     * @param quotationDetailIds 详情ids
     * @param projectId          项目id
     */
    void matchOrderScheme(List<String> quotationDetailIds, String projectId);

    /**
     * 导入订单方案-按点位导入
     *
     * @param quotationDetailIds 详情ids
     * @param projectId          项目id
     * @param filterList         点位
     */
    void matchOrderSchemeFolder(List<String> quotationDetailIds, String projectId, List<String> filterList);

    /**
     * 根据主项目 查询子项目信息
     *
     * @param ids 主项目id
     * @return 项目集合
     */
    List<DtoProject> findByParentIds(Collection<?> ids);

    /**
     * 批量保存项目
     *
     * @param dtoList 项目集合
     */
    void saveBatchProject(List<DtoProject> dtoList);

    /**
     * 获取历史内容（监测目的）
     *
     * @return 监测目的
     */
    List<String> findHistoryMsg();

    /**
     * 查询项目信息
     *
     * @param pb           分页
     * @param baseCriteria 查询条件
     */
    void queryProject(PageBean<DtoProject> pb, BaseCriteria baseCriteria);

    /**
     * 提交提醒明细
     *
     * @param objectIds    objIds
     * @param restrictType 类型
     * @param status       状态
     * @return 返回明细
     */
    List<DtoSubmitRestrictVo> submitMsgList(List<String> objectIds, String restrictType, String status);

    /**
     * 提交提醒明细
     *
     * @param objectId     objId
     * @param restrictType 类型
     * @param status       状态
     * @return 返回明细
     */
    List<DtoSubmitRestrictVo> submitMsgList(String objectId, String restrictType, String status);

    /**
     * 复制方案时检查方案中测试项目
     *
     * @param map 传输实体
     * @return 检查后提示信息
     */
    void checkTest(Map<String,Object> map);

    /**
     * 获取报告交付值数组
     * @param project 项目
     * @return 值数组
     */
    List<Integer> findPostMethodKeys(DtoProject project);

    /**
     * 根据合同id带出最近关联此合同的项目拓展信息
     *
     * @param contractId 合同id
     * @return           DtoProjectContract
     */
    Map<String,Object> findProjectExpandByContractId(String contractId);

    /**
     * 电子表单推送至移动端签名模块
     *
     * @param documentId 附件id
     */
    void pushSign(String documentId);

    /**
     * 获取项目下的样品最早采样日期
     *
     * @param projectId 项目id
     * @return 最早采样日期
     */
    String getEarliestSamplingTime(String projectId);

    /**
     * 获取监管平台合同列表
     * @param req 参数
     * @return 结果
     */
    List<Map<String, Object>> queryPlatformContract(Map<String, String> req, RestResponse<List<Map<String, Object>>> response);


    /**
     * 更新外部送样单
     * @param dto  送样项目实体
     */
    void updateOutReceiveRecord(DtoReceiveSampleRecordTemp dto);

    /**
     * 批量保存项目
     *
     * @param projectList      项目集合
     * @param isSampling       是否采样
     * @param projectDetailMap 项目明细
     */
    void saveBatchProject(List<DtoProject> projectList, Boolean isSampling, Map<String, List<DtoQuotationDetail>> projectDetailMap);


    /**
     * 获取项目下样品是否全部采测分包
     *
     * @param projectId 项目id
     * @return 是否全部采测分包
     */
    boolean getIsAllOutsourcing(String projectId);
}