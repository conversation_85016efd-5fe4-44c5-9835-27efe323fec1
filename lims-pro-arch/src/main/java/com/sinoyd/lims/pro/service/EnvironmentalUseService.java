package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;

import java.util.List;

/**
 * 环境信息操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
public interface EnvironmentalUseService {

    /**
     * 保存环境信息记录
     *
     * @param dto 环境信息记录
     * @return 环境信息记录
     */
    DtoEnvironmentalRecord save(DtoEnvironmentalRecord dto);

    /**
     * 新增共享仪器
     *
     * @param useRecordIds 数据载体
     * @return 返回数据
     */
    List<DtoEnvironmentalRecord> saveShareInstrument(List<String> useRecordIds, String objectId, Integer objectType);

    /**
     * 检查仪器使用情况
     *
     * @param dto 检查仪器使用情况
     * @return 仪器使用情况
     */
    String check(DtoEnvironmentalRecord dto);

    /**
     * 删除环境信息记录
     *
     * @param ids 环境信息id集合
     * @return 删除条数
     */
    Integer delete(List<String> ids);

    /**
     * 创建日志
     *
     * @param dto 数据载体
     */
    void createLog(DtoEnvironmentalRecord dto);
}
