package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseQualityControlData;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluateDetail;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluateRow;
import com.sinoyd.lims.pro.dto.customer.DtoQcSampleEvaluateDetail;

import java.util.List;
import java.util.Map;

/**
 * QualityControlEvaluate操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/10
 * @since V100R001
 */
public interface QualityControlEvaluateService extends IBaseJpaService<DtoQualityControlEvaluate, String> {

    /**
     * 更新质控评价
     *
     * @param workSheetFolderId 检测单id
     */
    void updateEvaluate(String workSheetFolderId);

    /**
     * 更新质控评价
     *
     * @param analyseQualityControlDataList 分析数据质控信息列表
     * @param workSheetFolderId             检测单id
     */
    void updateQualityControlEvaluate(List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId);

    /**
     * 查询质控评价详情
     *
     * @param evaluateId 质控评价id
     */
    DtoEvaluateDetail findEvaluateDetail(String evaluateId);

    /**
     * 查询质控样品评价明细
     *
     * @param pb     分页对象
     * @param detail 质控样评价明细查询条件
     * @return 样品评价明细列表
     */
    List<DtoQcSampleEvaluateDetail> findEvaluateDetailByPage(PageBean<DtoQcSampleEvaluateDetail> pb, DtoQcSampleEvaluateDetail detail);

    /**
     * 校验质控评价是否有不合格的
     *
     * @param workSheetFolderId 检测单id
     * @return 不合格的质控类型名称列表
     */
    List<String> checkEvaluatePass(String workSheetFolderId);

    /**
     * 根据质控评价以及样品和测试项目获取拓展信息
     *
     * @param evaluate    质控评价
     * @param analyseData 测试项目数据
     * @param sample      样品
     * @return 拓展信息
     */
    List<DtoEvaluateRow> getExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample);

    /**
     * 初始化质控评价记录
     *
     * @param analyseDataId           分析数据id
     * @param testId                  测试项目id
     * @param qcType                  质控类型
     * @param qcGrade                 质控等级
     * @param qualityControlLimitList 质控限值配置列表
     * @param qcId                    质控信息id
     */
    DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                         String qcValue, String qcId);

    /**
     * 初始化质控评价记录
     *
     * @param analyseDataId           分析数据id
     * @param testId                  测试项目id
     * @param qcType                  质控类型
     * @param qcGrade                 质控等级
     * @param qualityControlLimitList 质控限值配置列表
     * @param qcId                    质控信息id
     * @param uncertainType           不确定类型
     */
    DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                         String qcValue, String qcId, Integer uncertainType);

    /**
     * 初始化质控评价记录
     *
     * @param analyseDataId           分析数据id
     * @param testId                  测试项目id
     * @param qcType                  质控类型
     * @param qcGrade                 质控等级
     * @param qualityControlLimitList 质控限值配置列表
     * @param qcId                    质控信息id
     * @param uncertainType           不确定类型
     * @param rangeLow                范围低点
     * @param rangeHigh               范围高点
     */
    DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                         String qcValue, String qcId, Integer uncertainType, String rangeLow, String rangeHigh);

    /**
     * 初始化质控评价记录
     *
     * @param analyseDataId           分析数据id
     * @param testId                  测试项目id
     * @param qcType                  质控类型
     * @param qcGrade                 质控等级
     * @param qualityControlLimitList 质控限值配置列表
     * @param qcId                    质控信息id
     */
    List<DtoQualityControlEvaluate> initQualityControlEvaluateList(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                                   String qcValue, String qcId);

    /**
     * 组装一个分析数据质控信息对象
     *
     * @param analyseData    分析数据对象
     * @param qualityControl 质控信息对象
     * @param originalRecord 分析数据公示参数对象
     * @return 析数据质控信息对象
     */
    DtoAnalyseQualityControlData initAnalyseQualityControlData(DtoAnalyseData analyseData, DtoQualityControl qualityControl,
                                                               DtoAnalyseOriginalRecord originalRecord, DtoTest test);

    /**
     * 评价分析数据
     *
     * @param analyseData 分析数据
     * @return List<DtoQualityControlEvaluate>
     */
    List<DtoQualityControlEvaluate> evaluateAnaData(Map<String, Object> analyseData);

    /**
     * 手动修正允许限值并重新评价
     *
     * @param qualityControlEvaluate 质控评价
     */
    void handleEvaluate(DtoQualityControlEvaluate qualityControlEvaluate);

    /**
     * 重置为系统评价
     *
     * @param worksheetFolderId 检测单标识
     */
    void systemEvaluate(String worksheetFolderId);

    /**
     * 根据报告下的是样品获取质控信息
     *
     * @param reportIdList 报告ids
     * @param sampleList   报告绑定的样品信息
     * @return 质控样
     */
    List<DtoSample> getQcSampleForReportSample(List<String> reportIdList, List<DtoSample> sampleList);

    /**
     * 获取现场平行样数据信息
     *
     * @param pxData             平行样数据
     * @param yySample           原样数据
     * @param examLimitValueLess 测定下限
     * @param test               测试项目
     * @param allSampleCodeList  样品编号列表
     * @param allTstValList      原样结果与平行样结果
     */
    String getXcPxInfo(DtoAnalyseData pxData, DtoSample yySample, String examLimitValueLess, DtoTest test, List<String> allSampleCodeList, List<String> allTstValList);

    /**
     * 获取室内平行样测定值及均值信息
     *
     * @param analyseData   室内平行样数据
     * @param sample        室内平行样品
     * @param test          测试项目对象
     * @param expendInfo    扩展信息对象
     * @param yySample      原样品
     * @param yyAnaData     原样分析数据
     * @param allTstValList 平行样测定值列表
     * @return 均值
     */
    String getInPxInfo(DtoAnalyseData analyseData, DtoSample sample, DtoTest test, List<DtoEvaluateRow> expendInfo,
                       DtoSample yySample, DtoAnalyseData yyAnaData, List<String> allTstValList);
}