package com.sinoyd.lims.pro.service;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportNumberPool;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.DtoQcSampleEvaluateDetail;
import com.sinoyd.lims.pro.entity.ReportNumberPool;

import java.util.List;
import java.util.Map;


/**
 * Report操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface ReportService extends IBaseJpaService<DtoReport, String> {
    /**
     * 按照报告编号查询相应的报告
     *
     * @param code 报告编号
     * @return 返回相应的报告信息
     */
    DtoReport findByCode(String code);

    /**
     * 获取报告
     *
     * @param id 报告id
     * @return 报告
     */
    DtoReport findAttachPath(String id);

    /**
     * 生成报告编号
     *
     * @param reportName  报告名称
     * @param year        报告年份
     * @param projectCode 项目编号
     * @param reportId    报告id
     * @param reportCode  报告编号
     * @return 返回生成的报告编号
     */
    String createReportCode(String reportName, Integer year, String projectCode, String reportId, String reportCode);

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    void reportSignal(DtoWorkflowSign dtoWorkflowSign);


    /**
     * 修改报告数
     *
     * @param id        报告id
     * @param reportNum 报告数
     */
    void updateReportNum(String id, Integer reportNum);

    /**
     * 根据报告id获取报告下的送样单
     *
     * @param reportId 报告id
     * @return 送样单列表
     */
    List<DtoReceiveSampleRecord> findReceiveSampleRecord(String reportId);

    /**
     * 根据报告id获取报告下的检测单
     *
     * @param reportId 报告id
     * @return 送样单列表
     */
    List<DtoWorkSheetFolder> findWorksheetFolder(String reportId);

    /**
     * 获取报表名称
     *
     * @param map      生成报表id
     * @param configId 报表配置名称
     * @return 报表名称
     */
    DtoReportConfig documentFileName(Map<String, Object> map, String configId);

    /**
     * 校验报告所选样品对应的质控样中是否有不合格的样品
     *
     * @param reportIdList 报告id列表
     * @return 不合格样品信息
     */
    String checkQcSamplePass(PageBean<DtoQcSampleEvaluateDetail> pageBean, List<String> reportIdList);

    /**
     * 保存点位排序
     *
     * @param reportId         报告id
     * @param sortFolderIdList 点位id列表
     * @return 点位信息
     */
    void saveSortFolder(String reportId, List<String> sortFolderIdList);

    /**
     * 生成报告编号（新）
     *
     * @param reportNumberPool 报告编号池实体
     * @return ReportNumberPool
     */
    DtoReportNumberPool createCode(DtoReportNumberPool reportNumberPool);

    /**
     * 手动报告签名
     *
     * @param map 报告id
     */
    void reportSign(Map<String, Object> map);

    /**
     * 重制报告
     *
     * @param reportId 报告id
     */
    void reloadReport(String reportId);


    /**
     * 获取所有已记录的收件人信息
     * @return 已记录的收件人信息
     */
    List<Map<String,Object>> getExpressAgeInfo();
}