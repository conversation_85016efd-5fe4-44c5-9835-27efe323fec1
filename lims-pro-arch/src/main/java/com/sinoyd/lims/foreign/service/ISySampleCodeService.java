package com.sinoyd.lims.foreign.service;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;

import java.util.Date;
import java.util.Map;

/**
 * 送样类码服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/28
 **/
public interface ISySampleCodeService {

    /**
     * 直接生成原样编号
     *
     * @param projectId         项目信息id
     * @param projectTypeId     项目类型id
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采样时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param sample            样品信息
     * @param extendParams       扩展参数
     * @return 返回相应的编号信息
     */
    String createSampleCode(String projectId,
                            String projectTypeId,
                            String sampleTypeId,
                            String sampleFolderId,
                            Date samplingTimeBegin,
                            Boolean isCreate,
                            String currentUserId,
                            DtoTestPost testPost,
                            DtoReceiveSampleRecord record,
                            DtoSample sample,
                            Map<String, Object> extendParams);

    /**
     * 一般生成外部质控样的数据可以调用该方法
     *
     * @param projectId           项目Id
     * @param projectTypeId       项目类型id
     * @param sampleTypeId        样品类型id
     * @param sampleFolderId      点位Id
     * @param samplingTimeBegin   采用开始时间
     * @param isCreate            清除样品编号，是否重新创建样品流水号
     * @param currentUserId       当前人员id
     * @param isQC                是否质控
     * @param associateSampleId   原样信息Id
     * @param qcId                质控id
     * @param sampleCategory      样品类别
     * @param associateSampleCode 原样编号
     * @param extendParams         扩展参数
     * @return 返回样品编号
     */
    DtoGenerateSN createSampleCode(String projectId,
                                   String projectTypeId,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Map<String, Object> extendParams);

    /**
     * 一般生成外部质控样的数据可以调用该方法
     *
     * @param dtoProject        项目信息
     * @param dtoProjectType    项目类型
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采用时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param isQC              是否质控
     * @param associateSampleId 原样信息Id
     * @param qcId              质控id
     * @param sampleCategory    样品类别
     * @param extendParams       扩展参数
     * @return 返回样品编号
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams);

    /**
     * @param dtoProject          项目信息
     * @param dtoProjectType      项目类型
     * @param dtoSampleType       样品类型信息（小类）
     * @param dtoSampleTypeParent 样品类型信息（大类）
     * @param dtoSampleFolder     点位信息
     * @param samplingTimeBegin   采用时间
     * @param isCreate            清除样品编号，是否重新创建样品流水号
     * @param currentUserId       当前人员id
     * @param isQC                是否质控
     * @param associateSampleId   原样信息Id
     * @param qcId                质控id
     * @param sampleCategory      样品类别
     * @param extendParams        扩展参数
     * @return 返回样品编号
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams);
}

