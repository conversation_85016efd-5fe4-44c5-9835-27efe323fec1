package com.sinoyd.lims.foreign.service;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;

import java.util.Date;
import java.util.Map;

/**
 * 样品编号生成服务
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/27
 **/
public interface ISampleCodeService {


    /**
     * 一般生成外部质控样的数据可以调用该方法
     *
     * @param dtoProject        项目信息
     * @param dtoProjectType    项目类型
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采用时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param isQC              是否质控
     * @param associateSampleId 原样信息Id
     * @param qcId              质控id
     * @param sampleCategory    样品类别
     * @return 返回样品编号
     */
    String createSampleCode(DtoProject dtoProject,
                            DtoProjectType dtoProjectType,
                            DtoTestPost testPost,
                            DtoReceiveSampleRecord record,
                            String sampleTypeId,
                            String sampleFolderId,
                            Date samplingTimeBegin,
                            Boolean isCreate,
                            String currentUserId,
                            Boolean isQC,
                            String associateSampleId,
                            String qcId,
                            Integer sampleCategory,
                            String associateSampleCode,
                            Map<String, Object> extendParams);

    /**
     * 一般生成外部质控样的数据可以调用该方法
     *
     * @param dtoProject        项目信息
     * @param dtoProjectType    项目类型
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采用时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param isQC              是否质控
     * @param associateSampleId 原样信息Id
     * @param qcId              质控id
     * @param sampleCategory    样品类别
     * @param isAutoCommitSN    是否自动提交流水号
     * @param extendParams      扩展参数
     * @return 返回样品编号
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   String samplingPersonId,
                                   String sampleId,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   Boolean isAutoCommitSN,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams);

    /**
     * 一般生成外部质控样的数据可以调用该方法
     *
     * @param dtoProject        项目信息
     * @param dtoProjectType    项目类型
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采用时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param isQC              是否质控
     * @param associateSampleId 原样信息Id
     * @param qcId              质控id
     * @param sampleCategory    样品类别
     * @param qcType            质控类型
     * @param qcGrade           质控等级
     * @param isAutoCommitSN    是否自动提交流水号
     * @param extendParams       扩展参数
     * @return 返回样品编号
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   String samplingPersonId,
                                   String sampleId,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   Integer qcType, Integer qcGrade,
                                   Boolean isAutoCommitSN,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams);

    /**
     * 一般生成外部质控样的数据可以调用该方法
     *
     * @param projectId           项目Id
     * @param projectTypeId       项目类型id
     * @param sampleTypeId        样品类型id
     * @param sampleFolderId      点位Id
     * @param samplingTimeBegin   采用开始时间
     * @param isCreate            清除样品编号，是否重新创建样品流水号
     * @param currentUserId       当前人员id
     * @param isQC                是否质控
     * @param associateSampleId   原样信息Id
     * @param qcId                质控id
     * @param sampleCategory      样品类别
     * @param associateSampleCode 原样编号
     * @param extendParams 扩展参数
     * @return 返回样品编号
     */
    String createSampleCode(String projectId,
                            String projectTypeId,
                            String sampleTypeId,
                            String sampleFolderId,
                            Date samplingTimeBegin,
                            Boolean isCreate,
                            String currentUserId,
                            Boolean isQC,
                            String associateSampleId,
                            String qcId,
                            Integer sampleCategory,
                            String associateSampleCode,
                            DtoTestPost testPost,
                            DtoReceiveSampleRecord record,
                            Map<String, Object> extendParams);

    /**
     * @param dtoProject          项目信息
     * @param dtoProjectType      项目类型
     * @param dtoSampleType       样品类型信息（小类）
     * @param dtoSampleTypeParent 样品类型信息（大类）
     * @param dtoSampleFolder     点位信息
     * @param samplingTimeBegin   采用时间
     * @param isCreate            清除样品编号，是否重新创建样品流水号
     * @param currentUserId       当前人员id
     * @param isQC                是否质控
     * @param associateSampleId   原样信息Id
     * @param qcId                质控id
     * @param sampleCategory      样品类别
     * @param extendParams 扩展参数
     * @return 返回样品编号
     */
    String createSampleCode(DtoProject dtoProject,
                            DtoProjectType dtoProjectType,
                            DtoSampleType dtoSampleType,
                            DtoSampleType dtoSampleTypeParent,
                            DtoSampleFolder dtoSampleFolder,
                            DtoTestPost testPost,
                            DtoReceiveSampleRecord record,
                            Date samplingTimeBegin, Boolean isCreate, String currentUserId,
                            Boolean isQC, String associateSampleId, String qcId, Integer sampleCategory,
                            String associateSampleCode,
                            Map<String, Object> extendParams);

    /**
     * 直接生成原样编号
     *
     * @param dtoProject        项目信息
     * @param dtoProjectType    项目类型信息
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采样时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param extendParams      扩展参数
     * @return 返回相应的编号信息
     */
    String createSampleCode(DtoProject dtoProject,
                            DtoProjectType dtoProjectType,
                            DtoTestPost testPost,
                            DtoReceiveSampleRecord record,
                            String sampleTypeId,
                            String sampleFolderId,
                            Date samplingTimeBegin,
                            Boolean isCreate,
                            String currentUserId,
                            Map<String, Object> extendParams);


    /**
     * 直接生成原样编号
     *
     * @param projectId         项目信息id
     * @param projectTypeId     项目类型id
     * @param sampleTypeId      样品类型id
     * @param sampleFolderId    点位id
     * @param samplingTimeBegin 采样时间
     * @param isCreate          清除样品编号，是否重新创建样品流水号
     * @param currentUserId     当前人员id
     * @param extendParams      扩展参数
     * @return 返回相应的编号信息
     */
    String createSampleCode(String projectId,
                            String projectTypeId,
                            String sampleTypeId,
                            String sampleFolderId,
                            Date samplingTimeBegin,
                            Boolean isCreate,
                            String currentUserId,
                            DtoTestPost testPost,
                            DtoReceiveSampleRecord record,
                            Map<String, Object> extendParams);

    /**
     * 直接生成原样编号
     *
     * @param dtoProject        项目信息
     * @param dtoProjectType    项目类型信息
     * @param sampleTypeId      样品类型信息
     * @param sampleFolderId    样品点位
     * @param samplingTimeBegin 采样时间
     * @param isCreate          是否重新创建样编号
     * @param currentUserId     当前人id
     * @param isAutoCommitSN    是否自动提交流水号
     * @param extendParams      扩展参数
     * @return 返回信息
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   String samplingPersonId,
                                   String sampleId,
                                   Boolean isCreate,
                                   String currentUserId, Boolean isAutoCommitSN,
                                   Map<String, Object> extendParams);

    /**
     * s
     *
     * @param dtoProject          项目信息
     * @param dtoProjectType      项目类型信息
     * @param dtoSampleType       样品类型信息
     * @param dtoSampleTypeParent 样品类型信息（大类）
     * @param dtoSampleFolder     点位信息
     * @param samplingTimeBegin   采样时间
     * @param isCreate            清除样品编号，是否重新创建样品流水号
     * @param currentUserId       当前人员id
     * @param extendParams        扩展参数
     * @return 返回相应的编号信息
     */
    String createSampleCode(DtoProject dtoProject,
                            DtoProjectType dtoProjectType,
                            DtoSampleType dtoSampleType,
                            DtoSampleType dtoSampleTypeParent,
                            DtoSampleFolder dtoSampleFolder,
                            Date samplingTimeBegin, Boolean isCreate, String currentUserId,
                            Map<String, Object> extendParams);

    /**
     * s
     *
     * @param dtoProject          项目信息
     * @param dtoProjectType      项目类型信息
     * @param dtoSampleType       样品类型信息
     * @param dtoSampleTypeParent 样品类型信息（大类）
     * @param dtoSampleFolder     点位信息
     * @param samplingTimeBegin   采样时间
     * @param isCreate            清除样品编号，是否重新创建样品流水号
     * @param currentUserId       当前人员id
     * @param isAutoCommitSN      是否自动提交流水号
     * @param extendParams        扩展参数
     * @return 返回相应的编号信息
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isAutoCommitSN,
                                   Map<String, Object> extendParams);

    /***
     * 创建样品编号
     * @param dtoProject 项目信息
     * @param dtoProjectType 项目类型
     * @param dtoSampleType 检测类型
     * @param dtoSampleTypeParent 父类类型
     * @param dtoSampleFolder 点位
     * @param samplingTimeBegin 采用时间
     * @param isCreate   清除样品编号，是否重新创建样品流水号
     * @param currentUserId 当前人员id
     * @param isQC 是否质控
     * @param associateSampleId 原样id
     * @param qcId 质控id
     * @param sampleCategory 样品分类
     * @param  qcType 质控类型
     * @param  qcGrade 质控等级
     * @param isAutoCommitSN 是否自动提交流水号
     * @param associateSampleCode 原样编号
     * @param extendParams 扩展参数
     * @return 返回数据
     */
    DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   Integer qcType, Integer qcGrade,
                                   Boolean isAutoCommitSN,
                                   String associateSampleCode,
                                   Map<String, Object> extendParams);

    /**
     * 手动更新流水号
     *
     * @param sample 样品信息
     */
    void manualUpdateSerialNumber(DtoSample sample);
}
