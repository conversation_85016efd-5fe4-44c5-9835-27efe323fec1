package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.lims.qa.repository.LogRepository;
import com.sinoyd.lims.qa.service.LogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * Log操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/29
 * @since V100R001
 */
@Service("qaLogService")
public class LogServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoLog, String, LogRepository> implements LogService {

    @Override
    public void findByPage(PageBean<DtoLog> pb, BaseCriteria logCriteria) {
        pb.setEntityName("DtoLog a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, logCriteria);
    }

    @Transactional
    @Override
    public DtoLog generalSave(String operatorId, String operatorName, Date operateTime, String operateInfo, String nextOperatorId,
                              String nextOperatorName, String logType, String objectId, String objectType, String comment,
                              String opinion, String remark) {
        DtoLog dtoLog = new DtoLog();
        dtoLog.setOperatorId(operatorId);
        dtoLog.setOperatorName(operatorName);
        dtoLog.setOperateTime(operateTime);
        dtoLog.setOperateInfo(operateInfo);
        dtoLog.setNextOperatorId(nextOperatorId);
        dtoLog.setNextOperatorName(nextOperatorName);
        dtoLog.setLogType(logType);
        dtoLog.setObjectId(objectId);
        dtoLog.setObjectType(objectType);
        dtoLog.setComment(comment);
        dtoLog.setOpinion(opinion);
        dtoLog.setRemark(remark);
        return save(dtoLog);
    }

    @Override
    public List<DtoLog> findAll(DtoLog dtoLog) {
        return repository.findAllByObjectIdAndLogTypeOrderByOperateTimeDesc(dtoLog.getObjectId(),dtoLog.getLogType());
    }

    @Override
    public DtoLog getDtoLog(String objectId,String operateInfo,String nextOperatorId,String nextOperatorName,String logType, String objectType,String comment) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();//获取当前人的信息
        DtoLog dtoLog = new DtoLog();
        dtoLog.setOperatorId(currentUser.getUserId());//操作者id
        dtoLog.setOperatorName(currentUser.getUserName());//操作者名字
        dtoLog.setOperateTime(new Date());//操作时间
        dtoLog.setOperateInfo(operateInfo);//删除年度计划
        dtoLog.setNextOperatorId(nextOperatorId);
        dtoLog.setNextOperatorName(nextOperatorName);
        dtoLog.setLogType(logType);
        dtoLog.setObjectType(objectType);
        dtoLog.setObjectId(objectId);//对象id
        dtoLog.setComment(comment);
        return dtoLog;
    }

}