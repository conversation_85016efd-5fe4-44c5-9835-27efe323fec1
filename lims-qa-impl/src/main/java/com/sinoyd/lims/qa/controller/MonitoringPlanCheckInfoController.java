package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.MonitoringPlanCheckInfoService;
import com.sinoyd.lims.qa.criteria.MonitoringPlanCheckInfoCriteria;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlanCheckInfo;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * MonitoringPlanCheckInfo服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @Api(tags = "示例: MonitoringPlanCheckInfo服务")
 @RestController
 @RequestMapping("api/qa/monitoringPlanCheckInfo")
 @Validated
 public class MonitoringPlanCheckInfoController extends BaseJpaController<DtoMonitoringPlanCheckInfo, String,MonitoringPlanCheckInfoService> {


    /**
     * 分页动态条件查询MonitoringPlanCheckInfo
     * @param monitoringPlanCheckInfoCriteria 条件参数
     * @return RestResponse<List<MonitoringPlanCheckInfo>>
     */
     @ApiOperation(value = "分页动态条件查询MonitoringPlanCheckInfo", notes = "分页动态条件查询MonitoringPlanCheckInfo")
     @GetMapping
     public RestResponse<List<DtoMonitoringPlanCheckInfo>> findByPage(MonitoringPlanCheckInfoCriteria monitoringPlanCheckInfoCriteria) {
         PageBean<DtoMonitoringPlanCheckInfo> pageBean = super.getPageBean();
         RestResponse<List<DtoMonitoringPlanCheckInfo>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, monitoringPlanCheckInfoCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询MonitoringPlanCheckInfo
     * @param id 主键id
     * @return RestResponse<DtoMonitoringPlanCheckInfo>
     */
     @ApiOperation(value = "按主键查询MonitoringPlanCheckInfo", notes = "按主键查询MonitoringPlanCheckInfo")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoMonitoringPlanCheckInfo> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoMonitoringPlanCheckInfo> restResponse = new RestResponse<>();
         DtoMonitoringPlanCheckInfo monitoringPlanCheckInfo = service.findOne(id);
         restResponse.setData(monitoringPlanCheckInfo);
         restResponse.setRestStatus(StringUtil.isNull(monitoringPlanCheckInfo) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增MonitoringPlanCheckInfo
     * @param monitoringPlanCheckInfo 实体列表
     * @return RestResponse<DtoMonitoringPlanCheckInfo>
     */
     @ApiOperation(value = "新增MonitoringPlanCheckInfo", notes = "新增MonitoringPlanCheckInfo")
     @PostMapping
     public RestResponse<DtoMonitoringPlanCheckInfo> create(@Validated @RequestBody DtoMonitoringPlanCheckInfo monitoringPlanCheckInfo) {
         RestResponse<DtoMonitoringPlanCheckInfo> restResponse = new RestResponse<>();
         restResponse.setData(service.save(monitoringPlanCheckInfo));
         return restResponse;
      }

     /**
     * 新增MonitoringPlanCheckInfo
     * @param monitoringPlanCheckInfo 实体列表
     * @return RestResponse<DtoMonitoringPlanCheckInfo>
     */
     @ApiOperation(value = "修改MonitoringPlanCheckInfo", notes = "修改MonitoringPlanCheckInfo")
     @PutMapping
     public RestResponse<DtoMonitoringPlanCheckInfo> update(@Validated @RequestBody DtoMonitoringPlanCheckInfo monitoringPlanCheckInfo) {
         RestResponse<DtoMonitoringPlanCheckInfo> restResponse = new RestResponse<>();
         restResponse.setData(service.update(monitoringPlanCheckInfo));
         return restResponse;
      }

    /**
     * "根据id批量删除MonitoringPlanCheckInfo
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除MonitoringPlanCheckInfo", notes = "根据id批量删除MonitoringPlanCheckInfo")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }