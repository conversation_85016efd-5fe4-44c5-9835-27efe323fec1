package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.MonitoringPlanService;
import com.sinoyd.lims.qa.criteria.MonitoringPlanCriteria;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlan;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * MonitoringPlan服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @Api(tags = "示例: MonitoringPlan服务")
 @RestController
 @RequestMapping("api/qa/monitoringPlan")
 @Validated
 public class MonitoringPlanController extends BaseJpaController<DtoMonitoringPlan, String,MonitoringPlanService> {


    /**
     * 分页动态条件查询MonitoringPlan
     * @param monitoringPlanCriteria 条件参数
     * @return RestResponse<List<MonitoringPlan>>
     */
     @ApiOperation(value = "分页动态条件查询MonitoringPlan", notes = "分页动态条件查询MonitoringPlan")
     @GetMapping
     public RestResponse<List<DtoMonitoringPlan>> findByPage(MonitoringPlanCriteria monitoringPlanCriteria) {
         PageBean<DtoMonitoringPlan> pageBean = super.getPageBean();
         RestResponse<List<DtoMonitoringPlan>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, monitoringPlanCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询MonitoringPlan
     * @param id 主键id
     * @return RestResponse<DtoMonitoringPlan>
     */
     @ApiOperation(value = "按主键查询MonitoringPlan", notes = "按主键查询MonitoringPlan")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoMonitoringPlan> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoMonitoringPlan> restResponse = new RestResponse<>();
         DtoMonitoringPlan monitoringPlan = service.findOne(id);
         restResponse.setData(monitoringPlan);
         restResponse.setRestStatus(StringUtil.isNull(monitoringPlan) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增MonitoringPlan
     * @param monitoringPlan 实体列表
     * @return RestResponse<DtoMonitoringPlan>
     */
     @ApiOperation(value = "新增MonitoringPlan", notes = "新增MonitoringPlan")
     @PostMapping
     public RestResponse<DtoMonitoringPlan> create(@Validated @RequestBody DtoMonitoringPlan monitoringPlan) {
         RestResponse<DtoMonitoringPlan> restResponse = new RestResponse<>();
         restResponse.setData(service.save(monitoringPlan));
         return restResponse;
      }

     /**
     * 新增MonitoringPlan
     * @param monitoringPlan 实体列表
     * @return RestResponse<DtoMonitoringPlan>
     */
     @ApiOperation(value = "修改MonitoringPlan", notes = "修改MonitoringPlan")
     @PutMapping
     public RestResponse<DtoMonitoringPlan> update(@Validated @RequestBody DtoMonitoringPlan monitoringPlan) {
         RestResponse<DtoMonitoringPlan> restResponse = new RestResponse<>();
         restResponse.setData(service.update(monitoringPlan));
         return restResponse;
      }

    /**
     * "根据id批量删除MonitoringPlan
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除MonitoringPlan", notes = "根据id批量删除MonitoringPlan")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交质量监督计划
     *
     * @param dtoMonitoringPlan 实体列表
     * @return RestResponse<DtoMonitoringPlan>
     */
    @ApiOperation(value = "提交AnnualPlan", notes = "提交AnnualPlan")
    @PutMapping(path = "/submit")
    public RestResponse<DtoMonitoringPlan> submit(@RequestBody DtoMonitoringPlan dtoMonitoringPlan) {
        RestResponse<DtoMonitoringPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(dtoMonitoringPlan));
        return restResponse;
    }

    /**
     * 审核质量监督计划
     *
     * @param dtoMonitoringPlan 实体
     * @return RestResponse<DtoMonitoringPlan>
     */
    @ApiOperation(value = "审核DtoMonitoringPlan", notes = "审核DtoMonitoringPlan")
    @PutMapping(path = "/audit")
    public RestResponse<DtoMonitoringPlan> audit(@RequestBody DtoMonitoringPlan dtoMonitoringPlan) {
        RestResponse<DtoMonitoringPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.audit(dtoMonitoringPlan));
        return restResponse;
    }

    /**
     * 审核质量监督记录
     *
     * @param dtoMonitoringPlan 实体
     * @return RestResponse<DtoMonitoringPlan>
     */
    @ApiOperation(value = "审核DtoMonitoringPlan", notes = "审核DtoMonitoringPlan")
    @PutMapping(path = "/auditRecord")
    public RestResponse<DtoMonitoringPlan> auditRecord(@RequestBody DtoMonitoringPlan dtoMonitoringPlan) {
        RestResponse<DtoMonitoringPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.auditRecord(dtoMonitoringPlan));
        return restResponse;
    }

 }