package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.entity.Person;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.*;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.CustomerComplaintRegistRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.CustomerComplaintRegistService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * CustomerComplaintRegist操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class CustomerComplaintRegistServiceImpl extends BaseJpaServiceImpl<DtoCustomerComplaintRegist, String, CustomerComplaintRegistRepository> implements CustomerComplaintRegistService {

    private final LogService logService;

    private final SubmitRecordService submitRecordService;

    private final SubmitRecordRepository submitRecordRepository;

    private final CodeService codeService;

    private final PersonRepository personRepository;

    @Override
    public void findByPage(PageBean<DtoCustomerComplaintRegist> pb, BaseCriteria customerComplaintRegistCriteria) {
        pb.setEntityName("DtoCustomerComplaintRegist a,DtoSubmitRecordQa b");
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoCustomerComplaintRegist(" +
                "a.id,a.complaintName,a.complaintPerson,a.type,a.complaintDate,a.registPerson,a.registPersonId," +
                "a.registDate,a.level,a.finshDate,a.status)");
        comRepository.findByPage(pb, customerComplaintRegistCriteria);
        //获取查询到的所有不符合项
        List<DtoCustomerComplaintRegist> customerComplaintRegists = pb.getData();
        if (StringUtil.isNotEmpty(customerComplaintRegists)) {
            //获取所有实体id
            List<String> ids = customerComplaintRegists.stream().map(DtoCustomerComplaintRegist::getId).collect(Collectors.toList());
            List<String> levelCodeStr = customerComplaintRegists.stream().map(DtoCustomerComplaintRegist::getLevel).collect(Collectors.toList());
            List<String> typeStr = customerComplaintRegists.stream().map(DtoCustomerComplaintRegist::getType).collect(Collectors.toList());
            List<DtoCode> levelCodes = codeService.findByCodes(levelCodeStr);
            List<DtoCode> typeCodes = codeService.findByCodes(typeStr);
            //根据对象类型和id拿到记录对象集合
            List<DtoSubmitRecordQa> submitRecords = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumQAObjType.客户投诉管理.getValue(), ids);
            //遍历填充填充意见
            for (DtoCustomerComplaintRegist item : customerComplaintRegists) {
                //根据对象id拿绑定的记录对象集合
                if (StringUtil.isNotEmpty(submitRecords)) {
                    List<DtoSubmitRecordQa> submitRecordOfItem = submitRecords.stream()
                            .filter(s -> s.getObjectId().equals(item.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(submitRecordOfItem)) {
                        //集合按照记录时间倒序排序
                        submitRecordOfItem.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        //填充最新的一个意见，这里设置数据之后就不用调用page.setData了，因为通过引用修改的就是page对象下的数据
                        item.setSubmitRemark(submitRecordOfItem.get(0).getSubmitRemark());
                    }
                }
                if (StringUtil.isNotEmpty(levelCodes)) {
                    levelCodes.forEach(l -> {
                        if (l.getDictCode().equals(item.getLevel())) {
                            item.setLevelName(l.getDictName());
                        }
                    });
                }
                if (StringUtil.isNotEmpty(typeCodes)) {
                    typeCodes.forEach(t -> {
                        if (t.getDictCode().equals(item.getType())) {
                            item.setTypeName(t.getDictName());
                        }
                    });
                }
            }
        }
    }

    /**
     * 客户投诉新增方法：
     * 新增投诉时登记人默认为当前用户，登记时间默认为当前时间
     *
     * @param entity 保存的客户投诉实体
     * @return 保存后的实体
     */
    @Transactional
    @Override
    public DtoCustomerComplaintRegist save(DtoCustomerComplaintRegist entity) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //前台不传则默认为当前登录人
        if (StringUtil.isNotEmpty(entity.getRegistPerson())) {
            //前台传回的registerPerson实际为登录人id，这里转换一下
            entity.setRegistPersonId(entity.getRegistPerson());
            Person person = personRepository.findOne(entity.getRegistPersonId());
            entity.setRegistPerson(person.getCName());
        } else {
            entity.setRegistPerson(currentUser.getUserName());
            entity.setRegistPersonId(currentUser.getUserId());
        }
        if (StringUtil.isEmpty(entity.getComplaintPersonId())) {
            //前台投诉人为输入框，无法获取投诉人Id，数据库中字段为必输，填充空的uuid
            entity.setComplaintPersonId(UUIDHelper.GUID_EMPTY);
        }
        //如果没有投诉时间则默认填充当前时间
        if (entity.getComplaintDate() == null) {
            entity.setComplaintDate(new Date());
        }
        //如果没有登记时间则默认填充当前时间
        if (entity.getRegistDate() == null) {
            entity.setRegistDate(new Date());
        }
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.客户投诉管理.getValue(), EnumQA.EnumQASubmitType.客户投诉登记.getValue(),
                null, null, EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name(), EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name());
        //新增之后默认状态
        entity.setStatus(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增客户投诉.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.客户投诉流程.name(), entity.getId(), EnumQA.EnumLogObjectType.客户投诉.name(),
                currentUser.getUserName() + "新增客户投诉", "", "");
        return super.save(entity);
    }

    /**
     * 只能修改登记人为当前登录用户的投诉数据，投诉提交之后不可修改
     *
     * @param entity 客户投诉实体
     * @return 更新后的实体
     */
    @Transactional
    @Override
    public DtoCustomerComplaintRegist update(DtoCustomerComplaintRegist entity) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        if (!currentUser.getUserId().equals(entity.getRegistPersonId())) {
            throw new BaseException("只能修改自己登记的客户投诉");
        }
        if (StringUtil.isNotEmpty(entity.getRegistPersonId())) {
            Person person = personRepository.findOne(entity.getRegistPersonId());
            entity.setRegistPerson(person.getCName());
        }
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.编辑客户投诉.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.客户投诉流程.name(), entity.getId(), EnumQA.EnumLogObjectType.客户投诉.name(),
                currentUser.getUserName() + "修改客户投诉", "", "");
        return super.update(entity);
    }

    /**
     * 客户投诉删除方法：
     * 1.只能删除登记人为登录人的投诉
     *
     * @param ids 要删除的实体id集合
     * @return 删除实体计数
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //根据id集合获取实体
        List<DtoCustomerComplaintRegist> complaintRegists = super.findAll((Collection<String>) ids);
        //便利实体，判断登记人是否为当前登录用户
        for (DtoCustomerComplaintRegist item : complaintRegists) {
            if (!EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name().equals(item.getStatus())) {
                throw new BaseException("客户投诉已提交，不能删除。");
            }
            if (!currentUser.getUserId().equals(item.getRegistPersonId())) {
                throw new BaseException("只能删除自己登记的客户投诉");
            }
        }
        List<String> idList = (List<String>) ids;
        List<DtoLog> logList = new ArrayList<>();
        for (String id : idList) {
            logList.add(logService.getDtoLog(id, EnumQA.EnumLogOperateType.删除客户投诉.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.客户投诉流程.name(),
                    EnumQA.EnumLogObjectType.客户投诉.name(), currentUser.getUserName() + "删除了客户投诉"));
        }
        logService.save(logList);
        return super.logicDeleteById(ids);
    }

    /**
     * 客户投诉提交
     * 1.不可重复提交
     * 2.提交时必须指定下一步操作人
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return 提交的实体
     */
    @Transactional
    @Override
    public DtoCustomerComplaintRegist submit(DtoCustomerComplaintRegist dtoCustomerComplaintRegist) {
        //获取数据库中的实体
        DtoCustomerComplaintRegist customerComplaintRegist = super.findOne(dtoCustomerComplaintRegist.getId());
        if (customerComplaintRegist == null) {
            throw new BaseException("未找到当前投诉数据，请刷新页面重试");
        }
        if (!EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name().equals(customerComplaintRegist.getStatus())) {
            throw new BaseException("客户投诉已提交，不可重复提交");
        }
        //获取当前登录用户
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoPerson person = personRepository.findOne(dtoCustomerComplaintRegist.getNextOperator());
        String nextOperatorName = "";
        if (StringUtils.isNotNull(person)) {
            nextOperatorName = person.getCName();
        }
        submitRecordService.createSubmitRecord(customerComplaintRegist.getId(), EnumQA.EnumQAObjType.客户投诉管理.getValue(), EnumQA.EnumQASubmitType.客户投诉提交.getValue(),
                dtoCustomerComplaintRegist.getNextOperator(), null, EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name(), EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name());
        //更新当前状态
        customerComplaintRegist.setStatus(EnumQA.EnumCustomerComplaintRegistStatus.投诉审核中.name());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交客户投诉.name(), UUIDHelper.GUID_EMPTY,
                "", EnumQA.EnumLogType.客户投诉流程.name(), customerComplaintRegist.getId(), EnumQA.EnumQAObjType.客户投诉管理.name(),
                currentUser.getUserName() + "提交了客户投诉，下一步操作人：" + nextOperatorName, "", "");
        return repository.save(customerComplaintRegist);
    }

    /**
     * 客户投诉审核:
     * 1.必须先提交再审核
     * 2.只能审核指定自己审核的投诉
     * 3.审核通过记录下一步操作人
     * 4.审核不通过记录原因，状态置为办结
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return 审核的实体
     */
    @Transactional
    @Override
    public DtoCustomerComplaintRegist audit(DtoCustomerComplaintRegist dtoCustomerComplaintRegist) {
        //获取数据库中的实体
        DtoCustomerComplaintRegist customerComplaintRegist = super.findOne(dtoCustomerComplaintRegist.getId());
        //获取当前登录用户
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        String status;
        String stateTo;
        String nextOperate = "";
        //审核未通过状态置为办结
        if (!Optional.ofNullable(dtoCustomerComplaintRegist.getIsPassCompleteFlag()).isPresent()) {
            throw new BaseException("必须传回通过标示字段");
        } else {
            //通过审核必须选择下一步操作人
            if (dtoCustomerComplaintRegist.getIsPassCompleteFlag()) {
                if (StringUtil.isEmpty(dtoCustomerComplaintRegist.getNextOperator())) {
                    throw new BaseException("下一步操作人必填");
                }
                status = EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name();
                stateTo = EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name();
                DtoPerson person = personRepository.findOne(dtoCustomerComplaintRegist.getNextOperator());
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "审核了客户投诉，下一步操作人：" + person.getCName()+"。";
                }
            }
            //不通过直接办结，必须填写原因
            else {
                if (StringUtil.isEmpty(dtoCustomerComplaintRegist.getSubmitRemark())) {
                    throw new BaseException("原因为必填");
                }
                status = EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name();
                stateTo = EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name();
                nextOperate = "审核了客户投诉，审核不通过。";
            }
            if(StringUtil.isNotEmpty(dtoCustomerComplaintRegist.getSubmitRemark())){
                nextOperate += " 意见："+dtoCustomerComplaintRegist.getSubmitRemark();
            }
        }
        // 设置投诉状态
        customerComplaintRegist.setStatus(status);
        // 创建记录对象
        submitRecordService.createSubmitRecord(customerComplaintRegist.getId(), EnumQA.EnumQAObjType.客户投诉管理.getValue(), EnumQA.EnumQASubmitType.客户投诉审核.getValue(),
                dtoCustomerComplaintRegist.getNextOperator(), dtoCustomerComplaintRegist.getSubmitRemark(), EnumQA.EnumCustomerComplaintRegistStatus.投诉审核中.name(), stateTo);
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核客户投诉.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.客户投诉流程.name(), dtoCustomerComplaintRegist.getId(), EnumQA.EnumLogObjectType.客户投诉.name(),
                currentUser.getUserName() + nextOperate, "", "");
        return repository.save(customerComplaintRegist);
    }

    /**
     * 客户投诉确认，直接提交，只改变状态，下一步操作人为登记人员
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return 确认的实体
     */
    @Transactional
    @Override
    public DtoCustomerComplaintRegist confirm(DtoCustomerComplaintRegist dtoCustomerComplaintRegist) {
        //获取数据库中的实体
        DtoCustomerComplaintRegist customerComplaintRegist = super.findOne(dtoCustomerComplaintRegist.getId());
        //获取当前登录用户
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        customerComplaintRegist.setStatus(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
        String nextOperatorName = "";
        DtoPerson person = personRepository.findOne(customerComplaintRegist.getRegistPersonId());
        if (StringUtils.isNotNull(person)){
            nextOperatorName = person.getCName()+"。";
        }
        if(StringUtil.isNotEmpty(dtoCustomerComplaintRegist.getSubmitRemark())){
            nextOperatorName += " 意见："+dtoCustomerComplaintRegist.getSubmitRemark();
        }
        submitRecordService.createSubmitRecord(customerComplaintRegist.getId(), EnumQA.EnumQAObjType.客户投诉管理.getValue(), EnumQA.EnumQASubmitType.客户投诉确认.getValue(),
                customerComplaintRegist.getRegistPersonId(), dtoCustomerComplaintRegist.getSubmitRemark(), EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name(), EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.确认客户投诉.name(), UUIDHelper.GUID_EMPTY,
                "", EnumQA.EnumLogType.客户投诉流程.name(), customerComplaintRegist.getId(), EnumQA.EnumQAObjType.客户投诉管理.name(),
                currentUser.getUserName() + "确认了客户投诉，下一步操作人：" + nextOperatorName, "", "");
        return repository.save(customerComplaintRegist);
    }

    /**
     * 办结客户审核
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return 办结的实体
     */
    @Transactional
    @Override
    public DtoCustomerComplaintRegist complete(DtoCustomerComplaintRegist dtoCustomerComplaintRegist) {

        //获取数据库中的投诉实体
        DtoCustomerComplaintRegist customerComplaintRegist = super.findOne(dtoCustomerComplaintRegist.getId());
        //获取当前登录用户
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        String stateTo = "";
        String status = "";
        String nextOperate = "";
        String nextOperatorId = "";
        if (Optional.ofNullable(dtoCustomerComplaintRegist.getIsPassCompleteFlag()).isPresent()) {
            if (dtoCustomerComplaintRegist.getIsPassCompleteFlag()) {
                //设置状态为办结通过
                status = EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name();
                stateTo = EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name();
                nextOperatorId = customerComplaintRegist.getCreator();
                DtoPerson person = personRepository.findOne(nextOperatorId);
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "办结了客户投诉，下一步操作人："+person.getCName()+"。";
                }
            } else {
                status = EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name();
                stateTo = EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name();
                //办结不通过下一步操作人为投诉审核时指定的下一步操作人
                DtoSubmitRecordQa submitRecord = submitRecordService.findSubmitRecordByObjIdAndSubmitType(
                        customerComplaintRegist.getId(), EnumQA.EnumQASubmitType.客户投诉审核.getValue());
                DtoPerson person = personRepository.findOne(submitRecord.getNextPerson());
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "办结了客户投诉，办结不通过，下一步操作人："+person.getCName()+"。";
                    nextOperatorId = person.getId();
                }
            }
            if(StringUtil.isNotEmpty(dtoCustomerComplaintRegist.getSubmitRemark())){
                nextOperate += " 意见："+dtoCustomerComplaintRegist.getSubmitRemark();
            }
        }
        customerComplaintRegist.setStatus(status);
        submitRecordService.createSubmitRecord(customerComplaintRegist.getId(), EnumQA.EnumQAObjType.客户投诉管理.getValue(), EnumQA.EnumQASubmitType.客户投诉办结.getValue(),
                nextOperatorId, dtoCustomerComplaintRegist.getSubmitRemark(), EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name(),
                stateTo);
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.办结客户投诉.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.客户投诉流程.name(), dtoCustomerComplaintRegist.getId(), EnumQA.EnumLogObjectType.客户投诉.name(),
                currentUser.getUserName() + nextOperate, "", "");
        return repository.save(customerComplaintRegist);
    }

    @Override
    public DtoCustomerComplaintRegist findAttachPath(String id) {
        return findOne(id);
    }
}