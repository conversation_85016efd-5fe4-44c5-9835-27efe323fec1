package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.lims.qa.dto.DtoNotConformItem;
import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.NotConformItemRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.NotConformItemService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * NotConformItem操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class NotConformItemServiceImpl extends BaseJpaServiceImpl<DtoNotConformItem, String, NotConformItemRepository> implements NotConformItemService {

    private final LogService logService;

    private final SubmitRecordRepository submitRecordRepository;

    private final SubmitRecordService submitRecordService;

    private final CodeService codeService;

    private final DepartmentService departmentService;

    private final PersonRepository personRepository;

    @Override
    public void findByPage(PageBean<DtoNotConformItem> pb, BaseCriteria notConformItemCriteria) {
        pb.setEntityName("DtoNotConformItem a,DtoSubmitRecordQa b");
        //查询出下一步操作人
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoNotConformItem( " +
                "a.id,a.ncProduceDept, a.ncMainPerson, a.ncMainPersonId, " +
                "a.ncFindPerson,a.ncFindPersonId, a.ncFindDate, " +
                "a.ncType, a.sourceId, a.ncDescribe, a.ncSourceType, " +
                "a.creator,a.createDate, " +
                "a.status, b.nextPerson)");
        comRepository.findByPage(pb, notConformItemCriteria);
        //获取查询到的所有不符合项
        List<DtoNotConformItem> notConformItems = pb.getData();
        if (StringUtil.isNotEmpty(notConformItems)) {
            //获取所有实体id
            List<String> ids = notConformItems.stream().map(DtoNotConformItem::getId).collect(Collectors.toList());
            //获取枚举
            List<String> sourceIds = notConformItems.stream().map(DtoNotConformItem::getNcSourceType).collect(Collectors.toList());
            List<String> typeIds = notConformItems.stream().map(DtoNotConformItem::getNcType).collect(Collectors.toList());
            List<String> sourceTypeIds = notConformItems.stream().map(DtoNotConformItem::getNcSourceType).collect(Collectors.toList());
            List<DtoCode> sources = codeService.findByCodes(sourceIds);
            List<DtoCode> types = codeService.findByCodes(typeIds);
            List<DtoCode> sourceTypes = codeService.findByCodes(sourceTypeIds);
            List<DtoDepartment> dtoDepartments = departmentService.findAll();
            //根据对象类型和id拿到记录对象集合集合
            List<DtoSubmitRecordQa> submitRecords = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumQAObjType.不符合项管理.getValue(), ids);
            //遍历填充填充意见
            for (DtoNotConformItem item : notConformItems) {
                //根据对象id拿绑定的记录对象集合
                if (StringUtil.isNotEmpty(submitRecords)) {
                    List<DtoSubmitRecordQa> submitRecordOfItem = submitRecords.stream()
                            .filter(s -> s.getObjectId().equals(item.getId())).collect(Collectors.toList());
                    if (submitRecordOfItem.size() > 0) {
                        //集合按照记录时间倒序排序
                        submitRecordOfItem.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        //填充最新的一个意见，这里设置数据之后就不用调用page.setData了，因为通过引用修改的就是page对象下的数据
                        item.setSubmitRemark(submitRecordOfItem.get(0).getSubmitRemark());
                    }
                }
                if (StringUtil.isNotEmpty(sources)) {
                    sources.forEach(s -> {
                        if (s.getDictCode().equals(item.getNcSourceType())) {
                            item.setSourceName(s.getDictName());
                        }
                    });
                }
                if (StringUtil.isNotEmpty(types)) {
                    types.forEach(t -> {
                        if (t.getDictCode().equals(item.getNcType())) {
                            item.setTypeName(t.getDictName());
                        }
                    });
                }
                if (StringUtil.isNotEmpty(dtoDepartments)) {
                    dtoDepartments.forEach(d -> {
                        if (d.getId().equals(item.getNcProduceDept())) {
                            item.setDepartmentName(d.getDeptName());
                        }
                    });
                }
                if (StringUtil.isNotEmpty(sourceTypes)) {
                    sourceTypes.forEach(s -> {
                        if (s.getDictCode().equals(item.getNcSourceType())) {
                            item.setSourceTypeName(s.getDictName());
                        }
                    });
                }
            }
        }
    }

    /**
     * 不符合项新增方法，新增时填充必填项
     *
     * @param entity 保存的不符合项实体
     * @return 完成保存的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem save(DtoNotConformItem entity) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //负责人默认为当前人员
        if (StringUtil.isEmpty(entity.getNcMainPersonId())) {
            entity.setNcMainPersonId(currentPrincipalUser.getUserId());
            entity.setNcMainPerson(currentPrincipalUser.getUserName());
        }
        //发现人默认为当前人员
        if (StringUtil.isEmpty(entity.getNcFindPersonId())) {
            entity.setNcFindPersonId(currentPrincipalUser.getUserId());
            entity.setNcFindPerson(currentPrincipalUser.getUserName());
        }
        //实验室主任id不需要填写，暂时留作冗余字段，填充空uuid
        if (StringUtil.isEmpty(entity.getNcDeptPersonId())) {
            entity.setNcDeptPersonId(UUIDHelper.GUID_EMPTY);
        }
        //发现时间默认为当前时间
        if (entity.getNcFindDate() == null) {
            entity.setNcFindDate(new Date());
        }
        //对后面的必填信息进行预填充
        //不符合项原因分析
        entity.setNcCauseAnalysis("");
        //是否是潜在问题（0：不符合项，1：潜在不符合项及预防措施）
        entity.setPotential(0);
        //是否可恢复工作
        entity.setRestoreWork(0);
        //是否通知客户
        entity.setNotifyCustomer(0);
        //是否需要纠正措施
        entity.setCorrecteAction(0);
        //新增状态为登记中
        entity.setStatus(EnumQA.EnumNotConformItemStatus.不符合项登记中.name());
        //记录保存日志
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(),
                EnumQA.EnumLogOperateType.新增不符合项.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.不符合项登记流程.name(),
                entity.getId(), EnumQA.EnumLogObjectType.不符合项.name(), currentPrincipalUser.getUserName() + "新增不符合项", "", "");
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.不符合项管理.getValue(), EnumQA.EnumQASubmitType.不符合项登记.getValue(),
                null, null, EnumQA.EnumNotConformItemStatus.不符合项登记中.name(), EnumQA.EnumNotConformItemStatus.不符合项登记中.name());
        return super.save(entity);
    }

    /**
     * 不符合项修改，只能修改自己登记的不符合项，提交后不可修改
     *
     * @param entity 修改的不符合项实体
     * @return 完成修改的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem update(DtoNotConformItem entity) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.编辑不符合项.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.不符合项登记流程.name(), entity.getId(), EnumQA.EnumLogObjectType.不符合项.name(),
                currentPrincipalUser.getUserName() + "修改了不符合项", "", "");
        return super.update(entity);
    }

    /**
     * 删除方法，只能删除自己创建的不符合项数据，提交之后的数据不能删除
     *
     * @param ids 要删除的实体id集合
     * @return 删除的数据条数
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中存在的实体用于验证
        List<DtoNotConformItem> exists = super.findAll((Collection<String>) ids);
        for (DtoNotConformItem item : exists) {
            //只能删除自己创建的不符合项记录
            if (!currentPrincipalUser.getUserId().equals(item.getCreator())) {
                throw new BaseException("只能删除自己创建的不符合项记录");
            }
            //数据提交之后不能删除
            if (!EnumQA.EnumNotConformItemStatus.不符合项登记中.name().equals(item.getStatus())
                    && !EnumQA.EnumNotConformItemStatus.不符合项措施拟定不通过.name().equals(item.getStatus())) {
                throw new BaseException("数据提交之后不能删除");
            }
        }
        //保存日志
        List<DtoLog> logs = new ArrayList<>();
        for (String id : (List<String>) ids) {
            logs.add(logService.getDtoLog(id, EnumQA.EnumLogOperateType.删除不符合项.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.不符合项登记流程.name(),
                    EnumQA.EnumLogObjectType.不符合项.name(), currentPrincipalUser.getUserName() + "删除客户投诉"));
        }
        logService.save(logs);
        //删除
        return super.logicDeleteById(ids);
    }


    /**
     * 不符合项登记提交方法，提交选择可是主任，更新状态为措施拟定中
     *
     * @param notConformItem 需要提交的不符合项实体
     * @return 完成提交的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem submit(DtoNotConformItem notConformItem) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取当前id的实体
        DtoNotConformItem exist = super.findOne(notConformItem.getId());
        if (StringUtil.isEmpty(notConformItem.getNextOperator())) {
            throw new BaseException("下一步操作人不能为空");
        }
        //更新状态
        exist.setStatus(EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name());
        DtoPerson person = personRepository.findOne(notConformItem.getNextOperator());
        String nextOperate = "";
        if (StringUtils.isNotNull(person)) {
            nextOperate = "提交了不符合项，下一步操作人：" + person.getCName();
        }
        //记录日志
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交不符合项.name(), notConformItem.getNcDeptPersonId(),
                notConformItem.getNcDeptPerson(), EnumQA.EnumLogType.不符合项登记流程.name(), notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.name(),
                currentPrincipalUser.getUserName() + nextOperate, "", "");
        submitRecordService.createSubmitRecord(notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.getValue(), EnumQA.EnumQASubmitType.不符合项提交.getValue(),
                notConformItem.getNextOperator(), notConformItem.getSubmitRemark(), EnumQA.EnumNotConformItemStatus.不符合项登记中.name(),
                EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name());
        //保存实体
        return super.update(exist);
    }

    /**
     * 不符合项纠正措施拟定
     *
     * @param notConformItem 需要进行措施拟定的不符合项实体
     * @return 完成措施拟定的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem measuresSubmit(DtoNotConformItem notConformItem) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中数据
        DtoNotConformItem exist = super.findOne(notConformItem.getId());
        String status;
        String stateTo;
        String nextOperate = "";
        String nextOperatorId = "";
        //根据标识字段判断是否通过审核，通过下一步操作人必填，不通过时意见必填
        if (!Optional.ofNullable(notConformItem.getIsPassCompleteFlag()).isPresent()) {
            throw new BaseException("必须传回通过标示字段");
        } else {
            //通过
            if (notConformItem.getIsPassCompleteFlag()) {
                if (StringUtil.isEmpty(notConformItem.getNextOperator())) {
                    throw new BaseException("必须选择下一步操作人");
                }
                //设置通过状态
                status = EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name();
                stateTo = EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name();
                nextOperatorId = notConformItem.getNextOperator();
                DtoPerson person = personRepository.findOne(nextOperatorId);
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "拟定了不符合项措施，下一步操作人" + person.getCName() + "。";
                }
            }
            //不通过
            else {
                //设置不通过状态
                if (StringUtil.isEmpty(notConformItem.getSubmitRemark())) {
                    throw new BaseException("意见为必填");
                }
                status = EnumQA.EnumNotConformItemStatus.不符合项措施拟定不通过.name();
                stateTo = EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name();
                nextOperatorId = exist.getCreator();
                DtoPerson person = personRepository.findOne(nextOperatorId);
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "审核了不符合项，审核不通过，下一步操作人：" + person.getCName() + "。";
                }
            }
            if (StringUtil.isNotEmpty(notConformItem.getSubmitRemark())) {
                nextOperate += " 意见：" + notConformItem.getSubmitRemark();
            }
        }
        exist.setStatus(status);
        //保存日志
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交不符合项措施.name(), UUIDHelper.GUID_EMPTY, "",
                EnumQA.EnumLogType.不符合项登记流程.name(), notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.name(),
                currentPrincipalUser.getUserName() + nextOperate, "", "");
        submitRecordService.createSubmitRecord(notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.getValue(), EnumQA.EnumQASubmitType.不符合项拟定措施提交.getValue(),
                nextOperatorId, notConformItem.getSubmitRemark(), EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name(),
                stateTo);
        return super.save(exist);
    }

    /**
     * 不符合项措施批准
     *
     * @param notConformItem 需要对拟定措施进行审核的不符合项实体
     * @return 完成措施审核的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem measuresAudit(DtoNotConformItem notConformItem) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取之前保存的实体
        DtoNotConformItem exist = super.findOne(notConformItem.getId());
        String status;
        String stateTo;
        String nextOperate = "";
        String nextOperatorId = "";
        //根据标识字段判断是否通过审核
        if (!Optional.ofNullable(notConformItem.getIsPassCompleteFlag()).isPresent()) {
            throw new BaseException("必须传回通过标示字段");
        } else {
            //通过时下一步操作人为不符合项负责人
            if (notConformItem.getIsPassCompleteFlag()) {
                //设置通过状态
                status = EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name();
                stateTo = EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name();
                nextOperatorId = exist.getNcMainPersonId();
                DtoPerson person = personRepository.findOne(nextOperatorId);
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "批准了不符合项措施，下一步操作人：" + person.getCName() + "。";
                }
            }
            //不通过时意见必填，下一步操作人为提交时的下一步操作人
            else {
                //设置不通过状态
                if (StringUtil.isEmpty(notConformItem.getSubmitRemark())) {
                    throw new BaseException("意见为必填");
                }
                status = EnumQA.EnumNotConformItemStatus.不符合项措施批准不通过.name();
                stateTo = EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name();
                DtoSubmitRecordQa submitRecord = submitRecordService.findSubmitRecordByObjIdAndSubmitType(
                        exist.getId(), EnumQA.EnumQASubmitType.不符合项提交.getValue());
                if (StringUtils.isNotNull(submitRecord)) {
                    nextOperatorId = submitRecord.getNextPerson();
                    DtoPerson person = personRepository.findOne(nextOperatorId);
                    if (StringUtils.isNotNull(person)) {
                        nextOperate = "批准了不符合项措施，批准不通过，下一步操作人：" + person.getCName() + "。";
                    }
                }
            }
            if (StringUtil.isNotEmpty(notConformItem.getSubmitRemark())) {
                nextOperate += " 意见：" + notConformItem.getSubmitRemark();
            }
        }
        exist.setStatus(status);
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核不符合项措施.name(), UUIDHelper.GUID_EMPTY, "",
                EnumQA.EnumLogType.不符合项登记流程.name(), notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.name(),
                currentPrincipalUser.getUserName() + nextOperate, "", "");
        submitRecordService.createSubmitRecord(notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.getValue(), EnumQA.EnumQASubmitType.不符合项措施审核.getValue(),
                nextOperatorId, notConformItem.getSubmitRemark(), EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name(),
                stateTo);
        return super.update(exist);
    }

    /**
     * 不符合项完成情况确认
     * 1.不符合项的验证评价指定操作人为登记时的发现人，所以这里的下一步操作人为NcFindPerson
     *
     * @param notConformItem 需要进行不符合项完成情况确认的不符合项实体
     * @return 完成确认的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem measuresConfirm(DtoNotConformItem notConformItem) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中实体
        DtoNotConformItem exist = super.findOne(notConformItem.getId());
        //更新状态
        exist.setStatus(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
        DtoPerson person = personRepository.findOne(exist.getNcFindPersonId());
        String nextOperatorName = "";
        if (StringUtils.isNotNull(person)) {
            nextOperatorName = person.getCName() + "。";
        }
        if (StringUtil.isNotEmpty(notConformItem.getSubmitRemark())) {
            nextOperatorName += "意见：" + notConformItem.getSubmitRemark();
        }
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核不符合项措施完成情况.name(), UUIDHelper.GUID_EMPTY, "",
                EnumQA.EnumLogType.不符合项登记流程.name(), notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.name(),
                currentPrincipalUser.getUserName() + "审核了不符合项措施完成情况，下一步操作人：" + nextOperatorName, "", "");
        submitRecordService.createSubmitRecord(notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.getValue(), EnumQA.EnumQASubmitType.不符合项完成情况审核.getValue(),
                exist.getNcFindPersonId(), notConformItem.getSubmitRemark(), EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name(),
                EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
        return super.update(exist);
    }

    /**
     * 批量退回方法，填充统一退回原因
     *
     * @param goBack 退回对象，封装需要退回的实体id和退回原因
     * @return 完成退回的对象集合
     */
    @Transactional
    @Override
    public List<DtoNotConformItem> measuresConfirmBack(DtoGoBack goBack) {
        //获取所有需要退回的不符合项措施
        List<DtoNotConformItem> backNotConfirItems = super.findAll(goBack.getIds());
        //判断是否查出所有要退回的数据
        if (backNotConfirItems.size() != goBack.getIds().size()) {
            throw new BaseException("未发现需要退回的投诉，请刷新页面后重试");
        }
        //定义同期用于存储记录容器实体，统一保存
        List<DtoSubmitRecordQa> submitRecords = new ArrayList<>();
        //定义容器存储日志对象，统一保存
        List<DtoLog> logs = new ArrayList<>();
        //获取当前登录用户
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //退回时的下一步操作人为拟定措施时指定的下一步操作人
        List<DtoSubmitRecordQa> oldSubmitRecord = submitRecordService.findSubmitRecordBySubmitTypeAndObjectIds(
                EnumQA.EnumQASubmitType.不符合项拟定措施提交.getValue(), goBack.getIds());
        for (DtoNotConformItem item : backNotConfirItems) {
            //实例化记录对象，并填充数据
            DtoSubmitRecordQa submitRecord = new DtoSubmitRecordQa();
            submitRecord.setObjectId(item.getId());
            submitRecord.setObjectType(EnumQA.EnumQAObjType.不符合项管理.getValue());
            submitRecord.setSubmitType(EnumQA.EnumQASubmitType.不符合项完成情况退回.getValue());
            submitRecord.setSubmitTime(new Date());
            submitRecord.setSubmitPersonId(currentUser.getUserId());
            submitRecord.setSubmitPersonName(currentUser.getUserName());
            submitRecord.setStateFrom(item.getStatus());
            submitRecord.setStateTo(EnumQA.EnumQASubmitType.不符合项完成情况审核.name());
            submitRecord.setSubmitRemark(goBack.getSubmitRemark());
            oldSubmitRecord.forEach(s -> {
                if (item.getId().equals(s.getObjectId())) {
                    submitRecord.setNextPerson(s.getNextPerson());
                }
            });
            //添加记录对象到容器中，统一保存
            submitRecords.add(submitRecord);
            //实例化日志数据对象并填充数据
            DtoLog log = new DtoLog();
            log.setOperatorId(currentUser.getUserId());
            log.setOperatorName(currentUser.getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumQA.EnumLogOperateType.退回不符合项措施完成情况.name());
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumQA.EnumLogType.不符合项登记流程.name());
            log.setObjectId(item.getId());
            log.setObjectType(EnumQA.EnumLogObjectType.不符合项.name());
            String record = currentUser.getUserName() + "退回了不符合项纠正措施" + "。";
            if (StringUtil.isNotEmpty(goBack.getSubmitRemark())) {
                record += "意见：" + goBack.getSubmitRemark();
            }
            log.setComment(record);
            //添加日志对象到容器中，统一保存
            logs.add(log);
            //改变状态为退回
            item.setStatus(EnumQA.EnumNotConformItemStatus.不符合项措施纠正不通过.name());
        }
        submitRecordService.createSubmitRecords(submitRecords, currentUser.getUserId(), currentUser.getUserName(), currentUser.getOrgId());
        logService.save(logs);
        return repository.save(backNotConfirItems);
    }

    /**
     * 不符合项验证提交评价，通过isPassCompleteFlag字段验证是否通过
     * 验证通过：填充措施有效性,验证及评价
     * 验证未通过：填充原因
     *
     * @param notConformItem 需要进行确认评价的不符合项实体
     * @return 完成确认评价的不符合项实体
     */
    @Transactional
    @Override
    public DtoNotConformItem validate(DtoNotConformItem notConformItem) {
        //获取当前登录用户
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中保存的实体
        DtoNotConformItem exist = super.findOne(notConformItem.getId());
        String status;
        String stateTo;
        //如果通过验证必填信息并填充
        String nextOperatorId = "";
        String nextOperate = "";
        if (!Optional.ofNullable(notConformItem.getIsPassCompleteFlag()).isPresent()) {
            throw new BaseException("必须传回通过标示字段");
        } else {
            //通过
            if (notConformItem.getIsPassCompleteFlag()) {
                //更新状态
                status = EnumQA.EnumNotConformItemStatus.不符合项已办结.name();
                stateTo = EnumQA.EnumNotConformItemStatus.不符合项已办结.name();
                nextOperate = "确认了不符合项完成情况" + "。";
            }
            //未通过
            else {
                if (notConformItem.getSubmitRemark() == null || notConformItem.getSubmitRemark().equals("")) {
                    throw new BaseException("不通过原因为必填");
                }
                status = EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name();
                //填充下一步操作
                stateTo = EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name();
                //不通过时下一步操作人为不符合项责任人
                nextOperatorId = exist.getNcMainPersonId();
                nextOperate = "确认了不符合项完成情况，确认不通过，下一步操作人：" + exist.getNcMainPerson() + "。";
            }
            if (StringUtil.isNotEmpty(notConformItem.getSubmitRemark())) {
                nextOperate += "意见：" + notConformItem.getSubmitRemark();
            }
        }
        exist.setStatus(status);
        //保存日志
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交不符合项验证评价.name(), UUIDHelper.GUID_EMPTY, "",
                EnumQA.EnumLogType.不符合项登记流程.name(), notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.name(), currentPrincipalUser.getUserName() + nextOperate, "", "");
        submitRecordService.createSubmitRecord(notConformItem.getId(), EnumQA.EnumQAObjType.不符合项管理.getValue(), EnumQA.EnumQASubmitType.不符合项验证评价提交.getValue(),
                nextOperatorId, notConformItem.getSubmitRemark(), EnumQA.EnumNotConformItemStatus.不符合项验证中.name(),
                stateTo);
        return super.update(exist);
    }

    @Override
    public DtoNotConformItem findAttachPath(String id) {
        return findOne(id);
    }
}