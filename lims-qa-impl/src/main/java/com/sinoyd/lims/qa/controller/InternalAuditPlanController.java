package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.InternalAuditPlanService;
import com.sinoyd.lims.qa.criteria.InternalAuditPlanCriteria;
import com.sinoyd.lims.qa.dto.DtoInternalAuditPlan;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * InternalAuditPlan服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: InternalAuditPlan服务")
@RestController
@RequestMapping("api/qa/internalAuditPlan")
@Validated
public class InternalAuditPlanController extends BaseJpaController<DtoInternalAuditPlan, String, InternalAuditPlanService> {


    /**
     * 分页动态条件查询InternalAuditPlan
     *
     * @param internalAuditPlanCriteria 条件参数
     * @return RestResponse<List       <       InternalAuditPlan>>
     */
    @ApiOperation(value = "分页动态条件查询InternalAuditPlan", notes = "分页动态条件查询InternalAuditPlan")
    @GetMapping
    public RestResponse<List<DtoInternalAuditPlan>> findByPage(InternalAuditPlanCriteria internalAuditPlanCriteria) {
        PageBean<DtoInternalAuditPlan> pageBean = super.getPageBean();
        RestResponse<List<DtoInternalAuditPlan>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, internalAuditPlanCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询InternalAuditPlan
     *
     * @param id 主键id
     * @return RestResponse<DtoInternalAuditPlan>
     */
    @ApiOperation(value = "按主键查询InternalAuditPlan", notes = "按主键查询InternalAuditPlan")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoInternalAuditPlan> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        DtoInternalAuditPlan internalAuditPlan = service.findOne(id);
        restResponse.setData(internalAuditPlan);
        restResponse.setRestStatus(StringUtil.isNull(internalAuditPlan) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增InternalAuditPlan
     *
     * @param internalAuditPlan 实体列表
     * @return RestResponse<DtoInternalAuditPlan>
     */
    @ApiOperation(value = "新增InternalAuditPlan", notes = "新增InternalAuditPlan")
    @PostMapping
    public RestResponse<DtoInternalAuditPlan> create(@Validated @RequestBody DtoInternalAuditPlan internalAuditPlan) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.save(internalAuditPlan));
        return restResponse;
    }

    /**
     * 新增InternalAuditPlan
     *
     * @param internalAuditPlan 实体列表
     * @return RestResponse<DtoInternalAuditPlan>
     */
    @ApiOperation(value = "修改InternalAuditPlan", notes = "修改InternalAuditPlan")
    @PutMapping
    public RestResponse<DtoInternalAuditPlan> update(@Validated @RequestBody DtoInternalAuditPlan internalAuditPlan) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.update(internalAuditPlan));
        return restResponse;
    }

    /**
     * "根据id批量删除InternalAuditPlan
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除InternalAuditPlan", notes = "根据id批量删除InternalAuditPlan")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交DtoInternalAuditPlan
     *
     * @param dtoInternalAuditPlan 实体列表
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "提交DtoInternalAuditPlan", notes = "提交DtoInternalAuditPlan")
    @PutMapping(path = "/submit")
    public RestResponse<DtoInternalAuditPlan> submit(@RequestBody DtoInternalAuditPlan dtoInternalAuditPlan) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(dtoInternalAuditPlan));
        return restResponse;
    }

    /**
     * 审核DtoInternalAuditPlan 实体列表
     *
     * @param dtoInternalAuditPlan
     * @return RestResponse<DtoInternalAuditPlan>
     */
    @ApiOperation(value = "审核DtoInternalAuditPlan", notes = "审核DtoInternalAuditPlan")
    @PutMapping(path = "/audit")
    public RestResponse<DtoInternalAuditPlan> audit(@RequestBody DtoInternalAuditPlan dtoInternalAuditPlan) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.audit(dtoInternalAuditPlan));
        return restResponse;
    }

    /**
     * 提交报告DtoInternalAuditPlan
     *
     * @param dtoInternalAuditPlan 实体
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "提交报告DtoInternalAuditPlan", notes = "提交报告DtoInternalAuditPlan")
    @PutMapping(path = "/submitReport")
    public RestResponse<DtoInternalAuditPlan> submitReport(@RequestBody DtoInternalAuditPlan dtoInternalAuditPlan) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submitReport(dtoInternalAuditPlan));
        return restResponse;
    }

    /**
     * 审核报告DtoInternalAuditPlan
     *
     * @param dtoInternalAuditPlan 实体
     * @return RestResponse<DtoInternalAuditPlan>
     */
    @ApiOperation(value = "审核报告DtoInternalAuditPlan", notes = "审核报告DtoInternalAuditPlan")
    @PutMapping(path = "/auditReport")
    public RestResponse<DtoInternalAuditPlan> auditReport(@RequestBody DtoInternalAuditPlan dtoInternalAuditPlan) {
        RestResponse<DtoInternalAuditPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.auditReport(dtoInternalAuditPlan));
        return restResponse;
    }
}