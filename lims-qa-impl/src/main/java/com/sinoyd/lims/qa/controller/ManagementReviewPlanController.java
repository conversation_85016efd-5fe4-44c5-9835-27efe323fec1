package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.ManagementReviewPlanService;
import com.sinoyd.lims.qa.criteria.ManagementReviewPlanCriteria;
import com.sinoyd.lims.qa.dto.DtoManagementReviewPlan;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ManagementReviewPlan服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: ManagementReviewPlan服务")
@RestController
@RequestMapping("api/qa/managementReviewPlan")
@Validated
public class ManagementReviewPlanController extends BaseJpaController<DtoManagementReviewPlan, String, ManagementReviewPlanService> {

    /**
     * 分页动态条件查询ManagementReviewPlan
     *
     * @param managementReviewPlanCriteria 条件参数
     * @return RestResponse<List<ManagementReviewPlan>>
     */
    @ApiOperation(value = "分页动态条件查询ManagementReviewPlan", notes = "分页动态条件查询ManagementReviewPlan")
    @GetMapping
    public RestResponse<List<DtoManagementReviewPlan>> findByPage(ManagementReviewPlanCriteria managementReviewPlanCriteria) {
        PageBean<DtoManagementReviewPlan> pageBean = super.getPageBean();
        RestResponse<List<DtoManagementReviewPlan>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, managementReviewPlanCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ManagementReviewPlan
     *
     * @param id 主键id
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "按主键查询ManagementReviewPlan", notes = "按主键查询ManagementReviewPlan")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoManagementReviewPlan> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        DtoManagementReviewPlan managementReviewPlan = service.findOne(id);
        restResponse.setData(managementReviewPlan);
        restResponse.setRestStatus(StringUtil.isNull(managementReviewPlan) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ManagementReviewPlan
     *
     * @param managementReviewPlan 实体列表
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "新增ManagementReviewPlan", notes = "新增ManagementReviewPlan")
    @PostMapping
    public RestResponse<DtoManagementReviewPlan> create(@Validated @RequestBody DtoManagementReviewPlan managementReviewPlan) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.save(managementReviewPlan));
        return restResponse;
    }

    /**
     * 修改ManagementReviewPlan
     *
     * @param managementReviewPlan 实体列表
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "修改ManagementReviewPlan", notes = "修改ManagementReviewPlan")
    @PutMapping
    public RestResponse<DtoManagementReviewPlan> update(@Validated @RequestBody DtoManagementReviewPlan managementReviewPlan) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.update(managementReviewPlan));
        return restResponse;
    }

    /**
     * "根据id批量删除ManagementReviewPlan
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ManagementReviewPlan", notes = "根据id批量删除ManagementReviewPlan")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交DtoManagementReviewPlan
     *
     * @param dtoManagementReviewPlan
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "提交DtoManagementReviewPlan", notes = "提交DtoManagementReviewPlan")
    @PutMapping(path = "/submit")
    public RestResponse<DtoManagementReviewPlan> submit(@RequestBody DtoManagementReviewPlan dtoManagementReviewPlan) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(dtoManagementReviewPlan));
        return restResponse;
    }

    /**
     * 提交报告DtoManagementReviewPlan
     *
     * @param dtoManagementReviewPlan 实体
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "提交报告DtoManagementReviewPlan", notes = "提交报告DtoManagementReviewPlan")
    @PutMapping(path = "/submitReport")
    public RestResponse<DtoManagementReviewPlan> submitReport(@RequestBody DtoManagementReviewPlan dtoManagementReviewPlan) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submitReport(dtoManagementReviewPlan));
        return restResponse;
    }

    /**
     * 审核DtoManagementReviewPlan
     *
     * @param dtoManagementReviewPlan 实体
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "审核DtoManagementReviewPlan", notes = "审核DtoManagementReviewPlan")
    @PutMapping(path = "/audit")
    public RestResponse<DtoManagementReviewPlan> audit(@RequestBody DtoManagementReviewPlan dtoManagementReviewPlan) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.audit(dtoManagementReviewPlan));
        return restResponse;
    }


    /**
     * 审核报告DtoManagementReviewPlan
     *
     * @param dtoManagementReviewPlan 实体
     * @return RestResponse<DtoManagementReviewPlan>
     */
    @ApiOperation(value = "审核报告DtoManagementReviewPlan", notes = "审核报告DtoManagementReviewPlan")
    @PutMapping(path = "/auditReport")
    public RestResponse<DtoManagementReviewPlan> auditReport(@RequestBody DtoManagementReviewPlan dtoManagementReviewPlan) {
        RestResponse<DtoManagementReviewPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.auditReport(dtoManagementReviewPlan));
        return restResponse;
    }
}