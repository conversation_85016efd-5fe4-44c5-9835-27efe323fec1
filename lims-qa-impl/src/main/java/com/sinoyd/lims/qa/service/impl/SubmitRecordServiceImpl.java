package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * SubmitRecord操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/24
 * @since V100R001
 */
@Service("qaSubmitRecordService")
public class SubmitRecordServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSubmitRecordQa, String, SubmitRecordRepository> implements SubmitRecordService {

    @Override
    public void findByPage(PageBean<DtoSubmitRecordQa> pb, BaseCriteria submitRecordCriteria) {
        pb.setEntityName("DtoSubmitRecordQa a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, submitRecordCriteria);
    }

    @Transactional
    @Override
    public void createSubmitRecord(String objectId,
                                   Integer objectType,
                                   Integer submitType,
                                   String nextPerson,
                                   String submitRemark,
                                   String from,
                                   String to) {
        //需要把关联对象的旧记录的isNewest设置成false
        List<DtoSubmitRecordQa> dbDtoList = repository.findByObjectId(objectId);
        dbDtoList.forEach(dto -> dto.setIsNewest(Boolean.FALSE));
        super.update(dbDtoList);
        //新增当前操作的记录
        DtoSubmitRecordQa submitRecord = new DtoSubmitRecordQa();
        submitRecord.setObjectId(objectId);
        submitRecord.setObjectType(objectType);
        submitRecord.setSubmitType(submitType);
        submitRecord.setSubmitPersonId(PrincipalContextUser.getPrincipal().getUserId());
        submitRecord.setSubmitPersonName(PrincipalContextUser.getPrincipal().getUserName());
        submitRecord.setSubmitTime(new Date());
        submitRecord.setNextPerson(nextPerson);
        submitRecord.setSubmitRemark(submitRemark);
        submitRecord.setStateFrom(from);
        submitRecord.setStateTo(to);
        submitRecord.setIsNewest(Boolean.TRUE);
        repository.save(submitRecord);
    }

    @Transactional
    @Override
    public void createSubmitRecords(List<DtoSubmitRecordQa> submitRecords,
                                    String userId,
                                    String userName,
                                    String orgId) {
        //需要把关联对象的旧记录的isNewest设置成false
        List<String> objectIds = submitRecords.parallelStream().map(DtoSubmitRecordQa::getObjectId).distinct().collect(Collectors.toList());
        List<DtoSubmitRecordQa> dbDtoList = repository.findByObjectIdIn(objectIds);
        dbDtoList.forEach(dto -> dto.setIsNewest(Boolean.FALSE));
        super.update(dbDtoList);
        //新增当前操作的记录
        for (DtoSubmitRecordQa submitRecord : submitRecords) {
            submitRecord.setSubmitPersonId(userId);
            submitRecord.setSubmitPersonName(userName);
            submitRecord.setOrgId(orgId);
            submitRecord.setCreator(userId);
            submitRecord.setModifier(userId);
            submitRecord.setSubmitTime(new Date());
            submitRecord.setIsNewest(Boolean.TRUE);
        }
        repository.save(submitRecords);
    }

    @Override
    public DtoSubmitRecordQa findSubmitRecordByObjIdAndSubmitType(String objId, Integer submitType) {
        //获取根据创建时间倒序的记录对象集合
        List<DtoSubmitRecordQa> submitRecords = repository.findByObjectIdAndSubmitTypeOrderByCreateDateDesc(objId, submitType);
        DtoSubmitRecordQa result = null;
        if (StringUtil.isNotEmpty(submitRecords)) {
            //获取指定步骤最新的记录对象
            result = submitRecords.get(0);
        }
        return result;
    }

    @Override
    public List<DtoSubmitRecordQa> findSubmitRecordBySubmitTypeAndObjectIds(Integer submitType, List<String> objectIds) {
        List<DtoSubmitRecordQa> result = new ArrayList<>();
        List<DtoSubmitRecordQa> submitRecords = repository.findBySubmitTypeAndObjectIdIn(submitType,objectIds);
        if (StringUtil.isNotEmpty(submitRecords)){
            //获取所有去重后的objId
            Set<String> ids = submitRecords.stream().map(DtoSubmitRecordQa::getObjectId).collect(Collectors.toSet());
            //遍历id获取每个id所有的记录对象
            ids.forEach(item -> {
                List<DtoSubmitRecordQa> submitRecordsOfId = new ArrayList<>();
                submitRecords.forEach(s->{
                    if (item.equals(s.getObjectId())){
                        submitRecordsOfId.add(s);
                    }
                });
                //如果当前id有不止一个记录对象，则按照创建时间进行倒序排序，取最新一条
                if (StringUtil.isNotEmpty(submitRecordsOfId)){
                    if (submitRecordsOfId.size()>1){
                        submitRecordsOfId.sort(Comparator.comparing(DtoSubmitRecordQa::getCreateDate, Comparator.reverseOrder()));
                    }
                result.add(submitRecordsOfId.get(0));
                }
            });
        }
        return result;
    }


}