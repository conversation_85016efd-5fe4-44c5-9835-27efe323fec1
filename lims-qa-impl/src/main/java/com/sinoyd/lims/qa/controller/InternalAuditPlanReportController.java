package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.InternalAuditPlanReportService;
import com.sinoyd.lims.qa.criteria.InternalAuditPlanReportCriteria;
import com.sinoyd.lims.qa.dto.DtoInternalAuditPlanReport;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * InternalAuditPlanReport服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @Api(tags = "示例: InternalAuditPlanReport服务")
 @RestController
 @RequestMapping("api/qa/internalAuditPlanReport")
 @Validated
 public class InternalAuditPlanReportController extends BaseJpaController<DtoInternalAuditPlanReport, String,InternalAuditPlanReportService> {


    /**
     * 分页动态条件查询InternalAuditPlanReport
     * @param internalAuditPlanReportCriteria 条件参数
     * @return RestResponse<List<InternalAuditPlanReport>>
     */
     @ApiOperation(value = "分页动态条件查询InternalAuditPlanReport", notes = "分页动态条件查询InternalAuditPlanReport")
     @GetMapping
     public RestResponse<List<DtoInternalAuditPlanReport>> findByPage(InternalAuditPlanReportCriteria internalAuditPlanReportCriteria) {
         PageBean<DtoInternalAuditPlanReport> pageBean = super.getPageBean();
         RestResponse<List<DtoInternalAuditPlanReport>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, internalAuditPlanReportCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询InternalAuditPlanReport
     * @param id 主键id
     * @return RestResponse<DtoInternalAuditPlanReport>
     */
     @ApiOperation(value = "按主键查询InternalAuditPlanReport", notes = "按主键查询InternalAuditPlanReport")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoInternalAuditPlanReport> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoInternalAuditPlanReport> restResponse = new RestResponse<>();
         DtoInternalAuditPlanReport internalAuditPlanReport = service.findOne(id);
         restResponse.setData(internalAuditPlanReport);
         restResponse.setRestStatus(StringUtil.isNull(internalAuditPlanReport) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增InternalAuditPlanReport
     * @param internalAuditPlanReport 实体列表
     * @return RestResponse<DtoInternalAuditPlanReport>
     */
     @ApiOperation(value = "新增InternalAuditPlanReport", notes = "新增InternalAuditPlanReport")
     @PostMapping
     public RestResponse<DtoInternalAuditPlanReport> create(@Validated @RequestBody DtoInternalAuditPlanReport internalAuditPlanReport) {
         RestResponse<DtoInternalAuditPlanReport> restResponse = new RestResponse<>();
         restResponse.setData(service.save(internalAuditPlanReport));
         return restResponse;
      }

     /**
     * 新增InternalAuditPlanReport
     * @param internalAuditPlanReport 实体列表
     * @return RestResponse<DtoInternalAuditPlanReport>
     */
     @ApiOperation(value = "修改InternalAuditPlanReport", notes = "修改InternalAuditPlanReport")
     @PutMapping
     public RestResponse<DtoInternalAuditPlanReport> update(@Validated @RequestBody DtoInternalAuditPlanReport internalAuditPlanReport) {
         RestResponse<DtoInternalAuditPlanReport> restResponse = new RestResponse<>();
         restResponse.setData(service.update(internalAuditPlanReport));
         return restResponse;
      }

    /**
     * "根据id批量删除InternalAuditPlanReport
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除InternalAuditPlanReport", notes = "根据id批量删除InternalAuditPlanReport")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }