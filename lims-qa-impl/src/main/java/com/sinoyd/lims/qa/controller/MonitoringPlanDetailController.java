package com.sinoyd.lims.qa.controller;

import com.sinoyd.lims.qa.dto.DtoGoBack;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.MonitoringPlanDetailService;
import com.sinoyd.lims.qa.criteria.MonitoringPlanDetailCriteria;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlanDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * MonitoringPlanDetail服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @Api(tags = "示例: MonitoringPlanDetail服务")
 @RestController
 @RequestMapping("api/qa/monitoringPlanDetail")
 @Validated
 public class MonitoringPlanDetailController extends BaseJpaController<DtoMonitoringPlanDetail, String,MonitoringPlanDetailService> {


    /**
     * 分页动态条件查询MonitoringPlanDetail
     * @param monitoringPlanDetailCriteria 条件参数
     * @return RestResponse<List<MonitoringPlanDetail>>
     */
     @ApiOperation(value = "分页动态条件查询MonitoringPlanDetail", notes = "分页动态条件查询MonitoringPlanDetail")
     @GetMapping
     public RestResponse<List<DtoMonitoringPlanDetail>> findByPage(MonitoringPlanDetailCriteria monitoringPlanDetailCriteria) {
         PageBean<DtoMonitoringPlanDetail> pageBean = super.getPageBean();
         RestResponse<List<DtoMonitoringPlanDetail>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, monitoringPlanDetailCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询MonitoringPlanDetail
     * @param id 主键id
     * @return RestResponse<DtoMonitoringPlanDetail>
     */
     @ApiOperation(value = "按主键查询MonitoringPlanDetail", notes = "按主键查询MonitoringPlanDetail")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoMonitoringPlanDetail> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoMonitoringPlanDetail> restResponse = new RestResponse<>();
         DtoMonitoringPlanDetail monitoringPlanDetail = service.findOne(id);
         restResponse.setData(monitoringPlanDetail);
         restResponse.setRestStatus(StringUtil.isNull(monitoringPlanDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增MonitoringPlanDetail
     * @param monitoringPlanDetail 实体列表
     * @return RestResponse<DtoMonitoringPlanDetail>
     */
     @ApiOperation(value = "新增MonitoringPlanDetail", notes = "新增MonitoringPlanDetail")
     @PostMapping
     public RestResponse<DtoMonitoringPlanDetail> create(@Validated @RequestBody DtoMonitoringPlanDetail monitoringPlanDetail) {
         RestResponse<DtoMonitoringPlanDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.save(monitoringPlanDetail));
         return restResponse;
      }

     /**
     * 新增MonitoringPlanDetail
     * @param monitoringPlanDetail 实体列表
     * @return RestResponse<DtoMonitoringPlanDetail>
     */
     @ApiOperation(value = "修改MonitoringPlanDetail", notes = "修改MonitoringPlanDetail")
     @PutMapping
     public RestResponse<DtoMonitoringPlanDetail> update(@Validated @RequestBody DtoMonitoringPlanDetail monitoringPlanDetail) {
         RestResponse<DtoMonitoringPlanDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.update(monitoringPlanDetail));
         return restResponse;
      }

    /**
     * "根据id批量删除MonitoringPlanDetail
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除MonitoringPlanDetail", notes = "根据id批量删除MonitoringPlanDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交dtoMonitoringPlanDetail
     *
     * @param dtoMonitoringPlanDetail 实体列表
     * @return RestResponse<DtoInternalAuditImplementPlan>
     */
    @ApiOperation(value = "提交dtoMonitoringPlanDetail", notes = "提交dtoMonitoringPlanDetail")
    @PutMapping(path = "/submit")
    public RestResponse<DtoMonitoringPlanDetail> submit(@RequestBody DtoMonitoringPlanDetail dtoMonitoringPlanDetail) {
        RestResponse<DtoMonitoringPlanDetail> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(dtoMonitoringPlanDetail));
        return restResponse;
    }


    /**
     * 退回DtoMonitoringPlanDetail
     *
     * @param dtoGoBack
     * @return RestResponse<DtoMonitoringPlanDetail>
     */
    @ApiOperation(value = "退回DtoMonitoringPlanDetail", notes = "退回DtoMonitoringPlanDetail")
    @PutMapping(path = "/goBack")
    public RestResponse<List<DtoMonitoringPlanDetail>> goBack(@RequestBody DtoGoBack dtoGoBack) {
        RestResponse<List<DtoMonitoringPlanDetail>> restResponse = new RestResponse<>();
        restResponse.setData(service.goBack(dtoGoBack));
        return restResponse;
    }
 }