package com.sinoyd.lims.qa.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * RiskAndAccident查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskAndAccidentCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 1为风险机遇登记，2为风险机遇措施拟定，3为风险机遇方案审批，4为风险机遇完成情况，5为风险机遇确认列表，6为风险机遇进度查询
     */
    private int pageFrom;
    /**
     * 登记日期查询开始时间
     */
    private String startTime;
    /**
     * 登记日期查询结束时间
     */
    private String endTime;
    /**
     * 来源类型
     */
    private String sourceType;
    /**
     * 关键字，可以对风险机遇描述进行模糊查询
     */
    private String key;
    /**
     * 状态：-1为所有，1为待处理，2为已处理
     */
    private int status;
    /**
     * 风险机遇状态
     * 1为登记中
     * 2为措施拟定中
     * 3为措施批准中
     * 4为纠正中
     * 5为验证中
     * 6为已办结
     */
    private int riskAndAccidentStatus;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");
        Calendar calendar = new GregorianCalendar();
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //登记时间开始查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.discoverDate >= :startTime");
            values.put("startTime", date);
        }
        //登记时间结束查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.discoverDate < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.sourceType)) {
            condition.append(" and a.sourceType = :sourceType");
            values.put("sourceType", this.sourceType);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and a.description like :key");
            values.put("key", "%" + this.key + "%");
        }
        List<String> statusList = new ArrayList<>();
        //6为显示所有状态的风险机遇登记信息
        if (this.pageFrom == 6) {
            switch (this.riskAndAccidentStatus) {
                //登记中
                case 1:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.登记中.name());
                    break;
                //措施拟定中
                case 2:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name());
                    break;
                //措施批准中
                case 3:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施批准中.name());
                    break;
                //纠正中
                case 4:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施实施中.name());
                    break;
                //验证中
                case 5:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                    break;
                case 6:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                    break;
                case 7:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name());
                    break;
                case 8:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施批准不通过.name());
                    break;
                case 9:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认不通过.name());
                    break;
                case 10:
                    statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况不通过.name());
                    break;
                //默认显示全部
                default:
                    for (EnumQA.EnumRiskAndAccidentStatus item : EnumQA.EnumRiskAndAccidentStatus.values()) {
                        statusList.add(item.name());
                    }
                    break;
            }
            condition.append(" and status in :statusList");
            values.put("statusList", statusList);
        } else {
            switch (this.pageFrom) {
                //登记中
                case 1:
                    if (this.status == 2) {
                        for (EnumQA.EnumRiskAndAccidentStatus item : EnumQA.EnumRiskAndAccidentStatus.values()) {
                            if (!EnumQA.EnumRiskAndAccidentStatus.登记中.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name().equals(item.name())) {
                                statusList.add(item.name());
                            }
                        }
                    }
                    //待处理
                    else if (this.status == 1) {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.登记中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name());
                    }
                    //不传则默认全部显示
                    else {
                        statusList = null;
                    }
                    if (StringUtil.isNotEmpty(statusList)) {
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                    }
                    break;
                case 2:
                    //全部显示，显示除登记中和拟定不通过之外的数据
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        List<String> statusListB = new ArrayList<>();
                        for (EnumQA.EnumRiskAndAccidentStatus item : EnumQA.EnumRiskAndAccidentStatus.values()) {
                            if (!EnumQA.EnumRiskAndAccidentStatus.登记中.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.措施批准不通过.name().equals(item.name())) {
                                statusListB.add(item.name());
                            }
                        }
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name());
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.措施批准不通过.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //显示已处理，显示除登记中，拟定不通过，措施拟定中之外的数据
                    else if (this.status == 2) {
                        for (EnumQA.EnumRiskAndAccidentStatus item : EnumQA.EnumRiskAndAccidentStatus.values()) {
                            if (!EnumQA.EnumRiskAndAccidentStatus.登记中.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.措施批准不通过.name().equals(item.name())
                                    && !EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name().equals(item.name())) {
                                statusList.add(item.name());
                            }
                        }
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //显示待处理，显示措施拟定中，措施审核退回的数据
                    else {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施批准不通过.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
                //措施审核中
                case 3:
                    //全部显示，显示措施审核中，确认中，确认未通过，验证中，验证不通过，已办结
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.措施批准中.name());
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.完成情况不通过.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.措施实施中.name());
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.措施实施不通过.name());
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已处理，显示纠正中，验证中，验证不通过吗，已办结
                    else if (this.status == 2) {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施实施中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施实施不通过.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //待处理，显示批准中，纠正不通过
                    else {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施批准中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况不通过.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
                //措施完成情况确认
                case 4:
                    //全部显示，显示纠正中，验证中，验证未通过，已办结
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.措施实施中.name());
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.确认不通过.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.完成情况不通过.name());
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                        condition.append(" and (((b.nextPerson = :loginUser or a.dutyPersonId = :loginUser) and a.status in :statusListA)  " +
                                "or ((b.nextPerson = :loginUser or a.dutyPersonId = :loginUser) and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已完成，显示演示验证中，已办结
                    else if (this.status == 2) {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况不通过.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                        condition.append(" and ( b.submitPersonId = :loginUser or a.dutyPersonId = :loginUser)");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //待处理，显示纠正中，验证不通过
                    else {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.措施实施中.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认不通过.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser or a.dutyPersonId = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
                //风险机遇确认
                case 5:
                    //全部显示，显示验证中，已办结
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                        statusListB.add(EnumQA.EnumRiskAndAccidentStatus.确认不通过.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已处理，显示已办结
                    else if (this.status == 2) {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认完毕.name());
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.确认不通过.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //未处理，显示验证中
                    else {
                        statusList.add(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
            }
        }

        return condition.toString();
    }
}