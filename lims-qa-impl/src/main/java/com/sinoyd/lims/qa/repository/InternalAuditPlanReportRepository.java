package com.sinoyd.lims.qa.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.qa.dto.DtoInternalAuditPlanReport;


/**
 * InternalAuditPlanReport数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface InternalAuditPlanReportRepository extends IBaseJpaRepository<DtoInternalAuditPlanReport, String> {

    /**
     * 通过 内审计划id查询
     * @param InternalPlanId
     * @return DtoInternalAuditPlanReport实体
     */
    DtoInternalAuditPlanReport findByInternalPlanId(String InternalPlanId);

}