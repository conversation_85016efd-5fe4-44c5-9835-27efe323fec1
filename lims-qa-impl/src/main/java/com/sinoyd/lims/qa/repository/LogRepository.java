package com.sinoyd.lims.qa.repository;

import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * Log数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/3/29
 * @since V100R001
 */
public interface LogRepository extends IBaseJpaPhysicalDeleteRepository<DtoLog, String> {

    /**
     * 根据objectId和日志类型查询（按操作时间倒序返回结果）
     *
     * @param  objectId,ogType 根据Id 和 日志类型查询
     * @return 日志列表
     */
    List<DtoLog> findAllByObjectIdAndLogTypeOrderByOperateTimeDesc(String objectId, String logType);




}