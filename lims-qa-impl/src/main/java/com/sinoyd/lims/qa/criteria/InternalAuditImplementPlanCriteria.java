package com.sinoyd.lims.qa.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;



/**
 * InternalAuditImplementPlan查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InternalAuditImplementPlanCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评审时间
     */
    private String startTime;

    /**
     * 评审时间
     */
    private String endTime;

    /**
     * 审核要素和审核部门关键字
     */
    private String key;

    /**
     * 状态， 2已处理、1待处理、-1所有
     */
    private Integer status;

    /**
     * 内审id
     */
    private String auditPlanId;

    /**
     * 状态
     */
    private String checkStatus;



    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");
        List<String> statusList = new ArrayList();
        Calendar calendar = new GregorianCalendar();

        if (StringUtil.isNotEmpty(this.startTime)){
            Date date = DateUtil.stringToDate(this.startTime,DateUtil.YEAR);
            condition.append(" and a.auditTime >= :startTime");
            values.put("startTime",date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH,1); //把日期往后增加一天,整数  往后推,负数往前移动
            date = calendar.getTime();
            condition.append(" and a.auditTime < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (a.auditedDept like :key or a.auditElement like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(checkStatus)) {
            condition.append(" and a.status = :checkStatus");
            values.put("checkStatus", this.checkStatus);
        }
        if (StringUtil.isNotNull(status)){
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumInternalAuditPlanStatus.内审完毕.name());
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumInternalAuditPlanStatus.内审检查中.name());
            }else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                statusList.add(EnumQA.EnumInternalAuditPlanStatus.内审完毕.name());
                statusList.add(EnumQA.EnumInternalAuditPlanStatus.内审检查中.name());
            }
            condition.append(" and a.status in :statusList");
            condition.append(" and a.auditor = :loginUser ");
            CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
            values.put("loginUser",user.getUserId());
            values.put("statusList", statusList);
        }
        if (StringUtil.isNotNull(auditPlanId)) {
            condition.append(" and a.auditPlanId = :auditPlanId");
            values.put("auditPlanId", this.auditPlanId);
        }
        return condition.toString();
    }
}