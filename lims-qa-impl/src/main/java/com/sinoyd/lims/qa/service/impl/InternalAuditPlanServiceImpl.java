package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.*;
import com.sinoyd.lims.qa.entity.AnnualPlan;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.*;
import com.sinoyd.lims.qa.service.InternalAuditPlanService;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * InternalAuditPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class InternalAuditPlanServiceImpl extends BaseJpaServiceImpl<DtoInternalAuditPlan, String, InternalAuditPlanRepository> implements InternalAuditPlanService {

    private final SubmitRecordRepository submitRecordRepository;

    private final LogService logService;

    private final AnnualPlanRepository annualPlanRepository;

    private final InternalAuditPlanReportRepository internalAuditPlanReportRepository;

    private final PersonRepository personRepository;

    private final SubmitRecordService submitRecordService;

    private final InternalAuditImplementPlanRepository internalAuditImplementPlanRepository;


    @Override
    public void findByPage(PageBean<DtoInternalAuditPlan> pb, BaseCriteria internalAuditPlanCriteria) {
        pb.setEntityName("DtoInternalAuditPlan a ,DtoSubmitRecordQa b");
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoInternalAuditPlan(a.id ,a.auditPurp,a.auditScope, a.auditTime," +
                "a.auditContent,a.attendee,a.auditGist,a.status,a.creator,a.createDate,a.annualPlanId)");
        comRepository.findByPage(pb, internalAuditPlanCriteria);
        List<DtoInternalAuditPlan> dataList = pb.getData();
        if (StringUtil.isNotEmpty(dataList)) {
            //获取所有系统用户
            List<DtoPerson> dtoPersonList = personRepository.findAll();
            //接收DtoInternalAuditPlan的Id集合为ids
            List<String> ids = dataList.parallelStream().map(DtoInternalAuditPlan::getId).collect(Collectors.toList());
            //接收DtoManagementReviewPlan的Id集合为ids
            List<String> annualPlanIds = dataList.parallelStream().map(DtoInternalAuditPlan::getAnnualPlanId).collect(Collectors.toList());
            //通过ids获取submitRecord表的集合
            List<DtoSubmitRecordQa> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumLogObjectType.内审管理.getValue(), ids);
            List<DtoAnnualPlan> dtoAnnualPlanList = annualPlanRepository.findByIdIn(annualPlanIds);//通过ids获取submitRecord表的集合
            for (DtoInternalAuditPlan dto : dataList) {
                //将创建人id转换成名字返回前端
                for (DtoPerson dtoPerson : dtoPersonList) {
                    if (dtoPerson.getId().equals(dto.getCreator())) {
                        dto.setFounderName(dtoPerson.getCName());
                    }
                }
                if (StringUtil.isNotEmpty(dtoAnnualPlanList)) {
                    Optional<DtoAnnualPlan> optional = dtoAnnualPlanList.parallelStream()
                            .filter(p -> p.getId().equals(dto.getAnnualPlanId())).findFirst();
                    optional.ifPresent(dtoAnnualPlan -> dto.setAnnualPlanName(dtoAnnualPlan.getAuditPurpose()));
                }
                //获取单个的参加人员id字符串
                String attendees = dto.getAttendee();
                if (StringUtil.isNotEmpty(attendees)) {
                    //分割逗号并且将数组变成list集合
                    List<String> list = Arrays.asList(attendees.split(","));
                    //放名字的集合
                    List<String> name = new ArrayList<>();
                    list.forEach(s -> dtoPersonList.forEach(dtoPerson -> {
                        if (dtoPerson.getId().equals(s)) {
                            name.add(dtoPerson.getCName());
                        }
                    }));
                    //将查到的名字转成字符串
                    dto.setAttendeeName(String.join(",", name));
                }
                if (StringUtil.isNotEmpty(submitRecordList)) {
                    //获取DtoSubmitRecord表getObjectId和DtoAnnualPlan表的ID比较得到关联的集合
                    List<DtoSubmitRecordQa> thisDtoSubmitRecordQa = submitRecordList.parallelStream()
                            .filter(p -> p.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(thisDtoSubmitRecordQa)) {
                        //将得到的关联集合进行倒序排序，得到第一条数据（取最新的一条数据）
                        thisDtoSubmitRecordQa.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        dto.setSubmitRemark(thisDtoSubmitRecordQa.get(0).getSubmitRemark());
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public DtoInternalAuditPlan save(DtoInternalAuditPlan entity) {
        // 将id的集合转换成字符串
        entity.setAttendee(String.join(",", entity.getAttendeeId()));
        //新增就变成计划编制中
        entity.setStatus(EnumQA.EnumInternalAuditPlanStatus.计划编制中.name());
        entity = super.save(entity);
        //接收前端传的年度计划id
        DtoAnnualPlan dtoAnnualPlan = annualPlanRepository.findOne(entity.getAnnualPlanId());
        if (StringUtil.isNotNull(dtoAnnualPlan)) {
            entity.setAnnualPlanName(dtoAnnualPlan.getAuditPurpose());
        }
        //获取当前登录人
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.内审管理.getValue(), EnumQA.EnumQASubmitType.管理评审计划登记.getValue(),
                null, null, EnumQA.EnumInternalAuditPlanStatus.计划编制中.name(), EnumQA.EnumInternalAuditPlanStatus.计划编制中.name());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增内审管理计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), entity.getId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "新增了内审管理计划", "", "");
        return entity;
    }

    @Transactional
    @Override
    public DtoInternalAuditPlan submit(DtoInternalAuditPlan entity) {
        DtoInternalAuditPlan exist = super.findOne(entity.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(exist.getCreator())) {
            throw new BaseException("只能提交自己创建的内审计划");
        }
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(entity.getNextOperator())).findFirst();
        personOptional.ifPresent(dto -> exist.setNextOperatorName(dto.getCName()));
        //审核完将状态改成计划审核中
        exist.setStatus(EnumQA.EnumInternalAuditPlanStatus.计划审核中.name());
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.内审管理.getValue(), EnumQA.EnumQASubmitType.内审管理计划提交.getValue(),
                entity.getNextOperator(), null, EnumQA.EnumInternalAuditPlanStatus.计划编制中.name(), EnumQA.EnumInternalAuditPlanStatus.计划审核中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交内审管理计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "提交了内审计划。下一步操作人：" + exist.getNextOperatorName(), "", "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoInternalAuditPlan audit(DtoInternalAuditPlan dtoInternalAuditPlan) {
        DtoInternalAuditPlan exist = super.findOne(dtoInternalAuditPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        String result;
        StringBuilder comment = new StringBuilder();
        comment.append(currentUser.getUserName()).append("审核了内审管理计划，审核结果为").append(dtoInternalAuditPlan.getStatus()).append("。");
        if (StringUtil.isNotEmpty(dtoInternalAuditPlan.getSubmitRemark())) {
            comment.append("意见：").append(dtoInternalAuditPlan.getSubmitRemark()).append("。");
        }
        if (EnumQA.EnumInternalAuditPlanStatus.审核通过.name().equals(dtoInternalAuditPlan.getStatus())) {
            result = EnumQA.EnumInternalAuditPlanStatus.计划执行中.name();
            //审核内审计划的时候带出关联表的内审实施计划
            List<DtoInternalAuditImplementPlan> dtoInternalAuditImplementPlans = internalAuditImplementPlanRepository.findByAuditPlanId(exist.getId());
            Set<String> personIds = dtoInternalAuditImplementPlans.parallelStream().map(DtoInternalAuditImplementPlan::getAuditor).collect(Collectors.toSet());
            List<DtoPerson> people = personRepository.findAll(personIds);
            List<DtoSubmitRecordQa> submitList = new ArrayList<>();
            for (DtoInternalAuditImplementPlan dto : dtoInternalAuditImplementPlans) {
                DtoSubmitRecordQa submitRecord = new DtoSubmitRecordQa();
                submitRecord.setObjectId(dto.getId());
                submitRecord.setObjectType(EnumQA.EnumQAObjType.内审管理.getValue());
                submitRecord.setSubmitType(EnumQA.EnumQASubmitType.内审实施计划审核.getValue());
                //内审实施计划的下一步操作人id
                submitRecord.setNextPerson(dto.getAuditor());
                Optional<DtoPerson> auditor = people.parallelStream().filter(p -> p.getId().equals(dto.getAuditor())).findFirst();
                if (!comment.toString().contains("下一步操作人")) {
                    if (auditor.isPresent()) {
                        comment.append("下一步操作人：");
                        comment.append(auditor.get().getCName());
                    }
                } else {
                    auditor.ifPresent(dtoPerson -> comment.append("，").append(dtoPerson.getCName()));
                }
                submitRecord.setSubmitRemark(dtoInternalAuditPlan.getSubmitRemark());
                //操作前状态
                submitRecord.setStateFrom(EnumQA.EnumInternalAuditPlanStatus.计划编制中.name());
                //将所有内审实施计划变成内审检查中
                dto.setStatus(EnumQA.EnumInternalAuditPlanStatus.内审检查中.name());
                //操作后状态
                submitRecord.setStateTo(dto.getStatus());
                submitList.add(submitRecord);
            }
            submitRecordService.createSubmitRecords(submitList, currentUser.getUserId(), currentUser.getUserName(), currentUser.getOrgId());
        } else {
            result = EnumQA.EnumInternalAuditPlanStatus.审核不通过.name();
        }
        exist.setStatus(result);
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.内审管理.getValue(),
                EnumQA.EnumQASubmitType.内审管理计划审核.getValue(), exist.getCreator(), dtoInternalAuditPlan.getSubmitRemark(),
                EnumQA.EnumInternalAuditPlanStatus.计划审核中.name(), result);
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核内审管理计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.内审管理.name(), comment.toString(), dtoInternalAuditPlan.getSubmitRemark(), "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();//获取当前人的信息
        if (StringUtil.isNotEmpty(idList)) {
            List<DtoLog> logList = new ArrayList<>();
            List<DtoInternalAuditPlan> dtoInternalAuditPlans = repository.findAll(idList);
            if (StringUtil.isNotEmpty(dtoInternalAuditPlans)) {
                //遍历要删除的
                for (DtoInternalAuditPlan dto : dtoInternalAuditPlans) {
                    if (!currentUser.getUserId().equals(dto.getCreator())) {
                        throw new BaseException("只能删除自己创建的内审计划");
                    }
                    if (!EnumQA.EnumInternalAuditPlanStatus.计划编制中.name().equals(dto.getStatus())) {
                        throw new BaseException("只能删除未提交的内审计划");
                    }
                    logList.add(logService.getDtoLog(dto.getId(), EnumQA.EnumLogOperateType.删除内审管理计划.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(),
                            EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "删除了内审管理计划"));
                }
            }
            logService.save(logList);
            return super.logicDeleteById(ids);
        }
        return 0;
    }

    @Transactional
    @Override
    public DtoInternalAuditPlan update(DtoInternalAuditPlan entity) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoInternalAuditPlan exist = super.findOne(entity.getId());
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(exist.getCreator())) {
            throw new BaseException("只能修改自己创建的内审计划");
        }
        entity.setAttendee(String.join(",", entity.getAttendeeId()));
        if (entity.getDtoInternalAuditPlanReport() != null) {
            internalAuditPlanReportRepository.save(entity.getDtoInternalAuditPlanReport());
        }
        //接收前端传的年度计划id
        DtoAnnualPlan dtoAnnualPlan = annualPlanRepository.findOne(entity.getAnnualPlanId());
        if (StringUtil.isNotNull(dtoAnnualPlan)) {
            entity.setAnnualPlanName(dtoAnnualPlan.getAuditPurpose());
        }
        super.save(entity);
        return entity;
    }

    @Transactional
    @Override
    public DtoInternalAuditPlan submitReport(DtoInternalAuditPlan dtoInternalAuditPlan) {
        DtoInternalAuditPlan exist = super.findOne(dtoInternalAuditPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        exist.setStatus(EnumQA.EnumInternalAuditPlanStatus.报告审核中.name());
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dtoInternalAuditPlan.getNextOperator())).findFirst();
        personOptional.ifPresent(dto -> exist.setNextOperatorName(dto.getCName()));
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.内审管理.getValue(),
                EnumQA.EnumQASubmitType.内审管理报告提交.getValue(), dtoInternalAuditPlan.getNextOperator(), null,
                exist.getStatus(), EnumQA.EnumInternalAuditPlanStatus.报告审核中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交管理评审报告.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), dtoInternalAuditPlan.getId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "提交了内审管理报告。下一步操作人：" + exist.getNextOperatorName(),
                "", "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoInternalAuditPlan auditReport(DtoInternalAuditPlan dtoInternalAuditPlan) {
        DtoInternalAuditPlan exist = super.findOne(dtoInternalAuditPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        String result;
        if (EnumQA.EnumInternalAuditPlanStatus.审核通过.name().equals(dtoInternalAuditPlan.getStatus())) {
            result = EnumQA.EnumInternalAuditPlanStatus.报告审核通过.name();
        } else {
            result = EnumQA.EnumInternalAuditPlanStatus.报告审核不通过.name();
        }
        exist.setStatus(result);
        //如果没通过，返回给编制人重新提交
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.内审管理.getValue(),
                EnumQA.EnumQASubmitType.内审管理报告审核.getValue(), exist.getCreator(), dtoInternalAuditPlan.getSubmitRemark(),
                EnumQA.EnumInternalAuditPlanStatus.报告审核中.name(), result);
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核内审管理报告.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "审核了内审管理报告，审核结果为" + exist.getStatus() + "。意见：" + dtoInternalAuditPlan.getSubmitRemark() + "。",
                dtoInternalAuditPlan.getSubmitRemark(), "");
        return super.update(exist);
    }

    @Override
    public DtoInternalAuditPlan findOne(String id) {
        DtoInternalAuditPlan dtoInternalAuditPlan = super.findOne(id);
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        //获取对象的审核人员id
        String attendees = dtoInternalAuditPlan.getAttendee();
        if (StringUtil.isNotEmpty(attendees)) {
            //分割逗号并且将数组变成list集合
            List<String> list = Arrays.asList(attendees.split(","));
            //放名字的集合
            List<String> name = new ArrayList<>();
            list.forEach(s ->
                    dtoPersonList.forEach(dtoPerson -> {
                        if (dtoPerson.getId().equals(s)) {
                            name.add(dtoPerson.getCName());
                        }
                    }));
            //将查到的名字转成字符串
            dtoInternalAuditPlan.setAttendeeName(String.join(",", name));
        }
        //获取年度计划表的审核目的
        AnnualPlan annualPlan = annualPlanRepository.findOne(dtoInternalAuditPlan.getAnnualPlanId());
        if (StringUtil.isNotNull(annualPlan)) {
            dtoInternalAuditPlan.setAnnualPlanName(annualPlan.getAuditPurpose());
        }
        DtoInternalAuditPlanReport dtoInternalAuditPlanReport = internalAuditPlanReportRepository.findByInternalPlanId(id);
        dtoInternalAuditPlan.setDtoInternalAuditPlanReport(dtoInternalAuditPlanReport);
        return dtoInternalAuditPlan;
    }

    @Override
    public DtoInternalAuditPlan findAttachPath(String id) {
        return findOne(id);
    }
}