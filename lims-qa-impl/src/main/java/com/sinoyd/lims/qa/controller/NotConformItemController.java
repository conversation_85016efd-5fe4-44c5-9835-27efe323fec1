package com.sinoyd.lims.qa.controller;

import com.sinoyd.lims.qa.dto.DtoGoBack;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.NotConformItemService;
import com.sinoyd.lims.qa.criteria.NotConformItemCriteria;
import com.sinoyd.lims.qa.dto.DtoNotConformItem;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * NotConformItem服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: NotConformItem服务")
@RestController
@RequestMapping("api/qa/notConformItem")
@Validated
public class NotConformItemController extends BaseJpaController<DtoNotConformItem, String, NotConformItemService> {


    /**
     * 分页动态条件查询NotConformItem
     *
     * @param notConformItemCriteria 条件参数
     * @return RestResponse<List < NotConformItem>>
     */
    @ApiOperation(value = "分页动态条件查询NotConformItem", notes = "分页动态条件查询NotConformItem")
    @GetMapping
    public RestResponse<List<DtoNotConformItem>> findByPage(NotConformItemCriteria notConformItemCriteria) {
        PageBean<DtoNotConformItem> pageBean = super.getPageBean();
        RestResponse<List<DtoNotConformItem>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, notConformItemCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询NotConformItem
     *
     * @param id 主键id
     * @return RestResponse<DtoNotConformItem>
     */
    @ApiOperation(value = "按主键查询NotConformItem", notes = "按主键查询NotConformItem")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoNotConformItem> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        DtoNotConformItem notConformItem = service.findOne(id);
        restResponse.setData(notConformItem);
        restResponse.setRestStatus(StringUtil.isNull(notConformItem) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增NotConformItem
     *
     * @param notConformItem 实体列表
     * @return RestResponse<DtoNotConformItem>
     */
    @ApiOperation(value = "新增NotConformItem", notes = "新增NotConformItem")
    @PostMapping
    public RestResponse<DtoNotConformItem> create(@Validated @RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.save(notConformItem));
        return restResponse;
    }

    /**
     * 新增NotConformItem
     *
     * @param notConformItem 实体列表
     * @return RestResponse<DtoNotConformItem>
     */
    @ApiOperation(value = "修改NotConformItem", notes = "修改NotConformItem")
    @PutMapping
    public RestResponse<DtoNotConformItem> update(@Validated @RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.update(notConformItem));
        return restResponse;
    }

    /**
     * 根据id批量删除NotConformItem
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除NotConformItem", notes = "根据id批量删除NotConformItem")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交不符合项登记
     *
     * @param notConformItem 不符合项目实体
     * @return DtoNotConformItem 完成提交的不符合项实体
     */
    @ApiOperation(value = "提交NotConformItem", notes = "提交NotConformItem")
    @PutMapping("/submit")
    public RestResponse<DtoNotConformItem> submit(@RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(notConformItem));
        return restResponse;
    }

    /**
     * 不符合拟定项措施提交
     *
     * @param notConformItem 不符合项目实体
     * @return DtoNotConformItem 完成措施提交的不符合项实体
     */
    @ApiOperation(value = "不符合拟定项措施提交", notes = "不符合拟定项措施提交")
    @PutMapping("/measuresSubmit")
    public RestResponse<DtoNotConformItem> measuresSubmit(@RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.measuresSubmit(notConformItem));
        return restResponse;
    }

    /**
     * 不符合项措施审核
     *
     * @param notConformItem 不符合项目实体
     * @return DtoNotConformItem 完成措施审核的不符合项实体
     */
    @ApiOperation(value = "不符合项措施审核", notes = "不符合项措施审核")
    @PutMapping("/measuresAudit")
    public RestResponse<DtoNotConformItem> measuresAudit(@RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.measuresAudit(notConformItem));
        return restResponse;
    }

    /**
     * 不符合项措施完成情况验证
     *
     * @param notConformItem 要进行完成情况确认的不符合项实体
     * @return DtoNotConformItem 完成确认的不符合项实体
     */
    @ApiOperation(value = "不符合项措施完成情况验证", notes = "不符合项措施完成情况验证")
    @PutMapping("/measuresConfirm")
    public RestResponse<DtoNotConformItem> measuresConfirm(@RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.measuresConfirm(notConformItem));
        return restResponse;
    }

    /**
     * 不符合项验证评价
     *
     * @param notConformItem 不符合项实体
     * @return DtoNotConformItem 完成验证的不符合项实体
     */
    @ApiOperation(value = "不符合项验证评价", notes = "不符合项验证评价")
    @PutMapping("/validate")
    public RestResponse<DtoNotConformItem> validate(@RequestBody DtoNotConformItem notConformItem) {
        RestResponse<DtoNotConformItem> restResponse = new RestResponse<>();
        restResponse.setData(service.validate(notConformItem));
        return restResponse;
    }

    /**
     * 不符合项完成情况批量退回
     *
     * @param goBack 退回对象，封装需要退回的所有实体id，统一退回原因
     * @return 完成退回的不符合项实体集合
     */
    @ApiOperation(value = "根据id批量退回", notes = "根据id批量退回")
    @PutMapping("/back")
    public RestResponse<List<DtoNotConformItem>> confirmBack(@RequestBody DtoGoBack goBack) {
        RestResponse<List<DtoNotConformItem>> restResp = new RestResponse<>();
        List<DtoNotConformItem> notConformItems = service.measuresConfirmBack(goBack);
        restResp.setData(notConformItems);
        restResp.setCount(notConformItems.size());
        return restResp;
    }
}