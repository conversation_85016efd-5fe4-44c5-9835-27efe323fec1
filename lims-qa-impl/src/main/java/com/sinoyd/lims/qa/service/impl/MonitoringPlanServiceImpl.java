package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.criteria.MonitoringPlanCriteria;
import com.sinoyd.lims.qa.dto.*;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.MonitoringPlanDetailRepository;
import com.sinoyd.lims.qa.repository.MonitoringPlanRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.MonitoringPlanService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * MonitoringPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
public class MonitoringPlanServiceImpl extends BaseJpaServiceImpl<DtoMonitoringPlan, String, MonitoringPlanRepository> implements MonitoringPlanService {

    private LogService logService;

    private SubmitRecordRepository submitRecordRepository;

    private PersonRepository personRepository;

    private MonitoringPlanDetailRepository monitoringPlanDetailRepository;

    private SubmitRecordService submitRecordService;

    @Override
    public void findByPage(PageBean<DtoMonitoringPlan> pb, BaseCriteria monitoringPlanCriteria) {
        pb.setEntityName("DtoMonitoringPlan a,DtoSubmitRecordQa b");
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoMonitoringPlan(a.id, a.marker ,a.markDate,a.planName,a.status,a.creator,a.createDate)");
        comRepository.findByPage(pb, monitoringPlanCriteria);
        List<DtoMonitoringPlan> dataList = pb.getData();
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        //获取所有计划明细表
        List<DtoMonitoringPlanDetail> dtoMonitoringPlanDetails = monitoringPlanDetailRepository.findAll();

        if (StringUtil.isNotEmpty(dataList)) {
            //接收DtoMonitoringPlan的Id集合为ids
            List<String> ids = dataList.parallelStream().map(DtoMonitoringPlan::getId).collect(Collectors.toList());
            List<DtoSubmitRecordQa> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumLogObjectType.质量监督.getValue(), ids);
            for (DtoMonitoringPlan dto : dataList) {
                //质量监督记录页面列表中的意见显示最新的意见，如果所有步骤都没有填意见就不显示意见
                if (EnumQA.EnumMonitoringPlanPageFrom.质量监督记录.getValue().equals(((MonitoringPlanCriteria) monitoringPlanCriteria).getPageFrom())) {
                    List<DtoSubmitRecordQa> submitRecordQas = submitRecordList.parallelStream()
                            .filter(s -> StringUtil.isNotEmpty(s.getSubmitRemark())
                                    && s.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(submitRecordQas)) {
                        submitRecordQas.sort(Comparator.comparing(DtoSubmitRecordQa::getCreateDate, Comparator.reverseOrder()));
                        dto.setSubmitRemark(submitRecordQas.get(0).getSubmitRemark());
                    } else {
                        dto.setSubmitRemark("");
                    }
                } else {
                    if (StringUtil.isNotEmpty(submitRecordList)) {
                        //获取DtoSubmitRecord表getObjectId和DtoAnnualPlan表的ID比较得到关联的集合
                        List<DtoSubmitRecordQa> thisDtoSubmitRecordQa = submitRecordList.parallelStream()
                                .filter(p -> p.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(thisDtoSubmitRecordQa)) {
                            //将得到的关联集合进行倒序排序，得到第一条数据（取最新的一条数据）
                            thisDtoSubmitRecordQa.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                            dto.setSubmitRemark(thisDtoSubmitRecordQa.get(0).getSubmitRemark());
                        }
                    }
                }
                //将制定人id冗余成制定人名称
                if (StringUtil.isNotEmpty(dto.getMarker())) {
                    Optional<DtoPerson> dtoPerson = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dto.getMarker())).findFirst();
                    dtoPerson.ifPresent(person -> dto.setMarkerName(person.getCName()));
                }
                if (StringUtil.isNotNull(dtoMonitoringPlanDetails)) {
                    List<DtoMonitoringPlanDetail> list = dtoMonitoringPlanDetails.stream().filter(m -> m.getPlanId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotNull(list)) {
                        dto.setDtoMonitoringPlanDetailList(list);
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public DtoMonitoringPlan save(DtoMonitoringPlan entity) {
        //新增之后将状态改成计划编制中
        entity.setStatus(EnumQA.EnumMonitoringPlanStatus.计划编制中.name());
        entity = super.save(entity);
        //获取当前登录人
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.质量监督.getValue(), EnumQA.EnumQASubmitType.质量监督计划登记.getValue(),
                null, null, EnumQA.EnumMonitoringPlanStatus.计划编制中.name(), EnumQA.EnumMonitoringPlanStatus.计划编制中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增质量监督计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.质量监督流程.name(), entity.getId(),
                EnumQA.EnumLogObjectType.质量监督.name(), currentUser.getUserName() + "新增了质量监督", "", "");
        return entity;
    }


    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        if (StringUtil.isNotNull(idList)) {
            List<DtoLog> logList = new ArrayList<>();
            List<DtoMonitoringPlan> dtoMonitoringPlans = repository.findAll(idList);
            if (StringUtil.isNotEmpty(dtoMonitoringPlans)) {
                //遍历要删除集合
                for (DtoMonitoringPlan dto : dtoMonitoringPlans) {
                    if (!currentUser.getUserId().equals(dto.getCreator())) {
                        throw new BaseException("只能删除自己创建的质量监督计划");
                    }
                    if (!EnumQA.EnumMonitoringPlanStatus.计划编制中.name().equals(dto.getStatus())) {
                        throw new BaseException("只能删除编制中的数据");
                    }
                    logList.add(logService.getDtoLog(dto.getId(), EnumQA.EnumLogOperateType.删除质量监督计划.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.质量监督流程.name(),
                            EnumQA.EnumLogObjectType.质量监督.name(), currentUser.getUserName() + "删除了质量监督计划"));
                }
            }
            logService.save(logList);
            return super.logicDeleteById(ids);
        }
        return 0;
    }

    @Transactional
    @Override
    public DtoMonitoringPlan submit(DtoMonitoringPlan dtoMonitoringPlan) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoMonitoringPlan exist = super.findOne(dtoMonitoringPlan.getId());
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(exist.getCreator())) {
            throw new BaseException("只能提交自己创建的质量监督");
        }
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dtoMonitoringPlan.getNextOperator())).findFirst();
        personOptional.ifPresent(dto -> exist.setNextOperatorName(dto.getCName()));
        exist.setStatus(EnumQA.EnumMonitoringPlanStatus.计划评审中.name());
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.质量监督.getValue(),
                EnumQA.EnumQASubmitType.质量监督计划提交.getValue(), dtoMonitoringPlan.getNextOperator(), null,
                exist.getStatus(), EnumQA.EnumMonitoringPlanStatus.计划评审中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交质量监督计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.质量监督流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.质量监督.name(), currentUser.getUserName() + "提交了质量监督计划。下一步操作人：" + exist.getNextOperatorName(), "", "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoMonitoringPlan audit(DtoMonitoringPlan dtoMonitoringPlan) {
        DtoMonitoringPlan exist = super.findOne(dtoMonitoringPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        String result;
        StringBuilder comment = new StringBuilder();
        comment.append(currentUser.getUserName())
                .append("审核了质量监督计划，审核结果为：")
                .append(dtoMonitoringPlan.getStatus())
                .append("。");
        if (StringUtil.isNotEmpty(dtoMonitoringPlan.getSubmitRemark())) {
            comment.append("意见：").append(dtoMonitoringPlan.getSubmitRemark()).append("。");
        }
        if (EnumQA.EnumMonitoringPlanStatus.审核通过.name().equals(dtoMonitoringPlan.getStatus())) {
            result = EnumQA.EnumInternalAuditPlanStatus.计划执行中.name();
            //审核质量监督计划的时候带出计划明细表
            List<DtoMonitoringPlanDetail> dtoMonitoringPlanDetailList = monitoringPlanDetailRepository.findByPlanId(exist.getId());
            Set<String> nextOperatorIds = dtoMonitoringPlanDetailList.parallelStream().map(DtoMonitoringPlanDetail::getDutyPersonId).collect(Collectors.toSet());
            List<DtoPerson> nextOperators = personRepository.findAll(nextOperatorIds);
            List<DtoSubmitRecordQa> submitList = new ArrayList<>();
            for (DtoMonitoringPlanDetail dto : dtoMonitoringPlanDetailList) {
                DtoSubmitRecordQa submitRecord = new DtoSubmitRecordQa();
                submitRecord.setObjectId(dto.getId());
                submitRecord.setObjectType(EnumQA.EnumQAObjType.质量监督.getValue());
                submitRecord.setSubmitType(EnumQA.EnumQASubmitType.质量监督计划明细提交.getValue());
                submitRecord.setNextPerson(dto.getDutyPersonId());
                Optional<DtoPerson> temp = nextOperators.parallelStream().filter(p -> p.getId().equals(dto.getDutyPersonId())).findFirst();
                if (!comment.toString().contains("下一步操作人")) {
                    if (temp.isPresent()) {
                        comment.append("下一步操作人：").append(temp.get().getCName());
                    }
                } else {
                    if (temp.isPresent()) {
                        comment.append("，").append(temp.get().getCName());
                    }
                }
                submitRecord.setStateFrom(EnumQA.EnumMonitoringPlanStatus.计划明细编制中.name());
                submitRecord.setSubmitRemark(dtoMonitoringPlan.getSubmitRemark());
                dto.setStatus(EnumQA.EnumMonitoringPlanStatus.执行中.name());
                submitRecord.setStateTo(dto.getStatus());
                submitList.add(submitRecord);
            }
            submitRecordService.createSubmitRecords(submitList, currentUser.getUserId(), currentUser.getUserName(), currentUser.getOrgId());
        } else {
            result = EnumQA.EnumMonitoringPlanStatus.审核不通过.name();
        }
        exist.setStatus(result);
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.质量监督.getValue(),
                EnumQA.EnumQASubmitType.质量监督计划审核.getValue(), exist.getCreator(), dtoMonitoringPlan.getSubmitRemark(),
                exist.getStatus(), result);
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核质量监督计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.质量监督流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.质量监督.name(), comment.toString(), dtoMonitoringPlan.getSubmitRemark(), "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoMonitoringPlan auditRecord(DtoMonitoringPlan dtoMonitoringPlan) {
        DtoMonitoringPlan exist = super.findOne(dtoMonitoringPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        exist.setStatus(EnumQA.EnumMonitoringPlanStatus.质量监督完毕.name());
        //如果没通过，返回给编制人重新编制
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.质量监督.getValue(),
                EnumQA.EnumQASubmitType.质量监督记录审核.getValue(), exist.getCreator(), dtoMonitoringPlan.getSubmitRemark(),
                EnumQA.EnumMonitoringPlanStatus.质量监督中.name(), EnumQA.EnumMonitoringPlanStatus.质量监督完毕.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核质量监督记录.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.质量监督流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.质量监督.name(), currentUser.getUserName() + "审核了质量监督记录，审核结果为审核通过。",
                dtoMonitoringPlan.getSubmitRemark(), "");
        return super.update(exist);
    }

    @Override
    public DtoMonitoringPlan findOne(String id) {
        DtoMonitoringPlan dtoMonitoringPlan = repository.findOne(id);
        if (StringUtil.isNotNull(dtoMonitoringPlan)) {
            List<DtoMonitoringPlanDetail> dtoMonitoringPlanDetailList = monitoringPlanDetailRepository.findByPlanId(dtoMonitoringPlan.getId());
            if (StringUtil.isNotNull(dtoMonitoringPlanDetailList)) {
                dtoMonitoringPlan.setDtoMonitoringPlanDetailList(dtoMonitoringPlanDetailList);
            }
        }
        return dtoMonitoringPlan;
    }

    @Override
    public DtoMonitoringPlan findAttachPath(String id) {
        return findOne(id);
    }

    @Autowired
    public void setLogService(LogService logService) {
        this.logService = logService;
    }

    @Autowired
    public void setSubmitRecordRepository(SubmitRecordRepository submitRecordRepository) {
        this.submitRecordRepository = submitRecordRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setMonitoringPlanDetailRepository(MonitoringPlanDetailRepository monitoringPlanDetailRepository) {
        this.monitoringPlanDetailRepository = monitoringPlanDetailRepository;
    }

    @Autowired
    public void setSubmitRecordService(SubmitRecordService submitRecordService) {
        this.submitRecordService = submitRecordService;
    }
}
