package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.lims.qa.dto.DtoReviewPlanReport;
import com.sinoyd.lims.qa.repository.ReviewPlanReportRepository;
import com.sinoyd.lims.qa.service.ReviewPlanReportService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * ReviewPlanReport操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @Service
public class ReviewPlanReportServiceImpl extends BaseJpaServiceImpl<DtoReviewPlanReport,String,ReviewPlanReportRepository> implements ReviewPlanReportService {

    @Override
    public void findByPage(PageBean<DtoReviewPlanReport> pb, BaseCriteria reviewPlanReportCriteria) {
        pb.setEntityName("DtoReviewPlanReport a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, reviewPlanReportCriteria);
    }
}