package com.sinoyd.lims.qa.service.impl;


import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.DtoAnnualPlan;
import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.AnnualPlanRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.AnnualPlanService;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * AnnualPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class AnnualPlanServiceImpl extends BaseJpaServiceImpl<DtoAnnualPlan, String, AnnualPlanRepository> implements AnnualPlanService {

    private final SubmitRecordService submitRecordService;

    private final SubmitRecordRepository submitRecordRepository;

    private final LogService logService;

    private final PersonRepository personRepository;

    @Override
    public void findByPage(PageBean<DtoAnnualPlan> pb, BaseCriteria annualPlanCriteria) {
        pb.setEntityName("DtoAnnualPlan a,DtoSubmitRecordQa b");
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoAnnualPlan(a.id, a.year, a.planType, a.makePersonName, " +
                "a.makeTime, a.auditContent, a.auditPurpose, a.creator, a.createDate, a.status, a.remark , a.makePersonId)");
        comRepository.findByPage(pb, annualPlanCriteria);
        List<DtoAnnualPlan> dataList = pb.getData();//获取DtoAnnualPlan的集合
        if (StringUtil.isNotEmpty(dataList)) {
            //获取所有系统用户
            List<DtoPerson> dtoPersonList = personRepository.findAll();
            //获取年度计划查询结果集的所有查询实体id
            List<String> ids = dataList.parallelStream().map(DtoAnnualPlan::getId).collect(Collectors.toList());
            //根据id集合查询对应的记录对象
            List<DtoSubmitRecordQa> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumLogObjectType.年度计划.getValue(), ids);
            for (DtoAnnualPlan dto : dataList) {
                //填充创建人姓名（实体保存时自动填充creator为创建人id，需要根据id获取创建人姓名）
                Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dto.getCreator())).findFirst();
                personOptional.ifPresent(dtoPerson -> dto.setCreatorName(dtoPerson.getCName()));
                if (StringUtil.isNotEmpty(submitRecordList)) {
                    //获取DtoSubmitRecord表ObjectId和DtoAnnualPlan表的ID比较得到关联的集合
                    List<DtoSubmitRecordQa> thisDtoSubmitRecordQa = submitRecordList.parallelStream()
                            .filter(p -> p.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(thisDtoSubmitRecordQa)) {
                        //将得到的关联集合进行倒序排序，得到第一条数据（取最新的一条数据）
                        thisDtoSubmitRecordQa.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        dto.setSubmitRemark(thisDtoSubmitRecordQa.get(0).getSubmitRemark());
                        Optional<DtoSubmitRecordQa> submitRecordOptional = thisDtoSubmitRecordQa.parallelStream().filter(p -> p.getStateTo().equals(EnumQA.EnumAnnualPlanStatus.审核通过.name())).findFirst();
                        submitRecordOptional.ifPresent(dtoSubmitRecord -> dto.setAuditTime(dtoSubmitRecord.getSubmitTime()));
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public DtoAnnualPlan save(DtoAnnualPlan entity) {
        //新增之后将状态改成计划编制中
        entity.setStatus(EnumQA.EnumAnnualPlanStatus.编制中.name());
        entity = super.save(entity);
        //获取当前登录人
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.年度计划.getValue(), EnumQA.EnumQASubmitType.年度计划登记.getValue(),
                null, null, EnumQA.EnumAnnualPlanStatus.编制中.name(), EnumQA.EnumAnnualPlanStatus.编制中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增年度计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.年度计划流程.name(), entity.getId(),
                EnumQA.EnumLogObjectType.年度计划.name(), currentUser.getUserName() + "新增了年度计划", "", "");
        return entity;
    }

    /**
     * @param entity 页面传递的实体
     * @return 修改后的实体
     */
    @Transactional
    @Override
    public DtoAnnualPlan update(DtoAnnualPlan entity) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoAnnualPlan exist = super.findOne(entity.getId());
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(exist.getCreator())) {
            throw new BaseException("只能修改自己创建的年度计划");
        }
        return super.update(entity);
    }

    /**
     * 提交方法
     *
     * @param pageDto 提交的年度计划实体对象
     * @return 提交的年度计划实体对象
     */
    @Transactional
    @Override
    public DtoAnnualPlan submit(DtoAnnualPlan pageDto) {
        DtoAnnualPlan dbDto = super.findOne(pageDto.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(dbDto.getCreator())) {
            throw new BaseException("只能提交自己创建的年度计划");
        }
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(pageDto.getNextOperator())).findFirst();
        personOptional.ifPresent(dto -> dbDto.setNextOperatorName(dto.getCName()));
        submitRecordService.createSubmitRecord(dbDto.getId(), EnumQA.EnumQAObjType.年度计划.getValue(),
                EnumQA.EnumQASubmitType.年度计划提交.getValue(), pageDto.getNextOperator(), null,
                dbDto.getStatus(), EnumQA.EnumAnnualPlanStatus.审核中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交年度计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.年度计划流程.name(), dbDto.getId(),
                EnumQA.EnumLogObjectType.年度计划.name(), currentUser.getUserName() + "提交了年度计划。下一步操作人：" + dbDto.getNextOperatorName(), "", "");
        dbDto.setStatus(EnumQA.EnumAnnualPlanStatus.审核中.name());//计划审核中
        return super.save(dbDto);
    }

    /**
     * 审核方法
     *
     * @param dtoAnnualPlan 实体
     * @return 审核实体
     */
    @Transactional
    @Override
    public DtoAnnualPlan audit(DtoAnnualPlan dtoAnnualPlan) {
        DtoAnnualPlan exist = super.findOne(dtoAnnualPlan.getId());
        exist.setStatus(dtoAnnualPlan.getStatus());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.年度计划.getValue(),
                EnumQA.EnumQASubmitType.年度计划审核.getValue(), dtoAnnualPlan.getNextOperator(), dtoAnnualPlan.getSubmitRemark(),
                exist.getStatus(), dtoAnnualPlan.getStatus());
        String opinion = "";
        if (StringUtil.isNotEmpty(dtoAnnualPlan.getSubmitRemark())) {
            opinion = "意见" + dtoAnnualPlan.getSubmitRemark();
        }
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核年度计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.年度计划流程.name(), dtoAnnualPlan.getId(),
                EnumQA.EnumLogObjectType.年度计划.name(), currentUser.getUserName() + "审核了年度计划，审核结果为：" + dtoAnnualPlan.getStatus() + "。" + opinion,
                dtoAnnualPlan.getSubmitRemark(), "");
        return super.save(exist);
    }

    @Override
    public DtoAnnualPlan findAttachPath(String id) {
        return super.findOne(id);
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        if (StringUtil.isNotNull(idList)) {
            List<DtoLog> logList = new ArrayList<>();
            List<DtoAnnualPlan> dtoAnnualPlans = repository.findAll(idList);
            if (StringUtil.isNotEmpty(dtoAnnualPlans)) {
                //遍历要删除的用户
                for (DtoAnnualPlan dto : dtoAnnualPlans) {
                    if (!currentUser.getUserId().equals(dto.getCreator())) {
                        throw new BaseException("只能删除自己创建的年度计划");
                    }
                    logList.add(logService.getDtoLog(dto.getId(), EnumQA.EnumLogOperateType.删除年度计划.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.年度计划流程.name(),
                            EnumQA.EnumLogObjectType.年度计划.name(), currentUser.getUserName() + "删除了年度计划"));
                }
            }
            logService.save(logList);
            return super.logicDeleteById(ids);
        }
        return 0;
    }
}
