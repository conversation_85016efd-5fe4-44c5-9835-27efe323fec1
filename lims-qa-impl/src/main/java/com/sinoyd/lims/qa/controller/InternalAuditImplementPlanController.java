package com.sinoyd.lims.qa.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.criteria.InternalAuditImplementPlanCriteria;
import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoInternalAuditImplementPlan;
import com.sinoyd.lims.qa.service.InternalAuditImplementPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * InternalAuditImplementPlan服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: InternalAuditImplementPlan服务")
@RestController
@RequestMapping("api/qa/internalAuditImplementPlan")
@Validated
public class InternalAuditImplementPlanController extends BaseJpaController<DtoInternalAuditImplementPlan, String, InternalAuditImplementPlanService> {


    /**
     * 分页动态条件查询InternalAuditImplementPlan
     *
     * @param internalAuditImplementPlanCriteria 条件参数
     * @return RestResponse<List   <   InternalAuditImplementPlan>>
     */
    @ApiOperation(value = "分页动态条件查询InternalAuditImplementPlan", notes = "分页动态条件查询InternalAuditImplementPlan")
    @GetMapping
    public RestResponse<List<DtoInternalAuditImplementPlan>> findByPage(InternalAuditImplementPlanCriteria internalAuditImplementPlanCriteria) {
        PageBean<DtoInternalAuditImplementPlan> pageBean = super.getPageBean();
        RestResponse<List<DtoInternalAuditImplementPlan>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, internalAuditImplementPlanCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询AnnualPlan
     *
     * @param id 主键id
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "按主键查询AnnualPlan", notes = "按主键查询AnnualPlan")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoInternalAuditImplementPlan> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoInternalAuditImplementPlan> restResponse = new RestResponse<>();
        DtoInternalAuditImplementPlan internalAuditImplementPlan = service.findOne(id);
        restResponse.setData(internalAuditImplementPlan);
        restResponse.setRestStatus(StringUtil.isNull(internalAuditImplementPlan) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增InternalAuditImplementPlan
     *
     * @param internalAuditImplementPlan 实体列表
     * @return RestResponse<DtoInternalAuditImplementPlan>
     */
    @ApiOperation(value = "新增InternalAuditImplementPlan", notes = "新增InternalAuditImplementPlan")
    @PostMapping
    public RestResponse<DtoInternalAuditImplementPlan> create(@Validated @RequestBody DtoInternalAuditImplementPlan internalAuditImplementPlan) {
        RestResponse<DtoInternalAuditImplementPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.save(internalAuditImplementPlan));
        return restResponse;
    }

    /**
     * 新增InternalAuditImplementPlan
     *
     * @param internalAuditImplementPlan 实体列表
     * @return RestResponse<DtoInternalAuditImplementPlan>
     */
    @ApiOperation(value = "修改InternalAuditImplementPlan", notes = "修改InternalAuditImplementPlan")
    @PutMapping
    public RestResponse<DtoInternalAuditImplementPlan> update(@Validated @RequestBody DtoInternalAuditImplementPlan internalAuditImplementPlan) {
        RestResponse<DtoInternalAuditImplementPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.update(internalAuditImplementPlan));
        return restResponse;
    }

    /**
     * "根据id批量删除InternalAuditImplementPlan
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除InternalAuditImplementPlan", notes = "根据id批量删除InternalAuditImplementPlan")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交DtoInternalAuditImplementPlan
     *
     * @param dtoInternalAuditImplementPlan 实体列表
     * @return RestResponse<DtoInternalAuditImplementPlan>
     */
    @ApiOperation(value = "提交DtoInternalAuditPlan", notes = "提交DtoInternalAuditPlan")
    @PutMapping(path = "/submit")
    public RestResponse<DtoInternalAuditImplementPlan> submit(@RequestBody DtoInternalAuditImplementPlan dtoInternalAuditImplementPlan) {
        RestResponse<DtoInternalAuditImplementPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(dtoInternalAuditImplementPlan));
        return restResponse;
    }

    /**
     * 退回DtoInternalAuditImplementPlan
     *
     * @param dtoGoBack
     * @return RestResponse<DtoInternalAuditImplementPlan>
     */
    @ApiOperation(value = "退回DtoInternalAuditImplementPlan", notes = "退回DtoInternalAuditImplementPlan")
    @PutMapping(path = "/goBack")
    public RestResponse<List<DtoInternalAuditImplementPlan>> goBack(@RequestBody DtoGoBack dtoGoBack) {
        RestResponse<List<DtoInternalAuditImplementPlan>> restResponse = new RestResponse<>();
        restResponse.setData(service.goBack(dtoGoBack));
        return restResponse;
    }

}