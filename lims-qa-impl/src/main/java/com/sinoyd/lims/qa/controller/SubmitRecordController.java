package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import com.sinoyd.lims.qa.criteria.SubmitRecordCriteria;
import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SubmitRecord服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/3/24
 * @since V100R001
 */
 @Api(tags = "示例: SubmitRecord服务")
 @RestController("qaSubmitRecordController")
 @RequestMapping("api/qa/submitRecord")
 @Validated
 public class SubmitRecordController extends BaseJpaController<DtoSubmitRecordQa, String,SubmitRecordService> {


    /**
     * 分页动态条件查询SubmitRecord
     * @param submitRecordCriteria 条件参数
     * @return RestResponse<List<SubmitRecord>>
     */
     @ApiOperation(value = "分页动态条件查询SubmitRecord", notes = "分页动态条件查询SubmitRecord")
     @GetMapping
     public RestResponse<List<DtoSubmitRecordQa>> findByPage(SubmitRecordCriteria submitRecordCriteria) {
         PageBean<DtoSubmitRecordQa> pageBean = super.getPageBean();
         RestResponse<List<DtoSubmitRecordQa>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, submitRecordCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询SubmitRecord
     * @param id 主键id
     * @return RestResponse<DtoSubmitRecord>
     */
     @ApiOperation(value = "按主键查询SubmitRecord", notes = "按主键查询SubmitRecord")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoSubmitRecordQa> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoSubmitRecordQa> restResponse = new RestResponse<>();
         DtoSubmitRecordQa submitRecord = service.findOne(id);
         restResponse.setData(submitRecord);
         restResponse.setRestStatus(StringUtil.isNull(submitRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增SubmitRecord
     * @param submitRecord 实体列表
     * @return RestResponse<DtoSubmitRecord>
     */
     @ApiOperation(value = "新增SubmitRecord", notes = "新增SubmitRecord")
     @PostMapping
     public RestResponse<DtoSubmitRecordQa> create(@Validated @RequestBody DtoSubmitRecordQa submitRecord) {
         RestResponse<DtoSubmitRecordQa> restResponse = new RestResponse<>();
         restResponse.setData(service.save(submitRecord));
         return restResponse;
      }

     /**
     * 新增SubmitRecord
     * @param submitRecord 实体列表
     * @return RestResponse<DtoSubmitRecord>
     */
     @ApiOperation(value = "修改SubmitRecord", notes = "修改SubmitRecord")
     @PutMapping
     public RestResponse<DtoSubmitRecordQa> update(@Validated @RequestBody DtoSubmitRecordQa submitRecord) {
         RestResponse<DtoSubmitRecordQa> restResponse = new RestResponse<>();
         restResponse.setData(service.update(submitRecord));
         return restResponse;
      }

    /**
     * "根据id批量删除SubmitRecord
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SubmitRecord", notes = "根据id批量删除SubmitRecord")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }