package com.sinoyd.lims.qa.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * InternalCheckInfo查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InternalCheckInfoCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 内审实施计划id
     */
    private String implementPlanId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotEmpty(this.implementPlanId)) {
            condition.append(" and implementPlanId = :implementPlanId");
            values.put("implementPlanId", this.implementPlanId);
        }
        return condition.toString();
    }
}