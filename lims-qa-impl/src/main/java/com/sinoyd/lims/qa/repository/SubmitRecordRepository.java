package com.sinoyd.lims.qa.repository;

import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * SubmitRecord数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/24
 * @since V100R001
 */
@Repository("qaSubmitRecordRepository")
public interface SubmitRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoSubmitRecordQa, String> {

    List<DtoSubmitRecordQa> findByObjectTypeAndObjectIdIn(Integer objectType, List<String> objectIds);

    /**
     * 根据被记录对象id和操作步骤获取记录对象
     *
     * @param objectId   关联对象id
     * @param submitType 操作步骤
     * @return 记录对象结果集
     */
    List<DtoSubmitRecordQa> findByObjectIdAndSubmitType(String objectId, Integer submitType);

    /**
     * 根据关联对象id和操作步骤查询记录对象，根据创建时间倒序
     *
     * @param objectId
     * @param submitType
     * @return 记录对象结果集
     */
    List<DtoSubmitRecordQa> findByObjectIdAndSubmitTypeOrderByCreateDateDesc(String objectId, Integer submitType);

    /**
     * 根据关联对象id查询
     *
     * @param objectId 关联对象id
     * @return 实体列表
     */
    List<DtoSubmitRecordQa> findByObjectId(String objectId);

    /**
     * 根据关联对象id列表查询
     *
     * @param objectIds 关联对象id列表
     * @return 实体列表
     */
    List<DtoSubmitRecordQa> findByObjectIdIn(List<String> objectIds);

    List<DtoSubmitRecordQa> findBySubmitTypeAndObjectIdIn(Integer submitType, List<String> objectIds);
}