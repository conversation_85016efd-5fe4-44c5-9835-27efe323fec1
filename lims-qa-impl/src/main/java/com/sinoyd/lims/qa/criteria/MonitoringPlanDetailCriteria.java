package com.sinoyd.lims.qa.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * MonitoringPlanDetail查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitoringPlanDetailCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 计划id
     */
    private String planId;

    /**
     * 责任部门，质量监督内容关键字
     */
    private String key;

    /**
     * 状态， 2已处理、1待处理、-1所有
     */
    private Integer status;

    /**
     * 状态
     */
    private String checkStatus;

    /**
     * 部门id
     */
    private String dutyDept;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");
        List<String> statusList = new ArrayList();

        if (StringUtil.isNotEmpty(this.planId)) {
            condition.append(" and a.planId = :planId");
            values.put("planId", this.planId);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and content like :key");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(checkStatus)) {
            condition.append(" and a.status = :checkStatus");
            values.put("checkStatus", this.checkStatus);
        }
        if (StringUtil.isNotEmpty(dutyDept)) {
            condition.append(" and a.dutyDept = :dutyDept");
            values.put("dutyDept", this.dutyDept);
        }
        if (StringUtil.isNotNull(status)) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.执行完毕.name());
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.执行中.name());
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.执行完毕.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.执行中.name());
            }
            condition.append(" and a.status in :statusList");
            condition.append(" and a.dutyPersonId = :loginUser ");
            CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
            values.put("loginUser", user.getUserId());
            values.put("statusList", statusList);

        }
        return condition.toString();
    }
}