package com.sinoyd.lims.qa.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * NotConformItem查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotConformItemCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 1为不符合项登记，2为不符合项拟定措施，3为不符合项批准措施，4为不符合项完成情况，5为不符合项验证评价，6为不符合项进度查询
     */
    private int pageFrom;
    /**
     * 发现时间查询开始时间
     */
    private String startTime;
    /**
     * 发现时间查询结束时间
     */
    private String endTime;
    /**
     * 来源类型
     */
    private String sourceType;
    /**
     * 不符合项类型
     */
    private String type;
    /**
     * 关键字，可以对不符合项描述进行模糊查询
     */
    private String key;
    /**
     * 状态：-1为所有，1为待处理，2为已处理
     */
    private int status;
    /**
     * 不符合项状态
     * 1为登记中
     * 2为措施拟定中
     * 3为措施批准中
     * 4为纠正中
     * 5为验证中
     * 6为已办结
     */
    private Integer notConformItemStatus;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        Calendar calendar = new GregorianCalendar();
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");
        //发现时间开始查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.ncFindDate >= :startTime");
            values.put("startTime", date);
        }
        //发现时间结束查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.ncFindDate < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.sourceType)) {
            condition.append(" and a.ncSourceType = :sourceType");
            values.put("sourceType", this.sourceType);
        }
        if (StringUtil.isNotEmpty(this.type)) {
            condition.append(" and a.ncType = :type");
            values.put("type", this.type);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and a.ncDescribe like :key");
            values.put("key", "%" + this.key + "%");
        }
        List<String> statusList = new ArrayList<>();
        //pageFrom为6时显示所有的进度
        if (this.pageFrom == 6) {
            if (StringUtils.isNotNull(this.notConformItemStatus)) {
                switch (this.notConformItemStatus) {
                    //登记中
                    case 1:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项登记中.name());
                        break;
                    //措施拟定中
                    case 2:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name());
                        break;
                    //措施批准中
                    case 3:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name());
                        break;
                    //纠正中
                    case 4:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name());
                        break;
                    //验证中
                    case 5:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        break;
                    case 6:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        break;
                    case 7:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施拟定不通过.name());
                        break;
                    case 8:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施批准不通过.name());
                        break;
                    case 9:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正不通过.name());
                        break;
                    case 10:
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name());
                        break;
                    //默认显示全部
                    default:
                        statusList = null;
                        break;
                }
                if (StringUtil.isNotEmpty(statusList)) {
                    condition.append(" and a.status in :statusList");
                    values.put("statusList", statusList);
                }
            }
        } else {
            switch (this.pageFrom) {
                //1为不符合项登记
                case 1:
                    //已处理显示除登记中和拟定不通过以外所有数据
                    if (this.status == 2) {
                        for (EnumQA.EnumNotConformItemStatus item : EnumQA.EnumNotConformItemStatus.values()) {
                            if (!item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项登记中.name())
                                    && !item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项措施拟定不通过.name())) {
                                statusList.add(item.name());
                            }
                        }
                    }
                    //不传默认显示待处理，待处理显示登记中和措施拟定不通过
                    else if (this.status == 1) {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项登记中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施拟定不通过.name());
                    }
                    //不传则默认全部显示
                    else {
                        statusList = null;
                    }
                    if (StringUtil.isNotEmpty(statusList)) {
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                    }
                    break;
                //2为不符合项拟定措施
                case 2:
                    //全部显示为显示除登记中和拟定不通过之外的所有数据
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        List<String> statusListB = new ArrayList<>();
                        for (EnumQA.EnumNotConformItemStatus item : EnumQA.EnumNotConformItemStatus.values()) {
                            if (!item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项登记中.name())
                                    && !item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name())
                                    && !item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项措施拟定不通过.name())) {
                                statusListB.add(item.name());
                            }
                        }
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name());
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项措施批准不通过.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已处理显示除登记中，措施拟定不通过，措施拟定中之外数据
                    else if (this.status == 2) {
                        for (EnumQA.EnumNotConformItemStatus item : EnumQA.EnumNotConformItemStatus.values()) {
                            if (!item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项登记中.name())
                                    && !item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项措施批准不通过.name())
                                    && !item.name().equals(EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name())) {
                                statusList.add(item.name());
                            }
                        }
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //待处理显示措施拟定中和措施批准不通过数据
                    else {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施拟定中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施批准不通过.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
                //3为不符合项批准措施
                case 3:
                    //全部显示为措施批准中，措施纠正中，纠正不通过，验证中，验证不通过，已办结
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name());
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正不通过.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name());
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name());
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已处理显示纠正中，纠正不通过，验证中，验证不通过，已办结
                    else if (this.status == 2) {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //待处理显示批准中，纠正不通过
                    else {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施批准中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正不通过.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
                //4为符合项完成情况
                case 4:
                    //全部显示纠正中，验证中，验证不通过，已办结
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name());
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已处理显示验证中，已办结
                    else if (this.status == 2) {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //未处理显示纠正中，验证不通过
                    else {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项措施纠正中.name());
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证不通过.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
                //5为不符合项验证评价
                case 5:
                    //全部显示为验证中，已办结
                    if (this.status == -1) {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    //已处理显示已办结
                    else if (this.status == 2) {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项已办结.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //未处理显示验证中
                    else {
                        statusList.add(EnumQA.EnumNotConformItemStatus.不符合项验证中.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    break;
            }
        }
        return condition.toString();
    }
}