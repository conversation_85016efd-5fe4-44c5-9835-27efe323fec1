package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.qa.dto.DtoInternalAuditImplementPlan;
import com.sinoyd.lims.qa.dto.DtoInternalCheckInfo;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.InternalAuditImplementPlanRepository;
import com.sinoyd.lims.qa.repository.InternalCheckInfoRepository;
import com.sinoyd.lims.qa.service.InternalCheckInfoService;
import com.sinoyd.lims.qa.service.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * InternalCheckInfo操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
public class InternalCheckInfoServiceImpl extends BaseJpaServiceImpl<DtoInternalCheckInfo, String, InternalCheckInfoRepository> implements InternalCheckInfoService {

    private LogService logService;

    private InternalAuditImplementPlanRepository internalAuditImplementPlanRepository;

    @Override
    public void findByPage(PageBean<DtoInternalCheckInfo> pb, BaseCriteria internalCheckInfoCriteria) {
        pb.setEntityName("DtoInternalCheckInfo a ");
        pb.setSelect("select a");
        comRepository.findByPage(pb, internalCheckInfoCriteria);
    }

    @Transactional
    @Override
    public DtoInternalCheckInfo save(DtoInternalCheckInfo entity) {
        entity = super.save(entity);
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoInternalAuditImplementPlan internalAuditImplementPlan = internalAuditImplementPlanRepository.findOne(entity.getImplementPlanId());
        String checkResult = EnumQA.EnumCheckResultType.getByValue(entity.getCheckResult());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增内审管理计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), internalAuditImplementPlan.getAuditPlanId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "新增了检查项，评价结果：" + checkResult, "", "");
        return entity;
    }

    @Autowired
    public void setLogService(LogService logService) {
        this.logService = logService;
    }

    @Autowired
    public void setInternalAuditImplementPlanRepository(InternalAuditImplementPlanRepository internalAuditImplementPlanRepository) {
        this.internalAuditImplementPlanRepository = internalAuditImplementPlanRepository;
    }
}