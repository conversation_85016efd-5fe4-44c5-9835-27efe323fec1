package com.sinoyd.lims.qa.repository;

import com.sinoyd.lims.qa.dto.DtoMonitoringPlanDetail;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * MonitoringPlanDetail数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface MonitoringPlanDetailRepository extends IBaseJpaRepository<DtoMonitoringPlanDetail, String> {

    /**
     * 通过质量监督id查询
     *
     * @param planId
     * @return 实体列表
     */
    List<DtoMonitoringPlanDetail> findByPlanId(String planId);
}