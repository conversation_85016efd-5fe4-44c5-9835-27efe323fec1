package com.sinoyd.lims.qa.repository;

import com.sinoyd.lims.qa.dto.DtoAnnualPlan;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * AnnualPlan数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface AnnualPlanRepository extends IBaseJpaRepository<DtoAnnualPlan, String> {


    /**
     * 按照 ids查询DtoAnnualPlan集合
     *
     * @param ids 主键集合
     * @return 实体列表
     */
    List<DtoAnnualPlan> findByIdIn(List<String> ids);


}