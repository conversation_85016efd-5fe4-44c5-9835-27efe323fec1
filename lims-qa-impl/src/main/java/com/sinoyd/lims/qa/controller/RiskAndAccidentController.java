package com.sinoyd.lims.qa.controller;

import com.sinoyd.lims.qa.dto.DtoGoBack;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.RiskAndAccidentService;
import com.sinoyd.lims.qa.criteria.RiskAndAccidentCriteria;
import com.sinoyd.lims.qa.dto.DtoRiskAndAccident;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * RiskAndAccident服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/15
 * @since V100R001
 */
@Api(tags = "示例: RiskAndAccident服务")
@RestController
@RequestMapping("api/qa/riskAndAccident")
@Validated
public class RiskAndAccidentController extends BaseJpaController<DtoRiskAndAccident, String, RiskAndAccidentService> {


    /**
     * 分页动态条件查询RiskAndAccident
     *
     * @param riskAndAccidentCriteria 条件参数
     * @return RestResponse<List < RiskAndAccident>>
     */
    @ApiOperation(value = "分页动态条件查询RiskAndAccident", notes = "分页动态条件查询RiskAndAccident")
    @GetMapping
    public RestResponse<List<DtoRiskAndAccident>> findByPage(RiskAndAccidentCriteria riskAndAccidentCriteria) {
        PageBean<DtoRiskAndAccident> pageBean = super.getPageBean();
        RestResponse<List<DtoRiskAndAccident>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, riskAndAccidentCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询RiskAndAccident
     *
     * @param id 主键id
     * @return RestResponse<DtoRiskAndAccident>
     */
    @ApiOperation(value = "按主键查询RiskAndAccident", notes = "按主键查询RiskAndAccident")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoRiskAndAccident> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        DtoRiskAndAccident riskAndAccident = service.findOne(id);
        restResponse.setData(riskAndAccident);
        restResponse.setRestStatus(StringUtil.isNull(riskAndAccident) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增RiskAndAccident
     *
     * @param riskAndAccident 实体列表
     * @return RestResponse<DtoRiskAndAccident>
     */
    @ApiOperation(value = "新增RiskAndAccident", notes = "新增RiskAndAccident")
    @PostMapping
    public RestResponse<DtoRiskAndAccident> create(@Validated @RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.save(riskAndAccident));
        return restResponse;
    }

    /**
     * 新增RiskAndAccident
     *
     * @param riskAndAccident 实体列表
     * @return RestResponse<DtoRiskAndAccident>
     */
    @ApiOperation(value = "修改RiskAndAccident", notes = "修改RiskAndAccident")
    @PutMapping
    public RestResponse<DtoRiskAndAccident> update(@Validated @RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.update(riskAndAccident));
        return restResponse;
    }

    /**
     * "根据id批量删除RiskAndAccident
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除RiskAndAccident", notes = "根据id批量删除RiskAndAccident")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 风险机遇提交方法
     *
     * @param riskAndAccident 需要提交的风险机遇实体
     * @return 封装了风险机遇实体的返回结果集
     */
    @ApiOperation(value = "风险机遇提交", notes = "风险机遇提交")
    @PutMapping("/submit")
    public RestResponse<DtoRiskAndAccident> submit(@RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(riskAndAccident));
        return restResponse;
    }

    /**
     * 风险机遇措施拟定方法
     *
     * @param riskAndAccident 需要拟定措施的风险机遇实体
     * @return 完成拟定操作的实体
     */
    @ApiOperation(value = "风险机遇措施拟定", notes = "风险机遇措施拟定")
    @PutMapping("/measureSubmit")
    public RestResponse<DtoRiskAndAccident> measureSubmit(@RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.measureSubmit(riskAndAccident));
        return restResponse;
    }

    /**
     * 风险机遇措施审核方法
     *
     * @param riskAndAccident 需要进行措施审核的风险机遇实体
     * @return 完成措施审核的风险机遇实体
     */
    @ApiOperation(value = "风险机遇措施审核", notes = "风险机遇措施审核")
    @PutMapping("/measureAudit")
    public RestResponse<DtoRiskAndAccident> measureAudit(@RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.measureAudit(riskAndAccident));
        return restResponse;
    }

    /**
     * 风险机遇措施完成情况验证方法
     *
     * @param riskAndAccident 需要进行验证的风险机遇实体
     * @return 完成确认的等闲机遇实体
     */
    @ApiOperation(value = "风险机遇措施完成情况", notes = "风险机遇措施完成情况")
    @PutMapping("/measureConfirm")
    public RestResponse<DtoRiskAndAccident> measureConfirm(@RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.measureConfirm(riskAndAccident));
        return restResponse;
    }

    /**
     * 批量退回风险机遇完成情况方法
     *
     * @param goBack 需要退回信息的封装对象，包含需要退的的id集合和退回原因
     * @return 完成退回的实体集合
     */
    @ApiOperation(value = "批量退回风险机遇完成情况", notes = "批量退回风险机遇完成情况")
    @PutMapping("/back")
    public RestResponse<List<DtoRiskAndAccident>> measureBack(@RequestBody DtoGoBack goBack) {
        RestResponse<List<DtoRiskAndAccident>> restResponse = new RestResponse<>();
        restResponse.setData(service.measureBack(goBack));
        return restResponse;
    }

    /**
     * 风险机遇完成确认方法
     *
     * @param riskAndAccident 需要进行确认的实体
     * @return 完成确认的实体
     */
    @ApiOperation(value = "风险机遇完成确认", notes = "风险机遇完成确认")
    @PutMapping("/complete")
    public RestResponse<DtoRiskAndAccident> complete(@RequestBody DtoRiskAndAccident riskAndAccident) {
        RestResponse<DtoRiskAndAccident> restResponse = new RestResponse<>();
        restResponse.setData(service.complete(riskAndAccident));
        return restResponse;
    }
}