package com.sinoyd.lims.qa.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * CustomerComplaintRegist查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerComplaintRegistCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 列表标示，默认为1
     * 客户投诉登记为1，客户投诉审核为2，客户投诉确认为3，客户投诉办结为4，客户投诉进度查询为5
     */
    private int pageFrom = 1;
    /**
     * 投诉时间查询开始时间
     */
    private String startTime;
    /**
     * 投诉时间查询结束时间
     */
    private String endTime;
    /**
     * 登记人员
     */
    private String registPerson;
    /**
     * 关键字，可以对投诉方名称和投诉人进行模糊查询
     */
    private String key;
    /**
     * 投诉级别
     */
    private String level;
    /**
     * 状态：-1为所有，1为待处理，2为已处理
     */
    private int status;
    /**
     * 客户投诉状态：
     * 1为投诉登记中
     * 2为投诉审核中
     * 3为投诉确认中
     * 4为投诉办结中
     * 5为投诉已办结
     */
    private int complaintStatus;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        Calendar calendar = new GregorianCalendar();
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");
        //投诉时间开始查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.complaintDate >= :startTime");
            values.put("startTime", date);
        }
        //投诉时间结束查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.complaintDate < :endTime");
            values.put("endTime", date);
        }
        //登记人员查询
        if (StringUtil.isNotEmpty(this.registPerson)) {
            condition.append(" and a.registPersonId = :registPerson");
            values.put("registPerson", this.registPerson);
        }
        //投诉级别查询
        if (StringUtil.isNotEmpty(this.level)) {
            condition.append(" and a.level = :level");
            values.put("level", this.level);
        }
        //关键字查询，可以通过关键字对投诉方名称和投诉人员进行模糊查询
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.complaintName like :key or a.complaintPerson like :key)");
            values.put("key", "%" + this.key + "%");
        }
        //定义状态查询字段容器，用于不同页面状态定制
        List<String> statusList = new ArrayList<>();
        //如果页面为投诉进度查询则根据complaintStatus投诉字段进行查询
        if (this.pageFrom == 5) {
            switch (this.complaintStatus) {
                //登记中
                case 1:
                    statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name());
                    break;
                //审核中
                case 2:
                    statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉审核中.name());
                    break;
                //确认中
                case 3:
                    statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name());
                    break;
                //办结中
                case 4:
                    statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
                    break;
                //已办结
                case 5:
                    statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name());
                    break;
                case 6:
                    statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name());
                    break;
                //默认全部显示
                default:
                    statusList = null;
                    break;
            }
            if (StringUtil.isNotEmpty(statusList)) {
                condition.append(" and status in :statusList");
                values.put("statusList", statusList);
            }
        }
        //如果页面为登记，审核，确认，办结页面，则根据投诉状态和查询状态进行查询
        else {
            switch (this.pageFrom) {
                //客户投诉登记页面,全部查询时显示所有状态不做限制
                case 1:
                    //待处理查询所有未提交数据
                    if (status == 1) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name());
                    }
                    //已处理查询选择提交及以后所有步骤数据
                    else if (status == 2) {
                        for (EnumQA.EnumCustomerComplaintRegistStatus statusItem : EnumQA.EnumCustomerComplaintRegistStatus.values()) {
                            if (!statusItem.name().equals(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name())) {
                                statusList.add(statusItem.name());
                            }
                        }
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    //全部查询选择所有步骤数据
                    else {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name());
                        List<String> statusListB = new ArrayList<>();
                        for (EnumQA.EnumCustomerComplaintRegistStatus statusItem : EnumQA.EnumCustomerComplaintRegistStatus.values()) {
                            if (!statusItem.name().equals(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name())) {
                                statusListB.add(statusItem.name());
                            }
                        }
                        condition.append(" and ((b.nextPerson is null and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    if (StringUtil.isNotEmpty(statusList)) {
                        condition.append(" and status in :statusList");
                        values.put("statusList", statusList);
                    }
                    break;
                //客户投诉审核页面
                case 2:
                    //待处理查询时选择待审核数据
                    if (status == 1) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉审核中.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    //已完成查询所有待确认后数据
                    else if (status == 2) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name());
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name());
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //全部查询时选择所有登记中及以后状态的投诉
                    else {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉审核中.name());
                        List<String> statusListB = new ArrayList<>();
                        for (EnumQA.EnumCustomerComplaintRegistStatus statusItem : EnumQA.EnumCustomerComplaintRegistStatus.values()) {
                            if (!statusItem.name().equals(EnumQA.EnumCustomerComplaintRegistStatus.投诉登记中.name())
                                    &&!statusItem.name().equals(EnumQA.EnumCustomerComplaintRegistStatus.投诉审核中.name())) {
                                statusListB.add(statusItem.name());
                            }
                        }
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    break;
                //客户投诉确认页面
                case 3:
                    //待处理查询显示所有办结退回和待确认数据
                    if (status == 1) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name());
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    //已处理显示所有待办结及以后数据
                    else if (status == 2) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //显示所有待确认及以后数据
                    else {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉确认中.name());
                        statusListA.add(EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
                        statusListB.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    break;
                //客户投诉办结页面
                case 4:
                    //待处理查询显示所有待办结数据
                    if (status == 1) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
                        condition.append(" and a.status in :statusList");
                        values.put("statusList", statusList);
                        condition.append(" and (b.nextPerson = :loginUser)");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                    }
                    //已处理查询所有已办结数据
                    else if (status == 2) {
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name());
                        statusList.add(EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name());
                        condition.append(" and ( b.submitPersonId = :loginUser )");
                        condition.append(" and a.status in :statusList");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusList", statusList);
                    }
                    //查询所有待办结和已办结数据
                    else {
                        List<String> statusListA = new ArrayList<>();
                        statusListA.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉办结中.name());
                        List<String> statusListB = new ArrayList<>();
                        statusListB.add(EnumQA.EnumCustomerComplaintRegistStatus.投诉已办结.name());
                        statusListB.add(EnumQA.EnumCustomerComplaintRegistStatus.办结退回.name());
                        condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                                "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                        values.put("loginUser", currentPrincipalUser.getUserId());
                        values.put("statusListA", statusListA);
                        values.put("statusListB", statusListB);
                    }
                    break;
            }
        }
        return condition.toString();
    }
}