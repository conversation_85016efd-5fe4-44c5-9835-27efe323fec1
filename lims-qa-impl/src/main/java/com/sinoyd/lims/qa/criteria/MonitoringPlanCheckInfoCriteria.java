package com.sinoyd.lims.qa.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * MonitoringPlanCheckInfo查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitoringPlanCheckInfoCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 质量监督明细id
     */
    private String planDetailId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.planDetailId)) {
            condition.append(" and planDetailId = :planDetailId");
            values.put("planDetailId", this.planDetailId);
        }
        return condition.toString();
    }
}