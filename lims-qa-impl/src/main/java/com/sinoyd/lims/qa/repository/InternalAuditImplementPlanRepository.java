package com.sinoyd.lims.qa.repository;

import com.sinoyd.lims.qa.dto.DtoInternalAuditImplementPlan;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * InternalAuditImplementPlan数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface InternalAuditImplementPlanRepository extends IBaseJpaRepository<DtoInternalAuditImplementPlan, String> {

    /**
     * 通过内审计划id查询
     *
     * @param auditPlanId
     * @return 实体列表
     */
    List<DtoInternalAuditImplementPlan> findByAuditPlanId(String auditPlanId);


}