package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.qa.dto.DtoInternalAuditPlanReport;
import com.sinoyd.lims.qa.repository.InternalAuditPlanReportRepository;
import com.sinoyd.lims.qa.service.InternalAuditPlanReportService;
import org.springframework.stereotype.Service;


/**
 * InternalAuditPlanReport操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
public class InternalAuditPlanReportServiceImpl extends BaseJpaServiceImpl<DtoInternalAuditPlanReport, String, InternalAuditPlanReportRepository> implements InternalAuditPlanReportService {

    @Override
    public void findByPage(PageBean<DtoInternalAuditPlanReport> pb, BaseCriteria internalAuditPlanReportCriteria) {
        pb.setEntityName("DtoInternalAuditPlanReport a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, internalAuditPlanReportCriteria);
    }
}