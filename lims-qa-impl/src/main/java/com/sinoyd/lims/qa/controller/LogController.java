package com.sinoyd.lims.qa.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.criteria.LogCriteria;
import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.lims.qa.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Log服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/29
 * @since V100R001
 */
@Api(tags = "示例: Log服务")
@RestController("qaLogController")
@RequestMapping("api/qa/log")
@Validated
public class LogController extends BaseJpaController<DtoLog, String, LogService> {


    /**
     * 分页动态条件查询Log
     *
     * @param logCriteria 条件参数
     * @return RestResponse<List   <   Log>>
     */
    @ApiOperation(value = "分页动态条件查询Log", notes = "分页动态条件查询Log")
    @GetMapping
    public RestResponse<List<DtoLog>> findByPage(LogCriteria logCriteria) {
        PageBean<DtoLog> pageBean = super.getPageBean();
        RestResponse<List<DtoLog>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, logCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据日志类型查询日志
     *
     * @param dtoLog
     * @return RestResponse<List<Log>>
     */
    @ApiOperation(value = "根据日志类型查询日志", notes = "根据日志类型查询日志")
    @GetMapping("/query")
    public RestResponse<List<DtoLog>> findAll(DtoLog dtoLog) {
        RestResponse<List<DtoLog>> restResponse = new RestResponse<>();
        List<DtoLog> logList = service.findAll(dtoLog);
        restResponse.setRestStatus(StringUtil.isEmpty(logList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(logList);
        restResponse.setCount(logList.size());
        return restResponse;
    }

    /**
     * 按主键查询Log
     *
     * @param id 主键id
     * @return RestResponse<DtoLog>
     */
    @ApiOperation(value = "按主键查询Log", notes = "按主键查询Log")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoLog> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoLog> restResponse = new RestResponse<>();
        DtoLog log = service.findOne(id);
        restResponse.setData(log);
        restResponse.setRestStatus(StringUtil.isNull(log) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Log
     *
     * @param log 实体列表
     * @return RestResponse<DtoLog>
     */
    @ApiOperation(value = "新增Log", notes = "新增Log")
    @PostMapping
    public RestResponse<DtoLog> create(@Validated @RequestBody DtoLog log) {
        RestResponse<DtoLog> restResponse = new RestResponse<>();
        restResponse.setData(service.save(log));
        return restResponse;
    }

    /**
     * 新增Log
     *
     * @param log 实体列表
     * @return RestResponse<DtoLog>
     */
    @ApiOperation(value = "修改Log", notes = "修改Log")
    @PutMapping
    public RestResponse<DtoLog> update(@Validated @RequestBody DtoLog log) {
        RestResponse<DtoLog> restResponse = new RestResponse<>();
        restResponse.setData(service.update(log));
        return restResponse;
    }

    /**
     * "根据id批量删除Log
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Log", notes = "根据id批量删除Log")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}