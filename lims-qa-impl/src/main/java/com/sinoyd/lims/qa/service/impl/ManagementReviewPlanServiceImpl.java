package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.DtoAnnualPlan;
import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.lims.qa.dto.DtoManagementReviewPlan;
import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.AnnualPlanRepository;
import com.sinoyd.lims.qa.repository.ManagementReviewPlanRepository;
import com.sinoyd.lims.qa.repository.ReviewPlanReportRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.ManagementReviewPlanService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ManagementReviewPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
public class ManagementReviewPlanServiceImpl extends BaseJpaServiceImpl<DtoManagementReviewPlan, String, ManagementReviewPlanRepository> implements ManagementReviewPlanService {

    private SubmitRecordRepository submitRecordRepository;

    private ReviewPlanReportRepository reviewPlanReportRepository;

    private LogService logService;

    private AnnualPlanRepository annualPlanRepository;

    private PersonRepository personRepository;

    private SubmitRecordService submitRecordService;


    @Override
    public void findByPage(PageBean<DtoManagementReviewPlan> pb, BaseCriteria managementReviewPlanCriteria) {
        pb.setEntityName("DtoManagementReviewPlan a,DtoSubmitRecordQa b ");
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoManagementReviewPlan(a.id, a.reviewTime, a.reviewPurp, a.reviewContent, a.reviewAddr," +
                " a.reviewPrepareRequired, a.host,a.attendee, a.recorder, a.createDate, a.status, a.annualPlanId, a.creator) ");
        comRepository.findByPage(pb, managementReviewPlanCriteria);
        List<DtoManagementReviewPlan> dataList = pb.getData();
        if (StringUtil.isNotEmpty(dataList)) {
            //所有人员集合
            List<DtoPerson> dtoPersonList = personRepository.findAll();
            List<String> ids = dataList.parallelStream().map(DtoManagementReviewPlan::getId).collect(Collectors.toList());
            List<DtoSubmitRecordQa> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumLogObjectType.管理评审.getValue(), ids);
            for (DtoManagementReviewPlan dto : dataList) {
                //填充创建人姓名（实体保存时自动填充creator为创建人id，需要根据id获取创建人姓名）
                Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dto.getCreator())).findFirst();
                personOptional.ifPresent(dtoPerson -> dto.setCreatorName(dtoPerson.getCName()));
                //获取单个的参加人员id字符串
                String attendees = dto.getAttendee();
                if (StringUtil.isNotEmpty(attendees)) {
                    //分割逗号并且将数组变成list集合
                    List<String> list = Arrays.asList(attendees.split(","));
                    //放名字的集合
                    List<String> name = new ArrayList<>();
                    list.forEach(s -> dtoPersonList.forEach(dtoPerson -> {
                        if (dtoPerson.getId().equals(s)) {
                            name.add(dtoPerson.getCName());
                        }
                    }));
                    //将查到的名字转成字符串
                    dto.setAttendeeName(String.join(",", name));
                }
                //主持人名
                Optional<DtoPerson> hostOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dto.getHost()))
                        .findFirst();
                hostOptional.ifPresent(person -> dto.setHostName(person.getCName()));
                //记录人名
                Optional<DtoPerson> recordOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dto.getRecorder()))
                        .findFirst();
                recordOptional.ifPresent(record -> dto.setRecorderName(record.getCName()));
                //获取人员的Id字符串,转成集合类型
                dto.setAttendeeId(Arrays.asList(dto.getAttendee().split(",")));
                if (StringUtil.isNotEmpty(submitRecordList)) {
                    //获取DtoSubmitRecord表getObjectId和DtoAnnualPlan表的ID比较得到关联的集合
                    List<DtoSubmitRecordQa> thisDtoSubmitRecordQa = submitRecordList.parallelStream()
                            .filter(p -> p.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(thisDtoSubmitRecordQa)) {
                        //将得到的关联集合进行倒序排序，得到第一条数据（取最新的一条数据）
                        thisDtoSubmitRecordQa.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        dto.setSubmitRemark(thisDtoSubmitRecordQa.get(0).getSubmitRemark());
                    }
                }
            }
        }
    }

    @Override
    public DtoManagementReviewPlan findOne(String key) {
        DtoManagementReviewPlan managementReviewPlan = super.findOne(key);
        DtoAnnualPlan annualPlan = annualPlanRepository.findOne(managementReviewPlan.getAnnualPlanId());
        if (StringUtil.isNotNull(annualPlan)) {
            managementReviewPlan.setAnnualPlanName(annualPlan.getAuditPurpose());
        }
        return managementReviewPlan;
    }

    @Transactional
    @Override
    public DtoManagementReviewPlan save(DtoManagementReviewPlan entity) {
        entity.setAttendee(String.join(",", entity.getAttendeeId()));
        entity.setStatus(EnumQA.EnumManagementReviewPlanStatus.计划编制中.name());
        entity = super.save(entity);
        //接收前端传的年度计划id
        DtoAnnualPlan dtoAnnualPlan = annualPlanRepository.findOne(entity.getAnnualPlanId());
        if (StringUtil.isNotNull(dtoAnnualPlan)) {
            entity.setAnnualPlanName(dtoAnnualPlan.getAuditPurpose());
        }
        //获取当前登录人信息
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.管理评审.getValue(), EnumQA.EnumQASubmitType.管理评审计划登记.getValue(),
                entity.getCreator(), null, EnumQA.EnumManagementReviewPlanStatus.计划编制中.name(), EnumQA.EnumManagementReviewPlanStatus.计划编制中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增管理评审计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.管理评审流程.name(), entity.getId(),
                EnumQA.EnumLogObjectType.管理评审.name(), currentUser.getUserName() + "新增了管理评审计划", "", "");
        return entity;
    }

    @Transactional
    @Override
    public DtoManagementReviewPlan submit(DtoManagementReviewPlan dtoManagementReviewPlan) {
        DtoManagementReviewPlan exist = super.findOne(dtoManagementReviewPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(exist.getCreator())) {
            throw new BaseException("只能提交自己创建的管理评审计划");
        }
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dtoManagementReviewPlan.getNextOperator())).findFirst();
        personOptional.ifPresent(dto -> exist.setNextOperatorName(dto.getCName()));
        exist.setStatus(EnumQA.EnumManagementReviewPlanStatus.计划审核中.name());
        submitRecordService.createSubmitRecord(dtoManagementReviewPlan.getId(), EnumQA.EnumQAObjType.管理评审.getValue(), EnumQA.EnumQASubmitType.管理评审计划提交.getValue(),
                dtoManagementReviewPlan.getNextOperator(), null, exist.getStatus(), EnumQA.EnumManagementReviewPlanStatus.计划审核中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交管理评审计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.管理评审流程.name(), dtoManagementReviewPlan.getId(),
                EnumQA.EnumLogObjectType.管理评审.name(), currentUser.getUserName() + "提交了管理评审计划。下一步操作人：" + exist.getNextOperatorName(), "", "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoManagementReviewPlan audit(DtoManagementReviewPlan dtoManagementReviewPlan) {
        DtoManagementReviewPlan exist = super.findOne(dtoManagementReviewPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //接收前端的审核状态做判断接收
        String auditResult;
        if (EnumQA.EnumManagementReviewPlanStatus.审核通过.name().equals(dtoManagementReviewPlan.getStatus())) {
            auditResult = EnumQA.EnumManagementReviewPlanStatus.报告编制中.name();
        } else {
            auditResult = EnumQA.EnumManagementReviewPlanStatus.审核不通过.name();
        }
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.管理评审.getValue(),
                EnumQA.EnumQASubmitType.管理评审计划审核.getValue(), exist.getCreator(), dtoManagementReviewPlan.getSubmitRemark(),
                EnumQA.EnumManagementReviewPlanStatus.计划审核中.name(), auditResult);
        String opinion = "";
        if (StringUtil.isNotEmpty(dtoManagementReviewPlan.getSubmitRemark())) {
            opinion = "意见：" + dtoManagementReviewPlan.getSubmitRemark();
        }
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核管理评审计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.管理评审流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.管理评审.name(), currentUser.getUserName() + "审核了管理评审计划，审核结果为：" + dtoManagementReviewPlan.getStatus() + "。" + opinion,
                dtoManagementReviewPlan.getSubmitRemark(), "");
        exist.setStatus(auditResult);
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoManagementReviewPlan update(DtoManagementReviewPlan entity) {
        DtoManagementReviewPlan exist = repository.findOne(entity.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //验证创建人id和当前登录用户id是否相同
        if (!currentUser.getUserId().equals(exist.getCreator())) {
            throw new BaseException("只能修改自己创建的内审计划");
        }
        entity.setAttendee(String.join(",", entity.getAttendeeId()));
        super.save(entity);
        //如果前端传了编制报告对象 ，就开始往另外的关联表新增
        if (StringUtil.isNotNull(entity.getDtoReviewPlanReport())) {
            entity.getDtoReviewPlanReport().setReviewPlanId(entity.getId());
            reviewPlanReportRepository.save(entity.getDtoReviewPlanReport());
        }
        DtoAnnualPlan dtoAnnualPlan = annualPlanRepository.findOne(entity.getAnnualPlanId());
        if (StringUtil.isNotNull(dtoAnnualPlan)) {
            entity.setAnnualPlanName(dtoAnnualPlan.getAuditPurpose());
        }
        return entity;
    }

    @Transactional
    @Override
    public DtoManagementReviewPlan submitReport(DtoManagementReviewPlan dtoManagementReviewPlan) {
        //将状态改成计划审核中
        DtoManagementReviewPlan exist = super.findOne(dtoManagementReviewPlan.getId());
        //获取当前人的信息
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //将状态改成报告审核中
        exist.setStatus(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dtoManagementReviewPlan.getNextOperator())).findFirst();
        personOptional.ifPresent(dto -> exist.setNextOperatorName(dto.getCName()));
        submitRecordService.createSubmitRecord(dtoManagementReviewPlan.getId(), EnumQA.EnumQAObjType.管理评审.getValue(), EnumQA.EnumQASubmitType.管理评审报告编制提交.getValue(),
                dtoManagementReviewPlan.getNextOperator(), null, EnumQA.EnumManagementReviewPlanStatus.报告编制中.name(), EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交管理评审报告.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.管理评审流程.name(), dtoManagementReviewPlan.getId(),
                EnumQA.EnumLogObjectType.管理评审.name(), currentUser.getUserName() + "提交了管理评审报告。下一步操作人：" + exist.getNextOperatorName(),
                "", "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public DtoManagementReviewPlan auditReport(DtoManagementReviewPlan dtoManagementReviewPlan) {
        DtoManagementReviewPlan exist = super.findOne(dtoManagementReviewPlan.getId());
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //接收前端的审核状态做判断接收
        String auditResult;
        if (EnumQA.EnumManagementReviewPlanStatus.审核通过.name().equals(dtoManagementReviewPlan.getStatus())) {
            auditResult = EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name();
        } else {
            auditResult = EnumQA.EnumManagementReviewPlanStatus.报告审核不通过.name();
        }
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.管理评审.getValue(),
                EnumQA.EnumQASubmitType.管理评审报告审核.getValue(), exist.getCreator(), dtoManagementReviewPlan.getSubmitRemark(),
                EnumQA.EnumManagementReviewPlanStatus.报告审核中.name(), auditResult);
        String opinion = "";
        if (StringUtil.isNotEmpty(dtoManagementReviewPlan.getSubmitRemark())) {
            opinion = "意见：" + dtoManagementReviewPlan.getSubmitRemark();
        }
        //插入日志
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.审核管理评审报告.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.管理评审流程.name(), exist.getId(),
                EnumQA.EnumLogObjectType.管理评审.name(), currentUser.getUserName() + "审核了管理评审报告，审核结果为：" + dtoManagementReviewPlan.getStatus() + "。" + opinion,
                dtoManagementReviewPlan.getSubmitRemark(), "");
        exist.setStatus(auditResult);
        return super.update(exist);
    }

    @Override
    public DtoManagementReviewPlan findAttachPath(String id) {
        return findOne(id);
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        if (StringUtil.isNotNull(idList)) {
            List<DtoLog> logList = new ArrayList<>();
            List<DtoManagementReviewPlan> dtoManagementReviewPlans = repository.findAll(idList);
            //遍历要删除的用户
            for (DtoManagementReviewPlan dto : dtoManagementReviewPlans) {
                if (!currentUser.getUserId().equals(dto.getCreator())) {
                    throw new BaseException("只能删除自己创建的管理计划");
                }
                if (!dto.getStatus().equals(EnumQA.EnumManagementReviewPlanStatus.计划编制中.name()) && !dto.getStatus().equals(EnumQA.EnumManagementReviewPlanStatus.审核不通过.name())) {
                    throw new BaseException("不能删除已提交的管理计划");
                }
                logList.add(logService.getDtoLog(dto.getId(), EnumQA.EnumLogOperateType.删除管理评审计划.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.管理评审流程.name(),
                        EnumQA.EnumLogObjectType.管理评审.name(), currentUser.getUserName() + "删除了管理评审计划"));
            }
            logService.save(logList);
            return super.logicDeleteById(ids);
        }
        return 0;
    }

    @Autowired
    public void setSubmitRecordRepository(SubmitRecordRepository submitRecordRepository) {
        this.submitRecordRepository = submitRecordRepository;
    }

    @Autowired
    public void setReviewPlanReportRepository(ReviewPlanReportRepository reviewPlanReportRepository) {
        this.reviewPlanReportRepository = reviewPlanReportRepository;
    }

    @Autowired
    public void setLogService(LogService logService) {
        this.logService = logService;
    }

    @Autowired
    public void setAnnualPlanRepository(AnnualPlanRepository annualPlanRepository) {
        this.annualPlanRepository = annualPlanRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setSubmitRecordService(SubmitRecordService submitRecordService) {
        this.submitRecordService = submitRecordService;
    }
}