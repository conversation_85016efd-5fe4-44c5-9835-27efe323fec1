package com.sinoyd.lims.qa.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;


/**
 * ManagementReviewPlan查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ManagementReviewPlanCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评审时间
     */
    private String fromDate;

    /**
     * 评审时间
     */
    private String toDate;

    /**
     * 评审目的和内容关键字
     */
    private String key;

    /**
     * 状态， 2已处理、1待处理、-1所有
     */
    private Integer status;

    /**
     * 页面来源:  1代表管理评审计划编制页面, 2代表管理评审计划审核页面
     * 3代表管理评审报告编制, 4代表管理评审报告审核页面
     */
    private int pageFrom;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");

        List<String> statusList = new ArrayList();
        Calendar calendar = new GregorianCalendar();
        if (StringUtil.isNotEmpty(this.fromDate)) {
            Date date = DateUtil.stringToDate(this.fromDate, DateUtil.YEAR);
            condition.append(" and a.reviewTime >= :from");
            values.put("from", date);
        }
        if (StringUtil.isNotEmpty(this.toDate)) {
            Date date = DateUtil.stringToDate(this.toDate, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1); //把日期往后增加一天,整数  往后推,负数往前移动
            date = calendar.getTime();
            condition.append(" and a.reviewTime < :to");
            values.put("to", date);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (a.reviewPurp like :key or a.reviewContent like :key )");
            values.put("key", "%" + this.key + "%");
        }
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        if (1 == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.计划审核中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告编制中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.计划编制中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.审核不通过.name());
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.计划审核中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告编制中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.计划编制中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.审核不通过.name());
            }
            condition.append(" and a.status in :statusList");
            values.put("statusList", statusList);
        } else if (2 == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告编制中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.审核不通过.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                condition.append(" and ( b.submitPersonId = :loginUser )");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if  (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.计划审核中.name());
                condition.append(" and b.nextPerson = :loginUser ");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                        "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                List<String> statusListA = new ArrayList();
                statusListA.add(EnumQA.EnumManagementReviewPlanStatus.计划审核中.name());
                List<String> statusListB = new ArrayList();
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告编制中.name());
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.审核不通过.name());
                values.put("loginUser", user.getUserId());
                values.put("statusListA", statusListA);
                values.put("statusListB", statusListB);
            }

        } else if (3 == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                condition.append(" and ( b.submitPersonId = :loginUser )");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核不通过.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告编制中.name());
                condition.append(" and b.nextPerson = :loginUser ");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                        "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                List<String> statusListA = new ArrayList();
                statusListA.add(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
                List<String> statusListB = new ArrayList();
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告编制中.name());
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告审核不通过.name());
                values.put("loginUser", user.getUserId());
                values.put("statusListA", statusListA);
                values.put("statusListB", statusListB);
            }
        } else if (4 == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核不通过.name());
                condition.append(" and ( b.submitPersonId = :loginUser )");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
                condition.append(" and b.nextPerson = :loginUser ");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                        "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                List<String> statusListA = new ArrayList();
                statusListA.add(EnumQA.EnumManagementReviewPlanStatus.报告审核中.name());
                List<String> statusListB = new ArrayList();
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告审核不通过.name());
                statusListB.add(EnumQA.EnumManagementReviewPlanStatus.报告审核通过.name());
                values.put("loginUser", user.getUserId());
                values.put("statusListA", statusListA);
                values.put("statusListB", statusListB);
            }
        }
        return condition.toString();
    }
}
