package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlanCheckInfo;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlanDetail;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.MonitoringPlanCheckInfoRepository;
import com.sinoyd.lims.qa.repository.MonitoringPlanDetailRepository;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.MonitoringPlanCheckInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * MonitoringPlanCheckInfo操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
public class MonitoringPlanCheckInfoServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoMonitoringPlanCheckInfo, String, MonitoringPlanCheckInfoRepository> implements MonitoringPlanCheckInfoService {

    private DepartmentService departmentService;

    private LogService logService;

    private MonitoringPlanDetailRepository monitoringPlanDetailRepository;

    @Override
    public void findByPage(PageBean<DtoMonitoringPlanCheckInfo> pb, BaseCriteria monitoringPlanCheckInfoCriteria) {
        pb.setEntityName("DtoMonitoringPlanCheckInfo a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, monitoringPlanCheckInfoCriteria);
        List<DtoMonitoringPlanCheckInfo> checkInfoList = pb.getData();
        List<DtoDepartment> dtoDepartments = departmentService.findAll();
        for (DtoMonitoringPlanCheckInfo dtoMonitoringPlanCheckInfo : checkInfoList) {
            if (StringUtil.isNotEmpty(dtoDepartments)) {
                Optional<DtoDepartment> departmentOptional = dtoDepartments.parallelStream().filter(d -> d.getId().equals(dtoMonitoringPlanCheckInfo.getDutyDept())).findFirst();
                departmentOptional.ifPresent(dtoDepartment -> dtoMonitoringPlanCheckInfo.setDeptName(dtoDepartment.getDeptName()));
            }
        }
    }

    @Override
    public DtoMonitoringPlanCheckInfo findOne(String key) {
        DtoMonitoringPlanCheckInfo exit = super.findOne(key);
        if (StringUtil.isNotEmpty(exit.getDutyDept())) {
            DtoDepartment department = departmentService.findOne(exit.getDutyDept());
            if (StringUtil.isNotNull(department)) {
                exit.setDeptName(department.getDeptName());
            }
        }
        return exit;
    }

    @Override
    public DtoMonitoringPlanCheckInfo save(DtoMonitoringPlanCheckInfo entity) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoMonitoringPlanDetail detail = monitoringPlanDetailRepository.findOne(entity.getPlanDetailId());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增检查项.name(),
                null, null, EnumQA.EnumLogType.质量监督流程.name(), detail.getPlanId(), EnumQA.EnumQAObjType.管理评审.name(),
                currentUser.getUserName() + "新增检查项信息，检查结果：" + entity.getCheckResult(), "", "");
        return super.save(entity);
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setLogService(LogService logService) {
        this.logService = logService;
    }

    @Autowired
    public void setMonitoringPlanDetailRepository(MonitoringPlanDetailRepository monitoringPlanDetailRepository) {
        this.monitoringPlanDetailRepository = monitoringPlanDetailRepository;
    }
}
