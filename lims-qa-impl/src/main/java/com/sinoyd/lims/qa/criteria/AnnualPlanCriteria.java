package com.sinoyd.lims.qa.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * AnnualPlan查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnnualPlanCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 年度
     */
    private Integer year;

    /**
     * 指定人id
     */
    private String makePersonId;

    /**
     * 状态， 2已处理、1待处理、-1所有
     */
    private Integer status;

    /**
     * 审核状态
     */
    private String checkStatus;

    /**
     * 页面来源: 1表示年度计划编制表
     * 2表示年度计划审核表
     */
    private int pageFrom;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");
        List<String> statusList = new ArrayList();
        if (StringUtil.isNotNull(year)) {
            condition.append(" and a.year = :year");
            values.put("year", this.year);
        }
        if (StringUtil.isNotEmpty(makePersonId)) {
            condition.append(" and a.makePersonId = :makePersonId");
            values.put("makePersonId", this.makePersonId);
        }
        if (StringUtil.isNotEmpty(checkStatus)) {
            condition.append(" and a.status = :checkStatus");
            values.put("checkStatus", this.checkStatus);
        }
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        if (1 == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核中.name());
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核通过.name());
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumAnnualPlanStatus.编制中.name());
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核不通过.name());
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核中.name());
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核通过.name());
                statusList.add(EnumQA.EnumAnnualPlanStatus.编制中.name());
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核不通过.name());
            }
            condition.append(" and a.status in :statusList");
            values.put("statusList", statusList);
        } else if (2 == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核通过.name());
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核不通过.name());
                condition.append(" and ( b.submitPersonId = :loginUser )");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumAnnualPlanStatus.审核中.name());
                condition.append(" and b.nextPerson = :loginUser ");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                        "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                List<String> statusListA = new ArrayList();
                statusListA.add(EnumQA.EnumAnnualPlanStatus.审核中.name());
                List<String> statusListB = new ArrayList();
                statusListB.add(EnumQA.EnumAnnualPlanStatus.审核通过.name());
                statusListB.add(EnumQA.EnumAnnualPlanStatus.审核不通过.name());
                values.put("loginUser", user.getUserId());
                values.put("statusListA", statusListA);
                values.put("statusListB", statusListB);
            }
            values.put("loginUser", user.getUserId());
            if (StringUtil.isNotEmpty(statusList)){
                values.put("statusList", statusList);
            }
        }

        return condition.toString();
    }
}