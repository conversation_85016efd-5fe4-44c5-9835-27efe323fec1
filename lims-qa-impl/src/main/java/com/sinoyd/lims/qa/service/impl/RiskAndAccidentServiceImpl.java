package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoLog;
import com.sinoyd.lims.qa.dto.DtoRiskAndAccident;
import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.RiskAndAccidentRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.RiskAndAccidentService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * RiskAndAccident操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/15
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class RiskAndAccidentServiceImpl extends BaseJpaServiceImpl<DtoRiskAndAccident, String, RiskAndAccidentRepository> implements RiskAndAccidentService {

    private final LogService logService;

    private final SubmitRecordRepository submitRecordRepository;

    private final SubmitRecordService submitRecordService;

    private final PersonRepository personRepository;

    private final CodeService codeService;

    private final DepartmentService departmentService;

    @Override
    public void findByPage(PageBean<DtoRiskAndAccident> pb, BaseCriteria riskAndAccidentCriteria) {
        pb.setEntityName("DtoRiskAndAccident a,DtoSubmitRecordQa b");
        //查询结果封装对象
        pb.setSelect("select new com.sinoyd.lims.qa.dto.DtoRiskAndAccident(" +
                "a.dutyDomainId,a.dutyPersonId,a.finderId,a.discoverDate," +
                "a.description,a.sourceType,a.creator,a.createDate,a.status," +
                "b.nextPerson,a.id,a.directorId)");
        comRepository.findByPage(pb, riskAndAccidentCriteria);
        List<DtoRiskAndAccident> riskAndAccidents = pb.getData();
        if (StringUtil.isNotEmpty(riskAndAccidents)) {
            List<String> ids = riskAndAccidents.stream().map(DtoRiskAndAccident::getId).collect(Collectors.toList());
            List<DtoPerson> people = personRepository.findAll();
            List<DtoSubmitRecordQa> submitRecords = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumQAObjType.风险机遇管理.getValue(), ids);
            List<String> sourceIds = riskAndAccidents.stream().map(DtoRiskAndAccident::getSourceType).collect(Collectors.toList());
            List<DtoCode> sourceCodes = codeService.findByCodes(sourceIds);
            List<DtoDepartment> dtoDepartments = departmentService.findAll();
            for (DtoRiskAndAccident item : riskAndAccidents) {
                List<DtoSubmitRecordQa> submitRecordOfItem = submitRecords.stream()
                        .filter(s -> s.getObjectId().equals(item.getId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(submitRecordOfItem)) {
                    submitRecordOfItem.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                    //填充意见
                    item.setSubmitRemark(submitRecordOfItem.get(0).getSubmitRemark());
                }
                if (StringUtil.isNotEmpty(people)) {
                    people.forEach(p -> {
                        // 根据id填充责任人姓名
                        if (p.getId().equals(item.getDutyPersonId())) {
                            item.setDutyPersonName(p.getCName());
                        }
                        // 根据id填充发现人姓名
                        if (p.getId().equals(item.getFinderId())) {
                            item.setFinderName(p.getCName());
                        }
                        // 根据id填充责任科室主任姓名
                        if (p.getId().equals(item.getDirectorId())) {
                            item.setDirectorName(p.getCName());
                        }
                    });
                }
                if (StringUtil.isNotEmpty(sourceCodes)) {
                    sourceCodes.forEach(s -> {
                        if (s.getDictCode().equals(item.getSourceType())) {
                            item.setSourceTypeName(s.getDictName());
                        }
                    });
                }
                if (StringUtil.isNotEmpty(dtoDepartments)) {
                    dtoDepartments.forEach(d -> {
                        if (d.getId().equals(item.getDutyDomainId())) {
                            item.setDutyDomainName(d.getDeptName());
                        }
                    });
                }
            }
        }
    }

    @Override
    public DtoRiskAndAccident findOne(String key) {
        DtoRiskAndAccident result = repository.findOne(key);
        List<DtoPerson> people = personRepository.findAll();
        DtoCode code = codeService.findByCode(result.getSourceType());
        if (StringUtils.isNotNull(code)) {
            result.setSourceTypeName(code.getDictName());
        }
        people.forEach(p -> {
            if (p.getId().equals(result.getDirectorId())) {
                result.setDirectorName(p.getCName());
            }
            if (p.getId().equals(result.getDutyPersonId())) {
                result.setDutyPersonName(p.getCName());
            }
            if (p.getId().equals(result.getFinderId())) {
                result.setFinderName(p.getCName());
            }
        });
        List<DtoDepartment> dtoDepartments = departmentService.findAll();
        dtoDepartments.forEach(d -> {
            if (d.getId().equals(result.getDutyDomainId())) {
                result.setDutyDomainName(d.getDeptName());
            }
        });
        return result;
    }

    /**
     * 风险机遇保存方法：
     * 1. 设置初始状态为登记中
     * 2. 保存日志
     *
     * @param entity 风险机遇对象实体
     * @return 完成保存的实体
     */
    @Transactional
    @Override
    public DtoRiskAndAccident save(DtoRiskAndAccident entity) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        if (entity.getDiscoverDate() == null) {
            entity.setDiscoverDate(new Date());
        }
        if (entity.getPossibility() == null) {
            entity.setPossibility(1);
        }
        if (entity.getSeriousness() == null) {
            entity.setSeriousness(1);
        }
        entity.setStatus(EnumQA.EnumRiskAndAccidentStatus.登记中.name());
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(),
                new Date(), EnumQA.EnumLogOperateType.新增风险机遇.name(), UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.风险机遇流程.name(),
                entity.getId(), EnumQA.EnumLogObjectType.风险机遇.name(), currentPrincipalUser.getUserName() + "新增了风险机遇信息" + "", "", "");
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.风险机遇管理.getValue(), EnumQA.EnumQASubmitType.风险机遇登记.getValue(),
                null, null, EnumQA.EnumRiskAndAccidentStatus.登记中.name(), EnumQA.EnumRiskAndAccidentStatus.登记中.name());
        return super.save(entity);
    }

    /**
     * 风险机遇批量逻辑删除：
     * 1.删除并记录日志
     *
     * @param ids 要删除的风险机遇对象id集合
     * @return 完成删除的风险机遇对象集合
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<DtoLog> logs = new ArrayList<>();
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        for (String id : (List<String>) ids) {
            logs.add(logService.getDtoLog(id, EnumQA.EnumLogOperateType.删除风险机遇.name(), UUIDHelper.GUID_EMPTY, "",
                    EnumQA.EnumLogType.风险机遇流程.name(), EnumQA.EnumLogObjectType.风险机遇.name(), currentPrincipalUser.getUserName() + "删除了风险机遇信息"));
        }
        logService.save(logs);
        return super.logicDeleteById(ids);
    }

    /**
     * 风险机遇提交：
     * 1.提交时选择下一步操作人
     * 2.风险机遇对象状态更新为提交
     * 3.创建记录对象记录
     * 4.日志记录
     *
     * @param riskAndAccident 要提交的风险机遇实体对象
     * @return DtoRiskAndAccident完成提交操作的实体
     */
    @Transactional
    @Override
    public DtoRiskAndAccident submit(DtoRiskAndAccident riskAndAccident) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中完整风险机遇对象
        DtoRiskAndAccident exist = super.findOne(riskAndAccident.getId());
        //更新风险机遇对象状态
        exist.setStatus(EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name());
        DtoPerson person = personRepository.findOne(riskAndAccident.getNextOperator());
        String nextOperatorName = "";
        if (StringUtils.isNotNull(person)) {
            nextOperatorName = person.getCName();
        }
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交风险机遇.name(),
                UUIDHelper.GUID_EMPTY, riskAndAccident.getNextOperator(), EnumQA.EnumLogType.风险机遇流程.name(), riskAndAccident.getId(), EnumQA.EnumLogObjectType.风险机遇.name(),
                currentPrincipalUser.getUserName() + "提交了风险机遇信息，下一步操作人：" + nextOperatorName, "", "");
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.风险机遇管理.getValue(), EnumQA.EnumQASubmitType.风险机遇提交.getValue(),
                riskAndAccident.getNextOperator(), riskAndAccident.getSubmitRemark(), EnumQA.EnumRiskAndAccidentStatus.登记中.name(),
                EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name());
        return super.update(exist);
    }

    /**
     * 风险记录信息审核及拟定措施提交
     *
     * @param riskAndAccident 需要拟定措施的风险机遇实体对象
     * @return DtoRiskAndAccident 完成措施提交的实体
     */
    @Transactional
    @Override
    public DtoRiskAndAccident measureSubmit(DtoRiskAndAccident riskAndAccident) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中完整风险机遇对象
        DtoRiskAndAccident exist = super.findOne(riskAndAccident.getId());
        String stateTo;
        String status;
        String nextOperator = "";
        //如果有下一步操作人则判定为通过，否则没有
        if (StringUtil.isNotEmpty(riskAndAccident.getNextOperator())) {
            //更新状态
            status = EnumQA.EnumRiskAndAccidentStatus.措施批准中.name();
            stateTo = EnumQA.EnumRiskAndAccidentStatus.措施批准中.name();
            DtoPerson person = personRepository.findOne(riskAndAccident.getNextOperator());
            if (StringUtils.isNotNull(person)) {
                nextOperator = "拟定了风险机遇措施，下一步操作人：" + person.getCName() + "。";
            }
        } else {
            //更新状态
            status = EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name();
            stateTo = EnumQA.EnumRiskAndAccidentStatus.拟定不通过.name();
            riskAndAccident.setNextOperator(exist.getCreator());
            DtoPerson person = personRepository.findOne(exist.getCreator());
            if (StringUtils.isNotNull(person)) {
                nextOperator = "拟定了风险机遇措施，拟定不通过，下一步操作人：" + person.getCName() + "。";
            }
        }
        if (StringUtil.isNotEmpty(riskAndAccident.getSubmitRemark())) {
            nextOperator += "意见：" + riskAndAccident.getSubmitRemark();
        }
        exist.setStatus(status);
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.风险机遇措施拟定.name(),
                UUIDHelper.GUID_EMPTY, riskAndAccident.getNextOperator(), EnumQA.EnumLogType.风险机遇流程.name(), riskAndAccident.getId(), EnumQA.EnumLogObjectType.风险机遇.name(),
                currentPrincipalUser.getUserName() + nextOperator, "", "");
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.风险机遇管理.getValue(), EnumQA.EnumQASubmitType.风险机遇拟定措施提交.getValue(),
                riskAndAccident.getNextOperator(), riskAndAccident.getSubmitRemark(), EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name(),
                stateTo);
        return super.update(exist);
    }

    /**
     * 风险机遇措施审批：
     * 1.通过下一步操作人判断是否通过审批
     * 2.填充意见，如果未通过则为必填
     *
     * @param riskAndAccident 需要风险机遇措施审批的实体对象
     * @return 完成措施审批的实体
     */
    @Transactional
    @Override
    public DtoRiskAndAccident measureAudit(DtoRiskAndAccident riskAndAccident) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中完整风险机遇对象
        DtoRiskAndAccident exist = super.findOne(riskAndAccident.getId());
        String status;
        String stateTo;
        String nextOperate = "";
        String nextOperatorId = "";
        if (Optional.ofNullable(riskAndAccident.getIsPassCompleteFlag()).isPresent()) {
            //通过时下一步操作人为创建时指定的责任人
            if (riskAndAccident.getIsPassCompleteFlag()) {
                //更新状态
                status = EnumQA.EnumRiskAndAccidentStatus.措施实施中.name();
                stateTo = EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name();
                nextOperatorId = exist.getDutyPersonId();
                DtoPerson person = personRepository.findOne(nextOperatorId);
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "审批了风险机遇措施，下一步操作人：" + person.getCName() + "。";
                }
            } else {
                //更新状态
                status = EnumQA.EnumRiskAndAccidentStatus.措施批准不通过.name();
                stateTo = EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name();
                //不通过时下一步操作人应当为提交时指定的下一步操作人
                DtoSubmitRecordQa submitRecord = submitRecordService.findSubmitRecordByObjIdAndSubmitType(
                        exist.getId(), EnumQA.EnumQASubmitType.风险机遇提交.getValue());
                if (StringUtils.isNotNull(submitRecord)) {
                    nextOperatorId = submitRecord.getNextPerson();
                    DtoPerson person = personRepository.findOne(nextOperatorId);
                    nextOperate = "审核了风险机遇措施，审核不通过，下一步操作人：" + person.getCName() + "。";
                }
            }
            if (StringUtil.isNotEmpty(riskAndAccident.getSubmitRemark())) {
                nextOperate += "意见：" + riskAndAccident.getSubmitRemark();
            }
        } else {
            throw new BaseException("必须传回是否通过标识");
        }
        exist.setStatus(status);
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.风险机遇措施措施方案审批.name(),
                UUIDHelper.GUID_EMPTY, nextOperatorId, EnumQA.EnumLogType.风险机遇流程.name(), riskAndAccident.getId(), EnumQA.EnumLogObjectType.风险机遇.name(),
                currentPrincipalUser.getUserName() + nextOperate, "", "");
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.风险机遇管理.getValue(), EnumQA.EnumQASubmitType.风险机遇拟定措施审核.getValue(),
                nextOperatorId, riskAndAccident.getSubmitRemark(), EnumQA.EnumRiskAndAccidentStatus.措施拟定中.name(),
                stateTo);
        return super.update(exist);
    }

    /**
     * 风险机遇完成情况确认：
     * 1.填充完成日期 achieveDate，完成情况字段performance
     * 2.只能通过，更新状态为下一步状态
     *
     * @param riskAndAccident 需要风险机遇措施完成情况确认的实体对象
     * @return 确认完成的实体
     */
    @Transactional
    @Override
    public DtoRiskAndAccident measureConfirm(DtoRiskAndAccident riskAndAccident) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中完整风险机遇对象
        DtoRiskAndAccident exist = super.findOne(riskAndAccident.getId());
        //更新风险机遇对象状态
        exist.setStatus(EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
        String nextOperatorName = "";
        DtoPerson person = personRepository.findOne(exist.getFinderId());
        if (StringUtils.isNotNull(person)) {
            nextOperatorName = person.getCName() + "。";
        }
        if (StringUtil.isNotEmpty(riskAndAccident.getSubmitRemark())) {
            nextOperatorName += "意见：" + riskAndAccident.getSubmitRemark();
        }
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.风险机遇完成情况.name(),
                UUIDHelper.GUID_EMPTY, exist.getFinderId(), EnumQA.EnumLogType.风险机遇流程.name(), riskAndAccident.getId(), EnumQA.EnumLogObjectType.风险机遇.name(),
                currentPrincipalUser.getUserName() + "确认了风险机遇措施完成情况，下一步操作人：" + nextOperatorName, "", "");
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.风险机遇管理.getValue(), EnumQA.EnumQASubmitType.风险机遇拟定措施审核.getValue(),
                exist.getFinderId(), riskAndAccident.getSubmitRemark(), EnumQA.EnumRiskAndAccidentStatus.措施批准中.name(),
                EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name());
        return super.update(exist);
    }

    /**
     * 风险机遇措施完成情况批量退回：
     * 统一填充退回原因
     *
     * @param goBack 封装退回条件对象，包括需要退回的实体对象id集合和退回原因
     * @return 完成退回的实体List
     */
    @Transactional
    @Override
    public List<DtoRiskAndAccident> measureBack(DtoGoBack goBack) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取需要退回的风险机遇对象集合
        List<DtoRiskAndAccident> backRiskAndAccidents = super.findAll(goBack.getIds());
        //定义记录对象容器，用于统一保存
        List<DtoSubmitRecordQa> submitRecords = new ArrayList<>();
        //定义日志对象容器，用于统一保存
        List<DtoLog> logs = new ArrayList<>();
        //退回后的下一步操作人是措施拟定时候的下一步操作人
        List<DtoSubmitRecordQa> oldSubmitRecords = submitRecordService.findSubmitRecordBySubmitTypeAndObjectIds(
                EnumQA.EnumQASubmitType.风险机遇拟定措施提交.getValue(), goBack.getIds());
        //遍历退回对象集合，更新状态，创建记录
        for (DtoRiskAndAccident item : backRiskAndAccidents) {
            if (!EnumQA.EnumRiskAndAccidentStatus.确认不通过.name().equals(item.getStatus())
                    && !EnumQA.EnumRiskAndAccidentStatus.措施实施中.name().equals(item.getStatus())) {
                throw new BaseException("只能退回措施实施中和确认不通过的数据");
            }
            DtoSubmitRecordQa submitRecordItem = new DtoSubmitRecordQa();
            submitRecordItem.setSubmitRemark(goBack.getSubmitRemark());
            submitRecordItem.setSubmitTime(new Date());
            submitRecordItem.setSubmitType(EnumQA.EnumQASubmitType.风险机遇完成情况退回.getValue());
            submitRecordItem.setSubmitPersonId(currentPrincipalUser.getUserId());
            submitRecordItem.setSubmitPersonName(currentPrincipalUser.getUserName());
            submitRecordItem.setStateTo(EnumQA.EnumQASubmitType.风险机遇完成情况审核.name());
            submitRecordItem.setStateFrom(EnumQA.EnumQASubmitType.风险机遇完成情况审核.name());
            submitRecordItem.setObjectId(item.getId());
            submitRecordItem.setObjectType(EnumQA.EnumQAObjType.风险机遇管理.getValue());
            oldSubmitRecords.forEach(s -> {
                if (item.getId().equals(s.getObjectId())) {
                    submitRecordItem.setNextPerson(s.getNextPerson());
                }
            });
            submitRecords.add(submitRecordItem);
            DtoLog log = new DtoLog();
            log.setOperatorId(currentPrincipalUser.getUserId());
            log.setOperatorName(currentPrincipalUser.getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumQA.EnumLogOperateType.退回风险机遇完成情况.name());
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumQA.EnumLogType.风险机遇流程.name());
            log.setObjectId(item.getId());
            log.setObjectType(EnumQA.EnumLogObjectType.风险机遇.name());
            String comment = currentPrincipalUser.getUserName() + "退回风险机遇完成情况。";
            if (StringUtil.isNotEmpty(goBack.getSubmitRemark())) {
                comment += "意见：" + goBack.getSubmitRemark();
            }
            log.setComment(comment);
            //添加日志对象到容器中，统一保存
            logs.add(log);
            item.setStatus(EnumQA.EnumRiskAndAccidentStatus.完成情况不通过.name());
        }
        submitRecordService.createSubmitRecords(submitRecords, currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), currentPrincipalUser.getOrgId());
        logService.save(logs);
        return super.update(backRiskAndAccidents);
    }

    /**
     * 风险机遇确认：
     * 1. 根据isPassCompleteFlag判断是否通过，true为通过，false为不通过
     * 2. 通过填充确认日期affirmDate，确认及评价affirmEvaluate
     *
     * @param riskAndAccident 需要风险机遇完成确认的实体对象
     * @return 确认完成的实体
     */
    @Transactional
    @Override
    public DtoRiskAndAccident complete(DtoRiskAndAccident riskAndAccident) {
        CurrentPrincipalUser currentPrincipalUser = PrincipalContextUser.getPrincipal();
        //获取数据库中完整风险机遇对象
        DtoRiskAndAccident exist = super.findOne(riskAndAccident.getId());
        String status;
        String stateTo = "";
        String nextOperate = "";
        String nextOperatorId = "";
        if (Optional.ofNullable(riskAndAccident.getIsPassCompleteFlag()).isPresent()) {
            if (riskAndAccident.getIsPassCompleteFlag()) {
                //更新风险机遇对象状态
                status = EnumQA.EnumRiskAndAccidentStatus.确认完毕.name();
                nextOperate = "办结了风险机遇。";
            } else {
                status = EnumQA.EnumRiskAndAccidentStatus.确认不通过.name();
                stateTo = EnumQA.EnumQASubmitType.不符合项完成情况审核.name();
                //办结不通过退回的下一步操作人为指定的负责人
                nextOperatorId = exist.getDutyPersonId();
                DtoPerson person = personRepository.findOne(nextOperatorId);
                if (StringUtils.isNotNull(person)) {
                    nextOperate = "办结了风险机遇，办结不通过，下一步操作人：" + person.getCName() + "。";
                }
            }
            if (StringUtil.isNotEmpty(riskAndAccident.getSubmitRemark())) {
                nextOperate += "意见：" + riskAndAccident.getSubmitRemark();
            }
        } else {
            throw new BaseException("必须传回是否通过标示");
        }
        exist.setStatus(status);
        logService.generalSave(currentPrincipalUser.getUserId(), currentPrincipalUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.风险机遇确认.name(),
                UUIDHelper.GUID_EMPTY, riskAndAccident.getNextOperator(), EnumQA.EnumLogType.风险机遇流程.name(), riskAndAccident.getId(), EnumQA.EnumLogObjectType.风险机遇.name(),
                currentPrincipalUser.getUserName() + nextOperate, "", "");
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.风险机遇管理.getValue(), EnumQA.EnumQASubmitType.风险机遇方案确认.getValue(),
                nextOperatorId, riskAndAccident.getSubmitRemark(), EnumQA.EnumRiskAndAccidentStatus.完成情况确认中.name(),
                stateTo);
        return super.update(exist);
    }

    @Override
    public DtoRiskAndAccident findAttachPath(String id) {
        return super.findOne(id);
    }
}