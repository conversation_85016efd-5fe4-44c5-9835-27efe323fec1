package com.sinoyd.lims.qa.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.qa.enums.EnumQA;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;


/**
 * MonitoringPlan查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitoringPlanCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 评审时间
     */
    private String startTime;

    /**
     * 评审时间
     */
    private String endTime;

    /**
     * 计划名称关键字
     */
    private String key;

    /**
     * 制定人Id
     */
    private String marker;

    /**
     * 计划Id
     */
    private String planId;

    /**
     * 状态， 2已处理、1待处理、-1所有
     */
    private Integer status;

    /**
     * 页面来源: 1代表质量监督编制页面 ，2代表质量监督审核页面
     */
    private int pageFrom;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.objectId and b.isNewest = 1 ");

        List<String> statusList = new ArrayList();
        Calendar calendar = new GregorianCalendar();

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.markDate >= :startTime");
            values.put("startTime", date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1); //把日期往后增加一天,整数  往后推,负数往前移动
            date = calendar.getTime();
            condition.append(" and a.markDate < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and a.planName like :key ");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(marker)) {
            condition.append(" and a.marker = :marker");
            values.put("marker", this.marker);
        }
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        if (EnumQA.EnumMonitoringPlanPageFrom.质量监督编制.getValue() == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划评审中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.审核不通过.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划执行中.name());
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划编制中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.审核不通过.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督审核不通过.name());
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划评审中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.审核不通过.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划执行中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划编制中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.审核不通过.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督审核不通过.name());
            }
            condition.append(" and a.status in :statusList");
            values.put("statusList", statusList);
        } else if (EnumQA.EnumMonitoringPlanPageFrom.质量监督审核.getValue() == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.审核不通过.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划执行中.name());
                condition.append(" and ( b.submitPersonId = :loginUser )");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.计划评审中.name());
                condition.append(" and b.nextPerson = :loginUser ");
                condition.append(" and a.status in :statusList");
                values.put("loginUser", user.getUserId());
                values.put("statusList", statusList);
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                condition.append(" and ((b.nextPerson = :loginUser and a.status in :statusListA)  " +
                        "or (b.submitPersonId = :loginUser and a.status in :statusListB))");
                List<String> statusListA = new ArrayList();
                statusListA.add(EnumQA.EnumMonitoringPlanStatus.计划评审中.name());
                List<String> statusListB = new ArrayList();
                statusListB.add(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
                statusListB.add(EnumQA.EnumMonitoringPlanStatus.审核不通过.name());
                statusListB.add(EnumQA.EnumMonitoringPlanStatus.计划执行中.name());
                values.put("loginUser", user.getUserId());
                values.put("statusListA", statusListA);
                values.put("statusListB", statusListB);
            }
        } else if (EnumQA.EnumMonitoringPlanPageFrom.质量监督记录.getValue() == pageFrom) {
            if (EnumQA.EnumStatus.已处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督完毕.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督审核不通过.name());
                condition.append(" and (b.nextPerson = :loginUser or b.submitPersonId = :loginUser )");
            } else if (EnumQA.EnumStatus.待处理.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
                condition.append(" and b.nextPerson = :loginUser ");
            } else if (EnumQA.EnumStatus.所有.getValue().equals(status)) {
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督完毕.name());
                statusList.add(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
                condition.append(" and (b.nextPerson = :loginUser or b.submitPersonId = :loginUser )");
            }
            condition.append(" and a.status in :statusList");
            values.put("loginUser", user.getUserId());
            values.put("statusList", statusList);
        }
        return condition.toString();
    }
}