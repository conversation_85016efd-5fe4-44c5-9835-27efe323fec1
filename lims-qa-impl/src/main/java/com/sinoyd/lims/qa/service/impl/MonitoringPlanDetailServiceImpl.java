package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.*;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.MonitoringPlanDetailRepository;
import com.sinoyd.lims.qa.repository.MonitoringPlanRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.MonitoringPlanDetailService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * MonitoringPlanDetail操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
public class MonitoringPlanDetailServiceImpl extends BaseJpaServiceImpl<DtoMonitoringPlanDetail, String, MonitoringPlanDetailRepository> implements MonitoringPlanDetailService {

    private SubmitRecordRepository submitRecordRepository;

    private MonitoringPlanDetailRepository monitoringPlanDetailRepository;

    private MonitoringPlanRepository monitoringPlanRepository;

    private PersonRepository personRepository;

    private SubmitRecordService submitRecordService;

    private DepartmentService departmentService;

    private LogService logService;


    @Override
    public void findByPage(PageBean<DtoMonitoringPlanDetail> pb, BaseCriteria monitoringPlanDetailCriteria) {
        pb.setEntityName("DtoMonitoringPlanDetail a, DtoSubmitRecordQa b ");
        pb.setSelect("select a");
        super.findByPage(pb, monitoringPlanDetailCriteria);
        List<DtoMonitoringPlanDetail> dataList = pb.getData();
        //获取所有系统用户
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        //查询系统所有部门
        List<DtoDepartment> dtoDepartments = departmentService.findAll();
        //接收DtoMonitoringPlanDetail的Id集合为ids
        List<String> ids = dataList.parallelStream().map(DtoMonitoringPlanDetail::getId).collect(Collectors.toList());
        //通过ids获取submitRecord表的集合
        List<DtoSubmitRecordQa> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumQAObjType.质量监督.getValue(), ids);
        if (StringUtil.isNotEmpty(dataList)) {
            for (DtoMonitoringPlanDetail dto : dataList) {
                if (StringUtil.isNotEmpty(submitRecordList)) {
                    List<DtoSubmitRecordQa> thisDtoSubmitRecordQa = submitRecordList.parallelStream()
                            .filter(p -> p.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(thisDtoSubmitRecordQa)) {
                        //将得到的关联集合进行倒序排序，得到第一条数据（取最新的一条数据）
                        thisDtoSubmitRecordQa.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        dto.setSubmitRemark(thisDtoSubmitRecordQa.get(0).getSubmitRemark());
                    }
                }
                //填充责任人姓名
                Optional<DtoPerson> personOptional = dtoPersonList.parallelStream().filter(p -> p.getId().equals(dto.getDutyPersonId())).findFirst();
                personOptional.ifPresent(dtoPerson -> dto.setDutyPersonIdName(dtoPerson.getCName()));
                //填充部门名称字段
                Optional<DtoDepartment> deptOptional = dtoDepartments.parallelStream().filter(p -> p.getId().equals(dto.getDutyDept())).findFirst();
                deptOptional.ifPresent(dtoDepartment -> dto.setDeptName(dtoDepartment.getDeptName()));
            }
        }
    }

    @Override
    public DtoMonitoringPlanDetail findOne(String key) {
        DtoMonitoringPlanDetail one = super.findOne(key);
        DtoDepartment department = departmentService.findOne(one.getDutyDept());
        if (StringUtil.isNotNull(department)) {
            one.setDeptName(department.getDeptName());
        }
        return super.findOne(key);
    }

    @Transactional
    @Override
    public DtoMonitoringPlanDetail save(DtoMonitoringPlanDetail entity) {
        entity.setStatus(EnumQA.EnumMonitoringPlanStatus.计划明细编制中.name());
        entity = super.save(entity);
        //获取当前登录人
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoPerson responsible = personRepository.findOne(entity.getDutyPersonId());
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.质量监督.getValue(), EnumQA.EnumQASubmitType.质量监督计划明细登记.getValue(),
                null, null, EnumQA.EnumMonitoringPlanStatus.计划明细编制中.name(), EnumQA.EnumMonitoringPlanStatus.计划明细编制中.name());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增计划明细.name(),
                null, null, EnumQA.EnumLogType.质量监督流程.name(), entity.getPlanId(), EnumQA.EnumLogObjectType.质量监督.name(),
                currentUser.getUserName() + "新增计划明细信息，责任人：" + responsible.getCName(), "", "");
        return entity;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        if (StringUtil.isNotNull(idList)) {
            List<DtoMonitoringPlanDetail> dtoMonitoringPlanDetailList = repository.findAll(idList);
            if (StringUtil.isNotEmpty(dtoMonitoringPlanDetailList)) {
                //遍历要删除的用户
                for (DtoMonitoringPlanDetail dto : dtoMonitoringPlanDetailList) {
                    if (!currentUser.getUserId().equals(dto.getCreator())) {
                        throw new BaseException("只能删除自己创建的质量监督的计划明细");
                    }
                }
            }
            return super.logicDeleteById(ids);
        }
        return 0;
    }

    @Transactional
    @Override
    public DtoMonitoringPlanDetail submit(DtoMonitoringPlanDetail dtoMonitoringPlanDetail) {
        DtoMonitoringPlanDetail exist = super.findOne(dtoMonitoringPlanDetail.getId());
        exist.setStatus(EnumQA.EnumMonitoringPlanStatus.执行完毕.name());
        DtoMonitoringPlan dtoMonitoringPlan = monitoringPlanRepository.findOne(exist.getPlanId());
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.质量监督.getValue(),
                EnumQA.EnumQASubmitType.质量监督计划明细提交.getValue(), null, null,
                exist.getStatus(), EnumQA.EnumMonitoringPlanStatus.执行完毕.name());
        //通过质量监督的id,找到对应的所有质量监督计划明细  如果他们的状态都变成内审完毕,就将状态改成报告编制中
        List<DtoMonitoringPlanDetail> list = monitoringPlanDetailRepository.findByPlanId(exist.getPlanId());
        boolean internalAuditFinishedFlag = Boolean.TRUE;
        for (DtoMonitoringPlanDetail dto : list) {
            if (!dto.getStatus().equals(EnumQA.EnumMonitoringPlanStatus.执行完毕.name())) {
                internalAuditFinishedFlag = Boolean.FALSE;
                break;
            }
        }
        if (internalAuditFinishedFlag) {
            dtoMonitoringPlan.setStatus(EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
            monitoringPlanRepository.save(dtoMonitoringPlan);
            //添加提交状态
            submitRecordService.createSubmitRecord(dtoMonitoringPlan.getId(), EnumQA.EnumQAObjType.质量监督.getValue(),
                    EnumQA.EnumQASubmitType.质量监督计划审核.getValue(), exist.getCreator(), null,
                    EnumQA.EnumMonitoringPlanStatus.计划评审中.name(), EnumQA.EnumMonitoringPlanStatus.质量监督中.name());
        }
        //获取当前登录人
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoPerson nextPerson = personRepository.findOne(exist.getCreator());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交质量监督计划明细.name(),
                null, null, EnumQA.EnumLogType.质量监督流程.name(), exist.getPlanId(), EnumQA.EnumQAObjType.质量监督.name(),
                currentUser.getUserName() + "提交质量监督执行，下一步操作人：" + nextPerson.getCName(), "", "");
        return super.update(exist);
    }


    @Transactional
    @Override
    public List<DtoMonitoringPlanDetail> goBack(DtoGoBack dtoGoBack) {
        List<DtoMonitoringPlanDetail> list = super.findAll(dtoGoBack.getIds());
        //质量监督表退回成计划执行中
        DtoMonitoringPlan dtoMonitoringPlan = monitoringPlanRepository.findOne(list.get(0).getPlanId());
        dtoMonitoringPlan.setStatus(EnumQA.EnumMonitoringPlanStatus.计划执行中.name());
        //退回的操作也插入记录，把这条记录退回到编制中以及创建人手中
        submitRecordService.createSubmitRecord(dtoMonitoringPlan.getId(), EnumQA.EnumQAObjType.质量监督.getValue(),
                EnumQA.EnumQASubmitType.质量监督记录审核.getValue(), dtoMonitoringPlan.getCreator(), null,
                EnumQA.EnumMonitoringPlanStatus.质量监督中.name(), EnumQA.EnumInternalAuditPlanStatus.计划执行中.name());
        monitoringPlanRepository.save(dtoMonitoringPlan);
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        List<DtoPerson> responsiblePeople = personRepository.findAll(list.parallelStream().map(DtoMonitoringPlanDetail::getDutyPersonId).collect(Collectors.toSet()));
        List<DtoSubmitRecordQa> submitList = new ArrayList<>();
        List<DtoLog> logList = new ArrayList<>();
        for (DtoMonitoringPlanDetail dto : list) {
            DtoSubmitRecordQa submitRecord = new DtoSubmitRecordQa();
            submitRecord.setObjectId(dto.getId());
            submitRecord.setObjectType(EnumQA.EnumQAObjType.质量监督.getValue());
            submitRecord.setSubmitType(EnumQA.EnumQASubmitType.质量监督计划明细退回.getValue());
            submitRecord.setNextPerson(dto.getDutyPersonId());
            submitRecord.setSubmitRemark(dtoGoBack.getSubmitRemark());
            submitRecord.setStateFrom(EnumQA.EnumMonitoringPlanStatus.执行完毕.name());
            dto.setStatus(EnumQA.EnumMonitoringPlanStatus.执行中.name());
            submitRecord.setStateTo(dto.getStatus());
            submitList.add(submitRecord);
            DtoLog log = new DtoLog();
            log.setObjectId(dtoMonitoringPlan.getId());
            log.setObjectType(EnumQA.EnumLogObjectType.质量监督.name());
            log.setOperatorId(currentUser.getUserId());
            log.setOperatorName(currentUser.getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumQA.EnumLogOperateType.退回质量监督计划明细.name());
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumQA.EnumLogType.质量监督流程.name());
            Optional<DtoPerson> responsible = responsiblePeople.parallelStream().filter(p -> p.getId().equals(dto.getDutyPersonId())).findFirst();
            String comment = "";
            if (responsible.isPresent()) {
                comment = currentUser.getUserName() + "退回质量监督计划明细，责任人：" + responsible.get().getCName() + "，意见：" + dtoGoBack.getSubmitRemark();
            } else {
                comment = currentUser.getUserName() + "退回质量监督计划明细，意见：" + dtoGoBack.getSubmitRemark();
            }
            log.setComment(comment);
            logList.add(log);
        }
        submitRecordService.createSubmitRecords(submitList, currentUser.getUserId(), currentUser.getUserName(), currentUser.getOrgId());
        logService.save(logList);
        return super.update(list);
    }

    @Autowired
    public void setLogService(LogService logService) {
        this.logService = logService;
    }

    @Autowired
    public void setSubmitRecordRepository(SubmitRecordRepository submitRecordRepository) {
        this.submitRecordRepository = submitRecordRepository;
    }

    @Autowired
    public void setMonitoringPlanDetailRepository(MonitoringPlanDetailRepository monitoringPlanDetailRepository) {
        this.monitoringPlanDetailRepository = monitoringPlanDetailRepository;
    }

    @Autowired
    public void setMonitoringPlanRepository(MonitoringPlanRepository monitoringPlanRepository) {
        this.monitoringPlanRepository = monitoringPlanRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setSubmitRecordService(SubmitRecordService submitRecordService) {
        this.submitRecordService = submitRecordService;
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }
}