package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.InternalCheckInfoService;
import com.sinoyd.lims.qa.criteria.InternalCheckInfoCriteria;
import com.sinoyd.lims.qa.dto.DtoInternalCheckInfo;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * InternalCheckInfo服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: InternalCheckInfo服务")
@RestController
@RequestMapping("api/qa/internalCheckInfo")
@Validated
public class InternalCheckInfoController extends BaseJpaController<DtoInternalCheckInfo, String, InternalCheckInfoService> {


    /**
     * 分页动态条件查询InternalCheckInfo
     *
     * @param internalCheckInfoCriteria 条件参数
     * @return RestResponse<List   <   InternalCheckInfo>>
     */
    @ApiOperation(value = "分页动态条件查询InternalCheckInfo", notes = "分页动态条件查询InternalCheckInfo")
    @GetMapping
    public RestResponse<List<DtoInternalCheckInfo>> findByPage(InternalCheckInfoCriteria internalCheckInfoCriteria) {
        PageBean<DtoInternalCheckInfo> pageBean = super.getPageBean();
        RestResponse<List<DtoInternalCheckInfo>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, internalCheckInfoCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增InternalCheckInfo
     *
     * @param internalCheckInfo 实体列表
     * @return RestResponse<DtoInternalCheckInfo>
     */
    @ApiOperation(value = "新增InternalCheckInfo", notes = "新增InternalCheckInfo")
    @PostMapping
    public RestResponse<DtoInternalCheckInfo> create(@Validated @RequestBody DtoInternalCheckInfo internalCheckInfo) {
        RestResponse<DtoInternalCheckInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.save(internalCheckInfo));
        return restResponse;
    }

    /**
     * 新增InternalCheckInfo
     *
     * @param internalCheckInfo 实体列表
     * @return RestResponse<DtoInternalCheckInfo>
     */
    @ApiOperation(value = "修改InternalCheckInfo", notes = "修改InternalCheckInfo")
    @PutMapping
    public RestResponse<DtoInternalCheckInfo> update(@Validated @RequestBody DtoInternalCheckInfo internalCheckInfo) {
        RestResponse<DtoInternalCheckInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.update(internalCheckInfo));
        return restResponse;
    }

    /**
     * "根据id批量删除InternalCheckInfo
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除InternalCheckInfo", notes = "根据id批量删除InternalCheckInfo")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}