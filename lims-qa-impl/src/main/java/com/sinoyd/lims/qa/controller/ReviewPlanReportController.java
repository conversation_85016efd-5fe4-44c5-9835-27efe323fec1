package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.ReviewPlanReportService;
import com.sinoyd.lims.qa.criteria.ReviewPlanReportCriteria;
import com.sinoyd.lims.qa.dto.DtoReviewPlanReport;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ReviewPlanReport服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
 @Api(tags = "示例: ReviewPlanReport服务")
 @RestController
 @RequestMapping("api/qa/reviewPlanReport")
 @Validated
 public class ReviewPlanReportController extends BaseJpaController<DtoReviewPlanReport, String,ReviewPlanReportService> {


    /**
     * 分页动态条件查询ReviewPlanReport
     * @param reviewPlanReportCriteria 条件参数
     * @return RestResponse<List<ReviewPlanReport>>
     */
     @ApiOperation(value = "分页动态条件查询ReviewPlanReport", notes = "分页动态条件查询ReviewPlanReport")
     @GetMapping
     public RestResponse<List<DtoReviewPlanReport>> findByPage(ReviewPlanReportCriteria reviewPlanReportCriteria) {
         PageBean<DtoReviewPlanReport> pageBean = super.getPageBean();
         RestResponse<List<DtoReviewPlanReport>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, reviewPlanReportCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ReviewPlanReport
     * @param reviewPlanId
     * @return RestResponse<DtoReviewPlanReport>
     */
     @ApiOperation(value = "按主键查询ReviewPlanReport", notes = "按主键查询ReviewPlanReport")
     @GetMapping(path = "/{reviewPlanId}")
     public RestResponse<DtoReviewPlanReport> find(@PathVariable(name = "reviewPlanId") String reviewPlanId) {
         RestResponse<DtoReviewPlanReport> restResponse = new RestResponse<>();
         DtoReviewPlanReport reviewPlanReport = service.findOne(reviewPlanId);
         restResponse.setData(reviewPlanReport);
         restResponse.setRestStatus(StringUtil.isNull(reviewPlanReport) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }



    /**
     * 新增ReviewPlanReport
     * @param reviewPlanReport 实体列表
     * @return RestResponse<DtoReviewPlanReport>
     */
     @ApiOperation(value = "新增ReviewPlanReport", notes = "新增ReviewPlanReport")
     @PostMapping
     public RestResponse<DtoReviewPlanReport> create(@Validated @RequestBody DtoReviewPlanReport reviewPlanReport) {
         RestResponse<DtoReviewPlanReport> restResponse = new RestResponse<>();
         restResponse.setData(service.save(reviewPlanReport));
         return restResponse;
      }

     /**
     * 新增ReviewPlanReport
     * @param reviewPlanReport 实体列表
     * @return RestResponse<DtoReviewPlanReport>
     */
     @ApiOperation(value = "修改ReviewPlanReport", notes = "修改ReviewPlanReport")
     @PutMapping
     public RestResponse<DtoReviewPlanReport> update(@Validated @RequestBody DtoReviewPlanReport reviewPlanReport) {
         RestResponse<DtoReviewPlanReport> restResponse = new RestResponse<>();
         restResponse.setData(service.update(reviewPlanReport));
         return restResponse;
      }

    /**
     * "根据id批量删除ReviewPlanReport
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ReviewPlanReport", notes = "根据id批量删除ReviewPlanReport")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }