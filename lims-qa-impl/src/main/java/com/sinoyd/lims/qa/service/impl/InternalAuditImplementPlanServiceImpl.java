package com.sinoyd.lims.qa.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.qa.dto.*;
import com.sinoyd.lims.qa.enums.EnumQA;
import com.sinoyd.lims.qa.repository.InternalAuditImplementPlanRepository;
import com.sinoyd.lims.qa.repository.InternalAuditPlanRepository;
import com.sinoyd.lims.qa.repository.InternalCheckInfoRepository;
import com.sinoyd.lims.qa.repository.SubmitRecordRepository;
import com.sinoyd.lims.qa.service.InternalAuditImplementPlanService;
import com.sinoyd.lims.qa.service.InternalAuditPlanService;
import com.sinoyd.lims.qa.service.LogService;
import com.sinoyd.lims.qa.service.SubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * InternalAuditImplementPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class InternalAuditImplementPlanServiceImpl extends BaseJpaServiceImpl<DtoInternalAuditImplementPlan, String, InternalAuditImplementPlanRepository> implements InternalAuditImplementPlanService {

    private final SubmitRecordRepository submitRecordRepository;

    private final InternalAuditImplementPlanRepository internalAuditImplementPlanRepository;

    private final InternalAuditPlanService internalAuditPlanService;

    private final InternalAuditPlanRepository internalAuditPlanRepository;

    private final PersonRepository personRepository;

    private final SubmitRecordService submitRecordService;

    private final InternalCheckInfoRepository internalCheckInfoRepository;

    private final DepartmentService departmentService;

    private LogService logService;


    @Override
    public void findByPage(PageBean<DtoInternalAuditImplementPlan> pb, BaseCriteria internalAuditImplementPlanCriteria) {
        pb.setEntityName("DtoInternalAuditImplementPlan a, DtoSubmitRecordQa b");
        pb.setSelect("select a ");
        comRepository.findByPage(pb, internalAuditImplementPlanCriteria);
        List<DtoInternalAuditImplementPlan> dataList = pb.getData();//获取DtoInternalAuditImplementPlan的集合
        if (StringUtil.isNotEmpty(dataList)) {
            List<DtoDepartment> dtoDepartments = departmentService.findAll();
            //所有人员集合
            List<DtoPerson> dtoPerson = personRepository.findAll();
            List<String> ids = dataList.parallelStream().map(DtoInternalAuditImplementPlan::getId).collect(Collectors.toList());
            List<DtoSubmitRecordQa> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectIdIn(EnumQA.EnumQAObjType.内审管理.getValue(), ids);
            for (DtoInternalAuditImplementPlan dto : dataList) {
                if (StringUtil.isNotEmpty(submitRecordList)) {
                    //获取DtoSubmitRecord表getObjectId和DtoAnnualPlan表的ID比较得到关联的集合
                    List<DtoSubmitRecordQa> thisDtoSubmitRecordQa = submitRecordList.parallelStream()
                            .filter(p -> p.getObjectId().equals(dto.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(thisDtoSubmitRecordQa)) {
                        //将得到的关联集合进行倒序排序，得到第一条数据（取最新的一条数据）
                        thisDtoSubmitRecordQa.sort(Comparator.comparing(DtoSubmitRecordQa::getSubmitTime, Comparator.reverseOrder()));
                        dto.setSubmitRemark(thisDtoSubmitRecordQa.get(0).getSubmitRemark());
                    }
                }
                for (DtoPerson dtoPer : dtoPerson) {
                    //将责任人的id冗余成中文名
                    if (dto.getPersonInCharge().equals(dtoPer.getId())) {
                        dto.setPersonInChargeName(dtoPer.getCName());
                    }
                    //将审核人员的id冗余成中文名
                    if (dto.getAuditor().equals(dtoPer.getId())) {
                        dto.setAuditorName(dtoPer.getCName());
                    }
                }
                Optional<DtoDepartment> optionalDtoDepartment = dtoDepartments.parallelStream().filter(p -> p.getId().equals(dto.getAuditedDept())).findFirst();
                optionalDtoDepartment.ifPresent(dept -> dto.setAuditedDeptName(dept.getDeptName()));
            }
        }
    }

    @Transactional
    @Override
    public DtoInternalAuditImplementPlan save(DtoInternalAuditImplementPlan entity) {
        entity.setStatus(EnumQA.EnumInternalAuditPlanStatus.计划编制中.name());
        submitRecordService.createSubmitRecord(entity.getId(), EnumQA.EnumQAObjType.内审管理.getValue(), EnumQA.EnumQASubmitType.内审实施计划登记.getValue(),
                null, null, EnumQA.EnumInternalAuditPlanStatus.计划编制中.name(), EnumQA.EnumInternalAuditPlanStatus.计划编制中.name());
        super.save(entity);
        DtoDepartment dtoDepartment = departmentService.findOne(entity.getAuditedDept());
        if (StringUtil.isNotNull(dtoDepartment)) {
            entity.setAuditedDeptName(dtoDepartment.getDeptName());
        }
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        DtoPerson responsible = personRepository.findOne(entity.getPersonInCharge());
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.新增内审实施计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), entity.getAuditPlanId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "新增了内审实施计划" + "，责任人：" + responsible.getCName(), "", "");
        return entity;
    }

    @Transactional
    @Override
    public DtoInternalAuditImplementPlan update(DtoInternalAuditImplementPlan entity) {
        super.update(entity);
        DtoDepartment dtoDepartment = departmentService.findOne(entity.getAuditedDept());
        if (StringUtil.isNotNull(dtoDepartment)) {
            entity.setAuditedDeptName(dtoDepartment.getDeptName());
        }
        return entity;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        if (StringUtil.isNotEmpty(idList)) {
            List<DtoLog> logList = new ArrayList<>();
            List<DtoInternalAuditImplementPlan> dtoInternalAuditImplementPlans = repository.findAll(idList);
            if (StringUtil.isNotEmpty(dtoInternalAuditImplementPlans)) {
                //遍历要删除的集合
                for (DtoInternalAuditImplementPlan dto : dtoInternalAuditImplementPlans) {
                    if (!currentUser.getUserId().equals(dto.getCreator())) {
                        throw new BaseException("只能删除自己创建的内审管理实施计划");
                    }
                    DtoLog log = new DtoLog();
                    log.setObjectId(dto.getAuditPlanId());
                    log.setObjectType(EnumQA.EnumLogObjectType.内审管理.name());
                    log.setOperatorId(currentUser.getUserId());
                    log.setOperatorName(currentUser.getUserName());
                    log.setOperateTime(new Date());
                    log.setOperateInfo(EnumQA.EnumLogOperateType.删除内审实施计划.name());
                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    log.setNextOperatorName("");
                    log.setLogType(EnumQA.EnumLogType.内审管理流程.name());
                    String comment = currentUser.getUserName() + "删除了内审实施计划";
                    log.setComment(comment);
                    logList.add(log);
                }
            }
            logService.save(logList);
            return super.logicDeleteById(ids);
        }
        return 0;
    }

    @Transactional
    @Override
    public DtoInternalAuditImplementPlan submit(DtoInternalAuditImplementPlan dtoInternalAuditImplementPlan) {
        DtoInternalAuditImplementPlan exist = super.findOne(dtoInternalAuditImplementPlan.getId());
        DtoInternalAuditPlan dtoInternalAuditPlan = internalAuditPlanService.findOne(exist.getAuditPlanId());
        DtoPerson internalCreator = personRepository.findOne(dtoInternalAuditPlan.getCreator());
        exist.setStatus(EnumQA.EnumInternalAuditPlanStatus.内审完毕.name());
        submitRecordService.createSubmitRecord(exist.getId(), EnumQA.EnumQAObjType.内审管理.getValue(),
                EnumQA.EnumQASubmitType.内审实施计划提交.getValue(), null, null,
                exist.getStatus(), EnumQA.EnumInternalAuditPlanStatus.内审完毕.name());
        //通过内审计划的id,找到对应的所有内审实施计划  如果他们的状态都变成内审完毕,就将状态改成报告编制中
        List<DtoInternalAuditImplementPlan> list = internalAuditImplementPlanRepository.findByAuditPlanId(exist.getAuditPlanId());
        boolean internalAuditFinishedFlag = Boolean.TRUE;
        for (DtoInternalAuditImplementPlan dto : list) {
            if (!dto.getStatus().equals(EnumQA.EnumInternalAuditPlanStatus.内审完毕.name())) {
                internalAuditFinishedFlag = Boolean.FALSE;
                break;
            }
        }
        if (internalAuditFinishedFlag) {
            dtoInternalAuditPlan.setStatus(EnumQA.EnumInternalAuditPlanStatus.报告编制中.name());//报告编制中
            internalAuditPlanRepository.save(dtoInternalAuditPlan);
            //添加提交状态
            submitRecordService.createSubmitRecord(dtoInternalAuditPlan.getId(), EnumQA.EnumQAObjType.内审管理.getValue(),
                    EnumQA.EnumQASubmitType.内审管理计划审核.getValue(), dtoInternalAuditPlan.getCreator(), null,
                    EnumQA.EnumInternalAuditPlanStatus.计划审核中.name(), EnumQA.EnumInternalAuditPlanStatus.报告编制中.name());
        }
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        logService.generalSave(currentUser.getUserId(), currentUser.getUserName(), new Date(), EnumQA.EnumLogOperateType.提交内审实施计划.name(),
                UUIDHelper.GUID_EMPTY, "", EnumQA.EnumLogType.内审管理流程.name(), exist.getAuditPlanId(),
                EnumQA.EnumLogObjectType.内审管理.name(), currentUser.getUserName() + "提交内审计划，下一步操作人：" + internalCreator.getCName(), "", "");
        return super.update(exist);
    }

    @Transactional
    @Override
    public List<DtoInternalAuditImplementPlan> goBack(DtoGoBack dtoGoBack) {
        List<DtoInternalAuditImplementPlan> list = super.findAll(dtoGoBack.getIds());
        DtoInternalAuditPlan dtoInternalAuditPlan = internalAuditPlanRepository.findOne(list.get(0).getAuditPlanId());
        //退回就把内审管理计划状态改成计划执行中
        dtoInternalAuditPlan.setStatus(EnumQA.EnumInternalAuditPlanStatus.计划执行中.name());
        //退回的操作也插入记录，把这条记录退回到计划执行中
        submitRecordService.createSubmitRecord(dtoInternalAuditPlan.getId(), EnumQA.EnumQAObjType.内审管理.getValue(),
                EnumQA.EnumQASubmitType.内审管理计划审核.getValue(), dtoInternalAuditPlan.getCreator(), null,
                EnumQA.EnumInternalAuditPlanStatus.报告编制中.name(), EnumQA.EnumInternalAuditPlanStatus.计划执行中.name());
        internalAuditPlanRepository.save(dtoInternalAuditPlan);
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        List<DtoPerson> responsiblePeople = personRepository.findAll(list.parallelStream().map(DtoInternalAuditImplementPlan::getPersonInCharge).collect(Collectors.toSet()));
        List<DtoSubmitRecordQa> submitList = new ArrayList<>();
        List<DtoLog> logList = new ArrayList<>();
        for (DtoInternalAuditImplementPlan dto : list) {
            DtoSubmitRecordQa submitRecord = new DtoSubmitRecordQa();
            submitRecord.setObjectId(dto.getId());
            submitRecord.setObjectType(EnumQA.EnumQAObjType.内审管理.getValue());//关联对象类型id
            submitRecord.setSubmitType(EnumQA.EnumQASubmitType.内审实施计划退回.getValue());
            submitRecord.setNextPerson(dto.getAuditor());//内审实施计划的审核人员
            submitRecord.setStateFrom(EnumQA.EnumInternalAuditPlanStatus.内审完毕.name());//操作前状态
            submitRecord.setSubmitRemark(dtoGoBack.getSubmitRemark());
            dto.setStatus(EnumQA.EnumInternalAuditPlanStatus.内审检查中.name());//将所有内审实施计划变成内审检查中
            submitRecord.setStateTo(dto.getStatus());//操作后状态
            submitList.add(submitRecord);
            DtoLog log = new DtoLog();
            log.setObjectId(dto.getAuditPlanId());
            log.setObjectType(EnumQA.EnumLogObjectType.内审管理.name());
            log.setOperatorId(currentUser.getUserId());
            log.setOperatorName(currentUser.getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumQA.EnumLogOperateType.退回内审实施计划.name());
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumQA.EnumLogType.内审管理流程.name());
            Optional<DtoPerson> responsible = responsiblePeople.parallelStream().filter(p -> p.getId().equals(dto.getPersonInCharge())).findFirst();
            String comment = "";
            if (responsible.isPresent()) {
                comment = currentUser.getUserName() + "退回内审实施计划，责任人：" + responsible.get().getCName() + "，意见：" + dtoGoBack.getSubmitRemark();
            } else {
                comment = currentUser.getUserName() + "退回内审实施计划意见：" + dtoGoBack.getSubmitRemark();
            }
            log.setComment(comment);
            logList.add(log);
        }
        submitRecordService.createSubmitRecords(submitList, currentUser.getUserId(), currentUser.getUserName(), currentUser.getOrgId());
        logService.save(logList);
        return super.update(list);
    }

    @Override
    public DtoInternalAuditImplementPlan findAttachPath(String id) {
        return findOne(id);
    }

    @Override
    public DtoInternalAuditImplementPlan findOne(String id) {
        DtoInternalAuditImplementPlan auditImplementPlan = repository.findOne(id);
        if (StringUtil.isNotNull(auditImplementPlan)) {
            List<DtoInternalCheckInfo> list = internalCheckInfoRepository.findByImplementPlanId(auditImplementPlan.getId());
            if (StringUtil.isNotNull(list)) {
                //将检查项放到内审实施计划
                auditImplementPlan.setDtoInternalCheckInfoList(list);
            }
        }
        return auditImplementPlan;
    }

    @Autowired
    public void setLogService(LogService logService) {
        this.logService = logService;
    }
}