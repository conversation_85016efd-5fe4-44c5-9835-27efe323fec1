package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.AnnualPlanService;
import com.sinoyd.lims.qa.criteria.AnnualPlanCriteria;
import com.sinoyd.lims.qa.dto.DtoAnnualPlan;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * AnnualPlan服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: AnnualPlan服务")
@RestController
@RequestMapping("api/qa/annualPlan")
@Validated
public class AnnualPlanController extends BaseJpaController<DtoAnnualPlan, String, AnnualPlanService> {

    /**
     * 分页动态条件查询AnnualPlan
     *
     * @param annualPlanCriteria 条件参数
     * @return RestResponse<List<AnnualPlan>>
     */
    @ApiOperation(value = "分页动态条件查询AnnualPlan", notes = "分页动态条件查询AnnualPlan")
    @GetMapping
    public RestResponse<List<DtoAnnualPlan>> findByPage(AnnualPlanCriteria annualPlanCriteria) {
        PageBean<DtoAnnualPlan> pageBean = super.getPageBean();
        RestResponse<List<DtoAnnualPlan>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, annualPlanCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询AnnualPlan
     *
     * @param id 主键id
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "按主键查询AnnualPlan", notes = "按主键查询AnnualPlan")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoAnnualPlan> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoAnnualPlan> restResponse = new RestResponse<>();
        DtoAnnualPlan annualPlan = service.findOne(id);
        restResponse.setData(annualPlan);
        restResponse.setRestStatus(StringUtil.isNull(annualPlan) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增AnnualPlan实体
     *
     * @param annualPlan 实体列表
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "新增AnnualPlan", notes = "新增AnnualPlan")
    @PostMapping
    public RestResponse<DtoAnnualPlan> create(@Validated @RequestBody DtoAnnualPlan annualPlan) {
        RestResponse<DtoAnnualPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.save(annualPlan));
        return restResponse;
    }

    /**
     * 修改AnnualPlan实体
     *
     * @param annualPlan 实体列表
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "修改AnnualPlan", notes = "修改AnnualPlan")
    @PutMapping
    public RestResponse<DtoAnnualPlan> update(@Validated @RequestBody DtoAnnualPlan annualPlan) {
        RestResponse<DtoAnnualPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.update(annualPlan));
        return restResponse;
    }

    /**
     * 提交年度计划
     *
     * @param dtoAnnualPlan 实体列表
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "提交AnnualPlan", notes = "提交AnnualPlan")
    @PutMapping(path = "/submit")
    public RestResponse<DtoAnnualPlan> submit(@RequestBody @Validated  DtoAnnualPlan dtoAnnualPlan) {
        RestResponse<DtoAnnualPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(dtoAnnualPlan));
        return restResponse;
    }

    /**
     * 审核年度计划
     *
     * @param dtoAnnualPlan 实体
     * @return RestResponse<DtoAnnualPlan>
     */
    @ApiOperation(value = "审核AnnualPlan", notes = "审核AnnualPlan")
    @PutMapping(path = "/audit")
    public RestResponse<DtoAnnualPlan> audit(@RequestBody @Validated DtoAnnualPlan dtoAnnualPlan) {
        RestResponse<DtoAnnualPlan> restResponse = new RestResponse<>();
        restResponse.setData(service.audit(dtoAnnualPlan));
        return restResponse;
    }

    /**
     * "根据id批量删除年度计划
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除AnnualPlan", notes = "根据id批量删除AnnualPlan")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}