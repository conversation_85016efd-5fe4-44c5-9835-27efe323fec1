package com.sinoyd.lims.qa.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.qa.service.CustomerComplaintRegistService;
import com.sinoyd.lims.qa.criteria.CustomerComplaintRegistCriteria;
import com.sinoyd.lims.qa.dto.DtoCustomerComplaintRegist;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * CustomerComplaintRegist服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
@Api(tags = "示例: CustomerComplaintRegist服务")
@RestController
@RequestMapping("api/qa/customerComplaintRegist")
@Validated
public class CustomerComplaintRegistController extends BaseJpaController<DtoCustomerComplaintRegist, String, CustomerComplaintRegistService> {


    /**
     * 分页动态条件查询CustomerComplaintRegist
     *
     * @param customerComplaintRegistCriteria 条件参数
     * @return RestResponse<List < CustomerComplaintRegist>>
     */
    @ApiOperation(value = "分页动态条件查询CustomerComplaintRegist", notes = "分页动态条件查询CustomerComplaintRegist")
    @GetMapping
    public RestResponse<List<DtoCustomerComplaintRegist>> findByPage(CustomerComplaintRegistCriteria customerComplaintRegistCriteria) {
        PageBean<DtoCustomerComplaintRegist> pageBean = super.getPageBean();
        RestResponse<List<DtoCustomerComplaintRegist>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, customerComplaintRegistCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询CustomerComplaintRegist
     *
     * @param id 主键id
     * @return RestResponse<DtoCustomerComplaintRegist>
     */
    @ApiOperation(value = "按主键查询CustomerComplaintRegist", notes = "按主键查询CustomerComplaintRegist")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoCustomerComplaintRegist> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        DtoCustomerComplaintRegist customerComplaintRegist = service.findOne(id);
        restResponse.setData(customerComplaintRegist);
        restResponse.setRestStatus(StringUtil.isNull(customerComplaintRegist) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增CustomerComplaintRegist
     *
     * @param customerComplaintRegist 实体列表
     * @return RestResponse<DtoCustomerComplaintRegist>
     */
    @ApiOperation(value = "新增CustomerComplaintRegist", notes = "新增CustomerComplaintRegist")
    @PostMapping
    public RestResponse<DtoCustomerComplaintRegist> create(@Validated @RequestBody DtoCustomerComplaintRegist customerComplaintRegist) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        restResponse.setData(service.save(customerComplaintRegist));
        return restResponse;
    }

    /**
     * 修改CustomerComplaintRegist
     *
     * @param customerComplaintRegist 实体列表
     * @return RestResponse<DtoCustomerComplaintRegist>
     */
    @ApiOperation(value = "修改CustomerComplaintRegist", notes = "修改CustomerComplaintRegist")
    @PutMapping
    public RestResponse<DtoCustomerComplaintRegist> update(@Validated @RequestBody DtoCustomerComplaintRegist customerComplaintRegist) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        restResponse.setData(service.update(customerComplaintRegist));
        return restResponse;
    }

    /**
     * "根据id批量删除CustomerComplaintRegist
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除CustomerComplaintRegist", notes = "根据id批量删除CustomerComplaintRegist")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 提交客户投诉
     *
     * @param customerComplaintRegist 客户投诉实体
     * @return 封装返回结果
     */
    @ApiOperation(value = "提交customerComplaintRegist客户投诉", notes = "提交CustomerComplaintRegist客户投诉")
    @PutMapping("/submit")
    public RestResponse<DtoCustomerComplaintRegist> submit(@RequestBody DtoCustomerComplaintRegist customerComplaintRegist) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        restResponse.setData(service.submit(customerComplaintRegist));
        return restResponse;
    }

    /**
     * 审核客户投诉
     *
     * @param customerComplaintRegist 客户投诉实体
     * @return 审核实体封装结果
     */
    @ApiOperation(value = "审核customerComplaintRegist客户投诉", notes = "审核CustomerComplaintRegist客户投诉")
    @PutMapping("/audit")
    public RestResponse<DtoCustomerComplaintRegist> audit(@RequestBody DtoCustomerComplaintRegist customerComplaintRegist) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        restResponse.setData(service.audit(customerComplaintRegist));
        return restResponse;
    }

    /**
     * 确认客户投诉
     *
     * @param customerComplaintRegist 客户投诉实体
     * @return 确认实体封装结果
     */
    @ApiOperation(value = "确认customerComplaintRegist客户投诉", notes = "确认CustomerComplaintRegist客户投诉")
    @PutMapping("/confirm")
    public RestResponse<DtoCustomerComplaintRegist> confirm(@RequestBody DtoCustomerComplaintRegist customerComplaintRegist) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        restResponse.setData(service.confirm(customerComplaintRegist));
        return restResponse;
    }

    /**
     * 办结客户投诉
     *
     * @param customerComplaintRegist 客户投诉实体
     * @return 办结实体封装结果
     */
    @ApiOperation(value = "审核customerComplaintRegist客户投诉", notes = "审核CustomerComplaintRegist客户投诉")
    @PutMapping("/complete")
    public RestResponse<DtoCustomerComplaintRegist> complete(@RequestBody DtoCustomerComplaintRegist customerComplaintRegist) {
        RestResponse<DtoCustomerComplaintRegist> restResponse = new RestResponse<>();
        restResponse.setData(service.complete(customerComplaintRegist));
        return restResponse;
    }
}