package com.sinoyd.lims.qa.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.qa.dto.DtoInternalCheckInfo;

import java.util.List;


/**
 * InternalCheckInfo数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface InternalCheckInfoRepository extends IBaseJpaRepository<DtoInternalCheckInfo, String> {

    /**
     * 通过内审实施计划的id查询检查项列表
     * @param implementPlanId
     * @return 实体
     */
    List<DtoInternalCheckInfo> findByImplementPlanId(String implementPlanId);

}