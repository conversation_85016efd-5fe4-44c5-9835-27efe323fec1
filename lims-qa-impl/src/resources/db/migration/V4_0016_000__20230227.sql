
-- tb_qa_monitoringplancheckinfo
alter table tb_qa_monitoringplancheckinfo add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_qa_monitoringplancheckinfo add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_qa_monitoringplancheckinfo add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_qa_monitoringplancheckinfo add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_qa_monitoringplancheckinfo add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_qa_monitoringplancheckinfo add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_qa_monitoringplancheckinfo set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_qa_monitoringplancheckinfo set creator = '59141356591b48e18e139aa54d9dd351';
update tb_qa_monitoringplancheckinfo set createDate = '2023-02-21 09:14:15';
update tb_qa_monitoringplancheckinfo set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_qa_monitoringplancheckinfo set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_qa_monitoringplancheckinfo set modifyDate = '2023-02-21 09:14:15';