package com.sinoyd.lims.qa.service;

import com.sinoyd.lims.qa.dto.DtoSubmitRecordQa;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * SubmitRecord操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/24
 * @since V100R001
 */
public interface SubmitRecordService extends IBaseJpaService<DtoSubmitRecordQa, String> {

    /**
     * 创建提交记录
     *
     * @param objectId     关联对象id
     * @param objectType   关联对象类型
     * @param submitType   操作类型
     * @param nextPerson   下一步操作人
     * @param submitRemark 提交意见
     * @param from         操作前状态
     * @param to           操作后状态
     */
    void createSubmitRecord(String objectId,
                            Integer objectType,
                            Integer submitType,
                            String nextPerson,
                            String submitRemark,
                            String from,
                            String to);

    /**
     * 批量创建提交记录
     *
     * @param submitRecords 送样单记录
     * @param userId        用户id
     * @param userName      用户名称
     * @param orgId         组织机构id
     */
    void createSubmitRecords(List<DtoSubmitRecordQa> submitRecords,
                             String userId,
                             String userName,
                             String orgId);

    /**
     * 根据关联对象id和操作获取查询操作的最新submit对象
     *
     * @param objId      关联对象
     * @param submitType 操作类型
     * @return 操作记录对象
     */
    DtoSubmitRecordQa findSubmitRecordByObjIdAndSubmitType(String objId, Integer submitType);

    /**
     * 根据操作类型和关联对象id集合获取记录对象
     *
     * @param submitType 操作类型
     * @param objectIds  关联对象集合
     * @return 操作记录对象集合
     */
    List<DtoSubmitRecordQa> findSubmitRecordBySubmitTypeAndObjectIds(Integer submitType, List<String> objectIds);
}