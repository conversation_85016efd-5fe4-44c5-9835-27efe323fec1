package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoManagementReviewPlan;


/**
 * ManagementReviewPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface ManagementReviewPlanService extends IBaseJpaService<DtoManagementReviewPlan, String> {

    /**
     * 提交管理评审计划
     *
     * @param dtoManagementReviewPlan 实体
     * @return dtoManagementReviewPlan 实体
     */
    DtoManagementReviewPlan submit(DtoManagementReviewPlan dtoManagementReviewPlan);

    /**
     * 提交管理评审报告
     *
     * @param dtoManagementReviewPlan 实体
     * @return dtoManagementReviewPlan 实体
     */
    DtoManagementReviewPlan submitReport(DtoManagementReviewPlan dtoManagementReviewPlan);

    /**
     * 审核管理评审计划
     *
     * @param dtoManagementReviewPlan 实体
     * @return dtoManagementReviewPlan 实体
     */
    DtoManagementReviewPlan audit(DtoManagementReviewPlan dtoManagementReviewPlan);

    /**
     * 审核管理评审报告
     *
     * @param dtoManagementReviewPlan 实体
     * @return dtoManagementReviewPlan 实体
     */
    DtoManagementReviewPlan auditReport(DtoManagementReviewPlan dtoManagementReviewPlan);

    /**
     * 获取附件路径
     *
     * @param id 管理评审id
     * @return 返回相应的路径信息
     */
    DtoManagementReviewPlan findAttachPath(String id);
}