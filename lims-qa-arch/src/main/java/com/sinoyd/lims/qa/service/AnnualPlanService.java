package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoAnnualPlan;


/**
 * AnnualPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface AnnualPlanService extends IBaseJpaService<DtoAnnualPlan, String> {

    /**
     * 提交年度计划
     *
     * @param dtoAnnualPlan 实体
     * @return dtoAnnualPlan 实体
     */
    DtoAnnualPlan submit(DtoAnnualPlan dtoAnnualPlan);

    /**
     * 审核年度计划
     *
     * @param dtoAnnualPlan 实体
     * @return dtoAnnualPlan 实体
     */
    DtoAnnualPlan audit(DtoAnnualPlan dtoAnnualPlan);

    /**
     * 获取附件路径
     *
     * @param id 年度计划id
     * @return 返回相应的路径信息
     */
    DtoAnnualPlan findAttachPath(String id);
}