package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoRiskAndAccident;

import java.util.List;


/**
 * RiskAndAccident操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/15
 * @since V100R001
 */
public interface RiskAndAccidentService extends IBaseJpaService<DtoRiskAndAccident, String> {

    /**
     * 风险机遇提交
     *
     * @param riskAndAccident 要提交的风险机遇实体对象
     * @return 完成提交保存的风险机遇对象
     */
    DtoRiskAndAccident submit(DtoRiskAndAccident riskAndAccident);

    /**
     * 风险机遇措施拟定
     *
     * @param riskAndAccident 需要拟定措施的风险机遇实体对象
     * @return 完成拟定措施的风险机遇对象
     */
    DtoRiskAndAccident measureSubmit(DtoRiskAndAccident riskAndAccident);

    /**
     * 风险机遇措施审批
     *
     * @param riskAndAccident 需要风险机遇措施审批的实体对象
     * @return 完成风险机遇措施审批的风险机遇对象
     */
    DtoRiskAndAccident measureAudit(DtoRiskAndAccident riskAndAccident);

    /**
     * 风险机遇措施完成情况确认
     *
     * @param riskAndAccident 需要风险机遇措施完成情况确认的实体对象
     * @return 完成风险机遇措施完成情况确认的风险机遇对象
     */
    DtoRiskAndAccident measureConfirm(DtoRiskAndAccident riskAndAccident);

    /**
     * 风险机遇完成情况批量退回
     *
     * @param goBack 封装退回条件对象，包括需要退回的实体对象id集合和退回原因
     * @return 完成退回的实体对象集合
     */
    List<DtoRiskAndAccident> measureBack(DtoGoBack goBack);

    /**
     * 风险机遇完成确认
     *
     * @param riskAndAccident 需要风险机遇完成确认的实体对象
     * @return 完成风险机遇完成确认的实体对象
     */
    DtoRiskAndAccident complete(DtoRiskAndAccident riskAndAccident);

    /**
     * 获取附件路径
     *
     * @param id 风险基于id
     * @return 返回相应的路径信息
     */
    DtoRiskAndAccident findAttachPath(String id);


}