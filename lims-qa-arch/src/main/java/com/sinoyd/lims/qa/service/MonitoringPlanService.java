package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlan;


/**
 * MonitoringPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface MonitoringPlanService extends IBaseJpaService<DtoMonitoringPlan, String> {

    /**
     * 提交质量监督计划
     *
     * @param dtoMonitoringPlan 实体
     * @return dtoMonitoringPlan 实体
     */
    DtoMonitoringPlan submit(DtoMonitoringPlan dtoMonitoringPlan);

    /**
     * 审核质量监督计划
     *
     * @param dtoMonitoringPlan 实体
     * @return dtoMonitoringPlan 实体
     */
    DtoMonitoringPlan audit(DtoMonitoringPlan dtoMonitoringPlan);

    /**
     * 审核质量监督记录
     *
     * @param dtoMonitoringPlan 实体
     * @return dtoMonitoringPlan 实体
     */
    DtoMonitoringPlan auditRecord(DtoMonitoringPlan dtoMonitoringPlan);

    /**
     * 通过主键查询质量监督实体
     *
     * @param id 实体
     * @return 实体
     */
    DtoMonitoringPlan findOne(String id);

    /**
     * 获取附件路径
     *
     * @param id 质量监督id
     * @return 返回相应的路径信息
     */
    DtoMonitoringPlan findAttachPath(String id);
}