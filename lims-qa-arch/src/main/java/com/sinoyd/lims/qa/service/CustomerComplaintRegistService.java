package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoCustomerComplaintRegist;


/**
 * CustomerComplaintRegist操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface CustomerComplaintRegistService extends IBaseJpaService<DtoCustomerComplaintRegist, String> {

    /**
     * 提交客户投诉
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return DtoCustomerComplaintRegist 客户投诉实体
     */
    DtoCustomerComplaintRegist submit(DtoCustomerComplaintRegist dtoCustomerComplaintRegist);

    /**
     * 审核客户投诉
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return DtoCustomerComplaintRegist 客户投诉实体
     */
    DtoCustomerComplaintRegist audit(DtoCustomerComplaintRegist dtoCustomerComplaintRegist);

    /**
     * 客户投诉确认
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return DtoCustomerComplaintRegist 客户投诉实体
     */
    DtoCustomerComplaintRegist confirm(DtoCustomerComplaintRegist dtoCustomerComplaintRegist);

    /**
     * 办结客户投诉
     *
     * @param dtoCustomerComplaintRegist 客户投诉实体
     * @return DtoCustomerComplaintRegist 客户投诉实体
     */
    DtoCustomerComplaintRegist complete(DtoCustomerComplaintRegist dtoCustomerComplaintRegist);

    /**
     * 获取附件路径
     *
     * @param id 投诉管理id
     * @return 返回相应的路径信息
     */
    DtoCustomerComplaintRegist findAttachPath(String id);

}