package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoInternalAuditImplementPlan;

import java.util.List;


/**
 * InternalAuditImplementPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface InternalAuditImplementPlanService extends IBaseJpaService<DtoInternalAuditImplementPlan, String> {


    /**
     * 提交内审实施计划
     *
     * @param dtoInternalAuditImplementPlan 实体
     * @return dtoInternalAuditImplementPlan
     */
    DtoInternalAuditImplementPlan submit(DtoInternalAuditImplementPlan dtoInternalAuditImplementPlan);


    /**
     * 通过ids找到DtoInternalAuditImplementPlan退回数据
     *
     * @param dtoGoBack
     * @return DtoInternalAuditImplementPlan
     */
    List<DtoInternalAuditImplementPlan> goBack(DtoGoBack dtoGoBack);

    /**
     * 获取附件路径
     *
     * @param id 内审id
     * @return 返回相应的路径信息
     */
    DtoInternalAuditImplementPlan findAttachPath(String id);
}