package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoNotConformItem;

import java.util.List;


/**
 * NotConformItem操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface NotConformItemService extends IBaseJpaService<DtoNotConformItem, String> {

    /**
     * 不符合项提交
     *
     * @param notConformItem
     * @return
     */
    DtoNotConformItem submit(DtoNotConformItem notConformItem);

    /**
     * 不符合拟定项措施提交
     *
     * @param notConformItem
     * @return
     */
    DtoNotConformItem measuresSubmit(DtoNotConformItem notConformItem);

    /**
     * 不符合项措施审核
     *
     * @param notConformItem
     * @return
     */
    DtoNotConformItem measuresAudit(DtoNotConformItem notConformItem);

    /**
     * 不符合项措施完成情况
     *
     * @param notConformItem
     * @return
     */
    DtoNotConformItem measuresConfirm(DtoNotConformItem notConformItem);

    /**
     * 不符合项完成情况退回
     *
     * @param goBack 退回对象，封装需要退回的实体id和退回原因
     * @return
     */
    List<DtoNotConformItem> measuresConfirmBack(DtoGoBack goBack);

    /**
     * 不符合项验证评价
     *
     * @param notConformItem
     * @return
     */
    DtoNotConformItem validate(DtoNotConformItem notConformItem);

    /**
     * 获取附件路径
     *
     * @param id 不符合项id
     * @return 返回相应的路径信息
     */
    DtoNotConformItem findAttachPath(String id);

}