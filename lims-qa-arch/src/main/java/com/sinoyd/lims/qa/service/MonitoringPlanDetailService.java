package com.sinoyd.lims.qa.service;

import com.sinoyd.lims.qa.dto.DtoGoBack;
import com.sinoyd.lims.qa.dto.DtoMonitoringPlanDetail;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * MonitoringPlanDetail操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface MonitoringPlanDetailService extends IBaseJpaService<DtoMonitoringPlanDetail, String> {


    /**
     * 提交质量监督明细计划
     *
     * @param dtoMonitoringPlanDetail 实体
     * @return dtoMonitoringPlanDetail
     */
    DtoMonitoringPlanDetail submit(DtoMonitoringPlanDetail dtoMonitoringPlanDetail);

    /**
     * 通过ids找到DtoMonitoringPlanDetail退回数据
     *
     * @param dtoGoBack
     * @return DtoMonitoringPlanDetail
     */
    List<DtoMonitoringPlanDetail> goBack(DtoGoBack dtoGoBack);
}