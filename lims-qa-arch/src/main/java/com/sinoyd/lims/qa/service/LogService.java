package com.sinoyd.lims.qa.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.qa.dto.DtoLog;

import java.util.Date;
import java.util.List;


/**
 * Log操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/29
 * @since V100R001
 */
public interface LogService extends IBaseJpaService<DtoLog, String> {

    /**
     * 通用创建日志
     *
     * @param operatorId       操作者Id
     * @param operatorName     操作者名字
     * @param operateTime      操作时间
     * @param operateInfo      操作类型
     * @param nextOperatorId   下一步操作人Id
     * @param nextOperatorName 下一步操作人名字
     * @param logType          日志类型
     * @param objectId         对象id
     * @param objectType       对象类型
     * @param comment          说明
     * @param opinion          意见
     * @param remark           备注
     * @return 日志实体
     */
    DtoLog generalSave(String operatorId, String operatorName, Date operateTime, String operateInfo, String nextOperatorId,
                       String nextOperatorName, String logType, String objectId, String objectType, String comment, String opinion, String remark);

    /**
     * 根据objectId和日志类型查询（按操作时间倒序返回结果）
     *
     * @param dtoLog
     * @return 日志列表
     */
    List<DtoLog> findAll(DtoLog dtoLog);


    /**
     * 删除日志的公共方法
     *
     * @param objectId         对象id
     * @param operateInfo      操作类型
     * @param nextOperatorId   下一步操作人Id
     * @param nextOperatorName 下一步操作人名字
     * @param logType          日志类型
     * @param objectType       对象类型
     * @param comment          说明
     * @return 日志实体
     */
    DtoLog getDtoLog(String objectId, String operateInfo, String nextOperatorId, String nextOperatorName, String logType, String objectType, String comment);


}