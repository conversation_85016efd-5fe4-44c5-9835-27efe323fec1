package com.sinoyd.lims.qa.service;

import com.sinoyd.lims.qa.dto.DtoInternalAuditImplementPlan;
import com.sinoyd.lims.qa.dto.DtoInternalAuditPlan;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * InternalAuditPlan操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/3/23
 * @since V100R001
 */
public interface InternalAuditPlanService extends IBaseJpaService<DtoInternalAuditPlan, String> {

    /**
     * 提交内审管理计划
     *
     * @param dtoInternalAuditPlan 实体
     * @return dtoInternalAuditPlan
     */
    DtoInternalAuditPlan submit(DtoInternalAuditPlan dtoInternalAuditPlan);

    /**
     * 审核内审管理计划
     *
     * @param dtoInternalAuditPlan 实体
     * @return dtoInternalAuditPlan
     */
    DtoInternalAuditPlan audit(DtoInternalAuditPlan dtoInternalAuditPlan);

    /**
     * 提交内审报告
     *
     * @param dtoInternalAuditPlan 实体
     * @return dtoInternalAuditPlan
     */
    DtoInternalAuditPlan submitReport(DtoInternalAuditPlan dtoInternalAuditPlan);

    /**
     * 审核内审管理报告
     *
     * @param dtoInternalAuditPlan 实体
     * @return dtoInternalAuditPlan 实体
     */
    DtoInternalAuditPlan auditReport(DtoInternalAuditPlan dtoInternalAuditPlan);


    /**
     * 通过id查询内审实施计划
     * @param id
     * @return 实体
     */
    DtoInternalAuditPlan findOne(String id);

    /**
     * 获取附件路径
     *
     * @param id 内审id
     * @return 返回相应的路径信息
     */
    DtoInternalAuditPlan findAttachPath(String id);

}