package com.sinoyd;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.JdkDateSupport;
import com.sinoyd.frame.configuration.JdkTimestampSupport;
import com.sinoyd.lims.lim.listener.RabbitMQListener;
import com.sinoyd.lims.pro.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.WebApplicationInitializer;

import java.util.ArrayList;
import java.util.List;

/**
 * spring cloud系统启动入口
 */
@SpringBootApplication(exclude = {FlywayAutoConfiguration.class})
@ComponentScan(excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = RedisConfig.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = RabbitMQListener.class)})
@EnableJpaAuditing(auditorAwareRef = "principalContextUser")
@EnableAsync
@Slf4j
public class EnableEurekaServerApplication extends SpringBootServletInitializer implements WebApplicationInitializer {

    public static void main(String[] args) {
//        args = getArgs();
        new SpringApplicationBuilder(EnableEurekaServerApplication.class).web(true).run(args);
        JdkTimestampSupport.enable(DateUtil.FULL);
        JdkDateSupport.enable(DateUtil.FULL);
    }


//    /**
//     * 配置需要过滤orgId的实体路径
//     *
//     * @return 参数
//     */
//    private static String[] getArgs() {
//        List<String> list = new ArrayList<>();
//        list.add("com.sinoyd.base.dto.rcc");
//        list.add("com.sinoyd.base.dto.lims");
//        list.add("com.sinoyd.lims.lim.dto.lims");
//        list.add("com.sinoyd.lims.lim.dto.rcc");
//        list.add("com.sinoyd.lims.monitor.dto.lims");
//        list.add("com.sinoyd.lims.monitor.dto.rcc");
//        list.add("com.sinoyd.lims.pro.dto");
//        list.add("com.sinoyd.lims.pro.view");
//        list.add("com.sinoyd.lims.qa.dto");
//        list.add("com.sinoyd.lims.query.dto");
//        String[] strings = new String[list.size()];
//        return list.toArray(strings);
//    }
}
