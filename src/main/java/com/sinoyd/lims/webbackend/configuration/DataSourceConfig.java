package com.sinoyd.lims.webbackend.configuration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.sinoyd.boot.workflow.activiti.constant.BeanNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 多数据源配置（与frame数据源可分开处理）
 * <AUTHOR>
 * @version V1.0.0 2019/07/19
 * @since V100R001
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "primaryEntityManagerFactory",
        transactionManagerRef = "primaryTransactionManager",
        basePackages = {
                "com.sinoyd.base.repository",
                "com.sinoyd.lims.lim.repository",
                "com.sinoyd.lims.pro.repository",
                "com.sinoyd.lims.probase.repository",
                "com.sinoyd.lims.qa.repository",
                "com.sinoyd.lims.query.repository",
                "com.sinoyd.lims.monitor.repository",
                "com.sinoyd.lims.od.repository",
                "com.sinoyd.commons.repository"
        },
        repositoryFactoryBeanClass = LimsRepositoryFactoryBean.class
        //后续如果要引用pro的那basePackages要继续加pro的底层数据包
)
public class DataSourceConfig {

    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "primaryDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.primary")
    public DataSource limsDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Primary
    @Bean(name = "entityManagerPrimary")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return baseEntityManagerFactory(builder, limsDataSource()).getObject().createEntityManager();
    }

    @Bean(name = "primaryEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean baseEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("primaryDataSource") DataSource limsDataSource) {
        return builder
                .dataSource(limsDataSource)
                .packages("com.sinoyd.base.dto",
                        "com.sinoyd.lims.lim.dto",
                        "com.sinoyd.lims.pro.dto",
                        "com.sinoyd.lims.pro.view",
                        "com.sinoyd.lims.qa.dto",
                        "com.sinoyd.lims.query.dto",
                        "com.sinoyd.lims.monitor.dto",
                        "com.sinoyd.commons.dto",
                        "com.sinoyd.lims.od.dto"
                )
                //后续如果要引用pro的那basePackages要继续加pro的底层数据包，如果引用到视图也要加进来
                .properties(jpaProperties.getHibernateProperties(limsDataSource))
                .persistenceUnit("lims")
                .build();
    }

    @Bean(name = "primaryTransactionManager")
    @Primary
    public PlatformTransactionManager baseTransactionManager(
            @Qualifier("primaryEntityManagerFactory") EntityManagerFactory primaryEntityManagerFactory) {
        return new JpaTransactionManager(primaryEntityManagerFactory);
    }

    @Bean(BeanNameConstant.DATASOURCE_ACTIVITI)
    @ConfigurationProperties(prefix = "spring.datasource.activiti")
    public DataSource actDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "instrumentParseDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.instrumentParse")
    public DataSource instrumentParseDataSource() {
        return DataSourceBuilder.create().build();
    }
}