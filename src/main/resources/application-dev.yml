server:
  port: ${PORT:11001}

frame-boot:
  regVerifyUrl: ${REG_VERIFY_URL:http://*************:8760/api/register/auth/verify}
  gateUrlPrefix: ${GATE_URL_PREFIX:http://192.168.30.34:11011/api/proxy}
  restRequestTimeout: ${REST_REQUEST_TIMEOUT:30000}
  restLog:
    saveDay: ${REST_LOG_SAVE_DAY:30}

  user-center:
    localMode: ${USER_CENTER_LOCALMODE:true}
    serverUrl: ${USER_CENTER_SERVERURL:http://**************:7002/api/auth/users}
    clientId: ${USER_CENTER_CLIENTID:8}
    clientSecret: ${USER_CENTER_CLIENTSECRET:jIkjGEIfn7lYem52gcnfqHiLGkAEav98DfI5BRt4}
    sendSmsProductName: ${USER_CENTER_SMSNAME:云框架}
    passwordMode: ${USER_CENTER_PASSWORD_MODE:15}
    passwordLength: ${USER_CENTER_PASSWORD_LENGTH:8}
    remindCycle: ${USER_CENTER_REMIND_CYCLE:30}

  user-lock:
    enabled: ${USER_LOCK_ENABLED:true}
    cycleTime: ${USER_LOCK_CYCLE_TIME:30}
    maxFailNum: ${USER_LOCK_MAXFAIL_NUM:5}
    lockTime: ${USER_LOCK_LOCK_TIME:15}

spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 100000
    database: ${REDIS_DATABASE:1}

  datasource:
    primary:
      driver-class-name: com.mysql.jdbc.Driver
      url: jdbc:mysql://${DB_HOST:**************}:${DB_PORT:3306}/${LIMS_DB_NAME:lims_ent}?useUnicode=true&allowMultiQueries=true&characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Sinoyd**..123}
      initialSize: 10 # 初始连接数
      minIdle: 20 #最小空闲连接数
      maxActive: 50 #最大连接数
      maxWait: 60000 #单位毫秒，连接池中等待获取新连接的最大的时间
      timeBetweenEvictionRunsMillis: 60000 #连接池中空闲连接的存活时间，配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000 # 连接在池中最小生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000 #连接在池中最大生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL # 检测连接是否有效
      testWhileIdle: true #在连接空闲时检查连接是否有效
      testOnBorrow: false #生产不要开启
      testOnReturn: false #生产不要开启
      removeAbandoned: true #自动清理长时间未使用的连接
      removeAbandonedTimeout: 180 #

    frame:
      driverClassName: com.mysql.jdbc.Driver
      url: jdbc:mysql://${DB_HOST:**************}:${DB_PORT:3306}/${FRAME_DB_NAME:lims_ent_frame}?useUnicode=true&allowMultiQueries=true&characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Sinoyd**..123}
      initialSize: 10 # 初始连接数
      minIdle: 20 #最小空闲连接数
      maxActive: 50 #最大连接数
      maxWait: 60000 #单位毫秒，连接池中等待获取新连接的最大的时间
      timeBetweenEvictionRunsMillis: 60000 #连接池中空闲连接的存活时间，配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000 # 连接在池中最小生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000 #连接在池中最大生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL # 检测连接是否有效
      testWhileIdle: true #在连接空闲时检查连接是否有效
      testOnBorrow: false #生产不要开启
      testOnReturn: false #生产不要开启
      removeAbandoned: true #自动清理长时间未使用的连接
      removeAbandonedTimeout: 180 #单位秒

    activiti:
      driverClassName: com.mysql.jdbc.Driver
      url: jdbc:mysql://${DB_HOST:**************}:${DB_PORT:3306}/${LIMS_DB_NAME:lims_ent}?useUnicode=true&allowMultiQueries=true&characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Sinoyd**..123}
      initialSize: 10 # 初始连接数
      minIdle: 20 #最小空闲连接数
      maxActive: 50 #最大连接数
      maxWait: 60000 #单位毫秒，连接池中等待获取新连接的最大的时间
      timeBetweenEvictionRunsMillis: 60000 #连接池中空闲连接的存活时间，配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000 # 连接在池中最小生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000 #连接在池中最大生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL # 检测连接是否有效
      testWhileIdle: true #在连接空闲时检查连接是否有效
      testOnBorrow: false #生产不要开启
      testOnReturn: false #生产不要开启
      removeAbandoned: true #自动清理长时间未使用的连接
      removeAbandonedTimeout: 180 #单位秒

    instrumentParse:
      driver-class-name: com.mysql.jdbc.Driver
      url: jdbc:mysql://${DB_HOST:**************}:${DB_PORT:3306}/${INSTRUMENT_PARSE_DB_NAME:lims_ent_instrumentparse}?useUnicode=true&allowMultiQueries=true&characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Sinoyd**..123}
      initialSize: 10 # 初始连接数
      minIdle: 20 #最小空闲连接数
      maxActive: 50 #最大连接数
      maxWait: 60000 #单位毫秒，连接池中等待获取新连接的最大的时间
      timeBetweenEvictionRunsMillis: 60000 #连接池中空闲连接的存活时间，配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000 # 连接在池中最小生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000 #连接在池中最大生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL # 检测连接是否有效
      testWhileIdle: true #在连接空闲时检查连接是否有效
      testOnBorrow: false #生产不要开启
      testOnReturn: false #生产不要开启
      removeAbandoned: true #自动清理长时间未使用的连接
      removeAbandonedTimeout: 180 #单位秒

  # JPA相关配置
  jpa:
    database: mysql
    database-platform: com.sinoyd.frame.configuration.CustomMySQLDialect
    show-sql: ${JPA_SHOW_SQL:false}

  http:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:100MB}
      enabled: true
      max-request-size: ${MAX_REQUEST_SIZE:100MB}

  # RabbitMQ服务配置
  rabbitmq:
    # RabbitMQ服务器地址，使用环境变量RABBITMQ_HOST的值，如果未设置，则默认为*************
    host: ${RABBITMQ_HOST:**************}
    # RabbitMQ服务器端口，使用环境变量RABBITMQ_PORT的值，如果未设置，则默认为5672
    port: ${RABBITMQ_PORT:5672}
    # RabbitMQ用户名，使用环境变量RABBITMQ_USERNAME的值，如果未设置，则默认为guest
    username: ${RABBITMQ_USERNAME:lims}
    # RabbitMQ密码，使用环境变量RABBITMQ_PASSWORD的值，如果未设置，则默认为guest
    password: ${RABBITMQ_PASSWORD:Sinoyd**..123}
    # RabbitMQ虚拟主机，使用环境变量RABBITMQ_VIRTUALHOST的值，如果未设置，则默认为/
    virtual-host: ${RABBITMQ_VIRTUALHOST:/}
    # 启用发布者确认机制，确保消息被正确处理
    publisher-confirms: true
    listener:
      simple:
        acknowledge-mode: manual
        prefetch: 100
#        retry:
#          enabled: true
#          max-attempts: 4
#          max-interval: 10000
#          initial-interval: 2000
#          multiplier: 2
        default-requeue-rejected: false

fileProps:
  filePath: ${FILE_PATH:D:/Sinoyd/LIMS5.2/Proxy/Proxy_5.2Ent/files}
  outputPath: ${OUTPUT_PATH:D:/Sinoyd/LIMS5.2/Proxy/Proxy_5.2Ent/outputs}
  instrumentParseFilePath: ${INSTRUMENT_PARSE_FILEPATH:D:/Sinoyd/LIMS5.2/Proxy/Proxy_5.2Ent/files/instrumentParse}
  templatePath: ${TEMPLATE_PATH:D:/Sinoyd/LIMS5.2/Proxy/Proxy_5.2Ent/files/template}
  fileSuffix: ${FILE_SUFFIX:jpg,doc,docx,xls,xlsx,jpeg,png,txt,mp3,flac,avi,mp4,pdf}

logging:
  level: ${LOG_LEVEL:info}
  file: ${LOG_PATH:D:/Sinoyd/LIMS5.2/Proxy/Proxy_5.2Ent/logs/}${LOG_NAME:lims52-cloud-ent.log}

# APP下载地址
mobile:
  client:
    download:
      path: ${APP_DOWNLOAD_PATH:http://*************:8080/api/mobile/client/download}

# 远大LIMS基础数据共同库地址
standardData:
  host: ${STANDARD_DATA_HOST:http://*************:6001}
  loginId: ${STANDARD_DATA_LOGINID:13345678901}
  password: ${STANDARD_DATA_PASSWORD:111111}

ocr:
  url: ${OCR_URL:http://localhost:8866/predict/ocr_system}

aop:
  des:
    enabled: ${AOP_DES_ENABLED:false} #外部密策加密是否启用
  sign:
    enabled: ${AOP_SIGN_ENABLED:false} #外部延签功能是否启用

avoid:
  resubmit:
    enabled: ${AVOID_RESUBMIT_ENABLED:false} #是否启用防止重复提交功能
    excludeUri: ${AVOID_RESUBMIT_EXCLUDE_URI:} #需要排除的接口，多个用英文逗号拼接

#仪器接入配置信息
instrument-gather:
  # 在线平台的唯一标示
  appID: ${GATHER_APPID:16}
  # 秘钥
  appSecret: ${GATHER_APPSECRET:9e5bf0c1e2646ead56e239039cd08f7b}
  # 统一社会信用代码
  uscc: ${GATHER_USCC:123456789999999999}
  # 实时热数据URL
  hotdata_url: ${GATHER_HOTDATA_URL:https://ddc.envchina.com/api/pl-hotdata}
  # 查询数据URL
  rtdata_url: ${GATHER_RTDATA_URL:https://ddc.envchina.com/api/pl-rtdata}

# 导入时允许填写的时间类型格式
import:
  allow-date-format: ${IMPORT_ALLOW_FORMAT:yyyy-MM-dd,yyyy/MM/dd,yyyyMMdd,yyyy.MM.dd}

#环保企业通项目推送配置信息
environment-enterprise:
  # 在线平台的唯一标示
  appID: ${APPID:0}
  # 秘钥
  appSecret: ${APPSECRET:84761749b186d1828ce64c38273ac65b}
  # 推送接口地址
  push_gate_url: ${PUSH_GATE_URL:http://192.168.6.10:9819}
  # 推送接口请求路径
  push_url: ${PUSH_URL:/api/open/sample-detect/push}
  # token验证接口请求路径
  login_url: ${LOGIN_URL:/api/open/sample-detect/login}

#上海环境院推送相关配置
shanghaiAPI:
  url:  ${SH_PUSH_URL:https://www.shemss.cn:10006/WebService1/WebService.asmx}
  username: ${SH_PUSH_USERNAME:lyhb}
  password: ${SH_PUSH_PASSWORD:Liyuan@2025}

centerReport:
  url: ${CENTER_REPORT_URL:http://192.168.30.200:11002/api/sinoyd-report}

lims:
  https:
    enabled: ${ENABLED_HTTPS:false} #是否启用https
  admin: # 一体化平台后台管理员
    uid: ${LIMS_BACKEND_ADMIN_UID:i40XtquqCMru6oC8lDI0dw==}
    pid: ${LIMS_BACKEND_ADMIN_PID:tQVp6vcCaAdaU0GIIvN1Rw==}
  rcc:
    enabled: ${RCC_ENABLED:true} #是否启用RCC服务
    gateUrl: ${RCC_GATE_URL:http://*************:8760}

# 仪器解析Redis连接地址配置（需要指定对应的库）
instrument-parse:
  redis-connectStr: ${INSTRUMENT_PARSE_REDIS_CONNECTSTR:redis://:Sinoyd**..123@localhost:6379/0}