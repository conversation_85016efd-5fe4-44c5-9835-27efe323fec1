server:
  tomcat:
    uri-encoding: utf-8

sinoyd:
  mail:
    from: <EMAIL>

frame-boot:
  swagger:
    enabled: false
    basePackage: com.sinoyd
    title: 云框架
    description: LIMS API
    version: ent-5.4.0
    authorName: sinoyd
    authorEmail: <EMAIL>

  user-center:
    loginTypes:
      - code: useName
        name: 用户名
      - code: email
        name: 邮箱

  interceptor:
    userIncludePatterns:
    serviceIncludePatterns:
    excludePathPatterns:

api:
  prefix: /api

spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  profiles:
    include: ${activeProfile:dev}
  application:
    name: LIMS
  thymeleaf:
    prefix: classpath:/template/
    suffix: .html
    content-type: text/html
    cache: false
    encoding: UTF-8
    mode: LEGACYHTML5

  jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
  cache:
    type: redis
  redis:
    pool:
      # 连接池最大连接数（使用负值表示没有限制） 默认 8
      max-active: 8
      # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
      max-wait: -1
      # 连接池中的最大空闲连接 默认 8
      max-idle: 8
      # 连接池中的最小空闲连接 默认 0
      min-idle: 0

    # JPA相关配置
  jpa:
    hibernate:
      naming:
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        session_factory:
          statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
        format_sql: true
        jdbc:
          batch_size: 500
          order_inserts: true
          order_updates: true

mybatis-plus:
  openSqlPerfStat: false
  mapper-locations: classpath:/mapper/*Mapper.xml
  typeAliasesPackage: com.sinoyd.boot.frame.sys.model
  typeEnumsPackage: com.sinoyd.boot.common.model.enums
  global-config:
    id-type: 2
    field-strategy: 1
    db-column-underline: false
    refresh-mapper: true
    logic-delete-value: 1
    logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: false
    cache-enabled: false

management:
  security:
    enabled: false

jwt:
  user:
    token-header: Authorization
    expire: ${JWT_EXPIRE:120}
    delayed: ${JWT_DELAYED:5}
    rsa-secret: xx1WET12^%3^(WE45
  client:
    rsa-secret: x2318^^(*WRYQWR(QW&T

projectTask:
  projectModules:
   - moduleName : 项目登记 # 模块名称
     moduleCode : projectRegister # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 例行登记 # 模块名称
     moduleCode: routineRegister # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 多企业污染源 # 模块名称
     moduleCode: pollutionEnterprises # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 项目审核 # 项目审核
     moduleCode : projectAudit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - projectIssue # 项目下达
   - moduleName : 项目下达 # 项目下达
     moduleCode : projectIssue # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 方案登记 # 模块名称
     moduleCode: makeSolution # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 方案审核 # 模块名称
     moduleCode: auditSolution # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 采样准备 # 采样准备
     moduleCode : prepareSample # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 现场委托送样 # 现场委托送样
     moduleCode : localSendSample # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 现场任务 # 现场任务
     moduleCode : localTask # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - localDataCheck # 现场数据复核
      - localDataAudit # 现场数据审核
   - moduleName : 现场数据复核 # 现场数据复核
     moduleCode : localDataCheck # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 现场数据审核 # 现场数据审核
     moduleCode : localDataAudit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品交接 # 样品交接
     moduleCode : sampleReceive # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品分配 # 样品分配
     moduleCode : sampleAssign # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 编制报告 # 编制报告
     moduleCode : reportEdit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 编制报告(新) # 编制报告
     moduleCode: reportEditNew # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 报告审核 # 报告审核
     moduleCode : reportAudit # 模块编码、
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - reportSign # 报告签发
   - moduleName : 报告签发 # 报告签发
     moduleCode : reportSign # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 任务办结 # 任务办结
     moduleCode : projectEnd # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 质控任务登记 # 质控任务登记
     moduleCode : qcRegister # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 数据汇总 # 数据汇总
     moduleCode : qcCollect # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 评价结果 # 评价结果
     moduleCode : qcEvaluate # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 费用管理 # 费用管理
     moduleCode : costInfo # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - costInfoAudit # 费用审核
      - costInfoApprove # 费用审批
   - moduleName : 费用审核 # 费用审核
     moduleCode : costInfoAudit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 费用审批 # 费用审批
     moduleCode : costInfoApprove # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 重点项目 # 重点项目 #
     moduleCode : keyProject # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 公告 # 公告
     moduleCode : notice # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 快速导航 # 快速导航
     moduleCode : fastNavigation # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 委托统计 # 委托统计
     moduleCode : customer # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品统计 # 样品统计
     moduleCode : sample # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 检测状态统计 # 检测状态统计
     moduleCode : analyseStatus # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 检测及时率 # 检测及时率
     moduleCode : analysePromptness # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 业务量统计 # 业务量统计
     moduleCode : business # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品量统计 # 样品量统计
     moduleCode : sampleNum # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 消息提醒 # 消息提醒
     moduleCode : message # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 订单登记 # 模块名称
     moduleCode: orderRegister # 模块编码
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 订单审核 # 模块名称
     moduleCode: orderAudit  # 模块编码
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存

flyway:
  path:
    frame: /db/migration/frame # 框架相关sql文件位置
    lims: /db/migration/lims # lims 相关sql文件位置
    rcc: NONE # rcc 相关sql文件位置
    act: NONE # act相关sql文件

# 特殊字符校验（现在暂时用于附件上传）
special-str-check:
  # 是否启用
  enable: ${SPECIAL_STR_CHECK_ENABLED:true}
  # 匹配模式，配置哪些特殊字符不允许上传
  pattern: ${SPECIAL_STR_CHECK_PATTERN:`~!@#$%^&*+=|{}':;,\\<>/?~！￥%——|【】‘；：”“’。？}

# websocket启动配置
websocket:
  enabled: ${WEBSOCKET_ENABLED:false}

#排污许可证数据获取配置信息
pollutant-discharge-permit:
  # 是否启用排污许可证功能
  enabled: ${PDP_ENABLED:true}
  # 自行监测数据请求配置
  monitor-data:
    # 在线平台的唯一标示
    appId: ${PDP_MONITOR_DATA_APPID:16}
    # 秘钥
    appSecret: ${PDP_MONITOR_DATA_APPSECRET:9e5bf0c1e2646ead56e239039cd08f7b}
    # 数据获取请求网关地址
    gate-url: ${PDP_MONITOR_DATA_GATE_URL:https://ddc.envchina.com}
    # 数据获取请求接口
    url: ${PDP_MONITOR_DATA_URL:/api/pollic}

    # 仪器解析Redis连接地址配置（需要指定对应的库）
    instrument-parse:
      redis-connectStr: ${INSTRUMENT_PARSE_REDIS_CONNECTSTR:redis://localhost:6379/0}
