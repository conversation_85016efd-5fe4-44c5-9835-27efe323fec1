package com.sinoyd.lims.query.constant;

public class symbolConstant {

    public final static String 等于 = " = ";

    public final static String 不等于 = " != ";

    public final static String 模糊匹配 = " like ";

    public final static String 范围Bet = " between ";

    public final static String 范围And = " And ";

    public final static String 包含 = " in ";

    public final static String 不包含 = " not in ";

    public final static String 大于 = " > ";

    public final static String 小于 = " < ";

    public final static String 大于等于 = " >= ";

    public final static String 小于等于 = " <= ";

    public final static String 或者 = " or ";

    public final static String 并且 = " and ";
}
