package com.sinoyd.lims.query.service;

import com.sinoyd.lims.query.dto.DtoItem;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.query.dto.customer.DtoBasePageBean;
import com.sinoyd.lims.query.dto.customer.DtoDbConditionColumn;

import java.util.List;
import java.util.Map;


/**
 * Item操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
public interface ItemService extends IBaseJpaService<DtoItem, String> {

    /**
     * 复制自定义查询
     * @param oldItemId 自定义查询id
     */
    void copyItem(String oldItemId);

    /**
     * 查询类型是否已经关联自定义查询
     * @param viewId 查询类型
     * @return 个数
     */
    Integer getItemCount(String viewId);

    /**
     * 校验查询
     * @param itemId 自定义查询内容
     * @param dbConditionColumnList 查询列
     * @return 校验情况
     */
    Boolean checkoutSql(String itemId, List<DtoDbConditionColumn> dbConditionColumnList,DtoBasePageBean basePageBean);

    /**
     * 实现查询功能
     *
     * @param itemId 自定义查询内容
     * @param dbConditionColumnList 查询列
     */
    Map<String,Object> selectBaseByItem(String itemId, List<DtoDbConditionColumn> dbConditionColumnList, DtoBasePageBean basePageBean) throws Exception;
}