package com.sinoyd.lims.query.service;

import com.sinoyd.lims.query.dto.DtoItemColumn;
import com.sinoyd.lims.query.dto.DtoView;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.query.dto.customer.DtoTableColumnName;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * View操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/1
 * @since V100R001
 */
public interface ViewService extends IBaseJpaService<DtoView, String> {

    /**
     * 获取数据库中存在的表和视图
     * @return 返回表和视图
     * @throws Exception 返回报错内容
     */
    List<String> getDBTables() throws Exception;

    /**
     * 获取表或者视图中的字段
     * @param tableName 表或者视图的名称
     * @return 表或者视图中的字段
     * @throws Exception 返回报错内容
     */
    List<DtoTableColumnName> getTableColumns(String tableName)  throws Exception;

    /**
     * 获取所有有效的检索类别
     * @return 检索类别
     */
    Map<String,String> getTypeNameList();

    /**
     * 通过检索项获取显示列
     * @param viewId 检索项id
     * @return 显示列集合
     */
    List<DtoItemColumn> getItemColumnByViewId(String viewId);

    /**
     * 单个修改已选字段显示名称
     * @param view 查询类型实体对象
     * @return DtoView
     */
    DtoView updateViewField(DtoView view);
}